package com.sinosoft.eflex.config;

import ch.qos.logback.classic.Logger;
import com.sinosoft.eflex.EflexApplication;
import com.sinosoft.eflex.dao.FDSysVarMapper;
import com.sinosoft.eflex.model.FDSysVar;
import org.slf4j.LoggerFactory;

public class SysConst {

    private static Logger Log = (Logger) LoggerFactory.getLogger(SysConst.class);


    /**
     * 文件存储路径
     * 后台服务器文件根路径*
     */
    public static final String AppFileRoot;

    static {
        //获取bean
        FDSysVarMapper fdSysVarMapper = EflexApplication.ac.getBean(FDSysVarMapper.class);
        FDSysVar fdSysVar = fdSysVarMapper.selectByPrimaryKey("AppFileRoot");
        AppFileRoot = fdSysVar.getSysvarvalue();
        Log.info("后台文件根目录：" + AppFileRoot);
    }
}
