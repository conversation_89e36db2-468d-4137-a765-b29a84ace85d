package com.sinosoft.eflex.config;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/8/5
 * @desc EasyExcel配置
 */
public class EasyexcelConfig {

    /**
     * 导出福利投保信息自定义头部
     */
    public static List<List<String>> head1() {

        List<List<String>> list = new ArrayList<>();

        List head1 = new ArrayList();
        head1.add("企业名称");
        list.add(head1);

        List head2 = new ArrayList();
        head2.add("福利名称");
        list.add(head2);

        List head3 = new ArrayList();
        head3.add("福利编码");
        list.add(head3);

        List head4 = new ArrayList();
        head4.add("投保结束日期");
        list.add(head4);

        List head5 = new ArrayList();
        head5.add("保单生效日期");
        list.add(head5);

        List head6 = new ArrayList();
        head6.add("保单终止日期");
        list.add(head6);

        List head7 = new ArrayList();
        head7.add("联系人");
        list.add(head7);

        List head8 = new ArrayList();
        head8.add("联系人电话");
        list.add(head8);

        List head9 = new ArrayList();
        head9.add("联系人邮箱");
        list.add(head9);

        List head10 = new ArrayList();
        head10.add("业务员工号");
        list.add(head10);

        List head11 = new ArrayList();
        head11.add("投保员工保费");
        list.add(head11);

        List head12 = new ArrayList();
        head12.add("投保家属保费");
        list.add(head12);

        List head13 = new ArrayList();
        head13.add("投保员工人数");
        list.add(head13);

        List head14 = new ArrayList();
        head14.add("投保家属人数");
        list.add(head14);

        List head15 = new ArrayList();
        head15.add("待转默认计划保费");
        list.add(head15);

        List head16 = new ArrayList();
        head16.add("待转默认计划人数");
        list.add(head16);

        return list;
    }

}
