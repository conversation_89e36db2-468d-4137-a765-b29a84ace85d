package com.sinosoft.eflex.config;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.Enumeration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ch.qos.logback.core.rolling.TimeBasedRollingPolicy;

/**
 * 生成日志文件.
 */
public class MyTimeBasedRollingPolicy<E> extends TimeBasedRollingPolicy<E> {

    
    private static final Logger log = LoggerFactory.getLogger(MyTimeBasedRollingPolicy.class);

	@Override
	public void start() {
		fileNamePatternStr = generLogFileName(fileNamePatternStr);
		super.start();
	}

	/**
	 * 生成日志文件名.
	 */
	private String generLogFileName(String fileNamePatternStr) {
		String result = "";
		String ip = getHostIp();
		result = fileNamePatternStr.replaceAll("\\(serverip\\)", ip);
		return result;
	}

	/**
	 * 取得当前主机IP。
	 */
	private String getHostIp() {
		String ipAddress = "";
		InetAddress ia = null;
		if (isWindows()) {
			try {
				ia = InetAddress.getLocalHost();
			} catch (UnknownHostException e) {
				log.info("", e);
			}
		} else {
			boolean bFindIP = false;
			try {
				Enumeration<NetworkInterface> netInterfaces = (Enumeration<NetworkInterface>) NetworkInterface
						.getNetworkInterfaces();
				while (netInterfaces.hasMoreElements()) {
					if (bFindIP) {
						break;
					}
					NetworkInterface ni = (NetworkInterface) netInterfaces.nextElement();
					Enumeration<InetAddress> ips = ni.getInetAddresses();
					while (ips.hasMoreElements()) {
						ia = (InetAddress) ips.nextElement();
						if (ia.isSiteLocalAddress() && !ia.isLoopbackAddress()
								&& ia.getHostAddress().indexOf(":") == -1) {
							bFindIP = true;
							break;
						}
					}
				}
			} catch (SocketException e) {
			    log.info("", e);
			}
		}
		ipAddress = ia.getHostAddress();
		return ipAddress;
	}

	/**
	 * 判断OS是否为windows.
	 */
	private boolean isWindows() {
		boolean isWindowsOS = false;
		String osName = System.getProperty("os.name");
		if (osName.toLowerCase().indexOf("windows") > -1) {
			isWindowsOS = true;
		}
		return isWindowsOS;
	}
}
