package com.sinosoft.eflex;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.servlet.MultipartConfigElement;

@ComponentScan(basePackages = "com.sinosoft")
@EnableDiscoveryClient
@MapperScan("com.sinosoft.eflex.dao")
@Configuration
@EnableTransactionManagement
@SpringBootApplication
@EnableScheduling
@EnableAsync
@Slf4j
public class EflexApplication {

    public static ConfigurableApplicationContext ac;

    public static void main(String[] args) {

        EflexApplication.ac = SpringApplication.run(EflexApplication.class, args);
        /*try {
            Environment env = ac.getEnvironment();
            String ip = InetAddress.getLocalHost().getHostAddress();
            String port = env.getProperty("server.port");
            port = port == null ? "8080" : port;
            String path = env.getProperty("server.servlet.context-path");
            path = path == null ? "" : path;
            log.info("\n----------------------------------------------------------\n\t" +
                    "Application Demo is running! Access URLs:\n\t" +
                    "本地访问地址: \thttp://localhost:" + port + path + "/\n\t" +
                    "外部访问地址: \thttp://" + ip + ":" + port + path + "/\n\t" +
                    "Swagger文档: \thttp://" + ip + ":" + port + path + "/swagger-ui.html\n" +
                    "----------------------------------------------------------");
        }catch (Exception e){
            log.error("server.port.error:{}",e);
        }*/
    }

    /**
     * 文件上传配置
     *
     * @return
     */
    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        //单个文件最大
        factory.setMaxFileSize("10240KB"); //KB,MB
        /// 设置总上传数据总大小
        factory.setMaxRequestSize("102400KB");
        //String location = System.getProperty("user.dir") + "/uploadfile/temp";
        String location = System.getProperty("user.dir");
        //String location = "D:/temp";
        factory.setLocation(location);
        System.out.println("临时文件路径：" + location);
        return factory.createMultipartConfig();
    }




}
