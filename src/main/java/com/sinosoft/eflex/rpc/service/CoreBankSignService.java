package com.sinosoft.eflex.rpc.service;

import com.sinosoft.eflex.model.sign.apply.SignApplyRespData;
import com.sinosoft.eflex.model.sign.confirm.SignConfirmRespData;
import com.sinosoft.eflex.rpc.model.BankSign.SignApplyReqDTO;
import com.sinosoft.eflex.rpc.model.BankSign.SignConfirmReqDTO;

/**
 * 签单*
 *
 * <AUTHOR>
 */
public interface CoreBankSignService {

    /**
     * 银行卡签约
     *
     * @param signApplyReqDTO 请求信息
     * @return 签约信息
     */
    SignApplyRespData bankSignApply(SignApplyReqDTO signApplyReqDTO);

    /**
     * 银行卡签约
     *
     * @param signConfirmDTO 请求信息
     * @return 签约信息
     */
    SignConfirmRespData bankSignConfirm(SignConfirmReqDTO signConfirmDTO);
}
