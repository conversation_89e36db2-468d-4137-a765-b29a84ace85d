package com.sinosoft.eflex.rpc.service.Impl;

import com.hqins.common.utils.HttpUtils;
import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.framework.exception.EFlexServiceException;
import com.sinosoft.eflex.framework.exception.EFlexServiceExceptionEnum;
import com.sinosoft.eflex.model.sign.apply.SignApplyRespData;
import com.sinosoft.eflex.model.sign.confirm.SignConfirmRespData;
import com.sinosoft.eflex.rpc.model.BankSign.SignApplyReqDTO;
import com.sinosoft.eflex.rpc.model.BankSign.SignApplyRespDTO;
import com.sinosoft.eflex.rpc.model.BankSign.SignConfirmReqDTO;
import com.sinosoft.eflex.rpc.model.BankSign.SignConfirmRespDTO;
import com.sinosoft.eflex.rpc.service.CoreBankSignService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Objects;

import static com.sinosoft.eflex.constants.EflexConstants.FLAG_ONE;
import static com.sinosoft.eflex.constants.EflexConstants.SIGN_STATE;

/**
 * 银行卡签约*
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CoreBankSignServiceImpl implements CoreBankSignService {


    private final MyProps myProps;


    @Override
    public SignApplyRespData bankSignApply(SignApplyReqDTO signApplyReqDTO) {
        log.info("CoreBankSignServiceImpl bankSignApply request{}", JsonUtil.toJSON(signApplyReqDTO));
        HashMap<String, String> headerMap = new HashMap<>(3);
        headerMap.put("Content-Type", "application/json;charset=UTF-8");
        headerMap.put("app_id", myProps.getAppId());
        headerMap.put("app_secret", myProps.getAppSecret());
        String result;
        try {
            result = HttpUtils.postJSON(myProps.getSignApplyUrl(), JsonUtil.toJSON(signApplyReqDTO), headerMap);
            log.info("CoreBankSignServiceImpl bankSignApply response{}", result);
            if (StringUtils.isEmpty(result)) {
                throw new EFlexServiceException(EFlexServiceExceptionEnum.SIGN_APPLY_ERROR);
            }
        } catch (Exception e) {
            log.info("bankSignApply error", e);
            throw new EFlexServiceException(EFlexServiceExceptionEnum.SIGN_APPLY_ERROR);
        }
        SignApplyRespDTO response = JsonUtil.fromJSON(result, SignApplyRespDTO.class);
        if (!response.getSuccess()) {
            throw new EFlexServiceException(response.getMessage());
        }
        if (Objects.isNull(response.getData())) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.SIGN_APPLY_ERROR);
        }
        if (!FLAG_ONE.equals(response.getData().getSignState())) {
            throw new EFlexServiceException(response.getData().getSignMsg());
        }

        return response.getData();
    }


    @Override
    public SignConfirmRespData bankSignConfirm(SignConfirmReqDTO signConfirmDTO) {
        log.info("CoreBankSignServiceImpl bankSignConfirm request{}", JsonUtil.toJSON(signConfirmDTO));
        HashMap<String, String> headerMap = new HashMap<>(3);
        headerMap.put("Content-Type", "application/json;charset=UTF-8");
        headerMap.put("app_id", myProps.getAppId());
        headerMap.put("app_secret", myProps.getAppSecret());
        String result;
        try {
            result = HttpUtils.postJSON(myProps.getSignConfirmUrl(), JsonUtil.toJSON(signConfirmDTO), headerMap);
            log.info("CoreBankSignServiceImpl bankSignConfirm response:{}", JsonUtil.toJSON(result));
            if (StringUtils.isEmpty(result)) {
                throw new EFlexServiceException(EFlexServiceExceptionEnum.SIGN_CONFIRM_ERROR);
            }
        } catch (Exception e) {
            log.info("bankSignConfirm error", e);
            throw new EFlexServiceException(EFlexServiceExceptionEnum.SIGN_CONFIRM_ERROR);
        }
        SignConfirmRespDTO response = JsonUtil.fromJSON(result, SignConfirmRespDTO.class);
        if (!response.getSuccess()) {
            throw new EFlexServiceException(response.getMessage());
        }
        if (Objects.isNull(response.getData())) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.SIGN_CONFIRM_ERROR);
        }
        if (!FLAG_ONE.equals(response.getData().getSignState())) {
            throw new EFlexServiceException(response.getData().getSignMsg());
        }
        return response.getData();
    }


}
