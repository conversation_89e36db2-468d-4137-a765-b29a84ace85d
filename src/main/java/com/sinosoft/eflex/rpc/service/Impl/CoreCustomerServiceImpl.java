package com.sinosoft.eflex.rpc.service.Impl;


import com.google.common.collect.Lists;
import com.hqins.common.utils.HttpUtils;
import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.framework.exception.EFlexServiceException;
import com.sinosoft.eflex.framework.exception.EFlexServiceExceptionEnum;
import com.sinosoft.eflex.rpc.model.CheckSameCustomerReqDTO;
import com.sinosoft.eflex.rpc.model.CheckSameCustomerResDTO;
import com.sinosoft.eflex.rpc.model.CoreCustomerInfoResDTO;
import com.sinosoft.eflex.rpc.service.CoreCustomerService;
import com.sinosoft.eflex.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 核心 -客户验证*
 *
 * <AUTHOR> *
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CoreCustomerServiceImpl implements CoreCustomerService {

    private final MyProps myProps;


    @Override
    public List<CoreCustomerInfoResDTO> sameCustomer(CheckSameCustomerReqDTO request) {
        log.info("CoreCustomerServiceImpl sameCustomer request{}", JsonUtil.toJSON(request));
        HashMap<String, String> headerMap = new HashMap<>(3);
        headerMap.put("Content-Type", "application/json;charset=UTF-8");
        headerMap.put("app_id", myProps.getAppId());
        headerMap.put("app_secret", myProps.getAppSecret());
        log.debug("checkIdCard idCardVerifyRequest :{}", JsonUtil.toJSON(request));
        String result;
        try {
            result = HttpUtils.postJSON(myProps.getCheckSameCustomerBatch(), JsonUtil.toJSON(request), headerMap);
            if (StringUtil.isEmpty(result)) {
                throw new EFlexServiceException(EFlexServiceExceptionEnum.SAME_CUSTOMER_ERROR);
            }
        } catch (Exception e) {
            log.info("sameCustomer error", e);
            throw new EFlexServiceException(EFlexServiceExceptionEnum.SAME_CUSTOMER_ERROR);
        }
        log.info("CoreCustomerServiceImpl sameCustomer response:{}", JsonUtil.toJSON(result));
        CheckSameCustomerResDTO response = JsonUtil.fromJSON(result, CheckSameCustomerResDTO.class);
        if (Objects.isNull(response) || Objects.isNull(response.getBody())) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.SAME_CUSTOMER_ERROR);
        }
        return response.getBody().stream().filter(Objects::nonNull).collect(Collectors.toList());
    }
}
