package com.sinosoft.eflex.rpc.feign;

import com.sinosoft.eflex.model.sendmessage.SendEmailReq;
import com.sinosoft.eflex.model.sendmessage.SendSMSReq;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 * @date 2020-12-21 10:53
 */
@FeignClient(name = "sendMessage", url = "${myProps.sendMessage.url}")
@Service
public interface SendMessageApi {

    /**
     * 发送短信
     *
     * @param
     * @retur
     */
    @RequestMapping(method = RequestMethod.POST, value = "/sendSms", produces = "application/json;charset=utf-8", consumes = "application/json;charset=utf-8")
    String sendSms(SendSMSReq sendSMSReq);

    /**
     * 发送邮件
     */
    @RequestMapping(method = RequestMethod.POST, value = "/sendMail", produces = "application/json;charset=utf-8", consumes = "application/json;charset=utf-8")
    String sendMail(SendEmailReq sendEmailReq);


}
