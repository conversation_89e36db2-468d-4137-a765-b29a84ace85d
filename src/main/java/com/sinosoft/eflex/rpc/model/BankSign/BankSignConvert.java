package com.sinosoft.eflex.rpc.model.BankSign;

import com.sinosoft.eflex.model.FcBatchPayBankInfo;
import com.sinosoft.eflex.model.sign.apply.SignApplyReq;
import com.sinosoft.eflex.util.DateTimeUtil;

/**
 * 转换*
 *
 * <AUTHOR> *
 */
public class BankSignConvert {

    public static SignApplyReqDTO convert(SignApplyReq signApplyReq, String signSn, String businessNo) {
        SignApplyReqDTO signApplyReqDTO = new SignApplyReqDTO();
        signApplyReqDTO.setTransNo(signSn);
        signApplyReqDTO.setTransTime(DateTimeUtil.getCurrentDateTime());
        signApplyReqDTO.setRequestSeq(signSn);
        signApplyReqDTO.setPayKind("PORT");
        signApplyReqDTO.setChannelCode(signApplyReq.getChannelCode());
        signApplyReqDTO.setBusinessNo(businessNo);
        signApplyReqDTO.setBusinessDesc(signApplyReq.getBusinessDesc());
        signApplyReqDTO.setBusinessType(1);
        signApplyReqDTO.setProductId(signApplyReq.getProductId());
        signApplyReqDTO.setAmount(1);
        signApplyReqDTO.setDataBackUrl(signApplyReq.getDataBackUrl());
        signApplyReqDTO.setCustomName(signApplyReq.getCustomName());
        signApplyReqDTO.setCustomId(signApplyReq.getCustomId());
        signApplyReqDTO.setIdCardType(signApplyReq.getIdCardType());
        signApplyReqDTO.setIdCardNo(signApplyReq.getIdCardNo());
        signApplyReqDTO.setMobile(signApplyReq.getMobile());
        signApplyReqDTO.setSignType(signApplyReq.getSignType());
        signApplyReqDTO.setWithHoldFlag(signApplyReq.getWithHoldFlag());
        signApplyReqDTO.setPayFreq(signApplyReq.getPayFreq());
        signApplyReqDTO.setBankCode(signApplyReq.getBankCode());
        signApplyReqDTO.setBankAccNo(signApplyReq.getBankAccNo().replaceAll("\\s", ""));
        return signApplyReqDTO;
    }

    public static SignConfirmReqDTO convert(String signSn, String verifyCode, FcBatchPayBankInfo fcBatchPayBankInfo ) {
        SignConfirmReqDTO signConfirmReqDTO = new SignConfirmReqDTO();
        signConfirmReqDTO.setTransNo(signSn);
        signConfirmReqDTO.setTransTime(DateTimeUtil.getCurrentDateTime());
        signConfirmReqDTO.setBankAccCode(fcBatchPayBankInfo.getBankAccCode());
        signConfirmReqDTO.setSignSmsCode(verifyCode);
        signConfirmReqDTO.setTransId(fcBatchPayBankInfo.getTransId());
        return signConfirmReqDTO;
    }


}