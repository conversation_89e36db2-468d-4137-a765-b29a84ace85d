package com.sinosoft.eflex.rpc.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CoreCustomerInfoDTO implements Serializable {

    /**
     * 姓名
     */
    private String customerName;

    /**
     * 性别
     */
    private String customerSex;

    /**
     * 出生日期	*
     */
    private String customerBirthday;

    /**
     * 证件类型*
     */
    private String customerIDType;

    /**
     * 证件号码*
     */
    private String customerIDNo;

    /**
     * 手机号
     */
    private String mobile;
}