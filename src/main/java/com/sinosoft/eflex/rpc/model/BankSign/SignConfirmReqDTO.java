
package com.sinosoft.eflex.rpc.model.BankSign;

import lombok.Data;

@Data
public class SignConfirmReqDTO {

    // 网关校验参数,交易编码
    private final String transCode = "ISHARE-PAY-API-BANKSIGNSURE";
    // 交易来源
    private final String transSource = "YF";
    // 网关校验参数，交易流水号 交易流水号，唯一，如：1000000001609990196484
    private String transNo;
    // 交易时间 格式 yyyy-MM-dd HH:mm:ss
    private String transTime;
    // 银行卡签约记录编码 签约申请中返回
    private String bankAccCode;
    // 短信验证码
    private String signSmsCode;
    // 交易流水号 签约申请中返回
    private String transId;

}
