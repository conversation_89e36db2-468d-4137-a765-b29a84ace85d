package com.sinosoft.eflex.rpc.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CoreCustomerErrorResDTO extends CoreCustomerInfoDTO implements Serializable {

    /**
     * 错误类型*
     */
    private String errorType;

    /**
     * 错误信息 *
     */
    private String errorInfo;
}