package com.sinosoft.eflex.rpc.model;

import com.sinosoft.eflex.model.AddressEntity.ReturnCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/11/7 14:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CheckSameCustomerResDTO implements Serializable {

    private ReturnCode returnCode;
    private List<CoreCustomerInfoResDTO> body;
    private String cid;
    private String systime;
    private String elapsed;
    private String code;
    private String message;


}
