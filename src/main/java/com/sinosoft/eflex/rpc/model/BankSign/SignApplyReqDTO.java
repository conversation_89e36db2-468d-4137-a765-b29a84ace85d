
package com.sinosoft.eflex.rpc.model.BankSign;

import lombok.Data;

import java.io.Serializable;

/**
 * 签约申请请求报文
 *
 * <AUTHOR>
 */
@Data
public class SignApplyReqDTO implements Serializable {

    // 网关校验参数,交易编码
    private final String transCode = "ISHARE-PAY-API-BANKSIGNAPPLY";
    // 网关校验参数，交易流水号
    private String transNo;
    // 网关校验参数，交易来源
    private final String transSource = "YF";
    // 网关交易参数，交易时间，格式 yyyy-MM-dd HH:mm:ss
    private String transTime;
    // 接口请求序列号 32位唯一字符串，每次请求唯一
    private String requestSeq;
    // 接入类型 H5：H5接入 PC：PC端接入 PORT：接口数据接入
    private String payKind;
    // 渠道编码
    private long channelCode;
    // 业务单号 对接系统-投保单号，原样返回（如没有投保单号，则为对接系统数据的唯一标识）
    private String businessNo;
    // 业务描述 N
    private String businessDesc;
    // 业务类型 1 中台收银台签约 2 前端系统接口签约 3 核心系统接口签约
    private long businessType;
    // 产品编码 核心统一的产品编码或者其他系统独立的产品编码 N
    private String productId;
    // 付款金额 格式为两位小数的数字，如：20.02，没有则传递0
    private double amount;
    // 结果回调路径 签约结果通知路径，该字段非空，则中台接收到签约结果会调用此路径推送签约结果 N
    private String dataBackUrl;
    // 客户姓名
    private String customName;
    // 客户唯一标识 N
    private String customId;
    // 证件类型
    private String idCardType;
    // 证件号码
    private String idCardNo;
    // 手机号
    private String mobile;
    // 签约类型 签约类型，0-批量；1-单笔实时 N
    private String signType;
    // 续保标识 续保标识，Y-续保；N-非续保；默认为N N
    private String withHoldFlag;
    // 缴费方式 缴费方式，1-年缴；2-月缴；-1其他，默认为-1 N
    private String payFreq;
    // 银行编码
    private String bankCode;
    // 银行账号
    private String bankAccNo;

}
