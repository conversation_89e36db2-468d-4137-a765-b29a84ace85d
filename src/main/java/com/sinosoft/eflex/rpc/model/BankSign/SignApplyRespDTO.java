package com.sinosoft.eflex.rpc.model.BankSign;

import com.sinosoft.eflex.model.sign.apply.SignApplyRespData;
import lombok.Data;

import java.io.Serializable;

/**
 * 签约申请返回报文
 */
@Data
public class SignApplyRespDTO implements Serializable {

    // 响应编码 通信成功-200
    private String code;
    // 成功标识 成功true;失败false，非签约申请结果标识，签约申请结果以signState为准
    private Boolean success;
    // 返回信息
    private String message;
    // 以下数据信息在code为200，success为true时返回
    private SignApplyRespData data;

}
