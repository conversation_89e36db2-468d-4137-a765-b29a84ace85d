package com.sinosoft.eflex.rpc.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CheckSameCustomerReqDTO implements Serializable {
    /**
     * 网关校验参数,交易编码
     */
    private String transCode;
    /**
     * 网关校验参数，交易流水号
     */
    private String transNo;
    /**
     * 网关校验参数，交易来源
     */
    private String transSource;
    /**
     * 网关交易参数，交易时间
     */
    private String transTime;
    private String transSN;
    private String customerNo;

    /**
     * 客户信息*
     */
    private List<CoreCustomerInfoDTO> personList;

    public CheckSameCustomerReqDTO(List<CoreCustomerInfoDTO> personPojoList) {
        this.transCode = "INSURE-OFFLINE-CONTROLLER-CUSTOMER-BATCH-VERIFY";
        this.transNo = String.valueOf(System.currentTimeMillis());
        this.transSource = "YF";
        this.transTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        this.transSN = String.valueOf(System.currentTimeMillis());
        this.personList = personPojoList;
    }
}