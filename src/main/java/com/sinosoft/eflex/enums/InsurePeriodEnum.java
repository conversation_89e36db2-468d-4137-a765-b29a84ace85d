package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/15
 * 被保险人周期
 */
@Getter
public enum InsurePeriodEnum {

    UNTIL85("01","保至85周岁"),

    LIFELONG("02","终身");

    private String code;
    private String value;

    InsurePeriodEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static InsurePeriodEnum getTypeByCode(String code) {
        for (InsurePeriodEnum enums : InsurePeriodEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (InsurePeriodEnum enums : InsurePeriodEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
