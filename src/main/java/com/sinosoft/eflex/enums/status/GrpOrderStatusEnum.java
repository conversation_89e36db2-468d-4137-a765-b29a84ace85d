package com.sinosoft.eflex.enums.status;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/16 团体订单状态
 */
@Getter
public enum GrpOrderStatusEnum {

    NO_SUBMIT("01", "未提交"),

    SUBMITTED("02", "已提交"),

    CORE_SUBMITTED_FAIL("03", "提交核心失败"),

    INSURE_SUCCESS("04", "投保成功，提交核心成功"),

    INSURE_FAIL("05", "投保失败");

    private final String code;
    private final String value;

    GrpOrderStatusEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static GrpOrderStatusEnum getTypeByCode(String code) {
        for (GrpOrderStatusEnum enums : GrpOrderStatusEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (GrpOrderStatusEnum enums : GrpOrderStatusEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
