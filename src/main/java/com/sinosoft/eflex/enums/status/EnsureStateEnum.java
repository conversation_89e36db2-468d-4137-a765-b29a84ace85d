package com.sinosoft.eflex.enums.status;

import lombok.Getter;

/**
 * @author: z<PERSON><PERSON><PERSON>ang
 * @date: 2020/12/15
 * 福利状态
 */
@Getter
public enum EnsureStateEnum {

    NOTCUSTOM("0", "未定制"),

    CHECK_COMPLETED("1", "复核完成"),

    TOBACKGROUNDREVIEW("02", "待后台审核"),

    BACKGROUNDREVIEWING("03", "后台审核中"),

    RETURNHRCUSTOMIZATION("04", "退回HR定制"),

    TOCHECKTOCONFIRM("05", "待复核确认"),

    CHECKTOCONFIRMING("06", "复核确认中"),

    BACKGROUNDCUSTOMIZATIONING("07", "后台定制中"),

    TOHRCUSTOMIZATION("08", "待HR定制"),

    HRCUSTOMIZATIONING("09", "HR定制中"),

    HRRETURNCUSTOMBACKGROUND("010", "HR退回后台定制"),

    ADMINISTRATORRETURNCUSTOMBACKGROUND("011", "管理员退回后台定制"),

    CUSTOMIZATIONING("012", "定制中"),

    TOAUDIT("013", "待审核"),

    TOBECHARGED("014", "待收费签单"),

    HADINSURED("015", "已承保"),

    AUDIT_ING("016", "审核中"),

    AUDIT_RETURN("017", "审核退回"),

    UNDERWRITINGFAILURE("018", "承保失败"),

    ALREADYCANCELLATION("019", "已作废"),

    CORE_SUBMITTED("020", "已提交核心");

    private final String code;
    private final String value;

    EnsureStateEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static EnsureStateEnum getTypeByCode(String code) {
        for (EnsureStateEnum enums : EnsureStateEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (EnsureStateEnum enums : EnsureStateEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
