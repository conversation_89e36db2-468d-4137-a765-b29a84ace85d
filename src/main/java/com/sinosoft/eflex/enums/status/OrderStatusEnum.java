package com.sinosoft.eflex.enums.status;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/16
 * 团体订单状态
 */
@Getter
public enum OrderStatusEnum {

    TOSUBMITCORE("01","待提交核心"),

    SUBMITTEDCORE("02","已提交核心"),

    INSUREING("03","待确认"),

    TOPAY("04","待支付"),

    PAYING("05","支付中"),

    PAYCOMPLETION("06","支付完成"),

    PAYMENTFAILURE("07","支付失败"),

    INSURE_SUCCESS("08", "投保成功、待生效"),

    TOFEEDEDUCTION("09", "待扣费"),

    TOMANUALUNDERWRITING("010", "待人工核保"),

    DECLINATURE("011", "拒保"),

    POSTPONE("012", "延期"),

    CANCELLATIONS("013", "撤单"),

    WITHDRAW("016", "撤件");

    private String code;
    private String value;

    OrderStatusEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static OrderStatusEnum getTypeByCode(String code) {
        for (OrderStatusEnum enums : OrderStatusEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (OrderStatusEnum enums : OrderStatusEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
