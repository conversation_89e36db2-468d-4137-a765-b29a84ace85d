package com.sinosoft.eflex.enums.status;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/16
 * 保单状态（保全理赔保单查询）
 */
@Getter
public enum PolicyState2Enum {

    VALID("1","有效"),

    WITHINTENDAYSCANCELLATIONS("2","十日内撤单"),

    POLICYCANCELLATION("3","退保"),

    TERMINATION("4","终止");

    private String code;
    private String value;





    PolicyState2Enum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static PolicyState2Enum getTypeByCode(String code) {
        for (PolicyState2Enum enums : PolicyState2Enum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (PolicyState2Enum enums : PolicyState2Enum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
