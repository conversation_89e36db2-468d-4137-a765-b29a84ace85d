package com.sinosoft.eflex.enums.status;

import lombok.Getter;

/**
 * 承保签单状态（fcprtandcorerela记录的状态） 投保成功后，会进行承保
 * ps：看到这么多表的不同状态，我真的是头大！！！ 2021.8.9
 */
@Getter
public enum PolicySignStateEnum {

    SENTCODE("01", "承保数据已推送至核心"),

    UNDERWRITESUCCESS("02", "承保成功"),

    UNDERWRITEFAIL("03", "承保失败"),

    INSURESUCCESS("04", "投保成功"),

    INSUREFAIL("05", "投保失败");

    private String code;
    private String value;

    PolicySignStateEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static PolicySignStateEnum getTypeByCode(String code) {
        for (PolicySignStateEnum enums : PolicySignStateEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (PolicySignStateEnum enums : PolicySignStateEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
