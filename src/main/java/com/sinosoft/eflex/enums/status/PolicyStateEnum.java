package com.sinosoft.eflex.enums.status;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/16 保单状态（福利表中的状态，fcEnsure-PolicyState）
 */
@Getter
public enum PolicyStateEnum {

    INSUREING("1","投保中"),

    TOUNDERWRITE("2","待承保"),

    ALREADYUNDERWRITE("3","已承保"),

    UNDERWRITEFINISH("4","承保结束"),

    UNDERWRITEDEFEATED("5","承保失败");

    private String code;
    private String value;


    PolicyStateEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static PolicyStateEnum getTypeByCode(String code) {
        for (PolicyStateEnum enums : PolicyStateEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (PolicyStateEnum enums : PolicyStateEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
