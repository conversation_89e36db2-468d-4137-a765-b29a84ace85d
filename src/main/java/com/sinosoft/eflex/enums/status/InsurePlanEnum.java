package com.sinosoft.eflex.enums.status;

import lombok.Getter;

/**
 * @author: xuhong<PERSON>
 * @date: 计划书上下架状态
 */
@Getter
public enum InsurePlanEnum {

    UP(0, "上架"),

    DOWN(1, "下架");

    private int code;
    private String value;

    InsurePlanEnum(int code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static InsurePlanEnum getTypeByCode(int code) {
        for (InsurePlanEnum enums : InsurePlanEnum.values()) {
            if (enums.getCode()==code) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(int code) {
        for (InsurePlanEnum enums : InsurePlanEnum.values()) {
            if (enums.getCode()==code) {
                return enums.getValue();
            }
        }
        return "";
    }
}
