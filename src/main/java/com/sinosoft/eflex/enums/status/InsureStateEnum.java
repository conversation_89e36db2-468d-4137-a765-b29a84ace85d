package com.sinosoft.eflex.enums.status;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/16 团体订单状态
 */
@Getter
public enum InsureStateEnum {

    NOSUBMIT("0", "未提交订单表"),

    SUBMITTED("1", "已提交订单表"),

    CORESUBMITTED("2", "已提交订单至核心");

    private String code;
    private String value;

    InsureStateEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static InsureStateEnum getTypeByCode(String code) {
        for (InsureStateEnum enums : InsureStateEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (InsureStateEnum enums : InsureStateEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
