package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/15
 * 被保险人类型
 */
@Getter
public enum InsuredTypeEnum {

    STAFF("0","员工"),

    RELATION("1","家属");

    private String code;
    private String value;

    InsuredTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static InsuredTypeEnum getTypeByCode(String code) {
        for (InsuredTypeEnum enums : InsuredTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (InsuredTypeEnum enums : InsuredTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
