package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: wudezhong
 * @date: 2021/3/19 10:42 计划状态
 */
@Getter
public enum PlanStateEnum {

    MAKEING("01", "定制中"),

    MAKEDONE("02", "定制完成");

    private String code;
    private String value;

    PlanStateEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static PlanStateEnum getTypeByCode(String code) {
        for (PlanStateEnum enums : PlanStateEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (PlanStateEnum enums : PlanStateEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
