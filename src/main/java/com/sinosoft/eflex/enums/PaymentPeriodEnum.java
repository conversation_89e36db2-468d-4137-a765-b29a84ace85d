package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * <AUTHOR> wu<PERSON><PERSON><PERSON><PERSON>
 * @date : 2021-02-03 16:19
 * 日常计划安颐无忧险种交费期间
 **/
@Getter
public enum PaymentPeriodEnum {
    SINGLEPAYMENT("1","一次性交清"),

    THREEYEARPAYMENT("3","3年交"),

    FIVEYEARPAYMENT("5","5年交");


    private String code;
    private String value;





    PaymentPeriodEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static PaymentPeriodEnum getTypeByCode(String code) {
        for (PaymentPeriodEnum enums : PaymentPeriodEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (PaymentPeriodEnum enums : PaymentPeriodEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
