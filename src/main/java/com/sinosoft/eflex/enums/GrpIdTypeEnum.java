package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * 企业证件类型
 */
@Getter
public enum GrpIdTypeEnum {

    UNIFIEDSOCICODE("1", "统一社会信用代码"),

    ORGANIZATION("2", "组织机构代码证"),

    TAXATION("3", "税务登记证"),

    BUSINESSLICENSE("4", "营业执照"),

    GRPLEGALPERSONTYPE("5", "事业单位法人证书"),

    SOCIALGROUPSLEGALPERSONTYPE("6", "社会团体法人证书"),

    PRIVATECERTIFICATE("7", "民办非企业单位登记证书"),

    FOUNDATIONLEGALPERSONCERTIFICATE("8", "基金会法人登记证书"),

    BUSINESSNO("9", "工商注册号码"),

    OTHER("10", "其他证件");

    private String code;
    private String value;

    GrpIdTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static GrpIdTypeEnum getTypeByCode(String code) {
        for (GrpIdTypeEnum enums : GrpIdTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (GrpIdTypeEnum enums : GrpIdTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
