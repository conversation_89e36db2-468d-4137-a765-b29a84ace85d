package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * <AUTHOR> wu<PERSON><PERSON><PERSON><PERSON>
 * @date : 2021-01-20 17:50
 * 中台提供的缴费方式
 * code:    代表中台要求的参数值
 * value:   代表相应参数对应的属性
 **/
@Getter
public enum PayFreqEnum {
    ANNUALPAYMENT("1","年缴"),

    MONTHLYPAYMENT("2","月缴"),

    OTHER("-1","其他");


    private String code;
    private String value;

    PayFreqEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }



    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static PayFreqEnum getTypeByCode(String code) {
        for (PayFreqEnum enums : PayFreqEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (PayFreqEnum enums : PayFreqEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
