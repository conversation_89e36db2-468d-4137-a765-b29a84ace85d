package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/6/7 18:09
 */
@Getter
public enum ResultCodeEnum {

    // 成功请求
    SUCCESS("200", "成功"),
    // 重定向
    REDIRECT("301", "重定向异常"),
    // 资源未找到
    NOT_FOUND("404", "not found"),
    // 服务器错误
    SERVER_ERROR("500", "server error"),

    // 自定义业务异常
    BUSINESS_EXCEPTION("400", "业务异常"),

    // 自定义代码异常
    CODE_EXCEPTION("500", "代码异常"),

    // 自定义业务异常
    INSURERULE_EXCEPTION("300", "投保规则异常");

    private String code;
    private String value;

    ResultCodeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static ResultCodeEnum getTypeByCode(String code) {
        for (ResultCodeEnum enums : ResultCodeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (ResultCodeEnum enums : ResultCodeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }

}
