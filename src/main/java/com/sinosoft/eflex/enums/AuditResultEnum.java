package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/14
 * 承保状态
 */
@Getter
public enum AuditResultEnum {

    CHECKPENDING("0","待审核"),

    PASSTHEAUDIT("1","审核通过"),

    FAIL("2","未通过");


    private String code;
    private String value;

    AuditResultEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }



    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static AuditResultEnum getTypeByCode(String code) {
        for (AuditResultEnum enums : AuditResultEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (AuditResultEnum enums : AuditResultEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
