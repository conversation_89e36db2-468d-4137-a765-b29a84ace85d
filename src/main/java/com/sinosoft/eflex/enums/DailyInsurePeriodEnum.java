package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * <AUTHOR> wu<PERSON><PERSON><PERSON><PERSON>
 * @date : 2021-02-03 16:17
 * 日常计划安颐无忧险种保险期间
 **/
@Getter
public enum DailyInsurePeriodEnum {
    TWENTYFIVEPERIOD("25","25年期"),

    THIRTYPERIOD("30","30年期"),

    THIRTYFIVEPERIOD("35","35年期"),

    FORTYPERIOD("40","40年期");

    private String code;
    private String value;





    DailyInsurePeriodEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static DailyInsurePeriodEnum getTypeByCode(String code) {
        for (DailyInsurePeriodEnum enums : DailyInsurePeriodEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (DailyInsurePeriodEnum enums : DailyInsurePeriodEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
