package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/16
 * 缴费频次
 */
@Getter
public enum PayFrequencyEnum {

    SINGLEPREMIUM("1","趸交"),

    YEARPAY("2","年交");

    private String code;
    private String value;





    PayFrequencyEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static PayFrequencyEnum getTypeByCode(String code) {
        for (PayFrequencyEnum enums : PayFrequencyEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (PayFrequencyEnum enums : PayFrequencyEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
