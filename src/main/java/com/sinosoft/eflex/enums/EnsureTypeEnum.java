package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/16
 * 福利投保类型
 */
@Getter
public enum EnsureTypeEnum {

    GRPENSURE("0","企事业单位投保"),

    STUDENTENSURE("1","在校学生投保"),

    SITEGRPENSURE("2","场地险投保");

    private String code;
    private String value;





    EnsureTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static EnsureTypeEnum getTypeByCode(String code) {
        for (EnsureTypeEnum enums : EnsureTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (EnsureTypeEnum enums : EnsureTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
