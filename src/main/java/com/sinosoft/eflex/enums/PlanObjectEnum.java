package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2021/3/26
 */
@Getter
public enum PlanObjectEnum {

    STAFF("1", "员工"),

    FAMILY("2", "家属");

    private String code;
    private String value;

    PlanObjectEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static PlanObjectEnum getTypeByCode(String code) {
        for (PlanObjectEnum enums : PlanObjectEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (PlanObjectEnum enums : PlanObjectEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }

}
