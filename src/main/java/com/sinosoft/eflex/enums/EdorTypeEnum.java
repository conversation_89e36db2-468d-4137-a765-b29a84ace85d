package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/11/20 15:47
 * @desc 保全类型
 */

@Getter
public enum EdorTypeEnum {

    EDORNI("NI", "新增被保险人"),

    EDORZT("ZT", "减少被保险人"),

    EDORNZ("NZ", "同质风险加减人"),

    EDORIC("IC", "被保人资料变更"),

    EDORBC("BC", "受益人资料变更"),

    EDORAC("AC", "投保单位资料变更");

    public final String code;
    public final String value;

    EdorTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }


    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static EdorTypeEnum getTypeByCode(String code) {
        for (EdorTypeEnum enums : EdorTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (EdorTypeEnum enums : EdorTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
