package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/16
 * 菜单组类型
 */
@Getter
public enum MenuGrpCodeTypeEnum {

    STAFF("1", "员工"),

    HR("2", "HR"),

    GROUPINSURANCE("3","团险岗"),

    OPERATION("4","运营岗");

    private String code;
    private String value;

    MenuGrpCodeTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static MenuGrpCodeTypeEnum getTypeByCode(String code) {
        for (MenuGrpCodeTypeEnum enums : MenuGrpCodeTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (MenuGrpCodeTypeEnum enums : MenuGrpCodeTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
