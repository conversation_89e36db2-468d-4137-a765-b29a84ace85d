package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/15
 * 有无医保
 */
@Getter
public enum IsHasEnum {

    NOTHAVE("0","无"),

    HAVE("1","有");

    private String code;
    private String value;

    IsHasEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static IsHasEnum getTypeByCode(String code) {
        for (IsHasEnum enums : IsHasEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (IsHasEnum enums : IsHasEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
