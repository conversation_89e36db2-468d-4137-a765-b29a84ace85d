package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/16
 * 职业类别
 */
@Getter
public enum OccupationTypeEnum {

    ONE_LEVEL("1","一级"),

    TWO_LEVEL("2","二级"),

    THREE_LEVEL("3","三级"),

    FOUR_LEVEL("4","四级"),

    FIVE_LEVEL("5","五级"),

    SIX_LEVEL("6","六级"),

    SEVEN_LEVEL("7","七级");

    private String code;
    private String value;

    OccupationTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static OccupationTypeEnum getTypeByCode(String code) {
        for (OccupationTypeEnum enums : OccupationTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (OccupationTypeEnum enums : OccupationTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
