package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/16
 * 缴费方式
 */
@Getter
public enum PaymentTypeEnum {

    COMPANIESPAY("1","企业缴纳"),

    WITHHOLDINGBYENTERPRISES("2","企业代扣代缴"),

    PERSONALDEDUCTION("3","混合缴费");

    private String code;
    private String value;





    PaymentTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static PaymentTypeEnum getTypeByCode(String code) {
        for (PaymentTypeEnum enums : PaymentTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (PaymentTypeEnum enums : PaymentTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
