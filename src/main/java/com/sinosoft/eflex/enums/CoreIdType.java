package com.sinosoft.eflex.enums;

import lombok.Getter;
/**
  *核心对应反洗钱证件类型
  */
@Getter
public enum CoreIdType {
    ID("身份证", 1,"0"),
    ID_TEMPORARY("临时身份证", 2,"C"),
    HOUSEHOLD_REGISTER("户口本", 3,"4"),
    BIRTH_CERTIFICATE("出生证", 4,"7"),
    ID_TAIWAN("台湾居民居住证", 5,"H"),
    ID_HK_MACAO("港澳居民居住证", 6,"G"),
    HK_MACAO_CN_PASS("港澳居民来往内地通行证", 7,"B"),
    CH_HK_MACAO_TAIWAN_PASS("中国居民来往港澳台通行证", 8,"F"),
    TAIWAN_CN_PASS("台湾居民来往大陆通行证", 9,"E"),
    POLICE_CERTIFICATE("警官证", 10,"D"),
    SOLDIER_CERTIFICATE("士兵证", 11,"A"),
    OFFICER_CERTIFICATE("军人证（军官证）", 12,"2"),
    ID_FOREIGNER("外国人永久居留身份证", 13,"I"),
    FOREIGNER_PASSPORT("外国公民护照", 14,"1"),
    CHINA_PASSPORT("中国护照", 15,"J"),
    DRIVER_LICENSE("驾照", 16,"3"),
    EMPLOYEE_CARD("工作证", 17,"7"),
    STUDENT_CARD("学生证", 18,"5"),
    OTHER_CARD("其他证件", 19,"8"),
    NO_CARD("无证件", 20,"9"),
    HOME_CERTIFICATE("回乡证件", 21,"");

    private final String label;
    private final Integer order;
    private final String coreId;

    CoreIdType(String label, Integer order,String coreId) {
        this.label = label;
        this.order = order;
        this.coreId = coreId;
    }

    //根据name获取coreid
    public static String getCoreIdByName(String name) {
        for (CoreIdType value : CoreIdType.values()) {
            if (value.label.equals(name)) {
                return value.coreId;
            }
        }
        return "9";
    }
    public static CoreIdType getNameByCoreId(String coreId) {
        for (CoreIdType value : CoreIdType.values()) {
            if (value.coreId.equals(coreId)) {
                return value;
            }
        }
        return CoreIdType.NO_CARD;
    }
}
