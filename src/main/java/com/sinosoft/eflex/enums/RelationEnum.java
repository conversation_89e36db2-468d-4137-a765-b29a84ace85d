package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/16
 * 关系标志
 */
@Getter
public enum RelationEnum {

    SELF("0", "本人"),

    PARENT("1", "父母"),

    SPOUSE("2", "配偶"),

    CHILDREN("3", "子女");

    private String code;
    private String value;





    RelationEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static RelationEnum getTypeByCode(String code) {
        for (RelationEnum enums : RelationEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (RelationEnum enums : RelationEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
