package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/15
 * 投保单位性质
 */
@Getter
public enum GrpNatureEnum {

    STATEORGANS("110", "国家机关"),

    PARTYANDGOVERNMENTORGANS("120", "党政机关"),

    SOCIALGROUPS("130", "社会团体"),

    GRASSROOTSMASSAUTONOMOUSORGANIZATIONS("140", "基层群众自治组织"),

    PUBLICHEALTH("210", "卫生事业"),

    SPORTSUNDERTAKINGS("220", "体育事业"),

    SOCIALUNDERTAKINGS("230", "社会事业"),

    EDUCATION("240", "教育事业"),

    CULTUREANDARTINDUSTRY("250", "文化艺术业"),

    RADIOFILMANDTELEVISIONINDUSTRY("260", "广播电影电视业"),

    SCIENTIFICRESEARCHINDUSTRY("270", "科学研究业"),

    COMPREHENSIVETECHNICALSERVICEINDUSTRY("280", "综合技术服务业"),

    STATEOWNED("310", "国有"),

    COLLECTIVE("320", "集体"),

    INDIVIDUAL("330", "个体"),

    PRIVATEGRP("340", "私有"),

    FOREIGNCAPITAL("350", "外资"),

    MIXEDOWNERSHIP("360", "混合所有制"),

    OTHER("900", "其他");


    private String code;
    private String value;

    GrpNatureEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static GrpNatureEnum getTypeByCode(String code) {
        for (GrpNatureEnum enums : GrpNatureEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (GrpNatureEnum enums : GrpNatureEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
