package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: wudezhong
 * @date: 2020/11/29 10:42
 * 保全批改状态
 */
@Getter
public enum EdorStateEnum {

    // 人员导入后状态即为申请中的状态
    APPLYING("01", "申请中"),

    // 保全申请确认成功后即为申请完成
    APPLYDONE("02", "申请完成");

    private String code;
    private String value;

    EdorStateEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static EdorStateEnum getTypeByCode(String code) {
        for (EdorStateEnum enums : EdorStateEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (EdorStateEnum enums : EdorStateEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
