package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/15
 * 计划状态
 */
@Getter
public enum DesignStatusEnum {

    TOCONFIGURE("0","待配置"),

    RTURNBACKGROUNDCONFIGURE("1","退回后台配置"),

    CONFIGUREACCOMPLISH("2","配置完成"),

    PENDINGREVIEWCONFIRMATION("3","待复核确认"),

    CHECKCOMPLETED("4","复核完成");

    private String code;
    private String value;

    DesignStatusEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static DesignStatusEnum getTypeByCode(String code) {
        for (DesignStatusEnum enums : DesignStatusEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (DesignStatusEnum enums : DesignStatusEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
