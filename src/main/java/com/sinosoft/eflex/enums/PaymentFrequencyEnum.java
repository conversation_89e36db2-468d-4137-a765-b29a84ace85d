package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * <AUTHOR> wu<PERSON><PERSON><PERSON><PERSON>
 * @date : 2021-02-03 16:18
 * 日常计划安颐无忧险种交费频次
 **/
@Getter
public enum PaymentFrequencyEnum {
    SINGLEPAYMENT("0","趸交"),

    MONTHLYPAYMENT("2","月交"),

    SEASONPAYMENT("3","季交"),

    HALFAYEARPAYMENT("4","半年交"),

    YEARPAYMENT("12","年交");

    private String code;
    private String value;





    PaymentFrequencyEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static PaymentFrequencyEnum getTypeByCode(String code) {
        for (PaymentFrequencyEnum enums : PaymentFrequencyEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (PaymentFrequencyEnum enums : PaymentFrequencyEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
