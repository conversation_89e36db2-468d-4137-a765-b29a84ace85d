package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/14
 * 受益人类别
 */
@Getter
public enum BnfKindEnum {

    SURVIVALBENEFICIARY("0","生存受益人"),

    BENEFICIARYDEATH("1","身故受益人"),

    OTHER("2","除身故保险金以外的其他保险金受益人");


    private String code;
    private String value;

    BnfKindEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }



    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static BnfKindEnum getTypeByCode(String code) {
        for (BnfKindEnum enums : BnfKindEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (BnfKindEnum enums : BnfKindEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
