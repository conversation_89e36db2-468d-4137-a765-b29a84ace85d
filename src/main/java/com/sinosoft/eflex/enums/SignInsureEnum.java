package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * 签单类型
 */
@Getter
public enum SignInsureEnum {

    BATCHINSURE("1", "批处理签单"),

    ARTIFICIALINSURE("2", "页面手动签单");

    private String code;
    private String value;

    SignInsureEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static SignInsureEnum getTypeByCode(String code) {
        for (SignInsureEnum enums : SignInsureEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (SignInsureEnum enums : SignInsureEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
