package com.sinosoft.eflex.enums;

import com.hqins.common.base.constants.Strings;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 证件类型枚举（同核心、电商）
 *
 * <AUTHOR>
 * @since 2024/12/13 10:29
 */
@Getter
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public enum CertTypeEnums {

    /**
     * 身份证
     */
    ID("身份证", "0"),
    /**
     * 外国公民护照
     */
    FOREIGNER_PASSPORT("外国公民护照", "1"),
    /**
     * 户口本
     */
    HOUSEHOLD_REGISTER("户口本", "4"),
    /**
     * 出生医学证明
     */
    BIRTH_CERTIFICATE("出生证", "7"),
    /**
     * 港澳居民来往内地通行证
     */
    HK_MACAO_CN_PASS("港澳居民来往内地通行证", "B"),
    /**
     * 台湾居民来往大陆通行证
     */
    TAIWAN_CN_PASS("台湾居民来往大陆通行证", "E"),
    /**
     * 港澳居民居住证
     */
    ID_HK_MACAO("港澳居民居住证", "G"),
    /**
     * 台湾居民居住证
     */
    ID_TAIWAN("台湾居民居住证", "H"),
    /**
     * 外国人永久居留身份证
     */
    ID_FOREIGNER("外国人永久居留身份证", "I"),
    /**
     * 中国护照
     */
    CHINA_PASSPORT("中国护照", "J"),

    OTHER("其他", "8")
    ;





    /**
     * 证件类型描述
     */
    private final String certTypeDesc;
    /**
     * 证件类型code
     */
    private final String certTypeCode;

    public String getCertTypeDesc() {
        return certTypeDesc;
    }

    public String getCertTypeCode() {
        return certTypeCode;
    }

    /**
     * 根据code获取描述信息
     *
     * @param code code
     * @return desc
     */
    public static String getDescByCode(String code) {
        CertTypeEnums enumByCode = getEnumByCode(code);
        return Objects.nonNull(enumByCode) ? enumByCode.getCertTypeDesc() : Strings.EMPTY;
    }

    /**
     * 获取枚举对象
     *
     * @param code code
     * @return 枚举对象
     */
    public static CertTypeEnums getEnumByCode(String code) {
        for (CertTypeEnums certTypeEnum : values()) {
            if (certTypeEnum.getCertTypeCode().equals(code)) {
                return certTypeEnum;
            }
        }

        return null;
    }

    /**
     * 校验
     *
     * @param code code
     * @return bool
     */
    public static boolean check(String code) {
        return Arrays.stream(values()).map(CertTypeEnums::getCertTypeCode)
                .anyMatch(c -> c.equals(code));
    }
}
