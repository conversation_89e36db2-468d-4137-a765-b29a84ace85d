package com.sinosoft.eflex.enums;

/**
 * <AUTHOR>
 * @date 2019/10/16 是否有效类型
 */
public enum StateEnum {

    VALID("1", "是/启用/锁定/有效/有/已同步/处理中/失败"),

    INVALID("0", "否/禁用/未锁定/无效/无/未同步/处理完成/成功");

    private final String code;
    private final String value;

    StateEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static StateEnum getTypeByCode(String code) {
        for (StateEnum enums : StateEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (StateEnum enums : StateEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
