package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/14
 * 承保状态
 */
@Getter
public enum ApprovedStateEnum {

    DECLINATURE("1","拒保"),

    POSTPONE("2","延期"),

    ADDFEEUNDERWRITE("3","加费承保"),

    SPECIALCONTRACT("4","特约承保"),

    FAILEDAUTOUNDERWRITING("5","未通过自动核保"),

    STANDARDINSURANCE("9","标准承保"),

    CANCELINSURE("a","撤保"),

    UNDERWRITINGCORRECTIONS("z","核保订正");


    private String code;
    private String value;

    ApprovedStateEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }



    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static ApprovedStateEnum getTypeByCode(String code) {
        for (ApprovedStateEnum enums : ApprovedStateEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (ApprovedStateEnum enums : ApprovedStateEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
