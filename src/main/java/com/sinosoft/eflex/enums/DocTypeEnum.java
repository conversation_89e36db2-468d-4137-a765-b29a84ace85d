package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2021/8/5
 * @desc 文件类型，如下文件类型为后期追溯整理，如在使用过程中文件类型已经确认，请及时更新！
 */
@Getter
public enum DocTypeEnum {

    STAFFIMAGE("0101", "人员相关影像"),

    GRPCONTACTIMAGE("0105", "企业联系人影像"),

    IMPORTPLAN("0201", "计划导入模板"),

    IMPORTSTAFF("0202", "员工清单导入模板"),

    TIMESETTLEMENT("0203", "团体健康保险投保提示文件"),

    EDORIMPORTINSUREPEOPLE("0301", "增加被保险人导入模版，减少被保险人导入模版"),

    EDORREDUINSUREDIMPORT("0302", "保全减少被保险人导入"),

    EDORADDINSUREDIMPORT("0303", "保全增加被保险人导入"),

    EDORADDINSUREDNOTICEZIP("0304", "保全增人告知文件"),

    EDORHOMOGENEOUSINSUREDIMPORT("0305", "同质风险加减人导入模板"),

    INSURENOTICE("0401", "投保须知，投保声明"),

    INSURANCECLAUSES("0501", "《保险条款》"),

    PRODUCTINFO("0502", "《产品信息披露》"),

    HEALTHNOTICE("0503", "横琴人寿健康告知配置详情导入清单，《人身保险投保提示书》"),

    STAFFLISTIMPORT("0504", "横琴人寿员工清单导入模板"),

    INSURANCECLAUSES1("0505", "《保险条款》"),

    INSUREDNOTICE("0601", "被保人告知书"),

    SPECIALAGREEMENT("0602", "特别约定"),

    BENEFICIARYCHANGECERTIFICATE("0603", "日常投保-受益人变更证明文件"),

    IDENTITYDOCUMENT("0604", "日常投保-身份证明文件"),

    SUPPORTINGMATERIALS("0605", "日常投保-证明材料"),

    UNKNOWN1("0801", ""),

    UNKNOWN2("0802", ""),

    UNKNOWN3("0803", ""),

    UNKNOWN4("0804", ""),

    UNKNOWN5("0805", ""),

    LIFEINSURANCE("1001", "人身保险投保提示书"),

    PLANLIST("312011", "计划清单"),

    ENSUREINSUREINFOEXPORT("0701", "福利投保信息查询");

    private String code;
    private String value;

    DocTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static DocTypeEnum getTypeByCode(String code) {
        for (DocTypeEnum enums : DocTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (DocTypeEnum enums : DocTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }

}
