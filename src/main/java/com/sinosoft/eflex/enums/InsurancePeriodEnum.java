package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/15
 * 固定计划保险期间
 */
@Getter
public enum InsurancePeriodEnum {

    VERYSHORTTERM("1","极短期"),

    ONEYEAR("2","一年期");

    private String code;
    private String value;

    InsurancePeriodEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static InsurancePeriodEnum getTypeByCode(String code) {
        for (InsurancePeriodEnum enums : InsurancePeriodEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (InsurancePeriodEnum enums : InsurancePeriodEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
