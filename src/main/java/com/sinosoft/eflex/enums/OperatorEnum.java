package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * <AUTHOR> wu<PERSON><PERSON>H<PERSON>
 * @date : 2021-03-15 10:02
 **/

@Getter
public enum OperatorEnum {
    ADD("0", "新增"),

    MODIFY("1", "修改");

    private String code;
    private String value;

    OperatorEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static OperatorEnum getTypeByCode(String code) {
        for (OperatorEnum enums : OperatorEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (OperatorEnum enums : OperatorEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
