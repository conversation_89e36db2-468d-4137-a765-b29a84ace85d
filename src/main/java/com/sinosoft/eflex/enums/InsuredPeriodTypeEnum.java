package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: wudezhong
 * @date: 2022/2/15
 * 保险期间类型
 */
@Getter
public enum InsuredPeriodTypeEnum {

    SHORTINSURED("1", "极短期"),

    YEARINSURED("2", "一年期");

    private String code;
    private String value;

    InsuredPeriodTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static InsuredPeriodTypeEnum getTypeByCode(String code) {
        for (InsuredPeriodTypeEnum enums : InsuredPeriodTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (InsuredPeriodTypeEnum enums : InsuredPeriodTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
