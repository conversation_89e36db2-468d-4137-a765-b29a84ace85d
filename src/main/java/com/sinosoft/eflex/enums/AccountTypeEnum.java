package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/14
 * 保全试算结算类型
 */
@Getter
public enum AccountTypeEnum {

    TIMESETTLEMENT("1","实时结算"),

    REGULARSETTLEMENT("2","定期结算");

    private String code;
    private String value;

    AccountTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static AccountTypeEnum getTypeByCode(String code) {
        for (AccountTypeEnum enums : AccountTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (AccountTypeEnum enums : AccountTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }

}
