package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/16
 * 计划类型
 */
@Getter
public enum PlanTypeEnum {

    IMMOBILIZATIONPLAN("0", "固定计划"),

    EFLEXPLAN("1", "弹性计划"),

    DAILYPLAN("2", "日常计划");

    private final String code;
    private final String value;


    PlanTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static PlanTypeEnum getTypeByCode(String code) {
        for (PlanTypeEnum enums : PlanTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (PlanTypeEnum enums : PlanTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
