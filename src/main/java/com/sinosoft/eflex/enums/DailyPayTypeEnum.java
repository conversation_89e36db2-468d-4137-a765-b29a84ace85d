package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/15
 * 计划类型
 */
@Getter
public enum DailyPayTypeEnum {

    ENTERPRISESPAY("0","企业定期结算"),

    PERSONPAY("1","个人实时支付");

    private String code;
    private String value;

    DailyPayTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static DailyPayTypeEnum getTypeByCode(String code) {
        for (DailyPayTypeEnum enums : DailyPayTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (DailyPayTypeEnum enums : DailyPayTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
