package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2020/12/24
 * 短信模板编号
 */
@Getter
public enum EmailTemplateNoEnum {

    STAFF_BENEFITS_015("STAFF_BENEFITS_015","尊敬的客户：福利计划{ensure_name}被保险人{person_name}健康告知不满足投保要求，请联系{per_name}（{per_mobilephone}）协助处理！");


    private String code;
    private String value;


    EmailTemplateNoEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static ScaleTypeEnum getTypeByCode(String code) {
        for (ScaleTypeEnum enums : ScaleTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (ScaleTypeEnum enums : ScaleTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }

}
