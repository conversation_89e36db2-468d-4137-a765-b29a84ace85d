package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/16
 * 企业规模类型
 */
@Getter
public enum ScaleTypeEnum {

    LARGESCALE("1","大型"),

    MIDDLESIZED("2","中型"),

    SMALLSIZE("3","小型"),

    MINIATURE("4","微型");

    private String code;
    private String value;





    ScaleTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static ScaleTypeEnum getTypeByCode(String code) {
        for (ScaleTypeEnum enums : ScaleTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (ScaleTypeEnum enums : ScaleTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
