package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/16
 * 保全试算费用来源
 */
@Getter
public enum PremSourceEnum {

    ENTERPRISE("1","企业"),

    PERSON("2","个人");

    private String code;
    private String value;





    PremSourceEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static PremSourceEnum getTypeByCode(String code) {
        for (PremSourceEnum enums : PremSourceEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (PremSourceEnum enums : PremSourceEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
