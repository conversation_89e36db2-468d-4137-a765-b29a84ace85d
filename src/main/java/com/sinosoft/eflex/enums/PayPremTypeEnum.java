package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/16
 * 缴费方式
 */
@Getter
public enum PayPremTypeEnum {

    UNITALLPAY("1","单位全缴"),

    UNITPAY("2","单位代扣"),

    MIXEDPAY("3","混合缴费");

    private String code;
    private String value;





    PayPremTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static PayPremTypeEnum getTypeByCode(String code) {
        for (PayPremTypeEnum enums : PayPremTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (PayPremTypeEnum enums : PayPremTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
