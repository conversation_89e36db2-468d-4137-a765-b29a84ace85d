package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/16 菜单组类型
 */
@Getter
public enum UserTypeEnum {

    PERSON("1","个人"),

    ENTERPRISEHR("2","企业HR"),

    // 新增初审岗（团险岗）和复审岗（运营岗）的角色 add by wudezhong 2021.7.20
    AUDITWORK("3", "初审岗"),

    BASEOPERATION("4", "复审岗（总公司）"),

    OPERATION("5", "复审岗（分公司）");

    private String code;
    private String value;

    UserTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static UserTypeEnum getTypeByCode(String code) {
        for (UserTypeEnum enums : UserTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (UserTypeEnum enums : UserTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
