package com.sinosoft.eflex.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2023/8/17 17:18
 */
@Getter
@AllArgsConstructor
public enum InterfaceType {


    NATURAL_PERSONS("自然人"),
    NON_NATURAL_PERSON("非自然人");

    private final String label;


    public String getLabel() {
        return this.label;
    }

    public static boolean isValid(String name) {
        return get(name) != null;
    }

    private static InterfaceType get(String name) {
        for (InterfaceType value : InterfaceType.values()) {
            if (value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }

}
