package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2021/9/13
 * @desc 试算状态
 */
@Getter
public enum TrialStateEnum {

    INSUREDPREPARE("0", "人员导入"),

    TRIALDONE("1", "试算完成"),

    APPLYDONE("2", "申请完成");

    private String code;
    private String value;

    TrialStateEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static TrialStateEnum getTypeByCode(String code) {
        for (TrialStateEnum enums : TrialStateEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (TrialStateEnum enums : TrialStateEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }

}
