package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/16
 * 定期结算周期
 */
@Getter
public enum TermSettlementCycleEnum {

    MONTH("1","月"),

    YEAR("12","年"),

    SEASON("3","季"),

    HALFYEAR("6","半年");

    private String code;
    private String value;





    TermSettlementCycleEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static TermSettlementCycleEnum getTypeByCode(String code) {
        for (TermSettlementCycleEnum enums : TermSettlementCycleEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (TermSettlementCycleEnum enums : TermSettlementCycleEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
