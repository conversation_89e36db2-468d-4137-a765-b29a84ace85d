package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * 企业付款方式
 */
@Getter
public enum GrpPayTypeEnum {

    ACTIVETRANSFER("1", "现金"),

    BANKTRANSFER("4", "银行转账"),

    CASH("D", "主动转账");

    private String code;
    private String value;

    GrpPayTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static GrpPayTypeEnum getTypeByCode(String code) {
        for (GrpPayTypeEnum enums : GrpPayTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (GrpPayTypeEnum enums : GrpPayTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
