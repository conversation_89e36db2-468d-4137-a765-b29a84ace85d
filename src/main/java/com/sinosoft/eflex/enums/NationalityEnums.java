package com.sinosoft.eflex.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 国籍枚举
 *
 * <AUTHOR>
 * @since 2024/12/11
 */
@Getter
@AllArgsConstructor(access = AccessLevel.PUBLIC)
public enum NationalityEnums {

    AFG("AFG", "阿富汗"),
    AHO("AHO", "荷属安的列斯"),
    ALB("ALB", "阿尔巴尼亚"),
    ALG("ALG", "阿尔及利亚"),
    AND("AND", "安道尔"),
    ANG("ANG", "安果拉"),
    ARG("ARG", "阿根廷"),
    ARM("ARM", "亚美尼亚"),
    ARU("ARU", "阿鲁巴岛"),
    AUS("AUS", "澳大利亚"),
    AUT("AUT", "奥地利"),
    AZE("AZE", "阿塞拜疆"),
    BAN("BAN", "孟加拉国"),
    BAR("BAR", "巴巴多斯"),
    BAS("BAS", "巴西"),
    BEL("BEL", "比利时"),
    BER("BER", "百慕大"),
    BIH("BIH", "波斯尼亚-黑塞哥维"),
    BLR("BLR", "白俄罗斯"),
    BOL("BOL", "玻利维亚"),
    BOT("BOT", "博茨瓦纳"),
    BRN("BRN", "巴林"),
    BRU("BRU", "文莱"),
    BUL("BUL", "保加利亚"),
    CAN("CAN", "加拿大"),
    CHI("CHI", "智利"),
    CHN("CHN", "中国"),
    CIV("CIV", "象牙海岸"),
    COL("COL", "哥伦比亚"),
    CRC("CRC", "哥斯达黎加"),
    CRO("CRO", "克罗地亚"),
    CUB("CUB", "古巴"),
    CYP("CYP", "塞浦路斯"),
    CZE("CZE", "捷克共和国"),
    DEN("DEN", "丹麦"),
    DEU("DEU", "德国"),
    DG("DG", "加纳"),
    DOM("DOM", "多米尼加"),
    ECU("ECU", "厄瓜多尔"),
    EGY("EGY", "埃及"),
    ENG("ENG", "英国"),
    ESP("ESP", "西班牙"),
    EST("EST", "爱沙尼亚"),
    ETH("ETH", "埃塞俄比亚"),
    FAI("FAI", "法罗群岛"),
    FIN("FIN", "芬兰"),
    FRA("FRA", "法国"),
    GCI("GCI", "根西"),
    GEO("GEO", "格鲁吉亚"),
    GRE("GRE", "希腊"),
    HK("HKG", "中国香港"),
    HON("HON", "洪都拉斯"),
    HUN("HUN", "匈牙利"),
    INA("INA", "印度尼西亚"),
    IND("IND", "印度"),
    IRI("IRI", "伊朗"),
    IRL("IRL", "爱尔兰"),
    IRQ("IRQ", "伊拉克"),
    ISL("ISL", "冰岛"),
    ISR("ISR", "以色列"),
    ISV("ISV", "美属维京群岛"),
    ITA("ITA", "意大利"),
    IVB("IVB", "英属维尔京群岛"),
    JAM("JAM", "牙买加"),
    JAN("JAN", "日本"),
    JCI("JCI", "泽西岛"),
    KAZ("KAZ", "哈萨克斯坦"),
    KEN("KEN", "肯尼亚"),
    KGZ("KGZ", "塔吉克斯坦"),
    KOR("KOR", "韩国"),
    LAT("LAT", "拉脱维亚"),
    LBA("LBA", "利比亚"),
    LIB("LIB", "黎巴嫩"),
    LIE("LIE", "列支敦士登"),
    LTU("LTU", "立陶宛"),
    LUX("LUX", "卢森堡"),
    MAR("MAR", "摩洛哥"),
    MAW("MAW", "马拉维"),
    MEX("MEX", "墨西哥"),
    MGL("MGL", "蒙古"),
    MKD("MKD", "马其顿"),
    MLT("MLT", "马尔他"),
    MNC("MNC", "摩纳哥"),
    MO("MAC", "中国澳门"),
    MRI("MRI", "毛利求斯"),
    MY("MY", "马来西亚"),
    NAM("NAM", "纳米比亚"),
    NCA("NCA", "尼加拉瓜"),
    NGR("NGR", "尼日利亚"),
    NL("NL", "荷兰"),
    NO("NO\t", "挪威"),
    NZL("NZL", "新西兰"),
    OTH("OTH", "其他"),
    PAN("PAN", "巴拿马"),
    PAR("PAR", "巴拉圭"),
    PER("PER", "秘鲁"),
    PHL("PHL", "菲律宾"),
    PL("PL", "波兰"),
    PLE("PLE", "巴勒斯坦"),
    PNG("PNG", "巴布亚新几内亚"),
    POR("POR", "葡萄牙"),
    QAT("QAT", "卡塔尔"),
    ROM("ROM", "罗马尼亚"),
    RUS("RUS", "俄罗斯"),
    RWA("RWA", "卢旺达"),
    SCO("SCO", "苏格兰"),
    SEY("SEY", "塞舌尔"),
    SFA("SFA", "南非"),
    SG("SG", "新加坡"),
    SLK("SLK", "斯里兰卡"),
    SLO("SLO", "斯洛文尼亚"),
    SMR("SMR", "圣马力诺"),
    SOM("SOM", "索马里"),
    SUD("SUD", "苏丹"),
    SUI("SUI", "瑞士"),
    SUR("SUR", "苏里南"),
    SVK("SVK", "斯洛伐克"),
    SWE("SWE", "瑞典"),
    SYR("SYR", "叙利亚"),
    THA("THA", "泰国"),
    TJK("TJK", "塔吉克斯坦"),
    TKM("TKM", "土库曼斯坦"),
    TO("TO", "汤加"),
    TRI("TRI", "特立尼达和多巴哥"),
    TUN("TUN", "突尼斯"),
    TUR("TUR", "土尔其"),
    TW("TWN", "中国台湾"),
    UAE("UAE", "阿拉伯联合酋长国"),
    UGA("UGA", "乌干达"),
    UKR("UKR", "乌克兰"),
    URU("URU", "乌拉圭"),
    USA("USA", "美国"),
    UZB("UZB", "乌兹别克斯坦"),
    VEN("VEN", "委内瑞拉"),
    VIE("VIE", "越南"),
    WLS("WLS", "威尔士"),
    YEM("YEM", "也门"),
    YUG("YUG", "南斯拉夫"),
    ZAM("ZAM", "赞比亚"),
    ZIM("ZIM", "津巴布韦");

    private final String code;
    private final String value;


    public static NationalityEnums getByCode(String code) {
        if (code != null && code.length() > 0) {
            NationalityEnums[] var1 = values();
            int var2 = var1.length;

            for (NationalityEnums mode : var1) {
                if (mode.code.equals(code)) {
                    return mode;
                }
            }

            return null;
        } else {
            return null;
        }
    }

    public static String getValueByCode(String code) {
        if (code != null && code.length() > 0) {
            NationalityEnums[] var1 = values();
            int var2 = var1.length;

            for (NationalityEnums mode : var1) {
                if (mode.code.equals(code)) {
                    return mode.getValue();
                }
            }

            return null;
        } else {
            return null;
        }
    }

    public String getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }
}
