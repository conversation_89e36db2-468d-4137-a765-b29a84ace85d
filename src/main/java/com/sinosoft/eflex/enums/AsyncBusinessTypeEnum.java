package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/9/7
 * @desc 异步处理业务类型
 */
@Getter
public enum AsyncBusinessTypeEnum {

    EDORADDINSUREDIMPORT("NIImport", "保全增人导入"),

    NITRIALIO("NITrialIO", "保全增人试算"),

    NICONFIRMIO("NIConfirmIO", "保全增人申请确认");

    private String code;
    private String value;

    AsyncBusinessTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static AsyncBusinessTypeEnum getTypeByCode(String code) {
        for (AsyncBusinessTypeEnum enums : AsyncBusinessTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (AsyncBusinessTypeEnum enums : AsyncBusinessTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
