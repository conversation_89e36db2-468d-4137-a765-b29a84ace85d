package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/14
 * 计划类型
 */
@Getter
public enum AuditTypeEnum {

    ADDPLAN("0","新增计划"),

    UPDATEPLAN("1","变更计划");

    private String code;
    private String value;

    AuditTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static AuditTypeEnum getTypeByCode(String code) {
        for (AuditTypeEnum enums : AuditTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (AuditTypeEnum enums : AuditTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
