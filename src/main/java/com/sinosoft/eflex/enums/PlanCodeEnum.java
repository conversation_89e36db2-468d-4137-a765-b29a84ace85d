package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/16
 * 计划代码
 */
@Getter
public enum PlanCodeEnum {

    OVERBALANCEVERSIONS("ID6380","超值版"),

    DISCOUNTSVERSIONS("ID6381","优惠版"),

    INTIMATEVERSIONS("ID6382","贴心版"),

    LUXURYVERSIONS("ID6383","豪华版");

    private String code;
    private String value;





    PlanCodeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static PlanCodeEnum getTypeByCode(String code) {
        for (PlanCodeEnum enums : PlanCodeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (PlanCodeEnum enums : PlanCodeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
