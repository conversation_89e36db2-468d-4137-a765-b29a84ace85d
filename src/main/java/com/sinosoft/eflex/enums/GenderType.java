package com.sinosoft.eflex.enums;

import com.hqins.common.base.enums.Gender;
import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum GenderType {

    UNKNOWN("保密","3",Gender.UNKNOWN,"2"),
    MALE("男","1",Gender.MALE,"0"),
    FEMALE("女","2",Gender.FEMALE,"1");

    private final String label;
    private final String id;
    private final Gender gender;
    private final String coreId;

    public String getLabel() {
        return this.label;
    }

    public static boolean isValid(String name) {
        return get(name) != null;
    }

    private static GenderType get(String name) {
        for (GenderType value : GenderType.values()) {
            if (value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }

    public  static Gender getGenderById(String id) {
        for (GenderType value : GenderType.values()) {
            if (value.getId().equals(id)) {
                return value.getGender();
            }
        }
        return null;
    }

    public  static GenderType getGenderByCoreId(String coreId) {
        for (GenderType value : GenderType.values()) {
            if (value.getCoreId().equals(coreId)) {
                return value;
            }
        }
        return GenderType.UNKNOWN;
    }
}
