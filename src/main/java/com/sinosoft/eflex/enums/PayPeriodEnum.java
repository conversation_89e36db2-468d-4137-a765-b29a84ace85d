package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/16
 * 缴费期间
 */
@Getter
public enum PayPeriodEnum {

    ONETIMECLEARING("01","一次性交清"),

    FIVEYEARPAY("02","5年交"),

    TENYEARPAY("03","10年交"),

    TWENTYYEARPAY("04","20年交");

    private String code;
    private String value;





    PayPeriodEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static PayPeriodEnum getTypeByCode(String code) {
        for (PayPeriodEnum enums : PayPeriodEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (PayPeriodEnum enums : PayPeriodEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
