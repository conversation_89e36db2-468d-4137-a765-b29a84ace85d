package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/16
 * 参加社会统筹标志
 */
@Getter
public enum SociologyPlanSignEnum {

    NO("0","否"),

    IS("1","是");

    private String code;
    private String value;





    SociologyPlanSignEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static SociologyPlanSignEnum getTypeByCode(String code) {
        for (SociologyPlanSignEnum enums : SociologyPlanSignEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (SociologyPlanSignEnum enums : SociologyPlanSignEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
