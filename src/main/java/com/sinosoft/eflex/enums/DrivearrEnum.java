package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/15
 * 驾驶证类型
 */
@Getter
public enum DrivearrEnum {

    A1_DRIVINGLICENCE("01","A1驾驶证"),

    A2_DRIVINGLICENCE("02","A2驾驶证"),

    A3_DRIVINGLICENCE("03","A3驾驶证"),

    B1_DRIVINGLICENCE("04","B1驾驶证"),

    B2_DRIVINGLICENCE("05","B2驾驶证"),

    C1_DRIVINGLICENCE("06","C1驾驶证"),

    C2_DRIVINGLICENCE("07","C2驾驶证"),

    C3_DRIVINGLICENCE("08","C3驾驶证"),

    C4_DRIVINGLICENCE("09","C4驾驶证"),

    D_DRIVINGLICENCE("10","D驾驶证"),

    E_DRIVINGLICENCE("11","E驾驶证"),

    F_DRIVINGLICENCE("12","F驾驶证"),

    M_DRIVINGLICENCE("13","M驾驶证"),

    N_DRIVINGLICENCE("14","N驾驶证"),

    P_DRIVINGLICENCE("15","P驾驶证");

    private String code;
    private String value;

    DrivearrEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static DrivearrEnum getTypeByCode(String code) {
        for (DrivearrEnum enums : DrivearrEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (DrivearrEnum enums : DrivearrEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
