package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2020/12/24
 * 短信模板编号
 */
@Getter
public enum SMSTemplateNoEnum {

//    STAFF_BENEFITS_001("STAFF_BENEFITS_001", "尊敬的用户，您收到一条团体保单开具发票申请，保单号{grp_cont_no}；收件地址{get_address}；邮编{zipcode}；收件人{receiver}；联系电话{tel_phone}；请于{deal_date}前联系财务人员处理。"),

    STAFF_BENEFITS_002("STAFF_BENEFITS_002", "尊敬的客户：感谢您注册横琴人寿员工福利平台！您为【{grp_name}】提交的注册信息已通过审核，登录账户为您的证件号码，初始密码为您证件号码后六位。如有疑问，请随时与您的业务经理联系。"),

    STAFF_BENEFITS_003("STAFF_BENEFITS_003", "尊敬的客户：您为【{grp_name}】提交的注册信息已通过审核。您可通过HR登录账号及密码登录横琴人寿员工福利平台进行查看；如有疑问，请随时与您的业务经理联系。"),

    STAFF_BENEFITS_004("STAFF_BENEFITS_004", "尊敬的客户：您为【{grp_name}】提交的注册信息未通过审核，未通过原因:{audit_opinion}，请登录平台重新注册!"),

    STAFF_BENEFITS_005("STAFF_BENEFITS_005", "尊敬的客户：您在横琴人寿员工福利平台定制的[{ensure_name}]福利计划复核已通过，请尽快通知参保人员于{ensure_end_date}前登录平台进行投保，如有疑问，请随时与您的业务经理联系。"),

    STAFF_BENEFITS_006("STAFF_BENEFITS_006", "尊敬的客户：您在横琴人寿员工福利平台定制的[{ensure_name}]福利计划审核未通过，请您登录平台修改福利计划内容后重新提交审核，如有疑问，请随时与您的业务经理联系。"),

    STAFF_BENEFITS_007("STAFF_BENEFITS_007", "【横琴人寿】您的验证码是{validate_code}，如非本人操作，请忽略本信息。该验证码将在5分钟后失效。"),

    STAFF_BENEFITS_008("STAFF_BENEFITS_008", "尊敬的客户：您为{grp_name}提交的注册信息，管理员在审核时进行了部分修改，请悉知。"),

    STAFF_BENEFITS_009("STAFF_BENEFITS_009", "【横琴人寿】您的登录密码已重置，新密码为{password}，请重新登录。"),

    STAFF_BENEFITS_010("STAFF_BENEFITS_010", "尊敬的用户，您为{grp_name}企业定制的日常计划已审核通过，投保单号: {tprtno}，交费金额: {totalPrem}元。请联系企业HR进行缴费。"),

    STAFF_BENEFITS_011("STAFF_BENEFITS_011", "尊敬的客户：横琴人寿员工福利平台已配置【{grp_name}】[{ensure_name}]福利计划，请您登录平台审核配置信息并提供参保人员信息等，如有疑问，请随时与您的业务经理联系。"),

//    STAFF_BENEFITS_012("STAFF_BENEFITS_012", "尊敬的用户，您好，【{grp_name}】企业HR提交了保全申请，保全申请号【{edor_app_no}】，请及时登录系统进行审批。"),

    STAFF_BENEFITS_013("STAFF_BENEFITS_013", "尊敬的用户，您申请的横琴人寿员工福利平台{roleName}用户权限已配置，登录账号为您的身份证号，初始密码: {userInitPassword}，请尽快登录平台修改密码。"),

    STAFF_BENEFITS_014("STAFF_BENEFITS_014", "尊敬的用户，您在横琴人寿员工福利平台的用户权限已变更为{roleName}。"),

    STAFF_BENEFITS_016("STAFF_BENEFITS_016", "尊敬的客户，您在横琴人寿员工福利平台提交的{edor_type}的保全项，保费试算已完成，请及时进行保全申请确认！"),

    STAFF_BENEFITS_017("STAFF_BENEFITS_017", "尊敬的客户，您在横琴人寿员工福利平台提交的{edor_type}的保全项，保全申请确认已完成，可在变更信息查询中查询结果！");

    private String code;
    private String value;


    SMSTemplateNoEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static SMSTemplateNoEnum getTypeByCode(String code) {
        for (SMSTemplateNoEnum enums : SMSTemplateNoEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (SMSTemplateNoEnum enums : SMSTemplateNoEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }

}
