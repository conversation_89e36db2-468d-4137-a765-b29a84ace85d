package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/16
 * 关系标志
 */
@Getter
public enum RelationMarkEnum {

    MYSELF("0","本人"),

    TREATYREINSURANCE("1","父母"),

    POINTSINTHE("2","配偶"),

    COMPANYSELFHOLD("3","子女");

    private String code;
    private String value;





    RelationMarkEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static RelationMarkEnum getTypeByCode(String code) {
        for (RelationMarkEnum enums : RelationMarkEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (RelationMarkEnum enums : RelationMarkEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
