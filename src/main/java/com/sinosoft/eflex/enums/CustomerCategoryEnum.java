package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/15
 * 客户类别
 */
@Getter
public enum CustomerCategoryEnum {

    BUSINESSCUSTOMER("2","企业客户"),

    NOBUSINESSCUSTOMER("3","非企业组织客户");

    private String code;
    private String value;

    CustomerCategoryEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static CustomerCategoryEnum getTypeByCode(String code) {
        for (CustomerCategoryEnum enums : CustomerCategoryEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (CustomerCategoryEnum enums : CustomerCategoryEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
