package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * @author: zhangchunhang
 * @date: 2020/12/15
 * 新证件类型
 */
@Getter
public enum IDTypeNewEnum {

    IDCARD("0","身份证"),

    PASSPORTOFFOREIGNCITIZEN("1","外国公民护照"),

    HOUSEHOLDREGISTER("4","户口本"),

    BIRTHCERTIFICATE("7","出生证"),

    HONGKONGANDMACAOCONTINENTALPASS("B","港澳居民来往大陆通行证"),

    TAIWANCONTINENTALPASS("E","台湾居民来往大陆通行证"),

    HONGKONGANDMACAORESIDENCEPERMIT("G","港澳居民居住证"),

    TAIWANRESIDENCEPERMIT("H","台湾居民居住证"),

    RESIDENTIDENTITYCARDFORFOREIGNERS("I","外国人永久居留身份证");

    private String code;
    private String value;

    IDTypeNewEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static IDTypeNewEnum getTypeByCode(String code) {
        for (IDTypeNewEnum enums : IDTypeNewEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (IDTypeNewEnum enums : IDTypeNewEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
