package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2022/1/7
 * @desc
 */
@Getter
public enum OperationTypeEnum {

    add("add", "新增"),

    update("update", "修改");

    private String code;
    private String value;

    OperationTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static OperationTypeEnum getTypeByCode(String code) {
        for (OperationTypeEnum enums : OperationTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (OperationTypeEnum enums : OperationTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }
}
