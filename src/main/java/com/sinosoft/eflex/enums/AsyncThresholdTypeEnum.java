package com.sinosoft.eflex.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2021/11/25
 * @desc 异步阈值类型表
 */
@Getter
public enum AsyncThresholdTypeEnum {

    EDORNITRIALIO("01", "非场地险保全增人试算"),

    EDORNITRIALIOSITE("02", "场地险保全增人试算"),

    EDORNICONFIRMIO("03", "保全申请确认");

    private String code;
    private String value;

    AsyncThresholdTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过Code取枚举
     *
     * @param code
     * @return
     */
    public static AsyncThresholdTypeEnum getTypeByCode(String code) {
        for (AsyncThresholdTypeEnum enums : AsyncThresholdTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取描述
     *
     * @param code
     * @return
     */
    public static String getValueByCode(String code) {
        for (AsyncThresholdTypeEnum enums : AsyncThresholdTypeEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getValue();
            }
        }
        return "";
    }

}
