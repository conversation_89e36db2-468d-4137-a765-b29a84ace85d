package com.sinosoft.eflex.service;

import com.sinosoft.eflex.dao.FCEnsureConfigMapper;
import com.sinosoft.eflex.dao.FcBusinessProDutyGrpObjectMapper;
import com.sinosoft.eflex.dao.PremTrailMapper;
import com.sinosoft.eflex.model.DailyPremTrail;
import com.sinosoft.eflex.model.FcDefaultRiskGrade;
import com.sinosoft.eflex.model.insureEflexPlanPage.SelectMinAmntDefaultReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service("PremTrailService")
public class PremTrailService {

    //日志
    private final Logger log = LoggerFactory.getLogger(PremTrailService.class);

    @Autowired
    private PremTrailMapper premTrailMapper;
    @Autowired
    private FcBusinessProDutyGrpObjectMapper fcBusinessProDutyGrpObjectMapper;
    @Autowired
    private FCEnsureConfigMapper fcEnsureConfigMapper;

    public Map<String, Object> premTrail(Map<String, Object> map) {
        //定义返回对象
        Map<String, Object> resultMap = new HashMap<>();
        try {
            //试算规则校验
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "保费测算不支持此险种！");
            String isAmount = map.get("isAmount").toString();
            if ("1".equals(isAmount)) {
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "保费测算成功！");
                resultMap.put("Prem", "0");
                return resultMap;
            }
            String riskCode = map.get("RiskCode").toString();
            if (riskCode == null || riskCode.equals("")) {
                resultMap.put("message", "险种编码不能为空");
                return resultMap;
            }
            //保费试算逻辑
            switch (riskCode) {
                case "12020":
                    resultMap = premTrial_12020(map);
                    break;//横琴团体定期寿险条款
                case "17030":
                    resultMap = premTrial_17030(map);
                    break;//横琴门诊急诊团体医疗保险条款
                case "15030":
                    resultMap = premTrial_15030(map);
                    break;//横琴团体意外伤害保险条款
                case "15040":
                    resultMap = premTrial_15040(map);
                    break;//横琴意外伤害团体医疗保险
                case "15060":
                    resultMap = premTrial_15060(map);
                    break;//横琴意外伤害住院津贴团体医疗保险
                case "16040":
                    resultMap = premTrial_16040(map);
                    break;//横琴团体重大疾病保险
                case "16490":
                    resultMap = premTrial_16490(map);
                    break;//横琴琴逸重大疾病保险
                case "17020":
                    resultMap = premTrial_17020(map);
                    break;//横琴住院津贴团体医疗保险条款
                case "17010":
                    resultMap = premTrial_17010(map);
                    break;//横琴住院团体医疗保险条款
                case "17050":
                    resultMap = premTrial_17050(map);
                    break;//横琴尊享团体补充医疗保险
                case "15070":
                    resultMap = premTrial_15070(map);
                    break;//横琴综合交通团体意外伤害保险条款
                default:
                    break;
            }
            String code = resultMap.get("code").toString();
            if (code.equals("200")) {
                if (riskCode.matches("^1(702|506|507)0$")) {
                    Map<String, Object> dataMap = (Map<String, Object>) resultMap.get("Prem");
                    String prem = dataMap.get("Prem").toString();
                    dataMap.put("Prem", prem.replaceAll(",", ""));
                    List<Map<String, String>> premList = (List<Map<String, String>>) dataMap.get("PremDetailList");
                    if (premList != null && premList.size() > 0) {
                        for (Map<String, String> optPremMap : premList) {
                            String optPrem = optPremMap.get("Prem").toString();
                            optPremMap.put("Prem", optPrem.replaceAll(",", ""));
                        }
                    }
                } else {
                    String prem = resultMap.get("Prem").toString();
                    resultMap.put("Prem", prem.replaceAll(",", ""));
                }
            }
        } catch (Exception e) {
            log.info("保费测算异常！", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "保费测算异常！" + e.getMessage());
        }
        return resultMap;
    }

    public Map<String, Object> premTrial_12020(Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", false);
        resultMap.put("code", "500");
        String amountGrageCode = map.get("AmountGrageCode").toString();//保额档次编码
        String cvaliDate = map.get("CvaliDate").toString();//保单生效日
        String birthDay = map.get("BirthDay").toString();//出生日期
        String sex = map.get("Sex").toString();//性别
        Map<String, String> checkMap = new HashMap<String, String>();
        checkMap.put("保额档次编码", amountGrageCode);
        checkMap.put("保单生效日", cvaliDate);
        checkMap.put("出生日期", birthDay);
        checkMap.put("性别", sex);
        String errMsg = checkIsNull(checkMap);
        if (!errMsg.equals("")) {
            resultMap.put("message", errMsg);
        } else {
            boolean checkCvaliDate = checkDateFormat(cvaliDate);
            boolean checkBirthDay = checkDateFormat(birthDay);
            boolean checkSex = checkSexAndJoinMedProtectFormat(sex);
            if (checkCvaliDate && checkBirthDay && checkSex) {
                List<String> premList_12020 = premTrailMapper.getPrem_12020(map);
                if (premList_12020 == null || premList_12020.size() <= 0) {
                    resultMap.put("message", "保费测算失败!");
                } else {
                    String premTrail = premList_12020.get(0);
                    if (premTrail == null || premTrail.equals("")) {
                        resultMap.put("message", "险种12020保费测算失败!");
                    } else {
                        resultMap.put("Prem", premTrail);
                        resultMap.put("message", "保费测算成功!");
                        resultMap.put("success", true);
                        resultMap.put("code", "200");
                    }
                }
            } else {
                if (!checkCvaliDate) {
                    errMsg += "".equals(errMsg) ? "保费测算失败，保单生效日格式错误" : "，保单生效日格式错误";
                }
                if (!checkBirthDay) {
                    errMsg += "".equals(errMsg) ? "保费测算失败，出生日期格式错误" : "，出生日期格式错误";
                }
                if (!checkSex) {
                    errMsg += "".equals(errMsg) ? "保费测算失败，性别格式错误" : "，性别格式错误";
                }
                resultMap.put("message", errMsg);
            }
        }
        return resultMap;
    }

    public Map<String, Object> premTrial_17030(Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", false);
        resultMap.put("code", "500");
        String joinMedProtect = map.get("JoinMedProtect").toString();//有无医保
        String amountGrageCode = map.get("AmountGrageCode").toString();//保额档次编码
        String deductible = map.get("Deductible").toString();//免赔额
        String cvaliDate = map.get("CvaliDate").toString();//保单生效日
        String birthDay = map.get("BirthDay").toString();//出生日期
        String occupationType = map.get("OccupationType").toString();//职业类别
        String insureCount = map.get("InsureCount").toString();//投保人数
        String compensationRatio = map.get("CompensationRatio").toString();//赔付比例
        Map<String, String> checkMap = new HashMap<String, String>();
        checkMap.put("有无医保", joinMedProtect);
        checkMap.put("保额档次编码", amountGrageCode);
        checkMap.put("免赔额", deductible.replaceAll(",", ""));
        checkMap.put("保单生效日", cvaliDate);
        checkMap.put("出生日期", birthDay);
        checkMap.put("职业类别", occupationType);
        checkMap.put("投保人数", insureCount);
        checkMap.put("赔付比例", compensationRatio);
        String errMsg = checkIsNull(checkMap);
        if (!errMsg.equals("")) {
            resultMap.put("message", errMsg);
        } else {
            boolean checkCvaliDate = checkDateFormat(cvaliDate);
            boolean checkBirthDay = checkDateFormat(birthDay);
            boolean checkOccupationType = checkOccupationType(occupationType);
            if (checkCvaliDate && checkBirthDay && checkOccupationType) {
                map.put("Deductible", deductible.replaceAll(",", ""));
                List<String> premList_17030 = premTrailMapper.getPrem_17030(map);
                if (premList_17030 == null || premList_17030.size() <= 0) {
                    resultMap.put("message", "险种17030保费测算失败!");
                } else {
                    String premTrail = premList_17030.get(0);
                    if (premTrail == null || premTrail.equals("")) {
                        resultMap.put("message", "保费测算失败!");
                    } else {
                        resultMap.put("Prem", premTrail);
                        resultMap.put("message", "保费测算成功!");
                        resultMap.put("success", true);
                        resultMap.put("code", "200");
                    }
                }
            } else {
                if (!checkCvaliDate) errMsg += errMsg.equals("") ? "保费测算失败，保单生效日格式错误" : "，保单生效日格式错误";
                if (!checkBirthDay) errMsg += errMsg.equals("") ? "保费测算失败，出生日期格式错误" : "，出生日期格式错误";
                if (!checkOccupationType) errMsg += errMsg.equals("") ? "保费测算失败，职业类别格式错误" : "，职业类别格式错误";
                resultMap.put("message", errMsg);
            }
        }
        return resultMap;
    }

    public Map<String, Object> premTrial_15030(Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", false);
        resultMap.put("code", "500");
        String amountGrageCode = map.get("AmountGrageCode").toString();//保额档次编码
        String occupationType = map.get("OccupationType").toString();//职业类别
        Map<String, String> checkMap = new HashMap<String, String>();
        checkMap.put("保额档次编码", amountGrageCode);
        checkMap.put("职业类别", occupationType);
        String errMsg = checkIsNull(checkMap);
        if (!errMsg.equals("")) {
            resultMap.put("message", errMsg);
        } else {
            boolean checkOccupationType = checkOccupationType(occupationType);
            if (checkOccupationType) {
                List<String> premList_15030 = premTrailMapper.getPrem_15030(map);
                if (premList_15030 == null || premList_15030.size() <= 0) {
                    resultMap.put("message", "险种15030保费测算失败!");
                } else {
                    String premTrail = premList_15030.get(0);
                    if (premTrail == null || premTrail.equals("")) {
                        resultMap.put("message", "保费测算失败!");
                    } else {
                        resultMap.put("Prem", premTrail);
                        resultMap.put("message", "保费测算成功!");
                        resultMap.put("success", true);
                        resultMap.put("code", "200");
                    }
                }
            } else {
                resultMap.put("message", "保费测算失败，职业类别格式错误");
            }
        }
        return resultMap;
    }

    public Map<String, Object> premTrial_15040(Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", false);
        resultMap.put("code", "500");
        String joinMedProtect = map.get("JoinMedProtect").toString();//有无医保
        String amountGrageCode = map.get("AmountGrageCode").toString();//保额档次编码
        String deductible = map.get("Deductible").toString();//免赔额
        String occupationType = map.get("OccupationType").toString();//职业类别
        String compensationRatio = map.get("CompensationRatio").toString();//赔付比例
        Map<String, String> checkMap = new HashMap<String, String>();
        checkMap.put("有无医保", joinMedProtect);
        checkMap.put("保额档次编码", amountGrageCode);
        checkMap.put("免赔额", deductible.replaceAll(",", ""));
        checkMap.put("职业类别", occupationType);
        checkMap.put("赔付比例", compensationRatio);
        String errMsg = checkIsNull(checkMap);
        if (!errMsg.equals("")) {
            resultMap.put("message", errMsg);
        } else {
            boolean checkOccupationType = checkOccupationType(occupationType);
            if (checkOccupationType) {
                map.put("Deductible", deductible.replaceAll(",", ""));
                List<String> premList_15040 = premTrailMapper.getPrem_15040(map);
                if (premList_15040 == null || premList_15040.size() <= 0) {
                    resultMap.put("message", "险种15040保费测算失败!");
                } else {
                    String premTrail = premList_15040.get(0);
                    if (premTrail == null || premTrail.equals("")) {
                        resultMap.put("message", "保费测算失败!");
                    } else {
                        resultMap.put("Prem", premTrail);
                        resultMap.put("message", "保费测算成功!");
                        resultMap.put("success", true);
                        resultMap.put("code", "200");
                    }
                }
            } else {
                if (!checkOccupationType) errMsg += errMsg.equals("") ? "保费测算失败，职业类别格式错误" : "，职业类别格式错误";
                resultMap.put("message", errMsg);
            }
        }
        return resultMap;
    }

    public Map<String, Object> premTrial_15060(Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", false);
        resultMap.put("code", "500");
        String amountGrageCode = map.get("AmountGrageCode").toString();//保额档次编码
        String occupationType = map.get("OccupationType").toString();//职业类别
        Map<String, String> checkMap = new HashMap<String, String>();
        checkMap.put("保额档次编码", amountGrageCode);
        checkMap.put("职业类别", occupationType);
        String errMsg = checkIsNull(checkMap);
        if (!errMsg.equals("")) {
            resultMap.put("message", errMsg);
        } else {
            boolean checkOccupationType = checkOccupationType(occupationType);
            if (checkOccupationType) {
                List<String> optDutyCode = (List<String>) map.get("optDutyCode");
                if (optDutyCode != null && optDutyCode.size() > 0) {
                    List<Map<String, String>> premList_15060_2 = premTrailMapper.getPrem_15060_2(map);
                    if (premList_15060_2 == null || premList_15060_2.size() <= 0) {
                        resultMap.put("message", "险种15060保费测算失败!");
                    } else {
                        Double totlePrem = 0.0;
                        for (Map<String, String> map2 : premList_15060_2) {
                            totlePrem += Double.valueOf(map2.get("Prem").replaceAll(",", ""));
                        }
                        Map<String, Object> dataMap = new HashMap<String, Object>();
                        dataMap.put("Prem", totlePrem + "");
                        dataMap.put("PremDetailList", premList_15060_2);
                        if (dataMap == null || dataMap.equals("")) {
                            resultMap.put("message", "保费测算失败!");
                        } else {
                            resultMap.put("Prem", dataMap);
                            resultMap.put("message", "保费测算成功!");
                            resultMap.put("success", true);
                            resultMap.put("code", "200");
                            resultMap.put("isMust", "N");
                        }
                    }
                } else {
                    List<String> premList_15060 = premTrailMapper.getPrem_15060(map);
                    if (premList_15060 == null || premList_15060.size() <= 0) {
                        resultMap.put("message", "保费测算失败!");
                    } else {
                        String premTrail = premList_15060.get(0);
                        if (premTrail == null || premTrail.equals("")) {
                            resultMap.put("message", "保费测算失败!");
                        } else {
                            Map<String, Object> dataMap = new HashMap<String, Object>();
                            dataMap.put("Prem", premTrail);
                            dataMap.put("PremDetailList", new ArrayList<Map<String, String>>());
                            resultMap.put("Prem", dataMap);
                            resultMap.put("message", "保费测算成功!");
                            resultMap.put("success", true);
                            resultMap.put("code", "200");
                            resultMap.put("isMust", "Y");
                        }
                    }
                }
            } else {
                resultMap.put("message", "保费测算失败，职业类别格式错误");
            }
        }
        return resultMap;
    }

    public Map<String, Object> premTrial_16040(Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", false);
        resultMap.put("code", "500");
        String amountGrageCode = map.get("AmountGrageCode").toString();//保额档次编码
        String cvaliDate = map.get("CvaliDate").toString();//保单生效日
        String birthDay = map.get("BirthDay").toString();//出生日期
        String sex = map.get("Sex").toString();//性别
        Map<String, String> checkMap = new HashMap<String, String>();
        checkMap.put("保额档次编码", amountGrageCode);
        checkMap.put("保单生效日", cvaliDate);
        checkMap.put("出生日期", birthDay);
        checkMap.put("性别", sex);
        String errMsg = checkIsNull(checkMap);
        if (!errMsg.equals("")) {
            resultMap.put("message", errMsg);
        } else {
            boolean checkCvaliDate = checkDateFormat(cvaliDate);
            boolean checkBirthDay = checkDateFormat(birthDay);
            boolean checkSex = checkSexAndJoinMedProtectFormat(sex);
            if (checkCvaliDate && checkBirthDay && checkSex) {
                List<String> premList_16040 = premTrailMapper.getPrem_16040(map);
                if (premList_16040 == null || premList_16040.size() <= 0) {
                    resultMap.put("message", "险种16040保费测算失败!");
                } else {
                    String premTrail = premList_16040.get(0);
                    if (premTrail == null || premTrail.equals("")) {
                        resultMap.put("message", "保费测算失败!");
                    } else {
                        resultMap.put("Prem", premTrail);
                        resultMap.put("message", "保费测算成功!");
                        resultMap.put("success", true);
                        resultMap.put("code", "200");
                    }
                }
            } else {
                if (!checkCvaliDate) errMsg += errMsg.equals("") ? "保费测算失败，保单生效日格式错误" : "，保单生效日格式错误";
                if (!checkBirthDay) errMsg += errMsg.equals("") ? "保费测算失败，出生日期格式错误" : "，出生日期格式错误";
                if (!checkSex) errMsg += errMsg.equals("") ? "保费测算失败，性别格式错误" : "，性别格式错误";
                resultMap.put("message", errMsg);
            }
        }
        return resultMap;
    }

    public Map<String, Object> premTrial_17020(Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", false);
        resultMap.put("code", "500");
        String amountGrageCode = map.get("AmountGrageCode").toString();//保额档次编码
        String cvaliDate = map.get("CvaliDate").toString();//保单生效日
        String birthDay = map.get("BirthDay").toString();//出生日期
        Map<String, String> checkMap = new HashMap<String, String>();
        checkMap.put("保额档次编码", amountGrageCode);
        checkMap.put("保单生效日", cvaliDate);
        checkMap.put("出生日期", birthDay);
        String errMsg = checkIsNull(checkMap);
        if (!errMsg.equals("")) {
            resultMap.put("message", errMsg);
        } else {
            boolean checkCvaliDate = checkDateFormat(cvaliDate);
            boolean checkBirthDay = checkDateFormat(birthDay);
            if (checkCvaliDate && checkBirthDay) {
                List<String> optDutyCode = (List<String>) map.get("optDutyCode");
                if (optDutyCode != null && optDutyCode.size() > 0) {
                    List<Map<String, String>> premList_17020_2 = premTrailMapper.getPrem_17020_2(map);
                    if (premList_17020_2 == null || premList_17020_2.size() <= 0) {
                        resultMap.put("message", "险种17020保费测算失败!");
                    } else {
                        Double totlePrem = 0.0;
                        for (Map<String, String> map2 : premList_17020_2) {
                            totlePrem += Double.valueOf(map2.get("Prem").replaceAll(",", ""));
                        }
                        Map<String, Object> dataMap = new HashMap<String, Object>();
                        dataMap.put("Prem", totlePrem + "");
                        dataMap.put("PremDetailList", premList_17020_2);
                        if (dataMap == null || dataMap.equals("")) {
                            resultMap.put("message", "保费测算失败!");
                        } else {
                            resultMap.put("Prem", dataMap);
                            resultMap.put("message", "保费测算成功!");
                            resultMap.put("success", true);
                            resultMap.put("code", "200");
                            resultMap.put("isMust", "N");
                        }
                    }
                } else {
                    List<String> premList_17020 = premTrailMapper.getPrem_17020(map);
                    if (premList_17020 == null || premList_17020.size() <= 0) {
                        resultMap.put("message", "保费测算失败!");
                    } else {
                        String premTrail = premList_17020.get(0);
                        if (premTrail == null || premTrail.equals("")) {
                            resultMap.put("message", "保费测算失败!");
                        } else {
                            Map<String, Object> dataMap = new HashMap<String, Object>();
                            dataMap.put("Prem", premTrail);
                            dataMap.put("PremDetailList", new ArrayList<Map<String, String>>());
                            resultMap.put("Prem", dataMap);
                            resultMap.put("message", "保费测算成功!");
                            resultMap.put("success", true);
                            resultMap.put("code", "200");
                            resultMap.put("isMust", "Y");
                        }
                    }
                }
            } else {
                if (!checkCvaliDate) errMsg += errMsg.equals("") ? "保费测算失败，保单生效日格式错误" : "，保单生效日格式错误";
                if (!checkBirthDay) errMsg += errMsg.equals("") ? "保费测算失败，出生日期格式错误" : "，出生日期格式错误";
                resultMap.put("message", errMsg);
            }
        }
        return resultMap;
    }

    public Map<String, Object> premTrial_17010(Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", false);
        resultMap.put("code", "500");
        String joinMedProtect = map.get("JoinMedProtect").toString();//有无医保
        String amountGrageCode = map.get("AmountGrageCode").toString();//保额档次编码
        String deductible = map.get("Deductible").toString();//免赔额
        String cvaliDate = map.get("CvaliDate").toString();//保单生效日
        String birthDay = map.get("BirthDay").toString();//出生日期
        String occupationType = map.get("OccupationType").toString();//职业类别
        String insureCount = map.get("InsureCount").toString();//投保人数
        String compensationRatio = map.get("CompensationRatio").toString();//赔付比例
        Map<String, String> checkMap = new HashMap<String, String>();
        checkMap.put("有无医保", joinMedProtect);
        checkMap.put("保额档次编码", amountGrageCode);
        checkMap.put("免赔额", deductible.replaceAll(",", ""));
        checkMap.put("保单生效日", cvaliDate);
        checkMap.put("出生日期", birthDay);
        checkMap.put("职业类别", occupationType);
        checkMap.put("投保人数", insureCount);
        checkMap.put("赔付比例", compensationRatio);
        String errMsg = checkIsNull(checkMap);
        if (!errMsg.equals("")) {
            resultMap.put("message", errMsg);
        } else {
            boolean checkCvaliDate = checkDateFormat(cvaliDate);
            boolean checkBirthDay = checkDateFormat(birthDay);
            boolean checkOccupationType = checkOccupationType(occupationType);
            if (checkCvaliDate && checkBirthDay && checkOccupationType) {
                map.put("Deductible", deductible.replaceAll(",", ""));
                List<String> premList_17010 = premTrailMapper.getPrem_17010(map);
                if (premList_17010 == null || premList_17010.size() <= 0) {
                    resultMap.put("message", "险种17010保费测算失败!");
                } else {
                    String premTrail = premList_17010.get(0);
                    if (premTrail == null || premTrail.equals("")) {
                        resultMap.put("message", "保费测算失败!");
                    } else {
                        resultMap.put("Prem", premTrail);
                        resultMap.put("message", "保费测算成功!");
                        resultMap.put("success", true);
                        resultMap.put("code", "200");
                    }
                }
            } else {
                if (!checkCvaliDate) errMsg += errMsg.equals("") ? "保费测算失败，保单生效日格式错误" : "，保单生效日格式错误";
                if (!checkBirthDay) errMsg += errMsg.equals("") ? "保费测算失败，出生日期格式错误" : "，出生日期格式错误";
                if (!checkOccupationType) errMsg += errMsg.equals("") ? "保费测算失败，职业类别格式错误" : "，职业类别格式错误";
                resultMap.put("message", errMsg);
            }
        }
        return resultMap;
    }

    public Map<String, Object> premTrial_15070(Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", false);
        resultMap.put("code", "500");
        String amountGrageCode = map.get("AmountGrageCode").toString();//保额档次编码
        Map<String, String> checkMap = new HashMap<String, String>();
        checkMap.put("保额档次编码", amountGrageCode);
        String errMsg = checkIsNull(checkMap);
        if (!errMsg.equals("")) {
            resultMap.put("message", errMsg);
        } else {
            List<String> optDutyCode = (List<String>) map.get("optDutyCode");
            if (optDutyCode != null && optDutyCode.size() > 0) {
                String joinMedProtect = map.get("JoinMedProtect").toString();//有无医保
                if (joinMedProtect == null || joinMedProtect.equals("")) {
                    resultMap.put("message", "保费测算失败，有无医保不能为空");
                } else {
                    if (checkSexAndJoinMedProtectFormat(joinMedProtect)) {
                        List<Map<String, String>> premList_15070_2 = premTrailMapper.getPrem_15070_2(map);
                        if (premList_15070_2 == null || premList_15070_2.size() <= 0) {
                            resultMap.put("message", "险种15070保费测算失败!");
                        } else {
                            Double totlePrem = 0.0;
                            for (Map<String, String> map2 : premList_15070_2) {
                                totlePrem += Double.valueOf(map2.get("Prem").replaceAll(",", ""));
                            }
                            Map<String, Object> dataMap = new HashMap<String, Object>();
                            dataMap.put("Prem", totlePrem + "");
                            dataMap.put("PremDetailList", premList_15070_2);
                            if (dataMap == null || dataMap.equals("")) {
                                resultMap.put("message", "保费测算失败!");
                            } else {
                                resultMap.put("Prem", dataMap);
                                resultMap.put("message", "保费测算成功!");
                                resultMap.put("success", true);
                                resultMap.put("code", "200");
                                resultMap.put("isMust", "N");
                            }
                        }
                    } else {
                        resultMap.put("message", "保费测算失败，有无医保格式错误");
                    }
                }
            } else {
                List<String> premList_15070 = premTrailMapper.getPrem_15070(map);
                if (premList_15070 == null || premList_15070.size() <= 0) {
                    resultMap.put("message", "保费测算失败!");
                } else {
                    String premTrail = premList_15070.get(0);
                    if (premTrail == null || premTrail.equals("")) {
                        resultMap.put("message", "保费测算失败!");
                    } else {
                        Map<String, Object> dataMap = new HashMap<String, Object>();
                        dataMap.put("Prem", premTrail);
                        dataMap.put("PremDetailList", new ArrayList<Map<String, String>>());
                        resultMap.put("Prem", dataMap);
                        resultMap.put("message", "保费测算成功!");
                        resultMap.put("success", true);
                        resultMap.put("code", "200");
                        resultMap.put("isMust", "Y");
                    }
                }
            }
        }
        return resultMap;
    }

    public Map<String, Object> premTrial_17050(Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", false);
        resultMap.put("code", "500");
        String joinMedProtect = map.get("JoinMedProtect").toString();//有无医保
        String amountGrageCode = map.get("AmountGrageCode").toString();//保额档次编码
        String cvaliDate = map.get("CvaliDate").toString();//保单生效日
        String birthDay = map.get("BirthDay").toString();//出生日期
        String compensationRatio = map.get("CompensationRatio").toString();//赔付比例
        Map<String, String> checkMap = new HashMap<String, String>();
        checkMap.put("有无医保", joinMedProtect);
        checkMap.put("保额档次编码", amountGrageCode);
        checkMap.put("保单生效日", cvaliDate);
        checkMap.put("出生日期", birthDay);
        checkMap.put("赔付比例", compensationRatio);
        String errMsg = checkIsNull(checkMap);
        if (!errMsg.equals("")) {
            resultMap.put("message", errMsg);
        } else {
            boolean checkCvaliDate = checkDateFormat(cvaliDate);
            boolean checkBirthDay = checkDateFormat(birthDay);
            if (checkCvaliDate && checkBirthDay) {
                List<String> premList_17050 = premTrailMapper.getPrem_17050(map);
                if (premList_17050 == null || premList_17050.size() <= 0) {
                    resultMap.put("message", "险种17050保费测算失败!");
                } else {
                    String premTrail = premList_17050.get(0);
                    if (premTrail == null || premTrail.equals("")) {
                        resultMap.put("message", "保费测算失败!");
                    } else {
                        resultMap.put("Prem", premTrail);
                        resultMap.put("message", "保费测算成功!");
                        resultMap.put("success", true);
                        resultMap.put("code", "200");
                    }
                }
            } else {
                if (!checkCvaliDate) errMsg += errMsg.equals("") ? "保费测算失败，保单生效日格式错误" : "，保单生效日格式错误";
                if (!checkBirthDay) errMsg += errMsg.equals("") ? "保费测算失败，出生日期格式错误" : "，出生日期格式错误";
                resultMap.put("message", errMsg);
            }
        }
        return resultMap;
    }

    public Map<String, Object> premTrial_311012(DailyPremTrail dailyPremTrail) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", false);
        resultMap.put("code", "500");
        Map<String, String> checkMap = new HashMap<String, String>();
        checkMap.put("保险期间", dailyPremTrail.getInsurePeriod());
        checkMap.put("缴费期间", dailyPremTrail.getPayPeriod());
        checkMap.put("计划编码", dailyPremTrail.getPlanCode());
        checkMap.put("保额", dailyPremTrail.getAmount());
        checkMap.put("性别", dailyPremTrail.getGender());
        String errMsg = checkIsNull(checkMap);
        if (!errMsg.equals("")) {
            resultMap.put("message", errMsg);
        } else {
            boolean checkInsureDate = checkDateFormat(dailyPremTrail.getInsureDate());
            boolean checkBirthDay = checkDateFormat(dailyPremTrail.getBirthDay());
            boolean checkSex = checkSexAndJoinMedProtectFormat(dailyPremTrail.getGender());
            if (checkInsureDate && checkBirthDay && checkSex) {
                List<String> premList_311012 = premTrailMapper.premTrial_311012(dailyPremTrail);
                if (premList_311012 == null || premList_311012.size() <= 0) {
                    resultMap.put("message", "险种311012保费测算失败!");
                } else {
                    String premTrail = premList_311012.get(0);
                    if (premTrail == null || premTrail.equals("")) {
                        resultMap.put("message", "保费测算失败!");
                    } else {
                        resultMap.put("Prem", premTrail);
                        resultMap.put("message", "保费测算成功!");
                        resultMap.put("success", true);
                        resultMap.put("code", "200");
                    }
                }
            } else {
                if (!checkInsureDate) errMsg += errMsg.equals("") ? "保费测算失败，投保日期格式错误" : "，投保日期格式错误";
                if (!checkBirthDay) errMsg += errMsg.equals("") ? "保费测算失败，出生日期格式错误" : "，出生日期格式错误";
                if (!checkSex) errMsg += errMsg.equals("") ? "保费测算失败，性别格式错误" : "，性别格式错误";
                resultMap.put("message", errMsg);
            }
        }
        return resultMap;
    }

    public String checkIsNull(Map<String, String> map) {
        String errMsg = "";
        for (String key : map.keySet()) {
            String val = map.get(key);
            if (val == null || "".equals(val)) {
                errMsg += "".equals(errMsg) ? "保费测算失败，" + key + "不能为空" : "," + key + "不能为空";
            }
        }
        return errMsg;
    }

    public boolean checkDateFormat(String ymd) {
        if (ymd == null || ymd.length() == 0) {
            return false;
        }
        String s = ymd.replaceAll("[/\\- ]", "");
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        try {
            Date date = format.parse(s);
            if (!format.format(date).equals(s)) {
                return false;
            }
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    public boolean checkSexAndJoinMedProtectFormat(String sex) {
        String[] str = {"1", "0"};
        return Arrays.asList(str).contains(sex);
    }

    public boolean checkOccupationType(String occupationType) {
        String[] str = {"1", "2", "3", "4", "5", "6"};
        return Arrays.asList(str).contains(occupationType);
    }

    public boolean checkRiskType(String riskType) {
        String[] str = {"01", "02", "03", "04", "05"};
        return Arrays.asList(str).contains(riskType);
    }

    //保费测算请求报文生成
    public List<Map<String, Object>> generateRequest(Map<String, Object> map) {
        List<Map<String, Object>> requestMaplist = new ArrayList<>();
        String BirthDay = String.valueOf(map.get("BirthDay"));
        String CvaliDate = String.valueOf(map.get("CvaliDate"));
        String Sex = String.valueOf(map.get("Sex"));
        String insureCount = String.valueOf(map.get("InsureCount"));
        String JoinMedProtect = String.valueOf(map.get("JoinMedProtect"));
        String OccupationType = String.valueOf(map.get("OccupationType"));

        //查询符合员工的默认保额档次
        List<FcDefaultRiskGrade> fcDefaultRiskGradelist = fcBusinessProDutyGrpObjectMapper.selectDefaultRiskGrade(map);
        List<FcDefaultRiskGrade> fcDefaultRiskGrades = new ArrayList<>(fcDefaultRiskGradelist);
        String riskCode = "";
        String AmountGrageCode = "";
        List<String> riskNameList = new ArrayList<>();
        Map<String, FcDefaultRiskGrade> riskCodeAndInfoMap = new HashMap<>();
        for (FcDefaultRiskGrade fcDefaultRiskGrade : fcDefaultRiskGrades) {
            String riskName = fcDefaultRiskGrade.getRiskName();
            riskCode = fcDefaultRiskGrade.getRiskCode();
            if (riskNameList.contains(riskName)) {
                FcDefaultRiskGrade oldfcDefaultRiskGrade = riskCodeAndInfoMap.get(riskName);
                if (Double.parseDouble(oldfcDefaultRiskGrade.getAmnt()) <= Double.parseDouble(fcDefaultRiskGrade.getAmnt())) {
                    fcDefaultRiskGradelist.remove(fcDefaultRiskGrade);
                } else {
                    fcDefaultRiskGradelist.remove(oldfcDefaultRiskGrade);
                }
            } else {
                riskCodeAndInfoMap.put(riskName, fcDefaultRiskGrade);
                riskNameList.add(riskName);
            }
        }
        for (FcDefaultRiskGrade fcDefaultRiskGrade : fcDefaultRiskGradelist) {
            Map<String, Object> requestMap = new HashMap<>();
            AmountGrageCode = fcDefaultRiskGrade.getAmountGrageCode();
            riskCode = fcDefaultRiskGrade.getRiskCode();
            requestMap.put("RiskCode", riskCode);//险种编码
            requestMap.put("AmountGrageCode", AmountGrageCode);//必须责任保额档次编码
            requestMap.put("RiskType", fcDefaultRiskGrade.getRiskType());
            requestMap.put("isAmount", "0");
            requestMap.put("ensureCode", map.get("EnsureCode"));
            //出生日期 保单生效日 性别
            if (riskCode.matches("^1(202|703|604|649|702|701|705)0$")) {
                requestMap.put("BirthDay", BirthDay);
                requestMap.put("CvaliDate", CvaliDate);
                if (!riskCode.equals("17050")) {
                    requestMap.put("Sex", Sex);
                }
            }
            //是否参加医保
            if (riskCode.matches("^1(703|504|701|705|707)0$")) {
                requestMap.put("JoinMedProtect", JoinMedProtect);
            }
            //免赔额
            if (riskCode.matches("^1(703|504|701|705)0$")) {
                requestMap.put("Deductible", fcDefaultRiskGrade.getDeductible());
                requestMap.put("DeductibleAttr", fcDefaultRiskGrade.getDeductibleAttr());
            }
            //职业类别
            if (riskCode.matches("^1(703|503|504|506|701)0$")) {
                requestMap.put("OccupationType", OccupationType);
            }
            //投保人数
            if (riskCode.matches("^1(703|701)0$")) {
                requestMap.put("InsureCount", insureCount);
            }
            //赔付比例
            if (riskCode.matches("^1(703|504|701|705)0$")) {
                requestMap.put("CompensationRatio", fcDefaultRiskGrade.getCompensationRatio());
            }
            //可选责任编码
            if (riskCode.matches("^1(506|702|507)0$")) {
                //获取可选责任编码的list
//				List<String> optDutyCodeList = fcDutyGradeOptionalAmountInfoMapper.selectOptDutyCodeList(AmountGrageCode);
                requestMap.put("optDutyCode", new ArrayList<String>());
            }
            requestMaplist.add(requestMap);
        }
        return requestMaplist;
    }

    public Map<String, Object> getDefaultPlanPrem(Map<String, Object> map) {
        String riskCode = map.get("RiskCode").toString();
        String amountGrageCode = map.get("AmountGrageCode").toString();
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("RiskCode", riskCode);//险种编码
        requestMap.put("AmountGrageCode", amountGrageCode);//必须责任保额档次编码
        requestMap.put("isAmount", "0");
        //出生日期 保单生效日 性别
        if (riskCode.matches("^1(202|703|604|649|702|701|705)0$")) {
            requestMap.put("BirthDay", map.get("BirthDay").toString());
            requestMap.put("CvaliDate", map.get("CvaliDate").toString());
            if (!riskCode.equals("17050")) {
                requestMap.put("Sex", map.get("Sex").toString());
            }
        }
        //是否参加医保
        if (riskCode.matches("^1(703|504|701|705|707)0$")) {
            requestMap.put("JoinMedProtect", map.get("JoinMedProtect").toString());
        }
        //免赔额
        if (riskCode.matches("^1(703|504|701)0$")) {
            requestMap.put("Deductible", map.get("DefaultDeductible").toString());
        }
        //职业类别
        if (riskCode.matches("^1(703|503|504|506|701)0$")) {
            requestMap.put("OccupationType", map.get("OccupationType").toString());
        }
        //投保人数
        if (riskCode.matches("^1(703|701)0$")) {
            requestMap.put("InsureCount", map.get("Sex").toString());
        }
        //赔付比例
        if (riskCode.matches("^1(703|504|701|705)0$")) {
            requestMap.put("CompensationRatio", map.get("DefaultCompensationRatio").toString());
        }
        //可选责任编码
        if (riskCode.matches("^1(506|702|507)0$")) {
            requestMap.put("optDutyCode", new ArrayList<String>());
        }
        return requestMap;
    }

    public Map<String, Object> getDefaultPlanPrem1(SelectMinAmntDefaultReq selectMinAmntDefaultReq) {
        String riskCode = selectMinAmntDefaultReq.getRiskCode();
        String amountGrageCode = selectMinAmntDefaultReq.getAmountGrageCode();
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("RiskCode", riskCode);// 险种编码
        requestMap.put("AmountGrageCode", amountGrageCode);// 必须责任保额档次编码
        requestMap.put("isAmount", "0");
        // 出生日期 保单生效日 性别
        if (riskCode.matches("^1(202|703|604|649|702|701|705)0$")) {
            requestMap.put("BirthDay", selectMinAmntDefaultReq.getBirthDay());
            requestMap.put("CvaliDate", selectMinAmntDefaultReq.getCvaliDate());
            if (!riskCode.equals("17050")) {
                requestMap.put("Sex", selectMinAmntDefaultReq.getSex());
            }
        }
        // 是否参加医保
        if (riskCode.matches("^1(703|504|701|705|707)0$")) {
            requestMap.put("JoinMedProtect", selectMinAmntDefaultReq.getJoinMedProtect());
        }
        // 免赔额
        if (riskCode.matches("^1(703|504|701)0$")) {
            requestMap.put("Deductible", selectMinAmntDefaultReq.getDefaultDeductible());
        }
        // 职业类别
        if (riskCode.matches("^1(703|503|504|506|701)0$")) {
            requestMap.put("OccupationType", selectMinAmntDefaultReq.getOccupationType());
        }
        // 投保人数
        if (riskCode.matches("^1(703|701)0$")) {
            requestMap.put("InsureCount", selectMinAmntDefaultReq.getSex());
        }
        // 赔付比例
        if (riskCode.matches("^1(703|504|701|705)0$")) {
            requestMap.put("CompensationRatio", selectMinAmntDefaultReq.getDefaultCompensationRatio());
        }
        // 可选责任编码
        if (riskCode.matches("^1(506|702|507)0$")) {
            requestMap.put("optDutyCode", new ArrayList<String>());
        }
        return requestMap;
    }

    public Map<String, Object> dailyPremTrail(DailyPremTrail dailyPremTrail) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "保费测算不支持此险种！");
            if (dailyPremTrail.getRiskCode() == null || dailyPremTrail.getRiskCode().equals("")) {
                resultMap.put("message", "险种编码不能为空");
                return resultMap;
            }
            String riskCode = dailyPremTrail.getRiskCode();
            switch (riskCode) {
                //横琴福裕团体重大疾病保险
                case "16380":
                    resultMap = premTrial_311012(dailyPremTrail);
                    break;
            }
            String code = resultMap.get("code").toString();
            if (code.equals("200")) {
                String prem = resultMap.get("Prem").toString();
                resultMap.put("Prem", prem.replaceAll(",", ""));
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "保费测算异常！");
        }
        return resultMap;
    }

    public Map<String, Object> premTrial_16490(Map<String, Object> map) {
        //定义返回报文
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", false);
        resultMap.put("code", "500");
        //判断福利编码不能为空,因费率表需要判断是否续保字段。
        if (StringUtils.isEmpty(map.get("ensureCode"))) {
            resultMap.put("message", "福利编码不能为空");
            return resultMap;
        } else {
            Map params = new HashMap();
            params.put("ensureCode", map.get("ensureCode"));
            params.put("configNo", "013");
            String fcEnsureConfig_013 = fcEnsureConfigMapper.selectOnlyValue(params);
            map.put("IsRenewalInsurance", fcEnsureConfig_013);
        }
        //保费试算参数组装
        String amountGrageCode = map.get("AmountGrageCode").toString();//保额档次编码
        String cvaliDate = map.get("CvaliDate").toString();//保单生效日
        String birthDay = map.get("BirthDay").toString();//出生日期
        String sex = map.get("Sex").toString();//性别
        Map<String, String> checkMap = new HashMap<String, String>();
        checkMap.put("保额档次编码", amountGrageCode);
        checkMap.put("保单生效日", cvaliDate);
        checkMap.put("出生日期", birthDay);
        checkMap.put("性别", sex);
        String errMsg = checkIsNull(checkMap);
        if (!errMsg.equals("")) {
            resultMap.put("message", errMsg);
        } else {
            boolean checkCvaliDate = checkDateFormat(cvaliDate);
            boolean checkBirthDay = checkDateFormat(birthDay);
            boolean checkSex = checkSexAndJoinMedProtectFormat(sex);
            if (checkCvaliDate && checkBirthDay && checkSex) {
                List<String> premList_16050 = premTrailMapper.getPrem_16490(map);
                if (premList_16050 == null || premList_16050.size() <= 0) {
                    resultMap.put("message", "保费测算失败!");
                } else {
                    String premTrail = premList_16050.get(0);
                    if (premTrail == null || premTrail.equals("")) {
                        resultMap.put("message", "保费测算失败!");
                    } else {
                        resultMap.put("Prem", premTrail);
                        resultMap.put("message", "保费测算成功!");
                        resultMap.put("success", true);
                        resultMap.put("code", "200");
                    }
                }
            } else {
                if (!checkCvaliDate) errMsg += errMsg.equals("") ? "保费测算失败，保单生效日格式错误" : "，保单生效日格式错误";
                if (!checkBirthDay) errMsg += errMsg.equals("") ? "保费测算失败，出生日期格式错误" : "，出生日期格式错误";
                if (!checkSex) errMsg += errMsg.equals("") ? "保费测算失败，性别格式错误" : "，性别格式错误";
                resultMap.put("message", errMsg);
            }
        }
        return resultMap;
    }


}