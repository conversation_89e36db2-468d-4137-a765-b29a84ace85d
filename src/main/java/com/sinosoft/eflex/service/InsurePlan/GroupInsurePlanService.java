package com.sinosoft.eflex.service.InsurePlan;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.dao.InsurePlan.FcInsurePlanMapper;
import com.sinosoft.eflex.dao.InsurePlan.FcInsurePlanOrderMapper;
import com.sinosoft.eflex.dao.InsurePlan.FcInsurePlanOrderPersonrelMapper;
import com.sinosoft.eflex.dao.InsurePlan.FcInsurePlanRiskConfigMapper;
import com.sinosoft.eflex.enums.status.InsurePlanEnum;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.insurePlan.*;
import com.sinosoft.eflex.service.EnsureMakeService;
import com.sinosoft.eflex.service.MaxNoService;
import com.sinosoft.eflex.service.PlanMakeService;
import com.sinosoft.eflex.service.UserService;
import com.sinosoft.eflex.util.CommonUtil;
import com.sinosoft.eflex.util.IDCardUtil;
import com.sinosoft.eflex.util.PageHelperUtil;
import com.sinosoft.eflex.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.util.*;
import java.util.stream.Collectors;

@Service("GroupInsurePlanService")
public class GroupInsurePlanService {


    private static Logger Log = LoggerFactory.getLogger(GroupInsurePlanService.class);

    @Autowired
    private FcInsurePlanMapper fcInsurePlanMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private FcInsurePlanRiskConfigMapper fcInsurePlanRiskConfigMapper;

    @Autowired
    private MaxNoService maxNoService;

    @Autowired
    private FDRiskInfoMapper fdRiskInfoMapper;

    @Autowired
    private FcInsurePlanOrderPersonrelMapper fcInsurePlanOrderPersonrelMapper;


    @Autowired
    private FcInsurePlanOrderMapper fcInsurePlanOrderMapper;

    @Autowired
    private FCPerInfoMapper fcPerInfoMapper;

    @Autowired
    private FCFileDocMainMapper fcFileDocMainMapper;

    @Autowired
    private FDCodeMapper fdCodeMapper;

    @Autowired
    private EnsureMakeService ensureMakeService;

    @Autowired
    private FCPlanRiskDutyMapper fcPlanRiskDutyMapper;

    @Autowired
    private FCEnsureMapper fcEnsureMapper;

    @Autowired
    private PlanMakeService planMakeService;


    /**
     * 保存团险赠险计划书
     *
     * @param insurePlanVo
     * @param authorization
     */
    @Transactional
    public String saveInsurePlan(String authorization, InsurePlanVo insurePlanVo) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("code", "500");
        try {
            //获取用户信息
            GlobalInput globalInput = userService.getSession(authorization);

            InsurePlan insurePlanOld = fcInsurePlanMapper.selectByInsureplanName(insurePlanVo.getInsureplanName());
            if (null != insurePlanOld) {
                response.put("message", "计划书名称重复!");
                return JSON.toJSONString(response);
            }
            InsurePlan insurePlan = new InsurePlan();
            //复制参数属性
            BeanUtils.copyProperties(insurePlanVo, insurePlan);
            //计划书编号
            String insurePlanCode = "IPC" + maxNoService.createMaxNo("InsurePlanCode", "", 18);
            insurePlan.setInsureplanCode(insurePlanCode);
            insurePlan.setGrpNo(globalInput.getGrpNo() == null ? "1" : "1");
            Date date = new Date();
            //创建修改时间
            insurePlan.setCreateTime(date);
            insurePlan.setUpdateTime(date);
            insurePlan.setCreator(globalInput.getUserName());
            insurePlan.setModifier(globalInput.getUserName());
            insurePlan.setIsDeleted(1);
            //默认下架状态
            insurePlan.setStatus(InsurePlanEnum.DOWN.getCode());
            //保存计划书基本信息
            fcInsurePlanMapper.insertSelective(insurePlan);
//            //保存关联险种配置信息
//            this.saveRiskConfig(insurePlanVo,globalInput,insurePlanCode);
            response.put("data", "JHS" + insurePlan.getId());
            response.put("success", true);
            response.put("code", "200");
            response.put("message", "新增成功!");
        } catch (Exception e) {
            Log.error("系统异常:计划书新增操作异常:{},{}", e.getMessage(), e);
            response.put("message", "系统异常!");
        }
        return JSON.toJSONString(response);
    }

    /**
     * 计划书关联险种配置信息保存
     *
     * @param insurePlanVo
     */
    private void saveRiskConfig(InsurePlanVo insurePlanVo, GlobalInput globalInput, String insurePlanCode) {

        try {
            List<InsurePlanRiskConfig> insurePlanRiskConfigList = insurePlanVo.getInsurePlanRiskConfigList();
            for (InsurePlanRiskConfig insurePlanRiskConfig : insurePlanRiskConfigList) {
                InsurePlanRiskConfig riskConfig = new InsurePlanRiskConfig();
                BeanUtils.copyProperties(insurePlanRiskConfig, riskConfig);
                //用户信息
                riskConfig.setCreator(globalInput.getUserName());
                riskConfig.setModifier(globalInput.getUserName());
//                riskConfig.setCreatorId(globalInput.getUserNo());
//                riskConfig.setModifierId(globalInput.getUserNo());
                //创建修改时间
                Date date = new Date();
                riskConfig.setCreateTime(date);
                riskConfig.setUpdateTime(date);
                //计划书编号
                riskConfig.setInsureplanCode(insurePlanCode);
                fcInsurePlanRiskConfigMapper.insert(riskConfig);
            }
        } catch (Exception e) {
            Log.error("系统异常: 关联险种配置表保存异常:{},{}", e.getMessage(), e);
            throw new RuntimeException("系统异常: 关联险种配置表保存异常");
        }

    }


    /**
     * 计划书列表查询接口
     *
     * @param authorization
     * @param page
     * @param rows
     * @return
     */
    public String selectInsurePlanByPage(String authorization, Map<String, String> params, int page, int rows) {
        Map<String, Object> responseInfo = new HashMap<>();
        responseInfo.put("success", false);
        responseInfo.put("code", "500");
        try {
            PageHelper.startPage(page, rows);
            List<HashMap<String, Object>> insurePlanList = fcInsurePlanMapper.selectInsurePlanByPage(params);
            PageHelperUtil<HashMap<String, Object>> teamPageInfo = new PageHelperUtil<>(insurePlanList);
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("recordsTotal", teamPageInfo.getTotal());
            dataMap.put("list", teamPageInfo.getList());
            responseInfo.put("data", dataMap);
            responseInfo.put("success", true);
            responseInfo.put("code", "200");
            responseInfo.put("message", "查询成功!");
        } catch (Exception e) {
            Log.error("系统异常:计划书列表查询异常:", e);
            responseInfo.put("message", "查询计划书异常!");
        }
        return JSON.toJSONString(responseInfo);
    }


    /**
     * 计划书详情接口
     *
     * @param authorization
     * @param id
     * @return
     */
    public String getInsurePlanInfo(String authorization, Long id) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("code", "500");
        try {
            //返回数据集合
            Map<String, Object> data = new HashMap<>();
            //获取计划书详情
            InsurePlan insurePlan = fcInsurePlanMapper.selectByPrimaryKey(id);

            String ensureCode = "";
            //查询订单关联状态
            List<InsurePlanOrder> insurePlanOrders = fcInsurePlanOrderMapper.selectByInsurePlanCode(insurePlan.getInsureplanCode());
            insurePlanOrders = insurePlanOrders.stream().filter(i -> i.getStatus() == 1).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(insurePlanOrders)) {
                ensureCode = "JHS" + insurePlan.getId();
            } else {
                FCEnsure fcEnsure = fcEnsureMapper.selectByOrderNo(insurePlanOrders.get(0).getOrderNo());
                ensureCode = fcEnsure.getEnsureCode();
                data.put("ensureCode", ensureCode);
            }
            if (insurePlan.getInsureplanCode() != null) {
                Set<List> maps = new HashSet<>();
                List<InsurePlanRiskConfig> insurePlanRiskConfigs = fcInsurePlanRiskConfigMapper.selectRiskConfigListByCode(insurePlan.getInsureplanCode());
                FCPlanRiskDuty fcPlanRiskDuty = new FCPlanRiskDuty();
                fcPlanRiskDuty.setEnsureCode(ensureCode);
                fcPlanRiskDuty.setPlanCode("1");
                List<FCPlanRiskDuty> fcPlanRiskDuties = fcPlanRiskDutyMapper.slectRiskDutyAndEnsurePlan(fcPlanRiskDuty);
                Double premSum = new Double(0);
                for (FCPlanRiskDuty planRiskDuty : fcPlanRiskDuties) {
                    FCPlanRiskDuty planDuty = new FCPlanRiskDuty();
                    planDuty.setPlanCode("1");
                    planDuty.setDutyCode(planRiskDuty.getDutyCode());
                    planDuty.setEnsureCode(ensureCode);
                    planDuty.setRiskCode(planRiskDuty.getRiskCode());
                    String s = planMakeService.selectRiskDutyAndPlanRisk(planDuty);
                    Log.info("责任:{}", JSON.toJSONString(s));
                    JSONObject jsonObject = JSONObject.parseObject(s);
                    List<Map> duty = (List<Map>) jsonObject.get("data");
                    if (CollectionUtils.isEmpty(duty)) {
                        continue;
                    }
                    Double prem = CommonUtil.add(premSum, duty.stream().mapToDouble(d -> Double.valueOf(d.get("prem").toString())).sum());
                    maps.add(duty);
                }
                //总保费
                insurePlan.setAmountCount(premSum);
                data.put("fcPlanRiskDuties", maps);
                data.put("insurePlanRiskConfigs", insurePlanRiskConfigs);
            }
            data.put("insurePlan", insurePlan);
            response.put("data", data);
            response.put("success", true);
            response.put("code", "200");
            response.put("message", "查询成功!");
        } catch (Exception e) {
            Log.error("系统异常:计划书详情接口异常:{},{}", e.getMessage(), e);
        }
        return JSON.toJSONString(response);
    }


    /**
     * 修改计划书操作
     *
     * @param authorization
     * @param insurePlanVo
     * @return
     */
    @Transactional
    public String updateInsurePlan(String authorization, InsurePlanVo insurePlanVo) {
        HashMap<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("code", "500");
        try {
            GlobalInput globalInput = userService.getSession(authorization);
            //查询计划书编号
            InsurePlan insurePlanInfo = fcInsurePlanMapper.selectByPrimaryKey(insurePlanVo.getId());
            InsurePlan insurePlanOld = fcInsurePlanMapper.selectByInsureplanName(insurePlanVo.getInsureplanName());
            if (null != insurePlanOld && !insurePlanVo.getInsureplanName().equals(insurePlanOld.getInsureplanName())) {
                response.put("message", "计划书名称重复!");
                return JSON.toJSONString(response);
            }
            if (insurePlanInfo.getStatus() == InsurePlanEnum.UP.getCode()) {
                response.put("message", "计划书上架状态不支持修改!");
                return JSON.toJSONString(response);
            }
            InsurePlan insurePlan = new InsurePlan();
            BeanUtils.copyProperties(insurePlanVo, insurePlan);
            //修改人时间记录
            insurePlan.setModifier(globalInput.getUserName());
            insurePlan.setUpdateTime(new Date());
            //用户信息
            insurePlan.setStatus(InsurePlanEnum.DOWN.getCode());

            //统计总保费
            List<InsurePlanRiskConfig> insurePlanRiskConfigList = insurePlanVo.getInsurePlanRiskConfigList();
            double sum = insurePlanRiskConfigList.stream().mapToDouble(InsurePlanRiskConfig::getPrem).sum();
            insurePlan.setPremCount(sum);
            //修改
            fcInsurePlanMapper.updateByPrimaryKeySelective(insurePlan);
            //修改关联险种配置表
            this.updateRiskConfig(insurePlanInfo.getInsureplanCode(), globalInput, insurePlanVo);
            //返回结果
            response.put("success", true);
            response.put("code", "200");
            response.put("message", "修改成功!");
        } catch (Exception e) {
            Log.error("系统异常:修改计划书操作异常：" + e);
        }
        return JSON.toJSONString(response);
    }

    /**
     * @param insurePlanCode
     * @param globalInput
     * @param insurePlanVo
     */
    private void updateRiskConfig(String insurePlanCode, GlobalInput globalInput, InsurePlanVo insurePlanVo) {
        try {
            //删除之前计划关联的险种信息
            fcInsurePlanRiskConfigMapper.delByInsureplanCode(insurePlanCode);
            List<InsurePlanRiskConfig> insurePlanRiskConfigList = insurePlanVo.getInsurePlanRiskConfigList();
            //去重统计保额
            Map<String, List<InsurePlanRiskConfig>> riskConfig = insurePlanRiskConfigList.stream().collect(Collectors.groupingBy(InsurePlanRiskConfig::getRiskCode));
            Log.info("去重前险种与未统计的保费:{}", JSON.toJSONString(riskConfig));
            List<InsurePlanRiskConfig> insurePlanRiskConfigSet = new ArrayList<>();
            riskConfig.forEach((k, v) -> {
                Log.info("险种:{},与未统计的保费:{}", k, JSON.toJSONString(v));
                InsurePlanRiskConfig insurePlanRiskConfig = new InsurePlanRiskConfig();
                insurePlanRiskConfig.setRiskCode(k);
                double sum = v.stream().mapToDouble(InsurePlanRiskConfig::getAmount).sum();
                Log.info("保费:{}", sum);
                insurePlanRiskConfig.setAmount(sum);
                insurePlanRiskConfigSet.add(insurePlanRiskConfig);
            });
            Log.info("去重后险种与统计的保费总和:{}", JSON.toJSONString(riskConfig));
            //重复险种保额统计
            insurePlanVo.setInsurePlanRiskConfigList(insurePlanRiskConfigSet);
            //重新保存当前关联险种
            this.saveRiskConfig(insurePlanVo, globalInput, insurePlanCode);
        } catch (Exception e) {
            Log.error("系统异常:修改计划书操作修改关联险种异常:{},{}", e.getMessage(), e);
            throw new RuntimeException("系统异常:修改计划书操作修改关联险种异常");
        }
    }


    /**
     * 删除计划书及包含险种
     *
     * @param authorization
     * @param id
     * @return
     */
    @Transactional
    public String deleteInsurePlanInfo(String authorization, Long id) {
        Log.info("删除计划书ID:{}", id);
        Map<String, Object> responseInfo = new HashMap<>();
        responseInfo.put("success", false);
        responseInfo.put("code", "500");
        try {
            GlobalInput globalInput = userService.getSession(authorization);
            InsurePlan insurePlan = fcInsurePlanMapper.selectByPrimaryKey(id);
            if (insurePlan == null) {
                Log.info("计划ID:{}不存在！", id);
                responseInfo.put("message", "删除计划书信息失败：计划ID不存在！");
                return JSON.toJSONString(responseInfo);
            }
            //删除条件判断
            if (insurePlan.getStatus() == InsurePlanEnum.UP.getCode()) {
                responseInfo.put("message", "删除计划书信息失败：当前计划处于上架状态");
                return JSON.toJSONString(responseInfo);
            }
            InsurePlan insurePlanDel = new InsurePlan();
            //删除标识
            insurePlanDel.setIsDeleted(1);
            //修改信息
            //修改人时间记录
            insurePlanDel.setModifier(globalInput.getUserName());
//            insurePlan.setModifierId(globalInput.getUserNo());
            insurePlanDel.setUpdateTime(new Date());
            insurePlanDel.setStatus(insurePlan.getStatus());
            insurePlanDel.setId(id);
            //删除该计划关联的险种信息 (根据计划编号)
            this.delRiskConfig(insurePlan.getInsureplanCode(), globalInput);
            //删除计划书信息
            fcInsurePlanMapper.updateByPrimaryKeySelective(insurePlanDel);
            responseInfo.put("success", true);
            responseInfo.put("code", "200");
            responseInfo.put("message", "删除成功!");
        } catch (Exception e) {
            Log.error("计划书删除操作异常:{},{}", e.getMessage(), e);
            responseInfo.put("message", "删除计划书异常!");
        }
        Log.info("删除计划书信息----END");
        return JSON.toJSONString(responseInfo);
    }


    /**
     * 删除险种信息根据计划编号
     *
     * @param insureplanCode
     */
    private void delRiskConfig(String insureplanCode, GlobalInput globalInput) {
        Log.info("删除险种配置表--险种编码: {}", insureplanCode);
        try {
            InsurePlanRiskConfig insurePlanRiskConfig = new InsurePlanRiskConfig();
            insurePlanRiskConfig.setInsureplanCode(insureplanCode);
            //修改信息
            insurePlanRiskConfig.setModifier(globalInput.getUserName());
//            insurePlan.setModifierId(globalInput.getUserNo());
            insurePlanRiskConfig.setUpdateTime(new Date());
            //删除标识
            insurePlanRiskConfig.setIsDeleted(1);
            fcInsurePlanRiskConfigMapper.updateByInsurPlanCode(insurePlanRiskConfig);
        } catch (Exception e) {
            Log.error("系统异常：删除险种配置表异常:{},{}", e.getMessage(), e);
            throw new RuntimeException("系统异常：删除险种配置表异常");

        }
        Log.info("删除险种--END");
    }

    /**
     * 上下架计划书接口
     *
     * @param authorization
     * @param id
     * @param status
     * @return
     */
    public String upInsurePlanInfo(String authorization, Long id, int status) {
        Map<Object, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("code", "500");
        try {

            GlobalInput globalInput = userService.getSession(authorization);
            InsurePlan insurePlan = new InsurePlan();
            insurePlan.setId(id);
            insurePlan.setStatus(status);
            //修改人时间记录
            insurePlan.setModifier(globalInput.getUserName());
//            insurePlan.setModifierId(globalInput.getUserNo());
            insurePlan.setUpdateTime(new Date());
            //修改上下架状态
            fcInsurePlanMapper.updateByPrimaryKeySelective(insurePlan);
            response.put("success", true);
            response.put("code", "200");
            response.put("message", "上下架成功!");

        } catch (Exception e) {
            Log.error("系统异常: 上下架操作异常:{},{}", e.getMessage(), e);
        }
        return JSON.toJSONString(response);
    }


    /**
     * 保存员工个人信息
     *
     * @param insurePlanOrderPersonrel
     * @return
     */
    @Transactional
    public String savePersonrelInfo(InsurePlanOrderPersonrel insurePlanOrderPersonrel) {
        Map<Object, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("code", "500");
        try {
            FCEnsure fcEnsure = fcEnsureMapper.selectByOrderNo(insurePlanOrderPersonrel.getOrderNo());
            Log.info("福利状态:{}", JSON.toJSONString(fcEnsure));
            if (null != fcEnsure) {
                if (!fcEnsure.getEnsureState().equals("0")) {
                    response.put("message", "福利已经失效!");
                    return JSON.toJSONString(response);
                }
            }
            //人员信息表
            InsurePlanOrderPersonrel personal = new InsurePlanOrderPersonrel();
            BeanUtils.copyProperties(insurePlanOrderPersonrel, personal);
            ////时间人员信息
            Date date = new Date();
            personal.setCreateTime(date);
            personal.setUpdateTime(date);
            personal.setCreator(insurePlanOrderPersonrel.getName());
            personal.setModifier(insurePlanOrderPersonrel.getName());
            //人员信息保存
            fcInsurePlanOrderPersonrelMapper.insertSelective(personal);
            response.put("success", true);
            response.put("code", "200");
            response.put("message", "保存成功!");
        } catch (Exception e) {
            Log.info("系统异常: 保存人员信息操作异常:{},{}", e.getMessage(), e);
        }
        return JSON.toJSONString(response);
    }


    /**
     * 查询订单关联 未关联保存订单关联
     *
     * @param params
     */
    public String saveInsurePlanOrder(Map<String, String> params) {
        Map<Object, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("code", "500");
        try {
            InsurePlan insurePlan = fcInsurePlanMapper.selectByPrimaryKey(Long.valueOf(params.get("agentPlanId")));
            InsurePlanOrder insurePlanOrder = new InsurePlanOrder();
            insurePlanOrder.setOrderNo(params.get("orderNo"));
            insurePlanOrder.setInsureplanCode(insurePlan.getInsureplanCode());
            insurePlanOrder.setCompanyName(params.get("companyName"));
            //时间人员信息
            Date date = new Date();
            insurePlanOrder.setCreateTime(date);
            insurePlanOrder.setUpdateTime(date);
//            insurePlanOrder.setCreator("");
//            insurePlanOrder.setModifier();
            insurePlanOrder.setStatus(0); // 1 是 0 否
            //保存订单关联表信息
            fcInsurePlanOrderMapper.insertSelective(insurePlanOrder);
            response.put("success", true);
            response.put("code", "200");
            response.put("message", "保存成功!");
        } catch (Exception e) {
            Log.info("保存订单关联表异常:{},{}", e.getMessage(), e);
        }
        return JSON.toJSONString(response);
    }


    /**
     * 计划书列表
     *
     * @return
     */
    public String selectInsurePlanList(List<String> planIds) {
        Map<Object, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("code", "500");
        try {
            //查询所有上架的计划书
            List<HashMap<String, Object>> insurePlanList = fcInsurePlanMapper.selectInsurePlanList(planIds);
            response.put("data", insurePlanList);
            response.put("success", true);
            response.put("code", "200");
            response.put("message", "查询成功!");
        } catch (Exception e) {
            Log.info("查询计划书上架列表异常:{},{}", e.getMessage(), e);
        }
        return JSON.toJSONString(response);
    }

    /**
     * 获取险种列表
     *
     * @param authorization
     * @return
     */
    public String getRiskInfo(String authorization) {
        Map<Object, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("code", "500");
        try {
            List<FDRiskInfo> fdRiskInfos = fdRiskInfoMapper.selectRiskInfo();
            //删除业务不支持的险种
            fdRiskInfos.removeIf(fdRiskInfo -> ("14110".equals(fdRiskInfo.getRiskCode()) || "16040".equals(fdRiskInfo.getRiskCode()) || "16380".equals(fdRiskInfo.getRiskCode())));
            response.put("data", fdRiskInfos);
            response.put("success", true);
            response.put("code", "200");
            response.put("message", "查询成功!");
        } catch (Exception e) {
            Log.info("查询险种列表信息异常:{},{}", e.getMessage(), e);
        }
        return JSON.toJSONString(response);
    }

    /**
     * 根据订单编号查询计划书详情
     *
     * @param authorization
     * @param orderNo
     * @return
     */
    public String getInsurePlanInfoByOrderNo(String authorization, String orderNo) {
        Map<Object, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("code", "500");
        try {
            InsurePlanOrder insurePlanOrder = fcInsurePlanOrderMapper.selectByOrderNo(orderNo);
            if (ObjectUtils.isEmpty(insurePlanOrder)) {
                response.put("message", "未查询到关联的订单信息!");
                return JSON.toJSONString(response);
            }
            InsurePlanVo insurePlanInfo = fcInsurePlanMapper.selectByInsurePlanCode(insurePlanOrder.getInsureplanCode());
            HashMap<String, Object> restful = new HashMap<>();
            restful.put("insurePlanOrder", insurePlanOrder);
            restful.put("insurePlanInfo", insurePlanInfo);
            response.put("data", restful);
            response.put("success", true);
            response.put("code", "200");
            response.put("message", "查询成功!");
        } catch (Exception e) {
            Log.error("系统异常:根据订单号查询详情异常:{},{}", e.getMessage(), e);
        }
        return JSON.toJSONString(response);
    }

    /**
     * 查询个人信息
     *
     * @param insurePlanOrderPersonrel
     * @return
     */
    public String getStaffInfo(InsurePlanOrderPersonrel insurePlanOrderPersonrel) {
        Map<Object, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("code", "500");
        try {
            InsurePlanOrderPersonrel restful = new InsurePlanOrderPersonrel();
            if (StringUtil.isNotEmpty(insurePlanOrderPersonrel.getInsureplanCode()) || !StringUtil.isNotEmpty(insurePlanOrderPersonrel.getUnionId())) {
                restful = fcInsurePlanOrderPersonrelMapper.selectByPersonrelInfo(insurePlanOrderPersonrel);
                String IDType = fdCodeMapper.selectNameByCode("IDType", restful.getCredentialType());
                restful.setCredentialTypeName(IDType);
            }
            response.put("data", restful);
            response.put("success", true);
            response.put("code", "200");
            response.put("message", "查询成功!");
        } catch (Exception e) {
            Log.error("系统异常:根据员工详情异常:{},{}", e.getMessage(), e);
        }
        return JSON.toJSONString(response);
    }

    /**
     * 导出人员信息
     *
     * @param authorization
     * @param response
     * @param ensureCode
     * @param grpNo
     * @param name
     * @param sex
     * @param iDType
     * @param iDNo
     * @return
     */
    public String staffExportInfoExcel(String authorization, HttpServletResponse response, String ensureCode, String grpNo, String name, String sex, String iDType, String iDNo, String nativeplace, String defaultPlan) {
        ResponseMsg<Object> responseMsg = new ResponseMsg<>();
        try {
            if (StringUtils.isBlank(ensureCode)) {
                responseMsg.code("-1").message("请求参数有误，人员清单导出失败！");
                return JSON.toJSONString(responseMsg);
            }
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("grpNo", grpNo);
            param.put("ensureCode", ensureCode);
            param.put("defaultPlan", defaultPlan);
            param.put("name", name);
            param.put("sex", sex);
            param.put("IDType", iDType);
            param.put("IDNo", iDNo);
            param.put("nativeplace", nativeplace);
            //获取模版
            FCFileDocMain fcFileDocMain = fcFileDocMainMapper.selectByPrimaryKey("P0001");

            List<Map<String, String>> fcPerInfoList = fcPerInfoMapper.selectByParamList(param);
            String filePath = fcFileDocMain.getFilePath();
            String filename = fcFileDocMain.getFileSaveName() + fcFileDocMain.getFileSuffix();
//            File file = new File("/Users/<USER>/Downloads/横琴人寿员工清单导入模板.xlsx");
            File file = new File(filePath + filename);
            Workbook wb = WorkbookFactory.create(new FileInputStream(file));
            // 调用生成excel生成工具类,返回前台下载
            ServletOutputStream outputStream = response.getOutputStream();
            String filenameF = new String((fcFileDocMain.getFileName() + fcFileDocMain.getFileSuffix()).getBytes("UTF-8"), "iso-8859-1");
            response.setHeader("Content-disposition", "attachment;filename=" + filenameF);
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            // excel表格数据
            wb = staffInfoSheet(wb, fcPerInfoList);
            wb.write(outputStream);
            outputStream.flush();
            responseMsg.okStatus().message("导出人员清单EXCEL成功!");
        } catch (Exception e) {
            Log.info("导出人员清单EXCEL失败!", e);
            responseMsg.errorStatus().message("导出人员清单EXCEL失败!");
        }
        return JSON.toJSONString(responseMsg);
    }


    /**
     * 同步订单人员信息
     *
     * @param authorization
     * @param params
     * @return
     */
    public String synchronizeOrderStaff(String authorization, Map<String, String> params) {
        Map<Object, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("code", "500");
        try {
            GlobalInput globalInput = userService.getSession(authorization);

            String orderNo = params.get("orderNo");
            String ensureCode = params.get("ensureCode");
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            ensureMakeService.savePersonal(authorization, orderNo, globalInput, ensureCode, fcEnsure);
            response.put("success", true);
            response.put("code", "200");
            response.put("message", "同步成功!");
        } catch (Exception e) {
            Log.error("系统异常: 同步人员信息异常:{},{}", e, e.getMessage());
            throw new RuntimeException("系统异常: 同步人员信息异常");
        }
        return JSON.toJSONString(response);
    }


    /**
     * 导出数据封装
     *
     * @param wb
     * @param fcPerInfoList
     * @return
     */
    private Workbook staffInfoSheet(Workbook wb, List<Map<String, String>> fcPerInfoList) {

        int planIndex = 1;
        int row = 0;
        if (null != fcPerInfoList || fcPerInfoList.size() > 0) {
            Sheet inPlanSheet = (Sheet) wb.getSheetAt(0);
            inPlanSheet.setColumnWidth(1, 30 * 256);

            for (Map<String, String> fcPer : fcPerInfoList) {
                FCPerInfo fcPerInfo = JSONObject.parseObject(JSONObject.toJSONString(fcPer), FCPerInfo.class);
                planIndex = planIndex + 1;
                Row insuredRow = inPlanSheet.createRow(planIndex);
                insuredRow.setHeightInPoints((float) 24.95);
                CellStyle inCellStyle = this.cellStyleBorder(wb);
                // 创建家属清单页单元格
                for (int rowNum = 0; rowNum < 19; rowNum++) {
                    Cell cell = insuredRow.createCell(rowNum);
                    cell.setCellStyle(inCellStyle);
                }
                row = row + 1;
                //序号
                insuredRow.getCell(0).setCellValue(row);
                // 员工姓名
                insuredRow.getCell(1).setCellValue(fcPerInfo.getName());
                //部门
                insuredRow.getCell(2).setCellValue(fcPerInfo.getDepartment());
                String nativeplace = fdCodeMapper.selectNameByCode("nativeplace", fcPerInfo.getNativeplace());
                //国籍
                insuredRow.getCell(3).setCellValue(nativeplace);
                String IDType = fdCodeMapper.selectNameByCode("IDType", fcPerInfo.getIDType());
                // 证件类型
                insuredRow.getCell(4).setCellValue(IDType);
                // 证件号
                insuredRow.getCell(5).setCellValue(fcPerInfo.getIDNo());
                // 证件号有效期
                insuredRow.getCell(6).setCellValue("");
                // 手机号
                insuredRow.getCell(7).setCellValue(fcPerInfo.getMobilePhone());
                String Sex = fdCodeMapper.selectNameByCode("Sex", fcPerInfo.getSex());
                // 被保人性别
                insuredRow.getCell(8).setCellValue(Sex);
                // 出生日期
                insuredRow.getCell(9).setCellValue(IDCardUtil.dateOfBirth(fcPerInfo.getIDNo()));
                //计划编码
                insuredRow.getCell(10).setCellValue(fcPerInfo.getDefaultPlan());
                // 职级
                insuredRow.getCell(11).setCellValue(fcPerInfo.getLevelCode());
                // 职业类别
                insuredRow.getCell(12).setCellValue(fcPerInfo.getOccupationType());
                // 职业代码
                insuredRow.getCell(13).setCellValue(fcPerInfo.getOccupationCode());
                // 有无医保
                String joinMedProtect = fcPerInfo.getJoinMedProtect();
                if ("0".equals(joinMedProtect)) {
                    joinMedProtect = "否";
                } else if ("1".equals(joinMedProtect)) {
                    joinMedProtect = "是";
                } else {
                    joinMedProtect = "";
                }
                insuredRow.getCell(14).setCellValue(joinMedProtect);
                //开户行
                insuredRow.getCell(15).setCellValue(fcPerInfo.getOpenBank());
                //开户行帐号
                insuredRow.getCell(16).setCellValue(fcPerInfo.getOpenAccount());
                //员工福利额度
                insuredRow.getCell(17).setCellValue("");
                //家属福利额度
                insuredRow.getCell(18).setCellValue("");
            }
        }
        return wb;
    }

    /**
     * 表格样式设置
     *
     * @param wb
     * @return
     */
    private CellStyle cellStyleBorder(Workbook wb) {
        CellStyle inCellStyle = wb.createCellStyle();
        inCellStyle.setBorderBottom(BorderStyle.THIN);
        inCellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        inCellStyle.setBorderLeft(BorderStyle.THIN);
        inCellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        inCellStyle.setBorderRight(BorderStyle.THIN);
        inCellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        inCellStyle.setBorderTop(BorderStyle.THIN);
        inCellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        return inCellStyle;
    }
}
