package com.sinosoft.eflex.service;

import com.sinosoft.eflex.dao.FCGrpInfoMapper;
import com.sinosoft.eflex.dao.FcGrpContactMapper;
import com.sinosoft.eflex.dao.FcHrRegistTempMapper;
import com.sinosoft.eflex.model.FCGrpInfo;
import com.sinosoft.eflex.model.FcGrpContact;
import com.sinosoft.eflex.model.FcHrRegistTemp;
import com.sinosoft.eflex.model.HrRegist;
import com.sinosoft.eflex.service.admin.impl.AsyncImageChangeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 转换*
 *
 * <AUTHOR> *
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ImageConvertService {

    private final OssService ossService;

    private final FCGrpInfoMapper fcGrpInfoMapper;
    private final FcGrpContactMapper fcGrpContactMapper;
    private final FcHrRegistTempMapper fcHrRegistTempMapper;
    private final AsyncImageChangeService asyncImageChangeService;

    public FCGrpInfo convert(FCGrpInfo fcGrpInfo) {
        FCGrpInfo fcGrpInfoNew = new FCGrpInfo();
        if (StringUtils.isNotEmpty(fcGrpInfo.getGrpIDImage1())) {
            if (StringUtils.isEmpty(fcGrpInfo.getGrpImageFront())) {
                String grpImageFront = ossService.uploadUrl(fcGrpInfo.getGrpIDImage1());
                fcGrpInfoNew.setGrpImageFront(grpImageFront);
                fcGrpInfo.setGrpIDImage1(grpImageFront);
            } else {
                fcGrpInfo.setGrpIDImage1(fcGrpInfo.getGrpImageFront());
            }
        }
        if (StringUtils.isNotEmpty(fcGrpInfo.getGrpIDImage2())) {
            if (StringUtils.isEmpty(fcGrpInfo.getGrpImageBack())) {
                String grpImageBack = ossService.uploadUrl(fcGrpInfo.getGrpIDImage2());
                fcGrpInfoNew.setGrpImageBack(grpImageBack);
                fcGrpInfo.setGrpIDImage2(grpImageBack);
            } else {
                fcGrpInfo.setGrpIDImage2(fcGrpInfo.getGrpImageBack());
            }
        }
        if (StringUtils.isNotEmpty(fcGrpInfo.getLegIDImage1())) {
            if (StringUtils.isEmpty(fcGrpInfo.getLegalImgFront())) {
                String legalImgFront = ossService.uploadUrl(fcGrpInfo.getLegIDImage1());
                fcGrpInfoNew.setLegalImgFront(legalImgFront);
                fcGrpInfo.setLegIDImage1(legalImgFront);
            } else {
                fcGrpInfo.setLegIDImage1(fcGrpInfo.getLegalImgFront());
            }
        }
        if (StringUtils.isNotEmpty(fcGrpInfo.getLegIDImage2())) {
            if (StringUtils.isEmpty(fcGrpInfo.getLegalImgBack())) {
                String legalImgBack = ossService.uploadUrl(fcGrpInfo.getLegIDImage2());
                fcGrpInfoNew.setLegalImgBack(legalImgBack);
                fcGrpInfo.setLegIDImage2(legalImgBack);
            } else {
                fcGrpInfo.setLegIDImage2(fcGrpInfo.getLegalImgBack());
            }
        }

        if (StringUtils.isNotEmpty(fcGrpInfoNew.getGrpImageBack()) || StringUtils.isNotEmpty(fcGrpInfoNew.getGrpImageFront())
                || StringUtils.isNotEmpty(fcGrpInfoNew.getLegalImgBack()) || StringUtils.isNotEmpty(fcGrpInfoNew.getLegalImgFront())) {
            fcGrpInfoNew.setGrpNo(fcGrpInfo.getGrpNo());
            fcGrpInfoMapper.updateByPrimaryKeySelective(fcGrpInfoNew);
        }
        return fcGrpInfo;
    }


    public FcGrpContact convertFcGrpContact(FcGrpContact fcGrpContact) {
        FcGrpContact fcGrpContactNew = new FcGrpContact();
        if (StringUtils.isNotEmpty(fcGrpContact.getIdImage1())) {
            if (StringUtils.isEmpty(fcGrpContact.getIdCardFront())) {
                String idCardFront = ossService.uploadUrl(fcGrpContact.getIdImage1());
                fcGrpContactNew.setIdCardFront(idCardFront);
                fcGrpContact.setIdImage1(idCardFront);
            } else {
                fcGrpContact.setIdImage1(fcGrpContact.getIdCardFront());
            }
        }
        if (StringUtils.isNotEmpty(fcGrpContact.getIdImage2())) {
            if (StringUtils.isEmpty(fcGrpContact.getIdCardBack())) {
                String legalImgBack = ossService.uploadUrl(fcGrpContact.getIdImage2());
                fcGrpContactNew.setIdCardBack(legalImgBack);
                fcGrpContact.setIdImage2(legalImgBack);
            } else {
                fcGrpContact.setIdImage2(fcGrpContact.getIdCardBack());
            }
        }
        if (StringUtils.isNotEmpty(fcGrpContactNew.getIdCardBack()) || StringUtils.isNotEmpty(fcGrpContactNew.getIdCardFront())) {
            fcGrpContactNew.setContactNo(fcGrpContact.getContactNo());
            fcGrpContactMapper.updateByPrimaryKeySelective(fcGrpContactNew);
        }
        return fcGrpContact;
    }


    public List<HrRegist> convertHrRegister(List<HrRegist> hrRegisterContactList) {

        for (HrRegist hrRegister : hrRegisterContactList) {
            FcHrRegistTemp fcHrRegistTemp = new FcHrRegistTemp();
            if (StringUtils.isNotEmpty(hrRegister.getGrpIDImage1())) {
                if (StringUtils.isEmpty(hrRegister.getGrpImageFront())) {
                    String grpImageFront = ossService.uploadUrl(hrRegister.getGrpIDImage1());
                    fcHrRegistTemp.setGrpImageFront(grpImageFront);
                    hrRegister.setGrpIDImage1(grpImageFront);
                } else {
                    hrRegister.setGrpIDImage1(hrRegister.getGrpImageFront());
                }
            }
            if (StringUtils.isNotEmpty(hrRegister.getGrpIDImage2())) {
                if (StringUtils.isEmpty(hrRegister.getGrpImageBack())) {
                    String grpImageBack = ossService.uploadUrl(hrRegister.getGrpIDImage2());
                    fcHrRegistTemp.setGrpImageBack(grpImageBack);
                    hrRegister.setGrpIDImage2(grpImageBack);
                } else {
                    hrRegister.setGrpIDImage2(hrRegister.getGrpImageBack());
                }
            }
            if (StringUtils.isNotEmpty(hrRegister.getLegIDImage1())) {
                if (StringUtils.isEmpty(hrRegister.getLegalImgFront())) {
                    String legalImgFront = ossService.uploadUrl(hrRegister.getLegIDImage1());
                    fcHrRegistTemp.setLegalImgFront(legalImgFront);
                    hrRegister.setLegIDImage1(legalImgFront);
                } else {
                    hrRegister.setLegIDImage1(hrRegister.getLegalImgFront());
                }
            }
            if (StringUtils.isNotEmpty(hrRegister.getLegIDImage2())) {
                if (StringUtils.isEmpty(hrRegister.getLegalImgBack())) {
                    String legalImgBack = ossService.uploadUrl(hrRegister.getLegIDImage2());
                    fcHrRegistTemp.setLegalImgBack(legalImgBack);
                    hrRegister.setLegIDImage2(legalImgBack);
                } else {
                    hrRegister.setLegIDImage2(hrRegister.getLegalImgBack());
                }
            }

            if (StringUtils.isNotEmpty(hrRegister.getIdImage1())) {
                if (StringUtils.isEmpty(hrRegister.getIdCardFront())) {
                    String idCardFront = ossService.uploadUrl(hrRegister.getIdImage1());
                    fcHrRegistTemp.setIdCardFront(idCardFront);
                    hrRegister.setIdImage1(idCardFront);
                } else {
                    hrRegister.setIdImage1(hrRegister.getIdCardFront());
                }
            }
            if (StringUtils.isNotEmpty(hrRegister.getIdImage2())) {
                if (StringUtils.isEmpty(hrRegister.getIdCardBack())) {
                    String legalImgBack = ossService.uploadUrl(hrRegister.getIdImage2());
                    fcHrRegistTemp.setIdCardBack(legalImgBack);
                    hrRegister.setIdImage2(legalImgBack);
                } else {
                    hrRegister.setIdImage2(hrRegister.getIdCardBack());
                }
            }
            if (StringUtils.isNotEmpty(fcHrRegistTemp.getGrpImageBack()) || StringUtils.isNotEmpty(fcHrRegistTemp.getGrpImageFront())
                    || StringUtils.isNotEmpty(fcHrRegistTemp.getLegalImgBack()) || StringUtils.isNotEmpty(fcHrRegistTemp.getLegalImgFront())
                    || StringUtils.isNotEmpty(fcHrRegistTemp.getIdCardBack()) || StringUtils.isNotEmpty(fcHrRegistTemp.getIdCardFront())) {
                fcHrRegistTemp.setRegistSN(hrRegister.getRegistSN());
                fcHrRegistTempMapper.updateByPrimaryKeySelective(fcHrRegistTemp);
            }
        }

        return hrRegisterContactList;
    }


    public HrRegist convertHrRegisterSftp(HrRegist hrRegister) {
        if (StringUtils.isNotEmpty(hrRegister.getGrpIDImage1())) {
            hrRegister.setGrpImageFront(hrRegister.getGrpIDImage1());
            hrRegister.setGrpIDImage1(null);

        }
        if (StringUtils.isNotEmpty(hrRegister.getGrpIDImage2())) {
            hrRegister.setGrpImageBack(hrRegister.getGrpIDImage2());
            hrRegister.setGrpIDImage2(null);
        }
        if (StringUtils.isNotEmpty(hrRegister.getLegIDImage1())) {
            hrRegister.setLegalImgFront(hrRegister.getLegIDImage1());
            hrRegister.setLegIDImage1(null);
        }
        if (StringUtils.isNotEmpty(hrRegister.getLegIDImage2())) {
            hrRegister.setLegalImgBack(hrRegister.getLegIDImage2());
            hrRegister.setLegIDImage2(null);
        }
        if (StringUtils.isNotEmpty(hrRegister.getIdImage1())) {
            hrRegister.setIdCardFront(hrRegister.getIdImage1());
            hrRegister.setIdImage1(null);
        }
        if (StringUtils.isNotEmpty(hrRegister.getIdImage2())) {
            hrRegister.setIdCardBack(hrRegister.getIdImage2());
            hrRegister.setIdImage2(null);
        }

        asyncImageChangeService.fcHrRegisterUpload(hrRegister);
        return hrRegister;
    }


    public FCGrpInfo convertFcGrpInfoSftp(FCGrpInfo fcGrpInfo) {
        if (StringUtils.isNotEmpty(fcGrpInfo.getGrpIDImage1())) {
            fcGrpInfo.setGrpImageFront(fcGrpInfo.getGrpIDImage1());
            fcGrpInfo.setGrpIDImage1(null);
        }
        if (StringUtils.isNotEmpty(fcGrpInfo.getGrpIDImage2())) {
            fcGrpInfo.setGrpImageBack(fcGrpInfo.getGrpIDImage2());
            fcGrpInfo.setGrpIDImage2(null);
        } else {
            fcGrpInfo.setGrpIDImage2(null);
            fcGrpInfo.setGrpImageBack(null);
        }
        if (StringUtils.isNotEmpty(fcGrpInfo.getLegIDImage1())) {
            fcGrpInfo.setLegalImgFront(fcGrpInfo.getLegIDImage1());
            fcGrpInfo.setLegIDImage1(null);
        }
        if (StringUtils.isNotEmpty(fcGrpInfo.getLegIDImage2())) {
            fcGrpInfo.setLegalImgBack(fcGrpInfo.getLegIDImage2());
            fcGrpInfo.setLegIDImage2(null);
        }
        asyncImageChangeService.fcGrpInfoUpload(fcGrpInfo);
        return fcGrpInfo;
    }


    public FcGrpContact convertFcGrpContactSftp(FcGrpContact fcGrpContact) {
        if (StringUtils.isNotEmpty(fcGrpContact.getIdImage1())) {
            fcGrpContact.setIdCardFront(fcGrpContact.getIdImage1());
            fcGrpContact.setIdImage1(null);
        }
        if (StringUtils.isNotEmpty(fcGrpContact.getIdImage2())) {
            fcGrpContact.setIdCardBack(fcGrpContact.getIdImage2());
            fcGrpContact.setIdImage2(null);
        }
        asyncImageChangeService.fcGrpContactUpload(fcGrpContact);
        return fcGrpContact;
    }
}

