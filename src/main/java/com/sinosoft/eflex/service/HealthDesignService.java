package com.sinosoft.eflex.service;
/**
 * @DESCRIPTION
 * @create 2019-07-18 11:41:31
 **/

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Splitter;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.util.*;
import com.sinosoft.eflex.util.rest.exception.SystemException;

import ch.qos.logback.classic.Logger;

/**
 * <AUTHOR>
 * @ClassName: FCHealthDesignService
 * @Date: 2019/7/18 11:41:31
 * @Description: TODO
 * @Version: 1.0
 */
@Service
public class HealthDesignService {
    /**
     * 日志
     */
    private static Logger Log = (Logger) LoggerFactory.getLogger(HealthDesignService.class);

    @Autowired
    private FCHealthDesignMapper fcHealthDesignMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private FCPlanHealthDesignRelaMapper fcPlanHealthDesignRelaMapper;
    @Autowired
    private FCHealthDesignDetailRelaMapper fcHealthDesignDetailRelaMapper;
    @Autowired
    private FCHealthDesignDetailMapper fcHealthDesignDetailMapper;
    @Autowired
    private FileService fileService;
    @Autowired
    private FCBusPersonTypeMapper fcBusPersonTypeMapper;
    @Autowired
    private EnsureMakeService ensureMakeService;
    @Autowired
    private FDRiskInfoMapper fdRiskInfoMapper;


    //固定计划
    public static final String FIX_CODE = "0";
    //弹性计划
    public static final String FLEX_CODE = "1";

    /**
     * 得到方案编号返回给前台
     *
     * @param token
     * @return
     */
    public String getDesignNo(String token) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            String designNo = "FA" + maxNoService.createMaxNo("DesignNo", "", 18);
            resultMap.put("data", designNo);
            resultMap.put("success", true);
            resultMap.put("code", 200);
            resultMap.put("message", "得到方案编号");
        } catch (Exception e) {
            Log.info(e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", 500);
            resultMap.put("message", "方案编号未得到");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 新增/修改 方案
     *
     * @param token
     * @param params
     * @return
     */
    public String maintainDesignInfo(String token, Map<String, String> params) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        try {
            String designNo = params.get("designNo");
            String designName = params.get("designName");
            String ensureCode = params.get("ensureCode");
            resultMap.put("success", false);
            resultMap.put("code", 500);
            if (StringUtil.isEmpty(designName)) {
                resultMap.put("message", "请输入方案名称！");
                return JSON.toJSONString(resultMap);
            }
            if (StringUtil.isEmpty(ensureCode)) {
                resultMap.put("message", "请输入福利编号！");
                return JSON.toJSONString(resultMap);
            }
            //根据福利编号查询该福利是否是正确的弹性福利
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            if (fcEnsure == null || !FLEX_CODE.equals(fcEnsure.getPlanType())) {
                resultMap.put("message", "福利编号错误，请核对！");
                return JSON.toJSONString(resultMap);
            }
            if (StringUtils.isBlank(designNo)) {
                designNo = "FA" + maxNoService.createMaxNo("DesignNo", "", 18);
                params.put("designNo", designNo);
            }
            //查询福利是否被已被方案绑定
            FCPlanHealthDesignRela checkPhd = fcHealthDesignMapper.selectPlanByEnsureCode(ensureCode);
            //查询方案编号是否已存在，不存在新增，存在修改
            FCHealthDesign checkExist = fcHealthDesignMapper.selectByPrimaryKey(designNo);
            if (checkExist == null) {
                if (checkPhd != null) {
                    resultMap.put("message", "该福利编号已绑定方案，不能重复绑定！");
                    return JSON.toJSONString(resultMap);
                }
                FCHealthDesign fcHealthDesign = addFCHealthDesign(token, params);
                fcHealthDesign = (FCHealthDesign) CommonUtil.initObject(fcHealthDesign, "INSERT");
                fcHealthDesignMapper.insert(fcHealthDesign);
                FCPlanHealthDesignRela fcPlanHealthDesignRela = addFCPlanHealthDesignRela(token, params);
                fcPlanHealthDesignRela = (FCPlanHealthDesignRela) CommonUtil.initObject(fcPlanHealthDesignRela, "INSERT");
                fcPlanHealthDesignRelaMapper.insert(fcPlanHealthDesignRela);
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "新增方案成功！");
            } else {
                Map<String, String> checkByDesignNo = fcHealthDesignMapper.selectPlanByDesignNo(designNo);
                params.put("designStatus", checkByDesignNo.get("DesignStatus"));
                //修改福利编号
                if (!ensureCode.equals(checkByDesignNo.get("EnsureCode"))) {
                    if (checkPhd != null) {
                        resultMap.put("message", "该福利编号已绑定方案，不能重复绑定！");
                        return JSON.toJSONString(resultMap);
                    }
                    //查询该福利方案是否绑定方案详情
                    int countDetail = fcHealthDesignMapper.selectDetailByDesignNo(designNo);
                    if (countDetail > 0) {
                        resultMap.put("message", "若修改方案，请先删除健康告知方案详情！");
                        return JSON.toJSONString(resultMap);
                    }
                }
                //根据方案编号修改方案表
                FCHealthDesign fcHealthDesign = addFCHealthDesign(token, params);
                fcHealthDesign = (FCHealthDesign) CommonUtil.initObject(fcHealthDesign, "UPDATE");
                fcHealthDesignMapper.updateByPrimaryKeySelective(fcHealthDesign);
                //根据方案编号修改关联表
                FCPlanHealthDesignRela fcPlanHealthDesignRela = addFCPlanHealthDesignRela(token, params);
                fcPlanHealthDesignRela = (FCPlanHealthDesignRela) CommonUtil.initObject(fcPlanHealthDesignRela, "UPDATE");
                fcPlanHealthDesignRelaMapper.updateByDesignNo(fcPlanHealthDesignRela);
                resultMap.put("success", true);
                resultMap.put("code", 200);
                resultMap.put("message", "方案修改成功！");
            }
        } catch (Exception e) {
            Log.info("方案维护失败" + e.getMessage());
            e.printStackTrace();
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 查询方案信息
     *
     * @param token
     * @param params
     * @param page
     * @param rows
     * @return
     */
    public String getPlanHealthDesignByParams(String token, Map<String, String> params, int page, int rows, String isReal) {
        Map<String, Object> resultMap = new HashMap<>();
        // 获取token信息
        GlobalInput globalInput = userService.getSession(token);
        String manageCom = globalInput.getManageCom();
        if (org.springframework.util.StringUtils.isEmpty(manageCom)) {
            throw new SystemException("初审岗用户管理机构为空！");
        }
        try {
            PageHelper.startPage(page, rows);
            params.put("isReal", isReal);
            params.put("manageCom", manageCom);
            List<Map<String, Object>> planHealthDesignList = fcHealthDesignMapper.selectPlanHealthInfoByParams(params);
            PageHelperUtil<Map<String, Object>> teamPageInfo = new PageHelperUtil<>(planHealthDesignList);
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("recordsTotal", teamPageInfo.getTotal());
            dataMap.put("planHealthDesignList", teamPageInfo.getList());
            resultMap.put("data", dataMap);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "查询方案信息成功");
        } catch (Exception e) {
            Log.info("查询方案信息失败:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询方案信息失败");
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 退回后台配置 修改方案状态
     *
     * @param token
     * @param params
     * @return
     */
    public String modifyDesignStatus(String token, Map<String, String> params) {
        HashMap<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        try {
            String designNo = params.get("designNo");
            String designStatus = params.get("designStatus");
            if ("3".equals(designStatus)) {
                int countDetail = fcHealthDesignMapper.selectDetailByDesignNo(designNo);
                if (countDetail == 0) {
                    resultMap.put("success", false);
                    resultMap.put("code", 500);
                    resultMap.put("message", "请先导入健康告知方案详情，再提交复核！");
                    return JSON.toJSONString(resultMap);
                }
            }
            FCPlanHealthDesignRela fcPlanHealthDesignRela = addFCPlanHealthDesignRela(token, params);
            fcPlanHealthDesignRela = CommonUtil.initObject(fcPlanHealthDesignRela, "UPDATE");
            fcPlanHealthDesignRelaMapper.updateByPrimaryKeySelective(fcPlanHealthDesignRela);
            resultMap.put("success", true);
            resultMap.put("code", 200);
            switch (designStatus) {
                case ("1"):
                    resultMap.put("message", "退回成功！");
                    break;
                case ("3"):
                    resultMap.put("message", "提交成功！");
                    break;
                case ("4"):
                    resultMap.put("message", "复核完成！");
                    break;
                default:
            }
        } catch (Exception e) {
            Log.info("方案状态修改失败" + e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", 500);
            resultMap.put("message", "方案状态更新失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 删除方案信息
     *
     * @param token
     * @param designNo
     * @param ensureCode
     * @return
     */
    public String deletePlanInfoByDesignNo(String token, String designNo, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            //删除健告方案表
            fcHealthDesignMapper.deleteByPrimaryKey(designNo);
            //删除方案关联表
            fcPlanHealthDesignRelaMapper.deleteByPrimaryKey(ensureCode, designNo);
            //查询健告方案详情并删除
            List<FCHealthDesignDetailRela> detailRelaList = fcHealthDesignDetailRelaMapper.selectByDesignNo(designNo);
            if (detailRelaList.size() != 0) {
                for (FCHealthDesignDetailRela fcHealthDesignDetailRela : detailRelaList) {
                    String healthDesignNo = fcHealthDesignDetailRela.getHealthDesignNo();
                    fcHealthDesignDetailMapper.deleteByPrimaryKey(healthDesignNo);
                }
                fcHealthDesignDetailRelaMapper.deleteAllBydesign(designNo);
            }
            resultMap.put("success", true);
            resultMap.put("code", 200);
            resultMap.put("message", "删除成功！");
        } catch (Exception e) {
            Log.info("删除失败" + e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", 500);
            resultMap.put("message", "删除失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 查询方案详情信息
     *
     * @param token
     * @param params
     * @param page
     * @param rows
     * @return
     */
    public String getHealthDesignDetail(String token, Map<String, String> params, int page, int rows) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            PageHelper.startPage(page, rows);
//            List<FCHealthDesignDetail> detailList = fcHealthDesignMapper.selectHealthDesignDetail(params);
            List<FCHealthDesignDetail> detailList = fcHealthDesignMapper.selectHealthDesignDetailByGrpNo(params);
            PageHelperUtil teamPageInfo = new PageHelperUtil<>(detailList);
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("recordsTotal", teamPageInfo.getTotal());
            dataMap.put("detailList", teamPageInfo.getList());
            resultMap.put("data", dataMap);
            resultMap.put("success", true);
            resultMap.put("code", 200);
            resultMap.put("message", "查询成功！");
        } catch (Exception e) {
            Log.info("查询失败" + e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", 500);
            resultMap.put("message", "查询失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 修改健康告知详情
     *
     * @param token
     * @param fcHealthDesignDetail
     * @return
     */
    public String updateHealthDesignDetail(String token, FCHealthDesignDetail fcHealthDesignDetail, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        try {
            String healthDesignNo = fcHealthDesignDetail.getHealthDesignNo();
            String riskCode = fcHealthDesignDetail.getRiskCode();
            String gradeLevelTopLimit = fcHealthDesignDetail.getGradeLevelTopLimit();
            String gradeLevelLowLimit = fcHealthDesignDetail.getGradeLevelLowLimit();
            String amntTopLimit2 = String.valueOf(fcHealthDesignDetail.getAmntTopLimit());
            String amntLowLimit3 = String.valueOf(fcHealthDesignDetail.getAmntLowLimit());
            String amntTopLimit = "";
            String amntLowLimit = "";
            if (!StringUtil.isEmpty(amntTopLimit2)) {
                BigDecimal amntTopLimit1 = new BigDecimal(amntTopLimit2);
                amntTopLimit = amntTopLimit1.toPlainString();
            }
            if (!StringUtil.isEmpty(amntLowLimit3)) {
                BigDecimal amntLowLimit1 = new BigDecimal(amntLowLimit3);
                amntLowLimit = amntLowLimit1.toPlainString();
            }

            String ageTopLimit = fcHealthDesignDetail.getAgeTopLimit();
            String ageLowLimit = fcHealthDesignDetail.getAgeLowLimit();
            String sex = fcHealthDesignDetail.getSex();
            String insuredType = fcHealthDesignDetail.getInsuredType();
            //得到险种编码集合/职级编码集合/职级编码对应的职级顺序
            List<String> riskCodeList = getRiskCodeList();
            String grpNo = fcEnsureMapper.selectByPrimaryKey(ensureCode).getGrpNo();
            List<String> gradeList = getGradeList(grpNo);
            Map<String, String> gradeMap = getGradeMap(grpNo);
            resultMap.put("gradeList", gradeList);
            resultMap.put("gradeMap", gradeMap);
            List<String> errorMsgList = new ArrayList<>();
            String errorMsg = "";
            Boolean boo = true;
            if (StringUtil.isEmpty(riskCode)) {
                errorMsg += "险种编码不能为空，请核对！/";
            } else if (!riskCodeList.contains(riskCode)) {
                errorMsg += "险种编码录入错误，请核对！/";
            }
            if (!StringUtil.isEmpty(riskCode)) {
                boo = false;
            }
            if (StringUtil.isNotEmpty(gradeLevelTopLimit) || StringUtil.isNotEmpty(gradeLevelLowLimit)) {
                if (StringUtil.isEmpty(gradeLevelTopLimit) || StringUtil.isEmpty(gradeLevelLowLimit)) {
                    errorMsg += "职级上限、职级下限为绑定录入，请核对！/";
                } else if (!gradeList.contains(gradeLevelTopLimit) || !gradeList.contains(gradeLevelLowLimit)) {
                    errorMsg += "职级上、下限录入错误，请核对!/";
                } else if (Double.valueOf(gradeMap.get(gradeLevelLowLimit)) < Double.valueOf(gradeMap.get(gradeLevelTopLimit))) {
                    errorMsg += "职级下限不能大于上限，请核对！/";
                }
                boo = false;
            }
            if (StringUtil.isNotEmpty(amntTopLimit) || StringUtil.isNotEmpty(amntLowLimit)) {
                if (StringUtil.isEmpty(amntTopLimit) || StringUtil.isEmpty(amntLowLimit)) {
                    errorMsg += "保额上限、保额下限为绑定录入，请核对！/";
                } else if (!ensureMakeService.isNumber0(amntTopLimit) || !ensureMakeService.isNumber0(amntLowLimit)) {
                    errorMsg += "保额上限、下限应为正数，请核对！/";
                } else if (fcHealthDesignDetail.getAmntLowLimit() > fcHealthDesignDetail.getAmntTopLimit()) {
                    errorMsg += "保额下限不能大于上限，请核对！/";
                }
                boo = false;
            }
            if (StringUtil.isNotEmpty(ageTopLimit) || StringUtil.isNotEmpty(ageLowLimit)) {
                if (StringUtil.isEmpty(ageTopLimit) || StringUtil.isEmpty(ageLowLimit)) {
                    errorMsg += "年龄上限、年龄下限为绑定录入，请核对！/";
                } else if (!ageTopLimit.matches("^[0-9]\\d*$") || !ageLowLimit.matches("^[0-9]\\d*$")) {
                    errorMsg += "年龄上限、下限应为整数，请核对！/";
                } else if (Integer.valueOf(ageLowLimit) > Integer.valueOf(ageTopLimit)) {
                    errorMsg += "年龄下限不能大于上限，请核对！/";
                }
                boo = false;
            }
            //性别
            if (StringUtil.isNotEmpty(sex)) {
                if (!Arrays.asList("0", "1").contains(sex)) {
                    errorMsg += "性别只能填男或者女，请核对！/";
                }
                boo = false;
            }
            //被保人类型
            if (StringUtil.isNotEmpty(insuredType)) {
                if (!Arrays.asList("0", "1").contains(insuredType)) {
                    errorMsg += "被保人类别只能填员工或者家属，请核对！/";
                }
                boo = false;
            }
            if (boo) {
                errorMsg += "至少要填一项内容，请核对！/";
            }
            if (!"".equals(errorMsg)) {
                // 去前后空格&&去空string
                Splitter split = Splitter.on('/').trimResults().omitEmptyStrings();
                errorMsgList = new ArrayList<>(split.splitToList(errorMsg));
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", errorMsgList);
            } else {
                FCHealthDesignDetail fcHealthDesignDetailOld = fcHealthDesignDetailMapper.selectByPrimaryKey(healthDesignNo);
                fcHealthDesignDetail.setGradeLevelTopLimit(gradeMap.get(gradeLevelTopLimit));
                fcHealthDesignDetail.setGradeLevelLowLimit(gradeMap.get(gradeLevelLowLimit));
                fcHealthDesignDetail.setSex(sex);
                fcHealthDesignDetail.setInsuredType(insuredType);
                fcHealthDesignDetail.setOperator(globalInput.getUserNo());
                fcHealthDesignDetail.setMakeDate(fcHealthDesignDetailOld.getMakeDate());
                fcHealthDesignDetail.setMakeTime(fcHealthDesignDetailOld.getMakeTime());
                fcHealthDesignDetail = CommonUtil.initObject(fcHealthDesignDetail, "UPDATE");
                fcHealthDesignDetailMapper.updateByPrimaryKey(fcHealthDesignDetail);
                resultMap.put("success", true);
                resultMap.put("code", 200);
                resultMap.put("message", "修改成功！");
            }
        } catch (Exception e) {
            Log.info(e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", 500);
            resultMap.put("message", "修改失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 删除健康告知方案详情
     *
     * @param token
     * @param healthDesignNo
     * @return
     */
    public String deleteHealthDesignDetail(String token, String ensureCode, String designNo, String healthDesignNo) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        try {
            fcHealthDesignDetailMapper.deleteByPrimaryKey(healthDesignNo);
            fcHealthDesignDetailRelaMapper.deleteByPrimaryKey(designNo, healthDesignNo);
            //删完详情，方案状态变为待配置
            if (fcHealthDesignDetailRelaMapper.selectByDesignNo(designNo).size() == 0) {
                Map<String, String> params = new HashMap<>();
                params.put("ensureCode", ensureCode);
                params.put("designNo", designNo);
                params.put("designStatus", "0");
                modifyDesignStatus(token, params);
                resultMap.put("isStatus", "0");
            }
            resultMap.put("success", true);
            resultMap.put("code", 200);
            resultMap.put("message", "删除成功！");
        } catch (Exception e) {
            Log.info(e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", 500);
            resultMap.put("message", "删除失败！");
        }
        return JSON.toJSONString(resultMap);

    }

    /**
     * 导入健康告知方案详情解析
     *
     * @param token
     * @param file
     * @param ensureCode
     * @return
     */
    public String importHealthDesignDetailExcel(String token, MultipartFile file, String status, String ensureCode, String designNo) {
        Map<String, Object> resultMap = new HashMap<>();
        String path = FileUtil.getLocalPath("0503");
        String fileName = ensureCode + file.getOriginalFilename();
        String filePath = path + fileName;
        boolean uploadSuccess = fileService.fileUpload(file, filePath, "0503", ensureCode);
        if (uploadSuccess == false) {
            Log.info("健康告知方案详情导入失败：文件上传失败！");
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "健康告知方案详情导入失败！");
            return JSON.toJSONString(resultMap);
        }
        Workbook wb = null;
        try {
            wb = ExcelUtil.initWorkbook(filePath);
            resultMap = dealDetailExcel(token, status, wb, ensureCode, designNo);
        } catch (IOException e) {
            Log.info(e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "导入失败");
        }
        return JSON.toJSONString(resultMap);

    }


    @Transactional
    public Map<String, Object> dealDetailExcel(String token, String status, Workbook wb, String ensureCode, String designNo) {
        // 计划导入提示信息
        String errorMsg = "";
        Map<String, Object> resultMap = new HashMap<String, Object>();
        //存储错误信息
        List<String> errorMsgList = new ArrayList<>();
        List<FCHealthDesignDetail> fcHealthDesignDetailList = new ArrayList<FCHealthDesignDetail>();
        List<FCHealthDesignDetailRela> fcHealthDesignDetailRelaList = new ArrayList<FCHealthDesignDetailRela>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String grpNo = fcEnsureMapper.selectByPrimaryKey(ensureCode).getGrpNo();
            String operator = globalInput.getUserNo();
            //得到险种编码集合/职级编码集合/职级编码对应的职级顺序
            List<String> riskCodeList = getRiskCodeList();
            List<String> gradeList = getGradeList(grpNo);
            Map<String, String> gradeMap = getGradeMap(grpNo);
//            List<String> gradeList = getGradeList(ensureCode);
//            Map<String, String> gradeMap = getGradeMap(ensureCode);
            //第3行为数据的起始行
            //第一个sheet页
            Sheet sheet = wb.getSheetAt(0);
            Row row = null;
            //错误行
            int errorRow = 0;
            boolean boo = true;
            for (int i = 2; i <= sheet.getLastRowNum(); i++) {
                boo = true;
                errorRow = i + 1;
                row = sheet.getRow(i);
                if (ExcelUtil.isRowEmpty(row, 1)) {
                    Log.info("第" + (i + 1) + "行是无效数据。" + ExcelUtil.getCellValue(row.getCell(0)));
                    continue;
                }
                String riskCode = ExcelUtil.getCellValue(row.getCell(1));
                String gradeLevelTopLimit = ExcelUtil.getCellValue(row.getCell(2));
                String gradeLevelLowLimit = ExcelUtil.getCellValue(row.getCell(3));
                String amntTopLimit = ExcelUtil.getCellValue(row.getCell(4));
                String amntLowLimit = ExcelUtil.getCellValue(row.getCell(5));
                String ageTopLimit = ExcelUtil.getCellValue(row.getCell(6));
                String ageLowLimit = ExcelUtil.getCellValue(row.getCell(7));
                String sex = ExcelUtil.getCellValue(row.getCell(8));
                if ("男".equals(sex)) {
                    sex = "0";
                } else if ("女".equals(sex)) {
                    sex = "1";
                }
                String insuredType = ExcelUtil.getCellValue(row.getCell(9));
                if ("员工".equals(insuredType)) {
                    insuredType = "0";
                } else if ("家属".equals(insuredType)) {
                    insuredType = "1";
                }
                //险种编码
                if (StringUtil.isEmpty(riskCode)) {
                    errorMsg += "请录入第" + errorRow + "行险种编码/";
                } else {
                    if (!riskCodeList.contains(riskCode)) {
                        errorMsg += "第" + errorRow + "行险种编码录入错误，请核对！/";
                    }
                    //职级上限、职级下限
                    if (StringUtil.isNotEmpty(gradeLevelTopLimit) || StringUtil.isNotEmpty(gradeLevelLowLimit)) {
                        if (StringUtil.isEmpty(gradeLevelTopLimit) || StringUtil.isEmpty(gradeLevelLowLimit)) {
                            errorMsg += "第" + errorRow + "行职级上限、职级下限为绑定录入，请核对！/";
                        } else if (!gradeList.contains(gradeLevelTopLimit) || !gradeList.contains(gradeLevelLowLimit)) {
                            errorMsg += "第" + errorRow + "行职级上、下限录入错误，请核对!/";
                        } else if (Double.valueOf(gradeMap.get(gradeLevelLowLimit)) < Double.valueOf(gradeMap.get(gradeLevelTopLimit))) {
//                        } else if (Double.valueOf(gradeLevelLowLimit) > Double.valueOf(gradeLevelTopLimit)) {
                            errorMsg += "第" + errorRow + "行职级下限不能大于上限，请核对！/";
                        }
                        boo = false;
                    }
                    //保额上限、保额下限
                    if (StringUtil.isNotEmpty(amntTopLimit) || StringUtil.isNotEmpty(amntLowLimit)) {
                        if (StringUtil.isEmpty(amntTopLimit) || StringUtil.isEmpty(amntLowLimit)) {
                            errorMsg += "第" + errorRow + "行保额上限、保额下限为绑定录入，请核对！/";
                        } else if (!ensureMakeService.isNumber0(amntTopLimit) || !ensureMakeService.isNumber0(amntLowLimit)) {
                            errorMsg += "第" + errorRow + "行保额上限、下限应为正数，请核对！/";
                        } else if (Double.valueOf(amntLowLimit) > Double.valueOf(amntTopLimit)) {
                            errorMsg += "第" + errorRow + "行保额下限不能大于上限，请核对！/";
                        }
                        boo = false;
                    }
                    //年龄上限、年龄下限
                    if (StringUtil.isNotEmpty(ageTopLimit) || StringUtil.isNotEmpty(ageLowLimit)) {
                        if (StringUtil.isEmpty(ageTopLimit) || StringUtil.isEmpty(ageLowLimit)) {
                            errorMsg += "第" + errorRow + "行年龄上限、年龄下限为绑定录入，请核对！/";
                        } else if (!ageTopLimit.matches("^[0-9]\\d*$") || !ageLowLimit.matches("^[0-9]\\d*$")) {
                            errorMsg += "第" + errorRow + "行年龄上限、下限应为整数，请核对！/";
                        } else if (Integer.valueOf(ageLowLimit) > Integer.valueOf(ageTopLimit)) {
                            errorMsg += "第" + errorRow + "行年龄下限不能大于上限，请核对！/";
                        }
                        boo = false;
                    }
                    //性别
                    if (StringUtil.isNotEmpty(sex)) {
                        if (!Arrays.asList("0", "1").contains(sex)) {
                            errorMsg += "第" + errorRow + "行性别只能填男或者女，请核对！/";
                        }
                        boo = false;
                    }
                    //被保人类型
                    if (StringUtil.isNotEmpty(insuredType)) {
                        if (!Arrays.asList("0", "1").contains(insuredType)) {
                            errorMsg += "第" + errorRow + "行被保人类别只能填员工或者家属，请核对！/";
                        }
                        boo = false;
                    }
                }
                //5项内容不能全部为空
                if (boo) {
                    errorMsg += "请完善第" + errorRow + "行信息！/";
                }
                if ("".equals(errorMsg)) {
                    FCHealthDesignDetail fcHealthDesignDetail = new FCHealthDesignDetail();
                    String healthDesignNo = "JG" + maxNoService.createMaxNo("healthDesignNo", "", 18);
                    fcHealthDesignDetail.setHealthDesignNo(healthDesignNo);
                    fcHealthDesignDetail.setRiskCode(riskCode);
//                    fcHealthDesignDetail.setGradeLevelTopLimit(gradeLevelTopLimit);
//                    fcHealthDesignDetail.setGradeLevelLowLimit(gradeLevelLowLimit);
                    fcHealthDesignDetail.setGradeLevelTopLimit(gradeMap.get(gradeLevelTopLimit));
                    fcHealthDesignDetail.setGradeLevelLowLimit(gradeMap.get(gradeLevelLowLimit));
                    if (StringUtil.isNotEmpty(amntTopLimit)) {
                        fcHealthDesignDetail.setAmntTopLimit(Double.valueOf(amntTopLimit));
                    }
                    if (StringUtil.isNotEmpty(amntLowLimit)) {
                        fcHealthDesignDetail.setAmntLowLimit(Double.valueOf(amntLowLimit));
                    }
                    fcHealthDesignDetail.setAgeTopLimit(ageTopLimit);
                    fcHealthDesignDetail.setAgeLowLimit(ageLowLimit);
                    fcHealthDesignDetail.setSex(sex);
                    fcHealthDesignDetail.setInsuredType(insuredType);
                    fcHealthDesignDetail.setOperator(operator);
                    fcHealthDesignDetail = CommonUtil.initObject(fcHealthDesignDetail, "INSERT");
                    fcHealthDesignDetailList.add(fcHealthDesignDetail);
                    FCHealthDesignDetailRela fcHealthDesignDetailRela = new FCHealthDesignDetailRela();
                    fcHealthDesignDetailRela.setDesignNo(designNo);
                    fcHealthDesignDetailRela.setHealthDesignNo(healthDesignNo);
                    fcHealthDesignDetailRela.setOperator(operator);
                    fcHealthDesignDetailRela = CommonUtil.initObject(fcHealthDesignDetailRela, "INSERT");
                    fcHealthDesignDetailRelaList.add(fcHealthDesignDetailRela);
                }
            }
            if ("".equals(errorMsg)) {
                // 判断是添加导入还是覆盖导入 "1"-添加导入;"2"-覆盖导入
                if (status != null && "2".equals(status)) {
                    List<FCHealthDesignDetailRela> detailRelaList = fcHealthDesignDetailRelaMapper.selectByDesignNo(designNo);
                    for (FCHealthDesignDetailRela fcHealthDesignDetailRela : detailRelaList) {
                        fcHealthDesignDetailMapper.deleteByPrimaryKey(fcHealthDesignDetailRela.getHealthDesignNo());
                    }
                    fcHealthDesignDetailRelaMapper.deleteAllBydesign(designNo);
                }
                fcHealthDesignDetailMapper.insertList(fcHealthDesignDetailList);
                fcHealthDesignDetailRelaMapper.insertList(fcHealthDesignDetailRelaList);
                //更新方案状态
                Map<String, String> params = new HashMap<>();
                params.put("ensureCode", ensureCode);
                params.put("designNo", designNo);
                params.put("designStatus", "2");
                modifyDesignStatus(token, params);
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "方案导入成功！");
            } else {
                // 去前后空格&&去空string
                Splitter split = Splitter.on('/').trimResults().omitEmptyStrings();
                errorMsgList = new ArrayList<>(split.splitToList(errorMsg));
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", errorMsgList);
                return resultMap;
            }
        } catch (NumberFormatException e) {
            Log.info("方案导入失败：" + e.getMessage());
        }
        return resultMap;
    }

    //得到险种编码集合
    public List<String> getRiskCodeList() {
        return fdRiskInfoMapper.selectRiskCode().stream().map(fdRiskInfo -> fdRiskInfo.getRiskCode()).collect(Collectors.toList());
    }

    //得到职级编码集合
    public List<String> getGradeList(String grpNo) {
        return fcBusPersonTypeMapper.selectByGrpNo(grpNo).stream().map(busPersonType -> busPersonType.getGradeLevelCode()).collect(Collectors.toList());
    }

    //得到职级编码对应的职级顺序
    public Map<String, String> getGradeMap(String grpNo) {
        return fcBusPersonTypeMapper.selectByGrpNo(grpNo).stream().collect(Collectors.toMap(busPersonType -> busPersonType.getGradeLevelCode(), busPersonType -> busPersonType.getOrderNum()));
    }

    public FCHealthDesign addFCHealthDesign(String token, Map<String, String> params) {
        FCHealthDesign fcHealthDesign = new FCHealthDesign();
        GlobalInput globalInput = userService.getSession(token);
        fcHealthDesign.setDesignNo(params.get("designNo"));
        fcHealthDesign.setDesignName(params.get("designName"));
        fcHealthDesign.setOperator(globalInput.getUserNo());
        fcHealthDesign.setOperatorCom("");
        return fcHealthDesign;
    }

    public FCPlanHealthDesignRela addFCPlanHealthDesignRela(String token, Map<String, String> params) {
        FCPlanHealthDesignRela fcPlanHealthDesignRela = new FCPlanHealthDesignRela();
        GlobalInput globalInput = userService.getSession(token);
        fcPlanHealthDesignRela.setDesignNo(params.get("designNo"));
        fcPlanHealthDesignRela.setEnsureCode(params.get("ensureCode"));
        String designStatus = params.get("designStatus") == null ? "0" : params.get("designStatus");
        String returnReason = params.get("returnReason") == null ? "" : params.get("returnReason");
        fcPlanHealthDesignRela.setDesignStatus(designStatus);
        fcPlanHealthDesignRela.setReturnReason(returnReason);
        fcPlanHealthDesignRela.setOperator(globalInput.getUserNo());
        fcPlanHealthDesignRela.setOperatorCom("");
        return fcPlanHealthDesignRela;
    }

}
