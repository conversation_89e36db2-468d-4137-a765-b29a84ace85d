

/**
 * Copyright (c) 2002 sinosoft  Co. Ltd.
 * All right reserved.
 */
package com.sinosoft.eflex.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.sinosoft.eflex.dao.FRCalModeMapper;
import com.sinosoft.eflex.model.FRCalMode;
import com.sinosoft.eflex.util.StringUtil;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import ch.qos.logback.classic.Logger;
/*
 * <p>Title: 保费计算类 </p> <p>Description: 通过传入的保单信息和责任信息构建出保费信息和领取信息 </p> <p>Copyright:
 * Copyright (c) 2002</p> <p>Company: sinosoft</p> <AUTHOR>
 *
 * @version 1.0 @date 2002-07-01
 */
@Service
public class Calculator{
	/***************************我的start*************************/
	/**
	 * 日志
	 *
	 */
	private static Logger Log = (Logger) LoggerFactory.getLogger(Calculator.class);
	@Autowired
	private FRCalModeMapper frCalModeMapper;
	@Autowired
	private JdbcTemplate jdbcTemplate;
	/***************************我的end*************************/
	/** 错误处理类，每个需要错误处理的类中都放置该类 */
	//public CErrors mErrors = new CErrors();

	/** 计算需要用到的保单号码 */
	public String PolNo;


	/**
	 * 各种要素存放的琏表 1--基本要素、和常量要素相同，但是优先级最低 2--扩展要素，根据SQL语句从新计算 3--常量要素（只取默认值）

	 private LMCalFactorSet mCalFactors1 = new LMCalFactorSet(); // 存放基本要素
	 public LMCalFactorSet mCalFactors = new LMCalFactorSet();
	 */
	private Map<String, String> basicFctors = new HashMap<String, String>();
	// @Field
	// 计算编码
	private String CalCode = "";
	// 算法对应SQL语句所在表结构
	// private LMCalModeSchema mLMCalMode = new LMCalModeSchema();

	private FRCalMode frCalMode = new FRCalMode();


	//tongmeng 2011-05-23 modify
	//Calculator修改成支持批量调用
	//批量调用的算法编码
	private List mBatchCalCodeList = new ArrayList();
	//tongmeng 2011-05-25
	private HashMap mCalculatorResult =  new HashMap();


	/**
	 * 增加基本要素
	 *
	 * @param cFactorCode
	 *            要素的编码
	 * @param cFactorValue
	 *            要素的数据值
	 */
	public void addBasicFactor(String cFactorCode, String cFactorValue) {
		if(cFactorValue!=null) {
			basicFctors.put(cFactorCode, cFactorValue);
		}
	}
	public String getBasicFactor(String cFactorCode) {
		return basicFctors.get(cFactorCode);
	}


	// @Method
	public void setCalCode(String tCalCode) {
		CalCode = tCalCode;
	}

	/**
	 * 返回计算的结果
	 * @return
	 */
	public HashMap getCalResult()
	{
		return this.mCalculatorResult;
	}
	/**
	 * 公式计算函数
	 *
	 * @return: String 计算的结果，只能是单值的数据（数字型的转换成字符型）
	 * @author: YT
	 */
	public String calculate() {

		Log.info("start calculate++++++++++++++");
		if (!checkCalculate()) {
			return "0";
		}

		try {
			return calByCalculator();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			//CError.buildErr(this, e.toString());
			Log.info("公式计算函数异常:",e);
			return "0";
		}
	}
	/**
	 * 调用计算引擎计算
	 * @return
	 */
	private String calByCalculator() {
		// 取得数据库中计算要素
		//Log.info("calCode :" + CalCode);
		//modify by cuimq end
		// 解释计算要素
		/*if (!interpretFactors()) {
			return "0";
		}*/

		// 读取SQL语句
		if (!getSQL()) {
			return "0";
		}

		// 解释SQL语句中的变量
		if (!interpretFactorInSQL()) {
			return "0";
		}
		// 执行SQL语句
		Log.info("start execute SQL.....");
		return executeSQL();
	}


	/**
	 * 执行SQL语句
	 *
	 * @return String
	 */
	private String executeSQL() {
		String tReturn = "0";
		try {
			List<String> list = jdbcTemplate.queryForList(frCalMode.getCalSQL(),String.class);
			if(list == null || list.size() == 0 ){
				Log.info("execute SQL result : null" );
				return null;
			}else if(list.size()>0){
				tReturn = (String) list.get(0);
			}
		} catch (Exception e) {
			Log.info("执行SQL语句异常:",e);
			//CError.buildErr(this,"执行SQL语句：" + mLMCalMode.getCalCode() + "失败!");
			Log.info("执行SQL语句：" + frCalMode.getCalSQL() + "失败!");
			return "0";
		}
		Log.info("execute SQL result : " + tReturn );
		return tReturn;



	}

	/**
	 * 解释SQL语句中的变量
	 *
	 * @return boolean
	 */
	private boolean interpretFactorInSQL() {
		String tSql, tStr = "", tStr1 = "";
		tSql = frCalMode.getCalSQL();
		Log.info(tSql);
		// tSql=tSql.toLowerCase() ;
		//注意此处不要用split方法
		int tQuestionMarkCount = 0;
		tQuestionMarkCount = tSql.length() - tSql.replaceAll("\\?","").length();
		if (tQuestionMarkCount % 2 != 0) {
			//CError.buildErr(this,"lmcalmode中SQL语句的?问号个数不匹配，请核查");
			Log.info("lmcalmode中SQL语句的?问号个数不匹配，请核查");
			return false;
		}
		int count=0;
		try {
			while (true) {
				tStr = StringUtil.getStr(tSql, 2, "?");
				if (tStr.equals("")) {
					break;
				}
				tStr1 = "?" + tStr + "?";
				// 替换变量  //modify by zzm
				tSql = StringUtil.replaceEx(tSql, tStr1, getValueByName(tStr.trim()));
				if(count++>1000){
					Log.info("Error SQL:"+frCalMode.getCalSQL());
					throw new IllegalArgumentException("Error SQL:"+frCalMode.getCalSQL());
				}
			}
		} catch (Exception ex) {
			// @@错误处理
			//CError.buildErr(this,"解释" + tSql + "的变量:" + tStr + "时出错。");
			Log.info("解释" + tSql + "的变量:" + tStr + "时出错。");
			return false;
		}
		Log.info("解析后的sql: " + tSql);
		frCalMode.setCalSQL(tSql);
		return true;
	}



	/**
	 * 读取SQL语句
	 *
	 * @return boolean
	 */
	private boolean getSQL() {
		FRCalMode frCalMode = frCalModeMapper.selectByPrimaryKey(CalCode);
		if (frCalMode == null) {
			//CError.buildErr(this,"得到" + CalCode + "的SQL语句时出错。");
			Log.info("得到" + CalCode + "的SQL语句时出错。");
			return false;
		}
		this.frCalMode = frCalMode;
		return true;
	}

	/**
	 * 解释要素连表中的非变量要素
	 *
	 * @return boolean
	 */
	private boolean interpretFactors() {
		/*int i, iMax;
		LMCalFactorSchema tC = new LMCalFactorSchema();
		iMax = mCalFactors.size();
		for (i = 1; i <= iMax; i++) {
			tC = mCalFactors.get(i);
			// 如果是扩展要素，则解释该扩展要素
			if (tC.getFactorType().toUpperCase().equals("2")) {
				tC.setFactorDefault(calSubFactors(tC));
				// 如果在计算子要素的时候发生错误，则返回false
				if (this.mErrors.needDealError()) {
					return false;
				}
			}
		}*/
		return true;
	}

	/**
	 * 校验计算的输入是否足够
	 *
	 * @return boolean 如果不正确返回false
	 */
	private boolean checkCalculate() {
		if (CalCode == null || CalCode.equals("")) {
			// @@错误处理
			//CError.buildErr(this,"计算时必须有计算编码。");
			Log.info("计算时必须有计算编码CalCode。");
			return false;
		}else{
			FRCalMode frCalMode = frCalModeMapper.selectByPrimaryKey(CalCode);
			if(frCalMode==null){
				Log.info("计算编码CalCode在FRCalMode表中不存在。");
				return false;
			}
		}
		return true;
	}

	/**
	 * 根据变量名得到变量的值
	 *
	 * @param cVarName
	 *            String
	 * @return String 如果不正确返回"",否则返回变量值
	 */
	private String getValueByName(String cVarName) {
		String tReturn = "";
		if(basicFctors.get(cVarName)!=null){
			tReturn = basicFctors.get(cVarName);
			/*
			if (tReturn.indexOf("?") != -1) {
				return tReturn.replace("?", "");
			}*/
		}
		return tReturn;
	}

}

