package com.sinosoft.eflex.service.claim;

import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.model.BatchInsureInterface.Head;
import com.sinosoft.eflex.model.BatchInsureInterface.RiskInfo;
import com.sinosoft.eflex.model.ClaimCount;
import com.sinosoft.eflex.model.ClaimInfo;
import com.sinosoft.eflex.model.GrpClaimCount;
import com.sinosoft.eflex.model.GrpContInfo;
import com.sinosoft.eflex.model.ResponseMsg;
import com.sinosoft.eflex.model.claim.ClaimResponseInfo;
import com.sinosoft.eflex.util.*;

import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2018-11-06 15:50
 */
@Service
public class ClaimCountService {

    private static Logger Log = (Logger) LoggerFactory.getLogger(TestService.class);

    @Autowired
    private MyProps myProps;
    @Autowired
    private QueryPolicyService queryPolicyService;

    /**
     * <AUTHOR>
     * @description企业理赔统计
     * @date 17:19 17:19
     * @modified
     */
    public String getGrpClaimCount(String grpContNo) {
        Map<String, Object> resultMap = new HashMap<>();
        RemoteDelegate rd = RemoteDelegate.getInstance();
        //判断订单号是否为空
        if (grpContNo == null || "".equals(grpContNo)) {
            resultMap.put("code", "0");
            resultMap.put("message", "订单号不能为空");
            return JSON.toJSONString(resultMap);
        }
        try {
            String requestXml = "<?xml version=\"1.0\"?>\n" +
                    "<RequestInfo>\n" +
                    "\t<HEAD>\n" +
                    //交易流水号
                    "\t\t<TransRefGUID>" + UUID.randomUUID().toString().replaceAll("-", "") + "</TransRefGUID>\n" +
                    // 接口交易类型
                    "\t\t<TransType>BF0005</TransType>\n" +
                    // 交易日期
                    "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
                    // 交易时间
                    "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" +
                    "\t</HEAD>\n" +
                    "\t<BODY>\n" +
                    "\t\t<ClaimQuery>\n" +
                    "\t\t\t<GrpContNo>" + grpContNo + "</GrpContNo>\n" +
                    "\t\t</ClaimQuery>\n" +
                    "\t</BODY>\n" +
                    "</RequestInfo>";
            Log.info("请求报文：" + requestXml);
            long startTime = System.currentTimeMillis();
            // 调用核心接口
            boolean success = rd.submitData(myProps.getServiceInfo().get("serviceUrl"),
                    myProps.getServiceInfo().get("operation"), "", requestXml);
            long endTime = System.currentTimeMillis();
            Log.info("连接核心接口所用时间" + ((endTime - startTime) / 1000.0) + "秒");
            if (success) {
                Map<String, Object> responseXml = rd.getResult();
                Log.info("接收数据:" + JSON.toJSONString(responseXml));
                GrpClaimCount grpClaimCount = (GrpClaimCount) responseXml.get("GrpClaimCount");
                Head head = (Head) responseXml.get("Head");
                if (grpClaimCount != null) {
                    if (grpClaimCount.getRiskList().size() > 0) {
                        HashMap<String, String> claimAnalysis = getClaimAnalysis(grpClaimCount);
                        resultMap.put("claimAnalysis", claimAnalysis);
                    }
                    resultMap.put("data", grpClaimCount);
                    resultMap.put("code", head.getTransResult().getResultCode());
                    resultMap.put("message", head.getTransResult().getResultInfo());
                } else {
                    resultMap.put("code", "300");
                    resultMap.put("message", "企业理赔调试数据为空");
                }
            } else {
                resultMap.put("code", "400");
                resultMap.put("message", "查询企业理赔失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Log.info("打印错误报文" + e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("message", "查询理赔统计失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * <AUTHOR>
     * @description个人理赔统计
     * @date 17:20 17:20
     * @modified
     */
    public String getClaimCount(Map<String, String> map) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (map.get("grpContNo") == null || "".equals(map.get("grpContNo"))) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "团体保单号为必录项！");
                return JSON.toJSONString(resultMap);
            }
            String requestXml = "<?xml version=\"1.0\"?>\n" +
                    "<RequestInfo>\n" +
                    "\t<HEAD>\n" +
                    //交易流水号
                    "\t\t<TransRefGUID>" + CommonUtil.getUUID() + "</TransRefGUID>\n" +
                    // 接口交易类型
                    "\t\t<TransType>BF0006</TransType>\n" +
                    // 交易日期
                    "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
                    // 交易时间
                    "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" +
                    "\t</HEAD>\n" +
                    "\t<BODY>\n" +
                    "\t\t<ClaimQuery>\n" +
                    "\t\t\t<GrpContNo>" + map.get("grpContNo") + "</GrpContNo>\n" +
                    "\t\t\t<insuredName>" + map.get("insuredName") + "</insuredName>\n" +
                    "\t\t\t<certiType>" + map.get("certiType") + "</certiType>\n" +
                    "\t\t\t<certiCode>" + map.get("certiCode") + "</certiCode>\n" +
                    "\t\t</ClaimQuery>\n" +
                    "\t</BODY>\n" +
                    "</RequestInfo>";
            Log.info("请求报文：" + requestXml);
            long a = System.currentTimeMillis();
            // 调用核心接口
            RemoteDelegate remoteDelegate = RemoteDelegate.getInstance();
            boolean success = remoteDelegate.submitData(myProps.getServiceInfo().get("serviceUrl"),
                    myProps.getServiceInfo().get("operation"), "", requestXml);
            long b = System.currentTimeMillis();
            Log.info("连接核心接口所用时间" + ((b - a) / 1000.0) + "秒");
            if (success) {
                Map<String, Object> responseXml = remoteDelegate.getResult();
                Map<String, Object> claimAnalysis = new HashMap<>();
                Log.info("接收数据:" + JSON.toJSONString(responseXml));
                ClaimCount claimCount = (ClaimCount) responseXml.get("ClaimCount");
                Head head = (Head) responseXml.get("Head");
                if (claimCount != null) {
                    resultMap.put("claimAnalysis", claimAnalysis);
                    resultMap.put("data", claimCount);
                    resultMap.put("code", head.getTransResult().getResultCode());
                    resultMap.put("message", head.getTransResult().getResultInfo());
                } else {
                    resultMap.put("code", "300");
                    resultMap.put("message", "调用核心数据为空");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Log.info("打印错误报文" + e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("message", "查询理赔统计失败");
        }

        return JSON.toJSONString(resultMap);
    }


    public String exportGrpClaimCount(Map<String, Object> params, HttpServletResponse response) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (params.isEmpty()) {
                resultMap.put("code", "500");
                resultMap.put("message", "保单号不能为空");
                return JSON.toJSONString(resultMap);
            }
            System.out.println(JSON.toJSONString(params));
            if (!"".equals(params.get("insuredType")) && null != params.get("insuredType")) {
                params.put("insuredTypes", params.get("insuredType"));
            }
            Map<String, Object> map = queryPolicyService.claimCounmts(params);
            List<ClaimInfo> claimInfo = (List<ClaimInfo>) map.get("data");
            for (int i = 0; i < claimInfo.size(); i++) {
                String claimType = claimInfo.get(i).getClaimType();
                String status = claimInfo.get(i).getStatus();
                if ("00".equals(claimType)) {
                    claimInfo.get(i).setClaimType("医疗");
                } else if ("01".equals(claimType)) {
                    claimInfo.get(i).setClaimType("残疾");
                } else if ("02".equals(claimType)) {
                    claimInfo.get(i).setClaimType("身故");
                } else if ("03".equals(claimType)) {
                    claimInfo.get(i).setClaimType("全残");
                } else if ("04".equals(claimType)) {
                    claimInfo.get(i).setClaimType("重大疾病");
                } else if ("05".equals(claimType)) {
                    claimInfo.get(i).setClaimType("特定疾病");
                } else if ("09".equals(claimType)) {
                    claimInfo.get(i).setClaimType("豁免");
                } else if ("10".equals(claimType)) {
                    claimInfo.get(i).setClaimType("津贴");
                } else if ("11".equals(claimType)) {
                    claimInfo.get(i).setClaimType("疾病终末期");
                }

                if ("10".equals(status)) {
                    claimInfo.get(i).setStatus("报案");
                } else if ("15".equals(status)) {
                    claimInfo.get(i).setStatus("撤销");
                } else if ("20".equals(status)) {
                    claimInfo.get(i).setStatus("立案");
                } else if ("30".equals(status)) {
                    claimInfo.get(i).setStatus("审核");
                } else if ("40".equals(status)) {
                    claimInfo.get(i).setStatus("审批");
                } else if ("50".equals(status)) {
                    claimInfo.get(i).setStatus("结案");
                } else if ("60".equals(status)) {
                    claimInfo.get(i).setStatus("完成");
                } else if ("70".equals(status)) {
                    claimInfo.get(i).setStatus("关闭");
                }

                //身份证号脱敏处理
                claimInfo.get(i).setCertiCode(MaskUtils.maskIDCard(claimInfo.get(i).getCertiCode()));
            }
            // excel数组表头
            String[][] headers = {{"CertiCode", "证件号"},
                    {"InsuredName", "姓名"}, {"ClaimType", "理赔项目"},
                    {"RealPay", "赔付金额"}, {"IsCheck", "是否通过审核"}, {"Status", "当前进度"}};
            // 调用生成excel生成工具类,返回前台下载
            ServletOutputStream outputStream = response.getOutputStream();
            String filename = new String(("GrpClaimInfo_" + DateTimeUtil.getTimeString()).getBytes(), "ISO-8859-1");
            response.setHeader("Content-disposition", "attachment;filename=" + filename + ".xlsx");
            String title = "GrpClaimInfo";
            ExportExcelUtil.exportExcel(title, headers, claimInfo, outputStream);
            outputStream.flush();
            resultMap.put("code", "200");
            resultMap.put("message", "企业理赔清单导出excel成功");
        } catch (Exception e) {
            Log.info("打印错误报文", e);
            resultMap.put("code", "500");
            resultMap.put("message", "企业理赔清单导出excel失败");
        }
        return JSON.toJSONString(resultMap);
    }


    public String exportClaimCount(Map<String, Object> params, HttpServletResponse response) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (params.isEmpty()) {
                resultMap.put("code", "500");
                resultMap.put("message", "保单号不能为空");
                return JSON.toJSONString(resultMap);
            }
            System.out.println(JSON.toJSONString(params));
            Map<String, Object> map = queryPolicyService.claimCounmts(params);
            List<ClaimInfo> claimInfo = (List<ClaimInfo>) map.get("data");
            for (int i = 0; i < claimInfo.size(); i++) {
                String claimType = claimInfo.get(i).getClaimType();
                String status = claimInfo.get(i).getStatus();
                if ("00".equals(claimType)) {
                    claimInfo.get(i).setClaimType("医疗");
                } else if ("01".equals(claimType)) {
                    claimInfo.get(i).setClaimType("残疾");
                } else if ("02".equals(claimType)) {
                    claimInfo.get(i).setClaimType("身故");
                } else if ("03".equals(claimType)) {
                    claimInfo.get(i).setClaimType("全残");
                } else if ("04".equals(claimType)) {
                    claimInfo.get(i).setClaimType("重大疾病");
                } else if ("05".equals(claimType)) {
                    claimInfo.get(i).setClaimType("特定疾病");
                } else if ("09".equals(claimType)) {
                    claimInfo.get(i).setClaimType("豁免");
                } else if ("10".equals(claimType)) {
                    claimInfo.get(i).setClaimType("津贴");
                } else if ("11".equals(claimType)) {
                    claimInfo.get(i).setClaimType("疾病终末期");
                }

                if ("10".equals(status)) {
                    claimInfo.get(i).setStatus("报案");
                } else if ("15".equals(status)) {
                    claimInfo.get(i).setStatus("撤销");
                } else if ("20".equals(status)) {
                    claimInfo.get(i).setStatus("立案");
                } else if ("30".equals(status)) {
                    claimInfo.get(i).setStatus("审核");
                } else if ("40".equals(status)) {
                    claimInfo.get(i).setStatus("审批");
                } else if ("50".equals(status)) {
                    claimInfo.get(i).setStatus("结案");
                } else if ("60".equals(status)) {
                    claimInfo.get(i).setStatus("完成");
                } else if ("70".equals(status)) {
                    claimInfo.get(i).setStatus("关闭");
                }
            }
            // excel数组表头
            String[][] headers = {{"insuredName", "姓名"}, {"claimType", "理赔项目"},
                    {"RealPay", "赔付金额"}, {"isCheck", "是否通过审核"}, {"status", "当前进度"}};
            // 调用生成excel生成工具类,返回前台下载
            ServletOutputStream outputStream = response.getOutputStream();
            String filename = new String(("ClaimCount_" + DateTimeUtil.getTimeString()).getBytes(), "ISO-8859-1");
            response.setHeader("Content-disposition", "attachment;filename=" + filename + ".xlsx");
            String title = "ClaimCount";
            ExportExcelUtil.exportExcel(title, headers, claimInfo, outputStream);
            outputStream.flush();
            resultMap.put("code", "200");
            resultMap.put("meassage", "个人理赔清单导出excel成功");
        } catch (Exception e) {
            e.printStackTrace();
            Log.info("打印错误报文" + e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("message", "个人理赔清单导出excel失败");
        }
        return JSON.toJSONString(resultMap);
    }


    //赔付分析
    public HashMap<String, String> getClaimAnalysis(GrpClaimCount grpClaimCount) {
        List<RiskInfo> riskInfoList = grpClaimCount.getRiskList();
        HashMap<String, String> map = new HashMap<>();
        //总人次
        int totalNum = 0;
        //医疗险人次
        int medicalNum = 0;
        //意外险人次
        int accidentNum = 0;
        //重大疾病人次
        int diseaseNum = 0;
        //获取人次
        for (RiskInfo riskInfo : riskInfoList) {
            String getCount = riskInfo.getGetCount();
            String riskCode = riskInfo.getRiskCode();
            if (!"".equals(getCount) && getCount != null) {
                //计算险种总人次
                totalNum += Integer.valueOf(getCount);
                if ("16040".equals(riskCode)) {
                    diseaseNum = Integer.valueOf(riskInfo.getGetCount());
                    if (diseaseNum == 1) {
                        map.put("diseaseMsg", "本年度有员工办理重大疾病理赔手续，应多关注员工身心健康、劳逸结合，每年要求员工必须进行全面健康体检，防范于未然。");
                    } else if (diseaseNum > 1) {
                        map.put("diseaseMsg", "本年度员工重大疾病赔付较多，建议组织多种形式的关爱员工健康活动，包括全面体检、健康讲座、心理辅导等，安抚员工情绪。");
                    }
                }
                if ("15170".equals(riskCode) || "15030".equals(riskCode) || "15070".equals(riskCode) ||
                        "15100".equals(riskCode) || "15160".equals(riskCode)) {
                    accidentNum += Integer.valueOf(getCount);
                }
                if ("17010".equals(riskCode) || "17020".equals(riskCode) || "17030".equals(riskCode) || "15040".equals(riskCode) || "15060".equals(riskCode) || "17050".equals(riskCode)) {
                    medicalNum += Integer.valueOf(getCount);
                }
            }
        }
        if (accidentNum == 1) {
            map.put("accidentMsg", "本年度发生一起意外伤害事故，建议应组织安全警示教育，提高全员安全意识。");
        } else if (accidentNum > 1) {
            map.put("accidentMsg", "本年度已发生多起意外伤害事故，意外事故猛于虎，建议建议应组织安全警示教育、开展实地演练等，提高全员安全意识。");
        }
        double msg = (double) medicalNum / totalNum;
        if (msg > 0.03) {
            map.put("medicalMsg", "本年度员工办理医疗理赔手续较多，应倡导员工多加强日常锻炼，建议公司组织步行、骑行、爬山等活动，丰富业余生活。");
        }
        if (map.isEmpty()) {
            map.put("msg", "贵司员工健康状况良好，请多关注日常锻炼");
        }
        return map;
    }

}
