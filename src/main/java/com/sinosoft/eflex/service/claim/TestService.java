package com.sinosoft.eflex.service.claim;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.FCGrpInfoMapper;
import com.sinosoft.eflex.dao.FdUserMapper;
import com.sinosoft.eflex.model.FCGrpInfo;
import com.sinosoft.eflex.model.FdUser;
import com.sinosoft.eflex.model.GlobalInput;
import com.sinosoft.eflex.service.UserService;
import com.sinosoft.eflex.util.CommonUtil;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.RemoteDelegate;
import com.sinosoft.eflex.util.encrypt.LisIDEA;

/**
 * @DESCRIPTION
 * @create 2018-11-05 17:51
 **/
@Service
public class TestService {
    private static Logger Log = LoggerFactory.getLogger(TestService.class);
    @Autowired
    private UserService userService;
    @Autowired
    private FCGrpInfoMapper fcGrpInfoMapper;
    @Autowired
    private MyProps myProps;
    @Autowired
    private FdUserMapper fdUserMapper;


    /**
     * 查询密码
     * 
     * @param token
     * @param userName
     * @return
     */
    public List<FdUser> selectPassWord(String token, String userName) {
        GlobalInput globalInput = userService.getSession(token);
        Log.info("userNo: {}，userName: {}请求查询密码。", globalInput.getUserNo(), globalInput.getUserName());
        // 查询用户名对应的人员信息
        List<FdUser> fdUserList = fdUserMapper.findUsersByUsername(userName);
        fdUserList.forEach((FdUser fdUser) -> {
            LisIDEA lisIDEA = new LisIDEA();
            String str = lisIDEA.decryptString(fdUser.getPassWord());
            fdUser.setPassWord(str);
        });
        return fdUserList;
    }




    /**
     *  查询该企业下所有保单列表，包含平台保单和线下保单、包含有效、终止等所有状态的保单。
     * 
     * @param grpNo
     * @return
     */
    public String queryPolicyList(String grpNo){
        Map<String, Object> resultMap = new HashMap<>();

        FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(grpNo);
        if(fcGrpInfo == null){
            Log.info("查不到您的所属企业，请尝试重新登陆");
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查不到您的所属企业，请尝试重新登陆！");
            return JSON.toJSONString(resultMap);
        }
        RemoteDelegate rd = RemoteDelegate.getInstance();
        Map<String, String> myPrpsMap = myProps.getServiceInfo();
        String reqXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                "<RequestInfo>\n" +
                "\t<HEAD>\n" +
                /* 交易流水号 */
                "\t\t<TransRefGUID>"+ CommonUtil.getUUID()+"</TransRefGUID>\n" +
                /* 接口交易类型 */
                "\t\t<TransType>CLM</TransType>\n" +
                /* 交易日期 */
                "\t\t<TransExeDate>"+DateTimeUtil.getCurrentDate()+"</TransExeDate>\n" +
                /* 交易时间 */
                "\t\t<TransExeTime>"+DateTimeUtil.getCurrentTime()+"</TransExeTime>\n" +
                "\t</HEAD>\n" +
                "\t<BODY>\n" +
                "\t\t<GrpContQuery>\n" +
                "\t\t\t<grpIdType>"+fcGrpInfo.getGrpIdType()+"</grpIdType>\n" +
                "\t\t\t<GrpIdTypeName>"+"统一社会信用代码证"+"</GrpIdTypeName>\n" +
                "\t\t\t<grpIdNo>"+fcGrpInfo.getGrpIdNo()+"</grpIdNo>\t\n" +
                "\t\t</GrpContQuery>\n" +
                "\t</BODY>\t\n" +
                "</RequestInfo>";
        Log.info("调用核心投保接口请求报文：" + reqXml);
        long startStamp = System.currentTimeMillis();
        boolean success = rd.submitData(myPrpsMap.get("serviceUrl"), myPrpsMap.get("operation"), "", reqXml); //BenefitService
        long endStamp = System.currentTimeMillis();
        Log.info("调用核心接口用时：" + (endStamp - startStamp));
        if(success){
            Map<String, Object> responseXml = rd.getResult();
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "查询保单成功");
            resultMap.put("data",responseXml);
            return JSON.toJSONString(resultMap);
        }else{
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询保单失败，请联系管理员");
            return JSON.toJSONString(resultMap);
        }
    }

}
