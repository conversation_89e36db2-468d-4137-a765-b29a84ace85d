package com.sinosoft.eflex.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.SMSTemplateNoEnum;
import com.sinosoft.eflex.enums.StateEnum;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.configmanage.AuditUserInfo;
import com.sinosoft.eflex.model.configmanage.SelectAuditUserIsExistReq;
import com.sinosoft.eflex.model.configmanage.SelectAuditUserPhoneIsExistReq;
import com.sinosoft.eflex.model.configmanage.SelectAuditUserReq;
import com.sinosoft.eflex.model.sendmessage.SendSMSReq;
import com.sinosoft.eflex.service.sms.SendMessageService;
import com.sinosoft.eflex.util.CheckUtils;
import com.sinosoft.eflex.util.CommonUtil;
import com.sinosoft.eflex.util.rest.exception.SystemException;
import com.sinosoft.eflex.util.rest.util.ResponseResultUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021/7/20
 * @desc 用户配置管理相关处理类
 */
@Service
public class UserConfigManageService {

    @Autowired
    private FdComMapper fdComMapper;
    @Autowired
    private FdUserMapper fdUserMapper;
    @Autowired
    private FDPwdHistMapper fdPwdHistMapper;
    @Autowired
    private FDUserRoleMapper fdUserRoleMapper;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private FDusertomenugrpMapper fDusertomenugrpMapper;
    @Autowired
    private FDCodeMapper fdCodeMapper;
    @Autowired
    private FCUserLoginMapper fcUserLoginMapper;
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private SendMessageService sendMessageService;

    /**
     * 查询审核用户列表
     *
     * @param token
     * @param selectAuditUserReq
     * @return
     */
    public String selectAuditUser(String token, SelectAuditUserReq selectAuditUserReq) {
        if (StringUtils.isEmpty(selectAuditUserReq.getPageNum())
                || StringUtils.isEmpty(selectAuditUserReq.getPageSize())) {
            throw new SystemException("页码、条数相关信息不能为空！");
        }
        PageHelper.startPage(selectAuditUserReq.getPageNum(), selectAuditUserReq.getPageSize());
        List<AuditUserInfo> auditUserInfoList = fdUserMapper.selectAuditUserList(selectAuditUserReq);
        PageResp.restPage(auditUserInfoList);
        return JSONObject.toJSONString(ResponseResultUtil.success(PageResp.restPage(auditUserInfoList)));
    }

    /**
     * 查询单个审核岗用户
     *
     * @param token
     * @param auditUserInfo
     * @return
     */
    public String selectSingleAuditUser(String token, AuditUserInfo auditUserInfo) {
        String userNo = auditUserInfo.getUserNo();
        if (StringUtils.isEmpty(userNo)) {
            throw new SystemException("用户编号不能为空！");
        }
        auditUserInfo = fdUserMapper.selectSingleAuditUserInfo(userNo);

        return JSONObject.toJSONString(ResponseResultUtil.success(auditUserInfo));
    }

    /**
     * 新增用户
     *
     * @param token
     * @param auditUserInfo
     * @return
     */
    @Transactional
    public void dealAuditUser(String token, String operation, AuditUserInfo auditUserInfo) {
        //校验数据
        checkauditUserInfo(operation, auditUserInfo);
        // 处理数据
        dealAuditUserInfo(operation, auditUserInfo);

    }

    /**
     * 校验审核用户数据
     *
     * @param auditUserInfo
     */
    public void checkauditUserInfo(String operation, AuditUserInfo auditUserInfo) {
        // 校验操作符
        if (StringUtils.isEmpty(operation) || !operation.matches("add|update")) {
            throw new SystemException("您的操作有误，请重新操作！");
        }
        if ("update".equals(operation)) {
            if (StringUtils.isEmpty(auditUserInfo.getUserNo())) {
                throw new SystemException("员工编号不能为空！");
            }
        }
        // 校验用户管理机构
        if (StringUtils.isEmpty(auditUserInfo.getManageCom())) {
            throw new SystemException("用户所属机构编码不能为空！");
        } else {
            FdCom fdCom = fdComMapper.selectByPrimaryKey(auditUserInfo.getManageCom());
            if (ObjectUtils.isEmpty(fdCom)) {
                throw new SystemException("机构编码有误！");
            }
        }
        // 校验用户姓名
        if (StringUtils.isEmpty(auditUserInfo.getUserName())) {
            throw new SystemException("用户姓名不能为空！");
        } else if (auditUserInfo.getUserName().matches("^[\\u4e00-\\u9fa5]+$")) {
            String checkChineseNameResult = CheckUtils.checkChineseName(auditUserInfo.getUserName());
            if (!StringUtils.isEmpty(checkChineseNameResult)) {
                throw new SystemException(checkChineseNameResult);
            }
        } else {
            String checkEnglishNameResult = CheckUtils.checkEnglishName(auditUserInfo.getUserName());
            if (!StringUtils.isEmpty(checkEnglishNameResult)) {
                throw new SystemException(checkEnglishNameResult);
            }
        }
        // 校验用户账号
        if (StringUtils.isEmpty(auditUserInfo.getLoginName())) {
            throw new SystemException("用户账号不能为空！");
        } else {
            SelectAuditUserIsExistReq selectAuditUserIsExistReq = new SelectAuditUserIsExistReq();
            selectAuditUserIsExistReq.setLoginName(auditUserInfo.getLoginName());
            if ("update".equals(operation)) {
                selectAuditUserIsExistReq.setNotEqualUserNo(auditUserInfo.getUserNo());
            }
            int count = fdUserMapper.selectAuditUserIsExist(selectAuditUserIsExistReq);
            if (count > 0) {
                throw new SystemException("用户已存在！");
            }
        }
        // 校验手机号
        if (StringUtils.isEmpty(auditUserInfo.getPhone())) {
            throw new SystemException("手机号不能为空！");
        } else if (!auditUserInfo.getPhone().matches("[1]{1}[0-9]{10}")) {
            throw new SystemException("手机号填写有误！");
        } else {
            // 校验手机号的唯一性
            SelectAuditUserPhoneIsExistReq selectAuditUserPhoneIsExistReq = new SelectAuditUserPhoneIsExistReq();
            selectAuditUserPhoneIsExistReq.setPhone(auditUserInfo.getPhone());
            if ("update".equals(operation)) {
                selectAuditUserPhoneIsExistReq.setNotEqualUserNo(auditUserInfo.getUserNo());
            }
            int count = fdUserMapper.selectAuditUserPhoneIsExist(selectAuditUserPhoneIsExistReq);
            if (count > 0) {
                throw new SystemException("用户手机号已注册！");
            }
        }
        // 校验用户状态
        if (StringUtils.isEmpty(auditUserInfo.getUserState())) {
            throw new SystemException("用户状态不能为空！");
        } else if (!auditUserInfo.getUserState().matches("^(0|1)")) {
            throw new SystemException("用户状态不存在！");
        }
        // 校验用户角色
        if (StringUtils.isEmpty(auditUserInfo.getUserRole())) {
            throw new SystemException("用户角色不能为空！");
        }
    }

    // 存储用户信息
    public void dealAuditUserInfo(String operation, AuditUserInfo auditUserInfo) {
        /**
         * 准备数据
         */
        String userNo = auditUserInfo.getUserNo();

        // 是否在启用状态下修改了用户类型
        String isChangeCustomType = "0";
        // 是否第一次由禁用变为了启用
        String isFirstChangeState = "0";

        String password = "";
        if ("add".equals(operation)) {
            userNo = maxNoService.createMaxNo("UserNo", "", 20);
            // 默认密码为111111 say by xiexiren
            password = "7B492A8416C3289BD5CD180615AE61F4";
        } else {
            FdUser fdUser = fdUserMapper.selectByPrimaryKey(userNo);
            if (!auditUserInfo.getUserRole().equals(fdUser.getCustomType())
                    && auditUserInfo.getUserState().equals("1")) {
                isChangeCustomType = "1";
            }
            int loginTimes = fcUserLoginMapper.selectUserLoginTimesByUserNo(userNo);
            if (fdUser.getUserState().equals("0") && auditUserInfo.getUserState().equals("1") && loginTimes == 0) {
                isFirstChangeState = "1";
            }
        }

        // 用户表
        FdUser fdUser = new FdUser();
        fdUser.setUserNo(userNo);
        fdUser.setUserName(auditUserInfo.getLoginName());
        fdUser.setNickName(auditUserInfo.getUserName());
        fdUser.setPhone(auditUserInfo.getPhone());
        fdUser.setManageCom(auditUserInfo.getManageCom());
        fdUser.setPassWord(password);
        fdUser.setCustomType(auditUserInfo.getUserRole());
        fdUser.setIsLock(StateEnum.INVALID.getCode());
        fdUser.setIsVIP("N");
        fdUser.setLoginFailTimes(0);
        fdUser.setUserState(auditUserInfo.getUserState());
        fdUser.setPhone(auditUserInfo.getPhone());
        fdUser.setPWDState(StateEnum.INVALID.getCode());
        fdUser.setOperator("admin002");
        fdUser = CommonUtil.initObject(fdUser, operation);
        if ("add".equals(operation)) {
            fdUserMapper.insertSelective(fdUser);
        } else {
            fdUserMapper.updateByPrimaryKeySelective(fdUser);
        }

        // 密码历史表
        if ("add".equals(operation)) {
            FDPwdHist fdPwdHist = new FDPwdHist();
            String passWordSN = maxNoService.createMaxNo("PwdSN", "PWDSN", 15);
            fdPwdHist.setPassWordSN(passWordSN);
            fdPwdHist.setUserNo(userNo);
            fdPwdHist.setPassWord(password);
            fdPwdHist.setOperator("admin002");
            fdPwdHist = CommonUtil.initObject(fdPwdHist, "INSERT");
            fdPwdHistMapper.insertSelective(fdPwdHist);
        }

        fDusertomenugrpMapper.deleteByUserNo(userNo);
        fdUserRoleMapper.deleteByUserNo(userNo);
        List<String> userRoleList = Arrays.asList(auditUserInfo.getUserRole().split(","));
        // 角色名称
        String roleName = "";
        for (String userRole : userRoleList) {
            String roleTypeName = fdCodeMapper.selectNameByCode("RoleType", userRole);
            if (!StringUtils.isEmpty(roleName)) {
                roleName += "，";
            }
            roleName += roleTypeName;
            // 用户角色表，一对多
            FDUserRole fdUserRole = new FDUserRole();
            String userRoleSN = maxNoService.createMaxNo("UserRole", "SN", 18);
            fdUserRole.setUserRoleSN(userRoleSN);
            fdUserRole.setUserNo(userNo);
            fdUserRole.setRoleType(userRole);
            fdUserRole.setOperator("admin002");
            fdUserRole = CommonUtil.initObject(fdUserRole, "INSERT");
            fdUserRoleMapper.insertSelective(fdUserRole);
            // 用户菜单组，一对多
            FDusertomenugrpKey fDusertomenugrpKey = new FDusertomenugrpKey();
            fDusertomenugrpKey.setUserNo(userNo);
            fDusertomenugrpKey.setMenuGrpCode(userRole);
            fDusertomenugrpMapper.insertSelective(fDusertomenugrpKey);
        }

        // 短信
        SendSMSReq sendSMSReq = new SendSMSReq();
        sendSMSReq.setPhones(auditUserInfo.getPhone());
        Map<String, Object> map = null;
        if (("add".equals(operation) && auditUserInfo.getUserState().equals("1"))
                || ("update".equals(operation) && isFirstChangeState.equals("1"))) {
            sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_013.getCode());
            map = new HashMap<>();
            map.put("roleName", roleName);
            map.put("userName", auditUserInfo.getLoginName());
            map.put("userInitPassword", "111111");
        } else if ("update".equals(operation) && isChangeCustomType.equals("1")) {
            sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_014.getCode());
            map = new HashMap<>();
            map.put("roleName", roleName);
        }
        if (!ObjectUtils.isEmpty(map)) {
            sendSMSReq.setParam(map);
            sendMessageService.sendSMS(sendSMSReq);
        }

    }

    /**
     * 删除单个审核用户
     *
     * @param token
     * @param auditUserInfo
     * @return
     */
    @Transactional
    public void deleteSingleAuditUser(String token, AuditUserInfo auditUserInfo) {
        String userNo = auditUserInfo.getUserNo();
        if (StringUtils.isEmpty(userNo)) {
            throw new SystemException("用户编号不能为空！");
        }

        int count = fcEnsureMapper.selectByOperator(userNo);
        if (count > 0) {
            throw new SystemException("审核用户不能被删除！");
        }

        fdUserMapper.deleteByPrimaryKey(userNo);
        fdUserRoleMapper.deleteByUserNo(userNo);
        fDusertomenugrpMapper.deleteByUserNo(userNo);
    }

}
