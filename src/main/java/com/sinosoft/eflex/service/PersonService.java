package com.sinosoft.eflex.service;


import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.config.SysConst;
import com.sinosoft.eflex.dao.PersonMapper;
import com.sinosoft.eflex.model.Person;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.PageHelperUtil;
import com.sinosoft.eflex.util.RemoteDelegate;
import com.sinosoft.eflex.util.SFtpClientUtil;
import com.zhongan.gw.bean.RequestBase;
import com.zhongan.gw.bean.ResponseBase;
import com.zhongan.gw.exception.GatewayException;
import com.zhongan.gw.util.HttpClientUtil;
import com.zhongan.gw.util.SecurityHelper;

import ch.qos.logback.classic.Logger;

/**
 * person类的crud,这是一个示例
 * <AUTHOR>
 *
 */
@Service("PersonService")
@SuppressWarnings("unused")
public class PersonService {
	
	//日志
	private static Logger Log = (Logger) LoggerFactory.getLogger(PersonService.class);
	//注入Dao层
	@Autowired
	private PersonMapper personMapper;
	
	
	/**
	  * 根据id删除人类
	  * @param pId
	  * @return
	  */
	/*public String deletePersonById(Integer pId){
		Map<String, Object> resultMap = new HashMap<>();
		try {
			Person person = personMapper.selectByPrimaryKey(pId);
			if(person != null){
				personMapper.deleteByPrimaryKey(pId);
				resultMap.put("success", true);
				resultMap.put("code", "200");
				resultMap.put("message", "删除人员成功！");
			}else{
				resultMap.put("success", false);
				resultMap.put("code", "500");
				resultMap.put("message", "人员不存在!");
			}
		} catch (Exception e) {
			Log.info("删除人员失败", e);
			resultMap.put("success", false);
			resultMap.put("code", "500");
			resultMap.put("message", "删除人员失败");
		}
		return JSON.toJSONString(resultMap);
	}
	
	*//**
	  * 新增人类
	  * @param person
	  * @return
	  *//*
	public String addPersonInfo(Person person){
		Map<String, Object> resultMap = new HashMap<>();
		
		try {
			person.setMakedate(DateTimeUtil.getCurrentDate());
			personMapper.insert(person);
			resultMap.put("success", true);
			resultMap.put("code", "200");
			resultMap.put("message", "新增人员成功");
		} catch (Exception e) {
			Log.info("新增人员失败", e);
			resultMap.put("success", false);
			resultMap.put("code", "500");
			resultMap.put("message", "新增人员失败");
		}
		return JSON.toJSONString(resultMap);
	}
	
	*//**
	  * 根据id修改人类
	  * @param person
	  * @return
	  *//*
	public String updatePersonById(Person person) {
		Map<String, Object> resultMap = new HashMap<>();

		try {
			personMapper.updateByPrimaryKey(person);
			resultMap.put("success", true);
			resultMap.put("code", "200");
			resultMap.put("message", "更新人员成功");
		} catch (Exception e) {
			Log.info("更新人员失败", e);
			resultMap.put("success", false);
			resultMap.put("code", "500");
			resultMap.put("message", "更新人员失败");
		}
		return JSON.toJSONString(resultMap);
	}
	
	*//**
	  * 根据id查询人类
	  * @param pId
	  * @return
	  *//*
	public String selectPersonById(Integer pId){
		Map<String, Object> resultMap = new HashMap<>();
		try {
			Person person = personMapper.selectByPrimaryKey(pId);
			resultMap.put("success", true);
			resultMap.put("code", "200");
			resultMap.put("message", "查询人员成功");
			resultMap.put("data", person);
		} catch (Exception e) {
			Log.info("查询人员失败：", e);
			resultMap.put("success", false);
			resultMap.put("code", "500");
			resultMap.put("message", "查询人员失败");
		}
		return JSON.toJSONString(resultMap);
	}
	
	public List<Person> findAll(){
		return personMapper.findAll();
	}
	
	public String findByPage(int pageNo, int pageSize){
		Map<String, Object> resultMap = new HashMap<>();
		try {			
			PageHelper.startPage(pageNo, pageSize);
			List<HashMap<String, Object>> persons = personMapper.findByPage();
			PageHelperUtil<HashMap<String, Object>> teamPageInfo = new PageHelperUtil<>(persons);
			Map<String, Object> dataMap = new HashMap<String, Object>();
			dataMap.put("recordsTotal", teamPageInfo.getTotal());
			dataMap.put("list", teamPageInfo.getList());
			resultMap.put("data", dataMap);
			resultMap.put("success", true);
			resultMap.put("code", "200");
			resultMap.put("message", "查询人员成功");
		} catch (Exception e) {
			Log.info("查询人员失败：", e);
			resultMap.put("success", false);
			resultMap.put("code", "500");
			resultMap.put("message", "查询人员失败");
		}		
		
		return JSON.toJSONString(resultMap);
	}
	
	@Autowired
	private RestTemplate restTemplate;
	
	public String restTest(String spanToken) {
		
		Map<String, Object> resultMap = new HashMap<>();
		
		*//*HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(type);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        
        JSONObject jsonObj = new JSONObject();
        jsonObj.put("token", spanToken);
        jsonObj.put("systemCode", "fulipingtai");
        HttpEntity<String> formEntity = new HttpEntity<String>(jsonObj.toString(), headers);
        
        String url = "http://webservice-test.e-hqins.com/gw/getCustomerInfo-WelfarePlatform";
        JSONObject result = restTemplate.postForObject(url, formEntity, JSONObject.class);
		Log.info(">>>>>网销五要素接口返回结果："+result);*//*
		
		// 调用网销接口，获取用户五要素
        String url = myProps.getDSserviceInfo().get("url");
        String serviceName = "getCustomerInfo-WelfarePlatform";
        String version = myProps.getDSserviceInfo().get("version");
        String appkey = myProps.getDSserviceInfo().get("appkey");
        //不加密 a:rsa加密签名  b:3des加密，c
        String securityType = myProps.getDSserviceInfo().get("securityType");
        String publicKey = myProps.getDSserviceInfo().get("publicKey");
        //业务参数
        Map<String, Object> bizContent = new HashMap<String, Object>();
        bizContent.put("token", spanToken);
		bizContent.put("systemCode", "fulipingtai");
        RequestBase requestBase = new RequestBase();
        requestBase.setServiceName(serviceName);
        requestBase.setVersion(version);
        requestBase.setAppKey(appkey);

        Long timestamp = System.currentTimeMillis();
        requestBase.setTimestamp(timestamp.toString());

        requestBase.setBizContent(bizContent);
		String result = "";
		try {
			//加密加签
			SecurityHelper.encryptAndSign(requestBase, publicKey, securityType, "");
			result = HttpClientUtil.doPostJson(url, JSON.toJSONString(requestBase), 10000);
			Log.info(">>>>>调用电商五要素接口返回结果："+result);		
			ResponseBase responseBase = JSON.parseObject(result, ResponseBase.class);
			if(!"200".equals(responseBase.getCode())) {
				resultMap.put("code", "1");
				resultMap.put("msg", "调用电商接口返回错误信息："+responseBase.getMessage());
				return JSON.toJSONString(resultMap);
			}
			//解密返回报文
			result = SecurityHelper.checkSignAndDecrypt(responseBase, publicKey, securityType, "");
			Log.info(">>>>>调用电商五要素接口返回报文解密结果" + result.toString());
		}catch (Exception e) {
			Log.info("调用电商五要素接口异常：",e);
			resultMap.put("code", "1");
			resultMap.put("msg", "获取电商用户五要素失败！");
			return JSON.toJSONString(resultMap);
		}
		
		JSONObject resultObj = JSON.parseObject(result);
		 Map<String, Object> userMap = resultObj.getJSONObject("data");
		Log.info("获取的用户五要素",userMap);
        if(!resultObj.getBoolean("success") || resultObj.getJSONObject("data")==null) {
        	resultMap.put("code", "1");
			resultMap.put("msg", "调用电商接口返回错误信息："+resultObj.getString("message"));
			return JSON.toJSONString(resultMap);
        }
		
		return JSON.toJSONString(resultObj);
	}
	
	@Autowired
	private MyProps myProps;

	public String webserviceTest() {
		String resultInfo = "testtest";
		RemoteDelegate remoteDelegate = RemoteDelegate.getInstance();
		if (!remoteDelegate.submitData(myProps.getServiceInfo().get("serviceUrl"),
				myProps.getServiceInfo().get("operation"), "test1", "test2")) {

			resultInfo = remoteDelegate.getErrorMsg();
			Log.info("调用失败：" + resultInfo);
		} else {
			//resultInfo = remoteDelegate.getResult();
			//Log.info("调用成功：" + remoteDelegate.getResult());
		}
		return resultInfo;
	}
	
	public String sftpTest(String serverIP, int port, String userName,
			String userPassword) {
		SFtpClientUtil test = new SFtpClientUtil(serverIP, port, userName, userPassword);
		String localFilePathAndName = "/home/<USER>/test.xml";
		String ftpFilePathAndName = "/2018/9/13/0105/test.xml";
		try {
			test.uploadFile(ftpFilePathAndName, localFilePathAndName);
		} catch (Exception e) {
			Log.info("",e);
		}
		return "";
	}

    public String batchImport(String fileName, MultipartFile file) throws Exception {
    	 
        
        List<Person> personList = new ArrayList<Person>();
        if (!fileName.matches("^.+\\.(?i)(xls)$") && !fileName.matches("^.+\\.(?i)(xlsx)$")) {
        	return "上传文件格式不正确";
        }
        boolean isExcel2003 = true;
        if (fileName.matches("^.+\\.(?i)(xlsx)$")) {
            isExcel2003 = false;
        }
        InputStream is = file.getInputStream();
        Workbook wb = null;
        if (isExcel2003) {
            wb = new HSSFWorkbook(is);
        } else {
            wb = new XSSFWorkbook(is);
        }
        Sheet sheet = wb.getSheetAt(0);
        
        Person person;
        String msg = "解析完成 ";
        for (int r = 0; r <= sheet.getLastRowNum(); r++) {
            Row row = sheet.getRow(r);
            if (row == null){
                continue;
            }            
 
            person = new Person();
            
            row.getCell(0).setCellType(Cell.CELL_TYPE_STRING);
            String pid = row.getCell(0).getStringCellValue(); 
            if(pid == null || pid.isEmpty()){
            	msg += "导入失败(第"+(r+1)+"行,编号未填写)";
            }
 
            String name = row.getCell(1).getStringCellValue();
            if(name==null || name.isEmpty()){
            	msg += "导入失败(第"+(r+1)+"行,姓名未填写)";
            }
            
            row.getCell(2).setCellType(Cell.CELL_TYPE_STRING);
            String age = row.getCell(2).getStringCellValue();
            if(age==null){
            	msg += "导入失败(第"+(r+1)+"行,年龄未填写)";
            }
 
            String sex = row.getCell(3).getStringCellValue();
            if(sex==null){
            	msg += "导入失败(第"+(r+1)+"行,性别未填写)";
            }
 
            person.setPid(Integer.parseInt(pid));
            person.setName(name);
            person.setAge(age);
            person.setSex(sex);
 
            personList.add(person);
        }
        for (Person personResord : personList) {
        	
        	personMapper.insert(personResord);
            
            System.out.println(" 插入 "+personResord);
            
        }
        return msg;
    }

	public static void main(String[] args) {
	}
*/
}
