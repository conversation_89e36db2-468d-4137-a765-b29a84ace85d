package com.sinosoft.eflex.service;

import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.edor.EdorAddInsuredConfirmReq;
import com.sinosoft.eflex.service.edor.EdorConfirmNI;
import com.sinosoft.eflex.service.edor.EdorNIBL;
import com.sinosoft.eflex.service.edor.EdorNIService;
import com.sinosoft.eflex.service.edor.EdorService;
import com.sinosoft.eflex.service.sms.SendMessageService;
import com.sinosoft.eflex.util.CommonUtil;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.SFtpClientUtil;
import com.sinosoft.eflex.enums.AsyncBusinessTypeEnum;
import com.sinosoft.eflex.enums.EdorStateEnum;
import com.sinosoft.eflex.enums.StateEnum;
import com.sinosoft.eflex.enums.TrialStateEnum;
import com.sinosoft.eflex.util.encrypt.LisIDEA;
import com.sinosoft.eflex.util.rest.exception.SystemException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @create 2021/9/7
 * @desc 异步处理类交由Spring容器管理
 */
@Component
public class AsyncService {

    // 日志打印
    private static final Logger log = LoggerFactory.getLogger(EdorService.class);

    @Autowired
    private FcAsyncInfoMapper fcAsyncInfoMapper;
    @Autowired
    private FcEdorItemMapper fcEdorItemMapper;
    @Autowired
    private FCEdorAddInsuredMapper fcEdorAddInsuredMapper;
    @Autowired
    private EdorNIBL edorNIBL;
    @Autowired
    private EdorNIService edorNIService;
    @Autowired
    private FCEdoruploadfileMapper fcEdoruploadfileMapper;
    @Autowired
    private FDFTPInfoMapper fdftpInfoMapper;
    @Autowired
    private EdorConfirmNI edorConfirmNI;
    @Autowired
    private EdorService edorService;
    @Autowired
    private UserService userService;
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private FCEnsurePlanMapper fcEnsurePlanMapper;
    @Autowired
    private FCPerInfoMapper fcPerInfoMapper;
    @Autowired
    private FCPerInfoTempMapper fcPerInfoTempMapper;
    @Autowired
    private FCPersonMapper fcPersonMapper;
    @Autowired
    private FCGrpInfoMapper fcGrpInfoMapper;
    @Autowired
    private FCStaffFamilyRelaMapper fcStaffFamilyRelaMapper;
    @Autowired
    private FCPerinfoFamilyTempMapper fcPerinfoFamilyTempMapper;
    @Autowired
    private FdUserMapper fdUserMapper;
    @Autowired
    private EnsureMakeService ensureMakeService;


    /**
     * 复合完成后添加人员
     *
     * @param
     * @return
     */
    @Async
    public void insertNewUser(GlobalInput globalInput, Map<String, String> params) {
        log.info("复合完成后添加人员::::::{}", JSON.toJSONString(params));
        // 获取当前企业信息
        int peoples = 0;
        FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(params.get("grpNo"));
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(params.get("ensureCode"));
        if ("0".equals(fcEnsure.getEnsureType()) || "2".equals(fcEnsure.getEnsureType())) {
            List<HashMap<String, String>> listPerIfo = new ArrayList<>();
            Map<String, String> map = new HashMap<>();
            // 获取需要同步的人数
            List<FCPerInfoTemp> needSyncNum = ensureMakeService.getNeedSyncNum(params.get("ensureCode"));
            peoples = peoples + needSyncNum.size();
            if (needSyncNum.size() > 0) {
                LisIDEA encryPassword = new LisIDEA();
                for (int i = 0; i < needSyncNum.size(); i++) {
                    Calendar cal = Calendar.getInstance();
                    int year = cal.get(Calendar.YEAR);
                    HashMap<String, String> hashMap = new HashMap<>();
                    hashMap.put("currentDate", DateTimeUtil.getCurrentDate());
                    hashMap.put("currentTime", DateTimeUtil.getCurrentTime());
                    hashMap.put("Operator", globalInput.getUserNo());
                    hashMap.put("year", year + "");
                    FdUser fdUser = fdUserMapper.selectByPwd(needSyncNum.get(i).getIDNo());
                    if (fdUser == null) {
                        hashMap.put("PassWord", encryPassword.encryptString(needSyncNum.get(i).getIDNo().substring(needSyncNum.get(i).getIDNo().length() - 6)));
                    } else {
                        hashMap.put("PassWord", fdUser.getPassWord());
                    }
                    hashMap.put("ensureCode", params.get("ensureCode"));
                    hashMap.put("idNo", needSyncNum.get(i).getIDNo());
                    hashMap.put("nativeplace", needSyncNum.get(i).getNativeplace());
                    hashMap.put("IDType", needSyncNum.get(i).getIDType());
                    hashMap.put("levelCode", needSyncNum.get(i).getLevelCode());
                    hashMap.put("UserNo", maxNoService.createMaxNo("UserNo", "", 20));
                    hashMap.put("UserRole", maxNoService.createMaxNo("UserRole", "SN", 18));
                    hashMap.put("PwdSN", maxNoService.createMaxNo("PwdSN", "PWDSN", 15));
                    Map<String, Object> perInfoMap = new HashMap<>();
                    perInfoMap.put("IDNo", needSyncNum.get(i).getIDNo());
                    perInfoMap.put("grpNo", fcGrpInfo.getGrpNo());
                    List<FCPerInfo> fcPerInfo = fcPerInfoMapper.selectByExample(perInfoMap);
                    if (fcPerInfo.size() < 1) {
                        hashMap.put("PerNo", maxNoService.createMaxNo("PerNo", "", 20));
                    } else {
                        hashMap.put("PerNo", fcPerInfo.get(0).getPerNo());
                    }
                    Map<String, String> infoMap = new HashMap<>();
                    infoMap.put("grpNo", fcGrpInfo.getGrpNo());
                    infoMap.put("idNo", needSyncNum.get(i).getIDNo());
                    FCPerson fcPerson = fcPersonMapper.getPerPersonID(infoMap);
                    if (fcPerson != null) {
                        hashMap.put("PersonId", fcPerson.getPersonID());
                    } else {
                        hashMap.put("PersonId", maxNoService.createMaxNo("PersonId", "", 20));
                    }
                    hashMap.put("seriaNo", maxNoService.createMaxNo("DefaultPlan", "", 20));
                    hashMap.put("RegDay", maxNoService.createMaxNo("RegDay", "", 20));
                    //判断该福利是固定计划还是弹性,固定计划，defaultPlan不为空
                    if ("0".equals(fcEnsure.getPlanType())) {
                        hashMap.put("StaffGrpPrem", String.valueOf(fcEnsurePlanMapper.selectPlanPrem(params.get("ensureCode"), needSyncNum.get(i).getDefaultPlan())));
                    }
                    if ("1".equals(fcEnsure.getPlanType())) {
                        hashMap.put("StaffGrpPrem", "");
                    }
                    hashMap.put("grpNo", fcGrpInfo.getGrpNo());
                    listPerIfo.add(hashMap);
                }
                log.info("同步人员信息集合listPerIfo准备完毕，开始同步人员。。。");
                map.put("ensureCode", params.get("ensureCode"));
                map.put("planType", fcEnsure.getPlanType());
                map.put("year", Calendar.getInstance().get(Calendar.YEAR) + "");
                map.put("currentDate", DateTimeUtil.getCurrentDate());
                map.put("currentTime", DateTimeUtil.getCurrentTime());
                map.put("grpNo", fcGrpInfo.getGrpNo());
                // 同步客户表FcPerInfo（更新）
                //弹性计划 FcPerInfo 表中不存员工职级信息 update-20200417
                log.info("更新FcPerInfo表开始。。。");
                int i = fcPerInfoTempMapper.updateFcPerInfo(map);
                log.info("更新FcPerInfo表完毕。。。");

                // 同步客户表FcPerSon（更新）
                log.info("更新FcPerSon表开始。。。");
                int q = fcPerInfoTempMapper.updateFcPerSon(map);
                log.info("更新FcPerSon表完毕。。。");

                // 同步客户表FdUser（更新）
                log.info("更新FdUser开始。。。");
                int e = fcPerInfoTempMapper.updateFdUser(map);
                log.info("更新FdUser完毕。。。");

                log.info("更新FCPerRegistDay开始。。。");
                //弹性计划 FCPerRegistDay 表中存员工职级信息 update-20200417
                int l = fcPerInfoTempMapper.updateFCPerRegistDay(map);//应关闭
                log.info("更新FCPerRegistDay完毕。。。");
                log.info("更新人员相关表完成，开始插入人员相关表。。。");

                // 同步客户表FcPerSon（插入）
                int r = fcPerInfoTempMapper.insertFcPerSon(listPerIfo);
                // 同步家庭关系表FCStaffFamilyRela
                int t = fcPerInfoTempMapper.insertFCStaffFamilyRela(listPerIfo);
                // 同步员工默认计划表fcDefaultPlan
                if (!"1".equals(fcEnsure.getPlanType())) {
                    int y = fcPerInfoTempMapper.insertFCDefaultPlan(listPerIfo);
                }
                // 注册个人账号 custType : 1-个人账号
                int u = fcPerInfoTempMapper.insertFdUser(listPerIfo);
                int o = fcPerInfoTempMapper.insertFdUserRole(listPerIfo);
                int p = fcPerInfoTempMapper.insertFdPwdHist(listPerIfo);
                int f = fcPerInfoTempMapper.insertFDusertomenugrp(listPerIfo);
                // 同步员工注册期表 FCPerRegistDay
                //弹性计划 FCPerRegistDay 表中存员工职级信息 update-20200417
                int g = fcPerInfoTempMapper.insertFCPerRegistDay(listPerIfo);
                // 同步客户表FcPerInfo（插入）
                //弹性计划 FcPerInfo 表中不存员工职级信息 update-20200417
                int h = fcPerInfoTempMapper.insertFcPerInfo(listPerIfo);
                // 同步完成，更新临时表数据为已提交
                fcPerInfoTempMapper.updateFCPerinfoTemp(map);
                log.info("同步人员信息完成。。。");
            }
            //同步家属信息
            Map<String, Object> ensureInfoMap = new HashMap<>(params);
            ensureInfoMap.put("subStaus", "01");
            List<FCPerinfoFamilyTemp> fcPerinfoFamilyTempList = fcPerinfoFamilyTempMapper.getFamilyByEnsureCode(ensureInfoMap);
            if (fcPerinfoFamilyTempList.size() > 0) {
                map.put("ensureCode", params.get("ensureCode"));
                map.put("year", Calendar.getInstance().get(Calendar.YEAR) + "");
                map.put("currentDate", DateTimeUtil.getCurrentDate());
                map.put("currentTime", DateTimeUtil.getCurrentTime());
                map.put("grpNo", fcGrpInfo.getGrpNo());
                int j = fcPersonMapper.updateFcPerSonStudent(map);
                for (FCPerinfoFamilyTemp fcPerinfoFamilyTemp : fcPerinfoFamilyTempList) {
                    //获取员工家属关联表信息
                    FCStaffFamilyRela fcStaffFamilyRela = new FCStaffFamilyRela();
                    Map<String, String> familyInfo = new HashMap<>();
                    familyInfo.put("grpNo", fcGrpInfo.getGrpNo());
                    familyInfo.put("idNo", fcPerinfoFamilyTemp.getIDNo());
                    familyInfo.put("perIDNo", fcPerinfoFamilyTemp.getPerIDNo());
                    FCPerson fcPerson = fcPersonMapper.getFamPersonID(familyInfo);
                    if (fcPerson == null) {
                        HashMap<String, String> fcPersonMap = new HashMap<>();
                        fcPersonMap.put("familyTempNo", fcPerinfoFamilyTemp.getFamilyTempNo());
                        fcPersonMap.put("personID", maxNoService.createMaxNo("PersonID", null, 20));
                        fcPersonMap.put("ensureCode", fcPerinfoFamilyTemp.getEnsureCode());
                        fcPersonMap.put("IDType", fcPerinfoFamilyTemp.getIDType());
                        fcPersonMap.put("IDNo", fcPerinfoFamilyTemp.getIDNo());
                        fcPersonMap.put("operator", globalInput.getUserNo());
                        fcPersonMap.put("operatorCom", globalInput.getUserName());
                        fcPersonMap.put("makeDate", DateTimeUtil.getCurrentDate());
                        fcPersonMap.put("makeTime", DateTimeUtil.getCurrentTime());
                        fcPersonMap.put("modifyDate", DateTimeUtil.getCurrentDate());
                        fcPersonMap.put("modifyTime", DateTimeUtil.getCurrentTime());
                        int i = fcPersonMapper.insertMap(fcPersonMap);
                        fcStaffFamilyRela.setPersonID(fcPersonMap.get("personID"));
                    } else if (fcPerson != null) {
                        fcStaffFamilyRela.setPersonID(fcPerson.getPersonID());
                    }
                    fcStaffFamilyRela.setRelation(fcPerinfoFamilyTemp.getRelation());
                    fcStaffFamilyRela.setOperator(globalInput.getUserNo());
                    Map<String, Object> isEmptyMap = new HashMap<>();
                    isEmptyMap.put("IDNo", fcPerinfoFamilyTemp.getPerIDNo());
                    isEmptyMap.put("grpNo", fcGrpInfo.getGrpNo());
                    List<FCPerInfo> fcPerInfos = fcPerInfoMapper.isExitPerInfo(isEmptyMap); //调用之前已经写好的方法 返回List  一个企业下fcperinfo不循序重复，所以不会存在多个数据，既取第一条即可
                    if (fcPerInfos.size() > 0) {
                        fcStaffFamilyRela.setPerNo(fcPerInfos.get(0).getPerNo());
                    } else if (fcPerInfos.size() == 0) {
                        for (HashMap hashMap : listPerIfo) {
                            if (fcPerinfoFamilyTemp.getPerIDNo().equals(hashMap.get("idNo")) && fcPerinfoFamilyTemp.getPerIDType().equals(hashMap.get("IDType"))) {
                                fcStaffFamilyRela.setPerNo(String.valueOf(hashMap.get("PerNo")));
                            }
                        }
                    }
                    fcStaffFamilyRela = (FCStaffFamilyRela) CommonUtil.initObject(fcStaffFamilyRela, "INSERT");
                    //同步员工家属关联表
                    Map<String, String> StaffFamilyMap = new HashMap<>();
                    StaffFamilyMap.put("perNo", fcStaffFamilyRela.getPerNo());
                    StaffFamilyMap.put("personID", fcStaffFamilyRela.getPersonID());
                    FCStaffFamilyRela fcStaffFamilyRela1 = fcStaffFamilyRelaMapper.selectByPrimaryKey(StaffFamilyMap);
                    if (fcStaffFamilyRela1 == null) {
                        int o = fcStaffFamilyRelaMapper.insert(fcStaffFamilyRela);
                    }
                }
                fcPerinfoFamilyTempMapper.updateSubStaus(map);
            }
        }
        //同步企业人数
        peoples = peoples + fcGrpInfo.getPeoples();
        FCGrpInfo grpPeople = new FCGrpInfo();
        grpPeople.setGrpNo(params.get("grpNo"));
        grpPeople.setPeoples(peoples);
        fcGrpInfoMapper.updateByPrimaryKeySelective(grpPeople);

    }


    /**
     * 非场地险保全增人试算
     */
    @Async
    public void edorAddInsuredTrialIOAsync(GlobalInput globalInput, String addBatch, String grpContNo, List<FCEdorAddInsured> fcEdorAddInsuredList, FdAsyncThreshold fdAsyncThreshold) {
        log.info("异步处理进行中！");
        // 插入异步处理记录表
        FcAsyncInfo fcAsyncInfo = new FcAsyncInfo();
        fcAsyncInfo.setBusinessType(AsyncBusinessTypeEnum.NITRIALIO.getCode());
        fcAsyncInfo.setBusinessId(addBatch);
        fcAsyncInfo.setDealState(StateEnum.VALID.getCode());
        // 预估处理时长
        fcAsyncInfo.setEstimateDealTime(String.valueOf(fcEdorAddInsuredList.size() * fdAsyncThreshold.getSingleDealTime()));
        // 预估处理完成时间
        String estimateCompleteTime = DateTimeUtil.addTime(new Date(), fcEdorAddInsuredList.size() * fdAsyncThreshold.getSingleDealTime());
        fcAsyncInfo.setEstimateCompleteTime(estimateCompleteTime);
        fcAsyncInfo.setOperator(globalInput.getUserNo());
        fcAsyncInfo.setOperatorCom(globalInput.getGrpNo());
        fcAsyncInfo = CommonUtil.initObject(fcAsyncInfo, "add");
        fcAsyncInfoMapper.insertSelective(fcAsyncInfo);
        try {
            // 处理人员数据
            long startTime = System.currentTimeMillis();
            // 请求核心试算接口
            Map<String, Object> mapNI = edorNIBL.submitData(fcEdorAddInsuredList, addBatch, grpContNo);
            long endTime = System.currentTimeMillis();
            log.info("\n异步调用核心保全增人试算接口执行所用时间" + (endTime - startTime) + "毫秒。");
            // 处理返回结果
            boolean addFlag = (boolean) mapNI.get("resultFlag");
            if (addFlag) {
                log.info("增加被保险人试算接口，异步处理: success！");
                // 更新保全增人的信息，todo 其实这张表我更希望核心能够在保费试算的时候也能够错误的人信息返回过来，我们进行记录，返回前台 2021.9.8
                List<Insured> coreInsuredInfos = (List<Insured>) mapNI.get("NIInsuredList");
                coreInsuredInfos.forEach((Insured insured) -> {
                    FCEdorAddInsured fcEdorAddInsured = new FCEdorAddInsured();
                    fcEdorAddInsured.setGrpContNo(grpContNo);
                    fcEdorAddInsured.setBatch(addBatch);
                    fcEdorAddInsured.setIdType(insured.getIDType());
                    fcEdorAddInsured.setIdNo(insured.getIDNo());
                    fcEdorAddInsured.setTrialStatus(TrialStateEnum.TRIALDONE.getCode());
                    fcEdorAddInsured.setIsError(StateEnum.INVALID.getCode());
                    fcEdorAddInsured.setPrem(insured.getPrem());
                    fcEdorAddInsured = CommonUtil.initObject(fcEdorAddInsured, "update");
                    fcEdorAddInsuredMapper.updateEdorAddInsured(fcEdorAddInsured);
                });
                // 更新异步处理记录表
                fcAsyncInfo.setDealState(StateEnum.INVALID.getCode());
                fcAsyncInfo.setResultCode(StateEnum.INVALID.getCode());
                fcAsyncInfo.setResultMessage("成功");
                long useTime = (endTime - startTime);
                fcAsyncInfo.setDealTime(String.valueOf(useTime));
                fcAsyncInfoMapper.updateByPrimaryKeySelective(fcAsyncInfo);
                // 更新保全申请信息表的保单申请号
                FcEdorItem fcEdorItem = new FcEdorItem();
                fcEdorItem.setEdorBatch(addBatch);
                fcEdorItem.setEdorAppNo((String) mapNI.get("edorAppNo"));
                fcEdorItem = CommonUtil.initObject(fcEdorItem, "update");
                fcEdorItemMapper.updateByPrimaryKeySelective(fcEdorItem);
                /**
                 * 短信发送
                 */
                // SendSMSReq sendSMSReq = new SendSMSReq();
                // sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_005.getCode());
                // sendSMSReq.setPhones(Phone);
                // Map<String, Object> map = new HashMap<>();
                // map.put("edor_type", "「保全增人」");
                // sendSMSReq.setParam(map);
                // sendMessageService.sendSMS(sendSMSReq);

            } else {
                log.info("增加被保险人试算接口，异步处理: 试算失败！");
                // 更新异步处理记录表
                fcAsyncInfo.setDealState(StateEnum.INVALID.getCode());
                fcAsyncInfo.setResultCode(StateEnum.VALID.getCode());
                fcAsyncInfo.setResultMessage("增加被保险人试算失败，" + StringUtils.join(mapNI.get("errMsg"), ","));
                fcAsyncInfoMapper.updateByPrimaryKeySelective(fcAsyncInfo);
            }
        } catch (Exception e) {
            // 更新异步处理记录表
            fcAsyncInfo.setDealState(StateEnum.INVALID.getCode());
            fcAsyncInfo.setResultCode(StateEnum.VALID.getCode());
            fcAsyncInfo.setResultMessage(e.getMessage());
            fcAsyncInfoMapper.updateByPrimaryKeySelective(fcAsyncInfo);
        }
    }

    /**
     * 场地险保全增人试算
     *
     * @param globalInput
     * @param addBatch
     * @param grpContNo
     * @param fcEdorAddInsuredList
     */
    @Async
    public void edorAddInsuredTrialIOAsync1(GlobalInput globalInput, String addBatch, String grpContNo, List<FCEdorAddInsured> fcEdorAddInsuredList, FdAsyncThreshold fdAsyncThreshold) {
        log.info("保全增人试算异步处理进行中！");
        // 插入异步处理记录表
        FcAsyncInfo fcAsyncInfo = new FcAsyncInfo();
        fcAsyncInfo.setBusinessType(AsyncBusinessTypeEnum.NITRIALIO.getCode());
        fcAsyncInfo.setBusinessId(addBatch);
        fcAsyncInfo.setDealState(StateEnum.VALID.getCode());
        // 预估处理时长
        fcAsyncInfo.setEstimateDealTime(String.valueOf(fcEdorAddInsuredList.size() * fdAsyncThreshold.getSingleDealTime()));
        // 预估处理完成时间
        String estimateCompleteTime = DateTimeUtil.addTime(new Date(), fcEdorAddInsuredList.size() * fdAsyncThreshold.getSingleDealTime());
        fcAsyncInfo.setEstimateCompleteTime(estimateCompleteTime);
        fcAsyncInfo.setOperator(globalInput.getUserNo());
        fcAsyncInfo.setOperatorCom(globalInput.getGrpNo());
        fcAsyncInfo = CommonUtil.initObject(fcAsyncInfo, "add");
        fcAsyncInfoMapper.insertSelective(fcAsyncInfo);
        try {
            // 处理人员数据
            long startTime = System.currentTimeMillis();
            edorNIService.edorNITrial(grpContNo, addBatch);
            long endTime = System.currentTimeMillis();
            log.info("\n异步调用保全增人试算接口执行所用时间" + (endTime - startTime) + "毫秒。");
            // 更新异步处理记录表
            fcAsyncInfo.setDealState(StateEnum.INVALID.getCode());
            fcAsyncInfo.setResultCode(StateEnum.INVALID.getCode());
            fcAsyncInfo.setResultMessage("成功");
            long useTime = (endTime - startTime);
            fcAsyncInfo.setDealTime(String.valueOf(useTime));
            fcAsyncInfoMapper.updateByPrimaryKeySelective(fcAsyncInfo);
            // 更新保全申请信息表的保单申请号
            FcEdorItem fcEdorItem = new FcEdorItem();
            fcEdorItem.setEdorBatch(addBatch);
            String edorAppNo = maxNoService.createMaxNo("edorAppNo", "3" + grpContNo, 4);
            fcEdorItem.setEdorAppNo(edorAppNo);
            fcEdorItem = CommonUtil.initObject(fcEdorItem, "update");
            fcEdorItemMapper.updateByPrimaryKeySelective(fcEdorItem);
            /**
             * 短信发送
             */
            // SendSMSReq sendSMSReq = new SendSMSReq();
            // sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_005.getCode());
            // sendSMSReq.setPhones(Phone);
            // Map<String, Object> map = new HashMap<>();
            // map.put("edor_type", "「保全增人」");
            // sendSMSReq.setParam(map);
            // sendMessageService.sendSMS(sendSMSReq);

        } catch (Exception e) {
            // 更新异步处理记录表
            fcAsyncInfo.setDealState(StateEnum.INVALID.getCode());
            fcAsyncInfo.setResultCode(StateEnum.VALID.getCode());
            fcAsyncInfo.setResultMessage(e.getMessage());
            fcAsyncInfoMapper.updateByPrimaryKeySelective(fcAsyncInfo);
        }
    }


    /**
     * 保全申请确认接口
     */
    @Async
    public void edorAddInsuredConfirmIOAsync(String token, EdorAddInsuredConfirmReq edorAddInsuredConfirmReq,
                                             Map<String, Object> addInsuredMap, FcEdorItem fcEdorItem) {
        log.info("保全增人申请确认异步处理进行中！");
        GlobalInput globalInput = userService.getSession(token);
        List<FCEdorAddInsured> fcEdorAddInsuredList = fcEdorAddInsuredMapper.getAddInsuredInfo(addInsuredMap);
        if (fcEdorAddInsuredList.size() == 0) {

        }
        /**
         * 记录处理表
         */
        // 插入异步处理记录表
        FcAsyncInfo fcAsyncInfo = new FcAsyncInfo();
        fcAsyncInfo.setBusinessType(AsyncBusinessTypeEnum.NICONFIRMIO.getCode());
        fcAsyncInfo.setBusinessId(edorAddInsuredConfirmReq.getBatch());
        fcAsyncInfo.setDealState(StateEnum.VALID.getCode());
        // 预估处理时长
        fcAsyncInfo.setEstimateDealTime(String.valueOf(fcEdorAddInsuredList.size() * 30 * 1000));
        // 预估处理完成时间
        String estimateCompleteTime = DateTimeUtil.addTime(new Date(), fcEdorAddInsuredList.size() * 30 * 1000);
        fcAsyncInfo.setEstimateCompleteTime(estimateCompleteTime);
        fcAsyncInfo.setOperator(globalInput.getUserNo());
        fcAsyncInfo.setOperatorCom(globalInput.getGrpNo());
        fcAsyncInfo = CommonUtil.initObject(fcAsyncInfo, "add");
        fcAsyncInfoMapper.insertSelective(fcAsyncInfo);

        /**
         * 业务处理
         */
        // 将文件上传到FTP服务器，而后处理申请确认调用核心保全申请确认接口
        Map<String, Object> map = new HashMap<>();
        map.put("grpContNo", edorAddInsuredConfirmReq.getGrpContNo());
        map.put("batch", edorAddInsuredConfirmReq.getBatch());
        map.put("docType", "0304");
        // 查询出服务器上保全文件上传信息列表
        List<FCEdoruploadfile> uploadfilelist = fcEdoruploadfileMapper.selectUploadfileByGrpContNo(map);
        // 查询ftp信息上传到SFTP
        FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
        SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(), fdftpInfo.getUsername(),
                fdftpInfo.getPassword());
        for (FCEdoruploadfile fcEdoruploadfile : uploadfilelist) {
            // 封装上传实体类
            boolean flag = sFtp.uploadFile(fcEdoruploadfile.getFtpPath(), fcEdoruploadfile.getLocalPath());
            if (!flag) {
                log.info("被保人告知书上传FTP服务器失败！");
                throw new SystemException("被保人告知书上传失败!");
            }
        }
        long startTime = System.currentTimeMillis();
        Map<String, Object> mapNI = edorConfirmNI.addInsuredApply(edorAddInsuredConfirmReq.getGrpContNo(),
                addInsuredMap, fcEdorItem.getEdorAppNo(), uploadfilelist);
        long endTime = System.currentTimeMillis();
        log.info("\n异步调用核心保全增人申请确认接口执行所用时间" + (endTime - startTime) + "毫秒。");
        Boolean addFlag = (boolean) mapNI.get("resultFlag");
        if (addFlag) {
            log.info("增加被保险人申请接口: 异步调用成功！");
            // 更新当前保全申请状态
            edorService.updateEdorState(token, edorAddInsuredConfirmReq.getBatch(), EdorStateEnum.APPLYDONE.getCode());
            // 更新异步处理记录表
            fcAsyncInfo.setDealState(StateEnum.INVALID.getCode());
            fcAsyncInfo.setResultCode(StateEnum.INVALID.getCode());
            fcAsyncInfo.setResultMessage("成功");
            long useTime = (endTime - startTime);
            fcAsyncInfo.setDealTime(String.valueOf(useTime));
            fcAsyncInfoMapper.updateByPrimaryKeySelective(fcAsyncInfo);
            /**
             * 短信发送
             */
            // SendSMSReq sendSMSReq = new SendSMSReq();
            // sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_005.getCode());
            // sendSMSReq.setPhones(Phone);
            // Map<String, Object> map1 = new HashMap<>();
            // map.put("edor_type", "「保全增人」");
            // sendSMSReq.setParam(map);
            // sendMessageService.sendSMS(sendSMSReq);

        } else {
            log.info("增加被保险人申请接口: 异步调用失败！");
            // 更新异步处理记录表
            fcAsyncInfo.setDealState(StateEnum.INVALID.getCode());
            fcAsyncInfo.setResultCode(StateEnum.VALID.getCode());
            List<String> errMsgList = (List<String>) mapNI.get("errMsg");
            fcAsyncInfo.setResultMessage("保全增人申请确认失败，" + StringUtils.join(errMsgList.toArray(), ","));
            fcAsyncInfoMapper.updateByPrimaryKeySelective(fcAsyncInfo);
        }

    }

}
