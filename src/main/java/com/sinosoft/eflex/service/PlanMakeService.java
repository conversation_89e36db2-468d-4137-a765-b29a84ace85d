package com.sinosoft.eflex.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.constants.PatternConstants;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.OperatorEnum;
import com.sinosoft.eflex.enums.PlanStateEnum;
import com.sinosoft.eflex.framework.exception.EFlexServiceException;
import com.sinosoft.eflex.framework.exception.EFlexServiceExceptionEnum;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.util.CommonUtil;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR> wuShiHao
 * @date : 2021-03-11 09:26
 **/

@Service
@Slf4j
public class PlanMakeService {

    @Autowired
    private FCEnsurePlanMapper fcEnsurePlanMapper;
    @Autowired
    private FCPlanRiskMapper fcPlanRiskMapper;
    @Autowired
    private FCPlanRiskDutyMapper fcPlanRiskDutyMapper;
    @Autowired
    private FDRiskInfoMapper fdRiskInfoMapper;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private UserService userService;
    @Autowired
    EnsureMakeService ensureMakeService;
    @Autowired
    private FDCodeMapper fdCodeMapper;
    @Autowired
    private FDRiskDutyInfoMapper fdRiskDutyInfoMapper;

    boolean bool = true;
    // 计划保费
    private double planPrem = 0.0;
    // 计划导入提示信息
    private String planMessage = "";
    private List<FCEnsurePlan> fcEnsurePlanList = new ArrayList<FCEnsurePlan>();
    private List<FCPlanRisk> fcPlanRiskList = new ArrayList<FCPlanRisk>();
    private List<FCPlanRiskDuty> fcPlanRiskDutyList = new ArrayList<FCPlanRiskDuty>();
    // 固定计划下多责任险种必选责任集合
    List<String> requireddutyList = Arrays.asList("GD0029", "GD0032", "GD0050", "GD0051", "GD0052", "GD0053", "GD0054");
    // 固定计划下多责任险种可选责任集合
    List<String> optionaldutyList = Arrays.asList("GD0030", "GD0033", "GD0034", "GD0035", "GD0055", "GD0056", "GD0057", "GD0058", "GD0059");
    // 15070险种特殊责任集合
    List<String> specialDutyList15070 = Arrays.asList("GD0055", "GD0056", "GD0057", "GD0058", "GD0059");

    /**
     * 固定计划--删除福利下计划
     */
    public String deleteEnsurePlan(FCPlanRiskDuty fcPlanRiskDuty) {
        Map<String, Object> resultMap = new HashMap<>();
        String ensureCode = fcPlanRiskDuty.getEnsureCode();
        String planCode = fcPlanRiskDuty.getPlanCode();
        resultMap.put("success", false);
        resultMap.put("code", "500");
        try {
            if (StringUtils.isEmpty(ensureCode) || StringUtils.isEmpty(planCode)) {
                resultMap.put("message", "固定计划--删除福利下计划：请求参数有误！");
                return JSON.toJSONString(resultMap);
            }
            fcEnsurePlanMapper.deleteByEnsureCodeAndPlanCode(ensureCode, planCode);
            fcPlanRiskMapper.deleteByEnsureCodeAndPlanCode(ensureCode, planCode);
            fcPlanRiskDutyMapper.deleteByEnsureCodeAndPlanCode(ensureCode, planCode);
            resultMap.clear();
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "删除固定计划成功！");
        } catch (Exception e) {
            log.info("固定计划--删除福利下计划" + e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "删除固定计划失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 固定计划--删除福利计划下的险种
     */
    public String delPlanRiskInfo(String token, FCPlanRiskDuty fcPlanRiskDuty) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", false);
        resultMap.put("code", "500");
        try {
            String ensureCode = fcPlanRiskDuty.getEnsureCode();
            String planCode = fcPlanRiskDuty.getPlanCode();
            String riskCode = fcPlanRiskDuty.getRiskCode();
            if (StringUtils.isEmpty(ensureCode) || StringUtils.isEmpty(planCode) || StringUtils.isEmpty(riskCode)) {
                resultMap.put("message", "固定计划--删除福利计划下险种：请求参数有误！");
                return JSON.toJSONString(resultMap);
            }
            // 查询该福利计划下剩余险种数量
            List<String> list = fcPlanRiskMapper.selectRiskCodeList(ensureCode, planCode);
            // 设置一个计数器，计数当前计划下15070险种必选责任的录入个数
            int count = 0;
            // 若险种是15070的话需要单独判断
            if ("15070".equals(riskCode)) {
                List<FCPlanRiskDuty> fcPlanRiskDuties = fcPlanRiskDutyMapper.selectDutyInfo(ensureCode, planCode,
                        riskCode);
                for (FCPlanRiskDuty fcPlanRiskDuty1 : fcPlanRiskDuties) {
                    if (requireddutyList.contains(fcPlanRiskDuty1.getDutyCode())) {
                        count++;
                    }
                }
            }
            if (list.size() == 1 && !"15070".equals(riskCode)) {
                resultMap.clear();
                resultMap.put("success", false);
                resultMap.put("code", "303");
                resultMap.put("message", "当前险种删除后，此计划也要被删除，请确定！");
                return JSON.toJSONString(resultMap);
            } else if (list.size() == 1 && "15070".equals(riskCode) && count == 1) {
                resultMap.clear();
                resultMap.put("success", false);
                resultMap.put("code", "303");
                resultMap.put("message", "当前险种删除后，此计划也要被删除，请确定！");
                return JSON.toJSONString(resultMap);
            }
            // 删除责任层级
            fcPlanRiskDutyMapper.deleteRiskInfo(fcPlanRiskDuty);
            FCPlanRiskKey fcPlanRiskKey = new FCPlanRiskKey();
            fcPlanRiskKey.setEnsureCode(fcPlanRiskDuty.getEnsureCode());
            fcPlanRiskKey.setPlanCode(fcPlanRiskDuty.getPlanCode());
            fcPlanRiskKey.setRiskCode(fcPlanRiskDuty.getRiskCode());
            // 删除险种层级
            if ("15070".equals(riskCode)) {
                if (count == 1) {
                    fcPlanRiskMapper.deleteByPrimaryKey(fcPlanRiskKey);
                }
            } else {
                fcPlanRiskMapper.deleteByPrimaryKey(fcPlanRiskKey);
            }
            //重新试算计划保费
            FCEnsurePlan fcEnsurePlan = new FCEnsurePlan();
            fcEnsurePlan.setEnsureCode(ensureCode);
            fcEnsurePlan.setPlanCode(planCode);
            trialPrem(fcEnsurePlan, token);

            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "固定计划--删除福利计划下险种成功！");
        } catch (Exception e) {
            log.info("固定计划--删除福利计划下险种" + e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "固定计划--删除福利计划下险种失败！");
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * @param Authorization
     * @param fCPlanRiskDutyList 0 -- 新增、1 -- 修改
     * @return
     */
    public void insertRiskDuty(String Authorization, List<FCPlanRiskDuty> fCPlanRiskDutyList) {
        if (CollectionUtils.isEmpty(fCPlanRiskDutyList)) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.NOT_ONE_RISK_ERROR);
        }
        // 数据检测
        this.planCodeCheck(fCPlanRiskDutyList);
        // 数据处理
        String error = dealData(Authorization, fCPlanRiskDutyList);
        log.info("insertRiskDuty dealData response:{}", error);
        if (StringUtils.isNotEmpty(error)) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.NOT_ONE_RISK_ERROR.getCode(), error);
        }
    }

    /**
     * 计划编码校验*
     *
     * @param fcPlanRiskDutyList
     */
    private void planCodeCheck(List<FCPlanRiskDuty> fcPlanRiskDutyList) {
        FCPlanRiskDuty fcPlanRiskDuty = fcPlanRiskDutyList.stream().findFirst().orElse(null);
        if (null == fcPlanRiskDuty || OperatorEnum.MODIFY.getCode().equals(fcPlanRiskDuty.getOperating())) {
            log.debug("fcPlanRiskDuty is null");
            return;
        }
        String planCode = fcPlanRiskDuty.getPlanCode();

        // 判断数字还是字母
        boolean num = PatternConstants.POSITIVE_INTEGER.matcher(planCode).matches();
        boolean letter = PatternConstants.UPPERCASE_LETTERS.matcher(planCode).matches();
        if (!num && !letter) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.NUM_OR_LETTER_ERROR);
        }

        // 查询该福利下保存的计划编码信息
        List<FCEnsurePlan> fcEnsurePlans = fcEnsurePlanMapper.selectEnsureCodeByplanObject(fcPlanRiskDuty.getEnsureCode(), null);
        if (CollectionUtils.isEmpty(fcEnsurePlans)) {
            return;
        }

        FCEnsurePlan fcEnsurePlan = fcEnsurePlans.stream().max(Comparator.comparingInt(s -> Integer.parseInt(s.getPlanCode()))).orElse(null);
        if (null == fcEnsurePlan || null == fcEnsurePlan.getPlanCode()) {
            return;
        }
        log.info("planCodeCheck fcEnsurePlan :{}", fcEnsurePlan.getPlanCode());

        boolean oldNum = PatternConstants.POSITIVE_INTEGER.matcher(fcEnsurePlan.getPlanCode()).matches();
        if (oldNum && num) {
            if (Integer.parseInt(planCode) != Integer.parseInt(fcEnsurePlan.getPlanCode()) + 1) {
                log.debug("SMALL_TO_BIG_ERROR");
                throw new EFlexServiceException(EFlexServiceExceptionEnum.SMALL_TO_BIG_ERROR);
            }
        } else if (letter && !oldNum) {
            if (!planCode.equals(getNextCapitalLetter(fcEnsurePlan.getPlanCode()))) {
                log.debug("LETTER_ERROR");
                throw new EFlexServiceException(EFlexServiceExceptionEnum.LETTER_ERROR);
            }
        } else {
            log.debug("LETTER_ERROR");
            throw new EFlexServiceException(EFlexServiceExceptionEnum.NUM_LETTER_ERROR);
        }

    }


    public static String getNextCapitalLetter(String letter) {
        char currentLetter = letter.charAt(0);
        if (currentLetter < 'A' || currentLetter > 'Z') {
            throw new IllegalArgumentException("The input letter is not an uppercase letter.");
        }
        return String.valueOf((char) ((currentLetter + 1 - 'A') % 26 + 'A'));
    }


    @Transactional(rollbackFor = Exception.class)
    public String dealData(String token, List<FCPlanRiskDuty> fCPlanRiskDutyList) {
        planMessage = "";
        bool = true;
        try {
            FCPlanRiskDuty fcPlanRiskDuty = fCPlanRiskDutyList.get(0);
            String ensureCode = fcPlanRiskDuty.getEnsureCode();
            String planCode = fcPlanRiskDuty.getPlanCode();
            String riskCode = fcPlanRiskDuty.getRiskCode();
            String operating = fcPlanRiskDuty.getOperating();
            String planKey = fcPlanRiskDuty.getPlanKey();
            String planObject = fcPlanRiskDuty.getPlanObject();
            String planName = fcPlanRiskDuty.getPlanName();
            GlobalInput globalInput = userService.getSession(token);
            FCEnsure fcEnsure = new FCEnsure();
            if (!ensureCode.contains("JHS")) {
                fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            }
            String operator = globalInput.getUserNo();
            FCEnsurePlan plan = new FCEnsurePlan();
            fcEnsurePlanList.clear();
            fcPlanRiskList.clear();
            fcPlanRiskDutyList.clear();
            //声明一个计划对象的集合
            List<String> PlanObjectlist = new ArrayList<>();
            //获取停售的险种集合
            List<String> stopSaleRiskList = fdRiskInfoMapper.selectStopSaleRiskCode();
            // 对传进来List集合进行处理
            FCPlanRiskDuty fcPlanRiskDuty_GD0018 = null;
            FCPlanRiskDuty fcPlanRiskDuty_GD0029 = null;
            FCPlanRiskDuty fcPlanRiskDuty_GD0030 = null;
            FCPlanRiskDuty fcPlanRiskDuty_GD0050 = null;
            FCPlanRiskDuty fcPlanRiskDuty_GD0051 = null;
            FCPlanRiskDuty fcPlanRiskDuty_GD0052 = null;
            FCPlanRiskDuty fcPlanRiskDuty_GD0053 = null;
            FCPlanRiskDuty fcPlanRiskDuty_GD0054 = null;
            FCPlanRiskDuty fcPlanRiskDuty_GD0055 = null;
            FCPlanRiskDuty fcPlanRiskDuty_GD0056 = null;
            FCPlanRiskDuty fcPlanRiskDuty_GD0057 = null;
            FCPlanRiskDuty fcPlanRiskDuty_GD0058 = null;
            FCPlanRiskDuty fcPlanRiskDuty_GD0059 = null;
            FCPlanRiskDuty fcPlanRiskDutyMandatory_GD0017 = null;
            FCPlanRiskDuty fcPlanRiskDutyMandatory_GD0031 = null;
            FCPlanRiskDuty fcPlanRiskDutyMandatory_GD0037 = null;
            FCPlanRiskDuty fcPlanRiskDutyMandatory_GD0028 = null;
            FCPlanRiskDuty fcPlanRiskDutyMandatory_GD0060 = null;
            FCPlanRiskDuty fcPlanRiskDutyMandatory_ID6490 = null;
            FCPlanRiskDuty fcPlanRiskDutyMandatory_GD0032 = null;
            FCPlanRiskDuty fcPlanRiskDutyMandatory_GD0033 = null;
            FCPlanRiskDuty fcPlanRiskDutyMandatory_GD0034 = null;
            FCPlanRiskDuty fcPlanRiskDutyMandatory_GD0035 = null;
            FCPlanRiskDuty fcPlanRiskDutyMandatory_17050 = null;

            // 保费保额集合
            String amnt_GD0050 = "";
            String prem_GD0050 = "";
            String amnt_GD0051 = "";
            String prem_GD0051 = "";
            String amnt_GD0052 = "";
            String prem_GD0052 = "";
            String amnt_GD0053 = "";
            String prem_GD0053 = "";
            String amnt_GD0054 = "";
            String prem_GD0054 = "";
            String amnt_GD0055 = "";
            String prem_GD0055 = "";
            String amnt_GD0056 = "";
            String prem_GD0056 = "";
            String amnt_GD0057 = "";
            String prem_GD0057 = "";
            String amnt_GD0058 = "";
            String prem_GD0058 = "";
            String amnt_GD0059 = "";
            String prem_GD0059 = "";
            for (FCPlanRiskDuty fcPlanRiskDuty1 : fCPlanRiskDutyList) {
                if ("GD0018".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDuty_GD0018 = fcPlanRiskDuty1;
                }
                if ("GD0029".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDuty_GD0029 = fcPlanRiskDuty1;
                }
                if ("GD0030".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDuty_GD0030 = fcPlanRiskDuty1;
                }
                if ("GD0050".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDuty_GD0050 = fcPlanRiskDuty1;
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0050.getAmnt())) {
                        amnt_GD0050 = null;
                    } else {
                        amnt_GD0050 = String.valueOf(fcPlanRiskDuty_GD0050.getAmnt());
                    }
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0050.getPrem())) {
                        prem_GD0050 = null;
                    } else {
                        prem_GD0050 = String.valueOf(fcPlanRiskDuty_GD0050.getPrem());
                    }
                }
                if ("GD0051".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDuty_GD0051 = fcPlanRiskDuty1;
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0051.getAmnt())) {
                        amnt_GD0051 = null;
                    } else {
                        amnt_GD0051 = String.valueOf(fcPlanRiskDuty_GD0051.getAmnt());
                    }
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0051.getPrem())) {
                        prem_GD0051 = null;
                    } else {
                        prem_GD0051 = String.valueOf(fcPlanRiskDuty_GD0051.getPrem());
                    }
                }
                if ("GD0052".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDuty_GD0052 = fcPlanRiskDuty1;
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0052.getAmnt())) {
                        amnt_GD0052 = null;
                    } else {
                        amnt_GD0052 = String.valueOf(fcPlanRiskDuty_GD0052.getAmnt());
                    }
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0052.getPrem())) {
                        prem_GD0052 = null;
                    } else {
                        prem_GD0052 = String.valueOf(fcPlanRiskDuty_GD0052.getPrem());
                    }
                }
                if ("GD0053".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDuty_GD0053 = fcPlanRiskDuty1;
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0053.getAmnt())) {
                        amnt_GD0053 = null;
                    } else {
                        amnt_GD0053 = String.valueOf(fcPlanRiskDuty_GD0053.getAmnt());
                    }
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0053.getPrem())) {
                        prem_GD0053 = null;
                    } else {
                        prem_GD0053 = String.valueOf(fcPlanRiskDuty_GD0053.getPrem());
                    }
                }
                if ("GD0054".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDuty_GD0054 = fcPlanRiskDuty1;
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0054.getAmnt())) {
                        amnt_GD0054 = null;
                    } else {
                        amnt_GD0054 = String.valueOf(fcPlanRiskDuty_GD0054.getAmnt());
                    }
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0054.getPrem())) {
                        prem_GD0054 = null;
                    } else {
                        prem_GD0054 = String.valueOf(fcPlanRiskDuty_GD0054.getPrem());
                    }
                }
                if ("GD0055".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDuty_GD0055 = fcPlanRiskDuty1;
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0055.getAmnt())) {
                        amnt_GD0055 = null;
                    } else {
                        amnt_GD0055 = String.valueOf(fcPlanRiskDuty_GD0055.getAmnt());
                    }
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0055.getPrem())) {
                        prem_GD0055 = null;
                    } else {
                        prem_GD0055 = String.valueOf(fcPlanRiskDuty_GD0055.getPrem());
                    }
                }
                if ("GD0056".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDuty_GD0056 = fcPlanRiskDuty1;
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0056.getAmnt())) {
                        amnt_GD0056 = null;
                    } else {
                        amnt_GD0056 = String.valueOf(fcPlanRiskDuty_GD0056.getAmnt());
                    }
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0056.getPrem())) {
                        prem_GD0056 = null;
                    } else {
                        prem_GD0056 = String.valueOf(fcPlanRiskDuty_GD0056.getPrem());
                    }
                }
                if ("GD0057".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDuty_GD0057 = fcPlanRiskDuty1;
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0057.getAmnt())) {
                        amnt_GD0057 = null;
                    } else {
                        amnt_GD0057 = String.valueOf(fcPlanRiskDuty_GD0057.getAmnt());
                    }
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0057.getPrem())) {
                        prem_GD0057 = null;
                    } else {
                        prem_GD0057 = String.valueOf(fcPlanRiskDuty_GD0057.getPrem());
                    }
                }
                if ("GD0058".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDuty_GD0058 = fcPlanRiskDuty1;
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0058.getAmnt())) {
                        amnt_GD0058 = null;
                    } else {
                        amnt_GD0058 = String.valueOf(fcPlanRiskDuty_GD0058.getAmnt());
                    }
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0058.getPrem())) {
                        prem_GD0058 = null;
                    } else {
                        prem_GD0058 = String.valueOf(fcPlanRiskDuty_GD0058.getPrem());
                    }
                }
                if ("GD0059".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDuty_GD0059 = fcPlanRiskDuty1;
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0059.getAmnt())) {
                        amnt_GD0059 = null;
                    } else {
                        amnt_GD0059 = String.valueOf(fcPlanRiskDuty_GD0059.getAmnt());
                    }
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0059.getPrem())) {
                        prem_GD0059 = null;
                    } else {
                        prem_GD0059 = String.valueOf(fcPlanRiskDuty_GD0059.getPrem());
                    }
                }
                //  团体定期寿险保险责任(GD0017)
                if ("GD0017".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDutyMandatory_GD0017 = fcPlanRiskDuty1;
                }
                // 门诊急诊团体医疗保险责任(GD0031)
                if ("GD0031".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDutyMandatory_GD0031 = fcPlanRiskDuty1;
                }
                // 团体意外伤害保险责任(GD0037)
                if ("GD0037".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDutyMandatory_GD0037 = fcPlanRiskDuty1;
                }
                // 意外伤害团体医疗保险责任(GD0028)
                if ("GD0028".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDutyMandatory_GD0028 = fcPlanRiskDuty1;
                }
                // 重大疾病保险责任(GD0060)
                if ("GD0060".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDutyMandatory_GD0060 = fcPlanRiskDuty1;
                }
                // 横琴琴逸团体重大疾病保险责任(ID6490)
                if ("ID6490".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDutyMandatory_ID6490 = fcPlanRiskDuty1;
                }
                // 一般住院日额津贴保险金(GD0032)
                if ("GD0032".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDutyMandatory_GD0032 = fcPlanRiskDuty1;
                }
                // 癌症住院日额津贴保险金(可选责任) GD0033
                if ("GD0033".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDutyMandatory_GD0033 = fcPlanRiskDuty1;
                }
                // 重症监护日额津贴保险金(可选责任) GD0034
                if ("GD0034".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDutyMandatory_GD0034 = fcPlanRiskDuty1;
                }
                // 手术医疗津贴保险金(可选责任) GD0035
                if ("GD0035".equals(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDutyMandatory_GD0035 = fcPlanRiskDuty1;
                }
                // 一般医疗保险金GD0070/恶性肿瘤医疗保险金GD0071
                if ("17050".equals(fcPlanRiskDuty1.getRiskCode())) {
                    fcPlanRiskDutyMandatory_17050 = fcPlanRiskDuty1;
                }
            }
            // 对必选责任和可选责任处理
            // 必选责任保额
            String amnt_Mandatory = "";
            // 必选责任保费
            String prem_Mandatory = "";
            // 必选责任最大赔付天数
            String maxGetDay_Mandatory = "";
            // 可选责任保额
            String amnt_Optional = "";
            // 可选责任保费
            String prem_Optional = "";
            // 可选责任最大赔付天数
            String maxGetDay_Optional = "";
            // 处理险种
            planPrem = 0.0;
            //保额
            String amnt = "";
            //保费
            String prem = "";
            //免赔额
            String getLimit = "";
            //免赔额属性
            String getLimitType = "";
            //赔付比例
            String getRatio = "";
            //最大赔付天数
            String maxGetDay = "";
            // 17020险种必选责任保额
            String amnt_GD0032 = "";
            // 17020险种必选责任保费
            String prem_GD0032 = "";
            // 17020险种必选责任最大赔付天数
            String maxGetDay_GD0032 = "";
            // 17020险种可选责任1保额
            String amnt_GD0033 = "";
            // 17020险种可选责任1保费
            String prem_GD0033 = "";
            // 17020险种可选责任1最大赔付天数
            String maxGetDay_GD0033 = "";
            // 17020险种可选责任2保额
            String amnt_GD0034 = "";
            // 17020险种可选责任2保费
            String prem_GD0034 = "";
            // 17020险种可选责任2最大赔付天数
            String maxGetDay_GD0034 = "";
            // 17020险种可选责任3保额
            String amnt_GD0035 = "";
            // 17020险种可选责任3保费
            String prem_GD0035 = "";
            // 17020险种可选责任3最大赔付天数
            String maxGetDay_GD0035 = "";
            // 对计划编码、计划名称、计划对象、计划重点进行判断
            if (ObjectUtils.isEmpty(planName)) {
                planMessage += "计划名称不能为空！";
                return planMessage;
            }
            if (ObjectUtils.isEmpty(planObject)) {
                planMessage += "计划对象不能为空！";
                return planMessage;
            }
            if (ObjectUtils.isEmpty(planKey)) {
                planMessage += "计划重点不能为空！";
                return planMessage;
            }
            plan.setEnsureCode(ensureCode);
            plan.setPlanCode(planCode);
            plan.setPlanName(planName);
            plan.setPlanObject(planObject);
            plan.setPlanKey(planKey);
            // 1、团体定期寿险（12020）
            if ("12020".equals(riskCode)) {
                bool = true;
                amnt = String.valueOf(fcPlanRiskDutyMandatory_GD0017.getAmnt());
                prem = String.valueOf(fcPlanRiskDutyMandatory_GD0017.getPrem());

                if (StringUtils.isEmpty(amnt)
                        || !EnsureMakeService.isNumber(amnt)
                        || !EnsureMakeService.isAmnt(amnt, 1)) {
                    bool = false;
                    planMessage += "团体定期寿险保额不能为空，保额最低为1万，且保留到小数点后两位。（只能为数字）";
                    return planMessage;
                }
                if (StringUtils.isEmpty(prem)
                        || !EnsureMakeService.isNumber0(prem)) {
                    bool = false;
                    planMessage += "团体定期寿险保费不能为空,且必须是大于等于0的数字以及仅支持小数点后两位。";
                    return planMessage;
                }
                if (bool && stopSaleRiskList.contains(riskCode)) {
                    bool = false;
                    planMessage += "团体定期寿险已下架，暂不支持计划定制。";
                    return planMessage;
                }
                if (bool) {
                    dealRow(ensureCode, riskCode, "GD0017",
                            operator, "N", amnt, prem, getLimit,
                            getLimitType, getRatio, planCode);
                }
            }
            // 2、门诊急诊团体医疗保险（17030）
            if ("17030".equals(riskCode)) {
                bool = true;
                amnt = String.valueOf(fcPlanRiskDutyMandatory_GD0031.getAmnt());
                prem = String.valueOf(fcPlanRiskDutyMandatory_GD0031.getPrem());
                getLimit = String.valueOf(fcPlanRiskDutyMandatory_GD0031.getGetLimit());
                getLimitType = fcPlanRiskDutyMandatory_GD0031.getGetLimitType();
                getRatio = String.valueOf(fcPlanRiskDutyMandatory_GD0031.getGetRatio());

                if (StringUtils.isEmpty(amnt)
                        || !EnsureMakeService.isNumber(amnt)
                        || !EnsureMakeService.isAmnt(amnt, 2)) {
                    bool = false;
                    planMessage += "门诊急诊团体医疗保险保额不能为空，必须为下拉框指定的值。";
                    return planMessage;
                }
                if (StringUtils.isEmpty(prem) || !EnsureMakeService.isNumber0(prem)) {
                    bool = false;
                    planMessage += "门诊急诊团体医疗保险保费不能为空,且必须是大于等于0的数字以及仅支持小数点后两位。";
                    return planMessage;
                }
                if (StringUtils.isEmpty(getLimit)) {
                    bool = false;
                    planMessage += "门诊急诊团体医疗保险免赔额不能为空。";
                    return planMessage;
                }
                if (StringUtils.isEmpty(getLimitType)
                        || !EnsureMakeService.isGetLimitType(getLimitType, 1)) {
                    bool = false;
                    planMessage += "门诊急诊团体医疗保险免赔额属性不能为空，且只能为下拉框指定的值。";
                    return planMessage;
                } else {
                    //门诊急诊团体医疗保险（17030）险种的免赔额属性判断，免赔额的数值
                    if (getLimitType.equals("按次免赔")
                            && !StringUtils.isEmpty(getLimit)
                            && EnsureMakeService.isNumber0(getLimit)
                            && !EnsureMakeService.isDeductibles(getLimit, 1)) {
                        planMessage += "免赔额输入错误，请重新输入。";
                        return planMessage;
                    }
                    if (getLimitType.equals("按年免赔")
                            && !getLimit.matches("^\\d+$")) {
                        planMessage += "免赔额输入错误，请重新输入。";
                        return planMessage;
                    }
                }
                if (StringUtils.isEmpty(getRatio)
                        || !EnsureMakeService.isNumber1(getRatio)
                        || !EnsureMakeService.isReimbursementRatio(getRatio, 1)) {
                    bool = false;
                    planMessage += "门诊急诊团体医疗保险赔付比例不能为空,且只能为下拉框指定的值。";
                    return planMessage;
                }
                if (bool && stopSaleRiskList.contains(riskCode)) {
                    bool = false;
                    planMessage += "门诊急诊团体医疗保险已下架，暂不支持计划定制。";
                    return planMessage;
                }
                if (bool) {
                    dealRow(ensureCode, riskCode, "GD0031",
                            operator, "Y", amnt, prem, getLimit,
                            getLimitType, getRatio, planCode);
                }
            }
            // 3、团体意外伤害保险（15030）
            if ("15030".equals(riskCode)) {
                bool = true;
                amnt = String.valueOf(fcPlanRiskDutyMandatory_GD0037.getAmnt());
                prem = String.valueOf(fcPlanRiskDutyMandatory_GD0037.getPrem());

                if (StringUtils.isEmpty(amnt)
                        || !EnsureMakeService.isNumber(amnt)
                        || !EnsureMakeService.isAmnt(amnt, 3)) {
                    bool = false;
                    planMessage += "团体意外伤害保险保额不能为空，最低为1万且只能以千元递增，保留到小数点后两位。（只能为数字）";
                    return planMessage;
                }
                if (StringUtils.isEmpty(prem)
                        || !EnsureMakeService.isNumber0(prem)) {
                    bool = false;
                    planMessage += "团体意外伤害保险保费不能为空,且必须是大于等于0的数字，以及仅支持小数点后两位。";
                    return planMessage;
                }
                if (bool && stopSaleRiskList.contains(riskCode)) {
                    bool = false;
                    planMessage += "团体意外伤害保险保险已下架，暂不支持计划定制。/";
                    return planMessage;
                }
                if (bool) {
                    dealRow(ensureCode, riskCode, "GD0037",
                            operator, "N", amnt, prem, getLimit,
                            getLimitType, getRatio, planCode);
                }
            }

            // 4、意外伤害团体医疗保险（15040）
            if ("15040".equals(riskCode)) {
                bool = true;
                amnt = String.valueOf(fcPlanRiskDutyMandatory_GD0028.getAmnt());
                prem = String.valueOf(fcPlanRiskDutyMandatory_GD0028.getPrem());
                getLimit = String.valueOf(fcPlanRiskDutyMandatory_GD0028.getGetLimit());
                getRatio = String.valueOf(fcPlanRiskDutyMandatory_GD0028.getGetRatio());

                if (StringUtils.isEmpty(amnt)
                        || !EnsureMakeService.isNumber(amnt)
                        || !EnsureMakeService.isAmnt(amnt, 4)) {
                    bool = false;
                    planMessage += "意外伤害团体医疗保险保额不能为空，最低为0.5万且只能以千元递增，保留到小数点后两位。（只能为数字）";
                    return planMessage;
                }
                if (StringUtils.isEmpty(prem)
                        || !EnsureMakeService.isNumber0(prem)) {
                    bool = false;
                    planMessage += "意外伤害团体医疗保险保费不能为空，且必须是大于等于0的数字，以及仅支持小数点后两位。";
                    return planMessage;
                }
                if (StringUtils.isEmpty(getLimit)
                        || !EnsureMakeService.isNumber0(getLimit)
                        || !EnsureMakeService.isDeductibles(getLimit, 2)) {
                    bool = false;
                    planMessage += "意外伤害团体医疗保险免赔额不能为空,且只能为下拉框指定的值。";
                    return planMessage;
                }
                if (StringUtils.isEmpty(getRatio)
                        || !EnsureMakeService.isNumber1(getRatio)
                        || !EnsureMakeService.isReimbursementRatio(getRatio, 1)) {
                    bool = false;
                    planMessage += "意外伤害团体医疗保险赔付比例不能为空,且只能为下拉框指定的值。";
                    return planMessage;
                }
                if (bool && stopSaleRiskList.contains(riskCode)) {
                    bool = false;
                    planMessage += "意外伤害团体医疗保险已下架，暂不支持计划定制。/";
                    return planMessage;
                }
                if (bool) {
                    dealRow(ensureCode, riskCode, "GD0028",
                            operator, "Y", amnt, prem, getLimit,
                            getLimitType, getRatio, planCode);
                }
            }

            // 6、团体重大疾病保险（16040）
            if ("16040".equals(riskCode)) {
                bool = true;
                amnt = String.valueOf(fcPlanRiskDutyMandatory_GD0060.getAmnt());
                prem = String.valueOf(fcPlanRiskDutyMandatory_GD0060.getPrem());

                if (StringUtils.isEmpty(amnt)
                        || !EnsureMakeService.isNumber(amnt)
                        || !EnsureMakeService.isAmnt(amnt, 5)) {
                    bool = false;
                    planMessage += "团体重大疾病保险保额不能为空,且必须是1~100的数字，以及仅支持小数点后两位。";
                    return planMessage;
                }
                if (StringUtils.isEmpty(prem)
                        || !EnsureMakeService.isNumber0(prem)) {
                    bool = false;
                    planMessage += "团体重大疾病保险保费不能为空,且必须是大于等于0的数字，以及仅支持小数点后两位。";
                    return planMessage;
                }
                if (bool && stopSaleRiskList.contains(riskCode)) {
                    bool = false;
                    planMessage += "团体重大疾病保险已下架，暂不支持计划定制。";
                    return planMessage;
                }
                if (bool) {
                    dealRow(ensureCode, riskCode, "GD0060",
                            operator, "N", amnt, prem, getLimit,
                            getLimitType, getRatio, planCode);
                }
            }
            // 7、琴逸团体重大疾病保险
            if ("16490".equals(riskCode)) {
                bool = true;
                amnt = String.valueOf(fcPlanRiskDutyMandatory_ID6490.getAmnt());
                prem = String.valueOf(fcPlanRiskDutyMandatory_ID6490.getPrem());

                if (StringUtils.isEmpty(amnt)
                        || !EnsureMakeService.isNumber0(amnt)
                        || !EnsureMakeService.isAmnt(amnt, 5)) {
                    bool = false;
                    planMessage += "琴逸团体重大疾病保险保额不能为空,且必须是1~100的数字，以及仅支持小数点后两位。";
                    return planMessage;
                }
                if (StringUtils.isEmpty(prem)
                        || !EnsureMakeService.isNumber0(prem)) {
                    bool = false;
                    planMessage += "琴逸团体重大疾病保险保费不能为空,且必须是大于0的数字，以及仅支持小数点后两位。";
                    return planMessage;
                }
                if (bool && stopSaleRiskList.contains(riskCode)) {
                    bool = false;
                    planMessage += "琴逸团体重大疾病保险已下架，暂不支持计划定制。";
                    return planMessage;
                }
                if (bool) {
                    dealRow(ensureCode, riskCode, "ID6490",
                            operator, "N", amnt, prem, getLimit,
                            getLimitType, getRatio, planCode);
                }
            }
            // 7、住院津贴团体医疗保险（17020）
            if ("17020".equals(riskCode)) {
                bool = true;
                if ((ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0032.getAmnt())
                        && ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0032.getPrem())
                        && ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0032.getMaxGetDay()))
                        && ((!ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0033.getAmnt())
                        || !ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0033.getPrem())
                        || !ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0033.getMaxGetDay()))
                        || (!ObjectUtils
                        .isEmpty(fcPlanRiskDutyMandatory_GD0034.getAmnt())
                        || !ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0034.getPrem())
                        || !ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0034.getMaxGetDay()))
                        || (!ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0035.getAmnt())
                        || !ObjectUtils
                        .isEmpty(fcPlanRiskDutyMandatory_GD0035.getPrem())
                        || !ObjectUtils.isEmpty(
                        fcPlanRiskDutyMandatory_GD0035.getMaxGetDay())))) {
                    bool = false;
                    planMessage += "一般住院日额津贴保险金为必选责任，请录入";
                    return planMessage;
                }
                if (!ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0032.getAmnt())
                        || !ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0032.getPrem())
                        || !ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0032.getMaxGetDay())) {
                    /************************ 必选责任 ***********************************************/
                    if (ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0032.getAmnt())) {
                        amnt_GD0032 = null;
                    } else {
                        amnt_GD0032 = String.valueOf(fcPlanRiskDutyMandatory_GD0032.getAmnt());
                    }
                    if (ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0032.getPrem())) {
                        prem_GD0032 = null;
                    } else {
                        prem_GD0032 = String.valueOf(fcPlanRiskDutyMandatory_GD0032.getPrem());
                    }
                    if (ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0032.getMaxGetDay())) {
                        maxGetDay_GD0032 = null;
                    } else {
                        maxGetDay_GD0032 = String.valueOf(fcPlanRiskDutyMandatory_GD0032.getMaxGetDay());
                    }
                    if (StringUtils.isEmpty(amnt_GD0032) || !EnsureMakeService.isNumber(amnt_GD0032)
                            || !EnsureMakeService.isAmnt(amnt_GD0032, 6)) {
                        bool = false;
                        planMessage += "住院津贴团体医疗保险，一般住院日额津贴保险责任为必录责任保额不能为空，最低为10且只能以10元递增，保留到小数点后两位。（只能是数字）";
                        return planMessage;
                    }
                    if (StringUtil.isEmpty(prem_GD0032) || !EnsureMakeService.isNumber0(prem_GD0032)) {
                        bool = false;
                        planMessage += "住院津贴团体医疗保险，一般住院日额津贴保险责任为必录责任保费不能为空，且必须是大于等于0的数字，以及仅支持小数点后两位。";
                        return planMessage;
                    }
                    if (StringUtils.isEmpty(maxGetDay_GD0032) || !EnsureMakeService.isMaxPayDay(maxGetDay_GD0032)) {
                        bool = false;
                        planMessage += "住院津贴团体医疗保险，一般住院日额津贴保险责任为必录责任最大赔付天数不能为空，且必须是1-366之间的整数。";
                        return planMessage;
                    }
                }
                /************************ 可选责任1 ***********************************************/
                if (!ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0033.getAmnt())
                        || !ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0033.getPrem())
                        || !ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0033.getMaxGetDay())) {
                    if (ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0033.getAmnt())) {
                        amnt_GD0033 = null;
                    } else {
                        amnt_GD0033 = String.valueOf(fcPlanRiskDutyMandatory_GD0033.getAmnt());
                    }
                    if (ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0033.getPrem())) {
                        prem_GD0033 = null;
                    } else {
                        prem_GD0033 = String.valueOf(fcPlanRiskDutyMandatory_GD0033.getPrem());
                    }
                    if (ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0033.getMaxGetDay())) {
                        maxGetDay_GD0033 = null;
                    } else {
                        maxGetDay_GD0033 = String.valueOf(fcPlanRiskDutyMandatory_GD0033.getMaxGetDay());
                    }
                    if (StringUtils.isEmpty(amnt_GD0033) || !EnsureMakeService.isNumber(amnt_GD0033)
                            || !EnsureMakeService.isAmnt(amnt_GD0033, 6)) {
                        bool = false;
                        planMessage += "住院津贴团体医疗保险，癌症住院日额津贴保险金责任保额不能为空,最低为10且只能以10元递增，保留到小数点后两位。（只能是数字）";
                        return planMessage;
                    }
                    if (StringUtils.isEmpty(prem_GD0033) || !EnsureMakeService.isNumber0(prem_GD0033)) {
                        bool = false;
                        planMessage += "住院津贴团体医疗保险，癌症住院日额津贴保险金责任保费不能为空,且必须是大于等于0的数字，以及仅支持小数点后两位。";
                        return planMessage;
                    }
                    if (StringUtils.isEmpty(maxGetDay_GD0033) || !EnsureMakeService.isMaxPayDay(maxGetDay_GD0033)) {
                        bool = false;
                        planMessage += "住院津贴团体医疗保险，癌症住院日额津贴保险金责任最大赔付天数不能为空，且必须是1-366之间的整数。";
                        return planMessage;
                    }
                }
                /************************ 可选责任2 ***********************************************/
                if (!ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0034.getAmnt())
                        || !ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0034.getPrem())
                        || !ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0034.getMaxGetDay())) {

                    if (ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0034.getAmnt())) {
                        amnt_GD0034 = null;
                    } else {
                        amnt_GD0034 = String.valueOf(fcPlanRiskDutyMandatory_GD0034.getAmnt());
                    }
                    if (ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0034.getPrem())) {
                        prem_GD0034 = null;
                    } else {
                        prem_GD0034 = String.valueOf(fcPlanRiskDutyMandatory_GD0034.getPrem());
                    }
                    if (ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0034.getMaxGetDay())) {
                        maxGetDay_GD0034 = null;
                    } else {
                        maxGetDay_GD0034 = String.valueOf(fcPlanRiskDutyMandatory_GD0034.getMaxGetDay());
                    }
                    if (StringUtils.isEmpty(amnt_GD0034) || !EnsureMakeService.isNumber(amnt_GD0034)
                            || !EnsureMakeService.isAmnt(amnt_GD0034, 6)) {
                        bool = false;
                        planMessage += "住院津贴团体医疗保险，重症监护日额津贴保险金责任保额不能为空,最低为10且只能以10元递增，保留到小数点后两位。（只能是数字）";
                        return planMessage;
                    }
                    if (StringUtils.isEmpty(prem_GD0034) || !EnsureMakeService.isNumber0(prem_GD0034)) {
                        bool = false;
                        planMessage += "住院津贴团体医疗保险，重症监护日额津贴保险金责任保费不能为空,且必须是大于等于0的数字，以及仅支持小数点后两位。";
                        return planMessage;
                    }
                    if (StringUtils.isEmpty(maxGetDay_GD0034) || !EnsureMakeService.isMaxPayDay(maxGetDay_GD0034)) {
                        bool = false;
                        planMessage += "住院津贴团体医疗保险，重症监护日额津贴保险金责任最大赔付天数不能为空，且必须是1-366之间的整数。";
                        return planMessage;
                    }
                }
                /************************ 可选责任3 ***********************************************/
                if (!ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0035.getAmnt())
                        || !ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0035.getPrem())
                        || !ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0035.getMaxGetDay())) {
                    if (ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0035.getAmnt())) {
                        amnt_GD0035 = null;
                    } else {
                        amnt_GD0035 = String
                                .valueOf(CommonUtil.div(fcPlanRiskDutyMandatory_GD0035.getAmnt(), (double) 10000, 2));
                    }
                    if (ObjectUtils.isEmpty(fcPlanRiskDutyMandatory_GD0035.getPrem())) {
                        prem_GD0035 = null;
                    } else {
                        prem_GD0035 = String.valueOf(fcPlanRiskDutyMandatory_GD0035.getPrem());
                    }
                    if (StringUtils.isEmpty(amnt_GD0035) || !EnsureMakeService.isNumber(amnt_GD0035)
                            || !EnsureMakeService.isAmnt(amnt_GD0035, 4)) {
                        bool = false;
                        planMessage += "住院津贴团体医疗保险，手术医疗津贴保险金责任保额不能为空,且必须是最低为5000元的数字且以千元递增。";
                        return planMessage;
                    }
                    if (StringUtils.isEmpty(prem_GD0035) || !EnsureMakeService.isNumber0(prem_GD0035)) {
                        bool = false;
                        planMessage += "住院津贴团体医疗保险，手术医疗津贴保险金责任保费不能为空,且必须是大于等于0的数字，以及仅支持小数点后两位。";
                        return planMessage;
                    }
                }
                if (bool && stopSaleRiskList.contains(riskCode)) {
                    bool = false;
                    planMessage += "住院津贴团体医疗保险已下架，暂不支持计划定制。";
                    return planMessage;
                }
                if (bool) {
                    dealRow_17020Now(fCPlanRiskDutyList);
                }
            }
            // 9、尊享团体补充医疗保险（17050）
            if ("17050".equals(riskCode)) {
                bool = true;
                // 一般医疗保险金GD0070
                amnt = String.valueOf(fcPlanRiskDutyMandatory_17050.getAmnt());
                prem = String.valueOf(fcPlanRiskDutyMandatory_17050.getPrem());
                getLimit = String.valueOf(fcPlanRiskDutyMandatory_17050.getGetLimit());
                getRatio = String.valueOf(fcPlanRiskDutyMandatory_17050.getGetRatio());
                // 保额（元）
                if (StringUtils.isEmpty(amnt)
                        || !EnsureMakeService.isNumber(amnt)
                        || !EnsureMakeService.isAmnt(amnt, 11)) {
                    bool = false;
                    planMessage += "尊享团体补充医疗保险，一般医疗保险金责任为必录责任保额不能为空，且必须是下拉框中指定的数字。";
                    return planMessage;
                }
                // 保费（元）
                if (StringUtils.isEmpty(prem)
                        || !EnsureMakeService.isNumber0(prem)) {
                    bool = false;
                    planMessage += "尊享团体补充医疗保险，一般医疗保险金责任为必录责任保费不能为空，且必须是大于等于0的数字，以及仅支持小数点后两位。";
                    return planMessage;
                }
                // 免赔额
                if (StringUtils.isEmpty(getLimit)
                        || !EnsureMakeService.isNumber0(getLimit)) {
                    bool = false;
                    planMessage += "尊享团体补充医疗保险，一般医疗保险金责任为必录责任免赔额不能为空，且必须是大于等于0的数字，以及仅支持小数点后两位。";
                    return planMessage;
                }
                // 赔付比例(%)
                if (StringUtils.isEmpty(getRatio)
                        || !EnsureMakeService.isNumber1(getRatio)
                        || !EnsureMakeService.isReimbursementRatio(getRatio, 2)) {
                    bool = false;
                    planMessage += "尊享团体补充医疗保险，一般医疗保险金责任为必录责任赔付比例不能为空，且只能为下拉框指定的值。";
                    return planMessage;
                }
                if (bool && stopSaleRiskList.contains(riskCode)) {
                    bool = false;
                    planMessage += "尊享团体补充医疗保险已下架，暂不支持计划定制。";
                    return planMessage;
                }
                if (bool) {
                    dealRow_17050(ensureCode, riskCode, operator, amnt, prem, getLimit, getRatio, planCode);
                }
            }
            // 意外伤害住院津贴团体医疗保险（15060）
            if ("15060".equals(riskCode)) {
                if (!ObjectUtils.isEmpty(fcPlanRiskDuty_GD0029) || !ObjectUtils.isEmpty(fcPlanRiskDuty_GD0030)) {
                    bool = true;
                    // 必选责任
                    if (!ObjectUtils.isEmpty(fcPlanRiskDuty_GD0029)) {
                        if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0029.getAmnt())) {
                            amnt_Mandatory = null;
                        } else {
                            amnt_Mandatory = String.valueOf(fcPlanRiskDuty_GD0029.getAmnt());
                        }
                        if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0029.getPrem())) {
                            prem_Mandatory = null;
                        } else {
                            prem_Mandatory = String.valueOf(fcPlanRiskDuty_GD0029.getPrem());
                        }
                        if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0029.getMaxGetDay())) {
                            maxGetDay_Mandatory = null;
                        } else {
                            maxGetDay_Mandatory = String.valueOf(fcPlanRiskDuty_GD0029.getMaxGetDay());
                        }
                        // 如果必选责任未录入，只录入可选责任，则报错
                        if ((ObjectUtils.isEmpty(fcPlanRiskDuty_GD0029.getAmnt())
                                && ObjectUtils.isEmpty(fcPlanRiskDuty_GD0029.getPrem())
                                && ObjectUtils.isEmpty(fcPlanRiskDuty_GD0029.getMaxGetDay()))
                                && (!ObjectUtils.isEmpty(fcPlanRiskDuty_GD0030.getAmnt())
                                || !ObjectUtils.isEmpty(fcPlanRiskDuty_GD0030.getPrem())
                                || !ObjectUtils.isEmpty(fcPlanRiskDuty_GD0030.getMaxGetDay()))) {
                            bool = false;
                            planMessage += fcPlanRiskDuty_GD0029.getRiskName() + "责任为必选责任，请录入";
                            return planMessage;
                        }
                        if (ObjectUtils.isEmpty(amnt_Mandatory) || !EnsureMakeService.isNumber(amnt_Mandatory)
                                || !EnsureMakeService.isAmnt(amnt_Mandatory, 6)) {
                            bool = false;
                            planMessage += "意外伤害住院津贴团体医疗保险，意外伤害住院津贴保险责任为必录责任保额不能为空，最低为10且只能以10元递增，保留到小数点后两位。（只能为数字）";
                            return planMessage;
                        }
                        if (ObjectUtils.isEmpty(prem_Mandatory) || !EnsureMakeService.isNumber0(prem_Mandatory)) {
                            bool = false;
                            planMessage += "意外伤害住院津贴团体医疗保险，意外伤害住院津贴保险责任为必录责任保费不能为空，且必须是大于等于0的数字，以及仅支持小数点后两位。";
                            return planMessage;
                        }
                        if (ObjectUtils.isEmpty(maxGetDay_Mandatory)) {
                            bool = false;
                            planMessage += "意外伤害住院津贴团体医疗保险，意外伤害住院津贴保险责任为必录责任最大赔付天数不能为空!";
                            return planMessage;
                        } else {
                            if (!EnsureMakeService.isMaxPayDay(maxGetDay_Mandatory)) {
                                bool = false;
                                planMessage += "意外伤害住院津贴团体医疗保险最大赔付天数必须是1-366之间的整数。";
                                return planMessage;
                            }
                        }
                    }
                    // 可选责任
                    if (!ObjectUtils.isEmpty(fcPlanRiskDuty_GD0030)) {
                        if (!ObjectUtils.isEmpty(fcPlanRiskDuty_GD0030.getAmnt())
                                || !ObjectUtils.isEmpty(fcPlanRiskDuty_GD0030.getPrem())
                                || !ObjectUtils.isEmpty(fcPlanRiskDuty_GD0030.getMaxGetDay())) {
                            if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0030.getAmnt())) {
                                amnt_Optional = null;
                            } else {
                                amnt_Optional = String.valueOf(fcPlanRiskDuty_GD0030.getAmnt());
                            }
                            if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0030.getPrem())) {
                                prem_Optional = null;
                            } else {
                                prem_Optional = String.valueOf(fcPlanRiskDuty_GD0030.getPrem());
                            }
                            if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0030.getMaxGetDay())) {
                                maxGetDay_Optional = null;
                            } else {
                                maxGetDay_Optional = String.valueOf(fcPlanRiskDuty_GD0030.getMaxGetDay());
                            }
                            if (ObjectUtils.isEmpty(amnt_Optional) || !EnsureMakeService.isNumber(amnt_Optional)
                                    || !EnsureMakeService.isAmnt(amnt_Optional, 6)) {
                                bool = false;
                                planMessage += "意外伤害住院津贴团体医疗保险，意外伤害住院津贴保险责任为必录责任保额不能为空，最低为10且只能以10元递增，保留到小数点后两位。（只能为数字）";
                                return planMessage;
                            }
                            if (ObjectUtils.isEmpty(amnt_Optional) || !EnsureMakeService.isNumber(amnt_Optional)
                                    || Double.valueOf(amnt_Optional) > Double.valueOf(amnt_Mandatory) * 5) {
                                bool = false;
                                planMessage += "意外伤害住院津贴团体医疗保险，意外伤害重症住院津贴保险责任保额不能为空，且必须是大于0的数字且不得超过意外伤害住院日额津贴保险金的5倍以，及仅支持小数点后两位。";
                                return planMessage;
                            }
                            if (ObjectUtils.isEmpty(prem_Optional) || !EnsureMakeService.isNumber0(prem_Optional)) {
                                bool = false;
                                planMessage += "意外伤害住院津贴团体医疗保险，意外伤害重症住院津贴保险责任保费不能为空，且必须是大于等于0的数字，以及仅支持小数点后两位。";
                                return planMessage;
                            }
                            if (ObjectUtils.isEmpty(maxGetDay_Optional) || !EnsureMakeService.isMaxPayDay(maxGetDay_Optional)) {
                                bool = false;
                                planMessage += "意外伤害住院津贴团体医疗保险，意外伤害重症住院津贴保险责任最大赔付天数不能为空，且必须是1-366之间的整数。";
                                return planMessage;
                            }
                        }
                    }
                    if (bool && stopSaleRiskList.contains(riskCode)) {
                        bool = false;
                        planMessage += "意外伤害住院津贴团体医疗保险已下架，暂不支持计划定制。/";
                        return planMessage;
                    }
                    if (bool) {
                        dealRow_15060Now(fCPlanRiskDutyList);
                    }
                }
            }
            if ("15070".equals(riskCode)) {
                if (!ObjectUtils.isEmpty(fcPlanRiskDuty_GD0050) || !ObjectUtils.isEmpty(fcPlanRiskDuty_GD0051)
                        || !ObjectUtils.isEmpty(fcPlanRiskDuty_GD0052) || !ObjectUtils.isEmpty(fcPlanRiskDuty_GD0053)
                        || !ObjectUtils.isEmpty(fcPlanRiskDuty_GD0054) || !ObjectUtils.isEmpty(fcPlanRiskDuty_GD0055)
                        || !ObjectUtils.isEmpty(fcPlanRiskDuty_GD0056) || !ObjectUtils.isEmpty(fcPlanRiskDuty_GD0057)
                        || !ObjectUtils.isEmpty(fcPlanRiskDuty_GD0058) || !ObjectUtils.isEmpty(fcPlanRiskDuty_GD0059)) {
                    bool = true;
                    /************************ 必选责任 ***********************************************/
                    // 1、公路公共交通工具保险金GD0050
                    if (!ObjectUtils.isEmpty(fcPlanRiskDuty_GD0050)) {
                        if (!ObjectUtils.isEmpty(amnt_GD0050) || !ObjectUtils.isEmpty(prem_GD0050)) {
                            // 判断保额
                            if (ObjectUtils.isEmpty(amnt_GD0050)
                                    || !EnsureMakeService.isNumber(amnt_GD0050)
                                    || !EnsureMakeService.isAmnt(amnt_GD0050, 7)) {
                                bool = false;
                                planMessage += "公路公共交通工具保险金责任为必录责任保额不能为空，且必须是1万~200万的数字，以及仅支持小数点后两位。";
                                return planMessage;
                            }
                            // 判断保费
                            if (ObjectUtils.isEmpty(prem_GD0050) || !EnsureMakeService.isNumber0(prem_GD0050)) {
                                bool = false;
                                planMessage += "公路公共交通工具保险金责任为必录责任保费不能为空, 且必须是大于等于0的数字以及仅支持小数点后两位。";
                                return planMessage;
                            }
                        }
                    }
                    // 2、轨道交通工具保险金GD0051
                    if (!ObjectUtils.isEmpty(fcPlanRiskDuty_GD0051)) {
                        if (!ObjectUtils.isEmpty(amnt_GD0051) || !ObjectUtils.isEmpty(prem_GD0051)) {
                            // 判断保额
                            if (ObjectUtils.isEmpty(amnt_GD0051)
                                    || !EnsureMakeService.isNumber(amnt_GD0051)
                                    || !EnsureMakeService.isAmnt(amnt_GD0051, 8)) {
                                bool = false;
                                planMessage += "轨道交通工具保险金责任为必录责任保额不能为空，且必须是1万~300万的数字，以及仅支持小数点后两位。";
                                return planMessage;
                            }
                            // 判断保费
                            if (ObjectUtils.isEmpty(prem_GD0051) || !EnsureMakeService.isNumber0(prem_GD0051)) {
                                bool = false;
                                planMessage += "轨道交通工具保险金责任为必录责任保费不能为空，且必须是大于等于0的数字，以及仅支持小数点后两位。";
                                return planMessage;
                            }
                        }
                    }
                    // 3、水路公共交通工具保险金GD0052
                    if (!ObjectUtils.isEmpty(fcPlanRiskDuty_GD0052)) {
                        if (!ObjectUtils.isEmpty(amnt_GD0052) || !ObjectUtils.isEmpty(prem_GD0052)) {
                            // 判断保额
                            if (ObjectUtils.isEmpty(amnt_GD0052)
                                    || !EnsureMakeService.isNumber(amnt_GD0052)
                                    || !EnsureMakeService.isAmnt(amnt_GD0052, 7)) {
                                bool = false;
                                planMessage += "水路公共交通工具保险金责任为必录责任保额不能为空，且必须是1万~200万的数字，以及仅支持小数点后两位。";
                                return planMessage;
                            }
                            // 判断保费
                            if (ObjectUtils.isEmpty(prem_GD0052) || !EnsureMakeService.isNumber0(prem_GD0052)) {
                                bool = false;
                                planMessage += "水路公共交通工具保险金责任为必录责任保费不能为空，且必须是大于等于0的数字，以及仅支持小数点后两位。";
                                return planMessage;
                            }
                        }
                    }
                    // 4、民航班机保险金
                    if (!ObjectUtils.isEmpty(fcPlanRiskDuty_GD0053)) {
                        if (!ObjectUtils.isEmpty(amnt_GD0053) || !ObjectUtils.isEmpty(prem_GD0053)) {
                            // 判断保额
                            if (ObjectUtils.isEmpty(amnt_GD0053)
                                    || !EnsureMakeService.isNumber(amnt_GD0053)
                                    || !EnsureMakeService.isAmnt(amnt_GD0053, 9)) {
                                bool = false;
                                planMessage += "民航班机保险金责任为必录责任保额不能为空，且必须是1万~500万的数字，以及仅支持小数点后两位。";
                                return planMessage;
                            }
                            // 判断保费
                            if (ObjectUtils.isEmpty(prem_GD0053) || !EnsureMakeService.isNumber0(prem_GD0053)) {
                                bool = false;
                                planMessage += "民航班机保险金责任为必录责任保费不能为空，且必须是大于等于0的数字，以及仅支持小数点后两位。";
                                return planMessage;
                            }
                        }
                    }
                    // 5、私家车或公务车保险金
                    if (!ObjectUtils.isEmpty(fcPlanRiskDuty_GD0054)) {
                        if (!ObjectUtils.isEmpty(amnt_GD0054) || !ObjectUtils.isEmpty(prem_GD0054)) {
                            // 判断保额
                            if (ObjectUtils.isEmpty(amnt_GD0054)
                                    || !EnsureMakeService.isNumber(amnt_GD0054)
                                    || !EnsureMakeService.isAmnt(amnt_GD0054, 5)) {
                                bool = false;
                                planMessage += "私家车或公务车保险金责任为必录责任保额不能为空，且必须是1万~100万的数字，以及仅支持小数点后两位。";
                                return planMessage;
                            }
                            // 判断保费
                            if (ObjectUtils.isEmpty(prem_GD0054) || !EnsureMakeService.isNumber0(prem_GD0054)) {
                                bool = false;
                                planMessage += "私家车或公务车保险金责任为必录责任保费不能为空, 且必须是大于等于0的数字以及仅支持小数点后两位。";
                                return planMessage;
                            }
                        }
                    }
                    /************************ 可选责任 ***********************************************/
                    // 6、公路公共意外伤害医疗保险金GD0055
//                    if (!ObjectUtils.isEmpty(fcPlanRiskDuty_GD0055)) {
//                        if (!ObjectUtils.isEmpty(amnt_GD0055) || !ObjectUtils.isEmpty(prem_GD0055)) {
//                            // 判断保额
//                            if (ObjectUtils.isEmpty(amnt_GD0055)
//                                    || !ensureMakeService
//                                            .isNumber(amnt_GD0055)
//                                    || (ObjectUtils.isEmpty(amnt_GD0050) && ObjectUtils.isEmpty(prem_GD0050))) {
//                                bool = false;
//                                planMessage += "公路公共意外伤害医疗保险金责任保额不能为空,且必须是大于0的数字，以及仅支持小数点后两位,选择该责任请先录入公路公共交通工具保险金GD0050责任。";
//                                return planMessage;
//                            }
//                            // 判断保费
//                            if (ObjectUtils.isEmpty(prem_GD0055) || !ensureMakeService.isNumber0(prem_GD0055)) {
//                                bool = false;
//                                planMessage += "公路公共意外伤害医疗保险金责任保费不能为空, 且必须是大于等于0的数字以及仅支持小数点后两位。";
//                                return planMessage;
//                            }
//                        }
//                    }
                    // 7、轨道交通意外伤害医疗保险金GD0056
//                    if (!ObjectUtils.isEmpty(fcPlanRiskDuty_GD0056)) {
//                        if (!ObjectUtils.isEmpty(amnt_GD0056) || !ObjectUtils.isEmpty(prem_GD0056)) {
//                            // 判断保额
//                            if (ObjectUtils.isEmpty(amnt_GD0056)
//                                    || !ensureMakeService
//                                            .isNumber(amnt_GD0056)
//                                    || (ObjectUtils.isEmpty(amnt_GD0051) && ObjectUtils.isEmpty(prem_GD0051))) {
//                                bool = false;
//                                planMessage += "轨道交通意外伤害医疗保险金责任保额不能为空,且必须是大于0的数字，以及仅支持小数点后两位,选择该责任请先录入轨道交通工具保险金GD0051责任。";
//                                return planMessage;
//                            }
//                            // 判断保费
//                            if (ObjectUtils.isEmpty(prem_GD0056) || !ensureMakeService.isNumber0(prem_GD0056)) {
//                                bool = false;
//                                planMessage += "轨道交通意外伤害医疗保险金责任保费不能为空,且必须是大于等于0的数字，以及仅支持小数点后两位。";
//                                return planMessage;
//                            }
//                        }
//                    }
                    // 8、水路公共交通意外伤害医疗保险金GD0057
//                    if (!ObjectUtils.isEmpty(fcPlanRiskDuty_GD0057)) {
//                        if (!ObjectUtils.isEmpty(amnt_GD0057) || !ObjectUtils.isEmpty(prem_GD0057)) {
//                            // 判断保额
//                            if (ObjectUtils.isEmpty(amnt_GD0057)
//                                    || !ensureMakeService
//                                            .isNumber(amnt_GD0057)
//                                    || (ObjectUtils.isEmpty(amnt_GD0052) && ObjectUtils.isEmpty(prem_GD0052))) {
//                                bool = false;
//                                planMessage += "水路公共交通意外伤害医疗保险金责任保额不能为空,且必须是大于0的数字，以及仅支持小数点后两位,选择该责任请先录入水路公共交通工具保险金GD0052责任。";
//                                return planMessage;
//                            }
//                            // 判断保费
//                            if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0057.getPrem())
//                                    || !ensureMakeService.isNumber0(String.valueOf(fcPlanRiskDuty_GD0057.getPrem()))) {
//                                bool = false;
//                                planMessage += "水路公共交通意外伤害医疗保险金责任保费不能为空,且必须是大于等于0的数字，以及仅支持小数点后两位。";
//                                return planMessage;
//                            }
//                        }
//                    }
                    // 9、民航班机意外伤害医疗保险金GD0058
//                    if (!ObjectUtils.isEmpty(fcPlanRiskDuty_GD0058)) {
//                        if (!ObjectUtils.isEmpty(amnt_GD0058) || !ObjectUtils.isEmpty(prem_GD0058)) {
//                            // 判断保额
//                            if (ObjectUtils.isEmpty(amnt_GD0058)
//                                    || !ensureMakeService
//                                            .isNumber(amnt_GD0058)
//                                    || (ObjectUtils.isEmpty(amnt_GD0053) && ObjectUtils.isEmpty(prem_GD0053))) {
//                                bool = false;
//                                planMessage += "民航班机意外伤害医疗保险金责任保额不能为空,且必须是大于0的数字，以及仅支持小数点后两位,选择该责任请先录入民航班机保险金GD0053责任。";
//                                return planMessage;
//                            }
//                            // 判断保费
//                            if (ObjectUtils.isEmpty(prem_GD0058) || !ensureMakeService.isNumber0(prem_GD0058)) {
//                                bool = false;
//                                planMessage += "民航班机意外伤害医疗保险金责任保费不能为空,且必须是大于等于0的数字，以及仅支持小数点后两位。";
//                                return planMessage;
//                            }
//                        }
//                    }
                    // 10、私家车或公务车意外伤害医疗保险金GD0059
//                    if (!ObjectUtils.isEmpty(fcPlanRiskDuty_GD0059)) {
//                        if (!ObjectUtils.isEmpty(amnt_GD0059) || !ObjectUtils.isEmpty(prem_GD0059)) {
//                            // 判断保额
//                            if (ObjectUtils.isEmpty(amnt_GD0059)
//                                    || !ensureMakeService
//                                            .isNumber(amnt_GD0059)
//                                    || (ObjectUtils.isEmpty(amnt_GD0054) && ObjectUtils.isEmpty(prem_GD0054))) {
//                                bool = false;
//                                planMessage += "私家车或公务车意外伤害医疗保险金责任保额不能为空,且必须是大于0的数字，以及仅支持小数点后两位,选择该责任请先录入私家车或公务车保险金GD0054责任。";
//                                return planMessage;
//                            }
//                            // 判断保费
//                            if (ObjectUtils.isEmpty(prem_GD0059) || !ensureMakeService.isNumber0(prem_GD0059)) {
//                                bool = false;
//                                planMessage += "私家车或公务车意外伤害医疗保险金责任保费不能为空,且必须是大于等于0的数字，以及仅支持小数点后两位。";
//                                return planMessage;
//                            }
//                        }
//                    }
                    if (bool && stopSaleRiskList.contains(riskCode)) {
                        bool = false;
                        planMessage += "综合交通团体意外伤害保险已下架，暂不支持计划定制。";
                        return planMessage;
                    }
                    if (bool) {
                        dealRow_15070Now(fCPlanRiskDutyList);
                    }
                }
            }
            // 住院团体医疗保险（17010）
            if ("17010".equals(riskCode)) {
                if (!ObjectUtils.isEmpty(fcPlanRiskDuty_GD0018)) {
                    if (!ObjectUtils.isEmpty(fcPlanRiskDuty_GD0018.getAmnt())
                            || !ObjectUtils.isEmpty(fcPlanRiskDuty_GD0018.getPrem())
                            || !ObjectUtils.isEmpty(fcPlanRiskDuty_GD0018.getGetRatio())
                            || !ObjectUtils.isEmpty(fcPlanRiskDuty_GD0018.getGetLimit())) {
                        bool = true;
                    }
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0018.getAmnt())
                            || !EnsureMakeService.isNumber(String.valueOf(fcPlanRiskDuty_GD0018.getAmnt()))
                            || !EnsureMakeService.isAmnt(String.valueOf(fcPlanRiskDuty_GD0018.getAmnt()), 2)) {
                        bool = false;
                        planMessage += "住院团体医疗保险保额不能为空,必须为下拉框指定的值";
                        return planMessage;
                    }
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0018.getPrem())
                            || !EnsureMakeService.isNumber0(String.valueOf(fcPlanRiskDuty_GD0018.getPrem()))) {
                        bool = false;
                        planMessage += "住院团体医疗保险保费不能为空,且必须是大于等于0的数字以及仅支持小数点后两位。";
                        return planMessage;
                    }
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0018.getGetLimit())
                            || !EnsureMakeService.isNumber0(String.valueOf(fcPlanRiskDuty_GD0018.getGetLimit()))
                            || !EnsureMakeService.isDeductibles(String.valueOf(fcPlanRiskDuty_GD0018.getGetLimit()),
                            3)) {
                        bool = false;
                        planMessage += "住院团体医疗保险免赔额不能为空,且只能为下拉框指定的值。";
                        return planMessage;
                    }
                    if (ObjectUtils.isEmpty(fcPlanRiskDuty_GD0018.getGetRatio())
                            || !EnsureMakeService.isNumber1(String.valueOf(fcPlanRiskDuty_GD0018.getGetRatio()))
                            || !EnsureMakeService.isReimbursementRatio(String.valueOf(fcPlanRiskDuty_GD0018.getGetRatio()), 1)) {
                        bool = false;
                        planMessage += "住院团体医疗保险赔付比例不能为空,且只能为下拉框指定的值。";
                        return planMessage;
                    }
                    if (bool && stopSaleRiskList.contains(riskCode)) {
                        bool = false;
                        planMessage += "住院团体医疗保险已下架，暂不支持计划定制。";
                        return planMessage;
                    }
                    if (bool) {
                        dealRow_17010Now(fCPlanRiskDutyList);
                    }
                }
            }
            // 保留两位小数
            BigDecimal b = new BigDecimal(planPrem);
            double totalPlanPrem = b.setScale(2, RoundingMode.HALF_UP).doubleValue();
            plan.setTotalPrem(totalPlanPrem);
            fcEnsurePlanList.add(plan);
            PlanObjectlist.add(plan.getPlanObject());
            //非福利不走
            if (!ensureCode.contains("JHS")) {
                //判断是一年期还是极短期
                String policyEndDate = fcEnsure.getPolicyEndDate();
                String cvaliDate = fcEnsure.getCvaliDate();
                try {
                    Long days = DateTimeUtil.getDistanceDays(policyEndDate, cvaliDate);
                    if (days != 364 && days != 365) {
                        List<String> riskCodeList = new ArrayList<>();
                        riskCodeList.add("12020");
                        riskCodeList.add("17030");
                        riskCodeList.add("16040");
                        riskCodeList.add("16490");
                        riskCodeList.add("17020");
                        riskCodeList.add("17010");
                        riskCodeList.add("17050");
                        if (riskCodeList.contains(riskCode)) {
                            planMessage += "极短期险种导入有误";
                            return planMessage;
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            try {
                // 福利计划层级相关处理
                FCEnsurePlan fcEnsurePlan = fcEnsurePlanList.get(0);
                if (OperatorEnum.ADD.getCode().equals(operating)) {
                    // 判断计划层级是否已经添加
                    Map<String, Object> map = new HashMap();
                    map.put("ensureCode", ensureCode);
                    map.put("planCode", planCode);
                    List<FCEnsurePlan> fcEnsurePlans = fcEnsurePlanMapper.selectAllFCEnsurePlans(map);
                    if (fcEnsurePlans.size() == 0) {
                        fcEnsurePlan.setPlanState(PlanStateEnum.MAKEING.getCode());
                        fcEnsurePlan = CommonUtil.initObject(fcEnsurePlan, "INSERT");
                        fcEnsurePlanList.clear();
                        fcEnsurePlanList.add(fcEnsurePlan);
                        // 新增福利计划
                        fcEnsurePlanMapper.insertList(fcEnsurePlanList);
                    }
                } else if (OperatorEnum.MODIFY.getCode().equals(operating)) {
                    // 更新福利计划
                    fcEnsurePlan.setPlanState(PlanStateEnum.MAKEDONE.getCode());
                    fcEnsurePlan = CommonUtil.initObject(fcEnsurePlan, "UPDATE");
                    fcEnsurePlanMapper.updateByPrimaryKeySelective(fcEnsurePlan);
                }
                // 险种责任层级相关处理
                FCPlanRiskKey fcPlanRiskKey = new FCPlanRiskKey();
                fcPlanRiskKey.setEnsureCode(ensureCode);
                fcPlanRiskKey.setPlanCode(planCode);
                fcPlanRiskKey.setRiskCode(riskCode);
                fcPlanRiskMapper.deleteByPrimaryKey(fcPlanRiskKey);
                fcPlanRiskMapper.insertList(fcPlanRiskList);
                FCPlanRiskDutyKey fcPlanRiskDutyKey = new FCPlanRiskDutyKey();
                fcPlanRiskDutyKey.setEnsureCode(ensureCode);
                fcPlanRiskDutyKey.setPlanCode(planCode);
                fcPlanRiskDutyKey.setRiskCode(riskCode);
                for (FCPlanRiskDuty fcPlanRiskDuty1 : fCPlanRiskDutyList) {
                    fcPlanRiskDutyKey.setDutyCode(fcPlanRiskDuty1.getDutyCode());
                    fcPlanRiskDutyMapper.deleteRiskDutyInfo(fcPlanRiskDutyKey);
                }
                fcPlanRiskDutyMapper.insertList(fcPlanRiskDutyList);
                //重新试算计划保费
                trialPrem(fcEnsurePlanList.get(0), token);
            } catch (Exception e) {
                log.error("添加险种失败:", e);
                throw new RuntimeException();
            }
        } catch (Exception e) {
            log.error("添加险种失败", e);
        }
        return planMessage;
    }

    /**
     * 固定计划处理-意外伤害住院津贴团体医疗保险（15060） order by wuShiHao
     *
     * @param fCPlanRiskDutyList
     * @return
     */
    private boolean dealRow_15060Now(List<FCPlanRiskDuty> fCPlanRiskDutyList) throws Exception {
        FCPlanRiskDuty fcPlanRiskDuty = fCPlanRiskDutyList.get(0);
        // 处理计划险种
        FCPlanRisk risk = new FCPlanRisk();
        risk.setEnsureCode(fcPlanRiskDuty.getEnsureCode());
        risk.setPlanCode(fcPlanRiskDuty.getPlanCode());
        risk.setRiskCode(fcPlanRiskDuty.getRiskCode());
        // 不存RiskName
        risk.setOperator(fcPlanRiskDuty.getOperator());
        risk = CommonUtil.initObject(risk, "INSERT");
        fcPlanRiskList.add(risk);
        for (FCPlanRiskDuty fcPlanRiskDuty1 : fCPlanRiskDutyList) {
            if (!ObjectUtils.isEmpty(fcPlanRiskDuty1.getPrem()) && !ObjectUtils.isEmpty(fcPlanRiskDuty1.getAmnt())) {
                planPrem += fcPlanRiskDuty1.getPrem();
                fcPlanRiskDuty1 = CommonUtil.initObject(fcPlanRiskDuty1, "INSERT");
                fcPlanRiskDutyList.add(fcPlanRiskDuty1);
            }
        }
        return true;
    }

    /**
     * 固定计划处理-住院津贴团体医疗保险（17020） order by wuShiHao
     *
     * @param fCPlanRiskDutyList
     * @return
     */
    private boolean dealRow_17020Now(List<FCPlanRiskDuty> fCPlanRiskDutyList) throws Exception {
        FCPlanRiskDuty fcPlanRiskDuty = fCPlanRiskDutyList.get(0);
        // 处理计划险种
        FCPlanRisk risk = new FCPlanRisk();
        risk.setEnsureCode(fcPlanRiskDuty.getEnsureCode());
        risk.setPlanCode(fcPlanRiskDuty.getPlanCode());
        risk.setRiskCode(fcPlanRiskDuty.getRiskCode());
        // 不存RiskName
        risk.setOperator(fcPlanRiskDuty.getOperator());
        risk = CommonUtil.initObject(risk, "INSERT");
        fcPlanRiskList.add(risk);
        for (FCPlanRiskDuty fcPlanRiskDuty1 : fCPlanRiskDutyList) {
            if (!ObjectUtils.isEmpty(fcPlanRiskDuty1.getPrem()) && !ObjectUtils.isEmpty(fcPlanRiskDuty1.getAmnt())) {
                planPrem += fcPlanRiskDuty1.getPrem();
                fcPlanRiskDuty1 = CommonUtil.initObject(fcPlanRiskDuty1, "INSERT");
                fcPlanRiskDutyList.add(fcPlanRiskDuty1);
            }
        }
        return true;
    }

    /**
     * 固定计划处理-综合交通团体意外伤害保险（15070） order by wuShiHao
     *
     * @param fCPlanRiskDutyList
     * @return
     */
    private boolean dealRow_15070Now(List<FCPlanRiskDuty> fCPlanRiskDutyList) throws Exception {
        FCPlanRiskDuty fcPlanRiskDuty = fCPlanRiskDutyList.get(0);
        // 处理计划险种
        FCPlanRisk risk = new FCPlanRisk();
        risk.setEnsureCode(fcPlanRiskDuty.getEnsureCode());
        risk.setPlanCode(fcPlanRiskDuty.getPlanCode());
        risk.setRiskCode(fcPlanRiskDuty.getRiskCode());
        // 不存RiskName
        risk.setOperator(fcPlanRiskDuty.getOperator());
        risk = CommonUtil.initObject(risk, "INSERT");
        fcPlanRiskList.add(risk);
        //过滤保额为0 的
        fCPlanRiskDutyList = fCPlanRiskDutyList.stream().filter(s -> !s.getAmnt().equals(new Double(0))).collect(Collectors.toList());
        for (FCPlanRiskDuty fcPlanRiskDuty1 : fCPlanRiskDutyList) {
            if (!ObjectUtils.isEmpty(fcPlanRiskDuty1.getPrem()) && !ObjectUtils.isEmpty(fcPlanRiskDuty1.getAmnt())) {
                planPrem += fcPlanRiskDuty1.getPrem();
                Double amnt = fcPlanRiskDuty1.getAmnt();
                fcPlanRiskDuty1.setAmnt(CommonUtil.mul(amnt, 10000.0));
                if (specialDutyList15070.contains(fcPlanRiskDuty1.getDutyCode())) {
                    fcPlanRiskDuty1.setGetLimit(100.0);
                    fcPlanRiskDuty1.setGetLimitType("2");
                }
                fcPlanRiskDuty1 = CommonUtil.initObject(fcPlanRiskDuty1, "INSERT");
                fcPlanRiskDutyList.add(fcPlanRiskDuty1);
            }

        }
        return true;
    }


    /**
     * 新增计划——保障计划配置完成
     *
     * @param token
     * @param fcEnsurePlan
     * @return
     */
    @Transactional
    public String insertEnsurePlan(String token, FCEnsurePlan fcEnsurePlan) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            //校验参数是否为空
            String error = checkDataIsEmpty(fcEnsurePlan);
            if (!"".equals(error)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", error);
                return JSON.toJSONString(resultMap);
            }
            // 计划状态更新为配置完成
            fcEnsurePlan.setPlanState(PlanStateEnum.MAKEDONE.getCode());
            //重新试算计划保费
            trialPrem(fcEnsurePlan, token);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "保障计划配置成功！");
        } catch (Exception e) {
            log.info("新增计划——保障计划配置" + e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "保障计划配置失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 新增计划——责任信息回显
     *
     * @param fcPlanRiskDuty
     * @return
     */
    public String selectRiskDutyAndPlanRisk(FCPlanRiskDuty fcPlanRiskDuty) {
        Map<String, Object> resultMap = new HashMap<>();
        List<FDRiskDutyInfo> fdRiskDutyInfo_other = new ArrayList<>();
        List<FDRiskDutyInfo> fdRiskDutyInfoList_15070 = new ArrayList<>();
        //免赔额集合
        List<String> getLimitList = new ArrayList<>();
        //赔付比例集合
        List<String> getRatioList = new ArrayList<>();
        //赔付属性集合
        List<String> getLimitTypeList = new ArrayList<>();
        //保额集合
        List<String> amntList = new ArrayList<>();
        //获取险种编码
        String riskCode = fcPlanRiskDuty.getRiskCode();
        //个别险种保额展示为万元
        List<String> riskCodeList = new ArrayList<>();
        riskCodeList.add("12020");
        riskCodeList.add("15030");
        riskCodeList.add("17030");
        riskCodeList.add("15040");
        riskCodeList.add("16040");
        riskCodeList.add("16490");
        riskCodeList.add("17010");
        riskCodeList.add("17050");
        riskCodeList.add("15070");


        try {
            fcPlanRiskDutyList.clear();
            //查询险种责任信息
            fcPlanRiskDutyList = fcPlanRiskDutyMapper.slectRiskDutyAndPlanRisk(fcPlanRiskDuty);

            //个别险种保额展示为万元
            for (FCPlanRiskDuty fcPlanRiskDuty1 : fcPlanRiskDutyList) {
                //责任编码
                String dutyCode = fcPlanRiskDuty1.getDutyCode();

                if (riskCodeList.contains(fcPlanRiskDuty1.getRiskCode())) {
                    fcPlanRiskDuty1.setAmnt(CommonUtil.div(fcPlanRiskDuty1.getAmnt(), (double) 10000, 2));
                }
                String getLimitType = fcPlanRiskDuty1.getGetLimitType();
                //免赔额属性转换
                if (!StringUtils.isEmpty(getLimitType)) {
                    fcPlanRiskDuty1.setGetLimitType(getLimitType.equals("1") ? "按次免赔" : "按年免赔");
                }
                //17050险种GD0070责任、GD0071责任需要合并展示故责任名称写死
                // 存储时保额分配给两个险种50%，回显示保额合并乘2回显
                if ("GD0070".equals(dutyCode)) {
                    fcPlanRiskDuty1.setDutyName("一般医疗、恶性肿瘤医疗保险金");
                    fcPlanRiskDuty1.setAmnt(CommonUtil.mul(fcPlanRiskDuty1.getAmnt(), (double) 2));
                }
                //必选责任，回显时责任名称加上（必选责任）
                if (requireddutyList.contains(dutyCode)) {
                    fcPlanRiskDuty1.setDutyName(fcPlanRiskDuty1.getDutyName() + "（必选责任）");
                }
                //可选责任，回显时责任名称加上（可选责任）
                if (optionaldutyList.contains(dutyCode)) {
                    fcPlanRiskDuty1.setDutyName(fcPlanRiskDuty1.getDutyName() + "（可选责任）");
                }
                //如果是15070险种返回责任编码、责任名称
                if ("15070".equals(riskCode)) {
                    fdRiskDutyInfoList_15070 = fdRiskDutyInfoMapper.getRisk_15070ByDutyCode(dutyCode);
                    fcPlanRiskDuty1.setFdRiskDutyInfo(fdRiskDutyInfoList_15070);
                } else {
                    fdRiskDutyInfo_other = fdRiskDutyInfoMapper.selectByRiskCode(riskCode);
                    for (FDRiskDutyInfo fdRiskDutyInfo : fdRiskDutyInfo_other) {
                        // 如果是15040险种，则展示免赔额赔付比例
                        if ("15040".equals(riskCode)) {
                            getLimitList.add("0");
                            getLimitList.add("50");
                            getLimitList.add("100");
                            getRatioList.add("50");
                            getRatioList.add("60");
                            getRatioList.add("70");
                            getRatioList.add("80");
                            getRatioList.add("90");
                            getRatioList.add("100");
                            fcPlanRiskDuty1.setGetLimitList(getLimitList);
                            fcPlanRiskDuty1.setGetRateList(getRatioList);
                        }
                        //17010险种下拉框展示保额、免赔额、赔付比例
                        if ("17010".equals(riskCode)) {
                            amntList.add("0.2");
                            amntList.add("0.3");
                            amntList.add("0.4");
                            amntList.add("0.5");
                            amntList.add("0.6");
                            amntList.add("0.7");
                            amntList.add("0.8");
                            amntList.add("0.9");
                            amntList.add("1");
                            amntList.add("2");
                            amntList.add("3");
                            getLimitList.add("0");
                            getLimitList.add("200");
                            getLimitList.add("300");
                            getLimitList.add("500");
                            getLimitList.add("600");
                            getLimitList.add("700");
                            getLimitList.add("800");
                            getLimitList.add("900");
                            getLimitList.add("1000");
                            getRatioList.add("50");
                            getRatioList.add("60");
                            getRatioList.add("70");
                            getRatioList.add("80");
                            getRatioList.add("90");
                            getRatioList.add("100");
                            fcPlanRiskDuty1.setAmntList(amntList);
                            fcPlanRiskDuty1.setGetLimitList(getLimitList);
                            fcPlanRiskDuty1.setGetRateList(getRatioList);
                        }
                        //17030险种下拉框展免赔额、免赔额属性、赔付比例、保额
                        if ("17030".equals(riskCode)) {
                            getLimitTypeList.add("按次免赔");
                            getLimitTypeList.add("按年免赔");
                            getRatioList.add("50");
                            getRatioList.add("60");
                            getRatioList.add("70");
                            getRatioList.add("80");
                            getRatioList.add("90");
                            getRatioList.add("100");
                            getLimitList.add("0");
                            getLimitList.add("20");
                            getLimitList.add("50");
                            getLimitList.add("80");
                            getLimitList.add("100");
                            getLimitList.add("150");
                            getLimitList.add("200");
                            getLimitList.add("300");
                            getLimitList.add("400");
                            getLimitList.add("500");
                            amntList.add("0.2");
                            amntList.add("0.3");
                            amntList.add("0.4");
                            amntList.add("0.5");
                            amntList.add("0.6");
                            amntList.add("0.7");
                            amntList.add("0.8");
                            amntList.add("0.9");
                            amntList.add("1");
                            amntList.add("2");
                            amntList.add("3");
                            fcPlanRiskDuty1.setGetLimitTypeList(getLimitTypeList);
                            fcPlanRiskDuty1.setGetLimitList(getLimitList);
                            fcPlanRiskDuty1.setGetRateList(getRatioList);
                            fcPlanRiskDuty1.setAmntList(amntList);
                        }
                        //17050险种下拉框展赔付比例、保额
                        if ("17050".equals(riskCode)) {
                            amntList.add("100");
                            amntList.add("200");
                            amntList.add("400");
                            getRatioList.add("60");
                            getRatioList.add("70");
                            getRatioList.add("80");
                            getRatioList.add("90");
                            getRatioList.add("100");
                            fcPlanRiskDuty1.setAmntList(amntList);
                            fcPlanRiskDuty1.setGetRateList(getRatioList);
                        }
                    }
                }
            }
            resultMap.put("data", fcPlanRiskDutyList);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "责任信息回显成功！");
        } catch (Exception e) {
            log.info("新增计划——险种信息回显" + e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "责任信息回显失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 新增计划——险种信息回显
     *
     * @param fcPlanRiskDuty
     * @return
     */
    public String selectPlanRisk(FCPlanRiskDuty fcPlanRiskDuty) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            fcPlanRiskDutyList.clear();
            //查询险种责任信息
            fcPlanRiskDutyList = fcPlanRiskDutyMapper.slectRiskDutyAndEnsurePlan(fcPlanRiskDuty);
            for (FCPlanRiskDuty fcPlanRiskDuty1 : fcPlanRiskDutyList) {
                //责任编码
                String dutyCode = fcPlanRiskDuty1.getDutyCode();
                //17050险种信息回显需要合并
                if ("GD0070".equals(dutyCode)) {
                    fcPlanRiskDuty1.setDutyName("一般医疗、恶性肿瘤医疗保险金");
                    fcPlanRiskDuty1.setAmnt(CommonUtil.mul(fcPlanRiskDuty1.getAmnt(), (double) 2));
                }
                //由于15070险种需要两两合并展示，故添加标识logo来判断
                if ("GD0050".equals(dutyCode) || "GD0055".equals(dutyCode)) {
                    fcPlanRiskDuty1.setLogo("GD0050");
                } else if ("GD0051".equals(dutyCode) || "GD0056".equals(dutyCode)) {
                    fcPlanRiskDuty1.setLogo("GD0051");
                } else if ("GD0052".equals(dutyCode) || "GD0057".equals(dutyCode)) {
                    fcPlanRiskDuty1.setLogo("GD0052");
                } else if ("GD0053".equals(dutyCode) || "GD0058".equals(dutyCode)) {
                    fcPlanRiskDuty1.setLogo("GD0053");
                } else if ("GD0054".equals(dutyCode) || "GD0059".equals(dutyCode)) {
                    fcPlanRiskDuty1.setLogo("GD0054");
                } else {
                    // 为了排序
                    fcPlanRiskDuty1.setLogo(dutyCode);
                }
                String planObject = fcPlanRiskDuty1.getPlanObject();
                planObject = fdCodeMapper.selectNameByCode("PlanObject", planObject);
                fcPlanRiskDuty1.setPlanObject(planObject);
            }
            // 根据logo合并项排序
            fcPlanRiskDutyList.sort(Comparator.comparing(FCPlanRiskDuty::getLogo));
            resultMap.put("data", fcPlanRiskDutyList);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "险种信息回显成功！");
        } catch (Exception e) {
            e.printStackTrace();
            log.info("新增计划——险种信息回显" + e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "险种信息回显失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    public String checkDataIsEmpty(FCEnsurePlan fcEnsurePlan) {
        //校验数据是否为空
        String error = "";
        //福利编码
        if (StringUtils.isEmpty(fcEnsurePlan.getEnsureCode())) {
            error = "福利编码不能为空";
            return error;
        }
        //计划编码
        if (StringUtils.isEmpty(fcEnsurePlan.getPlanCode())) {
            error = "计划编码不能为空";
            return error;
        }
        //计划名称
        if (StringUtils.isEmpty(fcEnsurePlan.getPlanName())) {
            error = "计划名称不能为空";
            return error;
        }
        //计划对象
        if (StringUtils.isEmpty(fcEnsurePlan.getPlanObject())) {
            error = "计划对象不能为空";
            return error;
        }
        //计划重点
        if (StringUtils.isEmpty(fcEnsurePlan.getPlanKey())) {
            error = "计划重点不能为空";
            return error;
        }
        if (OperatorEnum.ADD.getCode().equals(fcEnsurePlan.getOperating())) {
            ArrayList<FCEnsurePlan> fcEnsurePlantemp = fcEnsurePlanMapper.selectByEnsureCode(fcEnsurePlan.getEnsureCode());
            List<String> planCodeListTemp = new ArrayList<>();
            for (FCEnsurePlan fcEnsurePlan1 : fcEnsurePlantemp) {
                planCodeListTemp.add(fcEnsurePlan1.getPlanCode());
            }
            if (planCodeListTemp.contains(fcEnsurePlan.getPlanCode())) {
                error = "已存在相同的计划编码，请修改！";
                return error;
            }
        }
        List<Map<String, String>> list = fcPlanRiskDutyMapper.selectDutyListByPlanCode(fcEnsurePlan.getEnsureCode(), fcEnsurePlan.getPlanCode());
        if (list.size() == 0) {
            error = "险种配置失败：请至少配置一个险种责任";
            return error;
        }
        return error;
    }


    /**
     * 以险种为单位处理数据
     *
     * @param ensureCode
     * @param riskCode
     * @param dutyCode
     * @param operator
     * @param isMedical
     * @param amnt
     * @param prem
     * @param getLimit
     * @param getLimitType
     * @param getRatio
     * @return
     */
    private boolean dealRow(String ensureCode, String riskCode, String dutyCode,
                            String operator, String isMedical,
                            String amnt, String prem, String getLimit,
                            String getLimitType, String getRatio, String planCode) throws Exception {
        FCPlanRisk risk = new FCPlanRisk();
        risk.setEnsureCode(ensureCode);
        risk.setPlanCode(planCode);
        risk.setRiskCode(riskCode);
        risk.setOperator(operator);
        risk = (FCPlanRisk) CommonUtil.initObject(risk, "INSERT");
        fcPlanRiskList.add(risk);
        FCPlanRiskDuty duty = new FCPlanRiskDuty();
        duty.setDutyCode(dutyCode);
        duty.setEnsureCode(ensureCode);
        duty.setRiskCode(riskCode);
        duty.setPlanCode(planCode);
//      应横琴需求，所有保额皆为万元   update  by  wudezhong
        duty.setAmnt(CommonUtil.mul(Double.parseDouble(amnt), 10000.0));
        duty.setPrem(Double.parseDouble(prem));
        planPrem += duty.getPrem();
        if ("Y".equals(isMedical)) {
            duty.setGetLimit(Double.parseDouble(getLimit));
            String GetLimitType;
            if ("17030".equals(riskCode)) {
                duty.setGetLimitType(getLimitType.equals("按次免赔") ? "1" : "2");
                duty.setGetRatio(CommonUtil.div(Double.parseDouble(getRatio), (double) 100, 2));
            } else if ("15040".equals(riskCode) || "17010".equals(riskCode)) {
                GetLimitType = "1";
                duty.setGetLimitType(GetLimitType);
                duty.setGetRatio(CommonUtil.div(Double.parseDouble(getRatio), (double) 100, 2));
            } else {
                duty.setGetRatio(CommonUtil.div(Double.parseDouble(getRatio), (double) 100, 2));
            }
        }
        Double ss = duty.getGetRatio();
        duty.setOperator(operator);
        duty = (FCPlanRiskDuty) CommonUtil.initObject(duty, "INSERT");
        fcPlanRiskDutyList.add(duty);
        return true;
    }

    /**
     * 处理险种-尊享团体补充医疗保险（17050）
     *
     * @param ensureCode
     * @param planCode
     * @param riskCode
     * @param operator
     * @param amnt
     * @param prem
     * @param getLimit
     * @param getRatio
     * @param planCode
     * @return
     */
    private boolean dealRow_17050(String ensureCode, String riskCode, String operator,
                                  String amnt, String prem, String getLimit,
                                  String getRatio, String planCode) throws Exception {
        FCPlanRisk risk = new FCPlanRisk();
        risk.setEnsureCode(ensureCode);
        risk.setPlanCode(planCode);
        risk.setRiskCode(riskCode);
        // 不存RiskName
        risk.setOperator(operator);
        risk = (FCPlanRisk) CommonUtil.initObject(risk, "INSERT");
        fcPlanRiskList.add(risk);


        FCPlanRiskDuty duty1 = new FCPlanRiskDuty();
        // 一般医疗保险金GD0070
        duty1.setDutyCode("GD0070");
        duty1.setEnsureCode(ensureCode);
        duty1.setRiskCode(riskCode);
        duty1.setPlanCode(planCode);
        duty1.setAmnt(CommonUtil.mul(CommonUtil.mul(Double.parseDouble(amnt), 10000.0), 0.5));
        duty1.setPrem(Double.parseDouble(prem));
        planPrem += duty1.getPrem();
        duty1.setGetLimit(Double.parseDouble(getLimit));
        duty1.setGetLimitType("2");
        duty1.setGetRatio(CommonUtil.div(Double.parseDouble(getRatio), (double) 100, 2));
        duty1.setOperator(operator);
        duty1 = (FCPlanRiskDuty) CommonUtil.initObject(duty1, "INSERT");
        fcPlanRiskDutyList.add(duty1);
        // 恶性肿瘤医疗保险金GD0071
        FCPlanRiskDuty duty2 = new FCPlanRiskDuty();
        duty2.setEnsureCode(ensureCode);
        duty2.setRiskCode(riskCode);
        duty2.setPlanCode(planCode);
        duty2.setDutyCode("GD0071");
        duty2.setAmnt(CommonUtil.mul(CommonUtil.mul(Double.parseDouble(amnt), 10000.0), 0.5));
        // -----应横琴核心要求，只获取第一列的责任的免赔额和赔付比例,保费置为0
        duty2.setPrem(0.0);
		/*duty2.setGetLimit(Double.parseDouble(ExcelUtil.getCellValue(row
				.getCell(startCell + 2))));*/
        duty2.setGetLimit(0.0);
        duty2.setGetLimitType("");
        duty2.setGetRatio(CommonUtil.div(Double.parseDouble(getRatio), (double) 100, 2));
        duty2.setOperator(operator);
        duty2 = (FCPlanRiskDuty) CommonUtil.initObject(duty2, "INSERT");
        fcPlanRiskDutyList.add(duty2);
        return true;
    }

    /**
     * 固定计划处理-住院团体医疗保险（17010） order by wuShiHao
     *
     * @param fCPlanRiskDutyList
     * @return
     */
    private boolean dealRow_17010Now(List<FCPlanRiskDuty> fCPlanRiskDutyList) throws Exception {
        FCPlanRiskDuty fcPlanRiskDuty = fCPlanRiskDutyList.get(0);
        // 处理计划险种
        FCPlanRisk risk = new FCPlanRisk();
        risk.setEnsureCode(fcPlanRiskDuty.getEnsureCode());
        risk.setPlanCode(fcPlanRiskDuty.getPlanCode());
        risk.setRiskCode(fcPlanRiskDuty.getRiskCode());
        // 不存RiskName
        risk.setOperator(fcPlanRiskDuty.getOperator());
        risk = CommonUtil.initObject(risk, "INSERT");
        fcPlanRiskList.add(risk);
        for (FCPlanRiskDuty fcPlanRiskDuty1 : fCPlanRiskDutyList) {
            planPrem += fcPlanRiskDuty1.getPrem();
            Double amnt = fcPlanRiskDuty1.getAmnt();
            fcPlanRiskDuty1.setAmnt(CommonUtil.mul(amnt, 10000.0));
            fcPlanRiskDuty1.setGetLimitType("1");
            fcPlanRiskDuty1.setGetRatio(CommonUtil.div(fcPlanRiskDuty1.getGetRatio(), (double) 100, 2));
            fcPlanRiskDuty1 = CommonUtil.initObject(fcPlanRiskDuty1, "INSERT");
            fcPlanRiskDutyList.add(fcPlanRiskDuty1);
        }
        return true;
    }

    /**
     * 固定计划--计划新增查询码表 order By wuShiHao
     *
     * @param fdCode
     * @return
     */
    public String planFindCode(FDCode fdCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (!fdCode.getCodeType().matches("[0-9A-Za-z_]*")) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "查询失败");
            } else {
                // 查询数据库中codeType字段下、符合otherSign条件的数据
                List<HashMap<String, Object>> codeInfoList = fdCodeMapper.findCodeInfoNew(fdCode.getCodeType(),
                        fdCode.getOtherSign());
                resultMap.put("codeList", codeInfoList);
                resultMap.put("code", "200");
                resultMap.put("success", true);
                resultMap.put("message", "查询成功");
            }
        } catch (Exception e) {
            log.info("查询失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询失败");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 固定计划--根据险种编码获取必选责任名称 order By wuShiHao
     *
     * @param fcPlanRiskDuty
     * @return
     */
    public String selectDutyNameByRiskCode(FCPlanRiskDuty fcPlanRiskDuty) {
        Map<String, Object> resultMap = new HashMap<>();
        List<FDRiskDutyInfo> fdRiskDutyInfo_other = new ArrayList<>();
        List<FDRiskDutyInfo> fdRiskDutyInfoList_15070 = new ArrayList<>();
        List<FDRiskDutyInfo> result = new ArrayList<>();
        String ensureCode = fcPlanRiskDuty.getEnsureCode();
        String planCode = "";
        if (!ObjectUtils.isEmpty(fcPlanRiskDuty.getPlanCode())) {
            planCode = fcPlanRiskDuty.getPlanCode();
        }
        String riskCode = fcPlanRiskDuty.getRiskCode();
        try {
            if ("15070".equals(riskCode)) {
                fdRiskDutyInfoList_15070 = fdRiskDutyInfoMapper.getRisk_15070ByEnsureCodeAndPlanCode(ensureCode,
                        planCode);
                for (FDRiskDutyInfo fdRiskDutyInfo : fdRiskDutyInfoList_15070) {
                    if ("GD0050".equals(fdRiskDutyInfo.getDutyCode())) {
                        fdRiskDutyInfo.setDutyName("公路公共交通工具");
                    }
                    if ("GD0051".equals(fdRiskDutyInfo.getDutyCode())) {
                        fdRiskDutyInfo.setDutyName("轨道交通工具");
                    }
                    if ("GD0052".equals(fdRiskDutyInfo.getDutyCode())) {
                        fdRiskDutyInfo.setDutyName("水路公共交通工具");
                    }
                    if ("GD0053".equals(fdRiskDutyInfo.getDutyCode())) {
                        fdRiskDutyInfo.setDutyName("民航班机");
                    }
                    if ("GD0054".equals(fdRiskDutyInfo.getDutyCode())) {
                        fdRiskDutyInfo.setDutyName("私家车或公务车");
                    }
                }
                result.addAll(fdRiskDutyInfoList_15070);
            } else {
                fdRiskDutyInfo_other = fdRiskDutyInfoMapper.selectByRiskCode(riskCode);
                result.add(fdRiskDutyInfo_other.get(0));
            }
            resultMap.put("data", result);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "查询成功");
        } catch (Exception e) {
            log.info("查询失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询失败");
        }
        return JSON.toJSONString(resultMap);

    }

    /**
     * 固定计划--根据险种编码获取责任信息 order By wuShiHao
     *
     * @param fcPlanRiskDuty
     * @return
     */
    public String selectDutyInfo(FCPlanRiskDuty fcPlanRiskDuty) {
        Map<String, Object> resultMap = new HashMap<>();
        // 其他险种返回的责任信息
        List<FDRiskDutyInfo> fdRiskDutyInfo_other = new ArrayList<>();
        // 15070险种返回的责任信息
        List<FDRiskDutyInfo> fdRiskDutyInfoList_15070 = new ArrayList<>();
        List<FDRiskDutyInfo> fdRiskDutyInfoList = new ArrayList<>();
        // 免赔额集合
        List<String> getLimitList = new ArrayList<>();
        // 赔付比例集合
        List<String> getRatioList = new ArrayList<>();
        // 赔付属性集合
        List<String> getLimitTypeList = new ArrayList<>();
        // 保额集合
        List<String> amntList = new ArrayList<>();
        String riskCode = fcPlanRiskDuty.getRiskCode();
        try {
            // 如果是15070险种的话
            if ("15070".equals(riskCode)) {
                String dutyCode = fcPlanRiskDuty.getDutyCode();
                // 15070险种之间有连带关系 如：GD0050和GD0055是一起展示到前台的
                fdRiskDutyInfoList_15070 = fdRiskDutyInfoMapper.getRisk_15070ByDutyCode(dutyCode);
                for (FDRiskDutyInfo fdRiskDutyInfo : fdRiskDutyInfoList_15070) {
                    fdRiskDutyInfoList.add(fdRiskDutyInfo);
                }
                // 除15070外的险种
            } else {
                fdRiskDutyInfo_other = fdRiskDutyInfoMapper.selectByRiskCode(riskCode);
                FDRiskDutyInfo fdRiskDutyInfo1 = fdRiskDutyInfo_other.get(0);
                // 17050险种数据库中责任为两条记录，但前台只展示一条GD0070，故显示时需要将GD0071删除
                if ("17050".equals(fdRiskDutyInfo1.getRiskCode())) {
                    fdRiskDutyInfo_other.remove(1);
                }
                for (FDRiskDutyInfo fdRiskDutyInfo : fdRiskDutyInfo_other) {
                    // 如果是15040险种，则展示免赔额赔付比例
                    if ("15040".equals(riskCode)) {
                        getLimitList.add("0");
                        getLimitList.add("50");
                        getLimitList.add("100");
                        getRatioList.add("50");
                        getRatioList.add("60");
                        getRatioList.add("70");
                        getRatioList.add("80");
                        getRatioList.add("90");
                        getRatioList.add("100");
                        fdRiskDutyInfo.setGetLimitList(getLimitList);
                        fdRiskDutyInfo.setGetRateList(getRatioList);
                    }
                    if ("17010".equals(riskCode)) {
                        amntList.add("0.2");
                        amntList.add("0.3");
                        amntList.add("0.4");
                        amntList.add("0.5");
                        amntList.add("0.6");
                        amntList.add("0.7");
                        amntList.add("0.8");
                        amntList.add("0.9");
                        amntList.add("1");
                        amntList.add("2");
                        amntList.add("3");
                        getLimitList.add("0");
                        getLimitList.add("200");
                        getLimitList.add("300");
                        getLimitList.add("500");
                        getLimitList.add("600");
                        getLimitList.add("700");
                        getLimitList.add("800");
                        getLimitList.add("900");
                        getLimitList.add("1000");
                        getRatioList.add("50");
                        getRatioList.add("60");
                        getRatioList.add("70");
                        getRatioList.add("80");
                        getRatioList.add("90");
                        getRatioList.add("100");
                        fdRiskDutyInfo.setAmntList(amntList);
                        fdRiskDutyInfo.setGetLimitList(getLimitList);
                        fdRiskDutyInfo.setGetRateList(getRatioList);
                    }
                    if ("17030".equals(riskCode)) {
                        getLimitTypeList.add("按次免赔");
                        getLimitTypeList.add("按年免赔");
                        getRatioList.add("50");
                        getRatioList.add("60");
                        getRatioList.add("70");
                        getRatioList.add("80");
                        getRatioList.add("90");
                        getRatioList.add("100");
                        getLimitList.add("0");
                        getLimitList.add("20");
                        getLimitList.add("50");
                        getLimitList.add("80");
                        getLimitList.add("100");
                        getLimitList.add("150");
                        getLimitList.add("200");
                        getLimitList.add("300");
                        getLimitList.add("400");
                        getLimitList.add("500");
                        amntList.add("0.2");
                        amntList.add("0.3");
                        amntList.add("0.4");
                        amntList.add("0.5");
                        amntList.add("0.6");
                        amntList.add("0.7");
                        amntList.add("0.8");
                        amntList.add("0.9");
                        amntList.add("1");
                        amntList.add("2");
                        amntList.add("3");
                        fdRiskDutyInfo.setGetLimitTypeList(getLimitTypeList);
                        fdRiskDutyInfo.setGetLimitList(getLimitList);
                        fdRiskDutyInfo.setGetRateList(getRatioList);
                        fdRiskDutyInfo.setAmntList(amntList);
                    }
                    if ("17050".equals(riskCode)) {
                        amntList.add("100");
                        amntList.add("200");
                        amntList.add("400");
                        getRatioList.add("60");
                        getRatioList.add("70");
                        getRatioList.add("80");
                        getRatioList.add("90");
                        getRatioList.add("100");
                        fdRiskDutyInfo.setAmntList(amntList);
                        fdRiskDutyInfo.setGetRateList(getRatioList);
                        fdRiskDutyInfo.setDutyName("一般医疗、恶性肿瘤医疗保险金");
                    }
                    fdRiskDutyInfoList.add(fdRiskDutyInfo);
                }
            }
            // 对返回的险种结果进行处理（加上必选或可选责任）
            for (FDRiskDutyInfo fdRiskDutyInfo : fdRiskDutyInfoList) {
                if (requireddutyList.contains(fdRiskDutyInfo.getDutyCode())) {
                    fdRiskDutyInfo.setDutyName(fdRiskDutyInfo.getDutyName() + "（必选责任）");
                }
                if (optionaldutyList.contains(fdRiskDutyInfo.getDutyCode())) {
                    fdRiskDutyInfo.setDutyName(fdRiskDutyInfo.getDutyName() + "（可选责任）");
                }
            }
            resultMap.put("data", fdRiskDutyInfoList);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "查询成功");
        } catch (Exception e) {
            log.info("查询失败", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询失败");
        }
        return JSON.toJSONString(resultMap);

    }

    /**
     * 获取当前计划未选择险种列表
     *
     * @param fcPlanRiskDuty
     * @return
     */
    public String getRiskInfo(FCPlanRiskDuty fcPlanRiskDuty) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            List<String> planCodeList = new ArrayList<>();
            String ensureCode = fcPlanRiskDuty.getEnsureCode();
            String planCode = "";
            if (!ObjectUtils.isEmpty(fcPlanRiskDuty.getPlanCode())) {
                planCode = fcPlanRiskDuty.getPlanCode();
            }
            /**
             * 计划相关校验
             */
            String operating = fcPlanRiskDuty.getOperating();
            if (OperatorEnum.ADD.getCode().equals(operating)) {
                ArrayList<FCEnsurePlan> fcEnsurePlans = fcEnsurePlanMapper.selectByEnsureCode(ensureCode);
                for (FCEnsurePlan fcEnsurePlan : fcEnsurePlans) {
                    planCodeList.add(fcEnsurePlan.getPlanCode());
                }
                if (planCodeList.contains(planCode)) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "已存在相同的计划编码，请修改");
                    return JSON.toJSONString(resultMap);
                }
            }
            // 判断计划编码、计划名称、计划对象、计划重点是否录入
            if (ObjectUtils.isEmpty(fcPlanRiskDuty.getPlanCode()) || ObjectUtils.isEmpty(fcPlanRiskDuty.getPlanName())
                    || ObjectUtils.isEmpty(fcPlanRiskDuty.getPlanObject())
                    || ObjectUtils.isEmpty(fcPlanRiskDuty.getPlanKey())) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "请完善计划编码、计划名称、计划对象、计划重点信息！");
                return JSON.toJSONString(resultMap);
            }
            // 极短期险种列表
            List<String> noShortRiskCodeList = new ArrayList<>();
            // 判断是一年期还是极短期
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            String policyEndDate = fcEnsure.getPolicyEndDate();
            String cvaliDate = fcEnsure.getCvaliDate();
            try {
                Long days = DateTimeUtil.getDistanceDays(policyEndDate, cvaliDate);
                if (days != 364) {
                    noShortRiskCodeList.add("15030");
                    noShortRiskCodeList.add("15040");
                    noShortRiskCodeList.add("15060");
                    noShortRiskCodeList.add("15070");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            /**
             * 获取相关险种责任列表
             */
            List<FDRiskInfo> fdRiskInfoList = fdRiskInfoMapper.selectByEnsureCodeAndPlanCode(ensureCode, planCode,
                    noShortRiskCodeList);
            List<FDRiskDutyInfo> fdRiskDutyInfoList = new ArrayList<>();
            for (FDRiskInfo riskInfo : fdRiskInfoList) {
                riskInfo.setRiskName(riskInfo.getRiskCode() + "-" + riskInfo.getRiskName());
                if ("15070".equals(riskInfo.getRiskCode())) {
                    fdRiskDutyInfoList = fdRiskDutyInfoMapper.getRisk_15070ByEnsureCodeAndPlanCode(ensureCode,
                            planCode);
                    for (FDRiskDutyInfo fdRiskDutyInfo : fdRiskDutyInfoList) {
                        fdRiskDutyInfo.setRiskName(riskInfo.getRiskName());
                    }
                }
            }
            resultMap.put("data", fdRiskInfoList);
            resultMap.put("_15070DutyList", fdRiskDutyInfoList);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "未添加险种列表查询成功！");
        } catch (Exception e) {
            log.info(e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "已添加险种列表信息查询失败！");
        } finally {
            return JSON.toJSONString(resultMap);
        }
    }

    /**
     * 重新试算接口
     *
     * @param fcEnsurePlan
     * @param token
     */
    public void trialPrem(FCEnsurePlan fcEnsurePlan, String token) {
        GlobalInput globalInput = userService.getSession(token);
        //总保费
        double totalPrem = fcPlanRiskDutyMapper.selectTotalPremPlan(fcEnsurePlan.getEnsureCode(), fcEnsurePlan.getPlanCode());
        fcEnsurePlan.setInsuredNumber(0);
        fcEnsurePlan.setTotalPrem(totalPrem);
        fcEnsurePlan.setOperator(globalInput.getUserNo());
        CommonUtil.initObject(fcEnsurePlan, "UPDATE");
        fcEnsurePlanMapper.updateByPrimaryKeySelective(fcEnsurePlan);
    }

    /**
     * 删除福利下定制中的险种信息
     *
     * @param ensureCode
     * @return
     */
    @Transactional
    public String deleteMakeingPlanRiskInfo(String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", true);
        resultMap.put("code", "200");
        // 校验数据
        if (StringUtils.isBlank(ensureCode)) {
            resultMap.put("message", "福利编码不能为空！");
            resultMap.put("success", false);
            resultMap.put("code", "500");
        } else {
            // 删除福利下配置中的定制中的计划和责任信息
            int riskDutyCount = fcPlanRiskDutyMapper.deleteMakeingRiskDutyInfoByEnsureCode(ensureCode);
            int riskCount = fcPlanRiskMapper.deleteMakeingPlanRiskByEnsureCode(ensureCode);
            int planCount = fcEnsurePlanMapper.deleteMakeingPlanByEnsureCode(ensureCode);
            resultMap.put("message",
                    "福利（" + ensureCode + "）下当前有计划：" + planCount + "条，" + "险种:" + riskCount + "条，责任：" + riskDutyCount
                            + "条被删除。");
        }
        return JSONObject.toJSONString(resultMap);
    }

}
