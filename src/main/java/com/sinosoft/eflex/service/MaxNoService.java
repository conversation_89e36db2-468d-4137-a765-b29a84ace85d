package com.sinosoft.eflex.service;

import ch.qos.logback.classic.Logger;
import com.sinosoft.eflex.util.RedisUtil;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 生成最大号
 *
 * <AUTHOR>
 */
@Service("MaxNoService")
@SuppressWarnings("unused")
public class MaxNoService {

    // 日志
    private static Logger Log = (Logger) LoggerFactory.getLogger(MaxNoService.class);


    @Autowired
    private RedisUtil redisUtil;

    /**
     * 生成最大号
     *
     * @param cNoType   最大号类型
     * @param cNoLimit  限制条件
     * @param cNoLength 最大号长度
     * @return
     */
    public String createMaxNo(String cNoType, String cNoLimit, int cNoLength) {

        try {
            //传入的参数不能为空，如果为空，则直接返回
            if ((cNoType == null) || (cNoType.trim().length() <= 0)) {
                Log.info("NoType长度错误或者NoType为空");

                return null;
            }
            if ((cNoLimit == null) || (cNoLimit.trim().length() <= 0)) {
                cNoLimit = "SN";
            }
            if (cNoLength < 1) {
                //默认流水号位数
                cNoLength = 10;
            }
            cNoType = cNoType.toUpperCase();
            cNoLimit = cNoLimit.toUpperCase();

            Date date = null;
            Long id = null;
            //是否根据日期递增，目前不需要
            if (false) {
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                calendar.set(Calendar.MILLISECOND, 999);
                date = calendar.getTime();
            }
            Log.info("createMaxNo生产,key:{}", cNoType + "_" + cNoLimit);
            id = redisUtil.generateId(cNoType + "_" + cNoLimit, date);
            Log.info("createMaxNo生产,id:{}", id);
            if (id != null) {
                String resId = this.format(id, cNoLimit, date, cNoLength);
                Log.info("createMaxNo生产结果:{}", resId);
                return resId;
            }
        } catch (Exception e) {
            Log.error("createMaxNo生产异常！", e);
        }
        Log.info("createMaxNo生产结果为空");
        return null;
    }


    private String format(Long id, String prefix, Date date, Integer minLength) {
        StringBuffer sb = new StringBuffer();
        if (!prefix.equals("SN")) {
            sb.append(prefix);
        }
        if (date != null) {
            DateFormat df = new SimpleDateFormat("yyyyMMdd");
            sb.append(df.format(date));
        }
        String strId = String.valueOf(id);
        int length = strId.length();
        if (length < minLength) {
            for (int i = 0; i < minLength - length; i++) {
                sb.append("0");
            }
            sb.append(strId);
        } else {
            sb.append(strId);
        }
        return sb.toString();
    }

}
