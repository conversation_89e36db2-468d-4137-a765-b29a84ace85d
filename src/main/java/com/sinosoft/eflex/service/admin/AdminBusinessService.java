package com.sinosoft.eflex.service.admin;

import com.sinosoft.eflex.model.request.GrpContactInsertReq;
import com.sinosoft.eflex.model.request.GrpContactReq;
import com.sinosoft.eflex.model.vo.GrpContactInfoVO;

import java.util.List;

/**
 * 后台管理接口 优化 BusInessAdminService**
 *
 * <AUTHOR>
 */
public interface AdminBusinessService {

    /**
     * 查询企业经办人信息*
     *
     * @param token 缓存
     * @param grpNo 企业编号
     * @return 结果
     */
    List<GrpContactInfoVO> queryGrpContact(String token, String grpNo);


    /**
     * 设置默认企业经办人*
     *
     * @param token         缓存
     * @param grpContactReq 企业经办人信息
     */
    void acquiesceContact(String token, GrpContactReq grpContactReq);


    /**
     * 新增企业经办人信息*
     *
     * @param token 缓存
     * @param req   请求信息
     */
    void insertContact(String token, GrpContactInsertReq req);

}
