package com.sinosoft.eflex.service.admin.impl;

import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.dao.FCGrpInfoMapper;
import com.sinosoft.eflex.dao.FDFTPInfoMapper;
import com.sinosoft.eflex.dao.FcGrpContactMapper;
import com.sinosoft.eflex.dao.FcHrRegistTempMapper;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.util.FileUtil;
import com.sinosoft.eflex.util.SFtpClientUtil;
import com.sinosoft.eflex.util.UUIDUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 异步处理图片*
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AsyncImageChangeService {

    /**
     * 获取ftp文件访问路径
     */
    @Value("${fileDisplay.path}")
    private String fileDisplayPath;

    private final FcGrpContactMapper fcGrpContactMapper;
    private final FCGrpInfoMapper fcGrpInfoMapper;
    private final FDFTPInfoMapper fdftpInfoMapper;
    private final FcHrRegistTempMapper fcHrRegistTempMapper;


    @Async("eFlexExecutor-2")
    public void fcGrpContactUpload(FcGrpContact fcGrpContact) {
        //查询ftp信息上传到SFTP
        FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
        //获取文件上传相对路径  0305-企业注册文件夹
        String ftpFilePath = fdftpInfo.getFtprootpath() + FileUtil.getFtpPath("0305", "");
        SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(), fdftpInfo.getUsername(), fdftpInfo.getPassword());

        FcGrpContact fcGrpContactNew = new FcGrpContact();
        if (StringUtils.isNotEmpty(fcGrpContact.getIdCardFront())) {
            fcGrpContactNew.setIdImage1(uploadSftp(sFtp, ftpFilePath, fcGrpContact.getIdCardFront()));
        }
        if (StringUtils.isNotEmpty(fcGrpContact.getIdCardBack())) {
            fcGrpContactNew.setIdImage2(uploadSftp(sFtp, ftpFilePath, fcGrpContact.getIdCardBack()));
        }
        if (StringUtils.isNotEmpty(fcGrpContactNew.getIdImage1()) || StringUtils.isNotEmpty(fcGrpContactNew.getIdImage2())) {
            fcGrpContactNew.setContactNo(fcGrpContact.getContactNo());
            log.info("fcGrpInfoUpload request:{}", JsonUtil.toJSON(fcGrpContactNew));
            fcGrpContactMapper.updateByPrimaryKeySelective(fcGrpContactNew);
        }
    }


    @Async("eFlexExecutor-2")
    public void fcGrpInfoUpload(FCGrpInfo fcGrpInfo) {
        //查询ftp信息上传到SFTP
        FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
        //获取文件上传相对路径  0305-企业注册文件夹
        String ftpFilePath = fdftpInfo.getFtprootpath() + FileUtil.getFtpPath("0305", "");
        SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(), fdftpInfo.getUsername(), fdftpInfo.getPassword());

        FCGrpInfo fcGrpInfoNew = new FCGrpInfo();
        if (StringUtils.isNotEmpty(fcGrpInfo.getGrpImageFront())) {
            fcGrpInfoNew.setGrpIDImage1(uploadSftp(sFtp, ftpFilePath, fcGrpInfo.getGrpImageFront()));
        }
        if (StringUtils.isNotEmpty(fcGrpInfo.getGrpImageBack())) {
            fcGrpInfoNew.setGrpIDImage2(uploadSftp(sFtp, ftpFilePath, fcGrpInfo.getGrpImageBack()));
        }
        if (StringUtils.isNotEmpty(fcGrpInfo.getLegalImgFront())) {
            fcGrpInfoNew.setLegIDImage1(uploadSftp(sFtp, ftpFilePath, fcGrpInfo.getLegalImgFront()));
        }
        if (StringUtils.isNotEmpty(fcGrpInfo.getLegalImgBack())) {
            fcGrpInfoNew.setLegIDImage2(uploadSftp(sFtp, ftpFilePath, fcGrpInfo.getLegalImgBack()));
        }
        if (StringUtils.isNotEmpty(fcGrpInfoNew.getGrpIDImage1()) || StringUtils.isNotEmpty(fcGrpInfoNew.getGrpIDImage2())
                || StringUtils.isNotEmpty(fcGrpInfoNew.getLegIDImage1()) || StringUtils.isNotEmpty(fcGrpInfoNew.getLegIDImage2())) {
            fcGrpInfoNew.setGrpNo(fcGrpInfo.getGrpNo());
            log.info("fcGrpInfoUpload request:{}", JsonUtil.toJSON(fcGrpInfoNew));
            fcGrpInfoMapper.updateByPrimaryKeySelective(fcGrpInfoNew);
        }
    }


    @Async("eFlexExecutor-2")
    public void fcHrRegisterUpload(HrRegist hrRegist) {
        //查询ftp信息上传到SFTP
        FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
        //获取文件上传相对路径  0305-企业注册文件夹
        String ftpFilePath = fdftpInfo.getFtprootpath() + FileUtil.getFtpPath("0305", "");
        SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(), fdftpInfo.getUsername(), fdftpInfo.getPassword());

        FcHrRegistTemp fcHrRegisterTempNew = new FcHrRegistTemp();
        if (StringUtils.isNotEmpty(hrRegist.getGrpImageFront())) {
            fcHrRegisterTempNew.setGrpIDImage1(uploadSftp(sFtp, ftpFilePath, hrRegist.getGrpImageFront()));
        }
        if (StringUtils.isNotEmpty(hrRegist.getGrpImageBack())) {
            fcHrRegisterTempNew.setGrpIDImage2(uploadSftp(sFtp, ftpFilePath, hrRegist.getGrpImageBack()));
        }
        if (StringUtils.isNotEmpty(hrRegist.getLegalImgFront())) {
            fcHrRegisterTempNew.setLegIDImage1(uploadSftp(sFtp, ftpFilePath, hrRegist.getLegalImgFront()));
        }
        if (StringUtils.isNotEmpty(hrRegist.getLegalImgBack())) {
            fcHrRegisterTempNew.setLegIDImage2(uploadSftp(sFtp, ftpFilePath, hrRegist.getLegalImgBack()));
        }
        if (StringUtils.isNotEmpty(hrRegist.getIdCardFront())) {
            fcHrRegisterTempNew.setIdImage1(uploadSftp(sFtp, ftpFilePath, hrRegist.getIdCardFront()));
        }
        if (StringUtils.isNotEmpty(hrRegist.getIdCardBack())) {
            fcHrRegisterTempNew.setIdImage2(uploadSftp(sFtp, ftpFilePath, hrRegist.getIdCardBack()));
        }
        if (StringUtils.isNotEmpty(fcHrRegisterTempNew.getGrpIDImage1()) || StringUtils.isNotEmpty(fcHrRegisterTempNew.getGrpIDImage2())
                || StringUtils.isNotEmpty(fcHrRegisterTempNew.getLegIDImage1()) || StringUtils.isNotEmpty(fcHrRegisterTempNew.getLegIDImage2())
                || StringUtils.isNotEmpty(fcHrRegisterTempNew.getIdImage1()) || StringUtils.isNotEmpty(fcHrRegisterTempNew.getIdImage2())) {
            fcHrRegisterTempNew.setRegistSN(hrRegist.getRegistSN());
            log.info("fcHrRegisterUpload request:{}", JsonUtil.toJSON(fcHrRegisterTempNew));
            fcHrRegistTempMapper.updateByPrimaryKeySelective(fcHrRegisterTempNew);
        }
    }


    private String uploadSftp(SFtpClientUtil sFtp, String ftpFilePath, String url) {
        String fileName = UUIDUtil.genUUID() + ".png";
        String rest = sFtp.uploadFileUrl(ftpFilePath + fileName, url, fileName);
        return fileDisplayPath + ftpFilePath + rest;
    }


}
