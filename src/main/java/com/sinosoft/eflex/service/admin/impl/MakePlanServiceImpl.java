package com.sinosoft.eflex.service.admin.impl;

import com.sinosoft.eflex.dao.FCEnsurePlanMapper;
import com.sinosoft.eflex.dao.FCPlanRiskDutyMapper;
import com.sinosoft.eflex.enums.OperatorEnum;
import com.sinosoft.eflex.framework.exception.EFlexServiceException;
import com.sinosoft.eflex.framework.exception.EFlexServiceExceptionEnum;
import com.sinosoft.eflex.model.FCEnsurePlan;
import com.sinosoft.eflex.model.GlobalInput;
import com.sinosoft.eflex.model.convert.FcEnsurePlanConvert;
import com.sinosoft.eflex.model.request.FcEnsurePlanReq;
import com.sinosoft.eflex.service.UserService;
import com.sinosoft.eflex.service.admin.MakePlanService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 定制福利*
 *
 * <AUTHOR> *
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MakePlanServiceImpl implements MakePlanService {

    private final UserService userService;
    private final FCEnsurePlanMapper fcEnsurePlanMapper;
    private final FCPlanRiskDutyMapper fcPlanRiskDutyMapper;

    @Override
    public void insertEnsurePlan(String token, FcEnsurePlanReq fcEnsurePlanReq) {
        GlobalInput globalInput = userService.getSession(token);
        // 校验参数是否为空
        this.checkDataIsEmpty(fcEnsurePlanReq);

        // 重新试算计划保费
        double totalPrem = fcPlanRiskDutyMapper.selectTotalPremPlan(fcEnsurePlanReq.getEnsureCode(), fcEnsurePlanReq.getPlanCode());

        // 计划状态更新为配置完成
        FcEnsurePlanReq ensurePlanReq = FcEnsurePlanConvert.convert(fcEnsurePlanReq, totalPrem, globalInput.getUserNo());
        fcEnsurePlanMapper.updateByPrimaryKeySelective(FcEnsurePlanConvert.convert(ensurePlanReq, "UPDATE"));
    }


    /**
     * 数据校验*
     *
     * @param fcEnsurePlanReq
     * @return
     */
    private void checkDataIsEmpty(FcEnsurePlanReq fcEnsurePlanReq) {
        if (OperatorEnum.ADD.getCode().equals(fcEnsurePlanReq.getOperating())) {
            List<FCEnsurePlan> fcEnsurePlanTemp = fcEnsurePlanMapper.selectByEnsureCode(fcEnsurePlanReq.getEnsureCode());
            List<String> planCodeListTemp = new ArrayList<>();
            for (FCEnsurePlan fcEnsurePlan : fcEnsurePlanTemp) {
                planCodeListTemp.add(fcEnsurePlan.getPlanCode());
            }
            if (planCodeListTemp.contains(fcEnsurePlanReq.getPlanCode())) {
                throw new EFlexServiceException(EFlexServiceExceptionEnum.PLAN_REPEAT_ERROR);
            }
        }
        List<Map<String, String>> riskDuty = fcPlanRiskDutyMapper.selectDutyListByPlanCode(fcEnsurePlanReq.getEnsureCode(), fcEnsurePlanReq.getPlanCode());
        if (CollectionUtils.isEmpty(riskDuty)) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.NOT_ONE_DUTY_ERROR);
        }
    }

}
