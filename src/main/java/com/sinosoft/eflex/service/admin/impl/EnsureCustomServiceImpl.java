package com.sinosoft.eflex.service.admin.impl;

import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.EnsureTypeEnum;
import com.sinosoft.eflex.framework.exception.EFlexServiceException;
import com.sinosoft.eflex.framework.exception.EFlexServiceExceptionEnum;
import com.sinosoft.eflex.model.FCEnsure;
import com.sinosoft.eflex.model.FCEnsurePlan;
import com.sinosoft.eflex.model.FCPlanRisk;
import com.sinosoft.eflex.model.FDRiskInfo;
import com.sinosoft.eflex.model.vo.InsuranceTermsVO;
import com.sinosoft.eflex.model.vo.convert.InsuranceTermsConvert;
import com.sinosoft.eflex.service.admin.EnsureCustomService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 福利地址 *
 * 优化 EnsureMakeService 接口*
 *
 * <AUTHOR>
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class EnsureCustomServiceImpl implements EnsureCustomService {


    private final FCEnsurePlanMapper fcEnsurePlanMapper;
    private final FCPlanRiskMapper fcPlanRiskMapper;
    private final FCEnsureMapper fcEnsureMapper;
    private final FCPerInfoTempMapper fcPerInfoTempMapper;
    private final FDRiskInfoMapper fdRiskInfoMapper;
    private final FCPerinfoFamilyTempMapper fcPerinfoFamilyTempMapper;

    /**
     * 保险条款 已阅读*
     */
    private static final Integer PROTOCOL_READING_STATE = 1;


    @Override
    public List<InsuranceTermsVO> insuranceTermsList(String ensureCode) {
        log.info("insuranceTermsList request:{}", ensureCode);
        // 查询所有福利配置险种信息
        List<FCPlanRisk> fcPlanRiskList = fcPlanRiskMapper.selectPlanDetailList(ensureCode, null);
        // 查询险种信息
        List<FDRiskInfo> fdRiskInfos = fdRiskInfoMapper.selectRiskInfo();

        return InsuranceTermsConvert.convert(fcPlanRiskList, fdRiskInfos);
    }

    @Override
    public boolean protocolReading(String ensureCode, String riskCode) {
        log.info("protocolReading request:{},{}", ensureCode, riskCode);
        int i = fcPlanRiskMapper.updateEnsureRiskInfo(new FCPlanRisk(ensureCode, riskCode, PROTOCOL_READING_STATE));
        return i > 0;
    }


    @Override
    public boolean importCheck(String ensureCode) {

        // 校验是否已经导入了员工计划和员工
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
        log.info("importCheck fcEnsure:{}", JsonUtil.toJSON(fcEnsure));

        // 查询计划内员工
        List<FCEnsurePlan> planList = fcEnsurePlanMapper.selectFCEnsurePlans(Collections.singletonMap("ensureCode", ensureCode));
        if (planList.size() < 1) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.NOT_ONE_EMPLOYEE);
        }

        // 至少存在一个学生或者员工
        if (fcPerInfoTempMapper.selectHas(ensureCode) < 1) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.NOT_ONE, "EnsureType", EnsureTypeEnum.STUDENTENSURE.getCode().equals(fcEnsure.getEnsureType()) ? "学生！" : "员工！");
        }

        // 人员信息查询
        List<Map<String, String>> fcPerInfoTemps = new ArrayList<>();
        if (EnsureTypeEnum.GRPENSURE.getCode().equals(fcEnsure.getEnsureType()) || EnsureTypeEnum.SITEGRPENSURE.getCode().equals(fcEnsure.getEnsureType())) {
            fcPerInfoTemps = fcPerInfoTempMapper.getPlanByEnsureCode(ensureCode);
        } else if (EnsureTypeEnum.STUDENTENSURE.getCode().equals(fcEnsure.getEnsureType())) {
            fcPerInfoTemps = fcPerinfoFamilyTempMapper.getPlanByEnsureCode(ensureCode);
        }
        if (fcPerInfoTemps.size() > 0) {
            List<String> planCodeList = planList.stream().map(FCEnsurePlan::getPlanCode).collect(Collectors.toList());
            log.info("importCheck planCodeList:{}", JsonUtil.toJSON(planCodeList));
            for (Map<String, String> fcPerInfoTemp : fcPerInfoTemps) {
                if (!planCodeList.contains(fcPerInfoTemp.get("defaultPlan"))) {
                    throw new EFlexServiceException(EFlexServiceExceptionEnum.PER_NAME_ERROR, "perName", fcPerInfoTemp.get("perName"));
                }
            }
        }

        // 保单条款阅读检测
        List<FCPlanRisk> fcPlanRiskList = fcPlanRiskMapper.selectPlanDetailList(ensureCode, null);
        List<FCPlanRisk> fcPlanRiskState = fcPlanRiskList.stream().filter(f -> !PROTOCOL_READING_STATE.equals(f.getProtocolReading())).collect(Collectors.toList());
        log.info("importCheck fcPlanRiskState:{}", JsonUtil.toJSON(fcPlanRiskState));
        if (!CollectionUtils.isEmpty(fcPlanRiskState)) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.PROTOCOL_READ_ERROR);
        }
        return true;
    }


}
