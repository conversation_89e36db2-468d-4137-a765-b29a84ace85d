package com.sinosoft.eflex.service.admin;

import ch.qos.logback.classic.Logger;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.IDTypeEnum;
import com.sinosoft.eflex.enums.SMSTemplateNoEnum;
import com.sinosoft.eflex.model.AddressEntity.IdCardVerifyRequest;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.sendmessage.SendMessageResp;
import com.sinosoft.eflex.model.sendmessage.SendSMSReq;
import com.sinosoft.eflex.service.AddressCheckService;
import com.sinosoft.eflex.service.MaxNoService;
import com.sinosoft.eflex.service.RegistRegService;
import com.sinosoft.eflex.service.UserService;
import com.sinosoft.eflex.service.sms.SendMessageService;
import com.sinosoft.eflex.util.*;
import com.sinosoft.eflex.util.encrypt.LisIDEA;
import org.apache.commons.lang.StringUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by SunZH on 2020/10/14.
 */
@Service
public class BusInessAdminService {

    /**
     * 日志
     */
    private static Logger Log = (Logger) LoggerFactory.getLogger(BusInessAdminService.class);

    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private UserService userService;
    @Autowired
    private RegistRegService registRegService;
    @Autowired
    private SendMessageService sendMessageService;
    @Autowired
    private FdUserMapper fdUserMapper;
    @Autowired
    private FcGrpContactMapper fcGrpContactMapper;
    @Autowired
    private FCContactGrpRelaMapper fcContactGrpRelaMapper;
    @Autowired
    private FCGrpInfoMapper fcGrpInfoMapper;
    @Autowired
    private MyProps myProps;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private FDusertomenugrpMapper fDusertomenugrpMapper;
    @Autowired
    private FDPwdHistMapper fdPwdHistMapper;
    @Autowired
    private FDUserRoleMapper fdUserRoleMapper;
    @Autowired
    private AddressCheckService addressCheckService;

    /**
     * 业务管理员--查询企业信息列表
     */
    public String gertGrplist(String token, String grpName, int pageNo, int pageSize) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", "500");
        resultMap.put("success", false);
        GlobalInput globalInput = userService.getSession(token);
        try {
            if (globalInput == null) {
                resultMap.put("message", "所登陆管理员已失效，请重新登陆。");
                return JSON.toJSONString(resultMap);
            }
            PageHelper.startPage(pageNo, pageSize);
            List<Map<String, String>> fcGrpInfos = fcGrpInfoMapper.getGrpListBygrpName(grpName);
            PageHelperUtil<Map<String, String>> teamPageInfo = new PageHelperUtil<>(fcGrpInfos);
            //判断是否进行过企业变换 换过默认使用变换企业 未变换过 赋予token默认企业
            if (StringUtils.isBlank(globalInput.getGrpNo()) && fcGrpInfos.size() > 0) {
                String grpNo = fcGrpInfos.get(0).get("grpNo");
                String userinfo = redisUtil.get(token);
                globalInput = JSON.parseObject(userinfo, GlobalInput.class);
                globalInput.setGrpNo(grpNo);
                //获取福利编号
                Map<String, Object> params = new HashMap<>();
                params.put("grpNo", fcGrpInfos.get(0).get("grpNo"));
                params.put("ensureState", "0");
                List<FCEnsure> fcEnsureList = fcEnsureMapper.findEnsureList(params);
                if (fcEnsureList != null && fcEnsureList.size() > 0) {
                    globalInput.setEnsureCode(fcEnsureList.get(0).getEnsureCode());
                } else {
                    globalInput.setEnsureCode("");
                }
                Integer loginValidity = Integer.parseInt(myProps.getTermValidityLogin());
                redisUtil.put(token, JSON.toJSONString(globalInput), loginValidity);
                if (JSON.parseObject(redisUtil.get(token), GlobalInput.class).getGrpNo().equals(grpNo)) {
                    Log.info("更换登录企业成功GrpNo:" + grpNo);
                }
            }
            resultMap.put("data", teamPageInfo.getList());
            resultMap.put("dataTotal", teamPageInfo.getTotal());
            //获取当前企业
            resultMap.put("curretGrpNo", globalInput.getGrpNo());
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("message", "企业列表信息获取成功。");
        } catch (Exception e) {
            Log.info("获取失败原因：" + e.getMessage());
            resultMap.put("message", "获取企业列表信息失败。");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 获取企业Hr
     *
     * @return
     */
    public String queryGrpContact(String token, String grpNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            resultMap.put("code", "500");
            resultMap.put("success", false);
            if (StringUtils.isBlank(grpNo)) {
                resultMap.put("message", "企业Hr信息查询参数缺失：GRPNO IS NULL！");
                return JSON.toJSONString(resultMap);
            }
            GlobalInput globalInput = userService.getSession(token);
            List<Map<String, String>> fcGrpContactList = new ArrayList<>();
            List<FCContactGrpRela> fcContactGrpRelaList = fcContactGrpRelaMapper.selectByGrpNo(grpNo);
            for (FCContactGrpRela fcContactGrpRela : fcContactGrpRelaList) {
                Map<String, String> grpContactMapInfo = fcGrpContactMapper.selectContactByNo(grpNo, fcContactGrpRela.getContactNo());
                if (grpContactMapInfo != null) {
                    //脱敏处理
                    grpContactMapInfo.put("IDNo", Base64AndMD5Util.base64SaltEncode(grpContactMapInfo.get("IDNo"), "130"));
                    grpContactMapInfo.put("MobilePhone", Base64AndMD5Util.base64SaltEncode(grpContactMapInfo.get("MobilePhone"), "130"));

                    fcGrpContactList.add(grpContactMapInfo);
                }
            }
            if (fcGrpContactList.size() > 0) {
                resultMap.put("code", "200");
                resultMap.put("success", true);
                resultMap.put("message", "企业Hr查询成功。");
            } else {
                resultMap.put("message", "未查询到相关Hr信息。");
            }
            //查询企业Hr 更新Token企业客户号
            String userinfo = redisUtil.get(token);
            globalInput = JSON.parseObject(userinfo, GlobalInput.class);
            globalInput.setGrpNo(grpNo);
            Integer loginValidity = Integer.parseInt(myProps.getTermValidityLogin());
            redisUtil.put(token, JSON.toJSONString(globalInput), loginValidity);
            if (JSON.parseObject(redisUtil.get(token), GlobalInput.class).getGrpNo().equals(grpNo)) {
                Log.info("token更新完成：GrpNo:" + grpNo);
            }
            resultMap.put("data", fcGrpContactList);
        } catch (Exception e) {
            Log.info("查询企业Hr失败:" + e.getMessage());
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "企业Hr信息查询失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 业务管理员新增Hr
     *
     * @param token
     * @param params
     * @return
     */
    @Transactional
    public String busInsertHrInfo(String token, Map<String, String> params) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", "500");
        resultMap.put("success", false);
        int checkint = fdUserMapper.checkByPhone(params.get("mobilePhone"), params.get("idNo"), "2");
        if (checkint > 0) {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "该手机号已注册！");
            return JSON.toJSONString(resultMap);
        }
        try {
            GlobalInput globalInput = userService.getSession(token);
            String grpNo = globalInput.getGrpNo();
            FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(grpNo);
            //联系人类别默认  第二联系人   -- update by wudezhong 2020.11.18 不存在第二联系人的情况
            String contactType = "02";
            //校验国籍
            String checkNationalityCode = "";
            checkNationalityCode = addressCheckService.checkNationalityCode(params.get("nativeplace"));
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(checkNationalityCode)) {
                checkNationalityCode = checkNationalityCode.replace("***", params.get("name"));
                resultMap.put("message", "业务管理员新增Hr失败:" + checkNationalityCode);
                return JSON.toJSONString(resultMap);
            }
            String error = CheckUtils.checkSinglePeople(params);
            if (StringUtils.isNotBlank(error)) {
                if ("3".equals(params.get("sign"))) {
                    error = "经办人 " + error;
                }
                resultMap.put("message", "业务管理员新增Hr失败:" + error);
                return JSON.toJSONString(resultMap);
            }
            if (!params.get("mobile").matches("^1[2-9][0-9]{9}$")) {
                resultMap.put("message", "请输入正确手机号！");
                return JSON.toJSONString(resultMap);
            } else {
                params.put("mobilePhone", params.get("mobile"));
            }
            //判断当前企业下是否存在该Hr  存在--阻断
            Map<String, String> paramInfo = new HashMap<String, String>();
            String idType = params.get("idType");
            paramInfo.put("idType", idType);
            paramInfo.put("idNo", params.get("idNo"));
            paramInfo.put("grpNo", grpNo);
            int sum = fcGrpContactMapper.checkIdNoIsExists(paramInfo);
            if (sum > 0) {
                resultMap.put("message", "当前企业已存在该Hr。");
                return JSON.toJSONString(resultMap);
            }

            // 校验证件号码和出生日期、性别是否一致
            if (idType.equals(IDTypeEnum.IDCARD.getCode()) || idType.equals(IDTypeEnum.HOUSEHOLDREGISTER.getCode())) {
                String checkIDCardResult = IDCardUtil.checkIDCard(params.get("idNo"), params.get("sex"), params.get("birthDay"));
                if (org.apache.commons.lang3.StringUtils.isNotBlank(checkIDCardResult)) {
                    return JSON.toJSONString(ResultUtil.error(checkIDCardResult));
                }
            }
            List<IdCardVerifyRequest.Verify> list = new ArrayList<>();
            list.add(IdCardVerifyRequest.Verify.builder()
                    .idCard(params.get("idNo"))
                    .name(params.get("name"))
                    .build());
            //校验二要素
            String failVerifies = addressCheckService.checkIdCard(list);
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(failVerifies)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", failVerifies);
                return JSON.toJSONString(resultMap);
            }

            //判断当前平台是否已存在该联系人  存在--选择性存储
            paramInfo.clear();
            paramInfo.put("idType", idType);
            paramInfo.put("idNo", params.get("idNo"));
            String contactStatus = "";
            FdUser fdUser = new FdUser();
            if (fcGrpContactMapper.isExist(paramInfo) != 0) {
                contactStatus = "1";
                //存在则获取FDuser的信息
                fdUser = fdUserMapper.findUserByIdno(params.get("idNo"));
            } else {
                contactStatus = "0";
                //不存在则插入用户信息表
                fdUser = insertFdUser(params);
            }
            //定义短信内容对象
            SendSMSReq sendSMSReq = new SendSMSReq();
            //定义企业联系人对象
            FcGrpContact fcGrpContact = new FcGrpContact();
            fcGrpContact.setIdType(idType);
            fcGrpContact.setIdNo(params.get("idNo"));
            //HR已经注册后这些表不会存
            if (contactStatus.equals("0")) {
                //查询该企业下是否有第一联系人
                Map<String, String> mapInfo = new HashMap<>();
                mapInfo.put("grpNo", grpNo);
                mapInfo.put("contactType", "01");
                mapInfo.put("lockState", "0");
                List<FCContactGrpRela> fcContactGrpRelaList = fcContactGrpRelaMapper.seletcContactListByMap(mapInfo);
                if (fcContactGrpRelaList.size() == 0) {
                    contactType = "01";
                }
                //存储企业联系人表
                BeanUtils.copyProperties(params, fcGrpContact);
                String contactNo = maxNoService.createMaxNo("ContactNo", null, 20);
                fcGrpContact.setContactNo(contactNo);
                fcGrpContact.setGrpNo(params.get("grpNo"));
                fcGrpContact.setName(params.get("name"));
                fcGrpContact.setSex(params.get("sex"));
                fcGrpContact.setBirthDay(params.get("birthDay"));
                fcGrpContact.setIdType(idType);
                fcGrpContact.setIdNo(params.get("idNo"));
                fcGrpContact.setIdTypeEndDate(params.get("idTypeEndDate"));
                fcGrpContact.setIdTypeStartDate(params.get("idTypeStartDate"));
                fcGrpContact.setNativeplace(params.get("nativeplace"));
                fcGrpContact.setMobilePhone(params.get("mobilePhone"));
                fcGrpContact.setIdImage1(params.get("idImage1"));
                fcGrpContact.setIdImage2(params.get("idImage2"));
                fcGrpContact.setContactType(contactType);
                fcGrpContact.setOperator(globalInput.getUserName());
                fcGrpContact = CommonUtil.initObject(fcGrpContact, "INSERT");
                fcGrpContactMapper.insert(fcGrpContact);
                /*用户与菜单表*/
                FDusertomenugrpKey fDusertomenugrpKey = registRegService.initFDusertomenugrpKey(fdUser);
                fDusertomenugrpMapper.insert(fDusertomenugrpKey);
                /*密码历史表*/
                FDPwdHist fdPwdHist = new FDPwdHist();
                //登录密码流水号
                fdPwdHist.setPassWordSN(maxNoService.createMaxNo("PassWordSN", null, 20));
                //密码历史表用户编码
                fdPwdHist.setUserNo(fdUser.getUserNo());
                //角色表用户密码
                fdPwdHist.setPassWord(fdUser.getPassWord());
                //密码历史表操作员
                fdPwdHist.setOperator(fdUser.getUserNo());
                fdPwdHist = CommonUtil.initObject(fdPwdHist, "INSERT");
                fdPwdHistMapper.insert(fdPwdHist);
                /*用户角色表*/
                FDUserRole fdUserRole = registRegService.insertFDUserRole(fdUser);
                fdUserRoleMapper.insert(fdUserRole);
                /*用户表*/
                fdUser.setCustomNo(fcGrpContact.getContactNo());
                fdUserMapper.insert(fdUser);
                //HR不存在进行注册
                sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_002.getCode());
            } else {  //已存在该Hr
                //查询已存在的H企业联系人信息
                fcGrpContact = fcGrpContactMapper.selectGrpContactInfo(fcGrpContact);
                //HR不存在进行注册
                sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_003.getCode());
            }
            //短信发送
            sendSMSReq.setPhones(fcGrpContact.getMobilePhone());
            Map<String, Object> map = new HashMap<>();
            map.put("grp_name", fcGrpInfo.getGrpName());
            sendSMSReq.setParam(map);
            SendMessageResp sendMessageResp = sendMessageService.sendSMS(sendSMSReq);
            if (sendMessageResp.getSuccess()) {
                Log.info("新增Hr用户通知短信发送成功。");
            } else {
                Log.info("新增Hr用户通知短信发送失败。{}", sendMessageResp.getMsg());
            }
            // 存储企业与企业联系人关联表
            FCContactGrpRela fcContactGrpRela = new FCContactGrpRela();
            fcContactGrpRela.setContactNo(fcGrpContact.getContactNo());
            fcContactGrpRela.setGrpNo(grpNo);
            fcContactGrpRela.setContactType(contactType);
            fcContactGrpRela.setLockState("0");// 默认启用
            fcContactGrpRela.setOperator(globalInput.getUserNo());
            fcContactGrpRela = CommonUtil.initObject(fcContactGrpRela, "INSERT");
            fcContactGrpRelaMapper.insertSelective(fcContactGrpRela);
            resultMap.put("grpNo", params.get("grpNo"));
            resultMap.put("code", "200");
            resultMap.put("success", true);
            resultMap.put("message", "新增Hr用户成功。");
        } catch (IllegalStateException e) {
            Log.info("实现文件上传:", e);
        } catch (Exception e) {
            Log.error("busInsertHrInfo error", e);
            resultMap.put("message", "新增Hr用户失败。");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return JSON.toJSONString(resultMap);
    }

    public FdUser insertFdUser(Map<String, String> params) {
        FdUser fdUser = new FdUser();
        LisIDEA encryPassword = new LisIDEA();
        //用户编号
        fdUser.setUserNo(maxNoService.createMaxNo("UserNo", null, 20));
        //用户类型
        fdUser.setCustomType("2");
        //登陆姓名为证件号
        fdUser.setUserName(params.get("idNo"));
        //昵称
        fdUser.setNickName(params.get("name"));
        //hr证件号
        fdUser.setIDNo(params.get("idNo"));
        //用户密码为证件号后6位
        fdUser.setPassWord(encryPassword.encryptString(params.get("idNo").length() >= 6 ?
                params.get("idNo").substring(params.get("idNo").length() - 6) :
                params.get("idNo") + "000000".substring(0, 6 - params.get("idNo").length())));
        //手机号
        fdUser.setPhone(params.get("mobile"));
        //是否锁定
        fdUser.setIsLock("0");
        //是否是VIP
        fdUser.setIsVIP("N");
        //用户状态
        fdUser.setUserState("1");
        /*//屏蔽首次登录功能 ： 为1时不用首次登录验证。为空时需要
        fdUser.setPWDState("1");*/
        //登录失败次数
        fdUser.setLoginFailTimes(0);
        //操作员
        fdUser.setOperator(fdUser.getUserNo());
        fdUser = (FdUser) CommonUtil.initObject(fdUser, "INSERT");
        return fdUser;
    }

}
