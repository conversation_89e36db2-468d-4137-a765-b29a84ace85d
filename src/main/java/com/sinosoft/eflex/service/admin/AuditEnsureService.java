package com.sinosoft.eflex.service.admin;

import com.sinosoft.eflex.model.request.RiskConfigReq;
import com.sinosoft.eflex.model.request.ToReviewAdoptRequest;
import com.sinosoft.eflex.model.vo.CheckClientNoVO;

import java.util.List;

/**
 * 初/复 审核岗位 接口*
 * 优化 EnsureAuditService  *
 *
 * <AUTHOR> *
 */
public interface AuditEnsureService {

    /**
     * 险种配置*
     *
     * @param token         token
     * @param riskConfigReq 请求参数
     * @return
     */
    boolean updatePlanRisk(String token, RiskConfigReq riskConfigReq);

    /**
     * 工号校验*
     *
     * @param authorization token*
     * @param clientNo      工号
     * @param grpNo         企业号
     * @return 姓名
     */
    CheckClientNoVO clientNoCheck(String authorization, String clientNo, String grpNo);


    /**
     * 工号查询
     *
     * @param clientNo 工号
     * @return
     */
    String employeesQuery(String clientNo);


    /**
     * 复审提交*
     *
     * @param token              登陆态
     * @param reviewAdoptRequest 请求
     */
    void toReviewAdopt(String token, ToReviewAdoptRequest reviewAdoptRequest);

    /**
     * 签单前校验
     *
     * @param ensureCode 福利编号
     * @return 错误信息
     */
    List<String> signBillsCheck(String ensureCode);
}
