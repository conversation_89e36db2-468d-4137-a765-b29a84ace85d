package com.sinosoft.eflex.service.admin.impl;

import com.google.common.collect.Lists;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.utils.AssertUtil;
import com.hqins.common.utils.HttpUtils;
import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.constants.EflexConstants;
import com.sinosoft.eflex.constants.OrgProperties;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.DesignStatusEnum;
import com.sinosoft.eflex.enums.PlanTypeEnum;
import com.sinosoft.eflex.enums.StateEnum;
import com.sinosoft.eflex.framework.exception.EFlexServiceException;
import com.sinosoft.eflex.framework.exception.EFlexServiceExceptionEnum;
import com.sinosoft.eflex.model.AddressEntity.convert.CheckSameCustomerConvert;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.convert.*;
import com.sinosoft.eflex.model.dto.OrgInfoDTO;
import com.sinosoft.eflex.model.request.RiskConfigReq;
import com.sinosoft.eflex.model.request.ToReviewAdoptRequest;
import com.sinosoft.eflex.model.vo.CheckClientNoVO;
import com.sinosoft.eflex.rpc.model.CoreCustomerErrorResDTO;
import com.sinosoft.eflex.rpc.model.CoreCustomerInfoResDTO;
import com.sinosoft.eflex.rpc.service.CoreCustomerService;
import com.sinosoft.eflex.service.EnsureMakeService;
import com.sinosoft.eflex.service.MaxNoService;
import com.sinosoft.eflex.service.UserService;
import com.sinosoft.eflex.service.admin.AuditEnsureService;
import com.sinosoft.eflex.service.sms.SendMessageService;
import com.sinosoft.eflex.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 初/复 审核岗位 接口*
 *
 * <AUTHOR> *
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AuditEnsureServiceImpl implements AuditEnsureService {


    private final OrgProperties orgProperties;
    private final UserService userService;
    private final MaxNoService maxNoService;
    private final SendMessageService sendMessageService;
    private final EnsureMakeService ensureMakeService;
    private final AsyncAuditService asyncAuditService;
    private final CoreCustomerService coreCustomerService;


    private final FCPlanRiskDutyMapper fcPlanRiskDutyMapper;
    private final FCPlanRiskMapper fcPlanRiskMapper;
    private final FCGrpInfoMapper fcGrpInfoMapper;
    private final FCEnsureMapper fcEnsureMapper;
    private final FcGrpContactMapper fcGrpContactMapper;
    private final FCGrpApplicantMapper fcGrpApplicantMapper;
    private final FCGrpApplicantContactMapper fcGrpApplicantContactMapper;
    private final FCGrpOrderMapper fcGrpOrderMapper;
    private final FcEnsureContactMapper fcEnsureContactMapper;
    private final FCEnsureConfigMapper fcEnsureConfigMapper;
    private final FCPlanHealthDesignRelaMapper fcPlanHealthDesignRelaMapper;
    private final FCOrderMapper fcOrderMapper;
    private final FCOrderInsuredMapper fcOrderInsuredMapper;
    private final FCOrderItemMapper fcOrderItemMapper;


    /**
     * "组织机构类型：PARTNER-合伙人；CHANNEL-渠道商"
     */
    private static final String ORG_TYPE = "PARTNER";


    /**
     * 险种信息设置
     */
    @Override
    public boolean updatePlanRisk(String token, RiskConfigReq riskConfigReq) {
        log.info("AuditEnsureServiceImpl updatePlanRisk token:{}, request:{}", token, JsonUtil.toJSON(riskConfigReq));
        if (StateEnum.VALID.getCode().equals(riskConfigReq.getGiftInsureSign())) {
            AssertUtil.notNull(riskConfigReq.getOriginalPrem(), new EFlexServiceException(EFlexServiceExceptionEnum.ORIGINAL_PREM_ERROR));
        }

        // 判断赠险标记为是，则该险种保费全为0 赠险标记(0-否,1-是)
        int selectCount = fcPlanRiskDutyMapper.selectCount(riskConfigReq.getEnsureCode(), riskConfigReq.getRiskCode());
        if (StateEnum.VALID.getCode().equals(riskConfigReq.getGiftInsureSign()) && selectCount > 0) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.RISK_CONFIG_ERROR, "riskCode", riskConfigReq.getRiskCode());
        }
        GlobalInput globalInput = userService.getSession(token);
        log.info("AuditEnsureServiceImpl updatePlanRisk globalInput:{}", JsonUtil.toJSON(globalInput));
        //维护配置险种信息
        int i = fcPlanRiskMapper.updateEnsureRiskInfo(FcPlanRiskConvert.convert(riskConfigReq, globalInput));
        return i > 0;
    }


    @Override
    public CheckClientNoVO clientNoCheck(String authorization, String clientNo, String grpNo) {

        // 查询企业创建员工号比对
        FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(grpNo);
        log.info("clientNoCheck fcGrpInfo:{}", JsonUtil.toJSON(fcGrpInfo));

        // 注册业务员名称
        String signName = null;
        if (null != fcGrpInfo && StringUtils.isNotEmpty(fcGrpInfo.getClientno()) && !fcGrpInfo.getClientno().equals(clientNo)) {
            signName = this.employeesQuery(fcGrpInfo.getClientno());
        }
        return new CheckClientNoVO(fcGrpInfo.getClientno(), signName, this.employeesQuery(clientNo));
    }


    /**
     * 验证工号信息真实
     *
     * @param clientNo 业务员工号
     * @return
     */
    @Override
    public String employeesQuery(String clientNo) {
        log.info("employeesQuery request:{}", clientNo);
        try {
            String rest = HttpUtils.get(orgProperties.getEmployeesUrl() + "?" + "employeeCode=" + clientNo + "&orgType=" + ORG_TYPE);
            log.info("employeesQuery response:{}", JsonUtil.toJSON(rest));
            ApiResult<OrgInfoDTO> apiResult = JsonUtil.fromJSON(rest, ApiResult.class);
            if (EflexConstants.SUCCESS.equals(apiResult.getCode()) && null != apiResult.getData()) {
                OrgInfoDTO orgInfoDTO = JsonUtil.fromJSON(JsonUtil.toJSON(apiResult.getData()), OrgInfoDTO.class);
                return orgInfoDTO.getName();
            }
        } catch (Exception e) {
            log.error("clientNoCheck http error", e);
        }
        return null;
    }


    /**
     * 福利复核通过   完成定制
     *
     * @param token              登陆态
     * @param reviewAdoptRequest 请求
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void toReviewAdopt(String token, ToReviewAdoptRequest reviewAdoptRequest) {
        log.info("toReviewAdopt request:{}", JsonUtil.toJSON(reviewAdoptRequest));
        // 获取session
        GlobalInput globalInput = userService.getSession(token);
        String userNo = globalInput.getUserNo();
        // 获取当前企业信息
        FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(reviewAdoptRequest.getGrpNo());
        // 获取当前 企业联系人
        FcGrpContact fcGrpContact = fcGrpContactMapper.selectByPrimaryKey(reviewAdoptRequest.getCustomNo());
        if (Objects.isNull(fcGrpContact)) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.CONTACT_NULL_ERROR);
        }

        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(reviewAdoptRequest.getEnsureCode());
        // 判断险种是否下架
        String riskStopSale = ensureMakeService.checkRiskStopSale(fcEnsure.getEnsureCode());
        AssertUtil.isTrue(StringUtil.isEmpty(riskStopSale), new EFlexServiceException(EFlexServiceExceptionEnum.PLAN_NOT_EXIST.getCode(), riskStopSale));

        // 判断是否是弹性计划 是弹性计划是否配置健康告知 健康告知是否复核通过
        this.healthDesignCheck(fcEnsure);

        // 首次入库即复核通过 ==null 证明是首次提交
        if (null == fcEnsure.getPolicyState()) {
            // 团体投保人编号
            String grpAppNo = maxNoService.createMaxNo("GrpAppNo", "", 20);
            fcGrpApplicantMapper.insertSelective(FcGrpApplicantConvert.convert(fcGrpInfo, grpAppNo, userNo));
            log.info("FCGrpApplicant insert");

            String serialNo = maxNoService.createMaxNo("GrpAppContact", "", 20);
            fcGrpApplicantContactMapper.insertSelective(FcGrpApplicantContactConvert.convert(fcGrpContact, serialNo, grpAppNo, reviewAdoptRequest.getGrpNo(), userNo));
            log.info("fcGrpApplicantContact insert");

            String grpOrderNo = maxNoService.createMaxNo("GrpOrderNo", null, 20);
            String prtNo = maxNoService.createMaxNo("TPrtNo", "", 20);
            fcGrpOrderMapper.insert(FcGrpOrderConvert.convert(reviewAdoptRequest, grpOrderNo, grpAppNo, prtNo, userNo));
        }

        // 异步数据处理
        asyncAuditService.dataHandling(fcEnsure, globalInput, fcGrpInfo);

        FcEnsureContact fcEnsureContact = fcEnsureContactMapper.selectByPrimaryKey(reviewAdoptRequest.getEnsureCode());
        // 短信发送
        sendMessageService.sendSMS(sendSMSReqConvert.convert(fcEnsureContact.getMobilePhone(), fcEnsure));

        fcEnsureMapper.updateByPrimaryKey(FcEnsureConvert.convert(fcEnsure, userNo, reviewAdoptRequest.getGreenInsurance()));
    }


    public void healthDesignCheck(FCEnsure fcEnsure) {
        if (PlanTypeEnum.EFLEXPLAN.getCode().equals(fcEnsure.getPlanType())) {
            Map<String, Object> ensureConfigMap = new HashMap<>(2);
            ensureConfigMap.put("ensureCode", fcEnsure.getEnsureCode());
            ensureConfigMap.put("configNo", "019");
            FCEnsureConfig fcEnsureConfig = fcEnsureConfigMapper.selectFcensureconfig(ensureConfigMap);
            // 1--配置健康告知方案
            if ("1".equals(fcEnsureConfig.getConfigValue())) {
                List<FCPlanHealthDesignRela> fcPlanHealthDesignList = fcPlanHealthDesignRelaMapper.selectByEnsureCode(fcEnsure.getEnsureCode());
                FCPlanHealthDesignRela fcPlanHealthDesignRela = fcPlanHealthDesignList.stream().findFirst().orElse(null);
                if (CollectionUtils.isEmpty(fcPlanHealthDesignList) || Objects.isNull(fcPlanHealthDesignRela) || !DesignStatusEnum.CHECKCOMPLETED.getCode().equals(fcPlanHealthDesignRela.getDesignStatus())) {
                    throw new EFlexServiceException(EFlexServiceExceptionEnum.HEALTH_RECHECK_ERROR);
                }
                if (fcPlanHealthDesignList.size() > 1) {
                    throw new EFlexServiceException(EFlexServiceExceptionEnum.HEALTH_REPEAT_ERROR);
                }
            }
        }
    }


    @Override
    public List<String> signBillsCheck(String ensureCode) {
        // 查询福利下团单信息
        FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(ensureCode);
        if (Objects.isNull(fcGrpOrder)) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.PLAN_NOT_EXIST);
        }

        // 查询订单信息
        List<FCOrder> fcOrderList = fcOrderMapper.selectOrderListByGrpOrderNo(fcGrpOrder.getGrpOrderNo());
        log.info("fcOrderList size:{}", fcOrderList.size());
        List<String> orderNoMap = fcOrderList.stream().map(FCOrder::getOrderNo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderNoMap)) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.PLAN_NOT_EXIST);
        }

        // 查询自订单信息
        List<String> orderItemNoList = Lists.newArrayList();
        List<List<String>> partition = Lists.partition(orderNoMap, 900);
        for (List<String> s : partition) {
            List<FCOrderItem> fcOrderItems = fcOrderItemMapper.selectByOrderNoList(s);
            List<String> collect = fcOrderItems.stream().map(FCOrderItem::getOrderItemNo).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                orderItemNoList.addAll(collect);
            }
        }
        log.info("orderItemNoList size:{}", orderItemNoList.size());

        // 查询投保人信息
        List<FCOrderInsured> fcOrderInsureds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(orderItemNoList)) {
            List<List<String>> orderItemNos = Lists.partition(orderItemNoList, 900);
            for (List<String> orderItemNo : orderItemNos) {
                // 查询人员信息
                fcOrderInsureds.addAll(fcOrderInsuredMapper.selectByOrderItemNoList(orderItemNo));
            }
        }
        log.info("fcOrderInsureds size:{}", fcOrderInsureds.size());
        // 投保人信息校验
        return this.insuredCheck(fcOrderInsureds);
    }

    private List<String> insuredCheck(List<FCOrderInsured> fcOrderInsureds) {
        List<String> errorList = new ArrayList<>();
        if (CollectionUtils.isEmpty(fcOrderInsureds)) {
            log.info("insuredCheck is null");
            return Lists.newArrayList();
        }

        Map<String, List<FCOrderInsured>> insuredMap = fcOrderInsureds.stream().filter(s -> StringUtils.isNotEmpty(s.getMobilePhone()))
                .collect(Collectors.groupingBy(FCOrderInsured::getMobilePhone));
        for (String s : insuredMap.keySet()) {
            List<FCOrderInsured> insuredList = insuredMap.get(s);
            if (insuredList.size() > 1) {
                fcOrderInsureds.removeAll(insuredList);
                List<String> names = insuredList.stream().map(FCOrderInsured::getName).collect(Collectors.toList());
                errorList.add("用户" + names + "手机号" + s + "重复");
            }
        }

        // 重客逻辑校验
        List<CoreCustomerInfoResDTO> coreCustomerInfoList = coreCustomerService.sameCustomer(CheckSameCustomerConvert.convert(fcOrderInsureds));
        List<CoreCustomerInfoResDTO> errors = coreCustomerInfoList.stream().filter(s -> CollectionUtils.isNotEmpty(s.getErrorList())).collect(Collectors.toList());
        // 添加错误信息
        for (CoreCustomerInfoResDTO error : errors) {
            List<CoreCustomerErrorResDTO> errorInfo = error.getErrorList();
            errorList.addAll(errorInfo.stream().map(CoreCustomerErrorResDTO::getErrorInfo).collect(Collectors.toList()));
        }
        return errorList;
    }


}
