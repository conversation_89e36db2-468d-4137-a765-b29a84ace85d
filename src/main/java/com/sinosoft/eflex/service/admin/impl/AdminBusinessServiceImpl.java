package com.sinosoft.eflex.service.admin.impl;

import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.IDTypeEnum;
import com.sinosoft.eflex.enums.SMSTemplateNoEnum;
import com.sinosoft.eflex.framework.exception.EFlexServiceException;
import com.sinosoft.eflex.framework.exception.EFlexServiceExceptionEnum;
import com.sinosoft.eflex.model.AddressEntity.convert.IdCardVerifyConvert;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.convert.*;
import com.sinosoft.eflex.model.request.GrpContactInsertReq;
import com.sinosoft.eflex.model.request.GrpContactReq;
import com.sinosoft.eflex.model.sendmessage.SendMessageResp;
import com.sinosoft.eflex.model.sendmessage.SendSMSReq;
import com.sinosoft.eflex.model.vo.GrpContactInfoVO;
import com.sinosoft.eflex.model.vo.convert.GrpContactConvert;
import com.sinosoft.eflex.service.AddressCheckService;
import com.sinosoft.eflex.service.MaxNoService;
import com.sinosoft.eflex.service.RegistRegService;
import com.sinosoft.eflex.service.UserService;
import com.sinosoft.eflex.service.admin.AdminBusinessService;
import com.sinosoft.eflex.service.sms.SendMessageService;
import com.sinosoft.eflex.util.CheckUtils;
import com.sinosoft.eflex.util.IDCardUtil;
import com.sinosoft.eflex.util.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 后台管理接口 优化 BusInessAdminService*
 *
 * <AUTHOR> *
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AdminBusinessServiceImpl implements AdminBusinessService {


    private final RedisUtil redisUtil;
    private final UserService userService;
    private final MyProps myProps;
    private final AddressCheckService addressCheckService;
    private final SendMessageService sendMessageService;
    private final MaxNoService maxNoService;
    private final RegistRegService registRegService;


    private final FCContactGrpRelaMapper fcContactGrpRelaMapper;
    private final FcGrpContactMapper fcGrpContactMapper;
    private final FdUserMapper fdUserMapper;
    private final FCGrpInfoMapper fcGrpInfoMapper;
    private final FDusertomenugrpMapper fDusertomenugrpMapper;
    private final FDPwdHistMapper fdPwdHistMapper;
    private final FDUserRoleMapper fdUserRoleMapper;

    @Override
    public List<GrpContactInfoVO> queryGrpContact(String token, String grpNo) {
        // 查询企业经办人关联信息
        List<FCContactGrpRela> fcContactGrpRelaList = fcContactGrpRelaMapper.selectByGrpNo(grpNo);

        // 结果创建
        List<GrpContactInfoVO> resultList = new ArrayList<>();

        // 查询经办人信息
        for (FCContactGrpRela fcContactGrpRela : fcContactGrpRelaList) {
            FcGrpContact fcGrpContact = fcGrpContactMapper.selectByPrimaryKey(fcContactGrpRela.getContactNo());
            if (fcGrpContact != null) {
                resultList.add(GrpContactConvert.convert(fcGrpContact, fcContactGrpRela));
            }
        }

        // 查询企业Hr 更新Token企业客户号
        String userinfo = redisUtil.get(token);
        GlobalInput globalInput = JsonUtil.fromJSON(userinfo, GlobalInput.class);
        log.info("Current globalInput:{}", JsonUtil.toJSON(globalInput));
        globalInput.setGrpNo(grpNo);
        int loginValidity = Integer.parseInt(myProps.getTermValidityLogin());
        log.info("update globalInput:{}", JsonUtil.toJSON(globalInput));
        redisUtil.put(token, JsonUtil.toJSON(globalInput), loginValidity);
        log.info("queryGrpContact.response:{}", JsonUtil.toJSON(resultList));
        return resultList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void acquiesceContact(String token, GrpContactReq grpContactReq) {
        GlobalInput session = userService.getSession(token);
        if (Objects.isNull(session)) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.UN_LOGIN);
        }

        // 查询企业经办人关联信息
        List<FCContactGrpRela> fcContactGrpRelaList = fcContactGrpRelaMapper.selectByGrpNo(grpContactReq.getGrpNo());

        // 判断经办人是否存在
        List<FCContactGrpRela> contactList = fcContactGrpRelaList.stream().filter(s -> grpContactReq.getContactNo().equals(s.getContactNo())).collect(Collectors.toList());
        if (contactList.isEmpty()) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.CONTACT_ERROR);
        }

        // 删除经办人第二经办人信息
        fcContactGrpRelaMapper.deleteByGrpNo(grpContactReq.getGrpNo());

        // 创建为第一经办人
        fcContactGrpRelaMapper.insertSelective(FCContactGrpRelaConvert.convert(grpContactReq, session.getUserNo()));
    }


    /**
     * 业务管理员新增Hr
     *
     * @param token
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertContact(String token, GrpContactInsertReq req) {

        // 用户信息查询
        GlobalInput globalInput = userService.getSession(token);
        if (Objects.isNull(globalInput)) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.UN_LOGIN);
        }

        // 企业信息查询
        String grpNo = req.getGrpNo();
        FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(grpNo);

        // 信息校验
        this.contactCheck(req);


        // 查询经办人信息
        FcGrpContact fcGrpContact = fcGrpContactMapper.selectGrpContactInfo(new FcGrpContact(req.getIdType(), req.getIdNo()));
        // 经办人编号
        String contactNo;
        if (Objects.isNull(fcGrpContact)) {
            // 创建用户信息
            String userNo = maxNoService.createMaxNo("UserNo", null, 20);
            FdUser fdUser = FdUserConvert.insertFdUser(req, userNo);
            log.info("insertContact fdUser:{}", JsonUtil.toJSON(fdUser));
            this.createUser(req, globalInput, fdUser);
            contactNo = fdUser.getCustomNo();
            sendSms(req.getMobilePhone(), fcGrpInfo.getGrpName(), SMSTemplateNoEnum.STAFF_BENEFITS_002.getCode());
        } else {
            log.info("insertContact fcGrpContact is not null");
            contactNo = fcGrpContact.getContactNo();
            // 修改经办人信息
            fcGrpContactMapper.updateByPrimaryKeySelective(FcGrpContactConvert.convert(req, globalInput.getUserNo(), contactNo));
            sendSms(req.getMobilePhone(), fcGrpInfo.getGrpName(), SMSTemplateNoEnum.STAFF_BENEFITS_003.getCode());
        }

        // 新企业经办人信息创建
        fcContactGrpRelaMapper.deleteByGrpNo(grpNo);
        fcContactGrpRelaMapper.insertSelective(FCContactGrpRelaConvert.convert(contactNo, globalInput.getUserNo(), grpNo));
    }


    private void contactCheck(GrpContactInsertReq req) {
        // 判断手机信息是否注册
        int checkByPhone = fdUserMapper.checkByPhone(req.getMobilePhone(), req.getIdNo(), "2");
        if (checkByPhone > 0) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.SIGN_IN_ERROR);
        }

        //校验国籍
        String checkNationalityCode = addressCheckService.checkNationalityCode(req.getNativeplace());
        if (StringUtils.isNotEmpty(checkNationalityCode)) {
            checkNationalityCode = checkNationalityCode.replace("***", req.getName());
            throw new EFlexServiceException(EFlexServiceExceptionEnum.CONTACT_ERROR.getCode(), "业务管理员新增Hr失败:" + checkNationalityCode);
        }

        String error = CheckUtils.checkSinglePeople(JsonUtil.fromJSON(JsonUtil.toJSON(req), Map.class));
        if (StringUtils.isNotBlank(error)) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.CONTACT_ERROR.getCode(), "业务管理员新增Hr失败 ,经办人 " + error);
        }

        // 手机格式校验
        if (!req.getMobile().matches("^1[2-9][0-9]{9}$")) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.CONTACT_ERROR.getCode(), "请输入正确手机号！");
        } else {
            req.setMobilePhone(req.getMobile());
        }

        // 校验证件号码和出生日期、性别是否一致
        if (req.getIdType().equals(IDTypeEnum.IDCARD.getCode()) || req.getIdType().equals(IDTypeEnum.HOUSEHOLDREGISTER.getCode())) {
            String checkIdCardResult = IDCardUtil.checkIDCard(req.getIdNo(), req.getSex(), req.getBirthDay());
            if (StringUtils.isNotBlank(checkIdCardResult)) {
                throw new EFlexServiceException(EFlexServiceExceptionEnum.CONTACT_ERROR.getCode(), checkIdCardResult);
            }
        }


        //校验二要素
        String failVerifies = addressCheckService.checkIdCard(IdCardVerifyConvert.convert(req.getName(), req.getIdNo(), req.getIdType()));
        if (StringUtils.isNotEmpty(failVerifies)) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.CONTACT_ERROR.getCode(), failVerifies);
        }

    }


    private void createUser(GrpContactInsertReq req, GlobalInput globalInput, FdUser fdUser) {
        String contactNo = maxNoService.createMaxNo("ContactNo", null, 20);
        fcGrpContactMapper.insert(FcGrpContactConvert.convert(req, globalInput.getUserNo(), contactNo));

        // 用户与菜单表
        fDusertomenugrpMapper.insert(FDUserRoleConvert.initFDusertomenugrpKey(fdUser));

        //密码历史表
        String passWordSn = maxNoService.createMaxNo("PassWordSN", null, 20);
        fdPwdHistMapper.insert(FDPwdHistConvert.convert(fdUser, passWordSn));

        // 用户角色表
        String userRoleSn = maxNoService.createMaxNo("UserRoleSN", null, 20);
        fdUserRoleMapper.insert(FDUserRoleConvert.insertFDUserRole(fdUser, userRoleSn));

        // 用户表
        fdUser.setCustomNo(contactNo);
        fdUserMapper.insert(fdUser);
    }


    private void sendSms(String mobilePhone, String grpName, String templateNo) {
        SendSMSReq sendSmsReq = new SendSMSReq();
        sendSmsReq.setTemplateNo(templateNo);
        sendSmsReq.setPhones(mobilePhone);
        Map<String, Object> map = new HashMap<>();
        map.put("grp_name", grpName);
        sendSmsReq.setParam(map);
        log.info("sendSms:{}", JsonUtil.toJSON(sendSmsReq));
        SendMessageResp sendMessageResp = sendMessageService.sendSMS(sendSmsReq);
        log.info("insertContact sendMessageResp {}", sendMessageResp);
    }


}
