package com.sinosoft.eflex.service.admin;

import com.sinosoft.eflex.model.vo.InsuranceTermsVO;

import java.util.List;

/**
 * 福利地址 *
 * 优化 EnsureMakeService 接口*
 *
 * <AUTHOR>
 */
public interface EnsureCustomService {


    /**
     * 保险条款列表*
     *
     * @param ensureCode 福利编号
     * @return 列表
     */
    List<InsuranceTermsVO> insuranceTermsList(String ensureCode);

    /**
     * 修改阅读状态*
     *
     * @param ensureCode 福利编号
     * @param riskCode   险种编号
     * @return boolean
     */
    boolean protocolReading(String ensureCode, String riskCode);


    /**
     * 福利定制下一步校验是否导入有效计划，员工
     *
     * @param ensureCode 福利编号
     * @return boolean
     */
    boolean importCheck(String ensureCode);
}
