package com.sinosoft.eflex.service.admin.impl;

import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.EnsureTypeEnum;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.service.EnsureMakeService;
import com.sinosoft.eflex.service.MaxNoService;
import com.sinosoft.eflex.util.CommonUtil;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.encrypt.LisIDEA;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 异步处理图片*
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AsyncAuditService {


    private final EnsureMakeService ensureMakeService;
    private final MaxNoService maxNoService;

    private final FCGrpInfoMapper fcGrpInfoMapper;
    private final FdUserMapper fdUserMapper;
    private final FCPerInfoTempMapper fcPerInfoTempMapper;
    private final FCPerInfoMapper fcPerInfoMapper;
    private final FCPersonMapper fcPersonMapper;
    private final FCEnsurePlanMapper fcEnsurePlanMapper;
    private final FCPerinfoFamilyTempMapper fcPerinfoFamilyTempMapper;
    private final FCStaffFamilyRelaMapper fcStaffFamilyRelaMapper;


    @Async("eFlexExecutor-2")
    @Transactional(rollbackFor = Exception.class)
    public void dataHandling(FCEnsure fcEnsure, GlobalInput globalInput, FCGrpInfo fcGrpInfo) {
        int peoples = 0;
        String ensureCode = fcEnsure.getEnsureCode();
        if (EnsureTypeEnum.GRPENSURE.getCode().equals(fcEnsure.getEnsureType()) || EnsureTypeEnum.SITEGRPENSURE.getCode().equals(fcEnsure.getEnsureType())) {
            List<HashMap<String, String>> listPerIfo = new ArrayList<>();
            Map<String, String> map = new HashMap<>();
            // 获取需要同步的人数
            List<FCPerInfoTemp> needSyncNum = ensureMakeService.getNeedSyncNum(ensureCode);
            peoples = peoples + needSyncNum.size();
            if (needSyncNum.size() > 0) {
                LisIDEA entryPassword = new LisIDEA();
                for (int i = 0; i < needSyncNum.size(); i++) {
                    Calendar cal = Calendar.getInstance();
                    int year = cal.get(Calendar.YEAR);
                    HashMap<String, String> hashMap = new HashMap<>();
                    hashMap.put("currentDate", DateTimeUtil.getCurrentDate());
                    hashMap.put("currentTime", DateTimeUtil.getCurrentTime());
                    hashMap.put("Operator", globalInput.getUserNo());
                    hashMap.put("relationship", needSyncNum.get(i).getRelationship());
                    hashMap.put("year", year + "");
                    FdUser fdUser = fdUserMapper.selectByPwd(needSyncNum.get(i).getIDNo());
                    if (fdUser == null) {
                        hashMap.put("PassWord", entryPassword.encryptString(needSyncNum.get(i).getIDNo().substring(needSyncNum.get(i).getIDNo().length() - 6)));
                    } else {
                        hashMap.put("PassWord", fdUser.getPassWord());
                    }
                    hashMap.put("ensureCode", fcEnsure.getEnsureCode());
                    hashMap.put("idNo", needSyncNum.get(i).getIDNo());
                    hashMap.put("nativeplace", needSyncNum.get(i).getNativeplace());
                    hashMap.put("IDType", needSyncNum.get(i).getIDType());
                    hashMap.put("levelCode", needSyncNum.get(i).getLevelCode());
                    hashMap.put("UserNo", maxNoService.createMaxNo("UserNo", "", 20));
                    hashMap.put("UserRole", maxNoService.createMaxNo("UserRole", "SN", 18));
                    hashMap.put("PwdSN", maxNoService.createMaxNo("PwdSN", "PWDSN", 15));
                    Map<String, Object> perInfoMap = new HashMap<>();
                    perInfoMap.put("IDNo", needSyncNum.get(i).getIDNo());
                    perInfoMap.put("grpNo", fcGrpInfo.getGrpNo());
                    List<FCPerInfo> fcPerInfo = fcPerInfoMapper.selectByExample(perInfoMap);
                    if (fcPerInfo.size() < 1) {
                        hashMap.put("PerNo", maxNoService.createMaxNo("PerNo", "", 20));
                    } else {
                        hashMap.put("PerNo", fcPerInfo.get(0).getPerNo());
                    }
                    Map<String, String> infoMap = new HashMap<>();
                    infoMap.put("grpNo", fcGrpInfo.getGrpNo());
                    infoMap.put("idNo", needSyncNum.get(i).getIDNo());
                    log.info("创建人员信息人员存在 ID::::::::{}", infoMap.get("idNo"));
                    FCPerson fcPerson = fcPersonMapper.getPerPersonID(infoMap);
                    if (fcPerson != null) {
                        log.info("创建人员信息人员存在PersonID::::::::{}", fcPerson.getPersonID());
                        hashMap.put("PersonId", fcPerson.getPersonID());
                    } else {
                        hashMap.put("PersonId", maxNoService.createMaxNo("PersonId", "", 20));
                        log.info("创建人员信息人员不存在PersonID::::::::{}", hashMap.get("PersonId"));
                    }
                    hashMap.put("seriaNo", maxNoService.createMaxNo("DefaultPlan", "", 20));
                    hashMap.put("RegDay", maxNoService.createMaxNo("RegDay", "", 20));
                    //判断该福利是固定计划还是弹性,固定计划，defaultPlan不为空
                    if ("0".equals(fcEnsure.getPlanType())) {
                        hashMap.put("StaffGrpPrem", String.valueOf(fcEnsurePlanMapper.selectPlanPrem(fcEnsure.getEnsureCode(), needSyncNum.get(i).getDefaultPlan())));
                    }
                    if ("1".equals(fcEnsure.getPlanType())) {
                        hashMap.put("StaffGrpPrem", "");
                    }
                    hashMap.put("grpNo", fcGrpInfo.getGrpNo());
                    log.info("创建人员信息hashMap::::::::{}", JsonUtil.toJSON(hashMap));
                    listPerIfo.add(hashMap);
                }
                log.info("同步人员信息集合listPerIfo准备完毕，开始同步人员。。。");
                map.put("ensureCode", fcEnsure.getEnsureCode());
                map.put("planType", fcEnsure.getPlanType());
                map.put("year", Calendar.getInstance().get(Calendar.YEAR) + "");
                map.put("currentDate", DateTimeUtil.getCurrentDate());
                map.put("currentTime", DateTimeUtil.getCurrentTime());
                map.put("grpNo", fcGrpInfo.getGrpNo());
                // 同步客户表FcPerInfo（更新）
                // 弹性计划 FcPerInfo 表中不存员工职级信息 update-20200417
                log.info("更新FcPerInfo表开始。。。");
                fcPerInfoTempMapper.updateFcPerInfo(map);
                log.info("更新FcPerInfo表完毕。。。");

                // 同步客户表FcPerSon（更新）
                log.info("更新FcPerSon表开始。。。");
                fcPerInfoTempMapper.updateFcPerSon(map);
                log.info("更新FcPerSon表完毕。。。");

                // 同步客户表FdUser（更新）
                log.info("更新FdUser开始。。。");
                fcPerInfoTempMapper.updateFdUser(map);
                log.info("更新FdUser完毕。。。");

                log.info("更新FCPerRegistDay开始。。。");
                //弹性计划 FCPerRegistDay 表中存员工职级信息 update-20200417
                fcPerInfoTempMapper.updateFCPerRegistDay(map);
                log.info("更新FCPerRegistDay完毕。。。");
                log.info("更新人员相关表完成，开始插入人员相关表。。。");

                // 同步客户表FcPerSon（插入）
                fcPerInfoTempMapper.insertFcPerSon(listPerIfo);
                // 同步家庭关系表FCStaffFamilyRela
                fcPerInfoTempMapper.insertFCStaffFamilyRela(listPerIfo);
                // 同步员工默认计划表fcDefaultPlan
                if (!"1".equals(fcEnsure.getPlanType())) {
                    fcPerInfoTempMapper.insertFCDefaultPlan(listPerIfo);
                }
                // 注册个人账号 custType : 1-个人账号
                fcPerInfoTempMapper.insertFdUser(listPerIfo);
                fcPerInfoTempMapper.insertFdUserRole(listPerIfo);
                fcPerInfoTempMapper.insertFdPwdHist(listPerIfo);
                fcPerInfoTempMapper.insertFDusertomenugrp(listPerIfo);
                // 同步员工注册期表 FCPerRegistDay
                //弹性计划 FCPerRegistDay 表中存员工职级信息 update-20200417
                fcPerInfoTempMapper.insertFCPerRegistDay(listPerIfo);
                // 同步客户表FcPerInfo（插入）
                //弹性计划 FcPerInfo 表中不存员工职级信息 update-20200417
                fcPerInfoTempMapper.insertFcPerInfo(listPerIfo);
                // 同步完成，更新临时表数据为已提交
                fcPerInfoTempMapper.updateFCPerinfoTemp(map);
                log.info("同步人员信息完成。。。");
            }
            //同步家属信息
            Map<String, Object> ensureInfoMap = new HashMap<>();
            ensureInfoMap.put("subStaus", "01");
            ensureInfoMap.put("ensureCode", fcEnsure.getEnsureCode());
            List<FCPerinfoFamilyTemp> fcPerinfoFamilyTempList = fcPerinfoFamilyTempMapper.getFamilyByEnsureCode(ensureInfoMap);
            if (fcPerinfoFamilyTempList.size() > 0) {
                map.put("ensureCode", ensureCode);
                map.put("year", Calendar.getInstance().get(Calendar.YEAR) + "");
                map.put("currentDate", DateTimeUtil.getCurrentDate());
                map.put("currentTime", DateTimeUtil.getCurrentTime());
                map.put("grpNo", fcGrpInfo.getGrpNo());
                fcPersonMapper.updateFcPerSonStudent(map);
                for (FCPerinfoFamilyTemp fcPerinfoFamilyTemp : fcPerinfoFamilyTempList) {
                    //获取员工家属关联表信息
                    FCStaffFamilyRela fcStaffFamilyRela = new FCStaffFamilyRela();
                    Map<String, String> familyInfo = new HashMap<>();
                    familyInfo.put("grpNo", fcGrpInfo.getGrpNo());
                    familyInfo.put("idNo", fcPerinfoFamilyTemp.getIDNo());
                    familyInfo.put("perIDNo", fcPerinfoFamilyTemp.getPerIDNo());
                    FCPerson fcPerson = fcPersonMapper.getFamPersonID(familyInfo);
                    if (fcPerson == null) {
                        HashMap<String, String> fcPersonMap = new HashMap<>();
                        fcPersonMap.put("familyTempNo", fcPerinfoFamilyTemp.getFamilyTempNo());
                        fcPersonMap.put("personID", maxNoService.createMaxNo("PersonID", null, 20));
                        fcPersonMap.put("ensureCode", fcPerinfoFamilyTemp.getEnsureCode());
                        fcPersonMap.put("IDType", fcPerinfoFamilyTemp.getIDType());
                        fcPersonMap.put("IDNo", fcPerinfoFamilyTemp.getIDNo());
                        fcPersonMap.put("operator", globalInput.getUserNo());
                        fcPersonMap.put("operatorCom", globalInput.getUserName());
                        fcPersonMap.put("makeDate", DateTimeUtil.getCurrentDate());
                        fcPersonMap.put("makeTime", DateTimeUtil.getCurrentTime());
                        fcPersonMap.put("modifyDate", DateTimeUtil.getCurrentDate());
                        fcPersonMap.put("modifyTime", DateTimeUtil.getCurrentTime());
                        fcPersonMap.put("relationship", "06");
                        fcPersonMapper.insertMap(fcPersonMap);
                        fcStaffFamilyRela.setPersonID(fcPersonMap.get("personID"));
                    } else {
                        fcStaffFamilyRela.setPersonID(fcPerson.getPersonID());
                    }
                    fcStaffFamilyRela.setRelation(fcPerinfoFamilyTemp.getRelation());
                    fcStaffFamilyRela.setOperator(globalInput.getUserNo());
                    Map<String, Object> isEmptyMap = new HashMap<>();
                    isEmptyMap.put("IDNo", fcPerinfoFamilyTemp.getPerIDNo());
                    isEmptyMap.put("grpNo", fcGrpInfo.getGrpNo());
                    List<FCPerInfo> fcPerInfos = fcPerInfoMapper.isExitPerInfo(isEmptyMap);
                    //调用之前已经写好的方法 返回List  一个企业下fcperinfo不循序重复，所以不会存在多个数据，既取第一条即可
                    if (fcPerInfos.size() > 0) {
                        fcStaffFamilyRela.setPerNo(fcPerInfos.get(0).getPerNo());
                    } else {
                        for (HashMap hashMap : listPerIfo) {
                            if (fcPerinfoFamilyTemp.getPerIDNo().equals(hashMap.get("idNo")) && fcPerinfoFamilyTemp.getPerIDType().equals(hashMap.get("IDType"))) {
                                fcStaffFamilyRela.setPerNo(String.valueOf(hashMap.get("PerNo")));
                            }
                        }
                    }
                    CommonUtil.initObject(fcStaffFamilyRela, "INSERT");
                    //同步员工家属关联表
                    Map<String, String> StaffFamilyMap = new HashMap<>();
                    StaffFamilyMap.put("perNo", fcStaffFamilyRela.getPerNo());
                    StaffFamilyMap.put("personID", fcStaffFamilyRela.getPersonID());
                    FCStaffFamilyRela fcStaffFamilyRela1 = fcStaffFamilyRelaMapper.selectByPrimaryKey(StaffFamilyMap);
                    if (fcStaffFamilyRela1 == null) {
                        fcStaffFamilyRelaMapper.insert(fcStaffFamilyRela);
                    }
                }
                fcPerinfoFamilyTempMapper.updateSubStaus(map);
            }
        }


        // 在校学生投保
        if (EnsureTypeEnum.STUDENTENSURE.getCode().equals(fcEnsure.getEnsureType())) {
            List<HashMap<String, String>> listPerIfo = new ArrayList<>();
            Map<String, String> map = new HashMap<>();
            // 获取需要同步的监护人总数
            List<Map<String, String>> needSyncNum = ensureMakeService.getNeedSyncGarNum(ensureCode);
            peoples = peoples + needSyncNum.size();
            if (needSyncNum.size() > 0) {
                LisIDEA encryPassword = new LisIDEA();
                for (int i = 0; i < needSyncNum.size(); i++) {
                    Calendar cal = Calendar.getInstance();
                    int year = cal.get(Calendar.YEAR);
                    HashMap<String, String> hashMap = new HashMap<>();
                    hashMap.put("currentDate", DateTimeUtil.getCurrentDate());
                    hashMap.put("currentTime", DateTimeUtil.getCurrentTime());
                    hashMap.put("Operator", globalInput.getUserNo());
                    hashMap.put("year", year + "");
                    hashMap.put("ensureCode", ensureCode);
                    hashMap.put("idNo", needSyncNum.get(i).get("IDNo"));
                    hashMap.put("nativeplace", needSyncNum.get(i).get("Nativeplace"));
                    hashMap.put("IDType", needSyncNum.get(i).get("IDType"));
                    hashMap.put("UserNo", maxNoService.createMaxNo("UserNo", "", 20));
                    hashMap.put("UserRole", maxNoService.createMaxNo("UserRole", "SN", 18));
                    hashMap.put("PwdSN", maxNoService.createMaxNo("PwdSN", "PWDSN", 15));
                    hashMap.put("RegDay", maxNoService.createMaxNo("RegDay", "", 20));
                    hashMap.put("StaffGrpPrem", "0.00");
                    hashMap.put("grpNo", fcGrpInfo.getGrpNo());
                    hashMap.put("perTempNo", needSyncNum.get(i).get("PerTempNo"));
                    //过渡Double类型
                    Map<String, String> ss = needSyncNum.get(i);
                    Object s = ss.get("studentGrpPrem");
                    if (s == null) {
                        s = 0.00;
                    }
                    hashMap.put("studentGrpPrem", String.valueOf(s));
                    FdUser fdUser = fdUserMapper.selectByPwd(needSyncNum.get(i).get("IDNo"));
                    if (fdUser == null) {
                        hashMap.put("PassWord", encryPassword.encryptString(needSyncNum.get(i).get("IDNo").substring(needSyncNum.get(i).get("IDNo").length() - 6)));
                    } else {
                        hashMap.put("PassWord", fdUser.getPassWord());
                    }

                    Map<String, Object> perInfoMap = new HashMap<>();
                    perInfoMap.put("IDNo", needSyncNum.get(i).get("IDNo"));
                    perInfoMap.put("grpNo", fcGrpInfo.getGrpNo());
                    List<FCPerInfo> fcPerInfo = fcPerInfoMapper.selectByExample(perInfoMap);
                    if (fcPerInfo.size() < 1) {
                        hashMap.put("PerNo", maxNoService.createMaxNo("PerNo", "", 20));
                    } else {
                        hashMap.put("PerNo", fcPerInfo.get(0).getPerNo());
                    }
                    listPerIfo.add(hashMap);
                }
                map.put("ensureCode", ensureCode);
                map.put("year", Calendar.getInstance().get(Calendar.YEAR) + "");
                map.put("currentDate", DateTimeUtil.getCurrentDate());
                map.put("currentTime", DateTimeUtil.getCurrentTime());
                map.put("grpNo", fcGrpInfo.getGrpNo());
                map.put("ensureType", fcEnsure.getEnsureType());
                map.put("planType", fcEnsure.getPlanType());
                // 同步客户表FcPerInfo（更新）
                log.info("更新FcPerInfo表开始。。。");
                fcPerInfoTempMapper.updateFcPerInfo(map);
                log.info("更新FcPerInfo表完毕。。。");
                // 同步客户表FdUser（更新）
                log.info("更新FdUser开始。。。");
                fcPerInfoTempMapper.updateFdUser(map);
                log.info("更新FdUser完毕。。。");
                log.info("同步人员信息集合listPerIfo准备完毕，开始同步人员。。。");
                fcPerInfoTempMapper.insertFdUser(listPerIfo);
                fcPerInfoTempMapper.insertFdUserRole(listPerIfo);
                fcPerInfoTempMapper.insertFdPwdHist(listPerIfo);
                fcPerInfoTempMapper.insertFDusertomenugrp(listPerIfo);
                // 同步员工注册期表 FCPerRegistDay
                fcPerInfoTempMapper.insertFCPerRegistDay(listPerIfo);
                // 同步客户表FcPerInfo（插入）
                fcPerInfoTempMapper.insertFcPerInfo(listPerIfo);
                // 同步完成，更新临时表数据为已提交
                fcPerInfoTempMapper.updateFCPerinfoTemp(map);
                // 同步学生信息
                Map<String, Object> ensureInfoMap = new HashMap<>();
                ensureInfoMap.put("subStaus", "01");
                ensureInfoMap.put("ensureCode", fcEnsure.getEnsureCode());
                List<FCPerinfoFamilyTemp> fcPerInfoFamilyTempList = fcPerinfoFamilyTempMapper.getFamilyByEnsureCode(ensureInfoMap);
                if (fcPerInfoFamilyTempList.size() > 0) {
                    Calendar cal = Calendar.getInstance();
                    int year = cal.get(Calendar.YEAR);
                    map.put("ensureCode", ensureCode);
                    map.put("year", Calendar.getInstance().get(Calendar.YEAR) + "");
                    map.put("currentDate", DateTimeUtil.getCurrentDate());
                    map.put("currentTime", DateTimeUtil.getCurrentTime());
                    map.put("grpNo", fcGrpInfo.getGrpNo());
                    map.put("planType", fcEnsure.getPlanType());
                    fcPersonMapper.updateFcPerSonStudent(map);
                    List<HashMap<String, String>> listStudentInfo = new ArrayList<>();
                    for (FCPerinfoFamilyTemp fcPerinfoFamilyTemp : fcPerInfoFamilyTempList) {
                        //获取监护人学生关联表信息
                        FCStaffFamilyRela fcStaffFamilyRela = new FCStaffFamilyRela();
                        Map<String, String> familyInfo = new HashMap<>();
                        familyInfo.put("grpNo", fcGrpInfo.getGrpNo());
                        familyInfo.put("idNo", fcPerinfoFamilyTemp.getIDNo());
                        familyInfo.put("perIDNo", fcPerinfoFamilyTemp.getPerIDNo());
                        FCPerson fcPerson = fcPersonMapper.getFamPersonID(familyInfo);
                        if (fcPerson == null) {
                            HashMap<String, String> fcPersonMap = new HashMap<>();
                            fcPersonMap.put("familyTempNo", fcPerinfoFamilyTemp.getFamilyTempNo());
                            fcPersonMap.put("personID", maxNoService.createMaxNo("PersonID", null, 20));
                            fcPersonMap.put("ensureCode", fcPerinfoFamilyTemp.getEnsureCode());
                            fcPersonMap.put("IDType", fcPerinfoFamilyTemp.getIDType());
                            fcPersonMap.put("IDNo", fcPerinfoFamilyTemp.getIDNo());
                            fcPersonMap.put("operator", globalInput.getUserNo());
                            fcPersonMap.put("operatorCom", globalInput.getUserName());
                            fcPersonMap.put("makeDate", DateTimeUtil.getCurrentDate());
                            fcPersonMap.put("makeTime", DateTimeUtil.getCurrentTime());
                            fcPersonMap.put("modifyDate", DateTimeUtil.getCurrentDate());
                            fcPersonMap.put("modifyTime", DateTimeUtil.getCurrentTime());
                            fcPersonMapper.insertMap(fcPersonMap);
                            fcStaffFamilyRela.setPersonID(fcPersonMap.get("personID"));
                        } else {
                            fcStaffFamilyRela.setPersonID(fcPerson.getPersonID());
                        }
                        fcStaffFamilyRela.setRelation(fcPerinfoFamilyTemp.getRelation());
                        fcStaffFamilyRela.setOperator(globalInput.getUserNo());
                        Map<String, Object> isEmptyMap = new HashMap<>();
                        isEmptyMap.put("IDNo", fcPerinfoFamilyTemp.getPerIDNo());
                        isEmptyMap.put("grpNo", fcGrpInfo.getGrpNo());
                        //调用之前已经写好的方法 返回List  一个企业下fcperinfo不循序重复，所以不会存在多个数据，既取第一条即可
                        List<FCPerInfo> fcPerInfos = fcPerInfoMapper.isExitPerInfo(isEmptyMap);
                        if (fcPerInfos.size() > 0) {
                            fcStaffFamilyRela.setPerNo(fcPerInfos.get(0).getPerNo());
                        } else {
                            for (HashMap<String, String> hashMap : listPerIfo) {
                                if (fcPerinfoFamilyTemp.getPerIDNo().equals(hashMap.get("idNo"))) {
                                    fcStaffFamilyRela.setPerNo(String.valueOf(hashMap.get("PerNo")));
                                }
                            }
                        }
                        CommonUtil.initObject(fcStaffFamilyRela, "INSERT");
                        //同步员工家属关联表
                        Map<String, String> staffFamilyMap = new HashMap<>();
                        staffFamilyMap.put("perNo", fcStaffFamilyRela.getPerNo());
                        staffFamilyMap.put("personID", fcStaffFamilyRela.getPersonID());
                        FCStaffFamilyRela fcStaffFamilyRela1 = fcStaffFamilyRelaMapper.selectByPrimaryKey(staffFamilyMap);
                        if (fcStaffFamilyRela1 == null) {
                            fcStaffFamilyRelaMapper.insert(fcStaffFamilyRela);
                        }
                        HashMap<String, String> listMap = new HashMap<>();
                        listMap.put("seriaNo", maxNoService.createMaxNo("DefaultPlan", "", 20));
                        listMap.put("grpNo", fcGrpInfo.getGrpNo());
                        listMap.put("personId", fcStaffFamilyRela.getPersonID());
                        listMap.put("ensureCode", fcEnsure.getEnsureCode());
                        listMap.put("year", year + "");
                        listMap.put("Operator", globalInput.getUserNo());
                        listMap.put("currentDate", DateTimeUtil.getCurrentDate());
                        listMap.put("currentTime", DateTimeUtil.getCurrentTime());
                        listMap.put("idNo", fcPerinfoFamilyTemp.getIDNo());
                        listMap.put("perTempNo", fcPerinfoFamilyTemp.getPerTempNo());
                        listStudentInfo.add(listMap);
                    }
                    //同步学生默认计划表fcDefaultPlan
                    fcPersonMapper.insertFCDefaultPlanStudent(listStudentInfo);
                    fcPerinfoFamilyTempMapper.updateSubStaus(map);
                    log.info("同步人员信息完成。。。");
                }
            }
        }
        peoples = peoples + fcGrpInfo.getPeoples();
        // 更新人数
        fcGrpInfoMapper.updateByPrimaryKeySelective(new FCGrpInfo(fcGrpInfo.getGrpNo(), peoples));
    }
}
