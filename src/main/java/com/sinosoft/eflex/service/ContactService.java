package com.sinosoft.eflex.service;

import ch.qos.logback.classic.Logger;
import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.dao.FCContactGrpRelaMapper;
import com.sinosoft.eflex.dao.FcGrpContactMapper;
import com.sinosoft.eflex.dao.FdUserMapper;
import com.sinosoft.eflex.model.FCContactGrpRela;
import com.sinosoft.eflex.model.FCContactGrpRelaKey;
import com.sinosoft.eflex.model.FcGrpContact;
import com.sinosoft.eflex.model.FdUser;
import com.sinosoft.eflex.model.GlobalInput;
import com.sinosoft.eflex.util.CheckUtils;
import com.sinosoft.eflex.util.CommonUtil;
import com.sinosoft.eflex.util.ResultUtil;
import com.sinosoft.eflex.util.StringUtil;
import com.sinosoft.eflex.enums.CustomTypeEnum;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @DESCRIPTION
 * @create 2018-08-09 19:55
 **/
@Service("ContactService")
@SuppressWarnings("unused")
public class ContactService {

    /**
     * 日志
     */
    private static Logger Log = (Logger) LoggerFactory.getLogger(ContactService.class);

    /**
     * 注入Dao层
     */
    @Autowired
    private FcGrpContactMapper grpContactMapper;
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private FdUserMapper fdUserMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private RegistService registService;
    @Autowired
    private FCContactGrpRelaMapper fcContactGrpRelaMapper;

    public String selectContact(String userCode) {

        return "";
    }

    /**
     * 企业第二联系人维护（新增/修改）
     * 1、第一次是新增企业第二联系人，后面维护都属于更新
     * 2、维护联系人表，维护账户表
     * 3、账户表：每次人员变更都（物理删除）删除账户并新增账户。
     * @param contact
     * @return
     */
    @Transactional
    public String maintainContact(String token, FcGrpContact contact,String isChangeState) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            //校验第二联系人信息
            Map<String, String> map1 = new HashMap<>();
            map1.put("sign", "3");//1：员工 2：家属  3：HR
            map1.put("idType", contact.getIdType());//证件类型
            map1.put("idNo", contact.getIdNo());//证件号
            map1.put("birthDay", contact.getBirthDay());//出生日期
            map1.put("sex", contact.getSex());//性别
            map1.put("nativeplace", contact.getNativeplace());//国籍
            map1.put("idTypeEndDate", contact.getIdTypeEndDate());//证件有效期
            map1.put("mobilePhone", contact.getMobilePhone());//证件有效期
            //校验姓名
            String errName = "";
            String name = contact.getName();
            String idType = contact.getIdType();
            if (!StringUtil.isEmpty(name) && !StringUtil.isEmpty(idType)) {
				if (idType.equals("1")) {
					errName = CheckUtils.checkForeignName(name);
				}else {
					errName = CheckUtils.checkChineseName(name);
				}
				if (!StringUtil.isEmpty(errName)) {
					return JSON.toJSONString(ResultUtil.error(errName));
				}
			}
            // 校验单个人的信息
			String resultMsg = CheckUtils.checkSinglePeople(map1);
            if(StringUtils.isNotBlank(resultMsg)) {
            	resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message",resultMsg);
                return JSON.toJSONString(resultMap);
            }
            if(!CheckUtils.checkMobilePhone(contact.getMobilePhone())){
            	resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "手机号格式错误，请检查");
                return JSON.toJSONString(resultMap);
            }
            int checkint = fdUserMapper.checkByPhone(contact.getMobilePhone(), contact.getIdNo(), "2");
            if (checkint > 0) {
                return JSON.toJSONString(ResultUtil.error("该手机号已注册！"));
            }
            //账户是否修改标识
            String isUpdateState = "N";
            //证件号置为大写
            if (contact.getIdType().equals("0")) {
                contact.setIdNo(contact.getIdNo().toUpperCase());
            }
            GlobalInput globalInput = userService.getSession(token);
            //第一联系人与第二联系人证件号不能相同
            Map<String, Object> paramss = new HashMap<String, Object>();
            paramss.put("grpNo", globalInput.getGrpNo());
            paramss.put("contactType", "01");
            List<FcGrpContact> contactList = grpContactMapper.selectContactsInfo(paramss);//置灰有一条
            if (contactList.size() > 1) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "企业第一联系人存在多条！");
                return JSON.toJSONString(resultMap);
            }
            if (contact.getIdNo().equals(contactList.get(0).getIdNo())) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "当前企业下第一联系人与第二联系人证件号不能相同！");
                return JSON.toJSONString(resultMap);
            }
        	//判断该企业是否存在第二联系人,不存在则创建,存在则更新
        	String contactNo = isExistTwoContact(globalInput.getGrpNo());
        	contact.setGrpNo(globalInput.getGrpNo());
        	contact.setOperator(globalInput.getUserNo());
        	//查询证件号是否已经存在
        	Map<String,String> params = new HashMap<String,String>();
            params.put("contactNo", contact.getContactNo());
            params.put("idNo", contact.getIdNo());
            int count = grpContactMapper.isExist(params);
			if (StringUtils.isBlank(contact.getIdTypeEndDate())){
				contact.setIdTypeEndDate(null);
			}
            /**该企业下无第二联系人*/
        	if("".equals(contactNo)){
        		//根据证件号判断是否已存在（0：不存在相同的证件号）
        		if(count==0){
        			//插入企业联系人表
        			contactNo=maxNoService.createMaxNo("ContactNo", "", 20);
        			contact.setContactNo(contactNo);
        			contact.setContactType("02");
        			contact = (FcGrpContact) CommonUtil.initObject(contact, "INSERT");
        			grpContactMapper.insert(contact);
        		}else {
        			//查询企业联系人编号
                    FcGrpContact fcGrpContact = grpContactMapper.selectGrpContact(contact.getIdNo());
        			//校验与提示操作
        			Map<String, Object> checkresultMap = isChange(fcGrpContact, contact, isChangeState);
        			if(checkresultMap.size()>1) {
        				return JSON.toJSONString(checkresultMap);
        			}
        			contactNo=fcGrpContact.getContactNo();
        			//更新企业联系人表
        			contact.setContactNo(fcGrpContact.getContactNo());
        			contact = (FcGrpContact) CommonUtil.initObject(contact, "UPDATE");
        			grpContactMapper.updateByPrimaryKeySelective(contact);
        			//账户信息是否应该删除
        			isUpdateState = "Y";
        		}
        		//插入企业与联系人关联表
        		FCContactGrpRela  fcContactGrpRela = new FCContactGrpRela();
        		fcContactGrpRela.setContactNo(contactNo);
        		fcContactGrpRela.setContactType("02");
        		fcContactGrpRela.setGrpNo(contact.getGrpNo());
        		fcContactGrpRela.setOperator(globalInput.getUserNo());
        		fcContactGrpRela = CommonUtil.initObject(fcContactGrpRela, "INSERT");
        		fcContactGrpRelaMapper.insertSelective(fcContactGrpRela);
        		
        	/**该企业下有第二联系人*/
        	}else {
        		//根据证件号判断是否已存在（0：不存在相同的证件号）
        		if(count == 0) {
        			//更新之前判断当前是否为关联企业
        			Map<String,Object>  deletemap = new HashMap<>();
        			deletemap.put("contactNo",contactNo);
          			int deleteRelaCount = grpContactMapper.selectContactCount(deletemap);
          			if(deleteRelaCount == 1) {//只有自己
          				//更新企业联系人表
          				contact.setContactNo(contactNo);
          				contact.setContactType("02");
          				contact = (FcGrpContact) CommonUtil.initObject(contact, "UPDATE");
          				grpContactMapper.updateByPrimaryKeySelective(contact);
          			}else{
          				//删除关系表
          				FCContactGrpRelaKey fcContactGrpRelaKey  = new FCContactGrpRelaKey();
            			fcContactGrpRelaKey.setContactNo(contactNo);
            			fcContactGrpRelaKey.setContactType("02");
            			fcContactGrpRelaKey.setGrpNo(contact.getGrpNo());
            			fcContactGrpRelaMapper.deleteByPrimaryKey(fcContactGrpRelaKey);
          			    //插入企业联系人表
            			contactNo=maxNoService.createMaxNo("ContactNo", "", 20);
            			contact.setContactNo(contactNo);
            			contact.setContactType("02");
            			contact = (FcGrpContact) CommonUtil.initObject(contact, "INSERT");
            			grpContactMapper.insert(contact);
            			//插入企业与联系人关联表
                		FCContactGrpRela  fcContactGrpRela = new FCContactGrpRela();
                		fcContactGrpRela.setContactNo(contactNo);
                		fcContactGrpRela.setContactType("02");
                		fcContactGrpRela.setGrpNo(contact.getGrpNo());
                		fcContactGrpRela.setOperator(globalInput.getUserNo());
                		fcContactGrpRela = CommonUtil.initObject(fcContactGrpRela, "INSERT");
                		fcContactGrpRelaMapper.insertSelective(fcContactGrpRela);
          			}
        		}else {
        			//查询企业联系人编号
                    FcGrpContact fcGrpContact = grpContactMapper.selectGrpContact(contact.getIdNo());
        			//校验与提示操作
        			Map<String, Object> checkresultMap = isChange(fcGrpContact, contact, isChangeState);
        			if(checkresultMap.size()>1) {
        				return JSON.toJSONString(checkresultMap);
        			}
        			//删除之前判断当前是否为关联企业
        			Map<String,Object>  deletemap = new HashMap<>();
        			deletemap.put("contactNo",contactNo);
          			int deleteRelaCount = grpContactMapper.selectContactCount(deletemap);
        			if(deleteRelaCount <= 1) {
        				//删除当前第二联系人
        				grpContactMapper.deleteByPrimaryKey(contactNo);
        				//如果存在第二联系人,则删除其对应的账户信息
        				registService.deleteResiter(contactNo);
        			}
        			//删除当前关系
        			FCContactGrpRelaKey fcContactGrpRelaKey  = new FCContactGrpRelaKey();
        			fcContactGrpRelaKey.setContactNo(contactNo);
        			fcContactGrpRelaKey.setContactType("02");
        			fcContactGrpRelaKey.setGrpNo(contact.getGrpNo());
        			fcContactGrpRelaMapper.deleteByPrimaryKey(fcContactGrpRelaKey);
        			contactNo=fcGrpContact.getContactNo();
        			//更新企业联系人表
            		contact.setContactNo(contactNo);
            		contact.setContactType("02");
            		contact = (FcGrpContact) CommonUtil.initObject(contact, "UPDATE");
            		grpContactMapper.updateByPrimaryKeySelective(contact);
            		//插入企业与联系人关联表
            		FCContactGrpRela  fcContactGrpRela = new FCContactGrpRela();
            		fcContactGrpRela.setContactNo(contactNo);
            		fcContactGrpRela.setContactType("02");
            		fcContactGrpRela.setGrpNo(contact.getGrpNo());
            		fcContactGrpRela.setOperator(globalInput.getUserNo());
            		fcContactGrpRela = CommonUtil.initObject(fcContactGrpRela, "INSERT");
            		fcContactGrpRelaMapper.insertSelective(fcContactGrpRela);
            		//账户信息是否应该删除
        			isUpdateState = "Y";
        		}
        		//账户信息是否应该删除
    			if(isUpdateState.equals("N")) {
    				//如果存在第二联系人,则删除其对应的账户信息
    				registService.deleteResiter(contactNo);
    			}
        	}
        	//账户信息新增or修改
			if(isUpdateState.equals("N")) {
				//插入企业联系人对应账户信息
                registService.register(contactNo, CustomTypeEnum.HR.getCode(), contact.getName(), contact.getIdNo(), contact.getMobilePhone(), "1");
			}else {
				List<FdUser> fdUserList=fdUserMapper.findUserByCustNo(contactNo);
	            for (FdUser fdUser:fdUserList) {
                    //修改企业联系人对应账户信息
                    FdUser user = new FdUser();
                    user.setUserNo(fdUser.getUserNo());
                    user.setUserName(contact.getIdNo());
                    user.setIDNo(contact.getIdNo());
                    user.setPhone(contact.getMobilePhone());
                    user.setOperator(fdUser.getUserNo());
                    user.setNickName(contact.getName());
                    user.setCustomNo(contactNo);
                    user.setUserState("1");
                    user = (FdUser) CommonUtil.initObject(user, "UPDATE");
                    fdUserMapper.updateByPrimaryKeySelective(user);
                }
			}
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "维护第二联系人信息成功");
        } catch (Exception e) {
            Log.info("维护第二联系人信息失败" + e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "维护第二联系人信息失败");
            throw new RuntimeException();
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 判断该企业是否存在第二联系人
     */
    public String isExistTwoContact(String grpNo){
        Map<String,Object> params = new HashMap<String,Object>();
        params.put("grpNo", grpNo);
        params.put("contactType", "02");
        List<FcGrpContact> contactList = grpContactMapper.selectContactsInfo(params);
        if(contactList.size()>0){
            return contactList.get(0).getContactNo();
        }else{
            return "";
        }
    }
    
    /**
     * 是否修改数据
     */
    public Map<String,Object> isChange(FcGrpContact fcGrpContact,FcGrpContact contact,String isChangeState){
    	Map<String, Object> resultMap = new HashMap<String, Object>();
		String message = "";
        if (!fcGrpContact.getName().equals(contact.getName())) {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "您当前维护的企业第二联系人信息已存在，姓名必须和已存在的企业联系人保持一致！");
            return resultMap;
        }
        if (!fcGrpContact.getIdType().equals(contact.getIdType())) {
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "您当前维护的企业第二联系人信息已存在，证件类型必须和已存在的企业联系人保持一致！");
            return resultMap;
        }
		//isChangeState => Y：确认  N：没有确认
		if(!isChangeState.equals("Y")) {
			//国籍与证件有效期为后加字段。
			if(StringUtils.isNotBlank(fcGrpContact.getNativeplace())) {
				if(!fcGrpContact.getNativeplace().equals(contact.getNativeplace())) {
					message += "国籍";
				}
			}
			if(StringUtils.isNotBlank(fcGrpContact.getIdTypeEndDate())) {
				if(!fcGrpContact.getIdTypeEndDate().equals(contact.getIdTypeEndDate())) {
					if(StringUtils.isNotBlank(message)) {
						message +=",";
					}
					message += "证件有效期";
				}
			}
			if(!fcGrpContact.getSex().equals(contact.getSex())) {
				if(StringUtils.isNotBlank(message)) {
					message +=",";
				}
				message += "性别";
			}
			if(!fcGrpContact.getBirthDay().equals(contact.getBirthDay())) {
				if(StringUtils.isNotBlank(message)) {
					message +=",";
				}
				message += "出生日期";
			}  
			if(!fcGrpContact.getMobilePhone().equals(contact.getMobilePhone())) {
				if(StringUtils.isNotBlank(message)) {
					message +=",";
				}
				message += "手机号";
			} 
			if(StringUtils.isNotBlank(message)) {
				resultMap.put("success", false);
				resultMap.put("code", "203");
				resultMap.put("message", "您当前维护的企业第二联系人信息已存在，但由于"+message+"与已存在的企业联系人信息不相符，请您核实确认是否修改。");
				return resultMap;
			}
		}
    	return resultMap;
    }
    
    
    
    
    
}
