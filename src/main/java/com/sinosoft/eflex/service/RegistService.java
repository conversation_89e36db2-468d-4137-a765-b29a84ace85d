package com.sinosoft.eflex.service;

import ch.qos.logback.classic.Logger;
import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.dao.FDPwdHistMapper;
import com.sinosoft.eflex.dao.FDUserRoleMapper;
import com.sinosoft.eflex.dao.FDValidateCodeMapper;
import com.sinosoft.eflex.dao.FDusertomenugrpMapper;
import com.sinosoft.eflex.dao.FdUserMapper;
import com.sinosoft.eflex.model.FDPwdHist;
import com.sinosoft.eflex.model.FDUserRole;
import com.sinosoft.eflex.model.FDValidateCode;
import com.sinosoft.eflex.model.FDusertomenugrpKey;
import com.sinosoft.eflex.model.FdUser;
import com.sinosoft.eflex.util.CommonUtil;
import com.sinosoft.eflex.util.encrypt.LisIDEA;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @DESCRIPTION
 * @create 2018-08-21 10:18
 **/
@Service
public class RegistService {

    private static Logger Log = (Logger) LoggerFactory.getLogger(UserService.class);
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private FdUserMapper fdUserMapper;
    @Autowired
    private FDUserRoleMapper fdUserRoleMapper;
    @Autowired
    private FDPwdHistMapper fdPwdHistMapper;
    @Autowired
    private FDValidateCodeMapper  fdValidateCodeMapper;
    @Autowired
    private FDusertomenugrpMapper fDusertomenugrpMapper;

    public void register(String custNo,String custType, String name, String idNo, String phone, String userState) throws RuntimeException{

            FdUser user = new FdUser();
            String userNo = maxNoService.createMaxNo("UserNo", "", 20);
            user.setUserNo(userNo);
            user.setUserName(idNo);
            user.setIDNo(idNo);
            user.setPhone(phone);
            user.setOperator(userNo);
            user.setNickName(name);
            LisIDEA encryPassword=new LisIDEA();
            user.setPassWord(encryPassword.encryptString(idNo.substring(idNo.length()-6)));
            user.setCustomType(custType);
            user.setCustomNo(custNo);
            user.setIsLock("0");
            user.setIsVIP("N");
            //Update by zch 2020/11/12 PWDState修改为0，由于HR要走首次登陆强制更改密码
            user.setPWDState("0");//HQ要求屏蔽发短信功能
            user.setUserState(userState);
            user.setLoginFailTimes(0);
            user.setPhone(phone);
            user=(FdUser) CommonUtil.initObject(user,"INSERT");
            fdUserMapper.insert(user);
            FDUserRole userRole = new FDUserRole();
            userRole.setUserRoleSN(maxNoService.createMaxNo("UserRole", "SN",18));
            userRole.setUserNo(userNo);
            userRole.setRoleType(custType);
            userRole.setOperator("这是操作员");
            userRole = (FDUserRole)CommonUtil.initObject(userRole,"INSERT");
            fdUserRoleMapper.insert(userRole);
            FDPwdHist pwd = new FDPwdHist();
            String passWordSN = maxNoService.createMaxNo("PwdSN","PWDSN",15);
            pwd.setPassWordSN(passWordSN);
            pwd.setUserNo(userNo);
            pwd.setPassWord(user.getPassWord());
            pwd.setOperator("这是操作员");
            pwd = (FDPwdHist)CommonUtil.initObject(pwd , "INSERT");
            fdPwdHistMapper.insert(pwd);

            FDusertomenugrpKey fDusertomenugrpKey=new FDusertomenugrpKey();
            fDusertomenugrpKey.setUserNo(userNo);
        // if("1".equals(custType)||custType=="1"){
        // fDusertomenugrpKey.setMenuGrpCode("2");
        // }else if("2".equals(custType)||custType=="2"){
        // fDusertomenugrpKey.setMenuGrpCode("1");
        // }
        // 更正了上述蹩脚的对换 -- add by wudezhong 2021.8.13
        fDusertomenugrpKey.setMenuGrpCode(custType);
            fDusertomenugrpMapper.insert(fDusertomenugrpKey);
    }

    /**
     * 判断员工是否注册。
     * true 已注册
     * false 未注册
     * @param custNo
     * @return
     */
    public boolean isRegist(String custNo){
        // 查询用户信息
        List<FdUser> usertList = fdUserMapper.findUserByCustNo(custNo);
        if(usertList.size()>0){
            return true;
        }else{
            return false;
        }
    }



    @Transactional
    public String deleteResiter(String custNo){
        Map<String,String> resultMap=new HashMap<>();
        try {
              //删除用户表
             List<FdUser> fdUserList=fdUserMapper.findUserByCustNo(custNo);
             for (FdUser fdUser:fdUserList) {
                String userNo=fdUser.getUserNo();
                fdUserMapper.deleteByPrimaryKey(userNo);
                Log.info("删除用户表成功");
                //删除历史密码表
                List<FDPwdHist> fdPwdHistList=fdPwdHistMapper.selectPwdByUserno(userNo);
                for (FDPwdHist fdPwdHist:fdPwdHistList) {
                     String pwd=fdPwdHist.getPassWordSN();
                     fdPwdHistMapper.deleteByPrimaryKey(pwd);
                }
                Log.info("删除历史密码表成功");
                //删除角色表
                fdUserRoleMapper.deleteByUserNo(userNo);
                Log.info("删除角色表成功");
                //删除动态密码表
                List<FDValidateCode> fdValidateCodeList=fdValidateCodeMapper.selectByUserNo(userNo);
                for (FDValidateCode fdValidateCode:fdValidateCodeList) {
                    String validatecodesn=fdValidateCode.getValidatecodesn();
                    fdValidateCodeMapper.deleteByPrimaryKey(validatecodesn);
                }
                Log.info("删除动态密码表成功");
            }
            resultMap.put("message","删除成功");
            resultMap.put("code","200");
        }catch (Exception e){
            Log.info("删除失败", e);
            resultMap.put("code","500");
            resultMap.put("message","删除失败");
        }
        return JSON.toJSONString(resultMap);
    }

}
