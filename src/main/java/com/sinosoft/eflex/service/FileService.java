package com.sinosoft.eflex.service;

import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import com.sinosoft.eflex.util.rest.exception.SystemException;
import org.apache.commons.collections.map.HashedMap;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.ocr.OcrIdCardInfo;
import com.sinosoft.eflex.model.ocr.OcrRepuest;
import com.sinosoft.eflex.model.ocr.OcrResponse;
import com.sinosoft.eflex.model.ocr.OcrResponseData;
import com.sinosoft.eflex.service.edor.EdorService;
import com.sinosoft.eflex.util.*;

import ch.qos.logback.classic.Logger;

import javax.imageio.ImageIO;
import javax.management.relation.Relation;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * person类的crud,这是一个示例
 *
 * <AUTHOR>
 */
@Service("FileService")
@SuppressWarnings("unused")
public class FileService {

    // 日志
    private static Logger Log = (Logger) LoggerFactory.getLogger(FileService.class);
    // 注入Dao层
    @Autowired
    private FCFileDocMainMapper fcFileDocMainMapper;
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private EdorService edorService;
    @Autowired
    private MyProps myProps;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private FCEnsurePlanMapper fcEnsurePlanMapper;
    @Autowired
    private FCBusPersonTypeMapper fcBusPersonTypeMapper;
    @Autowired
    private FCGrpOrderMapper fcGrpOrderMapper;
    @Autowired
    private FCEdorAddPlanInfoMapper fcEdorAddPlanInfoMapper;
    @Autowired
    private FCEdorAddPlanRiskInfoMapper fcEdorAddPlanRiskInfoMapper;
    @Autowired
    private FCEdorAddPlanRiskDutyInfoMapper fcEdorAddPlanRiskDutyInfoMapper;
    @Autowired
    private FDFTPInfoMapper fdftpInfoMapper;
    @Autowired
    private FCPersonImageMapper fcPersonImageMapper;
    @Autowired
    private UserService userService;
    /**
     * 实现文件上传
     *
     * @param file     文件对象
     * @param path     文件存放路径
     * @param fileType 文件类型
     * @param busiNo   业务号
     */
    public Boolean fileUpload(MultipartFile file, String path, String fileType, String busiNo) {
        if (file.isEmpty()) {
            Log.info("文件上传失败：上传文件为空！");
            return false;
        }

        File dest = new File(path);
        if (!dest.getParentFile().exists()) { // 判断文件父目录是否存在
            dest.getParentFile().mkdir();
        }
        try {
            file.transferTo(dest); // 保存文件
            Log.info("文件：" + path + "   上传成功！");
            // 文件信息存入文件表及业务关联表表
            String docID = maxNoService.createMaxNo("DocID", "", 20);
            String fileName = file.getOriginalFilename();
            String suffixFileName = fileName.substring(fileName.lastIndexOf("."));
            fileName = fileName.substring(0, fileName.lastIndexOf("."));
            FCFileDocMain fcFileDocMain = new FCFileDocMain();
            fcFileDocMain.setDocID(docID);
            fcFileDocMain.setDocType(fileType);
            fcFileDocMain.setFileName(path);
            fcFileDocMain.setFileSaveName(fileName);
            fcFileDocMain.setFileSuffix(suffixFileName);
            fcFileDocMain.setFilePath("");
            fcFileDocMain.setValidFlag("1");
            fcFileDocMain.setOperator("");
            CommonUtil.initObject(fcFileDocMain, "INSERT");
            fcFileDocMainMapper.insertSelective(fcFileDocMain);
        } catch (IllegalStateException | IOException e) {
            Log.info("实现文件上传:", e);
            return false;
        }
        return true;
    }

    /**
     * 多文件上传
     *
     * @param files
     * @return
     */
    public String multifileUpload(Map<String, MultipartFile> files) {
        Map<String, Object> filePathMap = new HashMap<>();
        filePathMap.put("success", false);
        filePathMap.put("code", "500");
        try {
            //查询ftp信息上传到SFTP
            FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
            //获取文件上传相对路径  0305-企业注册文件夹
            String ftpFilePath = fdftpInfo.getFtprootpath() + FileUtil.getFtpPath("0305", "");
            SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(), fdftpInfo.getUsername(), fdftpInfo.getPassword());
            filePathMap = sFtp.uploadSftp(files, ftpFilePath, filePathMap);
            if ("200".equals(filePathMap.get("code"))) {
                filePathMap.put("success", true);
                filePathMap.put("message", "相关影像件需要上传成功");
            }
        } catch (Exception e) {
            Log.info("文件上传：" + e);
            filePathMap.put("message", "注册相关文件上传失败");
        }
        return JSON.toJSONString(filePathMap);
    }


    public ResponseEntity<InputStreamResource> downloadFile(String docId, String ensureCode) throws IOException, InvalidFormatException {
        Log.info("福利编号为》》》" + ensureCode + "&&&docid为" + docId);
        FCFileDocMain fcFileDocMain = fcFileDocMainMapper.selectByPrimaryKey(docId);
        String filePath = fcFileDocMain.getFilePath();
        String filename = fcFileDocMain.getFileSaveName() + fcFileDocMain.getFileSuffix();
        String tempFilePath = filePath + filename;
        if (!docId.equals("00000015") && fcFileDocMain.getDocType().equals("0202")) {//人员清单模板
            tempFilePath = creatDefaulePlanSheet(fcFileDocMain, ensureCode);
        }
        if (docId.equals("00000015") && fcFileDocMain.getDocType().equals("0202")) {//人员弹性清单模板
            tempFilePath = createRankSheet(fcFileDocMain, ensureCode);
        }
        if ("00000020".equals(docId)) {
            tempFilePath = createRankSheet(fcFileDocMain, ensureCode);
        }
        //健康告知详情清单模板，填入职级信息
        if ("00000017".equals(docId) && "0503".equals(fcFileDocMain.getDocType())) {
            tempFilePath = createHealthRankSheet(fcFileDocMain, ensureCode);
        }
        Log.info("下载文件路径为》》》" + tempFilePath);
        FileSystemResource file = new FileSystemResource(tempFilePath);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
        /*headers.add("Content-Disposition", "attachment; filename=" + new String((fcFileDocMain.getFileName()+fcFileDocMain.getFileSuffix()).getBytes("UTF-8"), "iso-8859-1"));*/
        String filename8859 = new String((fcFileDocMain.getFileName() + fcFileDocMain.getFileSuffix()).getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
        String filenameutf8 = null;
        try {
            filenameutf8 = URLEncoder.encode((fcFileDocMain.getFileName() + fcFileDocMain.getFileSuffix()), StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        headers.add("Content-Disposition", "attachment;filename=" + filename8859 + ";filename*=utf-8''" + filenameutf8);
        headers.add("Pragma", "no-cache");
        headers.add("Expires", "0");
        return ResponseEntity.ok().headers(headers).contentLength(file.contentLength())
                .contentType(MediaType.parseMediaType("application/octet-stream"))
                .body(new InputStreamResource(file.getInputStream()));
    }

    //保全增人下载增人模版(按计划)  生成计划Sheet页
    public String createEdorAddPlanSheet(FCFileDocMain fcFileDocMain, String ensureCode) {
        String path = fcFileDocMain.getFilePath();
        String filename = fcFileDocMain.getFileSaveName() + fcFileDocMain.getFileSuffix();
        Workbook workbook;
        FileInputStream inStream = null;
        FileOutputStream out = null;
        String tempFilePath = "";
        try {
            FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(ensureCode);
            String grpContNo = fcGrpOrder.getGrpContNo();
            inStream = new FileInputStream(new File(path + filename));
            workbook = WorkbookFactory.create(inStream);

            Sheet sheet = workbook.createSheet(); // 添加一个新的sheet
            workbook.setSheetName(9, "保单计划信息表");


            //获取该保单下计划集合
            List<FCEdorAddPlanInfo> fcEdorAddPlanInfoList = fcEdorAddPlanInfoMapper
                    .getEdorPlanListByGrpContNo(grpContNo);
            for (int i = 0; i < fcEdorAddPlanInfoList.size(); i++) {

            }
            //获取该保单下所有险种集合（去重）
            List<FCEdorAddPlanRiskInfo> fcEdorAddPlanRiskInfoList = fcEdorAddPlanRiskInfoMapper.getEdorPlanRiskListBygrpContNo(grpContNo);
            //获取该保单下所有责任集合（去重）
            List<FCEdorAddPlanRiskDutyInfo> fcEdorAddPlanRiskDutyInfoList = fcEdorAddPlanRiskDutyInfoMapper.getDutyInfoByGrpCotNo(grpContNo);
            //获取该保单下责任属性集合（去重）
            Calendar cal = Calendar.getInstance();
            Date date = cal.getTime();
            tempFilePath = path + "temp" + File.separator + ensureCode + new SimpleDateFormat("yyyyMddHHmmssSSS").format(date) + fcFileDocMain.getFileSuffix();
            Log.info("临时文件路径为》》》" + tempFilePath);
            File file = new File(tempFilePath);
            //获取父目录
            File fileParent = file.getParentFile();
            //判断是否存在
            if (!fileParent.exists()) {
                //创建父目录文件
                fileParent.mkdirs();
            }
            file.createNewFile();
            out = new FileOutputStream(tempFilePath);//要输出的文件名字
            workbook.write(out);//写出文件
        } catch (Exception e) {
            Log.info("", e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (inStream != null) {
                    inStream.close();
                }
            } catch (IOException e) {
                Log.info("", e);
            }
        }
        return tempFilePath;
    }

    /**
     * 表格样式设置
     *
     * @param wb
     * @return
     */
    private CellStyle cellStyleBorder(Workbook wb) {
        CellStyle inCellStyle = wb.createCellStyle();
        inCellStyle.setBorderBottom(BorderStyle.THIN);
        inCellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        inCellStyle.setBorderLeft(BorderStyle.THIN);
        inCellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        inCellStyle.setBorderRight(BorderStyle.THIN);
        inCellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        inCellStyle.setBorderTop(BorderStyle.THIN);
        inCellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        return inCellStyle;
    }

    public String creatDefaulePlanSheet(FCFileDocMain fcFileDocMain, String ensureCode) {
        String path = fcFileDocMain.getFilePath();
        String filename = fcFileDocMain.getFileSaveName() + fcFileDocMain.getFileSuffix();
        Workbook workbook;
        FileInputStream inStream = null;
        FileOutputStream out = null;
        String tempFilePath = "";
        try {
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            inStream = new FileInputStream(new File(path + filename));
            workbook = WorkbookFactory.create(inStream);
            Sheet sheet = workbook.createSheet(); // 添加一个新的sheet
            if ("0".equals(fcEnsure.getEnsureType()) || "2".equals(fcEnsure.getEnsureType())) {
                workbook.setSheetName(8, "默认计划信息");
            } else if ("1".equals(fcEnsure.getEnsureType())) {
                workbook.setSheetName(6, "默认计划信息");
            }
            sheet.setColumnWidth(0, 30 * 256);
            sheet.setColumnWidth(1, 50 * 256);
            sheet.setColumnWidth(2, 30 * 256);
            sheet.setColumnWidth(3, 30 * 256);
            Row myRow = sheet.createRow(0);//创建 并设置第一行
            CellStyle style = workbook.createCellStyle();

            style.setAlignment(HorizontalAlignment.CENTER);// 对齐方式
            style.setBorderBottom(BorderStyle.THIN);// 上下左右边框
            style.setBorderLeft(BorderStyle.THIN);
            style.setBorderRight(BorderStyle.THIN);
            style.setBorderTop(BorderStyle.THIN);
            style.setFillForegroundColor(IndexedColors.AQUA.getIndex());
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            Font font = workbook.createFont();//设置字体样式
            font.setFontName("宋体");
            font.setFontHeightInPoints((short) 12);
            font.setBold(true);
            style.setFont(font);

            Cell cell = myRow.createCell((short) 0);
            cell.setCellStyle(style);
            cell.setCellValue("计划编号");

            cell = myRow.createCell((short) 1);
            cell.setCellStyle(style);
            cell.setCellValue("计划名称");

            cell = myRow.createCell((short) 2);
            cell.setCellStyle(style);
            cell.setCellValue("计划对象");

            cell = myRow.createCell((short) 3);
            cell.setCellStyle(style);
            cell.setCellValue("计划重点");

            Map<String, Object> params = new HashMap<String, Object>();
            params.put("ensureCode", ensureCode);
            List<FCEnsurePlan> fcEnsurePlanList = fcEnsurePlanMapper.selectFCEnsurePlans(params);
            // 遍历集合数据，产生数据行
            if (fcEnsurePlanList != null && fcEnsurePlanList.size() > 0) {
                int index = 1;
                for (FCEnsurePlan fCEnsurePlan : fcEnsurePlanList) {
                    myRow = sheet.createRow(index);
                    Cell cell0 = myRow.createCell((short) 0);
                    cell0.setCellValue(fCEnsurePlan.getPlanCode());
                    Cell cell1 = myRow.createCell((short) 1);
                    cell1.setCellValue(fCEnsurePlan.getPlanName());
                    Cell cell2 = myRow.createCell((short) 2);
                    if (fCEnsurePlan.getPlanObject().equals("3")) {
                        cell2.setCellValue("学生");
                    } else {
                        cell2.setCellValue(fCEnsurePlan.getPlanObject().equals("1") ? "员工" : "家属");
                    }
                    Cell cell3 = myRow.createCell((short) 3);
                    cell3.setCellValue(fCEnsurePlan.getPlanKey());
                    index++;
                }
            }
            //如果是员工的话才创建职级表，若是学生的话此张表省略
            if ("0".equals(fcEnsure.getEnsureType()) || "2".equals(fcEnsure.getEnsureType())) {
                //创建员工职级表
                Sheet sheetOne = workbook.createSheet(); // 添加一个新的sheet
                workbook.setSheetName(9, "员工职级");
                sheetOne.setColumnWidth(0, 30 * 256);
                sheetOne.setColumnWidth(1, 50 * 256);
                sheetOne.setColumnWidth(2, 30 * 256);
                sheetOne.setColumnWidth(3, 30 * 256);
                Row myRowOne = sheetOne.createRow(0);//创建 并设置第一行
                CellStyle styleOne = workbook.createCellStyle();

                styleOne.setAlignment(HorizontalAlignment.CENTER);// 对齐方式
                styleOne.setBorderBottom(BorderStyle.THIN);// 上下左右边框
                styleOne.setBorderLeft(BorderStyle.THIN);
                styleOne.setBorderRight(BorderStyle.THIN);
                styleOne.setBorderTop(BorderStyle.THIN);
                styleOne.setFillForegroundColor(IndexedColors.AQUA.getIndex());
                styleOne.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                Font fontOne = workbook.createFont();//设置字体样式
                fontOne.setFontName("宋体");
                fontOne.setFontHeightInPoints((short) 12);
                fontOne.setBold(true);
                styleOne.setFont(font);

                Cell cellOne = myRowOne.createCell((short) 0);
                cellOne.setCellStyle(style);
                cellOne.setCellValue("序号");

                cellOne = myRowOne.createCell((short) 1);
                cellOne.setCellStyle(style);
                cellOne.setCellValue("职级编码");

                cellOne = myRowOne.createCell((short) 2);
                cellOne.setCellStyle(style);
                cellOne.setCellValue("职级描述");

                cellOne = myRowOne.createCell((short) 3);
                cellOne.setCellStyle(style);
                cellOne.setCellValue("职级顺序");

                Map<String, Object> paramsOne = new HashMap<String, Object>();
                paramsOne.put("ensureCode", ensureCode);

                //查询数据
                List<FCBusPersonType> fcBusPersonTypelist = new ArrayList<>();
                fcBusPersonTypelist = fcBusPersonTypeMapper.selectByGrpNo(fcEnsureMapper.selectByPrimaryKey(ensureCode).getGrpNo());
                // 遍历集合数据，产生数据行
                if (fcBusPersonTypelist != null && fcBusPersonTypelist.size() > 0) {
                    int index = 1;
                    for (FCBusPersonType fcBusPersonType : fcBusPersonTypelist) {
                        myRowOne = sheetOne.createRow(index);
                        Cell cell0 = myRowOne.createCell((short) 0);
                        cell0.setCellValue(index);
                        Cell cell1 = myRowOne.createCell((short) 1);
                        cell1.setCellValue(fcBusPersonType.getGradeLevelCode() + "");
                        Cell cell2 = myRowOne.createCell((short) 2);
                        cell2.setCellValue(fcBusPersonType.getGradeDesc());
                        Cell cell3 = myRowOne.createCell((short) 3);
                        cell3.setCellValue(fcBusPersonType.getOrderNum());
                        index++;
                    }
                }
            }
            Calendar cal = Calendar.getInstance();
            Date date = cal.getTime();
            tempFilePath = path + "temp" + File.separator + ensureCode + new SimpleDateFormat("yyyyMddHHmmssSSS").format(date) + fcFileDocMain.getFileSuffix();
            Log.info("临时文件路径为》》》" + tempFilePath);
            File file = new File(tempFilePath);
            //获取父目录
            File fileParent = file.getParentFile();
            //判断是否存在
            if (!fileParent.exists()) {
                //创建父目录文件
                fileParent.mkdirs();
            }
            file.createNewFile();
            out = new FileOutputStream(tempFilePath);//要输出的文件名字
            workbook.write(out);//写出文件
        } catch (Exception e) {
            Log.info("", e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (inStream != null) {
                    inStream.close();
                }
            } catch (IOException e) {
                Log.info("", e);
            }
        }
        return tempFilePath;
    }

    public String createRankSheet(FCFileDocMain fcFileDocMain, String ensureCode) {
        String path = fcFileDocMain.getFilePath();
        String filename = fcFileDocMain.getFileSaveName() + fcFileDocMain.getFileSuffix();
        Workbook workbook;
        FileInputStream inStream = null;
        FileOutputStream out = null;
        String tempFilePath = "";
        try {
            inStream = new FileInputStream(new File(path + filename));
            workbook = WorkbookFactory.create(inStream);
            Sheet sheet = workbook.createSheet(); // 添加一个新的sheet
            if ("00000020".equals(fcFileDocMain.getDocID())) {
                workbook.setSheetName(4, "员工职级");
            }
            if ("00000015".equals(fcFileDocMain.getDocID())) {
                workbook.setSheetName(7, "员工职级");
            }
            sheet.setColumnWidth(0, 30 * 256);
            sheet.setColumnWidth(1, 50 * 256);
            sheet.setColumnWidth(2, 30 * 256);
            sheet.setColumnWidth(3, 30 * 256);
            Row myRow = sheet.createRow(0);//创建 并设置第一行
            CellStyle style = workbook.createCellStyle();

            style.setAlignment(HorizontalAlignment.CENTER);// 对齐方式
            style.setBorderBottom(BorderStyle.THIN);// 上下左右边框
            style.setBorderLeft(BorderStyle.THIN);
            style.setBorderRight(BorderStyle.THIN);
            style.setBorderTop(BorderStyle.THIN);
            style.setFillForegroundColor(IndexedColors.AQUA.getIndex());
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            Font font = workbook.createFont();//设置字体样式
            font.setFontName("宋体");
            font.setFontHeightInPoints((short) 12);
            font.setBold(true);
            style.setFont(font);

            Cell cell = myRow.createCell((short) 0);
            cell.setCellStyle(style);
            cell.setCellValue("序号");

            cell = myRow.createCell((short) 1);
            cell.setCellStyle(style);
            cell.setCellValue("职级编码");

            cell = myRow.createCell((short) 2);
            cell.setCellStyle(style);
            cell.setCellValue("职级描述");

            cell = myRow.createCell((short) 3);
            cell.setCellStyle(style);
            cell.setCellValue("职级顺序");

            Map<String, Object> params = new HashMap<String, Object>();
            params.put("ensureCode", ensureCode);

            //查询数据
            List<FCBusPersonType> fcBusPersonTypelist = new ArrayList<>();
            //企业员工管理下载模板  esureCode传的是grpNo
            if ("00000020".equals(fcFileDocMain.getDocID())) {
                fcBusPersonTypelist = fcBusPersonTypeMapper.selectByGrpNo(ensureCode);
            } else {
                fcBusPersonTypelist = fcBusPersonTypeMapper.selectByGrpNo(fcEnsureMapper.selectByPrimaryKey(ensureCode).getGrpNo());
            }
            // 遍历集合数据，产生数据行
            if (fcBusPersonTypelist != null && fcBusPersonTypelist.size() > 0) {
                int index = 1;
                for (FCBusPersonType fcBusPersonType : fcBusPersonTypelist) {
                    myRow = sheet.createRow(index);
                    Cell cell0 = myRow.createCell((short) 0);
                    cell0.setCellValue(index);
                    Cell cell1 = myRow.createCell((short) 1);
                    cell1.setCellValue(fcBusPersonType.getGradeLevelCode() + "");
                    Cell cell2 = myRow.createCell((short) 2);
                    cell2.setCellValue(fcBusPersonType.getGradeDesc());
                    Cell cell3 = myRow.createCell((short) 3);
                    cell3.setCellValue(fcBusPersonType.getOrderNum());
                    index++;
                }
            }
            Calendar cal = Calendar.getInstance();
            Date date = cal.getTime();
            tempFilePath = path + "temp" + File.separator + ensureCode + new SimpleDateFormat("yyyyMddHHmmssSSS").format(date) + fcFileDocMain.getFileSuffix();
            Log.info("临时文件路径为》》》" + tempFilePath);
            File file = new File(tempFilePath);
            //获取父目录
            File fileParent = file.getParentFile();
            //判断是否存在
            if (!fileParent.exists()) {
                //创建父目录文件
                fileParent.mkdirs();
            }
            file.createNewFile();
            out = new FileOutputStream(tempFilePath);//要输出的文件名字
            workbook.write(out);//写出文件
        } catch (Exception e) {
            Log.info("", e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (inStream != null) {
                    inStream.close();
                }
            } catch (IOException e) {
                Log.info("", e);
            }
        }
        return tempFilePath;
    }

    //下载方案详情模板
    public String createHealthRankSheet(FCFileDocMain fcFileDocMain, String ensureCode) {
        String path = fcFileDocMain.getFilePath();
        String filename = fcFileDocMain.getFileSaveName() + fcFileDocMain.getFileSuffix();
        Workbook workbook;
        FileInputStream inStream = null;
        FileOutputStream out = null;
        String tempFilePath = "";
        try {
            inStream = new FileInputStream(new File(path + filename));
            workbook = WorkbookFactory.create(inStream);
            Sheet sheet = workbook.createSheet(); // 添加一个新的sheet
            workbook.setSheetName(2, "职级");
            sheet.setColumnWidth(0, 30 * 256);
            sheet.setColumnWidth(1, 50 * 256);
            sheet.setColumnWidth(2, 30 * 256);
            sheet.setColumnWidth(3, 30 * 256);
            Row myRow = sheet.createRow(0);//创建 并设置第一行
            CellStyle style = workbook.createCellStyle();

            style.setAlignment(HorizontalAlignment.CENTER);// 对齐方式
            style.setBorderBottom(BorderStyle.THIN);// 上下左右边框
            style.setBorderLeft(BorderStyle.THIN);
            style.setBorderRight(BorderStyle.THIN);
            style.setBorderTop(BorderStyle.THIN);
            style.setFillForegroundColor(IndexedColors.AQUA.getIndex());
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            Font font = workbook.createFont();//设置字体样式
            font.setFontName("宋体");
            font.setFontHeightInPoints((short) 12);
            font.setBold(true);
            style.setFont(font);

            Cell cell = myRow.createCell((short) 0);
            cell.setCellStyle(style);
            cell.setCellValue("序号");

            cell = myRow.createCell((short) 1);
            cell.setCellStyle(style);
            cell.setCellValue("职级编码");

            cell = myRow.createCell((short) 2);
            cell.setCellStyle(style);
            cell.setCellValue("职级描述");

            cell = myRow.createCell((short) 3);
            cell.setCellStyle(style);
            cell.setCellValue("职级顺序");

            Map<String, Object> params = new HashMap<String, Object>();
            params.put("ensureCode", ensureCode);

            //查询数据
            List<FCBusPersonType> fcBusPersonTypelist = fcBusPersonTypeMapper.selectByGrpNo(fcEnsureMapper.selectByPrimaryKey(ensureCode).getGrpNo());
            // 遍历集合数据，产生数据行
            style = workbook.createCellStyle();
            style.setAlignment(HorizontalAlignment.CENTER);// 对齐方式
            style.setBorderBottom(BorderStyle.THIN);// 上下左右边框
            style.setBorderLeft(BorderStyle.THIN);
            style.setBorderRight(BorderStyle.THIN);
            style.setBorderTop(BorderStyle.THIN);
            if (fcBusPersonTypelist != null && fcBusPersonTypelist.size() > 0) {
                int index = 1;
                for (FCBusPersonType fcBusPersonType : fcBusPersonTypelist) {
                    myRow = sheet.createRow(index);
                    Cell cell0 = myRow.createCell((short) 0);
                    cell0.setCellValue(index);
                    cell0.setCellStyle(style);
                    Cell cell1 = myRow.createCell((short) 1);
                    cell1.setCellStyle(style);
                    cell1.setCellValue(fcBusPersonType.getGradeLevelCode() + "");
                    Cell cell2 = myRow.createCell((short) 2);
                    cell2.setCellValue(fcBusPersonType.getGradeDesc());
                    cell2.setCellStyle(style);
                    Cell cell3 = myRow.createCell((short) 3);
                    cell3.setCellValue(fcBusPersonType.getOrderNum());
                    cell3.setCellStyle(style);
                    index++;
                }
            }
            Calendar cal = Calendar.getInstance();
            Date date = cal.getTime();
            tempFilePath = path + "temp" + File.separator + ensureCode + new SimpleDateFormat("yyyyMddHHmmssSSS").format(date) + fcFileDocMain.getFileSuffix();
            Log.info("临时文件路径为》》》" + tempFilePath);
            File file = new File(tempFilePath);
            //获取父目录
            File fileParent = file.getParentFile();
            //判断是否存在
            if (!fileParent.exists()) {
                //创建父目录文件
                fileParent.mkdirs();
            }
            file.createNewFile();
            out = new FileOutputStream(tempFilePath);//要输出的文件名字
            workbook.write(out);//写出文件
        } catch (Exception e) {
            Log.info("", e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (inStream != null) {
                    inStream.close();
                }
            } catch (IOException e) {
                Log.info("", e);
            }
        }
        return tempFilePath;
    }


    public boolean deleteFile(String fileName) {
        File file = new File(fileName);
        // 如果文件路径所对应的文件存在，并且是一个文件，则直接删除
        if (file.exists() && file.isFile()) {
            if (file.delete()) {
                Log.info("删除临时文件\" + fileName + \"成功！");
                return true;
            } else {
                Log.info("删除临时文件\" + fileName + \"失败！");
                return false;
            }
        } else {
            Log.info("删除临时文件失败：\" + fileName + \"不存在！");
            return false;
        }
    }

    public void test() {
		/*String file_name = "用户导入模板.xlsx";
		try{
			OutputStream os = response.getOutputStream();
			String fileName = new String(file_name.getBytes("GBK"),"ISO-8859-1");
			InputStream is = this.getClass().getClassLoader().getResourceAsStream("excelTemplate/" + file_name);
			response.setContentType("application/force-download");
			response.addHeader("Content-Disposition","attachment;fileName=" + fileName);
			int len = 0;
			byte[] b = new byte[1024];
			while ((len = is.read(b, 0, b.length)) != -1) {
				os.write(b, 0, len);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}*/
    }

    public String getFileOrcInfo(String filPath) {
        filPath = formatPath(filPath);
        String str = uploadImg(filPath, myProps.getOcrUrl(), "FRONT");
        return str;
    }

    public String formatPath(String path) {
        // 将路径中的斜杠统一
        char[] chars = path.toCharArray();
        StringBuffer sbStr = new StringBuffer(256);
        for (int i = 0; i < chars.length; i++) {
            if ('\\' == chars[i]) {
                sbStr.append('/');
            } else {
                sbStr.append(chars[i]);
            }
        }
        path = sbStr.toString();
        return path;
    }

    public String uploadImg(String filePath, String ocrUrl, String cardSide) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        String rspStr = "";
        try {
            OcrRepuest ocrRepuest = new OcrRepuest();
            ocrRepuest.setTransCode(myProps.getOcrTransCode());
            ocrRepuest.setTransTime(DateTimeUtil.getCurrentDateTime());
            ocrRepuest.setChannelSource(myProps.getOcrChannelSource());
            ocrRepuest.setActionType(myProps.getOcrActionType());
            ocrRepuest.setCardSide(cardSide);
            ocrRepuest.setAction(myProps.getOcrAction());
            ocrRepuest.setImageBase64(Base64AndMD5Util.ImageToBase64(filePath));
            String jsonString = JSONObject.toJSONString(ocrRepuest);
            Log.info("ocr识别请求报文》》》" + jsonString);
            String postHttpRequestJson = HttpUtil.postHttpRequestJson(ocrUrl, jsonString, myProps.getOcrApp_id(),
                    myProps.getOcrApp_secret());
            JSONObject jsStr = JSONObject.parseObject(postHttpRequestJson);
            OcrResponse ocrResponse = JSONObject.toJavaObject(jsStr, OcrResponse.class);
            String code = ocrResponse.getCode();
            JSONObject jsonObject = new JSONObject();
            if (code.equals("200")) {
                OcrResponseData data = ocrResponse.getData();
                if (!StringUtil.isEmpty(data)) {
                    String errCode = data.getErrCode();
                    if (errCode.equals("0")) {
                        OcrIdCardInfo idCardInfo = data.getIdCardInfo();
                        if (!StringUtil.isEmpty(idCardInfo)) {
                            String idNum = idCardInfo.getIdNum();
                            jsonObject.put("rsp_code", "0000");
                            if (!StringUtil.isEmpty(idNum)) {// 正面
                                jsonObject.put("type", "第二代身份证");
                                jsonObject.put("name", idCardInfo.getName());
                                jsonObject.put("sex", idCardInfo.getSex());
                                jsonObject.put("birthday", DateTimeUtil.fillDateWillZero(idCardInfo.getBirthday()));
                                jsonObject.put("id_number", idNum);
                            }
                        } else {
                            jsonObject.put("rsp_code", "0002");
                        }
                    } else {
                        if (cardSide.equals("BACK")) {
                            jsonObject.put("rsp_code", "0002");
                        } else {
                            return uploadImg(filePath, ocrUrl, "BACK");
                        }
                    }
                } else {
                    jsonObject.put("rsp_code", "0002");
                }
            } else {
                if (cardSide.equals("BACK")) {
                    jsonObject.put("rsp_code", "0002");
                } else {
                    return uploadImg(filePath, ocrUrl, "BACK");
                }

            }
            resultMap.put("data", jsonObject.toJSONString());
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "ocr第三方接口调用成功");
        } catch (Exception e) {
            Log.info("ocr第三方接口调用失败", e);
            resultMap.put("data", e.getMessage());
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "ocr第三方接口调用失败");
        }
        return JSON.toJSONString(resultMap);
    }

    public boolean fileDisplay(HttpServletResponse response, String sftpPath) {
        //查询ftp信息上传到SFTP
        boolean bool = false;
        try {
            FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
            SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(), fdftpInfo.getUsername(), fdftpInfo.getPassword());
            bool = sFtp.FileDisplay(response, sftpPath);
        } catch (Exception e) {
            e.printStackTrace();
            Log.info("文件回显，连接服务器失败。");
            return false;
        }
        return bool;
    }

    public boolean dailyFileDisplay(HttpServletResponse response, String sftpPath) {
        try {
            InputStream in = new FileInputStream(new File(sftpPath));
            BufferedImage bi = ImageIO.read(in);
            response.setContentType("image/png");
            ServletOutputStream out = response.getOutputStream();
            ImageIO.write(bi, "png", out);
            try {
                out.flush();
            } finally {
                out.close();
//				if(in!=null) {
//					in.close();
//				}
            }
        } catch (IOException e) {
            e.printStackTrace();
            Log.error("文件读取错误。");
            return false;
        }
        return true;
    }

    public String dailyFileUpload(String token, Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        try {
            String file = map.get("file").toString();
            String fileType = map.get("fileType").toString();
            String orderItemNo = map.get("orderItemNo").toString();
            String relation = map.get("relation").toString();
            Log.info("dailyFileUpload入参为:token:{} ,orderitemNo:{}, relation:{},fileType:{} ,file:{}", token, orderItemNo, relation, fileType, file);
            MultipartFile multipartFile = BASE64DecodedMultipartFile.base64ToMultipart(file);
            String fileName = multipartFile.getOriginalFilename();
            FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
            SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(),
                    fdftpInfo.getUsername(), fdftpInfo.getPassword());
            if (fileType.equals("0804")) {
                // 删除历史签名信息
                Map<String, String> params = new HashMap<>();
                params.put("orderItemNo", orderItemNo);
                params.put("relation", relation);
                params.put("fileType", fileType);
                List<FCPersonImage> selectImages = fcPersonImageMapper.selectImages(params);
                selectImages.forEach((FCPersonImage fcPersonImage) -> {
//    				String imageFtpUrl = fcPersonImage.getImageFtpUrl();
                    fcPersonImageMapper.deleteByPrimaryKey(fcPersonImage.getImageNo());
                });
            }
            // 1、存储到fcpersonimage
            String imageNo = insertFcpersonImage(fdftpInfo.getFtprootpath(), orderItemNo, relation, fileType,
                    globalInput.getCustomNo(), fileName);
            // 2、上传至ftp
            boolean bool = sFtp.FileFtpDisplay(multipartFile, FileUtil.getFtpPath(fdftpInfo.getFtprootpath(), fileType, orderItemNo, relation), fileName);
//    		fileUpload(multipartFile, FileUtil.getLocalPath(fileType, orderItemNo, relation)+fileName, fileType, "");
            resultMap.put("imageNo", imageNo);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "上传成功");
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "影像件上传失败");
            return JSON.toJSONString(resultMap);
        }
        return JSON.toJSONString(resultMap);
    }

    public String insertFcpersonImage(String ftprootpath, String orderItemNo, String relation, String fileType, String operator, String fileName) {
        Integer imageOrder = fcPersonImageMapper.getImageOrderInfo(orderItemNo, fileType);
        FCPersonImage fcPersonImage = new FCPersonImage();
        String imageNo = maxNoService.createMaxNo("ImageNo", "", 20);
        fcPersonImage.setImageNo(imageNo);
        fcPersonImage.setOrderItemNo(orderItemNo);
        fcPersonImage.setRelation(relation);
        fcPersonImage.setImageType(fileType);
        fcPersonImage.setImageOrder(StringUtil.isEmpty(imageOrder) ? "1" : imageOrder + "");
        fcPersonImage.setOperator(operator);
        fcPersonImage.setImageUrl(FileUtil.getFtpPath(ftprootpath, fileType, orderItemNo, relation) + fileName);
        fcPersonImage = CommonUtil.initObject(fcPersonImage, "INSERT");
        fcPersonImageMapper.insertSelective(fcPersonImage);
        return imageNo;
    }

    public String dailyFileRemove(String imageNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            FCPersonImage fCPersonImage = fcPersonImageMapper.selectByPrimaryKey(imageNo);
//    		FileUtil.delete(new File(fCPersonImage.getImageUrl()));
            FDFTPInfo fdftpInfo = fdftpInfoMapper.selectByPrimaryKey("001");
            SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(),
                    fdftpInfo.getUsername(), fdftpInfo.getPassword());
            String imageUrl = fCPersonImage.getImageUrl();
            String[] split = imageUrl.split("/");
            sFtp.delete(imageUrl.replace(split[split.length - 1], ""), split[split.length - 1]);
            fcPersonImageMapper.deleteByPrimaryKey(imageNo);
            resultMap.put("imageNo", imageNo);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "删除成功");
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "删除失败");
            return JSON.toJSONString(resultMap);
        }
        return JSON.toJSONString(resultMap);
    }

    // 下载指定路径的文件
    public ResponseEntity<InputStreamResource> downloadAppointFilePath(HttpServletRequest request, String filePath,
                                                                       String fileName) {
        FileSystemResource file = new FileSystemResource(filePath);
        if (file.exists()) {
            try {
                String downloadFileName = "";
                // 浏览器乱码兼容性处理
                String userAgent = request.getHeader("user-agent").toLowerCase();
                if (userAgent.contains("msie") || userAgent.contains("trident") || userAgent.contains("edge")) {
                    downloadFileName = URLEncoder.encode(fileName, "UTF-8");
                } else {
                    downloadFileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
                }
                HttpHeaders headers = new HttpHeaders();
                headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
                headers.add("Content-Disposition", "attachment; filename=" + downloadFileName);
                headers.add("Pragma", "no-cache");
                headers.add("Expires", "0");
                return ResponseEntity.ok().headers(headers).contentLength(file.contentLength())
                        .contentType(MediaType.parseMediaType("application/octet-stream"))
                        .body(new InputStreamResource(file.getInputStream()));
            } catch (Exception e) {
                e.printStackTrace();
                throw new SystemException("文件下载失败！");
            }
        } else {
            throw new SystemException("文件不存在，无法下载！");
        }
    }
}
