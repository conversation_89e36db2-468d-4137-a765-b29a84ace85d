package com.sinosoft.eflex.service.dailyinsure.impl;

import com.hqins.common.base.utils.AssertUtil;
import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.dao.FCEnsureMapper;
import com.sinosoft.eflex.dao.FCGrpInfoMapper;
import com.sinosoft.eflex.dao.FCGrpOrderMapper;
import com.sinosoft.eflex.dao.FCPerInfoMapper;
import com.sinosoft.eflex.framework.exception.EFlexServiceException;
import com.sinosoft.eflex.framework.exception.EFlexServiceExceptionEnum;
import com.sinosoft.eflex.model.AddressEntity.convert.IdCardVerifyConvert;
import com.sinosoft.eflex.model.FCEnsure;
import com.sinosoft.eflex.model.FCGrpInfo;
import com.sinosoft.eflex.model.FCGrpOrder;
import com.sinosoft.eflex.model.GlobalInput;
import com.sinosoft.eflex.model.convert.FcEnsureConvert;
import com.sinosoft.eflex.service.AddressCheckService;
import com.sinosoft.eflex.service.UserService;
import com.sinosoft.eflex.service.dailyinsure.DailyPlanInsureService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 日常计划*
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DailyPlanInsureServiceImpl implements DailyPlanInsureService {


    private final AddressCheckService addressCheckService;
    private final UserService userService;
    private final AsyncDailyPlanInsureService asyncDailyPlanInsureService;

    private final FCPerInfoMapper fcPerInfoMapper;
    private final FCEnsureMapper fcEnsureMapper;
    private final FCGrpOrderMapper fcGrpOrderMapper;

    private final FCGrpInfoMapper fcGrpInfoMapper;


    @Override
    public void dailyPlanInsure(String token, String ensureCode) {

        // 登陆态信息
        GlobalInput globalInput = userService.getSession(token);
        log.info("dailyPlanInsure globalInput :{}", JsonUtil.toJSON(globalInput));

        // 福利信息
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
        fcEnsure.setOperator(globalInput.getUserNo());
        log.info("dailyPlanInsure fcEnsure :{}", JsonUtil.toJSON(fcEnsure));
        AssertUtil.isTrue("1".equals(fcEnsure.getPolicyState()), new EFlexServiceException(EFlexServiceExceptionEnum.POLICY_STATE_ERROR));

        // 团单信息
        FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(ensureCode);
        log.info("dailyPlanInsure fcEnsure :{}", JsonUtil.toJSON(fcGrpOrder));
        AssertUtil.isTrue(!ObjectUtils.isEmpty(fcGrpOrder), new EFlexServiceException(EFlexServiceExceptionEnum.GRP_ORDER_NULL_ERROR));

        // 获取企业信息
        FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(fcGrpOrder.getGrpNo());
        log.info("dailyPlanInsure fcGrpInfo :{}", JsonUtil.toJSON(fcGrpInfo));

        // 根据平台投保单号和待发送状态 查找到对应核心投保单号
        String tPrtNo = fcGrpOrderMapper.selectbyPrtNo06(fcGrpOrder.getPrtNo());


        List<Map<String, String>> maps = fcPerInfoMapper.selectInsuredPerson_Alls(ensureCode);
        // 校验二要素
        String failVerifies = addressCheckService.checkIdCard(IdCardVerifyConvert.convert(maps));
        AssertUtil.isTrue(StringUtils.isEmpty(failVerifies), new EFlexServiceException(EFlexServiceExceptionEnum.ID_CARD_ERROR), failVerifies);

        // 根据平台投保单号和待发送状态 查找到对应核心投保单号
        this.updateFcPrtAndCoreRela(tPrtNo, "01", "已发送!");

        // 修改状态
        fcEnsureMapper.updateByPrimaryKeySelective(FcEnsureConvert.convert(ensureCode));

        // 异步调用核心
        asyncDailyPlanInsureService.asyncDailyPlanInsure(fcEnsure, fcGrpOrder, tPrtNo, fcGrpInfo);

    }


    /**
     * 状态更新*
     *
     * @param tprtNo   核心投保单号
     * @param status   发送状态
     * @param describe 发送状态
     */
    public void updateFcPrtAndCoreRela(String tprtNo, String status, String describe) {
        Map<String, String> map = new HashMap<>(3);
        map.put("tPrtNo", tprtNo);
        map.put("status", status);
        map.put("describe", describe);
        fcGrpOrderMapper.updateFcPrtAndCoreRela(map);
    }

}
