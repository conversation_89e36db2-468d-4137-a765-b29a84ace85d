package com.sinosoft.eflex.service.dailyinsure;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import com.hqins.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.dailyplan.ImageReq;
import com.sinosoft.eflex.model.face.*;
import com.sinosoft.eflex.model.pay.PayCenterGetPayUrl;
import com.sinosoft.eflex.model.pay.PayCenterGetPayUrlResponse;
import com.sinosoft.eflex.model.pay.PayCenterPayStatusQuery;
import com.sinosoft.eflex.model.pay.PayCenterPayStatusQueryResponse;
import com.sinosoft.eflex.service.EnsureMakeService;
import com.sinosoft.eflex.service.MaxNoService;
import com.sinosoft.eflex.service.PremTrailService;
import com.sinosoft.eflex.service.UserService;
import com.sinosoft.eflex.util.*;
import com.sinosoft.eflex.enums.PayFreqEnum;
import com.sinosoft.eflex.enums.PayFrequencyEnum;

/**
 * <AUTHOR> wenying Xia
 * @date : 2020-05-12 11:27
 **/
@Service
@Slf4j
public class DailyPlanPhoneService {
    /**
     * 日志
     */
    private static Logger Log = (Logger) LoggerFactory.getLogger(DailyPlanPhoneService.class);

    /**
     * 注入Dao层
     */
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private FDCodeMapper fdCodeMapper;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private FcDailyInsureRiskDetailInfoMapper fcDailyInsureRiskDetailInfoMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private FCPersonMapper fcPersonMapper;
    @Autowired
    private FCPerInfoMapper fcPerInfoMapper;
    @Autowired
    private FCOrderMapper fcOrderMapper;
    @Autowired
    private FCOrderItemMapper fcOrderItemMapper;
    @Autowired
    private MyProps myProps;
    @Autowired
    private FCFileDocMainMapper fcFileDocMainMapper;
    @Autowired
    private FCAppntImpartInfoPersonalMapper fcAppntImpartInfoPersonalMapper;
    @Autowired
    private FCOrderItemDetailMapper fcOrderItemDetailMapper;
    @Autowired
    private FCOrderInsuredMapper fcOrderInsuredMapper;
    @Autowired
    private FCGrpOrderMapper fcGrpOrderMapper;
    @Autowired
    private PremTrailService premTrailService;
    @Autowired
    private FCPersonImageMapper fcPersonImageMapper;
    @Autowired
    private FCStaffFamilyRelaMapper fcStaffFamilyRelaMapper;
    @Autowired
    private FdUserMapper fdUserMapper;
    @Autowired
    private FCEnsureConfigMapper fcEnsureConfigMapper;
    @Autowired
    private StaffInsureService staffInsureService;
    @Autowired
    private DailyIssueService dailyIssueService;
    @Autowired
    private FCOrderBnfRelaMapper fcOrderBnfRelaMapper;
    @Autowired
    private FCOrderBnfMapper fcOrderBnfMapper;
    @Autowired
    private FCPerRegistDayMapper fcPerRegistDayMapper;
    @Autowired
    private EnsureMakeService ensureMakeService;
    @Autowired
    private FcDailyInsureRiskInfoMapper fcDailyInsureRiskInfoMapper;

    /**
     * 查询企业
     *
     * @param authorization
     * @return
     */
    public String getGrpForTheperson(String authorization) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(authorization);
            String perNo = globalInput.getCustomNo();
            List<Map<String, String>> fcPerInfos = fcPerInfoMapper.selectgrpListByPerNo(perNo);
            resultMap.put("success", true);
            resultMap.put("fcPerInfos", fcPerInfos);
            resultMap.put("code", "200");
            resultMap.put("message", "查询企业成功！");
        } catch (Exception e) {
            Log.info("查询企业失败:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查询企业失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 查看企业福利
     *
     * @param authorization
     * @param grpNo
     * @return
     */
    public String showEnsureForPeople(String authorization, String grpNo) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(authorization);
        try {
            if (StringUtil.isEmpty(grpNo)) {
                Log.info("grpNo 为空！！");
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "参数不齐全");
                return JSON.toJSONString(resultMap);
            }
            // 查询投保开放期结束的福利编号
            Map<String, Object> params = new HashMap<String, Object>();
            // 保单状态 1-投保中 2-待承保 3-已承保 4-承保结束 5-承保失败   年度计划保单状态时 1.。而 日常计划保单状态是 3
//            params.put("policyState", "3");
            // 所属公司
            params.put("grpNo", grpNo);
            // 系统当前时间大于开放投保开放日期，小于投保结束日期
            params.put("sysDate", DateTimeUtil.getCurrentDate());
            List<FCEnsureGrpInfo> fcEnsureList_Dailys = fcEnsureMapper.selectEnsureList_Dailys(params);//包括日常计划
            List<FCEnsureGrpInfo> fcEnsureList_Dailys1 = new ArrayList<>();
            // 去除包含下架产品或者是年金产品的日常福利
            for (FCEnsureGrpInfo fcEnsureGrpInfo : fcEnsureList_Dailys) {
                String checkRiskStopSale = ensureMakeService.checkRiskStopSale(fcEnsureGrpInfo.getEnsureCode());
                // 日常计划
                List<String> riskCodelist = fcDailyInsureRiskInfoMapper
                        .selectDailyRiskCodeByEnsureCode(fcEnsureGrpInfo.getEnsureCode());
                if (StringUtil.isEmpty(checkRiskStopSale) && !riskCodelist.contains("14110")) {
                    fcEnsureList_Dailys1.add(fcEnsureGrpInfo);
                } else {
                    Log.info("日常福利{}中含有下架的产品。", fcEnsureGrpInfo.getEnsureCode());
                }
            }
            params.clear();
            params.put("perNo", globalInput.getCustomNo());
            params.put("grpNo", grpNo);
            List<FCEnsureGrpInfo> fcEnsureList_years = fcEnsureMapper.selectAllEnsureandGrpNo(params);
            List<FCEnsureGrpInfo> fcEnsureList_years1 = new ArrayList<>();
            //去除下架产品的日常福利
            for (FCEnsureGrpInfo fcEnsureGrpInfo : fcEnsureList_years) {
                String checkRiskStopSale = ensureMakeService.checkRiskStopSale(fcEnsureGrpInfo.getEnsureCode());
                if (StringUtil.isEmpty(checkRiskStopSale)) {
                    fcEnsureList_years1.add(fcEnsureGrpInfo);
                } else {
                    Log.info("固定、弹性福利{}中含有下架的产品。", fcEnsureGrpInfo.getEnsureCode());
                }
            }
            fcEnsureList_Dailys1.addAll(fcEnsureList_years1);
            for (FCEnsureGrpInfo fcEnsureGrpInfo : fcEnsureList_Dailys) {
                String planType = fcEnsureGrpInfo.getPlanType();
                if (planType.equals("2")) {
                    fcEnsureGrpInfo.setEnsureName("横琴福裕团体重大疾病保险");
                }
            }

            if (fcEnsureList_Dailys1.size() == 0 || fcEnsureList_Dailys1 == null) {
                Log.info("亲，您所在的企业未开放福利产品哦~");
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "亲，您所在的企业未开放福利产品哦~");
                return JSON.toJSONString(resultMap);
            }


            resultMap.put("fcEnsureList", fcEnsureList_Dailys1);
            resultMap.put("fcEnsureList_Dailys", fcEnsureList_Dailys1);
            resultMap.put("fcEnsureList_years", fcEnsureList_years1);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "查看企业福利成功！");
        } catch (Exception e) {
            Log.info("查看企业福利失败:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "查看企业福利失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 回显产品详情页面
     *
     * @param authorization
     * @param ensureCode
     * @return
     */
    public String showProductDetails(String authorization, String ensureCode, String grpNo) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Object> list = new ArrayList<>();
        try {
            GlobalInput token = userService.getSession(authorization);
            String IDNo = token.getUserName();
            FCPerInfo fcPerInfo = fcPerInfoMapper.selectbyIdNoandGrpNo(IDNo, grpNo);
            String perNo = fcPerInfo.getPerNo();
            FdUser fdUser = fdUserMapper.selectByPerNoAndIdNo(perNo, IDNo);
            //重置globalInput信息
            GlobalInput globalInput = new GlobalInput();
            String userinfo = redisUtil.get(authorization);
            globalInput = JSON.parseObject(userinfo, GlobalInput.class);
            globalInput.setCustomNo(perNo);
            globalInput.setGrpNo(grpNo);
            globalInput.setEnsureCode(ensureCode);
            globalInput.setName(fcPerInfo.getName());
            globalInput.setNickName(fdUser.getNickName());
            globalInput.setUserNo(fdUser.getUserNo());
            Integer loginValidity = Integer.parseInt(myProps.getTermValidityLogin());
            redisUtil.put(authorization, JSON.toJSONString(globalInput), loginValidity);
            //1. 展示保障计划  根据 福利编号查出有效的 计划 供员工去投保
            List<FcDailyInsureRiskDetailInfo> fcDailyInsureRiskDetailInfos = fcDailyInsureRiskDetailInfoMapper.selectByCodeAndstate(ensureCode);
            for (FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo : fcDailyInsureRiskDetailInfos) {
                String planCode = fcDailyInsureRiskDetailInfo.getPlanCode();
                String codeName = fdCodeMapper.selectNameByCode("planCode", planCode);
                Map<String, Object> DateMap = new HashMap<>();
                DateMap.put("codeName", codeName);
                DateMap.put("planCode", planCode);
                list.add(DateMap);
            }
            resultMap.put("list", list);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "回显产品详情页面成功！");
        } catch (Exception e) {
            Log.info("回显产品详情页面错误:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "回显产品详情页面失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 回显快速报价
     *
     * @param authorization
     * @param planCode
     * @return
     */
    public String showQuickQuote(String authorization, String planCode, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> DateMap = new HashMap<>();
        Map<String, Object> DateMapAll = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(authorization);
            String IDNo = globalInput.getUserName();
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey_Daily(ensureCode);
            String grpNo = fcEnsure.getGrpNo();
            FCPerInfo fcPerInfo = fcPerInfoMapper.selectbyIdNoandGrpNo(IDNo, grpNo);
            String perNo = fcPerInfo.getPerNo();
            String birthDay1 = fcPerInfo.getBirthDay();
            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPerNo(perNo);
            String personID = fcStaffFamilyRela.getPersonID();

            Map<String, Object> maps = new HashMap<String, Object>();
            maps.put("customNo", perNo);
            maps.put("customType", "1");
            FdUser fdUser = fdUserMapper.selectCustomNoByType(maps);
            String userNo = fdUser.getUserNo();
  /*  1. 选择计划：。若“产品详情”页面选中计划，此时选中“产品详情”页面选中计划；
         若“产品详情”页面未选中计划，此时选中可投保计划中保费最低的计划（优先级：基本型>基本部分+身故>基本部分+轻中豁>全能型）。*/
            if (!StringUtil.isEmpty(planCode)) {
                DateMap.put("planCode", planCode);
                String planName = fdCodeMapper.selectNameByCode("planCode", planCode);
                DateMap.put("planName", planName);
            } else {
                Set<String> set = new HashSet<String>();
                List<FcDailyInsureRiskDetailInfo> fcDailyInsureRiskDetailInfos = fcDailyInsureRiskDetailInfoMapper.selectByCodeAndstate(ensureCode);
                for (FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo : fcDailyInsureRiskDetailInfos) {
                    set.add(fcDailyInsureRiskDetailInfo.getPlanCode());
                }
                if (set.contains("ID6380")) {
                    //超值版
                    DateMap.put("planCode", "ID6380");
                    DateMap.put("planName", "超值版");
                }
                if (set.contains("ID6381") && !set.contains("ID6380")) {
                    //基本部分+身故
                    DateMap.put("planCode", "ID6381");
                    DateMap.put("planName", "优惠版");
                }
                if (set.contains("ID6382") && !set.contains("ID6381") && !set.contains("ID6380")) {
                    //基本部分+轻中豁
                    DateMap.put("planCode", "ID6382");
                    DateMap.put("planName", "贴心版");
                }
                if (set.contains("ID6383") && !set.contains("ID6382") && !set.contains("ID6381") && !set.contains("ID6380")) {
                    //全能型
                    DateMap.put("planCode", "ID6383");
                    DateMap.put("planName", "豪华版");
                }
            }
//            List<Map<String, String>> planInfos = fcDailyInsureRiskDetailInfoMapper.selectDailyPlans(ensureCode);
            List<Map<String, String>> planInfos = new ArrayList<>();
            //*********************
            List<FcDailyInsureRiskDetailInfo> fcDailyInsureRiskDetailInfos = fcDailyInsureRiskDetailInfoMapper.selectByCodeAndstate(ensureCode);
            for (FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo : fcDailyInsureRiskDetailInfos) {
                String planC = fcDailyInsureRiskDetailInfo.getPlanCode();
                String codeName = fdCodeMapper.selectNameByCode("planCode", planC);
                Map<String, String> DateM = new HashMap<>();
                DateM.put("planName", codeName);
                DateM.put("planCode", planC);
                planInfos.add(DateM);
            }
            //*********************
            //2. 需要传一个默认出生日期：格式为YYYY-MM-DD，必录。出生日期默认选中“当前日期－28天”的日期
//            Date date = new Date();
//            String birthDay = DateTimeUtil.getAppointDate(date, -28, 3);
            //3. 计算年龄
            long dayByBirth = getDayByBirth(birthDay1);
            int age = DateTimeUtil.getCurrentAge(birthDay1, DateTimeUtil.getCurrentDate());
            resultMap.put("success", true);
            resultMap.put("DateMap", DateMap);
            resultMap.put("DateMapAll", planInfos);
            resultMap.put("birthDay", birthDay1);
            resultMap.put("dayByBirth", dayByBirth);
            resultMap.put("age", age);
            resultMap.put("grpNo", grpNo);
            resultMap.put("ensureCode", ensureCode);
            resultMap.put("perNo", perNo);
            resultMap.put("userNo", userNo);
            resultMap.put("code", "200");
            resultMap.put("message", "回显快速报价成功！");
        } catch (Exception e) {
            Log.info("移动端-员工投保--回显快速报价弹窗:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "回显快速报价失败！");
            return JSON.toJSONString(resultMap);
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 根据生日计算出年龄 的工类（根据基础单投保日期计算年龄）返回 -1 代表报错或者没到一岁。返回age代表的是年龄
     *
     * @param birthday
     * @return
     */
    public int getAge(String birthday) {
        int age = -1;
        try {
            Calendar now = Calendar.getInstance();
            Calendar birth = Calendar.getInstance();
            //把string转化为date
            DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
            Date date = new Date();
            Date datebirth = fmt.parse(birthday);
            now.setTime(date);//  数据库里面的基础单投保日期 转成 Date 格式
            birth.setTime(datebirth);//  数据库里面的基础单投保日期 转成 Date 格式
            age = now.get(Calendar.YEAR) - birth.get(Calendar.YEAR);
            if (now.get(Calendar.DAY_OF_YEAR) > birth.get(Calendar.DAY_OF_YEAR)) {
                age += 1;
            }
            return age;
        } catch (Exception e) {//兼容性更强,异常后返回数据

            return -1;
        }
    }

    /**
     * 计算出生天数
     *
     * @param birthday
     * @return
     */
    public long getDayByBirth(String birthday) {
        try {
            SimpleDateFormat s = new SimpleDateFormat("yyyy-MM-dd");
            Date nowDate = new Date();
            String now = s.format(nowDate);
            Date oldDate = s.parse(birthday);
            String old = s.format(oldDate);
            long birthdayold = oldDate.getTime();
            long nowday = nowDate.getTime();
            long time = nowday - birthdayold;
            Log.info("出生了{}天", time / 1000 / 60 / 60 / 24);
            return (time / 1000 / 60 / 60 / 24);
        } catch (ParseException e) {

        }
        return -1;
    }


    /**
     * 移动端-员工投保--快速报价弹窗-选择生日自动带出信息   “交费频次”根据“出生日期”（即：年龄）动态展示
     *
     * @param authorization
     * @param birthDay
     * @return
     */
    public String autoGetPlanInfoByOne(String authorization, String birthDay) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> resultAll = new HashMap<>();
        try {
// 1. 出日期默认选中“当前日期－28天”的日期。出生日期可选择的范围包括：“当前日期－66年＋1天”到“当前日期－28天”。选择出生日期后自动计算年龄（年龄是用当前日期和出生日期计算的）。
            int age = DateTimeUtil.getCurrentAge(birthDay, DateTimeUtil.getCurrentDate());
            long dayByBirth = getDayByBirth(birthDay);
// 3. “年龄”∈[28天, 60周岁]，“交费频次”展示“趸交”、“年交”，自动选中“年交”；
            if (dayByBirth >= 28 && age <= 60) {
                Map<String, Object> DateMap1 = new HashMap<>();
                Map<String, Object> DateMap2 = new HashMap<>();
                List<Map<String, Object>> list = new ArrayList();
                DateMap1.put("CodeKey", "1");
                DateMap1.put("CodeName", "一次交清");
                DateMap1.put("isCheck", false);
                DateMap2.put("CodeKey", "2");
                DateMap2.put("CodeName", "年交");
                DateMap2.put("isCheck", true);
                list.add(DateMap1);
                list.add(DateMap2);
                resultAll.put("payFrequency", list);
                //1. “年龄”∈[28天, 45周岁]，当“交费频次”选中“趸交”，“交费期间”展示“趸交”，自动选中“趸交”；
//              当“交费频次”选中“年交”，“交费期间”展示“5年”、“10年”、“20年”，自动选中“20年”。
                if (dayByBirth >= 28 && age <= 45) {
                    Map<String, Object> DateMap02 = new HashMap<>();
                    Map<String, Object> DateMap03 = new HashMap<>();
                    Map<String, Object> DateMap04 = new HashMap<>();
                    List<Map<String, Object>> listPayPeriod = new ArrayList();
                    DateMap02.put("CodeKey", "02");
                    DateMap03.put("CodeKey", "03");
                    DateMap04.put("CodeKey", "04");
                    DateMap02.put("CodeName", "5年交");
                    DateMap03.put("CodeName", "10年交");
                    DateMap04.put("CodeName", "20年交");
                    DateMap02.put("isCheck", false);
                    DateMap03.put("isCheck", false);
                    DateMap04.put("isCheck", true);
                    listPayPeriod.add(DateMap02);
                    listPayPeriod.add(DateMap03);
                    listPayPeriod.add(DateMap04);
                    resultAll.put("payPeriod", listPayPeriod);
                }
                //2. “年龄”∈[46周岁, 55周岁]，当“交费频次”选中“趸交”，“交费期间”展示“趸交”，自动选中“趸交”；
//              当“交费频次”选中“年交”，“交费期间”展示“5年”、“10年”，自动选中“10年”。
                if (age >= 46 && age <= 55) {
                    Map<String, Object> DateMap02 = new HashMap<>();
                    Map<String, Object> DateMap03 = new HashMap<>();
                    List<Map<String, Object>> listPayPeriod = new ArrayList();
                    DateMap02.put("CodeKey", "02");
                    DateMap03.put("CodeKey", "03");
                    DateMap02.put("CodeName", "5年交");
                    DateMap03.put("CodeName", "10年交");
                    DateMap02.put("isCheck", false);
                    DateMap03.put("isCheck", true);
                    listPayPeriod.add(DateMap02);
                    listPayPeriod.add(DateMap03);
                    resultAll.put("payPeriod", listPayPeriod);
                }
                //3. “年龄”∈[56周岁, 60周岁]，当“交费频次”选中“趸交”，“交费期间”展示“趸交”，自动选中“趸交”；当“交费频次”选中“年交”，“交费期间”展示“5年”，自动选中“5年”。
                if (age >= 56 && age <= 60) {
                    Map<String, Object> DateMap02 = new HashMap<>();
                    List<Map<String, Object>> listPayPeriod = new ArrayList();
                    DateMap02.put("CodeKey", "02");
                    DateMap02.put("CodeName", "5年交");
                    DateMap02.put("isCheck", true);
                    listPayPeriod.add(DateMap02);
                    resultAll.put("payPeriod", listPayPeriod);
                }
            }
// 3.2. “年龄”∈[61周岁,65周岁]，“交费频次”展示“趸交”，自动选中“趸交”；
            if (age >= 61 && age <= 65) {

                Map<String, Object> DateMap1 = new HashMap<>();
                List<Map<String, Object>> list = new ArrayList();
                DateMap1.put("CodeKey", "1");
                DateMap1.put("CodeName", "一次交清");
                DateMap1.put("isCheck", true);
                list.add(DateMap1);
                resultAll.put("payFrequency", list);
                //4. “年龄”∈[61周岁, 65周岁]，当“交费频次”选中“趸交”，“交费期间”展示“趸交”，自动选中“趸交”。趸交
                if (age >= 61 && age <= 65) {
                    Map<String, Object> DateMap02 = new HashMap<>();
                    List<Map<String, Object>> listPayPeriod = new ArrayList();
                    DateMap02.put("CodeKey", "01");
                    DateMap02.put("CodeName", "一次交清");
                    DateMap02.put("isCheck", true);
                    listPayPeriod.add(DateMap02);
                    resultAll.put("payPeriod", listPayPeriod);
                }
            } else if (age > 65) {
                return JSON.toJSONString(ResultUtil.error("年龄不符合投保要求"));
            }
            resultAll.put("insurePeriod", "01");
            resultMap.put("success", true);
            resultMap.put("age", age);
            resultMap.put("dayByBirth", dayByBirth);
            resultMap.put("resultAll", resultAll);
            resultMap.put("code", "200");
            resultMap.put("message", "动态展示成功！");
        } catch (Exception e) {
            Log.info("动态展示报错：", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "动态展示失败！");
            return JSON.toJSONString(resultMap);
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 快速报价-选择交费频次带出信息
     *
     * @param authorization
     * @return
     */
    public String autoGetPlanInfoByTwo(String authorization, String birthDay, String payFrequency) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> resultAll = new HashMap<>();
        try {
            long dayByBirth = getDayByBirth(birthDay);
            int age = DateTimeUtil.getCurrentAge(birthDay, DateTimeUtil.getCurrentDate());
            if (payFrequency.equals("1")) {
                //趸交
                Map<String, Object> DateMap01 = new HashMap<>();
                List<Map<String, Object>> listPayPeriod = new ArrayList();
                DateMap01.put("CodeKey", "01");
                DateMap01.put("CodeName", "一次交清");
                DateMap01.put("isCheck", true);
                listPayPeriod.add(DateMap01);
                resultAll.put("payPeriod", listPayPeriod);
            } else if (payFrequency.equals("2")) {
                //年交
                //1. “年龄”∈[28天, 45周岁]，当“交费频次”选中“趸交”，“交费期间”展示“趸交”，自动选中“趸交”；当“交费频次”选中“年交”，“交费期间”展示“5年”、“10年”、“20年”，自动选中“20年”。
                if (dayByBirth >= 28 && age <= 45) {
                    Map<String, Object> DateMap02 = new HashMap<>();
                    Map<String, Object> DateMap03 = new HashMap<>();
                    Map<String, Object> DateMap04 = new HashMap<>();
                    List<Map<String, Object>> listPayPeriod = new ArrayList();
                    DateMap02.put("CodeKey", "02");
                    DateMap03.put("CodeKey", "03");
                    DateMap04.put("CodeKey", "04");
                    DateMap02.put("CodeName", "5年交");
                    DateMap03.put("CodeName", "10年交");
                    DateMap04.put("CodeName", "20年交");
                    DateMap02.put("isCheck", false);
                    DateMap03.put("isCheck", false);
                    DateMap04.put("isCheck", true);
                    listPayPeriod.add(DateMap02);
                    listPayPeriod.add(DateMap03);
                    listPayPeriod.add(DateMap04);
                    resultAll.put("payPeriod", listPayPeriod);
                }
                //2. “年龄”∈[46周岁, 55周岁]，当“交费频次”选中“趸交”，“交费期间”展示“趸交”，自动选中“趸交”；当“交费频次”选中“年交”，“交费期间”展示“5年”、“10年”，自动选中“10年”。
                if (age >= 46 && age <= 55) {
                    Map<String, Object> DateMap02 = new HashMap<>();
                    Map<String, Object> DateMap03 = new HashMap<>();
                    List<Map<String, Object>> listPayPeriod = new ArrayList();
                    DateMap02.put("CodeKey", "02");
                    DateMap03.put("CodeKey", "03");
                    DateMap02.put("CodeName", "5年交");
                    DateMap03.put("CodeName", "10年交");
                    DateMap02.put("isCheck", false);
                    DateMap03.put("isCheck", true);
                    listPayPeriod.add(DateMap02);
                    listPayPeriod.add(DateMap03);
                    resultAll.put("payPeriod", listPayPeriod);
                }
                //3. “年龄”∈[56周岁, 60周岁]，当“交费频次”选中“趸交”，“交费期间”展示“趸交”，自动选中“趸交”；当“交费频次”选中“年交”，“交费期间”展示“5年”，自动选中“5年”。
                if (age >= 56 && age <= 60) {
                    Map<String, Object> DateMap02 = new HashMap<>();
                    List<Map<String, Object>> listPayPeriod = new ArrayList();
                    DateMap02.put("CodeKey", "02");
                    DateMap02.put("CodeName", "5年交");
                    DateMap02.put("isCheck", true);
                    listPayPeriod.add(DateMap02);
                    resultAll.put("payPeriod", listPayPeriod);
                }
            }
            resultMap.put("success", true);
            resultMap.put("resultAll", resultAll);
            resultMap.put("code", "200");
            resultMap.put("message", "动态展示成功！");
        } catch (Exception e) {
            Log.info("动态展示报错:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "动态展示失败！");
            return JSON.toJSONString(resultMap);
        }
        return JSON.toJSONString(resultMap);
    }

    public String autoGetPlanInfoByTwo2(String authorization, String birthDay, String payFrequency, String payPeriod) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> resultAll = new HashMap<>();
        List<Map<String, Object>> listPayPeriod = new ArrayList();
        try {
            long dayByBirth = getDayByBirth(birthDay);
            int age = DateTimeUtil.getCurrentAge(birthDay, DateTimeUtil.getCurrentDate());
            if (payFrequency.equals("2")) {
                //年交
                //1. “年龄”∈[28天, 45周岁]，当“交费频次”选中“趸交”，“交费期间”展示“趸交”，自动选中“趸交”；当“交费频次”选中“年交”，“交费期间”展示“5年”、“10年”、“20年”，自动选中“20年”。
                if (dayByBirth >= 28 && age <= 45) {
                    Map<String, Object> DateMap02 = new HashMap<>();
                    Map<String, Object> DateMap03 = new HashMap<>();
                    Map<String, Object> DateMap04 = new HashMap<>();
                    DateMap02.put("CodeKey", "02");
                    DateMap03.put("CodeKey", "03");
                    DateMap04.put("CodeKey", "04");
                    DateMap02.put("CodeName", "5年交");
                    DateMap03.put("CodeName", "10年交");
                    DateMap04.put("CodeName", "20年交");
                    if (payPeriod.equals("02")) {
                        DateMap02.put("isCheck", true);
                        DateMap03.put("isCheck", false);
                        DateMap04.put("isCheck", false);
                    } else if (payPeriod.equals("03")) {
                        DateMap02.put("isCheck", false);
                        DateMap03.put("isCheck", true);
                        DateMap04.put("isCheck", false);
                    } else if (payPeriod.equals("04")) {
                        DateMap02.put("isCheck", false);
                        DateMap03.put("isCheck", false);
                        DateMap04.put("isCheck", true);
                    }
                    listPayPeriod.add(DateMap02);
                    listPayPeriod.add(DateMap03);
                    listPayPeriod.add(DateMap04);
                    resultAll.put("payPeriod", listPayPeriod);
                }
                //2. “年龄”∈[46周岁, 55周岁]，当“交费频次”选中“趸交”，“交费期间”展示“趸交”，自动选中“趸交”；当“交费频次”选中“年交”，“交费期间”展示“5年”、“10年”，自动选中“10年”。
                if (age >= 46 && age <= 55) {
                    Map<String, Object> DateMap02 = new HashMap<>();
                    Map<String, Object> DateMap03 = new HashMap<>();
                    DateMap02.put("CodeKey", "02");
                    DateMap03.put("CodeKey", "03");
                    DateMap02.put("CodeName", "5年交");
                    DateMap03.put("CodeName", "10年交");
                    if (payPeriod.equals("02")) {
                        DateMap02.put("isCheck", true);
                        DateMap03.put("isCheck", false);
                    } else if (payPeriod.equals("03")) {
                        DateMap02.put("isCheck", false);
                        DateMap03.put("isCheck", true);
                    }
                    DateMap02.put("isCheck", false);
                    DateMap03.put("isCheck", true);
                    listPayPeriod.add(DateMap02);
                    listPayPeriod.add(DateMap03);
                    resultAll.put("payPeriod", listPayPeriod);
                }
                //3. “年龄”∈[56周岁, 60周岁]，当“交费频次”选中“趸交”，“交费期间”展示“趸交”，自动选中“趸交”；当“交费频次”选中“年交”，“交费期间”展示“5年”，自动选中“5年”。
                if (age >= 56 && age <= 60) {
                    Map<String, Object> DateMap02 = new HashMap<>();
                    DateMap02.put("CodeKey", "02");
                    DateMap02.put("CodeName", "5年交");
                    DateMap02.put("isCheck", true);
                    listPayPeriod.add(DateMap02);
                    resultAll.put("payPeriod", listPayPeriod);
                }
            }
            resultMap.put("success", true);
            resultMap.put("resultAll", resultAll);
            resultMap.put("resultAlla", listPayPeriod);
            resultMap.put("code", "200");
            resultMap.put("message", "动态展示成功！");
        } catch (Exception e) {
            Log.info("动态展示报错:", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "动态展示失败！");
            return JSON.toJSONString(resultMap);
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 员工投保-点击【我要投保】
     *
     * @param authorization
     * @param params
     * @return
     */
    public String insured(String authorization, Map<String, String> params) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Object> list = new ArrayList<>();
        GlobalInput globalInput = userService.getSession(authorization);
        try {
            resultMap.put("isHad", false);
            String perNo = globalInput.getCustomNo();
            String grpNo = params.get("grpNo");
            String prem = params.get("prem");
            String ensureCode = params.get("ensureCode");
            String amount = params.get("amount");
            //0. 判断保额是否为1到200的整数   /先判断是否为正整数，在判断是否大于1 小于200
            if (CommonUtil.isPureDigital(amount)) {
                int i = Integer.parseInt(amount);
                if (i < 1 || i > 200) {
                    Map<String, Object> error = ResultUtil.error("期望保额请录入1到200之间的整数");
                    return JSON.toJSONString(error);
                }
            } else {
                Map<String, Object> error = ResultUtil.error("期望保额请录入1到200之间的整数");
                return JSON.toJSONString(error);
            }
            FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
            String birthDay = fcPerInfo.getBirthDay();
            int age = DateTimeUtil.getCurrentAge(birthDay, DateTimeUtil.getCurrentDate());
            if (age > 65) {
                return JSON.toJSONString(ResultUtil.error("年龄不满足投保要求"));
            }
            //1. 保费是否有值
            if (prem.equals("--")) {
                resultMap.put("code", "500");
                resultMap.put("success", false);
                resultMap.put("message", "请录入期望保额");
                return JSON.toJSONString(resultMap);
            }
            //获取支付方式
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("ensureCode", ensureCode);
            param.put("grpNo", grpNo);
            param.put("configNo", "021");
            //交费方式 0企业 1个人
            String orderPayType = fcEnsureConfigMapper.selectOnlyValue(param);
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            //2. 判断该员工是否有订单  如果有订单是否有 投保中、待支付的订单。
            List<Map<String, String>> fcOrders = fcOrderMapper.selectAllByGrpNoAndPerNoandCode(grpNo, perNo, ensureCode);
            if (fcOrders.size() > 0) {
                resultMap.put("isHad", true);//有订单返回 true，没有订单返回false。作为判断是否有订单的一个标识
                for (Map<String, String> fcOrder : fcOrders) {
                    String orderStatus = fcOrder.get("OrderStatus");
                    String orderNo = fcOrder.get("OrderNo");
                    String grpOrderNo = fcOrder.get("GrpOrderNo");
                    String orderStatusName = fdCodeMapper.selectNameByCode("OrderStatus", orderStatus);
                    fcOrder.put("orderStatusName", orderStatusName);
                    FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectByPrimaryKey(grpOrderNo);
                    String grpContNo = fcGrpOrder.getGrpContNo();
                    Map<String, String> map1 = new HashMap<>();
                    map1.put("grpContNo", grpContNo);
                    map1.put("orderNo", orderNo);
                    FCOrderItem fcOrderItem = fcOrderItemMapper.selectBygrpContNoAndOrderNo(map1);
                    String orderItemNo = fcOrderItem.getOrderItemNo();
                    FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectByPrimaryKey(orderItemNo);
                    String personID = fcOrderInsured.getPersonID();
                    fcOrder.put("orderItemNo", orderItemNo);
                    fcOrder.put("ensureCode", ensureCode);
                    fcOrder.put("personID", personID);
                    fcOrder.put("Name", fcOrderInsured.getName());
                    //获取与员工关系
                    map1.clear();
                    map1.put("perNo", perNo);
                    map1.put("personID", personID);
                    FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPrimaryKey(map1);
                    fcOrder.put("payType", orderPayType);
                    fcOrder.put("relation", fcStaffFamilyRela.getRelation());
                    double sumPrem = 0.00;
                    double grpPrem = 0.00;
                    double selPrem = 0.00;
                    grpPrem = CommonUtil.add(grpPrem, fcOrderItem.getGrpPrem() == null ? 0.00 : fcOrderItem.getGrpPrem());
                    selPrem = CommonUtil.add(selPrem, fcOrderItem.getSelfPrem() == null ? 0.00 : fcOrderItem.getSelfPrem());
                    sumPrem = CommonUtil.add(grpPrem, selPrem);
                    fcOrder.put("sumPrem", String.valueOf(sumPrem));
                    fcOrder.put("grpPrem", String.valueOf(grpPrem));
                    fcOrder.put("selPrem", String.valueOf(selPrem));
                    fcOrder.put("planType", fcEnsure.getPlanType());
                }
            }
            resultMap.put("fcOrders", fcOrders);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "查询订单成功！");
        } catch (Exception e) {
            Log.info("发生了异常：" + e);
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "失败");
            return JSON.toJSONString(resultMap);
        }
        return JSON.toJSONString(resultMap);
    }

    @SuppressWarnings("finally")
    public String payStatusQuery(String authorization, String orderItemNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            FCOrder fcOrder = fcOrderMapper.selectOrderByOrderItemNo(orderItemNo);
            resultMap = payStatusQuery(fcOrder);
        } catch (Exception e) {

            Log.info("支付状态查询接口异常：" + e);
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "支付状态查询接口异常：" + e);
        } finally {
            return JSON.toJSONString(resultMap);
        }
    }

    @SuppressWarnings("finally")
    public String goToPayment(String authorization, String orderItemNo, String type) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(authorization);
            //判断子订单号是否为空
            if (orderItemNo == null || "".equals(orderItemNo)) {
                Log.info("子订单号不能为空");
                resultMap.put("code", "500");
                resultMap.put("success", false);
                resultMap.put("message", "子订单号不能为空");
            }
            //判断订单是否存在，且订单状态为待支付/支付失败/支付中。
            FCOrder fcOrder = fcOrderMapper.selectOrderByOrderItemNo(orderItemNo);
            if (fcOrder == null) {
                resultMap.put("code", "500");
                resultMap.put("success", false);
                resultMap.put("message", "当前订单不存在");
            } else {
                String orderStatus = fcOrder.getOrderStatus();
                if (orderStatus.matches("^0(4|5|7)$") || orderStatus.equals("010") || orderStatus.equals("015")) {//判断订单状态是否包含04-待支付；05-支付中；07-支付失败
                    FCOrderItem fCOrderItem = fcOrderItemMapper.selectByPrimaryKey(orderItemNo);
                    FCPerson fcPerson = fcPersonMapper.selectPersonInfoByOrderitemNo(orderItemNo);
                    if (orderStatus.equals("05")) {
                        //调用支付查询接口
                        resultMap = payStatusQuery(fcOrder);
                        String code = resultMap.get("code").toString();
                        String payUrlStatus = resultMap.get("payUrlStatus").toString();
                        //S-支付成功;F-支付失败;R-支付中;PI-待支付;N-支付信息不存在
                        if (code.equals("200") && (payUrlStatus.equals("PI") || payUrlStatus.equals("F") || payUrlStatus.equals("N"))) {
                            resultMap = payCenterGetPayUrl(fcOrder, fCOrderItem, fcPerson, type, globalInput.getEnsureCode());//调用支付页面获取地址
                        } else {
                            resultMap.put("code", "500");
                            resultMap.put("success", false);
                            resultMap.put("message", "当前订单不需要支付");
                        }
                    } else {
                        //调用支付页面获取地址
                        resultMap = payCenterGetPayUrl(fcOrder, fCOrderItem, fcPerson, type, globalInput.getEnsureCode());
                    }
                } else {
                    resultMap.put("code", "500");
                    resultMap.put("success", false);
                    resultMap.put("message", "当前订单不需要支付");
                }
            }
        } catch (Exception e) {

            Log.info("调用获取支付页面接口异常：" + e);
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "调用获取支付页面接口异常：" + e);
        } finally {
            return JSON.toJSONString(resultMap);
        }
    }

    //调用获取支付页面接口
    @SuppressWarnings("finally")
    public Map<String, Object> payCenterGetPayUrl(FCOrder fcOrder, FCOrderItem fCOrderItem, FCPerson fcPerson, String type, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            System.out.println("调取支付中信之前的订单状态为:" + fcOrder.getOrderStatus());
            FCPerson fcPersonPay = fcPersonMapper.selectByPrimaryKey(fCOrderItem.getPayPersonId());
            if (fcPersonPay == null) {
                resultMap.put("code", "500");
                resultMap.put("success", false);
                resultMap.put("message", "当前支付订单不存在");
                return resultMap;
            }
            saveFcorderPay(fcOrder, fCOrderItem);//更新fcorderpay数据为无效，插入fcorderpay，更新订单状态为支付中
            PayCenterGetPayUrl payCenterGetPayUrl = new PayCenterGetPayUrl();
            payCenterGetPayUrl.setTransSource(myProps.getTransSource());//交易系统
            payCenterGetPayUrl.setTransCode(myProps.getGetPayUrltransCode());//交易编码
            payCenterGetPayUrl.setTransTime(DateTimeUtil.getCurrentDateTime());//交易时间
            payCenterGetPayUrl.setTransNo(UUIDUtil.genUUID());//交易流水号
            payCenterGetPayUrl.setPayKind(myProps.getPayKind());//支付接入类型
            payCenterGetPayUrl.setChannelCode(myProps.getChannelCode());//渠道
            payCenterGetPayUrl.setBusinessNo(fcOrder.getOrderNo());//业务单号
            payCenterGetPayUrl.setBusinessDesc("XXXX支付");//业务描述
            payCenterGetPayUrl.setAmount(fCOrderItem.getSelfPrem());//付款金额
            FdUser fdUserInfo = fdUserMapper.findUserInfo(fcPerson.getIDNo(), fcPerson.getMobilePhone(), "1");
            //由于家属没有UserNo,导致给家属支付会报错，因此注释掉了，以后可能会有用（可能用来更新token之类的）
            payCenterGetPayUrl.setPageBackUrl(myProps.getPageBackUrl() + "?" + "orderNoAndPrem="
                    + "Split" + fcOrder.getOrderNo() + "Split" + fCOrderItem.getSelfPrem() + "Split" + ensureCode + "Split" + fcOrder.getPerNo() + "Split"/*+fdUserInfo.getUserNo()+"Split"*/);//页面回调地址
            Log.info("支付成功回调地址为：" + payCenterGetPayUrl.getPageBackUrl() + "  此时的订单状态为；" + fcOrder.getOrderStatus());
            payCenterGetPayUrl.setDataBackUrl(myProps.getDataBackUrl());//支付后台通知地址

            payCenterGetPayUrl.setCustomName(fcPersonPay.getName());//客户姓名
            payCenterGetPayUrl.setCustomId(fcPersonPay.getPersonID());//客户唯一标识
            payCenterGetPayUrl.setIdCardType(fcPersonPay.getIDType());//证件类型
            payCenterGetPayUrl.setIdCardNo(fcPersonPay.getIDNo());//证件号码
            payCenterGetPayUrl.setMobile(fcPersonPay.getMobilePhone());//手机号
            payCenterGetPayUrl.setOnlyPkFlag(type.equals("01") ? false : true);//是否仅批扣方式标识
            payCenterGetPayUrl.setPkChooseFlag(true);//是否批扣方式可选
            payCenterGetPayUrl.setThemeColor("");//页面主题风格，可不传，默认blue
            //报文新增字段
            //产品id
            payCenterGetPayUrl.setProductId(ensureCode);
            //appid    请求Header中的app_id
            payCenterGetPayUrl.setAid(myProps.getApp_id());
            //机构id  传的是所属企业的企业号
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            payCenterGetPayUrl.setOid(fcEnsure.getGrpNo());
            //页面当前流程id  随便传的一个String串
            payCenterGetPayUrl.setTid("payCenterGetPayUrl");
            //缴费方式  员福系统的缴费方式分为趸交和年缴（已有枚举类PayFrequencyEnum），中台提供的缴费方式分为年缴和月缴以及其他（已写枚举类PayFreqEnum）
            FCOrderItemDetail fcOrderItemDetail = fcOrderItemDetailMapper.selectByPrimaryKey_No(fCOrderItem.getOrderItemDetailNo());
            //若员福为趸交则传其他，若为年缴则传年缴
            if (PayFrequencyEnum.SINGLEPREMIUM.getCode().equals(fcOrderItemDetail.getPayFrequency())) {
                payCenterGetPayUrl.setPayFreq(PayFreqEnum.OTHER.getCode());
            } else if (PayFrequencyEnum.YEARPAY.getCode().equals(fcOrderItemDetail.getPayFrequency())) {
                payCenterGetPayUrl.setPayFreq(PayFreqEnum.ANNUALPAYMENT.getCode());
            } else {
                payCenterGetPayUrl.setPayFreq(PayFreqEnum.MONTHLYPAYMENT.getCode());
            }
            Log.info("获取支付链接请求报文: {}", JSONObject.toJSONString(payCenterGetPayUrl));
            String postHttpRequestJson = HttpUtil.postHttpRequestJson(myProps.getPayUrl(), JSONObject.toJSONString(payCenterGetPayUrl), myProps.getApp_id(), myProps.getApp_secret());
            PayCenterGetPayUrlResponse payCenterGetPayUrlResponse = JSONObject.toJavaObject(JSONObject.parseObject(postHttpRequestJson), PayCenterGetPayUrlResponse.class);
            if (payCenterGetPayUrlResponse != null) {
                String code = payCenterGetPayUrlResponse.getCode();
                if (code.equals("200")) {
                    resultMap.put("payUrl", payCenterGetPayUrlResponse.getData().getPayUrl());
                    resultMap.put("payType", "1");
                    resultMap.put("success", true);
                    resultMap.put("code", "200");
                    resultMap.put("message", payCenterGetPayUrlResponse);
                } else {
                    resultMap.put("code", "500");
                    resultMap.put("success", false);
                    resultMap.put("message", payCenterGetPayUrlResponse);
                }
            } else {
                resultMap.put("code", "500");
                resultMap.put("success", false);
                resultMap.put("message", "调用获取支付页面接口异常");
            }
        } catch (Exception e) {

            Log.info("调用获取支付页面接口异常：" + e);
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "调用获取支付页面接口异常");
        } finally {
            return resultMap;
        }
    }

    //更新fcorderpay数据为无效，插入fcorderpay，更新订单状态为支付中
    public void saveFcorderPay(FCOrder fcOrder, FCOrderItem fCOrderItem) {
        Map<String, Object> paraMap = new HashMap<String, Object>();
        paraMap.put("orderNo", fcOrder.getOrderNo());
        paraMap.put("operator", fcOrder.getPerNo());
        paraMap.put("modifyDate", DateTimeUtil.getCurrentDate());
        paraMap.put("modifyTime", DateTimeUtil.getCurrentTime());
        fcOrderMapper.updateFcorderpay(paraMap);
        paraMap.put("PayNo", maxNoService.createMaxNo("orderPayNo", "", 20));
        paraMap.put("PayType", "02");
        paraMap.put("TotalPrem", CommonUtil.add(fCOrderItem.getGrpPrem(), fCOrderItem.getSelfPrem()));
        paraMap.put("GrpPrem", fCOrderItem.getGrpPrem());
        paraMap.put("PersonPrem", fCOrderItem.getSelfPrem());
        paraMap.put("IsValid", "0");
        paraMap.put("BankCode", "");
        paraMap.put("BankAccType", "");
        paraMap.put("BankAccNo", "");
        paraMap.put("BankAccName", "");
        paraMap.put("bankAccPhone", "");
        paraMap.put("operatorCom", "");
        paraMap.put("makeDate", DateTimeUtil.getCurrentDate());
        paraMap.put("makeTime", DateTimeUtil.getCurrentTime());
        paraMap.put("modifyDate", DateTimeUtil.getCurrentDate());
        paraMap.put("modifyTime", DateTimeUtil.getCurrentTime());
        fcOrderMapper.insertFcorderpay(paraMap);
//        fcOrderMapper.updateFcorder("04", fcOrder.getOrderNo(), DateTimeUtil.getCurrentDate(), DateTimeUtil.getCurrentTime());
    }

    //调用支付状态查询接口
    public Map<String, Object> payStatusQuery(FCOrder fcOrder) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            PayCenterPayStatusQuery payCenterPayStatusQuery = new PayCenterPayStatusQuery();
            payCenterPayStatusQuery.setTransSource(myProps.getTransSource());//交易系统
            payCenterPayStatusQuery.setTransCode(myProps.getGetPayUrltransCode());//交易编码
            payCenterPayStatusQuery.setTransTime(DateTimeUtil.getCurrentDateTime());//交易时间
            payCenterPayStatusQuery.setTransNo(UUIDUtil.genUUID());//交易流水号
            payCenterPayStatusQuery.setChannelCode(myProps.getChannelCode());//渠道
            payCenterPayStatusQuery.setBusinessNo(fcOrder.getOrderNo());//业务单号
            payCenterPayStatusQuery.setPayTransactionNo("");//支付交易号
            String postHttpRequestJson = HttpUtil.postHttpRequestJson(myProps.getPayQueryUrl(), JSONObject.toJSONString(payCenterPayStatusQuery), myProps.getApp_id(), myProps.getApp_secret());
            PayCenterPayStatusQueryResponse payCenterPayStatusQueryResponse = JSONObject.toJavaObject(JSONObject.parseObject(postHttpRequestJson), PayCenterPayStatusQueryResponse.class);
            if (payCenterPayStatusQueryResponse != null) {
                String code = payCenterPayStatusQueryResponse.getCode();
                if (code.equals("200")) {
                    //S-支付成功;F-支付失败;R-支付中;PI-待支付
                    resultMap.put("payUrlStatus", payCenterPayStatusQueryResponse.getData().getPayStatus());
                    resultMap.put("success", true);
                    resultMap.put("code", "200");
                    resultMap.put("message", payCenterPayStatusQueryResponse);
                } else {
                    //N-支付信息不存在
                    resultMap.put("payUrlStatus", "N");
                    resultMap.put("success", true);
                    resultMap.put("code", "200");
                    resultMap.put("message", payCenterPayStatusQueryResponse);
                }
            } else {
                resultMap.put("code", "500");
                resultMap.put("success", false);
                resultMap.put("message", "支付状态查询接口异常");
            }
        } catch (Exception e) {

            Log.info("调用支付状态查询接口异常：" + e);
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "调用支付状态查询接口异常");
        } finally {
            return resultMap;
        }
    }

    /**
     * 个人端健康告知保存接口
     *
     * @param token
     * @param orderItemNo
     * @param map
     * @return
     */
    @Transactional
    public String appntImpartInfoPersonal(String token, String orderItemNo, Map<String, Map<String, String>> map, String yearSalary, String relation) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String ensureCode = globalInput.getEnsureCode();
            if (StringUtil.isEmpty(ensureCode)) {
                return JSON.toJSONString(ResultUtil.error("token中的ensureCode 为空"));
            }
            System.out.println("ensureCode为" + ensureCode);
            String name = globalInput.getName();
            String grpNo = globalInput.getGrpNo();
            String perNo = globalInput.getCustomNo();
            String userName = globalInput.getUserName();
            FdUser userInfo = fdUserMapper.findUserForLogin(userName, globalInput.getCustomType());
            String userNo = userInfo.getUserNo();
            if (StringUtils.isBlank(grpNo)) {
                Map<String, Object> error = ResultUtil.error("参数缺失，企业客户号为空！");
                return JSON.toJSONString(error);
            }
            if (StringUtils.isBlank(orderItemNo)) {
                Map<String, Object> error = ResultUtil.error("参数缺失，子订单号为空！");
                return JSON.toJSONString(error);
            }
            Log.info("个人健康告知 删除开始");
            //1. 直接根据主键 子订单号删除该员工 健康告知，然后再次保存(删除的是多个）
            fcAppntImpartInfoPersonalMapper.deleteImpartInfo(orderItemNo);
            Log.info("个人健康告知 删除成功  子订单号为:" + orderItemNo);
            //2. 循环输入的信息，逐个保存
            FCAppntImpartInfoPersonal fcAppntImpartInfoPersonal = new FCAppntImpartInfoPersonal();
            fcAppntImpartInfoPersonal.setOrderItemNo(orderItemNo);
            fcAppntImpartInfoPersonal.setGrpNo(grpNo);
            Map<String, String> map2 = map.get("he1");//详情
            Map<String, String> map1 = map.get("he2");//选项
            Log.info("个人健康告知循环 入库 开始");
            for (String key : map1.keySet()) {
                fcAppntImpartInfoPersonal.setImpartCode(key);
                fcAppntImpartInfoPersonal.setImpartParamModle(map1.get(key));
                fcAppntImpartInfoPersonal.setOperator(globalInput.getUserNo());
                fcAppntImpartInfoPersonal = (FCAppntImpartInfoPersonal) CommonUtil.initObject(fcAppntImpartInfoPersonal, "INSERT");
                fcAppntImpartInfoPersonalMapper.insertSelective(fcAppntImpartInfoPersonal);
            }
            for (String key : map2.keySet()) {
                fcAppntImpartInfoPersonalMapper.updateByModle(key, map2.get(key), orderItemNo);
            }
            if (!StringUtil.isEmpty(relation) && relation.equals("0")) {
                FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectByPrimaryKey(orderItemNo);
                fcOrderInsured.setMainYearSalary(yearSalary);
                fcOrderInsuredMapper.updateByPrimaryKeySelective(fcOrderInsured);
            }
            //查找有效的 且未锁定的 分享链接。设置为无效的。
            fcOrderItemMapper.updateShareIsLock(orderItemNo);
            //生成分享关联表fcorderitemsharerela
            String shareDetalNo = maxNoService.createMaxNo("shareDetalNo", "", 20);
            FcOrderItemShareRela fcOrderItemShareRela = new FcOrderItemShareRela();
            fcOrderItemShareRela.setOrderItemNo(orderItemNo);
            fcOrderItemShareRela.setShareDetalNo(shareDetalNo);
            fcOrderItemShareRela.setShareState("0");
            fcOrderItemShareRela.setIsLock("0");
            fcOrderItemShareRela.setOperator(globalInput.getUserNo());
            fcOrderItemShareRela = (FcOrderItemShareRela) CommonUtil.initObject(fcOrderItemShareRela, "INSERT");
            fcOrderItemMapper.insertShareRelaSelective(fcOrderItemShareRela);
            Log.info("个人健康告知 循环入库成功");
            resultMap.put("shareDetalNo", shareDetalNo);
            resultMap.put("success", true);
            resultMap.put("staffName", name);
            resultMap.put("userNo", userNo);
            resultMap.put("grpOrderNo", ensureCode);
            resultMap.put("code", "200");
            resultMap.put("message", "个人端健康告知保存成功！");
            Log.info("个人端健康告知保存成功。");
        } catch (Exception e) {
            Log.info("系统异常：个人端健康告知保存失败=={}", e);
            throw new RuntimeException(e);
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 个人订单查询接口
     *
     * @param token
     * @return
     */
    public String queryOrderByTime(String token, String commitDate1, String commitDate2) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            List<Map<String, String>> orderInfoList = new ArrayList<>();
            String IDNo = globalInput.getUserName();
            //根据IdNo查询所有的 该员工所有的订单
            Map<String, String> mapInfo = new HashMap<>();
            mapInfo.put("iDNo", IDNo);
            mapInfo.put("commitDate1", commitDate1);
            mapInfo.put("commitDate2", commitDate2);
            Map<String, String> map1 = new HashMap<>();
            String grpContNo = null;
            String commitDate = null;
            List<Map<String, Object>> fcOrderList = fcOrderMapper.getOrderListByPerNoStaff(mapInfo);
            for (Map<String, Object> map : fcOrderList) {
                String orderNo = String.valueOf(map.get("OrderNo"));
                if (map.get("CommitDate") != null) {
                    commitDate = String.valueOf(map.get("CommitDate"));
                }
                if (map.get("GrpContNo") != null) {
                    grpContNo = String.valueOf(map.get("GrpContNo"));
                }
                Map<String, String> orderInfo = fcOrderMapper.getOrderInfoByOrderNoDaily(orderNo);
                List<FCOrderItem> fcOrderItems = fcOrderItemMapper.selectOrderNo(orderNo);
                double sumPrem = 0.00;
                double grpPrem = 0.00;
                double selPrem = 0.00;
                for (FCOrderItem orderItemInfo : fcOrderItems) {
                    grpPrem = CommonUtil.add(grpPrem, orderItemInfo.getGrpPrem() == null ? 0.00 : orderItemInfo.getGrpPrem());
                    selPrem = CommonUtil.add(selPrem, orderItemInfo.getSelfPrem() == null ? 0.00 : orderItemInfo.getSelfPrem());
                }
                sumPrem = CommonUtil.add(grpPrem, selPrem);
                map1.put("orderNo", orderNo);
                FCOrderItem fcOrderItem = fcOrderItems.get(0);
                map1.clear();
                FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectByPrimaryKey(fcOrderItem.getOrderItemNo());
                String grpNo = String.valueOf(map.get("GrpNo"));
                FCPerInfo fcPerInfo = fcPerInfoMapper.selectbyIdNoandGrpNo(IDNo, grpNo);
                String perNo = fcPerInfo.getPerNo();
                String personID = fcOrderInsured.getPersonID();
                String contNo = fcOrderItem.getContNo();
                String ensureCode = String.valueOf(map.get("EnsureCode"));
                FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
                String planType = fcEnsure.getPlanType();
                String plancode = fcDailyInsureRiskInfoMapper.selectByEnsureCode(ensureCode);
                // 应前台要求，返回planCode字段作为险种编码
                orderInfo.put("planCode", plancode);
                if (!StringUtil.isEmpty(planType) && planType.equals("2")) {
                    //日常计划   福利名称展示
                    if ("14110".equals(plancode)) {
                        orderInfo.put("ensureName", "横琴安颐无忧团体年金保险");
                    } else if ("16380".equals(plancode)) {
                        orderInfo.put("ensureName", "横琴福裕团体重大疾病保险");
                    }
                } else if (!StringUtil.isEmpty(planType) && !planType.equals("2")) {
                    //年度计划，显示名称
                    orderInfo.put("ensureName", fcEnsure.getEnsureName());
                }
                //获取到注册期结束日
                String closeDay = orderInfo.get("closeDay");

                //获取与员工关系
                map1.clear();
                map1.put("perNo", perNo);
                map1.put("personID", personID);
                FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPrimaryKey(map1);
                log.info("queryOrderByTime response:{}", JsonUtil.toJSON(fcStaffFamilyRela));
                //获取支付方式
                Map<String, Object> param = new HashMap<String, Object>();
                param.put("ensureCode", ensureCode);
                param.put("grpNo", grpNo);
                param.put("configNo", "021");
                //交费方式 0企业 1个人
                String orderPayType = fcEnsureConfigMapper.selectOnlyValue(param);
                orderInfo.put("isCloseDay", StringUtils.isNotBlank(closeDay) ? String.valueOf(DateTimeUtil.checkDate1(closeDay, DateTimeUtil.getCurrentDate())) : "true");
                orderInfo.put("sumPrem", String.valueOf(sumPrem));
                orderInfo.put("grpPrem", String.valueOf(grpPrem));
                orderInfo.put("selPrem", String.valueOf(selPrem));
                orderInfo.put("grpContNo", grpContNo);
                orderInfo.put("contNo", contNo);
                orderInfo.put("payType", orderPayType);
                orderInfo.put("relation", null == fcStaffFamilyRela ? null : fcStaffFamilyRela.getRelation());
                orderInfo.put("commitDate", commitDate);
                orderInfo.put("orderItemNo", fcOrderItem.getOrderItemNo());
                orderInfo.put("personId", personID);
                orderInfo.put("perNo", perNo);
                orderInfoList.add(orderInfo);
                commitDate = null;
            }
            resultMap.put("data", orderInfoList);
            resultMap.put("success", true);
            resultMap.put("code", "200");
        } catch (Exception e) {
            log.info("DailyPlanPhoneService.queryOrderByTime error", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "订单查询失败。");
        }
        return JSON.toJSONString(resultMap);
    }

    public String getFaceUrl(String token, String orderItemNo, String personId, String faceFlag) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String perNo = globalInput.getCustomNo();
            if (StringUtil.isEmpty(orderItemNo) || StringUtil.isEmpty(personId)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "orderItemNo或者personId不能为空！");
                return JSON.toJSONString(resultMap);
            }
            FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(orderItemNo);
            FCPerson fcperson = fcPersonMapper.selectByPrimaryKey(personId);
            if (StringUtil.isEmpty(fcperson) || StringUtil.isEmpty(fcOrderItem)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "被保人不存在！");
                return JSON.toJSONString(resultMap);
            }
            resultMap = getFaceUrl(fcperson, fcOrderItem.getOrderNo(), perNo, faceFlag);
        } catch (Exception e) {
            Log.info("调用获取人脸识别URL接口异常：" + e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", e);
            return JSON.toJSONString(resultMap);
        }
        return JSON.toJSONString(resultMap);
    }

    //调用获取人脸识别URL接口
    @SuppressWarnings("finally")
    public Map<String, Object> getFaceUrl(FCPerson fcperson, String orderNo, String perNo, String faceFlag) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            FdUser fduserInfo = fdUserMapper.findUserInfo(fcperson.getIDNo(), fcperson.getMobilePhone(), "1");
            String insuranceSignatureParam = fcOrderMapper.getInsuranceSignatureByOrderNo(orderNo);
            FaceUrl faceUrl = new FaceUrl();
            faceUrl.setTransCode(myProps.getFace_transCode());
            faceUrl.setRuleId(myProps.getFace_ruleId());
            if (!StringUtil.isEmpty(faceFlag) && faceFlag.equals("1")) {
                //由于家属没有 UserNo，导致给家属投保得时候报错，这里注释UserNo，后期可能用得到。
                faceUrl.setRedirectUrl(myProps.getFace_redirectUrl() + "?insuranceSignatureParam=" + insuranceSignatureParam/*+fduserInfo.getUserNo()+"Split"*/);
            } else {
                faceUrl.setRedirectUrl(myProps.getFace_redirectUrlShare() + "?insuranceSignatureParam=" + insuranceSignatureParam);
            }
            Log.info("人脸识别回调地址：" + faceUrl.getRedirectUrl() + "   idno为：" + fcperson.getIDNo() + "订单号为；" + orderNo + "标识faceFlag为：" + faceFlag);
            faceUrl.setChannelSource(myProps.getFace_channelSource());
            faceUrl.setActionType(myProps.getFace_actionType());
            faceUrl.setName(fcperson.getName());
            faceUrl.setIdCard(fcperson.getIDNo());
            String postHttpRequestJson = HttpUtil.postHttpRequestJson(myProps.getFace_Url(), JSONObject.toJSONString(faceUrl), myProps.getFace_appId(), myProps.getFace_appSecret());
            FaceUrlResponse faceUrlResponse = JSONObject.toJavaObject(JSONObject.parseObject(postHttpRequestJson), FaceUrlResponse.class);
            if (!StringUtil.isEmpty(faceUrlResponse)) {
                String code = faceUrlResponse.getCode();
                if (code.equals("200")) {
                    saveFcorderFace(faceUrlResponse, fcperson, orderNo, perNo);
                    resultMap.put("faceUrl", faceUrlResponse.getData().getUrl());
                    resultMap.put("success", true);
                    resultMap.put("code", "200");
                    resultMap.put("message", faceUrlResponse);
                } else {
                    resultMap.put("code", "500");
                    resultMap.put("success", false);
                    resultMap.put("message", faceUrlResponse);
                }
            } else {
                resultMap.put("code", "500");
                resultMap.put("success", false);
                resultMap.put("message", "获取人脸识别URL接口异常");
            }
        } catch (Exception e) {

            Log.info("获取人脸识别URL接口异常：" + e);
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "获取人脸识别URL接口异常");
        } finally {
            return resultMap;
        }
    }

    public void saveFcorderFace(FaceUrlResponse faceUrlResponse, FCPerson fcperson, String orderNo, String perNo) {
        Map<String, Object> paraMap = new HashMap<String, Object>();
        paraMap.put("orderNo", orderNo);
        paraMap.put("personId", fcperson.getPersonID());
        paraMap.put("modifyDate", DateTimeUtil.getCurrentDate());
        paraMap.put("modifyTime", DateTimeUtil.getCurrentTime());
        fcOrderMapper.updateFcorderFace(paraMap);
        paraMap.put("FaceNo", maxNoService.createMaxNo("orderFaceNo", "", 20));
        paraMap.put("bizToken", faceUrlResponse.getData().getBizToken());
        paraMap.put("requestId", faceUrlResponse.getData().getRequestId());
        paraMap.put("faceUrl", faceUrlResponse.getData().getUrl());
        paraMap.put("IsValid", "0");
        paraMap.put("IsPass", "0");
        paraMap.put("operator", perNo);
        paraMap.put("operatorCom", "");
        paraMap.put("makeDate", DateTimeUtil.getCurrentDate());
        paraMap.put("makeTime", DateTimeUtil.getCurrentTime());
        paraMap.put("modifyDate", DateTimeUtil.getCurrentDate());
        paraMap.put("modifyTime", DateTimeUtil.getCurrentTime());
        fcOrderMapper.insertFcorderFace(paraMap);
    }

    public String faceStatusQuery(String token, String orderItemNo, String personId) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (StringUtil.isEmpty(orderItemNo) || StringUtil.isEmpty(personId)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "orderItemNo或者personId不能为空！");
                return JSON.toJSONString(resultMap);
            }
            FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(orderItemNo);
            FCPerson fcperson = fcPersonMapper.selectByPrimaryKey(personId);
            if (StringUtil.isEmpty(fcperson) || StringUtil.isEmpty(fcOrderItem)) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "被保人不存在！");
                return JSON.toJSONString(resultMap);
            }
            return faceStatusQuery(fcOrderItem.getOrderNo(), personId);
        } catch (Exception e) {

            Log.info("人脸识别状态查询接口异常：" + e);
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "人脸识别状态查询接口异常");
        }
        return JSON.toJSONString(resultMap);
    }

    public String faceStatusQuery(String orderNo, String personId) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            //查询是否包含有效的识别信息
            List<Map<String, Object>> faceInfoByPerInfo = fcOrderMapper.getFaceInfoByPerInfo(orderNo, personId);
            if (faceInfoByPerInfo.size() > 1) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "人脸识别数据异常！");
                return JSON.toJSONString(resultMap);
            } else if (faceInfoByPerInfo.size() == 0) {
                List<String> bizTokenByPerInfo = fcOrderMapper.getBizTokenByPerInfo(orderNo, personId);
                if (bizTokenByPerInfo.size() > 1) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "人脸识别数据异常！");
                    return JSON.toJSONString(resultMap);
                } else if (bizTokenByPerInfo.size() == 0) {
                    resultMap.put("success", true);
                    resultMap.put("code", "200");
                    resultMap.put("message", "未进行人脸识别！");
                    resultMap.put("num", "1");
                    return JSON.toJSONString(resultMap);
                } else if (bizTokenByPerInfo.size() == 1) {
                    FaceStatusQuery faceStatusQuery = new FaceStatusQuery();
                    faceStatusQuery.setTransCode(myProps.getFace_transCode());
                    faceStatusQuery.setInfoType("13");
                    faceStatusQuery.setChannelSource(myProps.getFace_channelSource());
                    faceStatusQuery.setBizToken(bizTokenByPerInfo.get(0));
                    faceStatusQuery.setActionType(myProps.getFace_actionType());
                    String postHttpRequestJson = HttpUtil.postHttpRequestJson(myProps.getFace_QueryUrl(), JSONObject.toJSONString(faceStatusQuery), myProps.getFace_appId(), myProps.getFace_appSecret());
                    FaceStatusQueryResponse faceStatusQueryResponse = JSONObject.toJavaObject(JSONObject.parseObject(postHttpRequestJson), FaceStatusQueryResponse.class);
                    if (!StringUtil.isEmpty(faceStatusQueryResponse)) {
                        String code = faceStatusQueryResponse.getCode();
                        if (code.equals("200")) {
                            resultMap = updateFcorderFace(orderNo, personId, faceStatusQueryResponse);
                        } else {
                            //更新状态为识别失败
                            resultMap.put("code", "500");
                            resultMap.put("success", false);
                            resultMap.put("message", faceStatusQueryResponse);
                        }
                    } else {
                        resultMap.put("code", "500");
                        resultMap.put("success", false);
                        resultMap.put("message", "人脸识别状态查询接口异常");
                    }
                }
            } else if (faceInfoByPerInfo.size() == 1) {
                Map<String, Object> faceInfomap = faceInfoByPerInfo.get(0);
                String isPass = faceInfomap.get("isPass").toString();
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "识别未通过");
                if (isPass.equals("1")) {//识别通过
                    resultMap.put("message", "识别通过");
                    resultMap.put("bestFrame", faceInfomap.get("bestFrame").toString());
                }
            }
        } catch (Exception e) {

            Log.info("人脸识别状态查询接口异常：" + e);
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "人脸识别状态查询接口异常");
        }
        return JSON.toJSONString(resultMap);
    }

    public Map<String, Object> updateFcorderFace(String orderNo, String personId, FaceStatusQueryResponse faceStatusQueryResponse) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            FaceStatusQueryResponseData data = faceStatusQueryResponse.getData();
            Map<String, Object> paraMap = new HashMap<String, Object>();
            if (StringUtil.isEmpty(data.getErrCode())) {
                paraMap.put("orderNo", orderNo);
                paraMap.put("personId", personId);
                paraMap.put("IsValid", "1");
                paraMap.put("IsPass", "2");
                paraMap.put("bestFrame", data.getBestFrame());
                paraMap.put("modifyDate", DateTimeUtil.getCurrentDate());
                paraMap.put("modifyTime", DateTimeUtil.getCurrentTime());
                fcOrderMapper.updateFcorderFaceByQuery(paraMap);
                resultMap.put("code", "300");
                resultMap.put("success", true);
                resultMap.put("message", "识别未通过");
            } else {
                String errCode = data.getErrCode();
                if (errCode.equals("0")) {
                    paraMap.put("orderNo", orderNo);
                    paraMap.put("personId", personId);
                    paraMap.put("IsValid", "1");
                    paraMap.put("IsPass", "1");
                    paraMap.put("bestFrame", data.getBestFrame());
                    paraMap.put("modifyDate", DateTimeUtil.getCurrentDate());
                    paraMap.put("modifyTime", DateTimeUtil.getCurrentTime());
                    fcOrderMapper.updateFcorderFaceByQuery(paraMap);
                    resultMap.put("bestFrame", data.getBestFrame());
                    resultMap.put("success", true);
                    resultMap.put("code", "200");
                    resultMap.put("message", "识别通过");
                } else {
                    paraMap.put("orderNo", orderNo);
                    paraMap.put("personId", personId);
                    paraMap.put("IsValid", "1");
                    paraMap.put("IsPass", "2");
                    paraMap.put("modifyDate", DateTimeUtil.getCurrentDate());
                    paraMap.put("modifyTime", DateTimeUtil.getCurrentTime());
                    fcOrderMapper.updateFcorderFaceByQuery(paraMap);
                    resultMap.put("code", "300");
                    resultMap.put("success", true);
                    resultMap.put("message", "识别未通过");
                }
            }
        } catch (Exception e) {

            Log.info("人脸识别状态查询接口异常：" + e);
            resultMap.put("code", "500");
            resultMap.put("success", false);
            resultMap.put("message", "人脸识别状态查询接口异常");
        }
        return resultMap;
    }

    //更新订单付款人信息
    public void updatePayPersonId(String orderItemNo, String personId) {
        FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(orderItemNo);
        fcOrderItem.setPayPersonId(personId);
        fcOrderItem = CommonUtil.initObject(fcOrderItem, "UPDATE");
        fcOrderItemMapper.updateByPrimaryKeySelective(fcOrderItem);
    }

    /**
     * 订单查询-去投保状态校验
     *
     * @param token
     * @param orderNo
     * @return
     */
    public String checkInsured(String token, String orderNo, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, String>> orderInfoList = new ArrayList<>();
        try {
            if (StringUtil.isEmpty(orderNo) || StringUtil.isEmpty(ensureCode)) {
                return JSON.toJSONString(ResultUtil.error("去投保校验参数不能为空!"));
            }
            GlobalInput globalInputone = userService.getSession(token);
            String ensureCod = globalInputone.getEnsureCode();
            if (StringUtil.isEmpty(ensureCod)) {
                GlobalInput globalInput = new GlobalInput();
                String userinfo = redisUtil.get(token);
                globalInput = JSON.parseObject(userinfo, GlobalInput.class);
                globalInput.setEnsureCode(ensureCode);
                Integer loginValidity = Integer.parseInt(myProps.getTermValidityLogin());
                redisUtil.put(token, JSON.toJSONString(globalInput), loginValidity);
            }
//            若订单处于未锁定状态，进入到“投保信息”修改页面；若订单处于已锁定状态，进入到“确认签名”页面
            FCOrder fcOrder = fcOrderMapper.selectByPrimaryKey(orderNo);
            String isLock = fcOrder.getIsLock();//订单是否锁定：0-未锁定  1-已锁定
            if (!StringUtil.isEmpty(isLock) && isLock.equals("0")) {
                Log.info("该订单属于未锁定状态，进入“投保信息”页面");
                resultMap.put("isLock", "0");
                resultMap.put("note", "该订单属于未锁定状态，进入“投保信息”页面");
            } else if (!StringUtil.isEmpty(isLock) && isLock.equals("1")) {
                Log.info("该订单属于已锁定状态，进入“确认签名”页面");
                resultMap.put("isLock", "1");
                resultMap.put("note", "该订单属于已锁定状态，进入“确认签名”页面");
            } else {
                Map<String, Object> error = ResultUtil.error("该订单的锁定状态为：" + fcOrder.getIsLock());
                return JSON.toJSONString(error);
            }
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "去投保状态校验成功。");
        } catch (Exception e) {
            Log.info("DailyPlanPhoneService.checkInsured error", e);
            resultMap.put("success", false);
            resultMap.put("code", "500");
            resultMap.put("message", "去投保状态校验失败。");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 投保人告知查询接口
     *
     * @param token
     * @param orderItemNo
     * @return
     */
    public String queryAppntImpartInfophone(String token, String orderItemNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(token);
            String grpNo = globalInput.getGrpNo();
            Map<String, String> params = new HashMap<String, String>();
            params.put("orderItemNo", orderItemNo);
//            List<FCAppntImpartInfo> fcAppntImpartInfoList = fcAppntImpartInfoMapper.selectList(params);
            List<FCAppntImpartInfoPersonal> fcAppntImpartInfoPhoneList = fcAppntImpartInfoPersonalMapper.selectList(params);
            List<FCAppntImpartInfoPersonal> fcAppntImpartInfoPersonals = fcAppntImpartInfoPersonalMapper.selectAllByOrderItemNo(orderItemNo);
            for (FCAppntImpartInfoPersonal fcAppntImpartInfoPersonal : fcAppntImpartInfoPersonals) {
                String impartCode1 = fcAppntImpartInfoPersonal.getImpartCode();
                if (StringUtil.isEmpty(impartCode1)) {
                    return JSON.toJSONString(ResultUtil.error("封装数据时没有发现健康告知信息！"));
                }
                if (impartCode1.equals("11201-A")) {//驾驶本
                    String impartParamModle = fcAppntImpartInfoPersonal.getImpartParamModle();//传回的值为：是/C1驾驶证/   需要切割。
                    String sign = impartParamModle.split("/")[0];//“是” 或者“否”
                    if (sign.equals("是")) {
                        String DrivearrNo = impartParamModle.split("/")[1];//代表驾驶证的码值
                        String drivearr = fdCodeMapper.selectNameByCode("Drivearr", DrivearrNo);
                        resultMap.put("drivearr", drivearr);
                    }
                }
            }
            if (fcAppntImpartInfoPhoneList != null && fcAppntImpartInfoPhoneList.size() > 0) {
                Map<String, String> map = new HashMap<String, String>();
                for (FCAppntImpartInfoPersonal fcAppntImpartInfoPersonal : fcAppntImpartInfoPhoneList) {
                    map.put(fcAppntImpartInfoPersonal.getImpartCode(), fcAppntImpartInfoPersonal.getImpartParamModle());
                }
                resultMap.put("data", map);
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "投保人告知查询成功！");
                Log.info("投保人告知查询成功。");
            } else {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", "没有投保告知信息！");
                Log.info("没有投保告知信息！");
            }
        } catch (Exception e) {

            Log.info("系统异常：投保人告知查询失败。");
            throw new RuntimeException("系统异常：投保人告知查询失败。");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 删除订单
     *
     * @param authorization
     * @param orderNo
     * @param orderitemNo
     * @return
     */
    @Transactional
    public String deleteorder(String authorization, String orderNo, String orderitemNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(authorization);
            String perNo = globalInput.getCustomNo();
            // 应该只删除订单就可以了。不需要删除个人表因为其他订单需要个人表信息呢。
            //.1`. 校验参数是否存在
            if (StringUtil.isEmpty(orderitemNo) || StringUtil.isEmpty(orderNo)) {
                Map<String, Object> error = ResultUtil.error("请求参数不能为空！");
                return JSON.toJSONString(error);
            }
            FCOrder fcOrder = fcOrderMapper.selectByPrimaryKey(orderNo);
            FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(orderitemNo);
            FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectByPrimaryKey(orderitemNo);
            String orderItemDetailNo = fcOrderItem.getOrderItemDetailNo();
            FCOrderItemDetail fcOrderItemDetail = fcOrderItemDetailMapper.selectByPrimaryKey_No(orderItemDetailNo);
            String personID = fcOrderInsured.getPersonID();
            String idNo = fcOrderInsured.getIDNo();
            //2. 校验订单表是否存在
            if (fcOrder == null || fcOrderItem == null || fcOrderItemDetail == null) {
                Map<String, Object> error = ResultUtil.error("订单表为空！");
                return JSON.toJSONString(error);
            }
            //3. 删除订单表
            fcOrderItemDetailMapper.deleteByKey(orderItemDetailNo);
            fcOrderItemMapper.deleteByPrimaryKey(orderitemNo);
            fcOrderMapper.deleteByPrimaryKey(orderNo);
            //4.删除受益人表
            List<FCOrderBnfRela> fcOrderBnfRelas = fcOrderBnfRelaMapper.selectlistByOrderItemNo(orderitemNo);
            for (FCOrderBnfRela fcOrderBnfRela : fcOrderBnfRelas) {
                String bnfNo = fcOrderBnfRela.getBnfNo();
                fcOrderBnfMapper.deleteByPrimaryKey(bnfNo);
            }
            fcOrderBnfRelaMapper.deleteByPrimaryKey(orderitemNo);
            //5. 删除被保人表
            fcOrderInsuredMapper.deleteByPrimaryKey(orderitemNo);
            //6. 删除家属的个人表，如果是员工本人则不删除
        /*     Map<String, String> map = new HashMap<String, String>();
            map.put("perNo", perNo);
            map.put("personID", personID);
            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPrimaryKey(map);
            Integer integer = fcOrderInsuredMapper.selectByPersonID(personID);
           if (fcStaffFamilyRela != null && integer == 1) {
                String relation = fcStaffFamilyRela.getRelation();
                if (!StringUtil.isEmpty(relation) && !relation.equals("0")) {
                    fcPersonMapper.deleteByPrimaryKey(personID);
                    Map<String, String> map1 = new HashMap<>();
                    map1.put("perNo", perNo);
                    map1.put("personID", personID);
                    fcStaffFamilyRelaMapper.deleteByPrimaryKey(map1);
                }
            }*/

            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "删除订单成功。");
        } catch (Exception e) {

            Log.info("系统异常：删除订单失败。");
            throw new RuntimeException("系统异常：删除订单失败。");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 修改信息
     *
     * @param authorization
     * @param orderNo
     * @param orderitemNo
     * @param ensureCode
     * @param grpNo
     * @param sumPrem
     * @param grpName
     * @return
     */
    public String updateInfo(String authorization, String orderNo, String orderitemNo, String ensureCode, String grpNo, String sumPrem, String grpName) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, String> maps = new HashMap<>();
        Map<String, String> mapss = new HashMap<>();
        List<String> list = new ArrayList<>();
        List<Map<String, String>> lists = new ArrayList<>();
        try {
            GlobalInput globalInput = userService.getSession(authorization);
            String perNo = globalInput.getCustomNo();
            FCStaffFamilyRela fcStaffFamilyRela1 = fcStaffFamilyRelaMapper.selectByPerNo(perNo);
            String personID1 = fcStaffFamilyRela1.getPersonID();//员工personID
            FCOrder fcOrder = fcOrderMapper.selectByPrimaryKey(orderNo);
            FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(orderitemNo);
            String payPersonId = fcOrderItem.getPayPersonId();
            //查询支付方式
            Map<String, Object> param = new HashMap<>();
            param.put("ensureCode", ensureCode);
            param.put("grpNo", grpNo);
            param.put("configNo", "021");
            String fcEnsureConfig_021 = fcEnsureConfigMapper.selectOnlyValue(param);//0-企业定期结算 1-个人实时支付
            if (!StringUtil.isEmpty(fcEnsureConfig_021) && fcEnsureConfig_021.equals("0")) {
//                i.	若基础单配置线上投保支付方式为“企业定期结算”，选项包括：企业名称。
                resultMap.put("sumPrem", sumPrem);
                list.add(grpName);
                maps.put("name", grpName);
                maps.put("flag", "0");
                resultMap.put("payName", list);
                resultMap.put("Name", maps);

                mapss.clear();
                mapss.put("grpName", grpName);
                mapss.put("flag", "0");
                lists.add(mapss);
                resultMap.put("grpName", lists);

            } else if (!StringUtil.isEmpty(fcEnsureConfig_021) && fcEnsureConfig_021.equals("1")) {
//                ii.	若基础单配置线上投保支付方式为“个人实时支付”，被保险人为“本人”时，选项包括：员工；被保险人为非“本人”时，选项包括：员工和被保险人。
                resultMap.put("sumPrem", sumPrem);
                String perNoThegrp = fcPerInfoMapper.selectperNobyPerNo(perNo, grpNo);//此处的 perNo  是该员工在该企业下对应的  perNo。
                FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectByPrimaryKey(orderitemNo);
                String personID = fcOrderInsured.getPersonID();//被保人personID
                Map<String, String> map = new HashMap<String, String>();
                map.put("perNo", perNoThegrp);
                map.put("personID", personID);
                FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPrimaryKey(map);
                String relation = fcStaffFamilyRela.getRelation();
                if (!StringUtil.isEmpty(relation) && relation.equals("0")) {
                    //代表被保险人是 员工本人
                    list.add(fcOrderInsured.getName());
                    resultMap.put("payName", list);


                    mapss.clear();
                    mapss.put("name", globalInput.getName());
                    mapss.put("personID", personID1);
                    mapss.put("flag", "0");

                    lists.add(mapss);
                    resultMap.put("ThisPay", lists);
                }
                if (!StringUtil.isEmpty(relation) && !relation.equals("0")) {
                    //代表 员工 给家属投保
                    list.add(fcOrderInsured.getName());
                    list.add(globalInput.getName());
                    resultMap.put("payName", list);
                    if (personID.equals(payPersonId)) {
                        //家属为付款人
                        maps.clear();
                        mapss.clear();
                        maps.put("name", fcOrderInsured.getName());
                        maps.put("personID", payPersonId);
                        maps.put("flag", "0");

                        mapss.put("name", globalInput.getName());
                        mapss.put("personID", personID1);
                        mapss.put("flag", "1");
                    } else {
                        //员工本人为付款人
                        maps.clear();
                        mapss.clear();
                        maps.put("name", fcOrderInsured.getName());
                        maps.put("personID", personID);
                        maps.put("flag", "1");

                        mapss.put("name", globalInput.getName());
                        mapss.put("personID", personID1);
                        mapss.put("flag", "0");

                    }


                    lists.add(maps);
                    lists.add(mapss);
                    resultMap.put("Name", maps);
                    resultMap.put("ThisPay", lists);
                }
            }
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "修改订单成功。");
        } catch (Exception e) {

            Log.info("系统异常：修改订单失败。");
            throw new RuntimeException("系统异常：修改订单失败。");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * 订单查询-申请发票
     *
     * @param authorization
     * @param orderNo
     * @return
     */
    public String RequestInvoice(String authorization, String orderNo, String orderitemNo) {
        Map<String, Object> resultMap = new HashMap<>();
        List<String> list = new ArrayList<>();
        try {
            GlobalInput globalInput = userService.getSession(authorization);
            FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(orderitemNo);
            Double selfPrem = fcOrderItem.getSelfPrem();


            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "申请发票成功。");
        } catch (Exception e) {

            Log.info("系统异常：申请发票失败。");
            throw new RuntimeException("系统异常:申请发票失败。");
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 查询核保结论
     *
     * @param authorization
     * @param orderitemNo
     * @return
     */
    public String getReview(String authorization, String orderitemNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            GlobalInput globalInput = userService.getSession(authorization);
            if (StringUtil.isEmpty(orderitemNo)) {
                Map<String, Object> error = ResultUtil.error("请求入参不能为空！");
                return JSON.toJSONString(error);
            }
            Map<String, String> review = fcOrderMapper.getReview(orderitemNo);
            resultMap.put("review", review);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "查询核保结论成功。");
        } catch (Exception e) {

            Log.info("系统异常：查询核保结论失败。");
            throw new RuntimeException("系统异常:查询核保结论失败。");
        }
        return JSON.toJSONString(resultMap);
    }


    /**
     * 保存付款人
     *
     * @param authorization
     * @param orderItemNo
     * @param payPersonId
     * @return
     */
    public String saveInfo(String authorization, String orderItemNo, String payPersonId, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (StringUtil.isEmpty(orderItemNo) || StringUtil.isEmpty(payPersonId) || StringUtil.isEmpty(ensureCode)) {
                Map<String, Object> error = ResultUtil.error("请求参数不能为空！");
                return JSON.toJSONString(error);
            }
            // 参数容器
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("ensureCode", ensureCode);
            params.put("configNo", "021");
            String fcEnsureConfig_021 = fcEnsureConfigMapper.selectOnlyValue(params);//（0-企业定期结算 1-个人实时支付）
            if (!StringUtil.isEmpty(fcEnsureConfig_021) && fcEnsureConfig_021.equals("1")) {
                FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(orderItemNo);
                fcOrderItem.setPayPersonId(payPersonId);
                fcOrderItem.setModifyDate(DateTimeUtil.getCurrentDate());
                fcOrderItem.setModifyTime(DateTimeUtil.getCurrentTime());
                fcOrderItemMapper.updateByPrimaryKeySelective(fcOrderItem);
            }
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "保存付款人成功。");
        } catch (Exception e) {

            Log.info("保存付款人失败。");
            throw new RuntimeException("保存付款人失败。");
        }
        return JSON.toJSONString(resultMap);
    }

    @Transactional
    public String savePrem(String authorization, String orderItemNo, String prem, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            String grpNo = fcEnsure.getGrpNo();
            if (StringUtil.isEmpty(orderItemNo) || StringUtil.isEmpty(prem) || StringUtil.isEmpty(ensureCode) || StringUtil.isEmpty(grpNo)) {
                Map<String, Object> error = ResultUtil.error("请求参数不能为空！");
                return JSON.toJSONString(error);
            }
            //获取支付方式
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("ensureCode", ensureCode);
            param.put("grpNo", grpNo);
            param.put("configNo", "021");
            //交费方式 0企业 1个人
            String orderPayType = fcEnsureConfigMapper.selectOnlyValue(param);
            FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(orderItemNo);
            if (!StringUtil.isEmpty(orderPayType) && orderPayType.equals("1")) {
                fcOrderItem.setSelfPrem(Double.valueOf(prem));
            } else if (!StringUtil.isEmpty(orderPayType) && orderPayType.equals("0")) {
                fcOrderItem.setGrpPrem(Double.valueOf(prem));
            }
            fcOrderItemMapper.updateByPrimaryKeySelective(fcOrderItem);
            //既然代码走到了这里有两种途径 1. 正常健康告知到确认签名页面再到此页面  2. 订单查询 去支付 直接调到这个页面，这个时候需要把状态改为投保中
            String orderNo = fcOrderItem.getOrderNo();
            FCOrder fcOrder = fcOrderMapper.selectByPrimaryKey(orderNo);
            fcOrder.setOrderStatus("03");
            fcOrderMapper.updateByPrimaryKeySelective(fcOrder);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "更新保费成功。");
        } catch (Exception e) {

            throw new RuntimeException("更新保费失败。");
        }
        return JSON.toJSONString(resultMap);
    }

    public String changePrem(String authorization, String orderItemNo, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            String grpNo = fcEnsure.getGrpNo();
            if (StringUtil.isEmpty(orderItemNo) || StringUtil.isEmpty(ensureCode) || StringUtil.isEmpty(grpNo)) {
                Map<String, Object> error = ResultUtil.error("请求参数不能为空！");
                return JSON.toJSONString(error);
            }
            FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(orderItemNo);
            fcOrderItemMapper.updateByPrimaryKeySelective(fcOrderItem);
            //保费因个别原因发生改变，导致 校验失败，改变状态为 投保中 ，使用户重新投保
            String orderNo = fcOrderItem.getOrderNo();
            FCOrder fcOrder = fcOrderMapper.selectByPrimaryKey(orderNo);
            fcOrder.setOrderStatus("03");
            fcOrderMapper.updateByPrimaryKeySelective(fcOrder);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "更新状态成功。");
        } catch (Exception e) {

            throw new RuntimeException("更新状态失败。");
        }
        return JSON.toJSONString(resultMap);
    }

    public String checkShare(String authorization, String orderItemNo, String perNo, String prem) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(orderItemNo);
            String orderItemDetailNo = fcOrderItem.getOrderItemDetailNo();
            FCOrderItemDetail fcOrderItemDetail = fcOrderItemDetailMapper.selectByPrimaryKey_No(orderItemDetailNo);
            String payFrequency = fcOrderItemDetail.getPayFrequency();
            String payPeriod = fcOrderItemDetail.getPayPeriod();
            //计算总保费
            Double total = 0.0;
            if ("1".equals(payFrequency)) {
                total = Double.valueOf(prem);
            } else {
                int year = 0;
                switch (payPeriod) {
                    case "02":
                        year = 5;
                        break;
                    case "03":
                        year = 10;
                        break;
                    case "04":
                        year = 20;
                        break;
                    default:
                        break;
                }
                total = CommonUtil.mul(Double.valueOf(prem), Double.valueOf(year));
            }
            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectByPerNo(perNo);
            String StaffpersonID = fcStaffFamilyRela.getPersonID();
            FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(StaffpersonID);
            String idType = fcPerson.getIDType();

            resultMap = checkStaffImage(total, orderItemNo, StaffpersonID, idType);
            if (!resultMap.get("code").equals("200")) {
                return JSON.toJSONString(resultMap);
            }
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "分享校验成功。");
        } catch (Exception e) {

            throw new RuntimeException("分享校验失败。");
        }
        return JSON.toJSONString(resultMap);

    }

    public Map<String, Object> checkStaffImage(Double totalPrem, String orderItemNo, String StaffpersonID, String idType) {
        Map<String, Object> resultMap = new HashMap<>();
        List<ImageReq> staffImage = new ArrayList<ImageReq>();
        //证件
        List<String> idTypeImageList1 = new ArrayList<>();
        //签名
        List<String> signImageList1 = new ArrayList<>();
        //本人拍照
        List<String> photoImageList1 = new ArrayList<>();
        try {
            if (StringUtil.isEmpty(totalPrem) && StringUtil.isEmpty(orderItemNo) && StringUtil.isEmpty(StaffpersonID) && StringUtil.isEmpty(idType)) {
                return ResultUtil.error("请求参数不能为空！");
            }
            List<FCPersonImage> fCPersonImageList = fcPersonImageMapper.selectByOrderItemNo(orderItemNo);
            for (FCPersonImage fCPersonImage : fCPersonImageList) {
                String relation = fCPersonImage.getRelation();
                if (relation.equals("0")) {
                    ImageReq imageReq = new ImageReq();
                    imageReq.setImageType(fCPersonImage.getImageType());
                    staffImage.add(imageReq);
                }
            }
            if (staffImage != null) {
                for (ImageReq imageReq : staffImage) {
                    String imageType = imageReq.getImageType();
                    switch (imageType) {
                        case "0801":
                            idTypeImageList1.add(imageType);
                            break;
                        case "0804":
                            signImageList1.add(imageType);
                            break;
                        case "0805":
                            photoImageList1.add(imageType);
                            break;
                        default:
                            break;
                    }
                }
                //如果总保费大于20W
                if (new BigDecimal(totalPrem).compareTo(new BigDecimal("200000")) > 0) {
                    if (idTypeImageList1.size() == 0) {
                        return ResultUtil.error("请先上传您的证件影像！");
                    }
                }
                if (idTypeImageList1.size() > 0) {
                    if ("0".equals(idType)) {
                        if (idTypeImageList1.size() != 2) {
                            return ResultUtil.error("证件影像件只能上传2张！");
                        }
                    } else {
                        if (idTypeImageList1.size() > 5) {
                            return ResultUtil.error("证件影像件最多上传5张！");
                        }
                    }
                }
                if (signImageList1.size() == 0) {
                    return ResultUtil.error("请先进行主被保险人（员工）签字！");
                }
                //校验是否人脸识别
                if ("0".equals(idType)) {
                    int faceNum = fcPersonImageMapper.selectOrderFaceishad(orderItemNo, StaffpersonID);
                    if (faceNum < 1) {
                        return ResultUtil.error("请先进行人脸识别！");
                    }
                } else {
                    if (photoImageList1.size() == 0) {
                        return ResultUtil.error("请手持证件拍照！");
                    }
                }
            } else {
                return ResultUtil.error("请先上传您的证件影像！");
            }
            resultMap = ResultUtil.success("分享校验中主被保人影像校验成功");
        } catch (Exception e) {
            resultMap = ResultUtil.error("分享校验中主被保人影像校验失败！");
        }
        return resultMap;

    }

    public String unlock(String authorization, String orderItemNo, String shareDetalNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (StringUtil.isEmpty(orderItemNo) || StringUtil.isEmpty(shareDetalNo)) {
                return JSON.toJSONString(ResultUtil.error("unlock 接口请求参数不能为空！"));
            }
            GlobalInput globalInput = userService.getSession(authorization);
            String name = globalInput.getName();
            String ensureCode = globalInput.getEnsureCode();
            String userName = globalInput.getUserName();
            FdUser userInfo = fdUserMapper.findUserForLogin(userName, globalInput.getCustomType());
            String userNo = userInfo.getUserNo();
            FCOrder fcOrder = fcOrderMapper.selectOrderByOrderItemNo(orderItemNo);

            String isLock = fcOrder.getIsLock();
            if (!StringUtil.isEmpty(isLock) && isLock.equals("1")) {
                //已锁定状态
                fcOrder.setIsLock("0");
                fcOrderMapper.updateByPrimaryKeySelective(fcOrder);
                resultMap.put("message", "解除锁定成功。");
            } else {
                //未锁定状态
                resultMap.put("message", "被保险人已签字提交，当前处于未锁定状态，请刷新页面！");
            }
            //更新分享链接为无效的
            fcOrderItemMapper.updateShareStatus(shareDetalNo);
            //这时候应该返回一些分享的流水号，为下次分享做准备
            String shareDetalNonew = maxNoService.createMaxNo("shareDetalNo", "", 20);
            FcOrderItemShareRela fcOrderItemShareRela = new FcOrderItemShareRela();
            fcOrderItemShareRela.setOrderItemNo(orderItemNo);
            fcOrderItemShareRela.setShareDetalNo(shareDetalNonew);
            fcOrderItemShareRela.setShareState("0");
            fcOrderItemShareRela.setIsLock("0");
            fcOrderItemShareRela.setOperator(globalInput.getUserNo());
            fcOrderItemShareRela = (FcOrderItemShareRela) CommonUtil.initObject(fcOrderItemShareRela, "INSERT");
            fcOrderItemMapper.insertShareRelaSelective(fcOrderItemShareRela);
            resultMap.put("shareDetalNo", shareDetalNonew);
            resultMap.put("staffName", name);
            resultMap.put("userNo", userNo);
            resultMap.put("grpOrderNo", ensureCode);
            resultMap.put("success", true);
            resultMap.put("code", "200");
        } catch (Exception e) {
            resultMap = ResultUtil.error("解锁按钮失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    public String submit(String authorization, String orderItemNo, String perNo, String shareDetalNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (StringUtil.isEmpty(orderItemNo) || StringUtil.isEmpty(perNo)) {
                return JSON.toJSONString(ResultUtil.error("submit 接口请求参数不能为空！"));
            }
            //判断电子签名和本人拍照/人脸识别
            Map<String, Object> map = checkstaffFamilyImage(orderItemNo, perNo);
            if ("304".equals(map.get("code")) || "500".equals(map.get("code"))) {
                return JSON.toJSONString(map);
            }
            FCOrder fcOrder = fcOrderMapper.selectOrderByOrderItemNo(orderItemNo);
            FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectByPrimaryKey(orderItemNo);
            //家属personID （被保人）
            FCPerInfo fcPerInfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
            String name = fcPerInfo.getName();//员工姓名
            String isLock = fcOrder.getIsLock();
            if (!StringUtil.isEmpty(isLock) && isLock.equals("1")) {
                //订单未解锁。即连接有效  ，弹出提示“您的签名已提交，请通知xxx进行后续的操作”，当前提示不可关闭，此时订单解锁，分享的链接失效。
                fcOrder.setIsLock("0");
                fcOrderMapper.updateByPrimaryKeySelective(fcOrder);
                resultMap.put("message", "您的签名已提交，请通知 " + name + " 进行后续的操作");

            } else {
                //订单已解锁。即连接失效，弹出提示“当前链接已失效或无效”，当前提示不可关闭。
                resultMap.put("message", "当前链接已失效或无效。");
            }
            //更新分享链接为无效的
            fcOrderItemMapper.updateShareStatus(shareDetalNo);
            resultMap.put("success", true);
            resultMap.put("code", "200");
        } catch (Exception e) {
            resultMap = ResultUtil.error("分享确认提交失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    public Map<String, Object> checkstaffFamilyImage(String orderItemNo, String perNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            FCOrderInsured fcOrderInsured = fcOrderInsuredMapper.selectByPrimaryKey(orderItemNo);
            String personID = fcOrderInsured.getPersonID();//家属 personID
            String birthday = fcOrderInsured.getBirthday();
            String insuredIDType = fcOrderInsured.getIDType();
            int age = DateTimeUtil.getCurrentAge(birthday, DateTimeUtil.getCurrentDate());
            List<FCPersonImage> fCPersonImageList = fcPersonImageMapper.selectByOrderItemNo(orderItemNo);
            Map<String, String> params = new HashMap<>();
            params.put("perNo", perNo);
            params.put("personId", personID);
            List<Map<String, String>> orderInsuredInfos = fcPersonMapper.selectFcPersonInfoByParams(params);
            String relation = "";
            if (StringUtil.isEmpty(personID)) {
                return ResultUtil.error("校验影像方法参数 被保人 personid为空！", "304");
            }
            List<ImageReq> staffFamilyImage = new ArrayList<ImageReq>();
            for (FCPersonImage fCPersonImage : fCPersonImageList) {
                relation = fCPersonImage.getRelation();
                if (!relation.equals("0")) {
                    ImageReq imageReq = new ImageReq();
                    imageReq.setImageType(fCPersonImage.getImageType());
                    staffFamilyImage.add(imageReq);
                }
            }
            //签名
            List<String> signImageList2 = new ArrayList<>();
            //本人拍照
            List<String> photoImageList2 = new ArrayList<>();
            if ((staffFamilyImage != null || staffFamilyImage.size() != 0)) {
                for (ImageReq imageReq : staffFamilyImage) {
                    String imageType = imageReq.getImageType();
                    switch (imageType) {
                        case "0804":
                            signImageList2.add(imageType);//签名  根据 关系来区分是本人的还是 家属的
                            break;
                        case "0805":
                            photoImageList2.add(imageType);//家属拍照
                            break;
                        default:
                            break;
                    }
                }
                if (age < 18 && relation.equals("3")) {
                    Log.info("被保人是家属，且年龄小于18并且关系为子女时不需要人脸识别/本人拍照", "304");
                } else {
                    if ("0".equals(insuredIDType)) {
                        int faceNum = fcPersonImageMapper.selectOrderFaceishad(orderItemNo, personID);
                        if (faceNum < 1) {
                            return ResultUtil.error("被保人请先进行人脸识别！", "304");
                        }
                    } else {
                        if (photoImageList2.size() == 0) {
                            return ResultUtil.error("被保人请先手持证件拍照！", "304");
                        }
                    }
                    //-------被保人签名---------------
                    if (signImageList2.size() == 0) {
                        return ResultUtil.error("被保人请先进行本人签字！", "304");
                    }
                }
            }
            resultMap = ResultUtil.success("被保人影像校验成功");
        } catch (Exception e) {
            Log.info("被保人影像文件校验失败：" + e);
            resultMap = ResultUtil.success("被保人影像校验失败！");
        }
        return resultMap;


    }

    public String makeToken(String userNo, String ensureCode) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (StringUtil.isEmpty(userNo)) {
                return JSON.toJSONString(ResultUtil.error("分享更新token 请求参数不能为空！"));
            }
            //1. 判断token是否失效。如果失效，提示你的token已经失效。  没有失效的话，根据token的key值查询的value 重新创建token。
            GlobalInput userInfo = new GlobalInput();
            Map<String, Object> params = new HashMap<String, Object>();
            userInfo = fdUserMapper.findGlobalInfoByUserNo(userNo);
            String loginSerialNo = maxNoService.createMaxNo("LoginSerialNo", "", 20);
            params.put("perNo", userInfo.customNo);
            params.put("sysDate", DateTimeUtil.getCurrentDate());
            if (ensureCode != null) {
                userInfo.setEnsureCode(ensureCode);
            }
            String oldLoginDate = null;
            String oldLoginTime = null;
            userInfo.setLoginSerialNo(loginSerialNo);
            userInfo.setOldLoginDate(oldLoginDate);
            userInfo.setOldLoginTime(oldLoginTime);
            // 生成token，将用户信息放入redis，token作为key，token返回前台
            String token = UUID.randomUUID().toString().replaceAll("-", "");
            Integer loginValidity = Integer.parseInt(myProps.getTermValidityLogin());
            redisUtil.put(token, JSON.toJSONString(userInfo), loginValidity);
            resultMap.put("token", token);
            resultMap.put("success", true);
            resultMap.put("code", "200");
        } catch (Exception e) {

            resultMap = ResultUtil.error("分享更新token失败！");
        }
        return JSON.toJSONString(resultMap);
    }

    public String checkLock(String authorization, String orderItemNo, String shareDetalNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (StringUtil.isEmpty(orderItemNo) || StringUtil.isEmpty(shareDetalNo)) {
                return JSON.toJSONString(ResultUtil.error("checkLock 接口请求参数不能为空！"));
            }
            //判断这个分享链接是否有效是否锁定。
            FcOrderItemShareRela fcOrderItemShareRela = fcOrderItemMapper.selectShareRela(shareDetalNo);
            String shareState = fcOrderItemShareRela.getShareState();
            if (StringUtil.isEmpty(shareState) || (!StringUtil.isEmpty(shareState) && shareState.equals("1"))) {
                return JSON.toJSONString(ResultUtil.error("当前链接已失效或无效", "303"));
            }

            FCOrder fcOrder = fcOrderMapper.selectOrderByOrderItemNo(orderItemNo);
            String isLock = fcOrder.getIsLock();
            if (!StringUtil.isEmpty(isLock) && isLock.equals("0")) {
                return JSON.toJSONString(ResultUtil.error("当前链接已失效或无效", "303"));
            }
            resultMap.put("success", true);
            resultMap.put("code", "200");
        } catch (Exception e) {
            resultMap = ResultUtil.error("分享锁定查询失败！");
        }
        return JSON.toJSONString(resultMap);

    }

    public String tolock(String authorization, String orderItemNo) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (StringUtil.isEmpty(orderItemNo)) {
                return JSON.toJSONString(ResultUtil.error("tolock 接口请求参数不能为空！"));
            }
            FCOrder fcOrder = fcOrderMapper.selectOrderByOrderItemNo(orderItemNo);
            fcOrder.setIsLock("1");
            fcOrderMapper.updateByPrimaryKeySelective(fcOrder);
            //锁定订单的同时把分享链接锁定  查找到唯一一个  未锁定、有效的 数据。并且更改锁定状态。
            fcOrderItemMapper.updateShareToLock(orderItemNo);
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "锁定订单成功！");
        } catch (Exception e) {
            resultMap = ResultUtil.error("锁定订单失败！");
        }
        return JSON.toJSONString(resultMap);

    }

}

