package com.sinosoft.eflex.service.dailyinsure;

import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.InsuredPeriodTypeEnum;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.OssEntity.OssEntity;
import com.sinosoft.eflex.model.confirmInsure.ConfirmInsureResp;
import com.sinosoft.eflex.model.confirmInsure.HealthNoticeInfo;
import com.sinosoft.eflex.model.confirmInsure.InsuredConfirmPageReq;
import com.sinosoft.eflex.model.confirmInsure.PeopleInsureInfo;
import com.sinosoft.eflex.model.dailyplan.AppntImpart;
import com.sinosoft.eflex.model.insurePlanPage.RiskDutyInfo;
import com.sinosoft.eflex.model.makeProposalForm.*;
import com.sinosoft.eflex.service.InsurePlanPage.InsurePlanService;
import com.sinosoft.eflex.service.MaxNoService;
import com.sinosoft.eflex.util.*;
import com.sinosoft.eflex.util.oss.OssUtils;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.io.xml.DomDriver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2020/6/11
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DailyMakeProposalFormService {


    private final FDCodeMapper fdCodeMapper;
    private final FCEnsureMapper fcEnsureMapper;
    private final FCGrpInfoMapper fcGrpInfoMapper;
    private final FCEnsureConfigMapper fcEnsureConfigMapper;
    private final FCOrderItemMapper fcOrderItemMapper;
    private final FCOrderItemDetailMapper fcOrderItemDetailMapper;
    private final FCOrderInsuredMapper fcOrderInsuredMapper;
    private final FCGrpOrderMapper fcGrpOrderMapper;
    private final FDAgentInfoMapper fdAgentInfoMapper;
    private final FCOrderBnfMapper fcOrderBnfMapper;
    private final FCPersonMapper fcPersonMapper;
    private final FCAppntImpartInfoPersonalMapper fcAppntImpartInfoPersonalMapper;
    private final FCOrderPayMapper fcOrderPayMapper;
    private final MyProps myProps;
    private final FCPersonImageMapper fcPersonImageMapper;
    private final InsurePlanService insurePlanService;
    private final FDFTPInfoMapper fdftpInfoMapper;
    private final MaxNoService maxNoService;
    private final OssUtils ossUtils;


    @Transactional
    public Map<String, String> MakeProposalForm(String ensureCode, String perNo, String orderItemNo) {
        log.info("自然人封装数据  电子投保书方法开始》》》》》》》》》》》》》》");
        Map<String, String> resultMap = new HashMap<String, String>();
        resultMap.put("code", "500");
        try {
            /** 请求报文拼接 */
            XStream xStream = new XStream(new DomDriver());
            DATASETS datasets = new DATASETS();
            datasets.setControl("01");
            /**
             * 影像类型编码集合
             */
            ImageTypes imageTypes = new ImageTypes();
            imageTypes.setImageType("511051");
            datasets.setImageTypes(imageTypes);
            /**
             * 影像报文集合
             */
            DATASET dataset = new DATASET();
            DecimalFormat df = new DecimalFormat("#.00");
            // 福利信息
            FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
            // 获取代理人信息
            List<FDAgentInfo> fdAgentInfoList = fdAgentInfoMapper.selectAgentCode(fcEnsure.getClientNo());
            FDAgentInfo fdAgentInfo = fdAgentInfoList.get(0);
            // 团体订单信息
            FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByEnsureCode(ensureCode);
            // 获取企业信息
            FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(fcGrpOrder.getGrpNo());
            // 子订单信息
            FCOrderItem fcOrderItem = fcOrderItemMapper.selectByPrimaryKey(orderItemNo);
            // 查询订单信息
            Map<String, String> orderItemDetailInfo = fcOrderItemDetailMapper.selectOrderItemInfo(orderItemNo);
            // 获取付款人银行信息
            Map<String, String> orderPayInfo = fcOrderPayMapper.selectByOrderNo(fcOrderItem.getOrderNo());
            // 获取缴费方式
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("ensureCode", fcGrpOrder.getEnsureCode());
            param.put("grpNo", fcGrpOrder.getGrpNo());
            param.put("configNo", "021");
            // 交费方式 0企业 1个人
            String orderPayType = fcEnsureConfigMapper.selectOnlyValue(param);
            // 交费方式 0企业 1个人
            String payMoney = "";
            String PayMode = "";
            String payType = orderPayInfo == null ? ""
                    : orderPayInfo.get("PayType") == null ? "" : orderPayInfo.get("PayType");
            String PayModeCon = "";
            if ("0".equals(orderPayType)) {
                payMoney = String.valueOf(fcOrderItem.getGrpPrem());
                PayMode = "定期结算";
            }
            if ("1".equals(orderPayType)) {
                payMoney = String.valueOf(fcOrderItem.getSelfPrem());
                if ("03".equals(payType)) {
                    PayMode = "微信支付";
                } else {
                    PayMode = "银行转账";
                    PayModeCon = "银行转账";
                }
            }
            dataset.setContNo(fcGrpOrder.getGrpContNo());
            dataset.setPrtNo(fcOrderItem.getContNo());
            dataset.setOrderNo(fcOrderItem.getOrderNo());
            dataset.setManageComCode(fdAgentInfo.getManageCom());
            dataset.setManageComName("");
            dataset.setAgentCode(fdAgentInfo.getAgentCode());
            dataset.setAgentName(fdAgentInfo.getName());
            dataset.setAgentPhone(fdAgentInfo.getMobile());
            dataset.setCurrencyType("");
            dataset.setPayMoneySumFigures(df.format(Double.valueOf(payMoney)));
            // 金额大写
            dataset.setPayMoneySumWrods(NumberToCN.number2CNMontrayUnit(new BigDecimal(payMoney)));
            // 续期缴费方式
            dataset.setPayMode(PayModeCon);
            // 首期缴费方式 默认自交
            dataset.setNewPayMode(PayMode);
            dataset.setPayIntv(orderItemDetailInfo.get("payFrequencyName").equals("趸交") ? "一次性交清" : "年交");
            // 一年期产品是否自动续保
            dataset.setRNewFlag("");
            dataset.setSocialInsuFlag("");
            dataset.setBonusGetMode("");
            dataset.setAnnuityGetMode("");
            dataset.setAnnuityGetIntv("");
            dataset.setGrpType("05");
            dataset.setEPolicyType("02");
            /************* 银行信息 ***********************/
            dataset.setBankName(orderPayInfo == null ? ""
                    : StringUtils.isNotBlank(orderPayInfo.get("BankName")) ? orderPayInfo.get("BankName") : "");
            dataset.setBankAccNo(orderPayInfo == null ? ""
                    : StringUtils.isNotBlank(orderPayInfo.get("BankAccNo")) ? orderPayInfo.get("BankAccNo") : "");
            dataset.setBankAccName(orderPayInfo == null ? ""
                    : StringUtils.isNotBlank(orderPayInfo.get("BankAccName")) ? orderPayInfo.get("BankAccName") : "");
            dataset.setBankAccType(orderPayInfo == null ? ""
                    : orderPayInfo.get("BankAccType") == null ? "" : orderPayInfo.get("BankAccType"));
            dataset.setSignDate(DateTimeUtil.getCurrentDate());
            dataset.setAgentSignDate("");
            dataset.setLoanType("");
            dataset.setLoanOrg("");
            dataset.setLoanContNo("");
            dataset.setLoanMoney("");
            /************* 投保人信息 *************************/
            // 员工信息
            String relation = "0";
            Map<String, String> params = new HashMap<>();
            params.put("perNo", perNo);
            params.put("relation", relation);
            List<Map<String, String>> fcPersonList = fcPersonMapper.selectFcPersonInfoByParams(params);
            Map<String, String> staffInfo = fcPersonList.get(0);
            dataset.setAppntName(staffInfo.get("name"));
            dataset.setAppntBirthday(staffInfo.get("birthDate"));
            dataset.setAppntSex(staffInfo.get("sexName"));
            dataset.setAppntNationality(staffInfo.get("nativeplaceName"));
            dataset.setAppntIdTypeName(staffInfo.get("idTypeName"));
            dataset.setAppntIdNo(staffInfo.get("idNo"));
            dataset.setAppntIdExpDate(staffInfo.get("idTypeEndDate"));
            dataset.setAppntOccupationName(staffInfo.get("occupationCodeName"));
            dataset.setAppntOccupationCode(staffInfo.get("occupationCode"));
            dataset.setAppntWorkPlace(fcGrpInfo.getGrpName());
            dataset.setAppntAddress(staffInfo.get("provinceName") + staffInfo.get("cityName")
                    + staffInfo.get("countyName") + staffInfo.get("detaileAddress"));
            dataset.setAppntZipCode(staffInfo.get("zipCode"));
            dataset.setAppntPhone(staffInfo.get("mobilePhone"));
            dataset.setAppntHomePhone("");
            dataset.setAppntEmail(staffInfo.get("eMail"));
            /**************************** 被保人节点 ********************************/
            // Insured第二层
            List<InsuredsItem> Insureds = new ArrayList<>();
            // 被保人信息
            // 查询被保人信息
            Map<String, String> fcOrderInsured = fcOrderInsuredMapper.selectByOrderItemNo(orderItemNo);
            // 根据perNo和personId查询被保人信息
            params.clear();
            params.put("perNo", perNo);
            params.put("personId", fcOrderInsured.get("personId"));
            List<Map<String, String>> orderInsuredInfos = fcPersonMapper.selectFcPersonInfoByParams(params);
            Map<String, String> orderInsuredInfo = orderInsuredInfos.get(0);
            // Insured第三层
            InsuredsItem insuredsItem = new InsuredsItem();
            // TODO 被保险人类型
            insuredsItem.setInsuredType("第一被保险人");
            insuredsItem.setInsuredName(fcOrderInsured.get("name"));
            insuredsItem.setInsuredBirthday(fcOrderInsured.get("birthday"));
            insuredsItem.setInsuredSex(fcOrderInsured.get("sexName"));
            insuredsItem.setInsuredNationality(orderInsuredInfo.get("nativeplaceName"));
            insuredsItem.setInsuredIdTypeName(fcOrderInsured.get("idTypeName"));
            insuredsItem.setInsuredIdNo(fcOrderInsured.get("idNo"));
            insuredsItem.setInsuredIdExpDate(orderInsuredInfo.get("idTypeEndDate"));
            insuredsItem.setInsuredOccupationName(fcOrderInsured.get("occupationCodeName"));
            insuredsItem.setInsuredOccupationCode(fcOrderInsured.get("occupationCode"));
            insuredsItem.setInsuredWorkPlace(fcOrderInsured.get("relation").equals("00") ? fcGrpInfo.getGrpName() : "");
            dataset.setAppntAnnualIncome(StringUtil.isEmpty(fcOrderInsured.get("mainYearSalary")) ? "0.0"
                    : CommonUtil.mul(Double.parseDouble(fcOrderInsured.get("mainYearSalary")), 10000.0) + "");
            insuredsItem.setInsuredAddress(fcOrderInsured.get("provinceName") + fcOrderInsured.get("cityName")
                    + fcOrderInsured.get("countyName") + fcOrderInsured.get("detaileAddress"));
            insuredsItem.setInsuredZipCode(fcOrderInsured.get("zipCode"));
            insuredsItem.setInsuredPhone(fcOrderInsured.get("mobilePhone"));
            insuredsItem.setInsuredHomePhone("");
            insuredsItem.setInsuredEmail(fcOrderInsured.get("eMail"));
            insuredsItem.setNeedConfirmationFlag("是");
            insuredsItem.setAppntRelationToInsured(fcOrderInsured.get("relation"));
            // Insured->BnfInfo第一层
            List<BnfInfosItem> bnfInfos = new ArrayList<>();
            FDCodeKey fdCodeKey = new FDCodeKey();
            FDCodeKey key = fdCodeKey;
            FDCode fdCode = new FDCode();
            List<Map<String, String>> bnfInfoMap = fcOrderBnfMapper.selectBnfInfos(orderItemNo);
            if (bnfInfoMap != null && bnfInfoMap.size() > 0) {
                for (Map<String, String> bnf : bnfInfoMap) {
                    // Insured->BnfInfo第二层
                    BnfInfosItem bnfInfoItem = new BnfInfosItem();
                    bnfInfoItem.setBnfGrade(bnf.get("bnfOrder"));
                    bnfInfoItem.setBnfName(bnf.get("name"));
                    bnfInfoItem.setBnfSex(bnf.get("sexName"));
                    bnfInfoItem.setBnfBirthday(bnf.get("birthday"));
                    bnfInfoItem.setBnfNationality(bnf.get("nativeplaceName"));
                    bnfInfoItem.setBnfIdType(bnf.get("idTypeName"));
                    bnfInfoItem.setBnfIdNo(bnf.get("idNo"));
                    bnfInfoItem.setBnfIDExpDate(bnf.get("idTypeEndDate"));
                    bnfInfoItem.setBnfLot(StringUtil.isEmpty(bnf.get("coreBnfRatio")) ? ""
                            : (CommonUtil.mul(Double.valueOf(bnf.get("coreBnfRatio")), 100.0) + "%").replace(".0", ""));
                    key.setCodeType("Relation");
                    key.setCodeKey(bnf.get("relation"));
                    fdCode = fdCodeMapper.selectByPrimaryKey(key);
                    bnfInfoItem.setBnfRelationToInsured(fdCode.getCoreCode());
                    String bnfAddress = bnf.get("provinceName") + bnf.get("cityName") + bnf.get("countyName")
                            + bnf.get("detaileAddress");
                    bnfInfoItem.setBnfAddress(bnfAddress);
                    bnfInfoItem.setBnfPhone(bnf.get("mobilePhone"));
                    bnfInfos.add(bnfInfoItem);
                }
            } else {
                BnfInfosItem bnfInfoItem = new BnfInfosItem();
                bnfInfoItem.setBnfName("法定");
                bnfInfos.add(bnfInfoItem);
            }
            insuredsItem.setBnfInfos(bnfInfos);
            /************** 险种列表 *************************/
            List<RiskInfosItem> riskInfos = new ArrayList<>();
            RiskInfosItem riskInfosItem = new RiskInfosItem();
            riskInfosItem.setRiskCode(orderItemDetailInfo.get("productCode"));
            riskInfosItem.setRiskName(orderItemDetailInfo.get("riskName"));
            riskInfosItem.setSubRiskFlag("M");
            riskInfosItem.setInsuredObjectName(fcOrderInsured.get("name"));
            riskInfosItem.setAmnt(df.format(Double
                    .valueOf(CommonUtil.mul(Double.parseDouble(orderItemDetailInfo.get("insuredAmount")), 10000.0))));
            riskInfosItem.setInsuYear(orderItemDetailInfo.get("insurePeriod").equals("01") ? "至85周岁" : "终身");
            riskInfosItem.setPayEndYear(
                    orderItemDetailInfo.get("payPeriodName").equals("一次性交清") ? orderItemDetailInfo.get("payPeriodName")
                            : orderItemDetailInfo.get("payPeriodName").replace("交", ""));
            riskInfosItem.setPrem(df.format(Double.valueOf(payMoney)));
            List<DutyInfoListItem> dutyInfoList = new ArrayList<>();
            DutyInfoListItem dutyInfoListItem = new DutyInfoListItem();
            String dutyCode = orderItemDetailInfo.get("dutyCode");
            String planName = "";
            switch (dutyCode) {
                case "ID6380":
                    planName = "重大疾病保险金";
                    break;
                case "ID6381":
                    planName = "重大疾病保险金+身故保险金";
                    break;
                case "ID6382":
                    planName = "重大疾病保险金+中症疾病保险金+轻症疾病保险金+中症疾病或轻症疾病豁免保险费";
                    break;
                case "ID6383":
                    planName = "重大疾病保险金+身故保险金+中症疾病保险金+轻症疾病保险金+中症疾病或轻症疾病豁免保险费";
                    break;
            }
            dutyInfoListItem.setDutyName(planName);
            dutyInfoListItem.setDutyCode(dutyCode);
            dutyInfoListItem.setAmnt(df.format(Double
                    .valueOf(CommonUtil.mul(Double.parseDouble(orderItemDetailInfo.get("insuredAmount")), 10000.0))));
            dutyInfoListItem.setInsuYear(orderItemDetailInfo.get("insurePeriod").equals("01") ? "至85周岁" : "终身");
            dutyInfoListItem.setPayEndYear(
                    orderItemDetailInfo.get("payPeriodName").equals("一次性交清") ? orderItemDetailInfo.get("payPeriodName")
                            : orderItemDetailInfo.get("payPeriodName").replace("交", ""));
            dutyInfoListItem.setPrem(df.format(Double.valueOf(payMoney)));
            dutyInfoList.add(dutyInfoListItem);
            riskInfosItem.setDutyInfoList(dutyInfoList);
            riskInfos.add(riskInfosItem);
            insuredsItem.setRiskInfos(riskInfos);
            Insureds.add(insuredsItem);
            dataset.setInsureds(Insureds);
            FCOrderInsured fcOrderInsuredSign = fcOrderInsuredMapper.selectByPrimaryKey(orderItemNo);
            int currentAge = DateTimeUtil.getCurrentAge(fcOrderInsuredSign.getBirthday(),
                    DateTimeUtil.getCurrentDate());
            /******************* 健告 **********************/
            List<CustomerImpart> customerImparts = new ArrayList<>();
            // TODO 少一层for循环吧
            /**********************************************************************************************************/
            CustomerImpart customerImpart = new CustomerImpart();
            customerImpart.setInsuredObjectName(fcOrderInsured.get("name"));
            customerImpart.setInsuredObjectType("第一被保人");
            List<InsuredImpart> insuredImpartList = new ArrayList<>();
            List<AppntImpart> appntImparts = fcAppntImpartInfoPersonalMapper.selectByOrderItemNo(orderItemNo);
            for (AppntImpart appntImpart : appntImparts) {
                String impartCode = appntImpart.getImpartCode();
                String introDuctions = appntImpart.getIntroDuctions();
                String impartContent = appntImpart.getImpartContent();
                String impartReply = appntImpart.getImpartReply();
                if (impartCode.equals("10000") || impartCode.equals("10001")) {// 身高、体重、年收入
                    InsuredImpart insuredImpart = new InsuredImpart();
                    insuredImpart.setImpver(impartCode.equals("10000") ? "A001" : "A002");
                    insuredImpart.setImpartCode(impartCode.equals("10000") ? "11111" : "11200");
                    insuredImpart.setImpartContent(impartContent);
                    insuredImpart.setImpartReply(impartReply);
//                    insuredImpart.setIntroDuctions(introDuctions);
                    insuredImpartList.add(insuredImpart);
                    if (impartCode.equals("10001"))
                        insuredsItem.setInsuredAnnualIncome(StringUtil.isEmpty(impartReply.replaceAll("/", "")) ? "0.0"
                                : CommonUtil.mul(Double.parseDouble(impartReply.replaceAll("/", "")), 10000.0) + "");
                } else {
                    String sex = fcOrderInsuredSign.getSex();
                    // 女性告知
                    if (impartCode.equals("11109-A") || impartCode.equals("11109-B") || impartCode.equals("11109-C")) {
                        if (sex.equals("1") && currentAge >= 18) {
                            InsuredImpart insuredImpart = new InsuredImpart();
                            insuredImpart.setImpver(appntImpart.getImpartVer());
                            insuredImpart.setImpartCode(impartCode);
                            insuredImpart.setImpartContent(impartContent);
                            insuredImpart.setImpartReply(impartReply);
                            String[] impartReplyArr = impartReply.split("/");
                            if (!impartReplyArr[0].equals("否") && !impartCode.equals("11109-A")) {
                                insuredImpart.setIntroDuctions(introDuctions.replace("/", ""));
                            }
                            insuredImpartList.add(insuredImpart);
                        }
                    } else if (impartCode.equals("11110-A") || impartCode.equals("11110-B")
                            || impartCode.equals("11110-C")) {
                        if (currentAge <= 2) {// 少儿告知
                            InsuredImpart insuredImpart = new InsuredImpart();
                            insuredImpart.setImpver(appntImpart.getImpartVer());
                            insuredImpart.setImpartCode(impartCode);
                            insuredImpart.setImpartContent(impartContent);
                            insuredImpart.setImpartReply(impartReply);
                            String[] impartReplyArr = impartReply.split("/");
                            if (!impartReplyArr[0].equals("否") && !impartCode.equals("11110-A")) {
                                insuredImpart.setIntroDuctions(introDuctions.replace("/", ""));
                            }
                            insuredImpartList.add(insuredImpart);
                        }
                    } else {
                        String[] insureArr = {"11103-A", "11103-B", "11103-C", "11104", "11105-A", "11105-B",
                                "11105-C", "11105-D", "11105-E", "11105-F", "11105-G", "11105-H", "11105-I", "11106",
                                "11107", "11108"};
                        InsuredImpart insuredImpart = new InsuredImpart();
                        insuredImpart.setImpver(appntImpart.getImpartVer());
                        insuredImpart.setImpartCode(impartCode);
                        insuredImpart.setImpartContent(impartContent);
                        String[] impartArr = impartReply.split("/");
                        if (Arrays.asList(insureArr).contains(impartCode) && impartArr[0].equals("是")) {
                            insuredImpart.setImpartReply(impartReply);
                            insuredImpart.setIntroDuctions(introDuctions.replace("/", ""));
                        } else {
                            if ("11201-A".equals(impartCode) && impartArr[0].equals("是")) {
                                FDCodeKey codeKey = new FDCodeKey();
                                codeKey.setCodeKey(impartArr[1]);
                                codeKey.setCodeType("Drivearr");
                                FDCode codeInfo = fdCodeMapper.selectByPrimaryKey(codeKey);
                                insuredImpart.setImpartReply("是/" + codeInfo.getCodeName() + "/");
                            } else if (("11204".equals(impartCode) || "11205".equals(impartCode))
                                    && impartArr[0].equals("是")) {
                                insuredImpart.setImpartReply(impartReply);
                                insuredImpart.setIntroDuctions(introDuctions.replace("/", ""));
                            } else {
                                insuredImpart.setImpartReply(impartReply);
                            }
                        }
                        insuredImpartList.add(insuredImpart);
                    }
                }
            }
            customerImpart.setInsuredImpartList(insuredImpartList);
            customerImparts.add(customerImpart);
            /**********************************************************************************************************/
            dataset.setCustomerImparts(customerImparts);
            // 插入签名地址
            List<PicFile> picFileList = new ArrayList<PicFile>();
            params.clear();
            params.put("orderItemNo", orderItemNo);
            params.put("fileType", "0804");
            List<FCPersonImage> fcPersonImageList = fcPersonImageMapper.selectImagesSign(params);
            if (fcPersonImageList != null && fcPersonImageList.size() > 0) {
                PicFile picFileKid = new PicFile();
                if (currentAge < 16)
                    picFileKid.setPicType("0203");
                for (FCPersonImage fcPersonImage : fcPersonImageList) {
                    String signRelation = fcPersonImage.getRelation();
                    if (signRelation.equals("0")) {
                        PicFile picFile = new PicFile();
                        String imageUrl = fcPersonImage.getImageUrl();
                        File file = new File(imageUrl);
                        picFile.setFileName(file.getName());
                        picFile.setHttpUrl(myProps.getPolicySignAddress() + "filePath=" + imageUrl);
                        picFile.setPicType("0201");
                        picFileList.add(picFile);
                        if (currentAge < 16) {
                            picFileKid.setPicType("0203");
                            picFileKid.setFileName(file.getName());
                            picFileKid.setHttpUrl(myProps.getPolicySignAddress() + "filePath=" + imageUrl);
                            picFileList.add(picFileKid);
                        }
                        if (fcPersonImageList.size() == 1) {
                            PicFile picFileFamily = new PicFile();
                            String imageUrFileFamilyl = fcPersonImage.getImageUrl();
                            File fileFileFamily = new File(imageUrl);
                            picFileFamily.setFileName(fileFileFamily.getName());
                            picFileFamily.setHttpUrl(myProps.getPolicySignAddress() + "filePath=" + imageUrFileFamilyl);
                            picFileFamily.setPicType("0202");
                            picFileList.add(picFileFamily);
                        }
                    } else {
                        if (currentAge >= 16) {
                            PicFile picFile = new PicFile();
                            String imageUrl = fcPersonImage.getImageUrl();
                            File file = new File(imageUrl);
                            picFile.setPicType("0202");
                            picFile.setFileName(file.getName());
                            picFile.setHttpUrl(myProps.getPolicySignAddress() + "filePath=" + imageUrl);
                            picFileList.add(picFile);
                        }
                    }
                }
            }
            if (picFileList != null && picFileList.size() > 0)
                dataset.setPicFiles(picFileList);
            datasets.setDATASET(dataset);

            // XML请求报文生成
            xStream.alias("DATASETS", DATASETS.class);
            xStream.alias("Insured", InsuredsItem.class);
            xStream.alias("BnfInfo", BnfInfosItem.class);
            xStream.alias("RiskInfo", RiskInfosItem.class);
            xStream.alias("DutyInfo", DutyInfoListItem.class);
            xStream.alias("CustomerImpart", CustomerImpart.class);
            xStream.alias("InsuredImpart", InsuredImpart.class);
            xStream.alias("LCCustomerImpart", LCCustomerImpart.class);
            xStream.alias("LCCustomerImparts", LCCustomerImparts.class);
            xStream.alias("PicFile", PicFile.class);
            String requestBodyXml = xStream.toXML(datasets);
            String reqXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
            requestBodyXml = reqXml + requestBodyXml;
            System.out.println("XML请求报文生成：\n" + requestBodyXml);

            // 调用接口
            log.info("调用电子投保单影像件生成请求报文：\n{}", requestBodyXml);
            long startTime = System.currentTimeMillis();
            String responseJson = HttpUtil.postHttpRequestXMl(myProps.getPolicyGenerateImageUrl(), requestBodyXml);
            long endTime = System.currentTimeMillis();
            log.info("电子投保单影像件生成接口用时：" + ((endTime - startTime) / 1000.00) + "秒");
            log.info("调用电子投保单影像件生成返回参数:{}", responseJson);
            if (StringUtils.isNotBlank(responseJson)) {
                GenerateImageResult generateImageResult = JSONObject.parseObject(responseJson,
                        GenerateImageResult.class);
                GenerateImageResultRowResp result = generateImageResult.getResult();
                List<GenerateImageResultRowItemResp> row = result.getRow();
                if (row != null && row.size() > 0) {
                    GenerateImageResultRowItemResp generateImageResultRowItemResp = row.get(0);
                    String rescode = generateImageResultRowItemResp.getRescode();
                    if (rescode.equals("0000")) {
                        resultMap.put("innerNetUrl", generateImageResultRowItemResp.getInnerNetUrl());
                        resultMap.put("code", "200");
                    }
                }
            } else {
                log.info("调用电子投保单影像件生成返回结果为空！");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultMap;
    }
}
