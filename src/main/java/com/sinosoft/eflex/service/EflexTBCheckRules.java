package com.sinosoft.eflex.service;

import ch.qos.logback.classic.Logger;
import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.RelationEnum;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.util.DateTimeUtil;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * makedate 20190729
 */
@Service("EflexTBCheckRules")
public class EflexTBCheckRules {

    /**
     * 日志
     */
    private static Logger Log = (Logger) LoggerFactory.getLogger(EflexTBCheckRules.class);

    /******************************* 注入 ****************************************/
    @Autowired
    private Calculator calculator;
    @Autowired
    private FCPersonMapper fcPersonMapper;
    @Autowired
    private FRCheckFieldMapper frCheckFieldMapper;
    @Autowired
    private InsureService insureService;
    @Autowired
    private FDRiskInfoMapper fdRiskInfoMapper;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private FPInsureEflexPlanMapper fPInsureEflexPlanMapper;
    @Autowired
    private FCStaffFamilyRelaMapper fcStaffFamilyRelaMapper;
    /******************************** 全局变量 *********************************************/
    private String personId;
    private String perNo;
    private String ensureCode;
    private String oprator;
    private List<String> riskCodeList;
    private String errorMsg;
    private List<Map<String, Object>> ruleMessageList = new ArrayList<>();
    Map<String, Object> params = new HashMap<>();
    private FCPerson fcPerson;

    public String getErrorMsg() {
        return this.errorMsg;
    }

    public List<Map<String, Object>> getResult() {
        return this.ruleMessageList;
    }

    /**
     * 校验单个被保人的投保规则
     *
     * @param personId 被保人客户号
     * @return
     */
    public boolean eflexCheckTBRules(String personId, String ensureCode, String perNo, String oprator) {
        if (!getInputData(personId, ensureCode, perNo, oprator)) {
            return false;
        }

        if (!prepareData()) {
            return false;
        }

        if (!dealData()) {
            return false;
        }
        return true;
    }

    /**
     * 入参为必录项，少则返回false,否则返回true
     */
    private boolean getInputData(String personId, String ensureCode, String perNo, String oprator) {
        errorMsg = "";
        ruleMessageList.clear();
        if (personId == null || "".equals(personId) || ensureCode == null || "".equals(ensureCode) || perNo == null
                || "".equals(perNo)) {
            Log.info("personId、ensureCode、perNo不能为空！personId=" + personId + "&ensureCode=" + ensureCode + "&perNo="
                    + perNo);
            errorMsg = "规则引擎系统异常，请联系管理员！";
            return false;
        }
        fcPerson = fcPersonMapper.selectByPrimaryKey(personId);
        if (fcPerson == null) {
            Log.info("该用户信息不存在");
            errorMsg = "该用户信息不存在！";
            return false;
        }
        this.personId = personId;
        this.ensureCode = ensureCode;
        this.perNo = perNo;
        this.oprator = oprator;
        ruleMessageList.clear();
        return true;
    }

    /**
     * 准备计算要素
     *
     * @return
     */
    private boolean prepareData() {

        /************************ 准备数据 **********************************/
        FCEnsure fcEnsure = fcEnsureMapper.selectByPrimaryKey(ensureCode);
        // 需要考虑的问题： 不是每个责任都为必录的。所以应该从险种信息表拿责任集合，从计划责任表拿责任保额，没有的为0.
        // 险种、责任的保额词条初始化
        String riskSql = "select riskcode from fdriskinfo";
        List<String> riskList = jdbcTemplate.queryForList(riskSql, String.class);
        for (String riskCode : riskList) {
            calculator.addBasicFactor(riskCode + "Amnt", "0");
        }
        String dutySql = "select DutyCode from fdriskdutyinfo";
        List<String> dutyList = jdbcTemplate.queryForList(dutySql, String.class);
        for (String dutyCode : dutyList) {
            calculator.addBasicFactor(dutyCode + "Amnt", "0");
        }
        /***************************** 根据计划编码获取险种数据start *******************************/
        // 投保险种编码
        switch (oprator) {
            case "01":
                riskCodeList = fPInsureEflexPlanMapper.selectCheckRuleRiskCodeList(ensureCode, perNo, personId);
                break;
            case "02":
                riskCodeList = fPInsureEflexPlanMapper.selectRiskCodeList(ensureCode, perNo, personId);
                break;
        }
        for (String riskCode : riskCodeList) {
            params.clear();
            params.put("ensureCode", ensureCode);
            params.put("personId", personId);
            params.put("perNo", perNo);
            params.put("riskCode", riskCode);
            List<FCPlanRiskDuty> riskDutyInfoList = new ArrayList<>();
            if (riskCode.equals("15070")) {// 横琴综合交通团体意外伤害保险条款(只取'GD0050','GD0051','GD0052','GD0054'保额最大的责任)
                switch (oprator) {
                    case "01":
                        riskDutyInfoList = fPInsureEflexPlanMapper.selectCheckRuleDutyListBY_15070(params);
                        break;
                    case "02":
                        riskDutyInfoList = fPInsureEflexPlanMapper.selectDutyListBY_15070(params);
                        break;
                }
            } else if (riskCode.equals("17050")) {// 横琴尊享团体补充医疗保险（一般医疗保险金GD0070、恶性肿瘤医疗保险金GD0071各自一半保额，保费记在GD0070责任上）
                switch (oprator) {
                    case "01":
                        riskDutyInfoList = fPInsureEflexPlanMapper.selectCheckRuleDutyListBY_17050(params);
                        break;
                    case "02":
                        riskDutyInfoList = fPInsureEflexPlanMapper.selectDutyListBY_17050(params);
                        break;
                }
                if (riskDutyInfoList != null && riskDutyInfoList.size() > 0) {
                    FCPlanRiskDuty fcPlanRiskDuty = riskDutyInfoList.get(0);
                    FCPlanRiskDuty fcPlanRiskDuty_GD0070 = new FCPlanRiskDuty();
                    fcPlanRiskDuty_GD0070.setDutyCode("GD0070");
                    fcPlanRiskDuty_GD0070.setAmnt(fcPlanRiskDuty.getAmnt());
                    fcPlanRiskDuty_GD0070.setPrem(fcPlanRiskDuty.getPrem());
                    FCPlanRiskDuty fcPlanRiskDuty_GD0071 = new FCPlanRiskDuty();
                    fcPlanRiskDuty_GD0071.setDutyCode("GD0071");
                    fcPlanRiskDuty_GD0071.setAmnt(fcPlanRiskDuty.getAmnt());
                    fcPlanRiskDuty_GD0071.setPrem(0.0);
                    riskDutyInfoList.clear();
                    riskDutyInfoList.add(fcPlanRiskDuty_GD0070);
                    riskDutyInfoList.add(fcPlanRiskDuty_GD0071);
                }
            } else {
                switch (oprator) {
                    case "01":
                        riskDutyInfoList = fPInsureEflexPlanMapper.selectCheckRuleDutyListBY_other(params);
                        break;
                    case "02":
                        riskDutyInfoList = fPInsureEflexPlanMapper.selectDutyListBY_other(params);
                        break;
                }
            }
            Double Amnt = 0.0;
            Double Prem = 0.0;
            if (riskDutyInfoList != null && riskDutyInfoList.size() > 0) {
                for (FCPlanRiskDuty duty : riskDutyInfoList) {
                    calculator.addBasicFactor(duty.getDutyCode() + "Amnt", String.valueOf(duty.getAmnt()));
                    calculator.addBasicFactor(duty.getDutyCode() + "Prem", String.valueOf(duty.getPrem()));
                    Amnt += duty.getAmnt();
                    Prem += duty.getPrem();
                }
            }
            calculator.addBasicFactor(riskCode + "Amnt", String.valueOf(Amnt));
            calculator.addBasicFactor(riskCode + "Prem", String.valueOf(Prem));
            calculator.addBasicFactor("RiskCode", "");
            if (riskCode.matches("^1(202|503|507)0$")) {
                calculator.addBasicFactor("RiskCode", riskCode);
            }
        }
        if (calculator.getBasicFactor("RiskCode") == null) calculator.addBasicFactor("RiskCode", "");
        // 添加通用投保规则
        riskCodeList.add("000000");
        /************************************* 当前被保人的数据 *****************************************/
        // FCPerson fcPerson = fcPersonMapper.selectByPrimaryKey(personId);
        // 1、被保人的投保年龄（周岁）
        int AppAge = DateTimeUtil.getCurrentAge(fcPerson.getBirthDate(), fcEnsure.getCvaliDate());
        String AppAgeDay = "";
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Calendar cal1 = Calendar.getInstance();
            cal1.setTime(sdf.parse(fcPerson.getBirthDate()));
            Calendar cal2 = Calendar.getInstance();
            cal2.setTime(sdf.parse(fcEnsure.getCvaliDate()));
            int day1 = cal1.get(Calendar.DAY_OF_YEAR);
            int day2 = cal2.get(Calendar.DAY_OF_YEAR);
            int year1 = cal1.get(Calendar.YEAR);
            int year2 = cal2.get(Calendar.YEAR);
            if (year1 != year2) { // 不同一年
                int timeDistance = 0;
                for (int i = year1; i < year2; i++) {
                    timeDistance += 365;
                }
                AppAgeDay = String.valueOf(timeDistance + (day2 - day1));
            } else { // 同年
                AppAgeDay = String.valueOf(day2 - day1);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        String OccupationType = fcPerson.getOccupationType();
        String OccupationCode = fcPerson.getOccupationCode();
        String RiskAmnt = insureService.getRiskAmnt(personId);
        if ("".equals(RiskAmnt)) {
            Log.info("未成年人投保确认书：调用核心接口查询风险保额失败，请联系管理员！");
            errorMsg = "调用核心接口查询风险保额失败，请联系管理员！";
            return false;
        }
        // 处理累计人身险保额 （含身故责任的险种保额）。目前只有
        double tempRiskAmnt = 0.0;
        if (calculator.getBasicFactor("12020Amnt") != null) {
            tempRiskAmnt += Double.valueOf(calculator.getBasicFactor("12020Amnt"));
        }
        if (calculator.getBasicFactor("15030Amnt") != null) {
            tempRiskAmnt += Double.valueOf(calculator.getBasicFactor("15030Amnt"));
        }
        if (calculator.getBasicFactor("15070Amnt") != null) {
            tempRiskAmnt += Double.valueOf(calculator.getBasicFactor("15070Amnt"));
        }
        // 获取被保人手机号
        String mobilePhone = "";
        if (fcPerson.getMobilePhone() != null) {
            mobilePhone = fcPerson.getMobilePhone();
        }

        boolean checkNull = true;
        Log.info("prepareData fcPerson info :{}", JsonUtil.toJSON(fcPerson));
        if (null != fcPerson.getPersonID()) {
            FCStaffFamilyRela fcStaffFamilyRela = fcStaffFamilyRelaMapper.selectPersonIdInfo(fcPerson.getPersonID());
            Log.info("prepareData fcStaffFamilyRela info :{}", JsonUtil.toJSON(fcStaffFamilyRela));
            if (null != fcStaffFamilyRela.getRelation() && !RelationEnum.SELF.getCode().equals(fcStaffFamilyRela.getRelation())) {
                Log.info("fcStaffFamilyRela Family");
                checkNull = false;
            }
        }

        if (checkNull) {
            calculator.addBasicFactor("MobilePhone", mobilePhone);
        }
        calculator.addBasicFactor("RiskAmnt", RiskAmnt);
        calculator.addBasicFactor("AppAge", String.valueOf(AppAge));
        calculator.addBasicFactor("AppAgeDay", AppAgeDay);
        calculator.addBasicFactor("OccupationType", OccupationType);
        calculator.addBasicFactor("OccupationCode", OccupationCode);
        calculator.addBasicFactor("TempRiskAmnt", String.valueOf(tempRiskAmnt));
        return true;
    }

    private boolean dealData() {
        for (String riskCode : riskCodeList) {
            params.clear();
            params.put("fieldName", "TBINSERT");
            params.put("riskCode", riskCode);
            List<FRCheckField> frCheckFieldList = frCheckFieldMapper.selectEflexListByParams(params);
            if (frCheckFieldList != null && frCheckFieldList.size() > 0 && !"000000".equals(riskCode)) {
                calculator.addBasicFactor("Amnt", calculator.getBasicFactor(riskCode + "Amnt"));
                calculator.addBasicFactor("Prem", calculator.getBasicFactor(riskCode + "Prem"));
            }
            List<String> msgList = new ArrayList<>();
            for (FRCheckField frCheckField : frCheckFieldList) {
                Log.info(frCheckField.getRiskCode() + "===" + frCheckField.getSerialNo() + "==="
                        + frCheckField.getCalCode());
                calculator.setCalCode(frCheckField.getCalCode());
                // 执行投保规则
                String result = calculator.calculate();
                if (result == null || "".equals(result)) {
                    continue;
                } else if ("0".equals(result)) {
                    Log.info("规则校验系统异常，请联系管理员！");
                    errorMsg = "规则校验系统异常，请联系管理员！";
                    return false;
                } else if (!"".equals(result)) {
                    if ("Y".equals(frCheckField.getMsgFlag())) {
                        msgList.add(frCheckField.getMsg());
                    } else {
                        msgList.add(result);
                    }
                }
            }
            if (msgList.size() > 0) {
                String riskName;
                if ("000000".equals(riskCode)) {
                    riskName = "通用规则";
                } else {
                    riskName = fdRiskInfoMapper.selectRiskNameByRiskCode(riskCode);
                }
                Log.info("这个险种有问题：" + riskCode + "==" + riskName);
                Map<String, Object> riskMsg = new HashMap<String, Object>();
                riskMsg.put("riskName", riskName);
                riskMsg.put("data", msgList);
                ruleMessageList.add(riskMsg);
            }
        }
        // 投保规则校验是否通过
        if (ruleMessageList.size() > 0) {
            return false;
        } else {
            return true;
        }
    }
}
