package com.sinosoft.eflex.service;

import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.FCBatchPayBankInfoMapper;
import com.sinosoft.eflex.dao.FCEnsureMapper;
import com.sinosoft.eflex.model.FcBatchPayBankInfo;
import com.sinosoft.eflex.model.GlobalInput;
import com.sinosoft.eflex.model.sign.apply.SignApplyReq;
import com.sinosoft.eflex.model.sign.apply.SignApplyResp;
import com.sinosoft.eflex.model.sign.apply.SignApplyRespData;
import com.sinosoft.eflex.model.sign.confirm.SignConfirmReq;
import com.sinosoft.eflex.model.sign.confirm.SignConfirmResp;
import com.sinosoft.eflex.model.sign.confirm.SignConfirmRespData;
import com.sinosoft.eflex.util.CommonUtil;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.HttpUtil;
import com.sinosoft.eflex.util.rest.exception.SystemException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021/3/15 签约申请相关处理类
 */
@Service
public class BankSignServiceOld {

    // 日志
    private static Logger Log = LoggerFactory.getLogger(BankSignServiceOld.class);

    @Autowired
    private MyProps myProps;

    @Autowired
    private UserService userService;
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private InsureService insureService;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private FCBatchPayBankInfoMapper fcBatchPayBankInfoMapper;

    /**
     * 签约申请
     *
     * @param token
     * @param signApplyReq
     * @return
     */
    @Transactional
    public Map signApply(String token, SignApplyReq signApplyReq) {
        // 定义返回结果
        Map<String, Object> resultMap = new HashMap<>();
        // 获取token信息
        GlobalInput globalInput = userService.getSession(token);
        /**
         * 判断签约申请请求参数
         */
        //增加订单号
        String BusinessNo = maxNoService.createMaxNo("OrderNo", DateTimeUtil.getTimeString().substring(0, 4), 8);
        signApplyReq.setBusinessNo(BusinessNo);
        checkSignApplyReq(signApplyReq);
        /**
         * 封装请求数据
         */
        // 交易流水号，唯一，如：1000000001609990196484
        String signSN = "SN" + maxNoService.createMaxNo("BankSign", "", 30);
        signApplyReq.setTransNo(signSN);
        // 交易时间
        signApplyReq.setTransTime(DateTimeUtil.getCurrentDateTime());
        // 接口请求序列号 32位唯一字符串，每次请求唯一
        signApplyReq.setRequestSeq(signSN);
        // 接入类型
        signApplyReq.setPayKind("PORT");
        // 渠道编码
        signApplyReq.setChannelCode(11);
        // 业务类型
        signApplyReq.setBusinessType(1);
        // 付款金额
        signApplyReq.setAmount(1);
        // 银行账号
        signApplyReq.setBankAccNo(signApplyReq.getBankAccNo().replaceAll("\\s", ""));
        /**
         * 请求接口
         */
        Log.info("调用签约申请接口请求报文: {}", JSONObject.toJSONString(signApplyReq));
        String applyResponseData = HttpUtil.postHttpRequestJson(myProps.getSignApplyUrl(),
                JSONObject.toJSONString(signApplyReq), myProps.getAppId(), myProps.getAppSecret());
        Log.info("调用签约申请接口返回报文: {}", applyResponseData);
        if (!applyResponseData.equals("")) {
            SignApplyResp signApplyResp = JSONObject.parseObject(applyResponseData, SignApplyResp.class);
            if (signApplyResp.getCode().equals("200") && signApplyResp.getSuccess()) {
                // 处理返回数据
                SignApplyRespData signApplyRespData = signApplyResp.getData();
                String signState = signApplyRespData.getSignState();
                if (signState == null || signState.equals("")) {
                    throw new SystemException("调用中台签约申请接口返回签约状态为空!");
                } else {
                    if ("1".equals(signState)) {
                        // 调用签约申请接口成功，已发送验证码，保存信息
                        FcBatchPayBankInfo fcBatchPayBankInfo = fcBatchPayBankInfoMapper.selectFcBatchPayBankInfoByOrderNo(signApplyReq.getBusinessNo());
                        if (!ObjectUtils.isEmpty(fcBatchPayBankInfo)) {
                            fcBatchPayBankInfoMapper.deleteFcBatchPayBankInfoByOrderNo(fcBatchPayBankInfo.getOrderNo());
                        }
                        // 存储支付信息
                        saveFcBatchPayBankInfo(globalInput, signApplyReq, signApplyRespData);
                        resultMap.put("globalInput", globalInput);
                        resultMap.put("orderNo", BusinessNo);
                        resultMap.put("message", "验证码发送成功！");
                    } else {
                        Log.info("调用中台签约申请接口失败，" + signApplyRespData.getSignMsg() + "！");
                        throw new SystemException(signApplyRespData.getSignMsg());
                    }
                }
            } else {
                throw new SystemException(signApplyResp.getMessage());
            }
        } else {
            throw new SystemException("签约申请接口请求验证码发送失败！");
        }
        return resultMap;
    }

    /**
     * 判断签约申请请求参数
     *
     * @return
     */
    private void checkSignApplyReq(SignApplyReq signApplyReq) {
        if (StringUtils.isEmpty(signApplyReq.getBusinessNo())) {
            throw new SystemException("订单号不能为空！");
        }
        if (StringUtils.isEmpty(signApplyReq.getCustomName())) {
            throw new SystemException("客户姓名不能为空！");
        }
        if (StringUtils.isEmpty(signApplyReq.getIdCardType())) {
            throw new SystemException("客户证件类型不能为空！");
        }
        if (StringUtils.isEmpty(signApplyReq.getIdCardNo())) {
            throw new SystemException("客户证件号不能为空！");
        }
        if (StringUtils.isEmpty(signApplyReq.getBankCode())) {
            throw new SystemException("银行编码不能为空！");
        }
        if (StringUtils.isEmpty(signApplyReq.getBankAccNo())) {
            throw new SystemException("银行账号不能为空！");
        }
    }


    /**
     * 存储申请申请成功信息
     */
    public void saveFcBatchPayBankInfo(GlobalInput globalInput, SignApplyReq signApply,
                                       SignApplyRespData signApplyRespData) {
        FcBatchPayBankInfo fcBatchPayBankInfo = new FcBatchPayBankInfo();
        fcBatchPayBankInfo.setEnsureCode(globalInput.getEnsureCode());
        fcBatchPayBankInfo.setPerNo(globalInput.getCustomNo());
        fcBatchPayBankInfo.setSignSN(signApplyRespData.getRdSeq());
        fcBatchPayBankInfo.setBankAccCode(signApplyRespData.getBankAccCode());
        fcBatchPayBankInfo.setTransId(signApplyRespData.getTransId());
        fcBatchPayBankInfo.setOrderNo(signApply.getBusinessNo());
        fcBatchPayBankInfo.setName(signApply.getCustomName());
        fcBatchPayBankInfo.setIdType(signApply.getIdCardType());
        fcBatchPayBankInfo.setIdNo(signApply.getIdCardNo());
        fcBatchPayBankInfo.setPayBankCode(signApply.getBankCode());
        fcBatchPayBankInfo.setReservePhone(signApply.getMobile());
        fcBatchPayBankInfo.setIsSidned("N");
        fcBatchPayBankInfo.setOperator(globalInput.getUserNo());
        fcBatchPayBankInfo = CommonUtil.initObject(fcBatchPayBankInfo, "INSERT");
        fcEnsureMapper.saveFcBatchPayBankInfo(fcBatchPayBankInfo);
    }

    /**
     * 银行签约申请确认
     *
     * @param perNo
     * @param ensureCode
     * @param orderNo
     */
    @Transactional(rollbackFor = Exception.class)
    public void signConfirm(String perNo, String ensureCode, String orderNo, String verifyCode) {
        Map<String, Object> fpMap = new HashMap<>();
        fpMap.put("ensureCode", ensureCode);
        fpMap.put("perNo", perNo);
        fpMap.put("orderNo", orderNo);
        List<FcBatchPayBankInfo> selectFcBatchPayBankInfo = fcEnsureMapper.selectFcBatchPayBankInfo(fpMap);
        if (selectFcBatchPayBankInfo == null || selectFcBatchPayBankInfo.size() == 0) {
            throw new SystemException("请先发送短信验证码！");
        } else {
            // 获取签约申请记录信息
            FcBatchPayBankInfo fcBatchPayBankInfo = selectFcBatchPayBankInfo.get(0);
            // 封装签约申请确认请求数据
            SignConfirmReq signConfirmReq = new SignConfirmReq();
            String signSNC = "SNC" + maxNoService.createMaxNo("BankSign", "", 30);
            signConfirmReq.setTransNo(signSNC);
            signConfirmReq.setSignSmsCode(verifyCode);
            signConfirmReq.setTransTime(DateTimeUtil.getCurrentDateTime());
            signConfirmReq.setBankAccCode(fcBatchPayBankInfo.getBankAccCode());
            signConfirmReq.setTransId(fcBatchPayBankInfo.getTransId());
            Log.info("调用中台签约申请确认接口请求报文: {}", JSONObject.toJSONString(signConfirmReq));
            String confirmResponseData = HttpUtil.postHttpRequestJson(myProps.getSignConfirmUrl(),
                    JSONObject.toJSONString(signConfirmReq), myProps.getAppId(), myProps.getAppSecret());
            Log.info("调用中台签约申请确认接口返回报文: {}", confirmResponseData);
            if (!confirmResponseData.equals("")) {
                SignConfirmResp signConfirmResp = JSONObject.parseObject(confirmResponseData, SignConfirmResp.class);
                if (signConfirmResp.getCode().equals("200") && signConfirmResp.getSuccess()) {
                    SignConfirmRespData signConfirmRespData = signConfirmResp.getData();
                    if (signConfirmRespData == null) {
                        throw new SystemException("调用中台签约确认接口返回结果数据为空!");
                    } else {
                        String signState = signConfirmRespData.getSignState();
                        if (signState == null || signState.equals("")) {
                            throw new SystemException("调用中台签约确认接口返回签约状态为空!");
                        } else {
                            if (signState.equals("1")) {
                                // 调用签约确认成功，保存信息
                                insureService.updateFcBatchPayBankInfo(fpMap, "Y");
                                insureService.updateOrderNoFcBatchPayBankInfo(signConfirmRespData.getBankAccNo(), fpMap, orderNo);
                                insureService.updateFcorder("08", orderNo);// 更新订单状态为待生效（此批扣方式为核心批扣）
                                Log.info("签约成功 ！！");
                            } else {
                                Log.info("调用中台签约确认接口失败，" + signConfirmRespData.getSignMsg() + "！");
                                throw new SystemException(signConfirmRespData.getSignMsg());
                            }
                        }
                    }
                } else {
                    throw new SystemException("调用中台签约确认接口失败，" + signConfirmResp.getMessage() + "！");
                }
            } else {
                throw new SystemException("调用中台签约确认接口返回结果为空!");
            }
        }
    }

}
