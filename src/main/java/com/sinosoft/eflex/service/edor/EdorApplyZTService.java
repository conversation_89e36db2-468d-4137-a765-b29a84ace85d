package com.sinosoft.eflex.service.edor;

import com.sinosoft.eflex.enums.EdorTypeEnum;
import com.sinosoft.eflex.framework.aop.EdorTypeHandler;
import com.sinosoft.eflex.model.edor.EdorApplyReq;
import com.sinosoft.eflex.model.edor.EdorApplyResp;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2021/9/14
 * @desc 保全减人申请
 */
@Service
@EdorTypeHandler(value = EdorTypeEnum.EDORZT)
public class EdorApplyZTService implements EdorApplyService {

    @Override
    public EdorApplyResp dealEdorApply(EdorApplyReq edorApplyReq) {
        System.out.println("进入ZT的处理类！");
        return null;
    }

}
