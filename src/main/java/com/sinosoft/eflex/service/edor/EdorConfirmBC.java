package com.sinosoft.eflex.service.edor;/**
 * @DESCRIPTION
 * @create 2019-03-18 10:14:52
 **/

import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.model.Body;
import com.sinosoft.eflex.model.FCEdoruploadfile;
import com.sinosoft.eflex.model.edor.BnfChgTrialIO;
import com.sinosoft.eflex.model.edor.BnfInsured;
import com.sinosoft.eflex.model.edor.Insureds;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.RemoteDelegate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @ClassName: EdorConfirmBC
 * @Auther: hhw
 * @Date: 2019/3/18 10:14:52
 * @Description: TODO
 * @Version: 1.0
 */
@Service
public class EdorConfirmBC {
    private static final Logger log = LoggerFactory.getLogger(EdorService.class);

    private static final String RESULTFLAG = "resultFlag";

    private static final String ERRMSG = "errMsg";

    private static final String EDORAPPNO = "edorAppNo";

    @Autowired
    private MyProps myProps;

    //受益人资料变更保全申请接口
    public Map<String, Object> edorApply(BnfChgTrialIO insuredInfoBNFTrialIO, String grpContNo, List<FCEdoruploadfile> uploadfilelist){
        Map<String, Object> resultMap = new HashMap<String, Object>();
        boolean resultFlag = false;
        List<String> errMsg = new ArrayList<String>();
        try {
            RemoteDelegate rd = RemoteDelegate.getInstance();
            log.info("受益人资料变更保全申请接口调用时间：" + DateTimeUtil.getCurrentDateTime());
            String insureStr = "";
            String esViewStr = "";
            String pageInsureStr = "";
            List<Insureds> insuredList= insuredInfoBNFTrialIO.getInsuredList();
            if (insuredList.size()>0){
                for (Insureds insured:insuredList) {
                    String BNFStr="";
                    for (BnfInsured beneficiary:insured.getBnfList()){
                        BNFStr +="\t\t\t\t\t<Bnf>\n"+
                                "\t\t\t\t\t\t<BnfType>1</BnfType>\n"+
                                "\t\t\t\t\t\t<BnfName>"+beneficiary.getBnfName()+"</BnfName>\n"+
                                "\t\t\t\t\t\t<BnfIdType>"+beneficiary.getBnfIdType()+"</BnfIdType>\n"+
                                "\t\t\t\t\t\t<BnfIdNo>"+beneficiary.getBnfIdNo()+"</BnfIdNo>\n"+
                                "\t\t\t\t\t\t<BnfNativePlace>"+beneficiary.getBnfNativePlace()+"</BnfNativePlace>\n"+
                                "\t\t\t\t\t\t<RelationToInsured>"+beneficiary.getRelationToInsured()+"</RelationToInsured>\n"+
                                "\t\t\t\t\t\t<BnfGrade>"+beneficiary.getBnfGrade()+"</BnfGrade>\n"+
                                "\t\t\t\t\t\t<BnfLot>"+beneficiary.getBnfLot()+"</BnfLot>\n"+
                                "\t\t\t\t\t\t<BnfSex>"+beneficiary.getBnfSex()+"</BnfSex>\n"+
                                "\t\t\t\t\t\t<BnfBirthday>"+beneficiary.getBirthDay()+"</BnfBirthday>\n"+
                                "\t\t\t\t\t</Bnf>\n";
                    }
                    insureStr = insureStr + "\t\t\t<Insured>\n"+
                            "\t\t\t\t<Name>"+insured.getName()+"</Name>\n"+
                            "\t\t\t\t<IDType>"+insured.getiDType()+"</IDType>\n"+
                            "\t\t\t\t<IDNo>"+insured.getIdNo()+"</IDNo>\n"+
                            "\t\t\t\t<BnfList>\n"+
                            BNFStr+
                            "\t\t\t\t</BnfList>\n"+
                            "\t\t\t</Insured>\n";
                }
            }else{
                insureStr = "\t\t\t<Insured>\n"+
                        "\t\t\t\t<Name></Name>\n"+
                        "\t\t\t\t<IDType></IDType>\n"+
                        "\t\t\t\t<IDNo></IDNo>\n"+
                        "\t\t\t\t<BnfList>\n"+
                        "\t\t\t\t\t<Bnf>\n"+
                        "\t\t\t\t\t\t<BnfType></BnfType>\n"+
                        "\t\t\t\t\t\t<BnfName></BnfName>\n"+
                        "\t\t\t\t\t\t<BnfIdType></BnfIdType>\n"+
                        "\t\t\t\t\t\t<BnfIdNo></BnfIdNo>\n"+
                        "\t\t\t\t\t\t<RelationToInsured></RelationToInsured>\n"+
                        "\t\t\t\t\t\t<BnfGrade></BnfGrade>\n"+
                        "\t\t\t\t\t\t<BnfLot></BnfLot>\n"+
                        "\t\t\t\t\t\t<BnfSex></BnfSex>\n"+
                        "\t\t\t\t\t\t<BnfBirthday></BnfBirthday>\n"+
                        "\t\t\t\t\t\t<BnfNativePlace></BnfNativePlace>\n"+
                        "\t\t\t\t\t</Bnf>\n"+
                        "\t\t\t\t</BnfList>\n"+
                        "\t\t\t</Insured>\n";
            }
         /*
			List<ESViewInsure> esViewInsureList = insuredInfoBNFTrialIO.getEsViewInsureList();
			if(esViewInsureList.size()>0){
				for (ESViewInsure esViewInsure:esViewInsureList){
					List<PageInsure> pageInsureList = esViewInsure.getPage();
					for (PageInsure pageInsure:pageInsureList){
						pageInsureStr = pageInsureStr+"\t\t\t\t\t<Page>\n" +
								"\t\t\t\t\t\t<ImageUrl>"+pageInsure.getImageUrl()+"</ImageUrl>\n" +
								"\t\t\t\t\t\t<ImageName>"+pageInsure.getImageName()+"</ImageName>\n" +
								"\t\t\t\t\t\t<PageCode>"+pageInsure.getPageCode()+"</PageCode>\n" +
								"\t\t\t\t\t</Page>\n";
					}
					esViewStr = esViewStr+"\t\t\t<ESView>\n" +
							"\t\t\t\t<SubType>"+esViewInsure.getSubType()+"</SubType>\n" +
							"\t\t\t\t<PageNum>"+esViewInsure.getPageNum()+"</PageNum>\n" +
							"\t\t\t\t<PageList>\n" +
							pageInsureStr+
							"\t\t\t\t</PageList>\n"+
							"\t\t\t</ESView>\n";
				}
			}else{
				esViewStr = "\t\t\t<ESView>\n" +
						"\t\t\t\t<SubType></SubType>\n" +
						"\t\t\t\t<PageNum></PageNum>\n" +
						"\t\t\t\t<PageList>\n" +
						"\t\t\t\t\t<Page>\n" +
						"\t\t\t\t\t\t<ImageUrl></ImageUrl>\n" +
						"\t\t\t\t\t\t<ImageName></ImageName>\n" +
						"\t\t\t\t\t\t<PageCode></PageCode>\n" +
						"\t\t\t\t\t</Page>\n"+
						"\t\t\t\t</PageList>\n"+
						"\t\t\t</ESView>\n";
			}*/
            String ins="\t\t\t<Insured>\n"+
                    "\t\t\t\t<Name>an20</Name>\n"+
                    "\t\t\t\t<IDType>0</IDType>\n"+
                    "\t\t\t\t<IDNo>110101198401010095</IDNo>\n"+
                    "\t\t\t\t<BnfList>\n"+
                    "\t\t\t\t\t<Bnf>\n"+
                    "\t\t\t\t\t\t<BnfType>1</BnfType>\n"+
                    "\t\t\t\t\t\t<BnfName>卡莲01</BnfName>\n"+
                    "\t\t\t\t\t\t<BnfIdType>8</BnfIdType>\n"+
                    "\t\t\t\t\t\t<BnfIdNo>777777</BnfIdNo>\n"+
                    "\t\t\t\t\t\t<RelationToInsured>06</RelationToInsured>\n"+
                    "\t\t\t\t\t\t<BnfGrade>1</BnfGrade>\n"+
                    "\t\t\t\t\t\t<BnfLot>1</BnfLot>\n"+
                    "\t\t\t\t\t\t<BnfSex>1</BnfSex>\n"+
                    "\t\t\t\t\t\t<BnfBirthday>1957-07-23</BnfBirthday>\n"+
                    "\t\t\t\t\t\t<BnfNativePlace></BnfNativePlace>\n"+
                    "\t\t\t\t\t</Bnf>\n"+
                    "\t\t\t\t</BnfList>\n"+
                    "\t\t\t</Insured>\n";
            //受益人资料变更影像件
            String ESViewXml = "";
            String fileXml = "";
            for (int i = 0; i < uploadfilelist.size(); i++) {
                //影像文件列表
                String ftpPath = uploadfilelist.get(i).getFtpPath().substring(0,uploadfilelist.get(i).getFtpPath().lastIndexOf("/")+1);
                fileXml +=
                        "\t\t<Page>\n" +
                                "\t\t<ImageUrl>"+ftpPath+"</ImageUrl>\n" +
                                "\t\t<ImageName>"+ uploadfilelist.get(i).getFileName() +"</ImageName>\n" +
                                "\t\t<PageCode>"+ i +"</PageCode>\n" +
                                "\t\t</Page>\n";

            }
            if (uploadfilelist.size()>0){
                ESViewXml="\t\t<ESViewList>\n"+
                        //影像件循环节点
                        "\t\t<ESView>\n" +
                        "\t\t<SubType>312011</SubType>\n" +
                        "\t\t<PageNum>1</PageNum>\n" +
                        "\t\t<PageList>\n" +
                        fileXml +
                        "\t\t</PageList>\n"+
                        "\t\t</ESView>\n" +
                        "\t\t</ESViewList>\n";
            }else{
                ESViewXml="\t\t<ESViewList>\n"+
                        "\t\t</ESViewList>\n";
            }
            String reqXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                    "<RequestInfo>\n" +
                    "\t<HEAD>\n" +
                    "\t\t<TransRefGUID>"+ UUID.randomUUID().toString().replaceAll("-", "") +"</TransRefGUID>\n" +
                    "\t\t<TransType>BC0002</TransType>\n" +
                    "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
                    "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" +
                    "\t</HEAD>\n" +
                    "\t<BODY>\n" +
                    "\t\t<GrpContNo>"+grpContNo+"</GrpContNo>\n" +
                    "\t\t<EdorType>BC</EdorType>\n" +
                    "\t\t<EdorAppNo>"+insuredInfoBNFTrialIO.getEdorAppNo()+"</EdorAppNo>\n" +
                    "\t\t<EdorAppDate>"+DateTimeUtil.getCurrentDate()+"</EdorAppDate>\n" +
                    "\t\t<InsuredList>\n"+
                    insureStr+
                    "\t\t</InsuredList>\n"+
                    //影像件信息
                    ESViewXml+
                    "\t</BODY>\n"+
                    "</RequestInfo>";
            log.info("调用接口请求报文：" + reqXml);
            long startStamp = System.currentTimeMillis();
            boolean success = rd.submitData(myProps.getServiceInfo().get("serviceUrl"), myProps.getServiceInfo().get("operation"), "BenefitService", reqXml);
            long endStamp = System.currentTimeMillis();
            log.info("调用接口用时：" +  (endStamp - startStamp));
            if (success) {
                Map<String, Object> responseXml = rd.getResult();
                Body body = (Body) responseXml.get("Body");
                if ("0".equals(body.getEdorFlag())){
                    resultFlag = true;
                    resultMap.put("BCInsuredBody",body);
                }else{
                    resultFlag = false;
                    errMsg.add(body.getEdorMark());
                }
            }else {
                resultFlag = false;
                errMsg.add("受益人资料变更调用核心失败");
                log.info("受益人资料变更申请：调用核心失败");
            }
        }catch (Exception e){
            resultFlag = false;
            log.info("错误异常", e);
            errMsg.add("受益人资料变更申请失败");
        }finally {
            resultMap.put(RESULTFLAG, resultFlag);
            resultMap.put(ERRMSG, errMsg);
            return resultMap;
        }
    }
}
