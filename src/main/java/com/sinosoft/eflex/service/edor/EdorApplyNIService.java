package com.sinosoft.eflex.service.edor;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sinosoft.eflex.dao.FcAsyncInfoMapper;
import com.sinosoft.eflex.dao.FcEdorItemMapper;
import com.sinosoft.eflex.model.FcEdorItem;
import com.sinosoft.eflex.model.edor.EdorApplyReq;
import com.sinosoft.eflex.model.edor.EdorApplyResp;
import com.sinosoft.eflex.service.MaxNoService;
import com.sinosoft.eflex.framework.aop.EdorTypeHandler;
import com.sinosoft.eflex.enums.EdorStateEnum;
import com.sinosoft.eflex.enums.EdorTypeEnum;
import com.sinosoft.eflex.util.rest.exception.SystemException;

/**
 * <AUTHOR>
 * @create 2021/9/14
 * @desc 保全增人申请
 */
@Service
@EdorTypeHandler(value = EdorTypeEnum.EDORNI)
public class EdorApplyNIService implements EdorApplyService {

    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private FcEdorItemMapper fcEdorItemMapper;
    @Autowired
    private FcAsyncInfoMapper fcAsyncInfoMapper;

    @Override
    public EdorApplyResp dealEdorApply(EdorApplyReq edorApplyReq) {
        /**
         * 声明变量
         */
        String addInsuredBatch;

        /**
         * 查询申请中的批次号
         */
        String grpContNo = edorApplyReq.getGrpContNo();
        FcEdorItem fcEdorItem = new FcEdorItem();
        fcEdorItem.setGrpContNo(grpContNo);
        fcEdorItem.setEdorState(EdorStateEnum.APPLYING.getCode());
        fcEdorItem.setEdorType(edorApplyReq.getEdorType());
        List<FcEdorItem> fcEdorItems = fcEdorItemMapper.selectEdorItemInfo(fcEdorItem);
        switch (fcEdorItems.size()) {
        case 0:
            addInsuredBatch = maxNoService.createMaxNo("addInsuredBatch", null, 20);
            break;
        case 1:
            addInsuredBatch = fcEdorItems.get(0).getEdorBatch();
            break;
        default:
            throw new SystemException("保单下保全申请项数据存在问题，存在多条申请中的保全项！");
        }

        // 定义返回参数
        EdorApplyResp edorApplyResp = new EdorApplyResp(addInsuredBatch);

        return edorApplyResp;
    }
}
