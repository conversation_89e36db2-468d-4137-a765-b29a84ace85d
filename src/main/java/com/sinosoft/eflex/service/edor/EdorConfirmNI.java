package com.sinosoft.eflex.service.edor;/**
 * @DESCRIPTION
 * @create 2019-03-18 10:15:21
 **/

import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.FCEdorAddInsuredMapper;
import com.sinosoft.eflex.dao.FCEdorPlanInfoMapper;
import com.sinosoft.eflex.dao.FCEdoruploadfileMapper;
import com.sinosoft.eflex.dao.FDCodeMapper;
import com.sinosoft.eflex.model.Body;
import com.sinosoft.eflex.model.FCEdorAddInsured;
import com.sinosoft.eflex.model.FCEdoruploadfile;
import com.sinosoft.eflex.model.FDCode;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.RemoteDelegate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName: EdorConfirmNI
 * @Auther: hhw
 * @Date: 2019/3/18 10:15:21
 * @Description: TODO
 * @Version: 1.0
 */
@Service
public class EdorConfirmNI {

    private static final Logger log = LoggerFactory.getLogger(EdorService.class);

    private static final String RESULTFLAG = "resultFlag";

    private static final String ERRMSG = "errMsg";

    private static final String EDORAPPNO = "edorAppNo";



    @Autowired
    private MyProps myProps;
    @Autowired
    private FCEdorAddInsuredMapper fcEdorAddInsuredMapper;
    @Autowired
    private FCEdoruploadfileMapper fcEdoruploadfileMapper;
    @Autowired
    private FCEdorPlanInfoMapper fcEdorPlanInfoMapper;
    @Autowired
    private FDCodeMapper fdCodeMapper;

    //添加被保险人申请
    public Map<String, Object> addInsuredApply(String GrpContNo, Map<String,Object> addInsuredMap, String edorAppNo,List<FCEdoruploadfile> uploadfilelist){
        Map<String, Object> resultMap = new HashMap<String, Object>();
        boolean resultFlag = false;
        List<String> errMsg = new ArrayList<String>();
        RemoteDelegate rd = RemoteDelegate.getInstance();
        String esViewXml="";
        String insuredXml="";
        String dutyXml="";
        // 获取员福和核心对应的关系码值对应
        Map relationMap = fdCodeMapper.selectCodeAndCoreCode("relation").stream()
                .collect(Collectors.toMap(FDCode::getCodeKey, FDCode::getCoreCode));
        try{
            List<FCEdorAddInsured> fcEdorAddInsuredList = fcEdorAddInsuredMapper.getAddInsuredInfo(addInsuredMap);
            if(fcEdorAddInsuredList.size()>0){
                for (FCEdorAddInsured fcEdorAddInsured : fcEdorAddInsuredList) {
                    String riskXml="";
                    Map<String,Object> planMap=new HashMap<>();
                    planMap.put("planCode",fcEdorAddInsured.getPlanCode());
                    planMap.put("batch",addInsuredMap.get("addBatch"));
                    /*List<FCEdorPlanInfo> riskList=fcEdorPlanInfoMapper.selectRiskListByPlan(planMap);
                    for(FCEdorPlanInfo risk : riskList){
                        planMap.put("riskCode", risk.getRiskCode());
                        List<FCEdorPlanInfo> dutyList = fcEdorPlanInfoMapper.selectRiskDutyListByPlanRisk(planMap);
                        riskXml+="\t\t\t\t\t<Risk>\n"+
                                "\t\t\t\t\t\t<RiskCode>"+risk.getRiskCode()+"</RiskCode>\n"+
                                "\t\t\t\t\t\t<MainRiskCode></MainRiskCode>\n"+
                                "\t\t\t\t\t\t<Amnt>"+risk.getAmnt()+"</Amnt>\n"+
                                "\t\t\t\t\t\t<Prem>"+risk.getPrem()+"</Prem>\n"+
                                "\t\t\t\t\t\t<Mult>1</Mult>\n"+
                                "\t\t\t\t\t\t<PayIntv>0</PayIntv>\n"+
                                "\t\t\t\t\t\t<PayYears>1</PayYears>\n"+
                                "\t\t\t\t\t\t<PayEndYear>12</PayEndYear>\n"+
                                "\t\t\t\t\t\t<PayEndYearFlag>M</PayEndYearFlag>\n"+
                                "\t\t\t\t\t\t<InsuYear>12</InsuYear>\n"+
                                "\t\t\t\t\t\t<InsuYearFlag>M</InsuYearFlag>\n"+
                                "\t\t\t\t\t\t<CalRule>3</CalRule>\n"+
                                "\t\t\t\t\t\t<BonusGetMode>1</BonusGetMode>\n"+
                                "\t\t\t\t\t\t<FullBonusGetMode></FullBonusGetMode>\n"+
                                "\t\t\t\t\t\t<GetYearFlag></GetYearFlag>\n"+
                                "\t\t\t\t\t\t<GetYear></GetYear>\n"+
                                "\t\t\t\t\t\t<GetTerms></GetTerms>\n"+
                                "\t\t\t\t\t\t<GetIntv></GetIntv>\n"+
                                "\t\t\t\t\t\t<GetBankCode></GetBankCode>\n"+
                                "\t\t\t\t\t\t<GetBankAccNo></GetBankAccNo>\n"+
                                "\t\t\t\t\t\t<GetAccName></GetAccName>\n"+
                                "\t\t\t\t\t\t<CompensationRatio>"+(risk.getGetLimit()==null?"":risk.getGetLimit())+"</CompensationRatio>\n"+
                                //"\t\t\t\t\t\t<CompensationValue></CompensationValue>\n"+
                                "\t\t\t\t\t\t<StandbyFlag1></StandbyFlag1>\n"+
                                "\t\t\t\t\t\t<StandbyFlag2></StandbyFlag2>\n"+
                                "\t\t\t\t\t\t<StandbyFlag3></StandbyFlag3>\n";

                        dutyXml = "\t\t\t\t\t\t<DutyList>\n";
                        for(FCEdorPlanInfo duty : dutyList){
                            dutyXml += "\t\t\t\t\t\t\t<Duty>\n"
                                    +"\t\t\t\t\t\t\t<DutyCode>"+duty.getDutyCode()+"</DutyCode>\n"
                                    +"\t\t\t\t\t\t\t<Amnt>"+duty.getAmnt()+"</Amnt>\n"
                                    +"\t\t\t\t\t\t\t<Prem>"+duty.getPrem()+"</Prem>\n"
                                    +"\t\t\t\t\t\t\t<GetLimit>"+(duty.getGetLimit()==null?"":duty.getGetLimit())+"</GetLimit>\n"
                                    +"\t\t\t\t\t\t\t<GetLimitType>"+duty.getGetLimitType()+"</GetLimitType>\n"
                                    +"\t\t\t\t\t\t\t<GetRate>"+(duty.getGetRatio()==null?"":duty.getGetRatio())+"</GetRate>\n"
                                    +"\t\t\t\t\t\t\t<CalRule>3</CalRule>\n"
                                    +"\t\t\t\t\t\t\t</Duty>\n";

                        }
                        riskXml += dutyXml+"\t\t\t\t\t\t</DutyList>\n"
                                + "\t\t\t\t\t</Risk>\n";
                        // "\t\t\t\t\t\t<PaySource></PaySource>\n"+
                    }*/

                    //保全增人新增字段--SubsidiaryInsuredFlag字段的是否为
                    //1-非附属被保人
                    //2-附属被保人（未生成客户号，同批次添加）
                    //3-附属被保人（已经生产客户号，非同批次添加）
                    String insuredAddXml = 
                    	    "\t\t\t\t<EmployeeName></EmployeeName>\n" +
                    		"\t\t\t\t<EmployeeIdType></EmployeeIdType>\n" +
                    		"\t\t\t\t<EmpolyeeIdNo></EmpolyeeIdNo>\n" +
                    		"\t\t\t\t<EmployeeRelation></EmployeeRelation>\n"+
                            "\t\t\t\t<EmployeeNo></EmployeeNo>\n";

                    if(fcEdorAddInsured.getSubsidiaryInsuredFlag().equals("2")  || fcEdorAddInsured.getSubsidiaryInsuredFlag().equals("3")) {
                        insuredAddXml = "\t\t\t\t<EmployeeName>" + fcEdorAddInsured.getStaffName() + "</EmployeeName>\n" + "\t\t\t\t<EmployeeIdType>" + fcEdorAddInsured.getMainIdType() + "</EmployeeIdType>\n" + "\t\t\t\t<EmpolyeeIdNo>" + fcEdorAddInsured.getMainIdNo() + "</EmpolyeeIdNo>\n" + "\t\t\t\t<EmployeeRelation>" + fdCodeMapper.selectCoreCodeKey("Relation", fcEdorAddInsured.getRelation()) + "</EmployeeRelation>\n";
                        //todo 如果SubsidiaryInsuredFlag为3的话，则需要传输客户号，确认下客户号从哪里来。
                        if (fcEdorAddInsured.getSubsidiaryInsuredFlag().equals("3")) {
                            insuredAddXml += "\t\t\t\t<EmployeeNo>123</EmployeeNo>\n";
                        } else {
                            insuredAddXml += "\t\t\t\t<EmployeeNo></EmployeeNo>\n";
                        }

                    }

                    insuredXml +=
                            "\t\t\t<Insured>\n" +
                                    "\t\t\t\t<Name>"+fcEdorAddInsured.getName()+"</Name>\n" +
                                    "\t\t\t\t<Sex>"+fcEdorAddInsured.getSex()+"</Sex>\n" +
                                    "\t\t\t\t<Birthday>"+fcEdorAddInsured.getBirthday()+"</Birthday>\n" +
                                    "\t\t\t\t<IDType>"+fcEdorAddInsured.getIdType()+"</IDType>\n" +
                                    "\t\t\t\t<IDNo>"+fcEdorAddInsured.getIdNo()+"</IDNo>\n" +
                                    "\t\t\t\t<JobLeve>"+fcEdorAddInsured.getJobType()+"</JobLeve>\n" +
                                    "\t\t\t\t<JobCode>"+fcEdorAddInsured.getJobCode()+"</JobCode>\n" +
                                    "\t\t\t\t<Mobile>"+(fcEdorAddInsured.getMobile()==null?"":fcEdorAddInsured.getMobile())+"</Mobile>\n" +
                                    "\t\t\t\t<InsuredPeoples>"+1+"</InsuredPeoples>\n" +
                                    "\t\t\t\t<JoinCompanyDate></JoinCompanyDate>\n" +
                                    "\t\t\t\t<WorkNo></WorkNo>\n" + "\t\t\t\t<Email></Email>\n" + "\t\t\t\t<MedicareStatus>" + fcEdorAddInsured.getMedicareStatus() + "</MedicareStatus>\n" + "\t\t\t\t<ContPlanCode>" + fcEdorAddInsured.getPlanCode() + "</ContPlanCode>\n" + "\t\t\t\t<GrpPayMode>" + fcEdorAddInsured.getPayMethod() + "</GrpPayMode>\n" + "\t\t\t\t<GrpBankCode>" + (fcEdorAddInsured.getDebitPayBank() == null ? "" : fcEdorAddInsured.getDebitPayBank()) + "</GrpBankCode>\n" + "\t\t\t\t<GrpBankAccName>" + (fcEdorAddInsured.getDebitPayName() == null ? "" : fcEdorAddInsured.getDebitPayName()) + "</GrpBankAccName>\n" + "\t\t\t\t<GrpBankAccNo>" + (fcEdorAddInsured.getDebitPayCode() == null ? "" : fcEdorAddInsured.getDebitPayCode()) + "</GrpBankAccNo>\n" + "\t\t\t\t<SelfPayMoney>" + (fcEdorAddInsured.getPerPayment() == null ? "" : fcEdorAddInsured.getPerPayment()) + "</SelfPayMoney>\n" + "\t\t\t\t<GrpPayMoney>" + (fcEdorAddInsured.getComPayment() == null ? "" : fcEdorAddInsured.getComPayment()) + "</GrpPayMoney>\n" + "\t\t\t\t<ContCValiDate>" + (fcEdorAddInsured.getPlusEffectDate() == null ? "" : fcEdorAddInsured.getPlusEffectDate()) + "</ContCValiDate>\n" +
                                    //增加保险期间字段 add by 2022.3.11
                                    "\t\t\t\t<InsuYear>" + (fcEdorAddInsured.getInsuYear() == null ? "" : fcEdorAddInsured.getInsuYear()) + "</InsuYear>\n" + "\t\t\t\t<InsuYearFlag>" + (fcEdorAddInsured.getInsuYearFlag() == null ? "" : fcEdorAddInsured.getInsuYearFlag()) + "</InsuYearFlag>\n" + "\t\t\t\t<GrpCompanFree></GrpCompanFree>\n" + "\t\t\t\t<JoinCompanyDate></JoinCompanyDate>\n" + "\t\t\t\t<Salary></Salary>\n" + "\t\t\t\t<ExecuteCom></ExecuteCom>\n" + "\t\t\t\t<CertifyCode></CertifyCode>\n" + "\t\t\t\t<StartCode></StartCode>\n" + "\t\t\t\t<ContCValiDate></ContCValiDate>\n" + "\t\t\t\t<MainInsuredName></MainInsuredName>\n" + "\t\t\t\t<MainInsuredNo></MainInsuredNo>\n" + "\t\t\t\t<MainRelation>" + relationMap.get(fcEdorAddInsured.getRelation())
                                    + "</MainRelation>\n" +
                                    //增人试算需求中新增字段---------start--------------
                                    "\t\t\t\t<SubsidiaryInsuredFlag>"+ fcEdorAddInsured.getSubsidiaryInsuredFlag() +"</SubsidiaryInsuredFlag>\n" +
                                            insuredAddXml +
                                    //---------------------------end----------------
                                    "\t\t\t\t<HealthFlag></HealthFlag>\n" +
                                    "\t\t\t\t<RelationToAppnt>"+(fcEdorAddInsured.getRelationToAppnt()==null?"06":fcEdorAddInsured.getRelationToAppnt())+"</RelationToAppnt>\n" +
                                    "\t\t\t\t<IdExpDate></IdExpDate>\n" +
                                    //增人试算需求中字段要求必录
                                    "\t\t\t\t<Nationality>"+ (fcEdorAddInsured.getNativeplace() == null ? "" : fcEdorAddInsured.getNativeplace())  +"</Nationality>\n"+
                                    "\t\t\t\t<NativePlace>"+ (fcEdorAddInsured.getNativeplace() == null ? "" : fcEdorAddInsured.getNativeplace())  +"</NativePlace>\n"+
                                    "\t\t\t\t<RiskList>\n"+
                                            riskXml+
                                    "\t\t\t\t</RiskList>\n"+
                                    "\t\t\t\t<BnfList>\n" +
                                    "\t\t\t\t</BnfList>\n" +
                                    "\t\t\t</Insured>\n";
                }
            }else{
                insuredXml=
                        "\t\t\t<Insured>\n"+
                                "\t\t\t\t<Name></Name>\n"+
                                "\t\t\t\t<Sex></Sex>\n"+
                                "\t\t\t\t<Birthday></Birthday>\n"+
                                "\t\t\t\t<ContPlanCode></ContPlanCode>\n"+
                                "\t\t\t\t<IDType></IDType>\n"+
                                "\t\t\t\t<IDNo></IDNo>\n"+
                                "\t\t\t\t<JobCode></JobCode>\n"+
                                "\t\t\t\t<JobLeve></JobLeve>\n"+
                                "\t\t\t\t<JoinCompanyDate></JoinCompanyDate>\n"+
                                "\t\t\t\t<Mobile></Mobile>\n"+
                                "\t\t\t\t<InsuredPeoples></InsuredPeoples>\n"+
                                "\t\t\t\t<EmployeeNo></EmployeeNo>\n"+
                                "\t\t\t\t<EmployeeName></EmployeeName>\n"+
                                "\t\t\t\t<EmployeeRelation></EmployeeRelation>\n"+
                                "\t\t\t\t<WorkNo></WorkNo>\n"+
                                "\t\t\t\t<MedicareStatus></MedicareStatus>\n"+
                                "\t\t\t\t<GrpPayMode></GrpPayMode>\n"+
                                "\t\t\t\t<GrpBankCode></GrpBankCode>\n"+
                                "\t\t\t\t<GrpBankAccName></GrpBankAccName>\n"+
                                "\t\t\t\t<GrpBankAccNo></GrpBankAccNo>\n"+
                                "\t\t\t\t<SelfPayMoney></SelfPayMoney>\n"+
                                "\t\t\t\t<GrpPayMoney></GrpPayMoney>\n"+
                                "\t\t\t\t<ContCValiDate></ContCValiDate>\n" +
                                "\t\t\t\t<AccountCode></AccountCode>\n"+
                                "\t\t\t\t<JoinCompanyDate></JoinCompanyDate>\n"+
                                "\t\t\t\t<Salary></Salary>\n"+
                                "\t\t\t\t<ExecuteCom></ExecuteCom>\n"+
                                "\t\t\t\t<CertifyCode></CertifyCode>\n"+
                                "\t\t\t\t<StartCode></StartCode>\n"+
                                "\t\t\t\t<EndCode></EndCode>\n"+
                                "\t\t\t\t<ContCValiDate></ContCValiDate>\n"+
                                "\t\t\t\t<MainInsuredName></MainInsuredName>\n"+
                                "\t\t\t\t<MainInsuredNo></MainInsuredNo>\n"+
                                "\t\t\t\t<MainRelation></MainRelation>\n"+
                                "\t\t\t\t<HealthFlag></HealthFlag>\n"+
                                "\t\t\t\t<RelationToAppnt></RelationToAppnt>\n"+
                                "\t\t\t\t<IdExpDate></IdExpDate>\n"+
                                "\t\t\t\t<Nationality></Nationality>\n"+
                                "\t\t\t\t<RiskList>\n"+
                                "\t\t\t\t\t<Risk>\n"+
                                "\t\t\t\t\t\t<MainRiskCode></MainRiskCode>\n"+
                                "\t\t\t\t\t\t<RiskCode></RiskCode>\n"+
                                "\t\t\t\t\t\t<Amnt></Amnt>\n"+
                                "\t\t\t\t\t\t<Prem></Prem>\n"+
                                "\t\t\t\t\t\t<Mult></Mult>\n"+
                                "\t\t\t\t\t\t<PayIntv></PayIntv>\n"+
                                "\t\t\t\t\t\t<PayYears></PayYears>\n"+
                                "\t\t\t\t\t\t<PayEndYear></PayEndYear>\n"+
                                "\t\t\t\t\t\t<PayEndYearFlag></PayEndYearFlag>\n"+
                                "\t\t\t\t\t\t<InsuYear></InsuYear>\n"+
                                "\t\t\t\t\t\t<InsuYearFlag></InsuYearFlag>\n"+
                                "\t\t\t\t\t\t<BonusGetMode></BonusGetMode>\n"+
                                "\t\t\t\t\t\t<FullBonusGetMode></FullBonusGetMode>\n"+
                                "\t\t\t\t\t\t<GetYearFlag></GetYearFlag>\n"+
                                "\t\t\t\t\t\t<GetYear></GetYear>\n"+
                                "\t\t\t\t\t\t<GetYearFlag></GetYearFlag>\n"+
                                "\t\t\t\t\t\t<GetTerms></GetTerms>\n"+
                                "\t\t\t\t\t\t<GetIntv></GetIntv>\n"+
                                "\t\t\t\t\t\t<CompensationRatio></CompensationRatio>\n"+
                                "\t\t\t\t\t\t<CompensationValue></CompensationValue>\n"+
                                "\t\t\t\t\t\t<CalRule></CalRule>\n"+
                                "\t\t\t\t\t\t<StandbyFlag1></StandbyFlag1>\n"+
                                "\t\t\t\t\t\t<StandbyFlag2></StandbyFlag2>\n"+
                                "\t\t\t\t\t\t<StandbyFlag3></StandbyFlag3>\n"+
                                "\t\t\t\t\t\t<PaySource></PaySource>\n"+
                                "\t\t\t\t\t\t<DutyList>\n"+
                                "\t\t\t\t\t\t\t<Duty>\n"+
                                "\t\t\t\t\t\t\t\t<DutyCode></DutyCode>\n"+
                                "\t\t\t\t\t\t\t\t<Amnt></Amnt>\n"+
                                "\t\t\t\t\t\t\t\t<Prem></Prem>\n"+
                                "\t\t\t\t\t\t\t\t<GetLimit></GetLimit>\n"+
                                "\t\t\t\t\t\t\t\t<GetRate></GetRate>\n"+
                                "\t\t\t\t\t\t\t\t<CalRule></CalRule>\n"+
                                "\t\t\t\t\t\t\t</Duty>\n"+
                                "\t\t\t\t\t\t</DutyList>\n"+
                                "\t\t\t\t\t</Risk>\n"+
                                "\t\t\t\t</RiskList>\n"+
                                "\t\t\t\t<BnfList></BnfList>\n"+
                                "\t\t\t</Insured>\n";
            }
            //获取保全增人影像件 todo 最好的支持的更加强大一点（任意获取文件里面的内容）
            String fileXml = "";
            for (int i = 0; i < uploadfilelist.size(); i++) {
            	//影像文件列表
                String ftpPath = uploadfilelist.get(i).getFtpPath().substring(0,uploadfilelist.get(i).getFtpPath().lastIndexOf("/")+1);
            	fileXml +=
                    "\t\t<Page>\n" +
                    "\t\t<ImageUrl>"+ftpPath+"</ImageUrl>\n" +
                    "\t\t<ImageName>"+ uploadfilelist.get(i).getFileName() +"</ImageName>\n" +
                    "\t\t<PageCode>"+ i +"</PageCode>\n" +
                    "\t\t</Page>\n";
                
			}
            if (uploadfilelist.size()>0){
                esViewXml="\t\t<ESViewList>\n"+
                        //影像件循环节点
                        "\t\t<ESView>\n" +
                        "\t\t<SubType>312011</SubType>\n" +
                        "\t\t<PageNum>1</PageNum>\n" +
                        "\t\t<PageList>\n" +
                        fileXml +
                        "\t\t</PageList>\n"+
                        "\t\t</ESView>\n" +
                        "\t\t</ESViewList>\n";
            }else{
                esViewXml="\t\t<ESViewList>\n"+
                        "\t\t</ESViewList>\n";
            }


            String requestXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                    "<RequestInfo>\n" +
                    "\t<HEAD>\n" +
                    "\t\t<TransRefGUID>"+ UUID.randomUUID().toString().replaceAll("-", "") +"</TransRefGUID>\n" +
                    "\t\t<TransType>YUG002</TransType>\n" +
                    "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
                    "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" +
                    "\t</HEAD>\n" +
                    "\t<BODY>\n" +
                    "\t\t<GrpContNo>"+GrpContNo+"</GrpContNo>\n"+
                    "\t\t<RnewContFlag>N</RnewContFlag>\n"+
                    "\t\t<EdorAppNo>"+edorAppNo+"</EdorAppNo>\n"+
                    "\t\t<EdorType>NI</EdorType>\n"+
                    "\t\t<EdorAppDate>"+ DateTimeUtil.getCurrentDate()+"</EdorAppDate>\n"+
                    "\t\t<InsuredList>\n"+
                        insuredXml+
                    "\t\t</InsuredList>\n"+
                    esViewXml+
                    "\t</BODY>\n"+
                    "</RequestInfo>";
            log.info("新增被保人申请接口请求报文：" + requestXml);
            long startStamp = System.currentTimeMillis();
            boolean success = rd.submitData(myProps.getServiceInfo().get("serviceUrl"), myProps.getServiceInfo().get("operation"), "BenefitService",requestXml);
            long endStamp = System.currentTimeMillis();
            log.info("调用接口用时：" +  (endStamp - startStamp));
            if (success){
            	//正常流程
                Map<String, Object> responseXml=rd.getResult();
				Body body = (Body) responseXml.get("Body");
                log.info("新增被保人申请接口返回报文：" + JSON.toJSONString(responseXml));
                if("0".equals(body.getEdorFlag())){
                    resultFlag = true;
                    resultMap.put("NIInsuredBody",body);
                }else{
                    resultFlag = false;
                    errMsg.add("增加被保人："+body.getEdorMark());
                }
            }else {
                resultFlag = false;
                errMsg.add("新增被保险人调用核心失败");
                log.info("新增被保险人申请：调用核心失败");
            }
        }catch (Exception e){
            resultFlag = false;
            log.info("错误异常", e);
            errMsg.add("增加保险人申请失败");
        }finally {
            resultMap.put(RESULTFLAG, resultFlag);
            resultMap.put(ERRMSG, errMsg);
            return resultMap;
        }
    }

}
