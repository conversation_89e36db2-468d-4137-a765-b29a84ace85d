package com.sinosoft.eflex.service.edor;/**
 * @DESCRIPTION
 * @create 2019-03-18 10:15:21
 **/

import com.alibaba.druid.support.logging.Log;
import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.FCEdorAddInsuredMapper;
import com.sinosoft.eflex.dao.FCEdorHomogeneousRiskInsuredMapper;
import com.sinosoft.eflex.dao.FCEdorPlanInfoMapper;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.RemoteDelegate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
/**
 * Yeah,is me,<PERSON>!
 * <AUTHOR>
 * @date 2019年5月30日
 */
@Service
public class EdorConfirmHNI {

    private static final Logger log = LoggerFactory.getLogger(EdorService.class);


    @Autowired
    private MyProps myProps;
    @Autowired
    private FCEdorHomogeneousRiskInsuredMapper fcEdorHomogeneousRiskInsuredMapper;

    //同质风险加减人申请
    public Map<String, Object> hrarInsuredApply(String grpContNo,String edorAppNo,FCEdorHomogeneousRiskInsured InsuredParm, List<FCEdoruploadfile> uploadfilelist){
        Map<String, Object> resultMap = new HashMap<String, Object>();
		List<String> errMsg = new ArrayList<String>();
		boolean resultFlag = false;
        RemoteDelegate rd = RemoteDelegate.getInstance();
		String esViewXml = "";
        try{
        	//同质风险加减人数据
        	List<FCEdorHomogeneousRiskInsured> fcEdorHomogeneousRiskInsuredlist = fcEdorHomogeneousRiskInsuredMapper.getHomogeneousRiskInsuredInfo(InsuredParm);
        	if(fcEdorHomogeneousRiskInsuredlist.size() < 1) {
        		resultMap.put("success", false);
				resultMap.put("code", "500");
				resultMap.put("message","同质风险加减人数据为空！");
				return resultMap;
        	}
        	String insuredXml = "";
        	for (FCEdorHomogeneousRiskInsured fcEdorHomogeneousRiskInsured : fcEdorHomogeneousRiskInsuredlist) {
				insuredXml +=
						"\t\t\t<Insured>\n" +
								"\t\t\t\t<OName>" + fcEdorHomogeneousRiskInsured.getOldName() + "</OName>\n" +
								"\t\t\t\t<OSex>" + fcEdorHomogeneousRiskInsured.getOldSex() + "</OSex>\n" +
								"\t\t\t\t<OBirthday>" + fcEdorHomogeneousRiskInsured.getOldBirthday() + "</OBirthday>\n" +
								"\t\t\t\t<OIDType>" + fcEdorHomogeneousRiskInsured.getOldIdType() + "</OIDType>\n" +
								"\t\t\t\t<OIDNo>" + fcEdorHomogeneousRiskInsured.getOldIdNo() + "</OIDNo>\n" +
								"\t\t\t\t<NewName>" + fcEdorHomogeneousRiskInsured.getNewName() + "</NewName>\n" +
								"\t\t\t\t<NewNativeplace>" + (fcEdorHomogeneousRiskInsured.getNewNativeplace() == null ? "" : fcEdorHomogeneousRiskInsured.getNewNativeplace()) + "</NewNativeplace>\n" +
								"\t\t\t\t<NewSex>" + fcEdorHomogeneousRiskInsured.getNewSex() + "</NewSex>\n" +
								"\t\t\t\t<NewBirthday>" + fcEdorHomogeneousRiskInsured.getNewBirthday() + "</NewBirthday>\n" +
								"\t\t\t\t<NewIDType>" + fcEdorHomogeneousRiskInsured.getNewIdType() + "</NewIDType>\n" +
								"\t\t\t\t<NewIDNo>" + fcEdorHomogeneousRiskInsured.getNewIdNo() + "</NewIDNo>\n" +
								"\t\t\t\t<NewIdTypeEndDate>" + fcEdorHomogeneousRiskInsured.getNewIdTypeEndDate() + "</NewIdTypeEndDate>\n" +
								"\t\t\t\t<JobCode>" + fcEdorHomogeneousRiskInsured.getNewJobCode() + "</JobCode>\n" +
								"\t\t\t\t<SocialInsuFlag>"+fcEdorHomogeneousRiskInsured.getNewJoinMedProtect()+"</SocialInsuFlag>\n"+
								"\t\t\t\t<NZValidate>"+fcEdorHomogeneousRiskInsured.getNzValidate()+"</NZValidate>\n"+
                        "\t\t\t</Insured>\n";
			}

        	//影像文件信息
        	String PageXml = "";
//        	for (int i = 0; i < array.length; i++) {
//        		PageXml += 
//        				"\t\t\t<Page>\n" +
//        						//影像件位置
//                                "\t\t\t\t<ImageUrl>"++"</ImageUrl>\n" +
//        						//文件名
//                                "\t\t\t\t<ImageName>"++"</ImageName>\n" +
//                                //页码
//                                "\t\t\t\t<PageCode>"++"</PageCode>\n" +
//                        "\t\t\t</Page>\n";
//        			
//			}
        	
        	//影像件信息
        	//String ESViewXml = "";
//        	for (int i = 0; i < array.length; i++) {
//        		ESViewXml += 
//        				"\t\t\t<ESView>\n" +
//        						//影像件类别
//                                "\t\t\t\t<SubType>"++"</SubType>\n" +
//        						//页数
//                                "\t\t\t\t<PageNum>"++"</PageNum>\n" +
//        						//影像文件列表
//        						"\t\t\t\t<PageList>"+
//        							PageXml+
//        						"\t\t\t\t</PageList>\n" +
//        						
//                        "\t\t\t</ESView>\n";
//        			
//			}

			//获取同质风险加减人影像件
			String fileXml = "";
			for (int i = 0; i < uploadfilelist.size(); i++) {
				//影像文件列表
				String ftpPath = uploadfilelist.get(i).getFtpPath().substring(0,uploadfilelist.get(i).getFtpPath().lastIndexOf("/")+1);
				fileXml +=
						"\t\t<Page>\n" +
								"\t\t<ImageUrl>"+ftpPath+"</ImageUrl>\n" +
								"\t\t<ImageName>"+ uploadfilelist.get(i).getFileName() +"</ImageName>\n" +
								"\t\t<PageCode>"+ i +"</PageCode>\n" +
								"\t\t</Page>\n";

			}
			if (uploadfilelist.size()>0){
				esViewXml ="\t\t<ESViewList>\n"+
						//影像件循环节点
						"\t\t<ESView>\n" +
						"\t\t<SubType>312011</SubType>\n" +
						"\t\t<PageNum>1</PageNum>\n" +
						"\t\t<PageList>\n" +
						fileXml +
						"\t\t</PageList>\n"+
						"\t\t</ESView>\n" +
						"\t\t</ESViewList>\n";
			}else{
				esViewXml ="\t\t<ESViewList>\n"+
						"\t\t</ESViewList>\n";
			}
        	//接口请求数据
        	String requestXml = "<?xml version=\"1.0\"?>\n" +
					"<RequestInfo>\n" +
					"\t<HEAD>\n" +
					"\t\t<TransRefGUID>" + UUID.randomUUID().toString().replaceAll("-", "") + "</TransRefGUID>\n" +
					"\t\t<TransType>YUG009</TransType>\n" +
					"\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
					"\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" +
					"\t</HEAD>\n" +
					"\t<BODY>\n" +
					//保单号
					"\t\t<GrpContNo>" + grpContNo + "</GrpContNo>\n" +
					//保全类型
					"\t\t<EdorType>NZ</EdorType>\n" +
					//保全申请日期
					"\t\t<EdorAppDate>" + DateTimeUtil.getCurrentDate() + "</EdorAppDate>\n" +
					"\t\t<EdorAppNo>"+edorAppNo+"</EdorAppNo>\n"+
					//是否扣除管理费
					"\t\t<FeeFlag>" + "2" + "</FeeFlag>\n" +
					//同质风险加减人信息
					"\t\t<InsuredList>\n"+
                    	insuredXml +
                    "\t\t</InsuredList>\n"+
                    //影像件信息
					esViewXml +
					"\t</BODY>\n" +
					"</RequestInfo>";
			log.info("调用核心同质风险加减人申请确定请求报文：" + requestXml);

			long startStamp = System.currentTimeMillis();
			boolean success = rd.submitData(myProps.getServiceInfo().get("serviceUrl"), myProps.getServiceInfo().get("operation"), "BenefitService", requestXml);
			//boolean success = true;
			long endStamp = System.currentTimeMillis();
            log.info("调用核心同质风险加减人申请确认接口用时：" +  (endStamp - startStamp));
			if (success) {
				Map<String, Object> responseXml = rd.getResult();
				Body body = (Body) responseXml.get("Body");
				if ("0".equals(body.getEdorFlag())){
					resultMap.put("data", body);
					resultMap.put("code", "200");
					resultMap.put("success", true);
					resultMap.put("message", "调用核心同质风险加减人申请确认接口成功！");
				}else{
					resultMap.put("code", "500");
					resultMap.put("success", false);
					resultMap.put("message", "同质风险加减人申请确认失败！");
					errMsg.add("同质风险加减人："+body.getEdorMark());
				}
			}else {
				resultFlag = false;
				errMsg.add("同质风险加减调用核心失败");
				log.info("新增被保险人申请：调用核心失败");
			}
        }catch (Exception e){
        	log.info("同质风险加减人申请确认失败:"+e.getMessage());
        	resultMap.put("success", false);
			resultMap.put("code", "500");
			resultMap.put("message","同质风险加减人申请确认失败！");
			return resultMap;
        }finally {
			resultMap.put("resultFlag", resultFlag);
			resultMap.put("errMsg", errMsg);
			return resultMap;
		}
    }

}
