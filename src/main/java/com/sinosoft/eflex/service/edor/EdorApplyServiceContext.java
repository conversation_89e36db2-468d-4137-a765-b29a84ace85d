package com.sinosoft.eflex.service.edor;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2021/9/14
 * @desc 保全申请映射关系
 */
@Component
public class EdorApplyServiceContext {

    private final Map<String, EdorApplyService> edorApplyMap = new HashMap<>();

    /**
     * 项目初始装配对象使用
     * 
     * @param code
     * @param edorApplyService
     */
    public void putEdorApplyService(String code, EdorApplyService edorApplyService) {
        edorApplyMap.put(code, edorApplyService);
    }

    /**
     * 获取相应的处理类
     * 
     * @param type
     * @return
     */
    public EdorApplyService getEdorApplyService(String type) {
        return edorApplyMap.get(type);
    }

}
