package com.sinosoft.eflex.service.edor;/**
 * @DESCRIPTION
 * @create 2019-03-18 10:15:06
 **/

import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.FDCodeMapper;
import com.sinosoft.eflex.model.Body;
import com.sinosoft.eflex.model.FCEdoruploadfile;
import com.sinosoft.eflex.model.FDCode;
import com.sinosoft.eflex.model.edor.InsuredChgTrialIO;
import com.sinosoft.eflex.model.edor.Insurers;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.RemoteDelegate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @ClassName: EdorConfirmIC
 * @Auther: hhw
 * @Date: 2019/3/18 10:15:06
 * @Description: TODO
 * @Version: 1.0
 */
@Service
public class EdorConfirmIC {
    private static final Logger log = LoggerFactory.getLogger(EdorService.class);

    private static final String RESULTFLAG = "resultFlag";

    private static final String ERRMSG = "errMsg";

    private static final String EDORAPPNO = "edorAppNo";

    @Autowired
    private MyProps myProps;
    @Autowired
    private FDCodeMapper fdCodeMapper;

    //被保人重要资料变更保全申请确认接口
    public Map<String,Object> InsuredInfoChgApplyIO(InsuredChgTrialIO insurerInfoChgTrialIO, String grpContNo, List<FCEdoruploadfile> uploadfilelist){
        Map<String,Object> resultMap=new HashMap<>();
        boolean resultFlag = false;
        List<String> errMsg = new ArrayList<String>();
    	/*List<ESViewInsure> esView = insurerInfoChgTrialIO.geteSView();
    	String esViewXml="";
    	if(esView.size()>0){
    		esViewXml="\t\t<ESViewList>\n";
    		for (ESViewInsure esViewc : esView) {
    			List<PageInsure> page = esViewc.getPage();
    			String pageXml="";
    			if(page.size()>0){
    				pageXml="\t\t<PageList>\n";
    				for (PageInsure pagec : page) {
    					pageXml+="\t\t<Page>\n" +
    							"\t\t<ImageUrl>"+pagec.getImageUrl()+"</ImageUrl>\n" +
    							"\t\t<ImageName>"+pagec.getImageName()+"</ImageName>\n" +
    							"\t\t<PageCode>"+pagec.getPageCode()+"</PageCode>\n" +
    							"\t\t</Page>\n";
    				}
    				pageXml+="\t\t</PageList>\n";
    			}else{
    				pageXml="\t\t<PageList>\n" +
    						"\t\t</Page>\n" +
    						"\t\t<ImageUrl></ImageUrl>\n" +
    						"\t\t<ImageName></ImageName>\n" +
    						"\t\t<PageCode></PageCode>\n" +
    						"\t\t</Page>\n" +
    						"\t\t</PageList>\n";
    			}
    			esViewXml+="\t\t<ESView>\n" +
    					"\t\t<SubType>"+esViewc.getSubType()+"</SubType>\n" +
    					"\t\t<PageNum>"+esViewc.getPageNum()+"</PageNum>\n" +
    					pageXml+
    					"\t\t</ESView>\n";
    		}
    		esViewXml+="\t\t</ESViewList>\n";
    	}else{
    		esViewXml="\t\t<ESViewList>\n"+
    				"\t\t<ESView>\n" +
    				"\t\t<SubType></SubType>\n" +
    				"\t\t<PageNum></PageNum>\n" +
    				"\t\t<PageList>\n" +
    				"\t\t</Page>\n" +
    				"\t\t<ImageUrl></ImageUrl>\n" +
    				"\t\t<ImageName></ImageName>\n" +
    				"\t\t<PageCode></PageCode>\n" +
    				"\t\t</Page>\n" +
    				"\t\t</PageList>\n" +
    				"\t\t</ESView>\n" +
    				"\t\t</ESViewList>\n";
    	}*/
        try {
            List<Insurers> insured = insurerInfoChgTrialIO.getInsurersList();
            String insuredXml="";
            if(insured.size()>0){
                insuredXml="\t\t<InsuredList>\n";
                for (Insurers insuredc : insured) {
                    FDCode fdCode = fdCodeMapper.selectByCodeKey(insuredc.getOccupationCode());
                    insuredXml+="\t\t\t<Insured>\n" +
                            "\t\t\t<Name>"+insuredc.getName()+"</Name>\n" +
                            "\t\t\t<NewName>"+insuredc.getNewName()+"</NewName>"+
                            "\t\t\t<Sex>"+insuredc.getSex()+"</Sex>\n" +
                            "\t\t\t<Birthday>"+insuredc.getBirthday()+"</Birthday>\n" +
                            "\t\t\t<IDType>"+insuredc.getiDType()+"</IDType>\n" +
                            "\t\t\t<IDNo>"+insuredc.getIdNo()+"</IDNo>\n" +
                            "\t\t\t<NewIDType>"+insuredc.getNewIdType()+"</NewIDType>\n" +
                            "\t\t\t<NewIDNo>"+insuredc.getNewIdNo()+"</NewIDNo>\n" +
                            "\t\t\t<IdExpDate>"+insuredc.getIdExpDate()+"</IdExpDate>\n" +
                            "\t\t\t<NewNativePlace>"+insuredc.getNewNativePlace()+"</NewNativePlace>\n" +
                            "\t\t\t<OccupationType>"+fdCode.getOtherSign()+"</OccupationType>\n" +
                            "\t\t\t<OccupationCode>"+insuredc.getOccupationCode()+"</OccupationCode>\n" +
                            "\t\t\t</Insured>\n" ;
                }
                insuredXml+="\t\t</InsuredList>\n";
            }else{
                insuredXml="\t\t<InsuredList>\n"+
                        "\t\t\t<Insured>\n" +
                        "\t\t\t<Name></Name>\n" +
                        "\t\t\t<Sex></Sex>\n" +
                        "\t\t\t<Birthday></Birthday>\n" +
                        "\t\t\t<IDType></IDType>\n" +
                        "\t\t\t<IDNo></IDNo>\n" +
                        "\t\t\t<NewIDType></NewIDType>\n" +
                        "\t\t\t<NewIDNo></NewIDNo>\n" +
                        "\t\t\t<IdExpDate></IdExpDate>\n" +
                        "\t\t\t<NewNativePlace></NewNativePlace>\n" +
                        "\t\t\t<OccupationType></OccupationType>\n" +
                        "\t\t\t<OccupationCode></OccupationCode>\n" +
                        "\t\t\t</Insured>\n" +
                        "\t\t</InsuredList>\n";
            }
            //被保人重要资料变更影像件
            String ESViewXml = "";
            String fileXml = "";
            for (int i = 0; i < uploadfilelist.size(); i++) {
                //影像文件列表
                String ftpPath = uploadfilelist.get(i).getFtpPath().substring(0,uploadfilelist.get(i).getFtpPath().lastIndexOf("/")+1);
                fileXml +=
                        "\t\t<Page>\n" +
                                "\t\t<ImageUrl>"+ftpPath+"</ImageUrl>\n" +
                                "\t\t<ImageName>"+ uploadfilelist.get(i).getFileName() +"</ImageName>\n" +
                                "\t\t<PageCode>"+ i +"</PageCode>\n" +
                                "\t\t</Page>\n";

            }
            if (uploadfilelist.size()>0){
                ESViewXml="\t\t<ESViewList>\n"+
                        //影像件循环节点
                        "\t\t<ESView>\n" +
                        "\t\t<SubType>312011</SubType>\n" +
                        "\t\t<PageNum>1</PageNum>\n" +
                        "\t\t<PageList>\n" +
                        fileXml +
                        "\t\t</PageList>\n"+
                        "\t\t</ESView>\n" +
                        "\t\t</ESViewList>\n";
            }else{
                ESViewXml="\t\t<ESViewList>\n"+
                        "\t\t</ESViewList>\n";
            }
            String requestXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                    "<RequestInfo>\n" +
                    "\t<HEAD>\n" +
				/* 交易流水号 */
                    "\t\t<TransRefGUID>"+ UUID.randomUUID().toString().replaceAll("-", "")+"</TransRefGUID>\n" +
				/* 接口交易类型 */
                    "\t\t<TransType>IC0002</TransType>\n" +
				/* 交易日期 */
                    "\t\t<TransExeDate>"+ DateTimeUtil.getCurrentDate()+"</TransExeDate>\n" +
				/* 交易时间 */
                    "\t\t<TransExeTime>"+DateTimeUtil.getCurrentTime()+"</TransExeTime>\n" +
                    "\t</HEAD>\n" +
                    "\t<BODY>\n" +
                    "\t\t<GrpContNo>"+grpContNo+"</GrpContNo>\n" +
                    "\t\t<EdorAppNo>"+insurerInfoChgTrialIO.getEdorAppNo()+"</EdorAppNo>\n" +
                    "\t\t<EdorType>IC</EdorType>\n" +
                    "\t\t<EdorAppDate>"+DateTimeUtil.getCurrentDate()+"</EdorAppDate>\n" +
                    insuredXml+
                    //影像件信息
                    ESViewXml+
                    "\t</BODY>\n"+
                    "</RequestInfo>";
            log.info("被保险人资料变更申请接口请求报文："+requestXml);
            long a = System.currentTimeMillis();
            // 调用核心接口
            RemoteDelegate remoteDelegate = RemoteDelegate.getInstance();
            boolean success=remoteDelegate.submitData(myProps.getServiceInfo().get("serviceUrl"),
                    myProps.getServiceInfo().get("operation"), "", requestXml);
            long b = System.currentTimeMillis();
            log.info("连接核心接口所用时间"+ ((a - b) / 1000.0)+"秒");
            if (success){
                Map<String,Object> responseXml = remoteDelegate.getResult();
                Body body= (Body) responseXml.get("Body");
                log.info("调用接口返回报文：" + JSON.toJSONString(responseXml));
                if ("0".equals(body.getEdorFlag())){
                    resultFlag = true;
                    resultMap.put("ICInsuredBody",body);
                }else{
                    resultFlag = false;
                    errMsg.add(body.getEdorMark());
                }
            }else {
                resultFlag = false;
                errMsg.add("被保险人资料变更调用核心失败");
                log.info("被保险人资料变更申请：调用核心失败");
            }
        }catch (Exception e){
            resultFlag = false;
            log.info("错误异常", e);
            errMsg.add("被保险人资料变更申请失败");
        }finally {
            resultMap.put(RESULTFLAG, resultFlag);
            resultMap.put(ERRMSG, errMsg);
            return resultMap;
        }
//    	resultMap.put("identifiers","02");//01-投保人资料变更;02-/被保人重要资料变更
    }
}
