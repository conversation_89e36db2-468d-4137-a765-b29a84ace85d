package com.sinosoft.eflex.service.edor;/**
 * @DESCRIPTION
 * @create 2019-03-13 13:52:48
 **/

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.base.Splitter;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.service.EnsureMakeService;
import com.sinosoft.eflex.service.MaxNoService;
import com.sinosoft.eflex.service.UserService;
import com.sinosoft.eflex.util.*;

/**
 * @ClassName: ImportInsuredNI
 * @Auther: hhw
 * @Date: 2019/3/13 13:52:48
 * @Description: TODO  新增被保人导入清单解析类
 * @Version: 1.0
 */
@Service
public class ImportInsuredNI {

    /**
     * 日志打印工具
     */
    private static final Logger log = LoggerFactory.getLogger(EdorService.class);

    @Autowired
    private FDEdorItemMapper fdEdorItemMapper;
    @Autowired
    private FCGrpEdorConfigMapper fcGrpEdorConfigMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private FDCodeMapper fdCodeMapper;
    @Autowired
    private FCPerInfoTempMapper fcPerInfoTempMapper;
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private FCEdorAddInsuredMapper fcEdorAddInsuredMapper;
    @Autowired
    private FCEdorReduInsuredMapper fcEdorReduInsuredMapper;
    @Autowired
    private FDRiskInfoMapper fdRiskInfoMapper;
    @Autowired
    private FDRiskDutyInfoMapper fdRiskDutyInfoMapper;
    @Autowired
    private FCEdorPlanInfoMapper fcEdorPlanInfoMapper;
    @Autowired
    private FCGrpInfoMapper fcGrpInfoMapper;
    @Autowired
    private FcEdorItemMapper fcEdorItemMapper;

    //导入计划错误信息
    private  String errorMsg="";
    //导入人员清单的信息
    List<FCEdorAddInsured> listMap = new ArrayList<>();
    //员工五要素信息集合
	Map<String,Map<String, String>> staffMap = new HashMap<>();

    // 险种保费
    private double riskPrem;
    boolean bool = true;

    //解析增加被保险人清单excel
    public Map<String,Object> dealPlusInsuredExcel(String token, Workbook wb, String batch, String grpContNo,String policyeffectDate,String policyendDate){
    	GlobalInput globalInput=userService.getSession(token);
        String grpNo=globalInput.getGrpNo();
        //返回前台结果
        Map<String,Object> resultMap=new HashMap<>();
        //被保人解析结果
        Map<String,Object> insuredMap=new HashMap<>();
        listMap.clear();
        staffMap.clear();
        try {
        	//解析之前增加一下关于该批次下的人员问题,提示会覆盖导入
        	Map<String,Object> insuerdNumMap1 =new HashMap<>();
        	insuerdNumMap1.put("grpNo",grpNo);
        	insuerdNumMap1.put("batch",batch);
        	int addInsuredNum1=fcEdorAddInsuredMapper.selectInsuredCount(insuerdNumMap1);
        	if(addInsuredNum1 != 0) {
        		fcEdorPlanInfoMapper.deleteBatch(batch);
        		fcEdorAddInsuredMapper.deleteAddinsured(batch);
            }
            //删除之前的申请信息
            fcEdorItemMapper.deleteByPrimaryKey(batch);
            int sheetNum=sheetCirculation(wb);
            errorMsg = "";
            //解析Excepl
            for(int i=0;i<sheetNum-1;i++){
                //处理计划详情信息
                if(i==0){
                    if(dealPlanInfo(token,wb,i,batch)){
                        log.info("导入计划成功");
                    }else {
                        List<String> errorMsgList = new ArrayList<>();
                        Splitter split = Splitter.on('/').trimResults().omitEmptyStrings(); // 去前后空格&&去空string
                        errorMsgList = split.splitToList(errorMsg);
                        resultMap.put("success", false);
                        resultMap.put("code", "500");
                        resultMap.put("message",errorMsgList);
                        break;
                    }
                //处理人员清单信息
                }else if(i==1||i==2) {
                    insuredMap = dealPlusInsuredInfo(token, wb, i,batch,grpContNo,policyeffectDate,policyendDate);
                    if(!insuredMap.isEmpty()){
                    	resultMap.put("success",false);
                    	resultMap.put("code","500");
                    	resultMap.put("message",insuredMap.get("message"));
                        break;
                    }
                }
            }
            //解析完成后存入保全增人表
            if(resultMap.isEmpty()) {
            	if(listMap.isEmpty()) {
            		resultMap.put("success",false);
                	resultMap.put("code","500");
                	resultMap.put("message","导入人员不能为空！");
            	}else {
                    System.out.println("listMap为："+listMap);
                    fcEdorAddInsuredMapper.insertNext(listMap);
            		Map<String,Object> insuerdNumMap=new HashMap<>();
            		insuerdNumMap.put("grpNo",grpNo);
            		insuerdNumMap.put("batch",batch);
            		int addInsuredNum=fcEdorAddInsuredMapper.selectInsuredCount(insuerdNumMap);
            		resultMap.put("InsuredNum",addInsuredNum);
            		resultMap.put("code","200");
            		resultMap.put("message","导入成功");
            	}
            }
            return resultMap;
        }catch (Exception e){
            log.info("失败信息"+e.getMessage());
            resultMap.put("code","500");
            resultMap.put("message","导入失败");
            return resultMap;
        }
    }

    /**
     * 获取sheet页数
     * @param wb
     * @return
     */
    public int sheetCirculation(Workbook wb) {
        int sheetCount = 0;
        sheetCount = wb.getNumberOfSheets();
        return sheetCount;
    }

    //处理计划详情清单
    public boolean dealPlanInfo(String token,Workbook wb,int i,String batch){
        //封装数据
        List<FCEdorPlanInfo> fcEdorPlanInfoList=new ArrayList<FCEdorPlanInfo>();
        GlobalInput globalInput=userService.getSession(token);
        String userNo=globalInput.getUserNo();
        try{
            Sheet sheet=wb.getSheetAt(i);
            int physicalNumberOfCells = sheet.getRow(sheet.getLastRowNum())
                    .getPhysicalNumberOfCells();
            if (physicalNumberOfCells != 59) {
                errorMsg += "模板有误，请您重新下载！/";
                return false;
            }
            Row row = null;
            int errorRow = 0;
            //声明存放计划名称的集合
            List<Object> PlanNamelist = new ArrayList<>();
            //声明存放计划编码的集合
            List<Object> PlanCodelist = new ArrayList<>();
            for(int j=4;j<sheet.getLastRowNum();j++){
                errorRow = j + 1;
                row = sheet.getRow(j);
                //校验输入数据是否为空
                if (ExcelUtil.isRowEmpty(row,1)) {
                    log.info("第" + (j + 1) + "行是无效数据。"
                            + ExcelUtil.getCellValue(row.getCell(0)));
                    continue;
                }
                //校验计划名称是否为空
                if (ExcelUtil.isCellEmpty(row.getCell(1))) {
                    errorMsg  +=  "第" + (j + 1) + "行数据异常,保险计划名称不能为空！/";
                }
                //校验计划编码不能为空
                if (ExcelUtil.isCellEmpty(row.getCell(2))) {
                    errorMsg  +=  "第" + (j + 1) + "行数据异常,保险计划编码不能为空！/";
                }else {
                	//英文,数字组合
                	String regex =  "^[0-9A-Za-z]{1,100}$";
                	if(!ExcelUtil.getCellValue(row.getCell(2)).matches(regex)) {
                		errorMsg  +=  "第" + (j + 1) + "行数据异常,保险计划编码长度应大于1,并且只能为数字或者字母以及数字字母的组合！/";
                	}
                }
                //校验计划不能重复
                if(PlanCodelist.contains(ExcelUtil.getCellValue(row.getCell(2)))){
                    int index = PlanCodelist.indexOf(ExcelUtil.getCellValue(row.getCell(2)));
                    errorMsg += "第" + (j + 1) + "行 ，与第"+ (index+5) +"行数据 计划编码重复！/";
                }
                //将计划名称暂存
                if (!ExcelUtil.isCellEmpty(row.getCell(2))) {
                	PlanCodelist.add(ExcelUtil.getCellValue(row.getCell(2)));
                }
                //校验计划对象不能为空
                if (ExcelUtil.isCellEmpty(row.getCell(3))) {
                    errorMsg += "第" + (j + 1) + "行数据异常,计划对象不能为空！/";
                }
                //校验计划不能重复
                if(PlanNamelist.contains(ExcelUtil.getCellValue(row.getCell(1)))){
                    int index = PlanNamelist.indexOf(ExcelUtil.getCellValue(row.getCell(1)));
                    errorMsg += "第" + (j + 1) + "行 ，与第"+ (index+5) +"行数据 计划名称重复！/";
                }
                //将计划名称暂存
                if (!ExcelUtil.isCellEmpty(row.getCell(3))) {
                    PlanNamelist.add(ExcelUtil.getCellValue(row.getCell(1)));
                }
                
                // 存放变量
                int count = 0;
                // 处理险种
                riskPrem = 0.0;
                int  stateNumber=0;
                String riskCode="";
                // 1、团体定期寿险（12020）
                stateNumber=4;
                riskCode="12020";
                if(!ExcelUtil.isCellEmpty(row.getCell(stateNumber))||
                        !ExcelUtil.isCellEmpty(row.getCell(stateNumber+1))){
                    bool=true;
                    if(ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row.getCell(stateNumber)))
                            || !EnsureMakeService.isAmnt(ExcelUtil.getCellValue(row.getCell(stateNumber)),1)){
                        bool=false;
                        errorMsg += "第" + errorRow
                                + "行，团体定期寿险保额不能为空，保额最低为1万，且保留到小数点后两位。（只能为数字）/";
                    }
                    stateNumber++;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))) {
                        bool=false;
                        errorMsg += "第" + errorRow
                                + "行，团体定期寿险保费不能为空,且必须是大于0的数字以及仅支持小数点后两位。/";
                    }
                    if(bool==true){
                        //将数据封装存储
                        dealRow(fcEdorPlanInfoList,row,ExcelUtil.getCellValue(row.getCell(2)), riskCode, "GD0017",
                                userNo, 4, "N",batch);
                    }
                }else {
                    count++;
                }
                // 2、门诊急诊团体医疗保险（17030）
                stateNumber=6;
                riskCode="17030";
                if(!ExcelUtil.isCellEmpty(row.getCell(stateNumber))||
                        !ExcelUtil.isCellEmpty(row.getCell(stateNumber+1))||
                        !ExcelUtil.isCellEmpty(row.getCell(stateNumber+2))||
                        !ExcelUtil.isCellEmpty(row.getCell(stateNumber+3))){
                    bool=true;
                    if(ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))
                            ||!EnsureMakeService.isAmnt(ExcelUtil.getCellValue(row.getCell(stateNumber)),2)){
                        bool=false;
                        errorMsg += "第" + errorRow
                                + "行，门诊急诊团体医疗保险保额不能为空，必须为0.2～3之间的数字，且保留到小数点后两位。/";
                    }
                    stateNumber++;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))) {
                        bool=false;
                        errorMsg += "第" + errorRow
                                + "行，门诊急诊团体医疗保险保费不能为空,且必须是大于0的数字以及仅支持小数点后两位。/";
                    }
                    stateNumber++;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))) {
                        bool=false;
                        errorMsg += "第" + errorRow
                                + "行，门诊急诊团体医疗保险免赔额不能为空。/";
                    }
                    stateNumber++;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !EnsureMakeService.isGetLimitType(ExcelUtil.getCellValue(row.getCell(stateNumber)), 1) ) {
                        bool=false;
                        errorMsg += "第" + errorRow
                                + "行，门诊急诊团体医疗保险免赔额属性不能为空，且只能为模板下拉框指定的值。/";
                    }else {
                    	//门诊急诊团体医疗保险（17030）险种的免赔额属性判断，免赔额的数值
						if (ExcelUtil.getCellValue(row.getCell(stateNumber)).equals("按次免赔")
								&& !ExcelUtil.isCellEmpty(row.getCell(stateNumber-1))
								&& isNumber0(ExcelUtil.getCellValue(row.getCell(stateNumber-1)))  
								&& !EnsureMakeService.isDeductibles(ExcelUtil.getCellValue(row.getCell(stateNumber-1)), 1)) {
							errorMsg += "第" + errorRow+ "行，免赔额输入错误，请重新输入。/";
						}
						if(ExcelUtil.getCellValue(row.getCell(stateNumber)).equals("按年免赔")
								&& !ExcelUtil.getCellValue(row.getCell(stateNumber-1)).matches("^\\d+$")) {
							errorMsg += "第" + errorRow+ "行，免赔额输入错误，请重新输入。/";
						}
                    }
                    stateNumber++;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !isNumber1(ExcelUtil.getCellValue(row.getCell(stateNumber)))
                            || !EnsureMakeService.isReimbursementRatio(ExcelUtil.getCellValue(row.getCell(stateNumber)), 1)) {
                        bool=false;
                        errorMsg += "第" + errorRow
                                + "行，门诊急诊团体医疗保险赔付比例不能为空,且只能为模板下拉框指定的值。/";
                    }
                    if(bool==true){
                        dealRow(fcEdorPlanInfoList,row,ExcelUtil.getCellValue(row.getCell(2)), riskCode, "GD0031",
                                userNo, 6, "Y",batch);
                    }
                } else {
                    count++;
                }
                //3.团体意外伤害险
                stateNumber=11;
                riskCode="15030";
                if(!ExcelUtil.isCellEmpty(row.getCell(stateNumber))||
                        !ExcelUtil.isCellEmpty(row.getCell(stateNumber+1))){
                    bool=true;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))
                            ||!EnsureMakeService.isAmnt(ExcelUtil.getCellValue(row.getCell(stateNumber)),3)) {
                        bool=false;
                        errorMsg += "第" + errorRow
                                + "行，团体意外伤害保险保额不能为空，最低为1万且只能以千元递增，保留到小数点后两位。（只能为数字）/";
                    }
                    stateNumber++;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))) {
                        bool=false;
                        errorMsg += "第" + errorRow
                                + "行，团体意外伤害保险保费不能为空,且必须是大于0的数字以及仅支持小数点后两位。/";
                    }
                    if(bool==true){
                        dealRow(fcEdorPlanInfoList,row, ExcelUtil.getCellValue(row.getCell(2)), riskCode, "GD0037",
                                userNo, 11, "N",batch);
                    }
                }else {
                    count++;
                }
                //4、意外伤害团体医疗保险（15040）
                stateNumber=13;
                riskCode="15040";
                if (!ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 1))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 2))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 3))) {
                    bool = true;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))
                            ||!EnsureMakeService.isAmnt(ExcelUtil.getCellValue(row.getCell(stateNumber)),4)) {
                        bool=false;
                        errorMsg += "第" + errorRow
                                + "行，意外伤害团体医疗保险保额不能为空，最低为0.5万且只能以千元递增，保留到小数点后两位。（只能为数字）/";
                    }
                    stateNumber++;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))) {
                        bool=false;
                        errorMsg += "第" + errorRow
                                + "行，意外伤害团体医疗保险保费不能为空，且必须是大于0的数字以及仅支持小数点后两位。/";
                    }
                    stateNumber++;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber)) || !isNumber0(ExcelUtil.getCellValue(row.getCell(stateNumber))) ||!EnsureMakeService.isDeductibles(ExcelUtil.getCellValue(row.getCell(stateNumber)), 2)) {
                        bool=false;
                        System.out.println("=====================================");
                        System.out.println(errorRow+"===="+stateNumber+"=="+ExcelUtil.getCellValue(row.getCell(stateNumber)));
                        System.out.println(ExcelUtil.isCellEmpty(row.getCell(stateNumber)));
                        System.out.println(isNumber0(ExcelUtil.getCellValue(row.getCell(stateNumber))));
                        System.out.println(EnsureMakeService.isDeductibles(ExcelUtil.getCellValue(row.getCell(stateNumber)), 2));
                        errorMsg += "第" + errorRow
                                + "行，意外伤害团体医疗保险免赔额不能为空,且只能为模板下拉框指定的值。/";
                    }
                    stateNumber++;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !isNumber1(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))
                            || !EnsureMakeService.isReimbursementRatio(ExcelUtil.getCellValue(row.getCell(stateNumber)),1)) {
                        bool=false;
                        errorMsg += "第" + errorRow
                                + "行，意外伤害团体医疗保险赔付比例不能为空,且只能为模板下拉框指定的值。/";
                    }
                    if(bool==true){
                        dealRow(fcEdorPlanInfoList,row, ExcelUtil.getCellValue(row.getCell(2)), riskCode, "GD0028",
                                userNo, 13, "Y",batch);
                    }
                } else {
                    count++;
                }
                // 5、 意外伤害住院津贴团体医疗保险（15060）
                stateNumber = 17;
                riskCode = "15060";
                if (!ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 1))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 2))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 3))) {
                    bool = true;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))
                            || !EnsureMakeService.isAmnt(ExcelUtil.getCellValue(row.getCell(stateNumber)),6)) {
                        bool=false;
                        errorMsg += "第"
                                + errorRow
                                + "行，意外伤害住院津贴团体医疗保险，意外伤害住院津贴保险责任为必录责任保额不能为空，最低为10且只能以10元递增，保留到小数点后两位。（只能为数字）/";
                    }
                    stateNumber++;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))) {
                        bool=false;
                        errorMsg += "第"
                                + errorRow
                                + "行，意外伤害住院津贴团体医疗保险，意外伤害住院津贴保险责任为必录责任保费不能为空，且必须是大于0的数字以及仅支持小数点后两位。/";
                    }
                    stateNumber++;
                    // 可选责任
                    if (!ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !ExcelUtil.isCellEmpty(row
                            .getCell(stateNumber + 1))) {
                        if (!"".equals(ExcelUtil.getCellValue(row.getCell(stateNumber))) && !"".equals(ExcelUtil.getCellValue(row.getCell(16)))){
                            if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                    || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                    .getCell(stateNumber)))
                                    //重症监护日额津贴保险金不得超过意外伤害住院日额津贴保险金的5倍
                                    || (Double.valueOf(ExcelUtil.getCellValue(row.getCell(stateNumber)))>Double.valueOf(ExcelUtil.getCellValue(row.getCell(16)))*5)) {
                                bool=false;
                                errorMsg += "第"
                                        + errorRow
                                        + "行，意外伤害住院津贴团体医疗保险，意外伤害重症住院津贴保险责任保额不能为空，且必须是大于0的数字且不得超过意外伤害住院日额津贴保险金的5倍以及仅支持小数点后两位。/";
                            }
                        }
                        stateNumber++;
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，意外伤害住院津贴团体医疗保险，意外伤害重症住院津贴保险责任保费不能为空，且必须是大于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    if(bool==true){
                        dealRow_15060(fcEdorPlanInfoList,row,ExcelUtil.getCellValue(row.getCell(2)), riskCode,
                                userNo, 17, batch);
                    }
                } else {
                    count++;
                }
                //6、团体重大疾病保险（16040）
                stateNumber= 21;
                riskCode = "16040";
                if (!ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 1))) {
                    bool = true;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))
                            || !EnsureMakeService.isAmnt(ExcelUtil.getCellValue(row.getCell(stateNumber)), 5)) {
                        bool=false;
                        errorMsg += "第" + errorRow
                                + "行，团体重大疾病保险保额不能为空,且必须是1~100的数字以及仅支持小数点后两位。/";
                    }
                    stateNumber++;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))) {
                        bool=false;
                        errorMsg += "第" + errorRow
                                + "行，团体重大疾病保险保费不能为空,且必须是大于0的数字以及仅支持小数点后两位。/";
                    }
                    if (bool==true){
                        dealRow(fcEdorPlanInfoList,row, ExcelUtil.getCellValue(row.getCell(2)), riskCode, "GD0060",
                                userNo, 21, "N",batch);
                    }
                } else {
                    count++;
                }
                // 7、住院津贴团体医疗保险（17020）
                stateNumber = 23;
                riskCode = "17020";
                if (!ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 1))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 2))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 3))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 4))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 5))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 6))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 7))) {
                    bool = true;
                    // 必录责任
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))
                            ||!EnsureMakeService.isAmnt(ExcelUtil.getCellValue(row.getCell(stateNumber)), 6)) {
                        bool=false;
                        errorMsg += "第"
                                + errorRow
                                + "行，住院津贴团体医疗保险，一般住院日额津贴保险责任为必录责任保额不能为空，最低为10且只能以10元递增，保留到小数点后两位。（只能是数字）/";
                    }
                    stateNumber++;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))) {
                        bool=false;
                        errorMsg += "第"
                                + errorRow
                                + "行，住院津贴团体医疗保险，一般住院日额津贴保险责任为必录责任保费不能为空且必须是大于0的数字以及仅支持小数点后两位。/";
                    }
                    // 可选责任1
                    stateNumber = 25;
                    if (!ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !ExcelUtil.isCellEmpty(row
                            .getCell(stateNumber + 1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))
                                || !EnsureMakeService.isAmnt(ExcelUtil.getCellValue(row.getCell(stateNumber)), 6)) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，住院津贴团体医疗保险，癌症住院日额津贴保险金责任保额不能为空,最低为10且只能以10元递增，保留到小数点后两位。（只能是数字）/";
                        }
                        stateNumber++;
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，住院津贴团体医疗保险，癌症住院日额津贴保险金责任保费不能为空,且必须是大于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    // 可选责任2
                    stateNumber = 27;
                    if (!ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !ExcelUtil.isCellEmpty(row
                            .getCell(stateNumber + 1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))
                                ||!EnsureMakeService.isAmnt(ExcelUtil.getCellValue(row.getCell(stateNumber)), 6)) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，住院津贴团体医疗保险，重症监护日额津贴保险金责任保额不能为空,最低为10且只能以10元递增，保留到小数点后两位。（只能是数字）/";
                        }
                        stateNumber++;
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，住院津贴团体医疗保险，重症监护日额津贴保险金责任保费不能为空,且必须是大于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    // 可选责任3
                    stateNumber = 29;
                    if (!ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !ExcelUtil.isCellEmpty(row
                            .getCell(stateNumber + 1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))
                                ||!EnsureMakeService.isAmnt(ExcelUtil.getCellValue(row.getCell(stateNumber)), 4)) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，住院津贴团体医疗保险，手术医疗津贴保险金责任保额不能为空,且必须是最低为0.5万的数字且以千元递增以及仅支持小数点后两位。/";
                        }
                        stateNumber++;
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，住院津贴团体医疗保险，手术医疗津贴保险金责任保费不能为空,且必须是大于0的数字以及仅支持小数点后两位。/";
                        }
                        stateNumber++;
                    }
                    if (bool==true){
                        dealRow_17020(fcEdorPlanInfoList,row,  ExcelUtil.getCellValue(row.getCell(2)), riskCode,
                                userNo, 23, batch);
                    }
                } else {
                    count++;
                }
                // 8、住院团体医疗保险（17010）
                stateNumber = 31;
                riskCode = "17010";
                if (!ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 1))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 2))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 3))) {
                    bool = true;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))
                            ||!EnsureMakeService.isAmnt(ExcelUtil.getCellValue(row.getCell(stateNumber)), 2)) {
                        bool=false;
                        errorMsg += "第" + errorRow
                                + "行，住院团体医疗保险保额不能为空,必须为0.2～3之间的数字，且保留到小数点后两位。/";
                    }
                    stateNumber++;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))) {
                        bool=false;
                        errorMsg += "第" + errorRow
                                + "行，住院团体医疗保险保费不能为空,且必须是大于0的数字以及仅支持小数点后两位。/";
                    }
                    stateNumber++;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !isNumber0(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))
                            || !EnsureMakeService.isDeductibles(ExcelUtil.getCellValue(row.getCell(stateNumber)), 3)) {
                        bool=false;
                        errorMsg += "第" + errorRow
                                + "行，住院团体医疗保险免赔额不能为空,且只能为模板下拉框指定的值。/";
                    }
                    stateNumber++;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !isNumber1(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))
                            || !EnsureMakeService.isReimbursementRatio(ExcelUtil.getCellValue(row.getCell(stateNumber)),1)) {
                        bool=false;
                        errorMsg += "第" + errorRow
                                + "行，住院团体医疗保险赔付比例不能为空,且只能为模板下拉框指定的值。/";
                    }
                    if(bool==true){
                        dealRow(fcEdorPlanInfoList,row,  ExcelUtil.getCellValue(row.getCell(2)), riskCode, "GD0018",
                                userNo, 31, "Y",batch);
                    }
                } else {
                    count++;
                }

                // 9、尊享团体补充医疗保险（17050）
                stateNumber = 35;
                riskCode = "17050";
                if (!ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 1))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 2))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 3))) {
                    bool = true;
                    // 一般医疗保险金GD0070

                    // 保额（元）
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))
                            || !EnsureMakeService.isAmnt(ExcelUtil.getCellValue(row.getCell(stateNumber)), 11)) {
                        bool=false;
                        errorMsg += "第"
                                + errorRow
                                + "行，尊享团体补充医疗保险，一般医疗保险金责任为必录责任保额不能为空，且必须是模板下拉框中指定的数字。/";
                    }
                    // 保费（元）
                    stateNumber++;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))) {
                        bool=false;
                        errorMsg += "第"
                                + errorRow
                                + "行，尊享团体补充医疗保险，一般医疗保险金责任为必录责任保费不能为空且必须是大于0的数字以及仅支持小数点后两位。/";
                    }
                    // 免赔额
                    stateNumber++;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !isNumber0(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))) {
                        bool=false;
                        errorMsg += "第"
                                + errorRow
                                + "行，尊享团体补充医疗保险，一般医疗保险金责任为必录责任免赔额不能为空且必须是大于等于0的数字以及仅支持小数点后两位。/";
                    }
                    // 赔付比例(%)
                    stateNumber++;
                    if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !isNumber1(ExcelUtil.getCellValue(row
                            .getCell(stateNumber)))
                            || !EnsureMakeService.isReimbursementRatio(ExcelUtil.getCellValue(row.getCell(stateNumber)),2)) {
                        bool=false;
                        errorMsg += "第"
                                + errorRow
                                + "行，尊享团体补充医疗保险，一般医疗保险金责任为必录责任赔付比例不能为空，且只能为模板下拉框指定的值。/";
                    }
                    if (bool==true){
                        dealRow_17050(fcEdorPlanInfoList,row, ExcelUtil.getCellValue(row.getCell(2)), riskCode,
                                userNo, 35, batch);
                    }
                } else {
                    count++;
                }

                // 10、综合交通团体意外伤害保险（15070）
                stateNumber = 39;///
                riskCode = "15070";
                if (!ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 1))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 2))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 3))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 4))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 5))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 6))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 7))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 8))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 9))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 10))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 11))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 12))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 13))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 14))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 15))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 16))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 17))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 18))
                        || !ExcelUtil.isCellEmpty(row
                        .getCell(stateNumber + 19))) {
                    bool = true;
                    // 1、公路公共交通工具保险金GD0050
                    if (!ExcelUtil.isCellEmpty(row.getCell(stateNumber)) || !ExcelUtil.isCellEmpty(row.getCell(stateNumber+1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))
                                || !EnsureMakeService.isAmnt(ExcelUtil.getCellValue(row.getCell(stateNumber)), 7)) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，公路公共交通工具保险金责任为必录责任保额不能为空，且必须是1万~200万的数字以及仅支持小数点后两位。/";
                        }
                        stateNumber++;
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，公路公共交通工具保险金责任为必录责任保费不能为空且必须是大于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    stateNumber = 41;
                    // 2、轨道交通工具保险金GD0051
                    if (!ExcelUtil.isCellEmpty(row.getCell(stateNumber)) || !ExcelUtil.isCellEmpty(row.getCell(stateNumber+1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))
                                ||  !EnsureMakeService.isAmnt(ExcelUtil.getCellValue(row.getCell(stateNumber)), 8)) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，轨道交通工具保险金责任为必录责任保额不能为空，且必须是1万~300万的数字以及仅支持小数点后两位。/";
                        }
                        stateNumber++;
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，轨道交通工具保险金责任为必录责任保费不能为空且必须是大于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    stateNumber = 43;
                    // 3、水路公共交通工具保险金GD0052
                    if (!ExcelUtil.isCellEmpty(row.getCell(stateNumber)) || !ExcelUtil.isCellEmpty(row.getCell(stateNumber+1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))
                                || !EnsureMakeService.isAmnt(ExcelUtil.getCellValue(row.getCell(stateNumber)), 7)) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，水路公共交通工具保险金责任为必录责任保额不能为空，且必须是1万~200万的数字以及仅支持小数点后两位。/";
                        }
                        stateNumber++;
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，水路公共交通工具保险金责任为必录责任保费不能为空且必须是大于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    stateNumber = 45;
                    // 4、民航班机保险金
                    if (!ExcelUtil.isCellEmpty(row.getCell(stateNumber)) || !ExcelUtil.isCellEmpty(row.getCell(stateNumber+1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))
                                || !EnsureMakeService.isAmnt(ExcelUtil.getCellValue(row.getCell(stateNumber)),9)) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，民航班机保险金责任为必录责任保额不能为空，且必须是1万~500万的数字以及仅支持小数点后两位。/";
                        }
                        stateNumber++;
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，民航班机保险金责任为必录责任保费不能为空且必须是大于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    stateNumber = 47;
                    // 5、私家车或公务车保险金
                    if (!ExcelUtil.isCellEmpty(row.getCell(stateNumber)) || !ExcelUtil.isCellEmpty(row.getCell(stateNumber+1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))
                                ||!EnsureMakeService.isAmnt(ExcelUtil.getCellValue(row.getCell(stateNumber)), 5)) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，私家车或公务车保险金责任为必录责任保额不能为空，且必须是1万~100万的数字以及仅支持小数点后两位。/";
                        }
                        stateNumber++;
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，私家车或公务车保险金责任为必录责任保费不能为空且必须是大于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    /************************ 可选责任 ***********************************************/
                    // 6、公路公共意外伤害医疗保险金GD0055
                    stateNumber = 49;
                    if (!ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !ExcelUtil.isCellEmpty(row.getCell(stateNumber + 1))
                            ) {
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))
                                || ExcelUtil.isCellEmpty(row.getCell(38))) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，公路公共意外伤害医疗保险金责任保额不能为空,且必须是大于0的数字以及仅支持小数点后两位,选择该责任请先录入公路公共交通工具保险金GD0050责任。/";
                        }
                        stateNumber++;
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，公路公共意外伤害医疗保险金责任保费不能为空,且必须是大于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    // 7、轨道交通意外伤害医疗保险金GD0056
                    stateNumber = 51;
                    if (!ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !ExcelUtil.isCellEmpty(row
                            .getCell(stateNumber + 1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))
                                || ExcelUtil.isCellEmpty(row.getCell(40))) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，轨道交通意外伤害医疗保险金责任保额不能为空,且必须是大于0的数字以及仅支持小数点后两位,选择该责任请先录入轨道交通工具保险金GD0051责任。/";
                        }
                        stateNumber++;
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，轨道交通意外伤害医疗保险金责任保费不能为空,且必须是大于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    // 8、水路公共交通意外伤害医疗保险金GD0057
                    stateNumber = 53;
                    if (!ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !ExcelUtil.isCellEmpty(row
                            .getCell(stateNumber + 1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))
                                || ExcelUtil.isCellEmpty(row.getCell(42))) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，水路公共交通意外伤害医疗保险金责任保额不能为空,且必须是大于0的数字以及仅支持小数点后两位,选择该责任请先录入水路公共交通工具保险金GD0052责任。/";
                        }
                        stateNumber++;
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，水路公共交通意外伤害医疗保险金责任保费不能为空,且必须是大于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    // 9、民航班机意外伤害医疗保险金GD0058
                    stateNumber = 55;
                    if (!ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !ExcelUtil.isCellEmpty(row
                            .getCell(stateNumber + 1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))
                                || ExcelUtil.isCellEmpty(row.getCell(44))) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，民航班机意外伤害医疗保险金责任保额不能为空,且必须是大于0的数字以及仅支持小数点后两位,选择该责任请先录入民航班机保险金GD0053责任。/";
                        }
                        stateNumber++;
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，民航班机意外伤害医疗保险金责任保费不能为空,且必须是大于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    // 10、私家车或公务车意外伤害医疗保险金GD0059
                    stateNumber = 57;
                    if (!ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                            || !ExcelUtil.isCellEmpty(row
                            .getCell(stateNumber + 1))) {
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))
                                || ExcelUtil.isCellEmpty(row.getCell(46))) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，私家车或公务车意外伤害医疗保险金责任保额不能为空,且必须是大于0的数字以及仅支持小数点后两位,选择该责任请先录入私家车或公务车保险金GD0054责任。/";
                        }
                        stateNumber++;
                        if (ExcelUtil.isCellEmpty(row.getCell(stateNumber))
                                || !EnsureMakeService.isNumber(ExcelUtil.getCellValue(row
                                .getCell(stateNumber)))) {
                            bool=false;
                            errorMsg += "第"
                                    + errorRow
                                    + "行，综合交通团体意外伤害保险，私家车或公务车意外伤害医疗保险金责任保费不能为空,且必须是大于0的数字以及仅支持小数点后两位。/";
                        }
                    }
                    if (bool==true){
                        dealRow_15070(fcEdorPlanInfoList,row, ExcelUtil.getCellValue(row.getCell(2)), riskCode,
                                userNo, 39, batch);
                    }
                } else {
                    count++;
                }
                // 是否录入险种
                if (count == 10) {
                    errorMsg += "第" + (j+1) + "行,请至少录入一个险种!/";
                }
            }
            try{
                if(!"".equals(errorMsg)){
                    return false;
                }else {
                    fcEdorPlanInfoMapper.deleteBatch(batch);
                    fcEdorPlanInfoMapper.insertPlan(fcEdorPlanInfoList);
                }
            }catch (Exception e){
                log.info("导入计划失败：", e);
                throw new RuntimeException();
            }
            errorMsg="计划导入成功";
            return true;
        }catch (Exception e){
            log.info("错误信息"+e.getMessage());
            errorMsg= "计划导入失败";
            return false;
        }
    }

    //处理被保险人清单
    public  Map<String,Object> dealPlusInsuredInfo(String token,Workbook wb,int i,String batch,String grpContNo,String policyeffectDate,String policyendDate) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        String grpNo = globalInput.getGrpNo();
        //获取性别Value-Key
        List<HashMap<String, Object>> sexCodeList = fdCodeMapper.CodeInfo("Sex");
        //获取证件类型和证件号码的Value-Key
        List<HashMap<String, Object>> idTypeCodeList = fdCodeMapper.CodeInfo("IDType");
        //错误信息
        String errorMsg = "";
        //记录录入行数
        int count = 0;

        String personType = "";
        if (i == 1) {
            personType = "员工";
        } else {
            personType = "家属";
        }
        try {
            List<String> idNoList = new ArrayList<>();
            List<Map<String, String>> mapList = new ArrayList<>();
            // 获取证件类型集合
            List<String> idTypeList = fcPerInfoTempMapper.getIdTypeList("01");
            // 获取职业类别集合
            List<String> occupationTypeList = fcPerInfoTempMapper.getOccupationTypeList("01");
            // 获取职业集合
            List<String> occupationCodeList = fcPerInfoTempMapper.getOccupationCodeList("01");
            // 获取职业类别与职业代码对应关系
            Map<String, List<String>> occupationTypeCodeMap = getOccupationTypeCode();
            // 获取银行代码集合
            List<String> openBankList = fcPerInfoTempMapper.getOpenBankList("01");
            Sheet sheet = wb.getSheetAt(i);
            Row row = null;
            //被保险人--员工
            if (i == 1) {
                for (int j = 3; j <= sheet.getLastRowNum(); j++) {
                    Map<String, String> hashMap = new HashMap<>();
                    Map<String, String> staffhashMap = new HashMap<>();
                    row = sheet.getRow(j);
                    if (ExcelUtil.isRowEmpty(row, 3)) {
                        log.info(personType+"清单第" + (j - 2) + "行是无效数据。");
                    } else {
                        count++;
                        //校验所录数据是否为空
                        errorMsg += checkIsEmpty(j + 1, row, token);
                        if(!errorMsg.equals("")) {
                        	break;
                        }
                        if (!checkScientifiNotation(ExcelUtil.getCellValue(row.getCell(11))) ||
                    		!CheckUtils.checkMobilePhone(ExcelUtil.getCellValue(row.getCell(11)))) {
                            errorMsg += personType+"清单第" + (j - 2) + "行,手机号填写格式有误!";
                        }
                        if (!checkScientifiNotation(ExcelUtil.getCellValue(row
                                .getCell(3)))) {
                            errorMsg += personType+"清单第" + (j - 2) + "行,被保险人证件号填写格式有误!";
                        }
                        String payMethod = fdCodeMapper.selectKeyByCodeName("PayPremType",ExcelUtil.getCellValue(row.getCell(12)));
                        if(!Arrays.asList(new String[]{"1","2","3"}).contains(payMethod)) {
                        	errorMsg += personType+"清单第" + (j - 2) + "行,缴费方式不存在！";
                        }
                        if ("3".equals(payMethod)) {
                            if (!checkScientifiNotation(ExcelUtil.getCellValue(row.getCell(16)))) {
                                errorMsg += personType+"清单第" + (j - 2) + "行,扣款缴费银行账号填写格式有误!";
                            }
                        }
                        if(payMethod.equals("1") || payMethod.equals("2")){
                            if(StringUtil.isNotEmpty(ExcelUtil.getCellValue(row.getCell(14)))){
                            	errorMsg += personType+"清单第" + (j - 2) + "行,扣款缴费银行应为空；";
                            }
                            if(StringUtil.isNotEmpty(ExcelUtil.getCellValue(row.getCell(15)))){
                                errorMsg += personType+"清单第" + (j - 2) + "行,扣款缴费账户名应为空；";
                            }
                            if(StringUtil.isNotEmpty(ExcelUtil.getCellValue(row.getCell(16)))){
                            	errorMsg += personType+"清单第" + (j - 2) + "行,扣款缴费银行账号应为空；";
                            }
                        }
                        if(!isNumber0(ExcelUtil.getCellValue(row.getCell(13)))){
                        	errorMsg += personType+"清单第" + (j - 2) + "行,公司缴费金额必须大于等于0；";
                        }
                        if(!checkCode_Vlue("Relation", null, ExcelUtil.getCellValue(row.getCell(18)))) {
                        	errorMsg += personType+"清单第" + (j - 2) + "行,受益人与被保险人关系不存在!";
                    	};
                        //获取excel表中的数据
                        hashMap.put("serialNo", ExcelUtil.getCellValue(row.getCell(0)));
                        //获取被保险人
                        String name = ExcelUtil.getCellValue(row.getCell(1));
                        hashMap.put("name", name);
                        // 校验证件类型代码、职业类别代码、职业代码、银行码表是否录入符合规则
                        errorMsg += checkCodeKey(j - 1, row, idTypeList,
                                occupationTypeList, occupationCodeList,
                                openBankList);
                        // 校验当前职业代码是否符合当前职业类别
                        if (!occupationTypeCodeMap.get(
                                ExcelUtil.getCellValue(row.getCell(8))).contains(
                                ExcelUtil.getCellValue(row.getCell(9)))) {
                            errorMsg += personType+"清单第" + (j - 2) + "行职业类别不包含所录职业；";
                        }
                        // 校验证件类型为身份证时，证件号，性别，出生日期是否录入正确
                        if (ExcelUtil.getCellValue(row.getCell(2)).equals("身份证")) {
                            errorMsg += checkIDCard(j - 2, row);
                        }
                        //校验计划编码是否存在
                        String planCode=ExcelUtil.getCellValue(row.getCell(7));
                        Map<String,Object> planMap=new HashMap<>();
                        planMap.put("planCode",planCode);
                        planMap.put("batch",batch);
                        planMap.put("planObject",personType);
                        int exitPlanCodeNum=fcEdorPlanInfoMapper.exitPlanCodeNum(planMap);
                        if(exitPlanCodeNum<1){
                        	errorMsg += personType+"清单第" + (j - 2) + "行导入"+personType+"计划中不包含此计划；";
                        }
                        //校验增员生效日期应在保单的生效日期和截至日期之间
                        String strDate = ExcelUtil.getCellValue(row.getCell(6));
                        if(!(strDate.compareTo(policyeffectDate) >= 0 && policyendDate.compareTo(strDate) > 0)) {
                        	errorMsg += personType+"清单第" + (j - 2) + "行增员生效日期应在保单的生效日期和截至日期之间；";
                        }else{
                            String endDate = DateTimeUtil.plusDay(30,DateTimeUtil.getCurrentDate());
                            String beginDate = DateTimeUtil.plusDay(-30,DateTimeUtil.getCurrentDate());
                            if (endDate.compareTo(strDate) <0){
                                errorMsg += personType+"清单第" + (j - 2) + "行，增员生效日期应小于等于当前日期+30天";
                            }
                            if (beginDate.compareTo(strDate) > 0){
                                errorMsg += personType+"清单第" + (j - 2) + "行，增员生效日期应大于等于当前日期-30天";
                            }
                        }
                        //获取证件类型
                        //证件类型码值转换
                        String idTypeExcel = ExcelUtil.getCellValue(row.getCell(2));
                        String idType = "";
                        //性别的公式值转换
                        String sexExcel = ExcelUtil.getCellFormula(row.getCell(4));
                        //性别码值转换
                        String sex = "";
                        Map<String, String> sexByIdTypeMap = getSexByIdType(idType, idTypeExcel, idTypeCodeList, sex, sexExcel, sexCodeList);
                        hashMap.put("idType", sexByIdTypeMap.get("idType"));
                        hashMap.put("sex", sexByIdTypeMap.get("sex"));
                        //获取证件号
                        String idNo = ExcelUtil.getCellValue(row.getCell(3));
                        hashMap.put("idNo", idNo);
                        //出生日期
                        String birthDayExcel = ExcelUtil.getCellFormula(row.getCell(5));
                        hashMap.put("birthDay", birthDayExcel);
                        hashMap.put("plusEffectDate", ExcelUtil.getCellValue(row.getCell(6)));
                        hashMap.put("planCode", ExcelUtil.getCellValue(row.getCell(7)));
                        hashMap.put("jobType", ExcelUtil.getCellValue(row.getCell(8)));
                        hashMap.put("jobCode", ExcelUtil.getCellValue(row.getCell(9)));
                        //医保险种没有待定
                        String JoinMedProtect = "";
                        if ("是".equals(ExcelUtil.getCellValue(row.getCell(10)))) {
                            JoinMedProtect = "1";
                        } else {
                            JoinMedProtect = "0";
                        }
                        hashMap.put("medicareStatus", JoinMedProtect);
                        hashMap.put("relationToAppnt", "06");
                        hashMap.put("mobile", ExcelUtil.getCellValue(row.getCell(11)));
                        //1-代表单位全缴 2-单位代扣 3-混合缴费
                        hashMap.put("payMethod", payMethod);
                        
                        //根据计划保费算出个人保费
                        Map<String,String> map1 = new HashMap<>(); 
                        map1.put("planCode",planCode);
                        map1.put("batch",batch);
                        Double planPrem = fcEdorPlanInfoMapper.selectPermByPlanCode(map1);
                        Double comPayment = Double.valueOf(ExcelUtil.getCellValue(row.getCell(13)));
                        Map<String,String> map11 = calculationPrem(planPrem, comPayment,payMethod);
                        if(StringUtils.isNotBlank(map11.get("errmsg"))) {
                        	errorMsg += personType+"清单第" + (j - 2) + "行"+map11.get("errmsg")+"；";
                        }
                        hashMap.put("comPayment", map11.get("comPayment"));
                        hashMap.put("perPayment",map11.get("perPayment"));
                        
                        hashMap.put("debitPayBank", ExcelUtil.getCellValue(row.getCell(14)));
                        hashMap.put("debitPayName", ExcelUtil.getCellValue(row.getCell(15)));
                        hashMap.put("debitPayCode", ExcelUtil.getCellValue(row.getCell(16)));
                        //受益人
                        hashMap.put("deathBenefiName", ExcelUtil.getCellValue(row.getCell(17)));
                        hashMap.put("deathBenefiRelation", fdCodeMapper.selectKeyByCodeName("Relation",ExcelUtil.getCellValue(row.getCell(18))));
                        hashMap.put("subsidiaryInsuredFlag","1");
                        mapList.add(hashMap);
                        idNoList.add(idNo);
                        staffhashMap.put("idType", hashMap.get("idType"));
                        staffhashMap.put("idno", idNo);
                        staffhashMap.put("name", name);
                        staffhashMap.put("sex", hashMap.get("sex"));
                        staffMap.put(idNo,staffhashMap);
                    }
                }
            }
            //被保险人--家属
            if (i == 2) {
                for (int j = 3; j <= sheet.getLastRowNum(); j++) {
                    Map<String, String> hashMap = new HashMap<>();
                    row = sheet.getRow(j);
                    if (ExcelUtil.isRowEmpty(row, 4)) {
                        log.info(personType+"清单第" + (j - 2) + "行是无效数据。");
                    } else {
                        count++;
                        //校验所录数据是否为空
                        errorMsg += checkFamilyIsEmpty(j - 2, row, token, i);
                        if(!errorMsg.equals("")) {
                        	break;
                        }
                        if(!checkCode_Vlue("Relation", null, ExcelUtil.getCellValue(row.getCell(6)))) {
                        	errorMsg += personType+"清单第" + (j - 2) + "行,与主被保险人关系不存在!";
                    	};
                        if (!checkScientifiNotation(ExcelUtil.getCellValue(row.getCell(15))) ||
                    		!CheckUtils.checkMobilePhone(ExcelUtil.getCellValue(row.getCell(15)))) {
                            errorMsg += personType+"清单第" + (j - 2) + "行,手机号填写格式有误!";
                        }
                        if (!checkScientifiNotation(ExcelUtil.getCellValue(row
                                .getCell(9)))) {
                            errorMsg += personType+"清单第" + (j - 2) + "行,主被保险人证件号填写格式有误!";
                        }
                        if (!checkScientifiNotation(ExcelUtil.getCellValue(row
                                .getCell(3)))) {
                            errorMsg += personType+"清单第" + (j - 2) + "行,证件号填写格式有误!";
                        }
                        String payMethod = fdCodeMapper.selectKeyByCodeName("PayPremType",ExcelUtil.getCellValue(row.getCell(16)));
                        if(!Arrays.asList(new String[]{"1","2","3"}).contains(payMethod)) {
                        	errorMsg += personType+"清单第" + (j - 2) + "行,缴费方式不存在！";
                        }
                        if ("3".equals(payMethod)) {
                            if (!checkScientifiNotation(ExcelUtil.getCellValue(row.getCell(20)))) {
                                errorMsg += personType+"清单第" + (j - 2) + "行,扣款缴费银行账号填写格式有误!";
                            }
                        }
                        if(payMethod.equals("1")||payMethod.equals("2")){
                            if(StringUtil.isNotEmpty(ExcelUtil.getCellValue(row.getCell(18)))){
                            	errorMsg += personType+"清单第" + (j - 2) + "行,扣款缴费银行应为空；";
                            }
                            if(StringUtil.isNotEmpty(ExcelUtil.getCellValue(row.getCell(19)))){
                                errorMsg += personType+"清单第" + (j - 2) + "行,扣款缴费账户名应为空；";
                            }
                            if(StringUtil.isNotEmpty(ExcelUtil.getCellValue(row.getCell(20)))){
                            	errorMsg += personType+"清单第" + (j - 2) + "行,扣款缴费银行账号应为空；";
                            }
                        }
                        if(payMethod.equals("3")||payMethod.equals("2")){
                        	if(!isNumber0(ExcelUtil.getCellValue(row.getCell(17)))){
                        		errorMsg += personType+"清单第" + (j - 2) + "行,公司缴费金额必须大于等于0；";
                            }
                        }
                        if(!checkCode_Vlue("Relation", null, ExcelUtil.getCellValue(row.getCell(22)))) {
                        	errorMsg += personType+"清单第" + (j - 2) + "行,受益人与被保险人关系不存在!";
                    	};
                        //获取excel表中的数据
                        hashMap.put("serialNo", ExcelUtil.getCellValue(row.getCell(0)));
                        //主被保险人姓名
                        hashMap.put("staffName", ExcelUtil.getCellValue(row.getCell(7)));
                        //获取被保险人姓名
                        hashMap.put("name", ExcelUtil.getCellValue(row.getCell(1)));
                        //与主被保险人关系
                        hashMap.put("relation", ExcelUtil.getCellValue(row.getCell(6)));
                        //与员工关系
                        hashMap.put("relationToAppnt", "06");
                        // 校验证件类型代码、职业类别代码、职业代码、银行码表是否录入符合规则
                        errorMsg += checkFamilyCodeKey(j - 1, row, idTypeList,
                                occupationTypeList, occupationCodeList,
                                openBankList);
                        // 校验当前职业代码是否符合当前职业类别
                        if (!occupationTypeCodeMap.get(
                                ExcelUtil.getCellValue(row.getCell(12))).contains(
                                ExcelUtil.getCellValue(row.getCell(13)))) {
                            errorMsg += personType+"清单第" + (j - 2) + "行职业类别不包含所录职业";
                        }
                        // 校验被保人证件类型为身份证时，证件号，性别，出生日期是否录入正确
                        if (ExcelUtil.getCellValue(row.getCell(2)).equals("身份证")) {
                            errorMsg += checkFamilyIDCard(j - 2, row);
                        }
                        //校验计划编码是否存在
                        String planCode=ExcelUtil.getCellValue(row.getCell(11));
                        Map<String,Object> planMap=new HashMap<>();
                        planMap.put("planCode",planCode);
                        planMap.put("batch",batch);
                        planMap.put("planObject",personType);
                        int exitPlanCodeNum=fcEdorPlanInfoMapper.exitPlanCodeNum(planMap);
                        if(exitPlanCodeNum<1){
                            errorMsg += personType+"清单第" + (j - 2) + "行导入"+personType+"计划中不包含此计划；";
                        }
                        //校验增员生效日期应在保单的生效日期和截至日期之间
                        String strDate = ExcelUtil.getCellValue(row.getCell(10));
                        if(!(strDate.compareTo(policyeffectDate) >= 0 && policyendDate.compareTo(strDate) > 0)) {
                        	errorMsg += personType+"清单第" + (j - 2) + "行增员生效日期应在保单的生效日期和截至日期之间；";
                        }
                        //证件类型码值转换
                        String idTypeExcel = ExcelUtil.getCellValue(row.getCell(2));
                        String idType = "";
                        //性别码值转换
                        String sexExcel = ExcelUtil.getCellFormula(row.getCell(4));
                        String sex = "";
                        //转码方法
                        Map<String, String> sexByIdTypeMap = getSexByIdType(idType, idTypeExcel, idTypeCodeList, sex, sexExcel, sexCodeList);
                        hashMap.put("idType", sexByIdTypeMap.get("idType"));
                        hashMap.put("sex", sexByIdTypeMap.get("sex"));
                        //主被保人证件类型码值转换
                        String mainIdTypeExcel = ExcelUtil.getCellValue(row.getCell(8));
                        String mainIdType = "";
                        for (HashMap<String, Object> hashMap1 : idTypeCodeList) {
                            if (mainIdTypeExcel.equals(hashMap1.get("CodeName").toString())) {
                                mainIdType = hashMap1.get("CodeKey").toString();
                                break;
                            }
                        }
                        hashMap.put("mainIdType", mainIdType);
                        hashMap.put("mainIdNo", ExcelUtil.getCellValue(row.getCell(9)));
                        //获取被保人证件号
                        String idNo = ExcelUtil.getCellValue(row.getCell(3));
                        hashMap.put("idNo", idNo);//性别的公式值转换
                        //出生日期
                        String birthDayExcel = ExcelUtil.getCellFormula(row.getCell(5));
                        hashMap.put("birthDay", birthDayExcel);
                        hashMap.put("plusEffectDate", ExcelUtil.getCellValue(row.getCell(10)));
                        hashMap.put("jobType", ExcelUtil.getCellValue(row.getCell(12)));
                        hashMap.put("jobCode", ExcelUtil.getCellValue(row.getCell(13)));
                        //医保险种没有待定
                        String JoinMedProtect = "";
                        if ("是".equals(ExcelUtil.getCellValue(row.getCell(14)))) {
                            JoinMedProtect = "1";
                        } else {
                            JoinMedProtect = "0";
                        }
                        hashMap.put("medicareStatus", JoinMedProtect);
                        hashMap.put("mobile", ExcelUtil.getCellValue(row.getCell(15)));
                        hashMap.put("planCode", ExcelUtil.getCellValue(row.getCell(11)));
                        //1-代表单位全缴 2-单位代扣 3-混合缴费
                        hashMap.put("payMethod", payMethod);
                        
                        //根据计划保费算出个人保费
                        Map<String,String> map1 = new HashMap<>(); 
                        map1.put("planCode",planCode);
                        map1.put("batch",batch);
                        Double planPrem = fcEdorPlanInfoMapper.selectPermByPlanCode(map1);
                        Double comPayment = Double.valueOf(ExcelUtil.getCellValue(row.getCell(17)));
                        Map<String,String> map11 = calculationPrem(planPrem, comPayment,payMethod);
                        if(StringUtils.isNotBlank(map11.get("errmsg"))) {
                        	errorMsg += personType+"清单第" + (j - 2) + "行"+map11.get("errmsg")+"；";
                        }
                        hashMap.put("comPayment", map11.get("comPayment"));
                        hashMap.put("perPayment",map11.get("perPayment"));
                        
                        hashMap.put("debitPayBank", ExcelUtil.getCellValue(row.getCell(18)));
                        hashMap.put("debitPayName", ExcelUtil.getCellValue(row.getCell(19)));
                        hashMap.put("debitPayCode", ExcelUtil.getCellValue(row.getCell(20)));
                        //受益人
                        hashMap.put("deathBenefiName", ExcelUtil.getCellValue(row.getCell(21)));
                        hashMap.put("deathBenefiRelation", fdCodeMapper.selectKeyByCodeName("Relation",ExcelUtil.getCellValue(row.getCell(22))));
                        hashMap.put("remarks", ExcelUtil.getCellValue(row.getCell(24)));
                        hashMap.put("subsidiaryInsuredFlag","1");
                        mapList.add(hashMap);
                        idNoList.add(idNo);
                    }
                }
            }
            // 判断模板中是否存在相同的证件号员工以及家属
            HashSet<String> set = new HashSet<>(idNoList);
            if (idNoList.size() != set.size()) {
                // 获得list与set的差集
                Collection rs = CollectionUtils.disjunction(idNoList, set);
                // 将collection转换为list
                List<String> list1 = new ArrayList<>(rs);
                for (String str : list1) {
                    String serialNo = "";
                    for (Map<String, String> hashMap : mapList) {
                        if (hashMap.get("idNo").equals(str)) {
                            serialNo += Double.valueOf(hashMap.get("serialNo")).intValue() + ",";
                        }
                    }
                    errorMsg += "第" + serialNo.substring(0, serialNo.length() - 1) + "行证件号重复；";
                }
            }
          
            //新增人员
            for (Map<String, String> map : mapList) {
            	if (i == 2) {
            		//校验证件号
            		Map<String,String> staffmap = staffMap.get(map.get("mainIdNo"));
            		if(staffmap == null || staffmap.isEmpty()) {
            			errorMsg += "被保险人"+map.get("name")+"所属主被保险人证件号不存在！";
            		}else {
            			//主被保险人姓名
            			if(!staffmap.get("name").equals(map.get("staffName"))) {
            				errorMsg += "被保险人"+map.get("name")+"所属主被保险人姓名不正确！";
            			}
            			//主被保人证件类型
            			if(!staffmap.get("idType").equals(map.get("mainIdType"))) {
            				errorMsg += "被保险人"+map.get("name")+"所属主被保险人证件类型不正确！";
            			};
            		}
            	}
                FCEdorAddInsured fcEdorInsured = new FCEdorAddInsured();
                String plusInsuredSN = maxNoService.createMaxNo("PlusInsuredSN", null, 20);
                //最大号
                fcEdorInsured.setPlusInsuredSN(plusInsuredSN);
                //企业编号
                fcEdorInsured.setGrpNo(grpNo);
                //待定
                fcEdorInsured.setBatch(batch);
                //被保险人姓名
                fcEdorInsured.setName(map.get("name"));
                //出生日期
                fcEdorInsured.setBirthday(map.get("birthDay"));
                //证件类型
                fcEdorInsured.setIdType(map.get("idType"));
                //证件号
                fcEdorInsured.setIdNo(map.get("idNo"));
                //性别
                fcEdorInsured.setSex(map.get("sex"));
                //增员生效日期
                fcEdorInsured.setPlusEffectDate(map.get("plusEffectDate"));
                //计划编码
                fcEdorInsured.setPlanCode(map.get("planCode"));
                //职业代码
                fcEdorInsured.setJobCode(map.get("jobCode"));
                //职业类型
                fcEdorInsured.setJobType(map.get("jobType"));
                //是否有医保
                fcEdorInsured.setMedicareStatus(map.get("medicareStatus"));
                //手机号
                fcEdorInsured.setMobile(map.get("mobile"));
                //缴费方式
                fcEdorInsured.setPayMethod(map.get("payMethod"));
                //企业缴费
                String comPayment = map.get("comPayment");
                if (!"".equals(comPayment)) {
                    double ComPayment = Double.parseDouble(comPayment);
                    fcEdorInsured.setComPayment(ComPayment);
                }
                //个人缴费
                String perPayment = map.get("perPayment");
                if (!"".equals(perPayment)) {
                    double PerPayment = Double.parseDouble(perPayment);
                    fcEdorInsured.setPerPayment(PerPayment);
                }
                fcEdorInsured.setDebitPayBank(map.get("debitPayBank"));
                fcEdorInsured.setDebitPayCode(map.get("debitPayCode"));
                fcEdorInsured.setDebitPayName(map.get("debitPayName"));
                //受益人
                fcEdorInsured.setDeathBenefiName(map.get("deathBenefiName"));
                //受益人与被保人关系
                fcEdorInsured.setDeathBenefiRelation(map.get("deathBenefiRelation"));
                if (i == 1) {
                    //与投保人关系
                    fcEdorInsured.setRelationToAppnt(map.get("relationToAppnt"));
                }
                if (i == 2) {
                    //主被保人证件类型
                    fcEdorInsured.setMainIdType(map.get("mainIdType"));
                    //主被保险人证件号
                    fcEdorInsured.setMainIdNo(map.get("mainIdNo"));
                    //员工关系
                    fcEdorInsured.setRelation(map.get("relation"));
                    //员工姓名
                    fcEdorInsured.setStaffName(map.get("staffName"));
                }
                //备注
                fcEdorInsured.setRemarks(map.get("remarks"));
                //核心人员身份
                fcEdorInsured.setSubsidiaryInsuredFlag(map.get("subsidiaryInsuredFlag"));
                //试算状态0-未提交1-已提交2-申请完成
                fcEdorInsured.setTrialStatus("0");
                //团体保单号
                fcEdorInsured.setGrpContNo(grpContNo);
                //保全类型
                fcEdorInsured.setEdorType("NT");
                fcEdorInsured.setOperator(globalInput.getUserNo());
                fcEdorInsured = (FCEdorAddInsured) CommonUtil.initObject(fcEdorInsured, "INSERT");
                listMap.add(fcEdorInsured);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info(errorMsg);
        } finally {
            if (!errorMsg.equals("")) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", errorMsg);
                return resultMap;
            }
        }
        return resultMap;
    }

    /**
     * 以险种为单位处理数据
     *
     * @param row
     * @param batch
     * @param planCode
     * @param riskCode
     * @param operator
     * @param startCell
     * @param isMedical
     * @param fcEdorPlanInfoList
     * @param dutyCode
     * @return
     */
    public boolean dealRow(List<FCEdorPlanInfo> fcEdorPlanInfoList,Row row,String planCode,String riskCode,String dutyCode,
                           String operator, int startCell,String isMedical,String batch){
        FCEdorPlanInfo fcEdorPlanInfo=new FCEdorPlanInfo();
        fcEdorPlanInfo=planInfo(fcEdorPlanInfo,row,planCode,riskCode,batch,operator,dutyCode);
        //保额
        fcEdorPlanInfo.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                .getCell(startCell))), 10000.0));
        //保费
        fcEdorPlanInfo.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                .getCell(startCell + 1))));
        //判断是否存在免赔额和赔付比例
        String getLimitType;
        if(isMedical.equalsIgnoreCase("Y")){
            fcEdorPlanInfo.setGetLimit(Double.parseDouble(ExcelUtil.getCellValue(row.getCell(startCell+2))));
            if("17030".equals(riskCode)){
                getLimitType=ExcelUtil.getCellValue(row.getCell(startCell+3)).equals("按次免赔") ? "1":"2";
                fcEdorPlanInfo.setGetLimitType(getLimitType);
                fcEdorPlanInfo.setGetRatio(CommonUtil.div(Double.parseDouble(ExcelUtil.getCellValue(row.getCell(startCell + 4))),(double) 100, 2));
            }else if("15040".equals(riskCode)||"17010".equals(riskCode)){
                getLimitType="1";
                fcEdorPlanInfo.setGetLimitType(getLimitType);
                fcEdorPlanInfo.setGetRatio(CommonUtil.div(Double.parseDouble(ExcelUtil.getCellValue(row.getCell(startCell + 3))),(double) 100, 2));
            }else {
                fcEdorPlanInfo.setGetRatio(CommonUtil.div(Double.parseDouble(ExcelUtil.getCellValue(row.getCell(startCell + 3))),(double) 100, 2));
            }
        }
        fcEdorPlanInfoList.add(fcEdorPlanInfo);
        return  true;
    }

    // 校验金额为数字或者小数点后两位 且 大等于0
    public boolean isNumber0(String str) {
        // 判断小数点后2位的数字的正则表达式
        Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$");
        Matcher match = pattern.matcher(str);
        if (match.matches() == false) {
            return false;
        } else if (Double.valueOf(str) >= 0) {
            return true;
        }
        return false;
    }

    // 校验金额为数字或者小数点后两位 且 大等于0
    public boolean isNumber1(String str) {
        // 判断小数点后2位的数字的正则表达式
        Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$");
        Matcher match = pattern.matcher(str);
        if (match.matches() == false) {
            return false;
        } else if (Double.valueOf(str) > 0 && Double.valueOf(str) <= 100) {
            return true;
        }
        return false;
    }

    /**
     * 处理险种-意外伤害住院津贴团体医疗保险（15060）
     *
     * @param row
     * @param batch
     * @param planCode
     * @param riskCode
     * @param operator
     * @param startCell
     * @param fcEdorPlanInfoList
     * @return
     */
    public boolean dealRow_15060(List<FCEdorPlanInfo> fcEdorPlanInfoList,Row row,String planCode,String riskCode,
                                 String operator, int startCell,String batch){
        // 意外伤害住院津贴保险责任GD0029
        FCEdorPlanInfo fcEdorPlanInfo=new FCEdorPlanInfo();
        String dutyCode="GD0029";
        fcEdorPlanInfo=planInfo(fcEdorPlanInfo,row,planCode,riskCode,batch,operator,dutyCode);
        //保额
        fcEdorPlanInfo.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                .getCell(startCell))), 10000.0));
        //保费
        fcEdorPlanInfo.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                .getCell(startCell + 1))));
        fcEdorPlanInfoList.add(fcEdorPlanInfo);
        int stateNum=18;
        if(!ExcelUtil.isCellEmpty(row.getCell(stateNum))&&
                !ExcelUtil.isCellEmpty(row.getCell(stateNum+1))){
            // 意外伤害重症住院津贴保险责任 （可选责任）GD0030
            FCEdorPlanInfo fcEdorPlanInfo1=new FCEdorPlanInfo();
            String dutyCode1="GD0030";
            fcEdorPlanInfo1=planInfo(fcEdorPlanInfo1,row,planCode,riskCode,batch,operator,dutyCode1);
            //保额
            fcEdorPlanInfo1.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell+2))), 10000.0));
            //保费
            fcEdorPlanInfo1.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 3))));
            fcEdorPlanInfoList.add(fcEdorPlanInfo1);
        }
        return  true;
    }

    /**
     * 处理险种-住院津贴团体医疗保险（17020）
     *
     * @param row
     * @param batch
     * @param planCode
     * @param riskCode
     * @param operator
     * @param startCell
     * @param fcEdorPlanInfoList
     * @return
     */
    public boolean dealRow_17020(List<FCEdorPlanInfo> fcEdorPlanInfoList,Row row,String planCode,String riskCode,
                                 String operator, int startCell,String batch){
        String dutyCode="";
        if(!ExcelUtil.isCellEmpty(row.getCell(startCell))||
                !ExcelUtil.isCellEmpty(row.getCell(startCell+1))){
            // 一般住院日额津贴保险责任GD0032
            FCEdorPlanInfo fcEdorPlanInfo=new FCEdorPlanInfo();
            dutyCode="GD0032";
            fcEdorPlanInfo=planInfo(fcEdorPlanInfo,row,planCode,riskCode,batch,operator,dutyCode);
            //保额
            fcEdorPlanInfo.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell))), 10000.0));
            //保费
            fcEdorPlanInfo.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 1))));
            fcEdorPlanInfoList.add(fcEdorPlanInfo);
        }
        startCell=24;
        if(!ExcelUtil.isCellEmpty(row.getCell(startCell))||
                !ExcelUtil.isCellEmpty(row.getCell(startCell+1))){
            // 癌症住院日额津贴保险金 （可选责任）GD0033
            FCEdorPlanInfo fcEdorPlanInfo1=new FCEdorPlanInfo();
            dutyCode="GD0033";
            fcEdorPlanInfo1=planInfo(fcEdorPlanInfo1,row,planCode,riskCode,batch,operator,dutyCode);
            //保额
            fcEdorPlanInfo1.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell+2))), 10000.0));
            //保费
            fcEdorPlanInfo1.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 3))));
            fcEdorPlanInfoList.add(fcEdorPlanInfo1);
        }
        startCell=26;
        if(!ExcelUtil.isCellEmpty(row.getCell(startCell))||
                !ExcelUtil.isCellEmpty(row.getCell(startCell+1))){
            // "重症监护日额津贴保险金 （可选责任）GD0034"
            FCEdorPlanInfo fcEdorPlanInfo2=new FCEdorPlanInfo();
            dutyCode="GD0034";
            fcEdorPlanInfo2=planInfo(fcEdorPlanInfo2,row,planCode,riskCode,batch,operator,dutyCode);
            //保额
            fcEdorPlanInfo2.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell+4))), 10000.0));
            //保费
            fcEdorPlanInfo2.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 5))));
            fcEdorPlanInfoList.add(fcEdorPlanInfo2);
        }
        startCell=28;
        if(!ExcelUtil.isCellEmpty(row.getCell(startCell))||
                !ExcelUtil.isCellEmpty(row.getCell(startCell+1))){
            // 手术医疗津贴保险金（可选责任）GD0035
            FCEdorPlanInfo fcEdorPlanInfo3=new FCEdorPlanInfo();
            dutyCode="GD0035";
            fcEdorPlanInfo3=planInfo(fcEdorPlanInfo3,row,planCode,riskCode,batch,operator,dutyCode);
            //保额
            fcEdorPlanInfo3.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell+6))), 10000.0));
            //保费
            fcEdorPlanInfo3.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 7))));
            fcEdorPlanInfoList.add(fcEdorPlanInfo3);
        }
        return  true;
    }

    /**
     * 处理险种-尊享团体补充医疗保险（17050）
     *
     * @param row
     * @param batch
     * @param planCode
     * @param riskCode
     * @param operator
     * @param startCell
     * @param fcEdorPlanInfoList
     * @return
     */
    public boolean dealRow_17050(List<FCEdorPlanInfo> fcEdorPlanInfoList,Row row,String planCode,String riskCode,
                                 String operator, int startCell,String batch){
        // 一般医疗保险金GD0070
        FCEdorPlanInfo fcEdorPlanInfo=new FCEdorPlanInfo();
        String dutyCode="GD0070";
        fcEdorPlanInfo=planInfo(fcEdorPlanInfo,row,planCode,riskCode,batch,operator,dutyCode);
        //保额
        fcEdorPlanInfo.setAmnt(CommonUtil.mul(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                .getCell(startCell))), 10000.0), 0.5));
        //保费
        fcEdorPlanInfo.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                .getCell(startCell + 1))));

        //免赔额
        fcEdorPlanInfo.setGetLimit(Double.parseDouble(ExcelUtil.getCellValue(row
                .getCell(startCell + 2))));
        //免赔额属性1-按此免赔 2-按年免赔
        fcEdorPlanInfo.setGetLimitType("2");
        //赔付比例
        fcEdorPlanInfo.setGetRatio(CommonUtil.div(Double.parseDouble(ExcelUtil.getCellValue(row.getCell(startCell + 3))),(double) 100, 2));
        fcEdorPlanInfoList.add(fcEdorPlanInfo);
        // 恶性肿瘤医疗保险金GD0071
        FCEdorPlanInfo fcEdorPlanInfo1=new FCEdorPlanInfo();
        dutyCode="GD0071";
        fcEdorPlanInfo1=planInfo(fcEdorPlanInfo1,row,planCode,riskCode,batch,operator,dutyCode);
        //保额
        fcEdorPlanInfo1.setAmnt(CommonUtil.mul(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                .getCell(startCell))), 10000.0), 0.5));
        // -----应横琴核心要求，只获取第一列的责任的免赔额和赔付比例,保费置为0
        fcEdorPlanInfo1.setPrem(0.0);
        //免赔额
        fcEdorPlanInfo1.setGetLimit(0.0);
        //赔付比例
        fcEdorPlanInfo1.setGetRatio(CommonUtil.div(Double.parseDouble(ExcelUtil.getCellValue(row.getCell(startCell + 3))),(double) 100, 2));
        fcEdorPlanInfoList.add(fcEdorPlanInfo1);
        return true;
    }

    /**
     * 处理险种-综合交通团体意外伤害保险（15070）
     *
     * @param row
     * @param batch
     * @param planCode
     * @param riskCode
     * @param operator
     * @param startCell
     * @param fcEdorPlanInfoList
     * @return
     */
    public boolean dealRow_15070(List<FCEdorPlanInfo> fcEdorPlanInfoList,Row row,String planCode,String riskCode,
                                 String operator, int startCell,String batch){
        String dutyCode="";
        if(!ExcelUtil.isCellEmpty(row.getCell(startCell))||
                !ExcelUtil.isCellEmpty(row.getCell(startCell+1))){
            // 公路公共交通工具保险金GD0050
            FCEdorPlanInfo fcEdorPlanInfo=new FCEdorPlanInfo();
            dutyCode="GD0050";
            fcEdorPlanInfo=planInfo(fcEdorPlanInfo,row,planCode,riskCode,batch,operator,dutyCode);
            fcEdorPlanInfo.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell))), 10000.0));
            fcEdorPlanInfo.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 1))));
            fcEdorPlanInfo.setGetLimit(100.0);
            fcEdorPlanInfo.setGetLimitType("2");
            fcEdorPlanInfoList.add(fcEdorPlanInfo);
        }
        // 轨道交通工具保险金GD0051
        startCell=40;
        if(!ExcelUtil.isCellEmpty(row.getCell(startCell))||
                !ExcelUtil.isCellEmpty(row.getCell(startCell+1))){
            FCEdorPlanInfo fcEdorPlanInfo1=new FCEdorPlanInfo();
            dutyCode="GD0051";
            fcEdorPlanInfo1=planInfo(fcEdorPlanInfo1,row,planCode,riskCode,batch,operator,dutyCode);
            fcEdorPlanInfo1.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell+2))), 10000.0));
            fcEdorPlanInfo1.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 3))));
            fcEdorPlanInfo1.setGetLimit(100.0);
            fcEdorPlanInfo1.setGetLimitType("2");
            fcEdorPlanInfoList.add(fcEdorPlanInfo1);
        }
        // 水路公共交通工具保险金GD0052
        startCell=42;
        if(!ExcelUtil.isCellEmpty(row.getCell(startCell))||
                !ExcelUtil.isCellEmpty(row.getCell(startCell+1))){
            FCEdorPlanInfo fcEdorPlanInfo2=new FCEdorPlanInfo();
            dutyCode="GD0052";
            fcEdorPlanInfo2=planInfo(fcEdorPlanInfo2,row,planCode,riskCode,batch,operator,dutyCode);
            fcEdorPlanInfo2.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell+4))), 10000.0));
            fcEdorPlanInfo2.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 5))));
            fcEdorPlanInfo2.setGetLimit(100.0);
            fcEdorPlanInfo2.setGetLimitType("2");
            fcEdorPlanInfoList.add(fcEdorPlanInfo2);
        }
        // 民航班机保险金GD0053
        startCell=44;
        if(!ExcelUtil.isCellEmpty(row.getCell(startCell))||
                !ExcelUtil.isCellEmpty(row.getCell(startCell+1))){
            FCEdorPlanInfo fcEdorPlanInfo3=new FCEdorPlanInfo();
            dutyCode="GD0053";
            fcEdorPlanInfo3=planInfo(fcEdorPlanInfo3,row,planCode,riskCode,batch,operator,dutyCode);
            fcEdorPlanInfo3.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell+6))), 10000.0));
            fcEdorPlanInfo3.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 7))));
            fcEdorPlanInfo3.setGetLimit(100.0);
            fcEdorPlanInfo3.setGetLimitType("2");
            fcEdorPlanInfoList.add(fcEdorPlanInfo3);

        }
        // 私家车或公务车保险金GD0054
        startCell=46;
        if(!ExcelUtil.isCellEmpty(row.getCell(startCell))||
                ExcelUtil.isCellEmpty(row.getCell(startCell+1))){
            FCEdorPlanInfo fcEdorPlanInfo4=new FCEdorPlanInfo();
            dutyCode="GD0054";
            fcEdorPlanInfo4=planInfo(fcEdorPlanInfo4,row,planCode,riskCode,batch,operator,dutyCode);
            fcEdorPlanInfo4.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell+8))), 10000.0));
            fcEdorPlanInfo4.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 9))));
            fcEdorPlanInfo4.setGetLimit(100.0);
            fcEdorPlanInfo4.setGetLimitType("2");
            fcEdorPlanInfoList.add(fcEdorPlanInfo4);

        }
        // 公路公共意外伤害医疗保险金GD0055
        startCell=48;
        if(!ExcelUtil.isCellEmpty(row.getCell(startCell))||
                ExcelUtil.isCellEmpty(row.getCell(startCell+1))){
            FCEdorPlanInfo fcEdorPlanInfo5=new FCEdorPlanInfo();
            dutyCode="GD0055";
            fcEdorPlanInfo5=planInfo(fcEdorPlanInfo5,row,planCode,riskCode,batch,operator,dutyCode);
            fcEdorPlanInfo5.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell+10))), 10000.0));
            fcEdorPlanInfo5.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 11))));
            fcEdorPlanInfo5.setGetLimit(100.0);
            fcEdorPlanInfo5.setGetLimitType("2");
            fcEdorPlanInfoList.add(fcEdorPlanInfo5);

        }
        // 轨道交通意外伤害医疗保险金GD0056
        startCell=50;
        if(!ExcelUtil.isCellEmpty(row.getCell(startCell))||
                ExcelUtil.isCellEmpty(row.getCell(startCell+1))){
            FCEdorPlanInfo fcEdorPlanInfo6=new FCEdorPlanInfo();
            dutyCode="GD0056";
            fcEdorPlanInfo6=planInfo(fcEdorPlanInfo6,row,planCode,riskCode,batch,operator,dutyCode);
            fcEdorPlanInfo6.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell+12))), 10000.0));
            fcEdorPlanInfo6.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 13))));
            fcEdorPlanInfo6.setGetLimit(100.0);
            fcEdorPlanInfo6.setGetLimitType("2");
            fcEdorPlanInfoList.add(fcEdorPlanInfo6);

        }
        // 水路公共交通意外伤害医疗保险金GD0057
        startCell=52;
        if(!ExcelUtil.isCellEmpty(row.getCell(startCell))||
                ExcelUtil.isCellEmpty(row.getCell(startCell+1))){
            FCEdorPlanInfo fcEdorPlanInfo7=new FCEdorPlanInfo();
            dutyCode="GD0057";
            fcEdorPlanInfo7=planInfo(fcEdorPlanInfo7,row,planCode,riskCode,batch,operator,dutyCode);
            fcEdorPlanInfo7.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell+14))), 10000.0));
            fcEdorPlanInfo7.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 15))));
            fcEdorPlanInfo7.setGetLimit(100.0);
            fcEdorPlanInfo7.setGetLimitType("2");
            fcEdorPlanInfoList.add(fcEdorPlanInfo7);

        }
        // 民航班机意外伤害医疗保险金GD0058
        startCell=54;
        if(!ExcelUtil.isCellEmpty(row.getCell(startCell))||
                ExcelUtil.isCellEmpty(row.getCell(startCell+1))){
            FCEdorPlanInfo fcEdorPlanInfo8=new FCEdorPlanInfo();
            dutyCode="GD0058";
            fcEdorPlanInfo8=planInfo(fcEdorPlanInfo8,row,planCode,riskCode,batch,operator,dutyCode);
            fcEdorPlanInfo8.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell+16))), 10000.0));
            fcEdorPlanInfo8.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 17))));
            fcEdorPlanInfo8.setGetLimit(100.0);
            fcEdorPlanInfo8.setGetLimitType("2");
            fcEdorPlanInfoList.add(fcEdorPlanInfo8);

        }
        // 私家车或公务车意外伤害医疗保险金GD0059
        startCell=56;
        if(!ExcelUtil.isCellEmpty(row.getCell(startCell))||
                ExcelUtil.isCellEmpty(row.getCell(startCell+1))){
            FCEdorPlanInfo fcEdorPlanInfo9=new FCEdorPlanInfo();
            dutyCode="GD0059";
            fcEdorPlanInfo9=planInfo(fcEdorPlanInfo9,row,planCode,riskCode,batch,operator,dutyCode);
            fcEdorPlanInfo9.setAmnt(CommonUtil.mul(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell+18))), 10000.0));
            fcEdorPlanInfo9.setPrem(Double.parseDouble(ExcelUtil.getCellValue(row
                    .getCell(startCell + 19))));
            fcEdorPlanInfo9.setGetLimit(100.0);
            fcEdorPlanInfo9.setGetLimitType("2");
            fcEdorPlanInfoList.add(fcEdorPlanInfo9);
        }
        return true;
    }
    //封装对象方法
    public  FCEdorPlanInfo planInfo(FCEdorPlanInfo fcEdorPlanInfo,
                                    Row row,String planCode,String riskCode,String batch,String operator,String dutyCode){
        String edorPlanSN=maxNoService.createMaxNo("fcEdorPlanInfo",null,20);
        fcEdorPlanInfo.setEdorPlanSN(edorPlanSN);
        fcEdorPlanInfo.setPlanName(ExcelUtil.getCellValue(row.getCell(1)));
        fcEdorPlanInfo.setPlanObject(ExcelUtil.getCellValue(row.getCell(3)));
        fcEdorPlanInfo.setPlanCode(planCode);
        fcEdorPlanInfo.setBatch(batch);
        fcEdorPlanInfo.setRiskCode(riskCode);
        //查询险种名称
        FDRiskInfo fdRiskInfo=fdRiskInfoMapper.selectByPrimaryKey(riskCode);
        fcEdorPlanInfo.setRiskName(fdRiskInfo.getRiskName());
        //查询责任名称
        fcEdorPlanInfo.setDutyCode(dutyCode);
        FDRiskDutyInfo fdRiskDutyInfo=new FDRiskDutyInfo();
        fdRiskDutyInfo.setRiskCode(riskCode);
        fdRiskDutyInfo.setDutyCode(dutyCode);
        FDRiskDutyInfo fdRiskDutyInfo1=fdRiskDutyInfoMapper.selectByPrimaryKey(fdRiskDutyInfo);
        fcEdorPlanInfo.setDutyName(fdRiskDutyInfo1.getDutyName());
        fcEdorPlanInfo.setOperator(operator);
        fcEdorPlanInfo=(FCEdorPlanInfo)CommonUtil.initObject(fcEdorPlanInfo,"INSERT");
        return  fcEdorPlanInfo;

    }

    //获取职业类型和职业代码
    public Map<String, List<String>> getOccupationTypeCode() {
        Map<String, List<String>> occupationMap = new HashMap<>();
        List<Map<String, String>> list = fcPerInfoTempMapper.selectOccupationList("01");
        if(list!=null&&list.size()>0){
            for(Map typeMap:list){
                List<String> codeList = new ArrayList<String>();
                String codeKey = typeMap.get("occupationType").toString().trim();
                List<Map<String, String>> codelist = fcPerInfoTempMapper
                        .selectOccupationCode(codeKey);
                if (codelist != null && codelist.size() > 0) {
                    for (int i = 0; i < codelist.size(); i++) {
                        codeList.add(codelist.get(i).get("occupationCode")
                                .toString().trim());
                    }
                }
                occupationMap.put(typeMap.get("occupationType").toString().trim(),
                        codeList);
            }
        }
        return occupationMap;
    }

    //校验员工被保险人证件号
    public String checkIDCard(int i,Row row){
        DateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            String idno = ExcelUtil.getCellValue(row.getCell(3));
            String sex = ExcelUtil.getCellFormula(row.getCell(4));
            String birthday = ExcelUtil.getCellFormula(row.getCell(5));
            if (!IDCardUtil.isIDCard(idno)) {
                return "第" + i + "行身份证号格式错误："+idno;
            } else if (idno.length() != 18) {
                // 经由核心、运营确认，身份证号仅能为18位 add by wudezhong 2021.5.27
                return "第" + i + "行证件号码长度应为18位！";
            } else {
                date = format1.parse(birthday);
                SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
                String dateString = formatter.format(date);
                String idBir = IDCardUtil.dateOfBirth(idno);
                if (!dateString.equals(idBir)) {
                    return "第" + i + "行出生日期与身份证不符";
                }
                if (!getGenderByIdCard(idno).equals(sex)) {
                    return "第" + i + "行性别与身份证不符";
                }
            }
        } catch (ParseException e) {
            return "第" + i + "行数据异常";
        }
        return "";
    }

    // 校验性别及证件号公式
    public static String getGenderByIdCard(String idCard) {
        String sGender = "未知";
        String sCardNum = IDCardUtil.sex(idCard);
        if (Integer.parseInt(sCardNum) == 0) {
            sGender = "男";
        } else {
            sGender = "女";
        }
        return sGender;
    }

    // 科学记数法
    public boolean checkScientifiNotation(String str) {
        if (str.contains("E") || str.contains(".")) {
            return false;
        }
        return true;
    }

    //校验家属被保险人证件号
    public String checkFamilyIDCard(int i,Row row){
        DateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            String idno = ExcelUtil.getCellValue(row.getCell(3));
            String sex = ExcelUtil.getCellFormula(row.getCell(4));
            String birthday = ExcelUtil.getCellFormula(row.getCell(5));
            if (!IDCardUtil.isIDCard(idno)) {
                return "第" + i + "行身份证号格式错误："+idno;
            } else if (idno.length() != 18) {
                // 经由核心、运营确认，身份证号仅能为18位 add by wudezhong 2021.5.27
                return "第" + i + "行证件号码长度应为18位！";
            } else {
                date = format1.parse(birthday);
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                String dateString = formatter.format(date);
                String idBir = IDCardUtil.dateOfBirth(idno);
                if (!dateString.equals(idBir)) {
                    return "第" + i + "行出生日期与身份证不符";
                }
                if (!getGenderByIdCard(idno).equals(sex)) {
                    return "第" + i + "行性别与身份证不符";
                }
            }
        } catch (ParseException e) {
            return "第" + i + "行数据异常";
        }
        return "";
    }

    //证件类型和性别转码
    public static Map<String,String> getSexByIdType(String idType,String idTypeExcel,List<HashMap<String,Object>> idTypeCodeList,
                                                    String sex,String  sexExcel,List<HashMap<String,Object>> sexCodeList){
        Map<String,String> map=new HashMap<>();
        for (HashMap<String, Object> hashMap1 : idTypeCodeList) {
            if (idTypeExcel.equals(hashMap1.get("CodeName").toString())) {
                idType = hashMap1.get("CodeKey").toString();
                break;
            }
        }
        map.put("idType",idType);
        for (HashMap<String, Object> hashMap2 : sexCodeList) {
            if(sexExcel.equals(hashMap2.get("CodeName").toString())) {
                sex = hashMap2.get("CodeKey").toString();
                break;
            }
        }
        map.put("sex",sex);
        return  map;
    }

    //校验员工所录数据是否为空
    public String checkIsEmpty(int i,Row row,String token){
    	String errMsg = "";
        GlobalInput globalInput=userService.getSession(token);
        String grpNo=globalInput.getGrpNo();
        //获取企业信息
//        FCGrpInfo fcGrpInfo=fcGrpInfoMapper.selectByPrimaryKey(grpNo);
        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(1)))){
        	errMsg += "第"+i+"行被保险人姓名不能为空；";
        }
        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(2)))){
        	errMsg +=  "第"+i+"行被保险人证件类型不能为空；";
        }
        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(3)))){
        	errMsg +=  "第"+i+"行被保险人证件号码不能为空；";
        }
        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(4)))){
        	errMsg +=  "第"+i+"行被保险人性别不能为空；";
        }
        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(5)))){
        	errMsg +=  "第"+i+"行被保险人出生日期不能为空；";
        }
        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(6)))){
        	errMsg +=  "第"+i+"行增员生效日期不能为空；";
        }
        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(7)))){
        	errMsg +=  "第"+i+"行保障计划编码不能为空；";
        }
        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(8)))){
        	errMsg +=  "第"+i+"行职业类别不能为空；";
        }
        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(9)))){
        	errMsg +=  "第"+i+"行职业代码不能为空；";
        }
        //缺少投保医疗保险或门急诊医疗保险时（含意外医疗保险），“有无医保”为必填项校验
        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(10)))){
        	errMsg +=  "第"+i+"行有无医保不能为空；";
        }
        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(11)))){
        	errMsg +=  "第"+i+"行手机号不能为空；";
        }
        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(12)))){
        	errMsg +=  "第"+i+"行缴费方式不能为空；";
        }else{
        	if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(13)))){
        		errMsg +=  "第"+i+"行公司缴费金额不能为空；";
            }
            String payMethod = fdCodeMapper.selectKeyByCodeName("PayPremType",ExcelUtil.getCellValue(row.getCell(12)));
            if(payMethod.equals("3")){
                if(payMethod.equals("3")){
                    if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(14)))){
                    	errMsg +=  "第"+i+"行扣款缴费银行不能为空；";
                    }
                    if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(15)))){
                    	errMsg +=  "第"+i+"行扣款缴费账户名不能为空；";
                    }
                    if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(16)))){
                    	errMsg +=  "第"+i+"行扣款缴费银行账号不能为空；";
                    }
                }
            }
        }
        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(17)))){
        	errMsg +=  "第"+i+"行身故受益人姓名不能为空；";
        }
        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(18)))){
        	errMsg +=  "第"+i+"行身故受益人与被保险人关系不能为空；";
        }
        if(!errMsg.equals("")) {
        	errMsg = "员工清单中："+errMsg;
        }
        return errMsg;
    }

    //校验员工证件类型、职业类别、职业编码、银行码表规则
    public String checkCodeKey(int i, Row row, List<String> idTypeList,
                               List<String> occupationTypeList, List<String> occupationCodeList,
                               List<String> openBankList ){
        String idType=ExcelUtil.getCellValue(row.getCell(2));
        String occupationType=ExcelUtil.getCellValue(row.getCell(8));
        String occupationCode=ExcelUtil.getCellValue(row.getCell(9));
        String openBank=ExcelUtil.getCellValue(row.getCell(14));
        if(!"".equals(openBank)){
            if (!openBankList.contains(openBank)) {
                return "第" + i + "行扣款缴费银行录入错误";
            }
        }
        if (!idTypeList.contains(idType)) {
            return "第" + i + "行证件类型录入错误";
        }
        if (!occupationTypeList.contains(occupationType)) {
            return "第" + i + "行职业类别录入错误";
        }
        if (!occupationCodeList.contains(occupationCode)) {
            return "第" + i + "行职业代码录入错误";
        }

        return "";
    }

    //校验家属录入数据是否为空
    public String checkFamilyIsEmpty(int i,Row row,String token,int j){
    	String errMeg =  "";
        GlobalInput globalInput=userService.getSession(token);
        String grpNo=globalInput.getGrpNo();
        //获取企业信息
        FCGrpInfo fcGrpInfo=fcGrpInfoMapper.selectByPrimaryKey(grpNo);
        String grpType= fcGrpInfo.getGrpType();
        if("".equals(ExcelUtil.getCellValue(row.getCell(1)))){
        	errMeg += "第"+i+"行被保险人姓名不能为空；";
        }
        if("".equals(ExcelUtil.getCellValue(row.getCell(2)))){
        	errMeg += "第"+i+"行被保险人证件类型不能为空；";
        }
        if("".equals(ExcelUtil.getCellValue(row.getCell(3)))){
        	errMeg += "第"+i+"行被保险人证件号不能为空；";
        }
        if("".equals(ExcelUtil.getCellFormula(row.getCell(4)))){
        	errMeg += "第"+i+"行被保险人性别不能为空；";
        }
        if("".equals(ExcelUtil.getCellFormula(row.getCell(5)))){
        	errMeg += "第"+i+"行被保险人出生日期不能为空；";
        }
        if("".equals(ExcelUtil.getCellValue(row.getCell(6)))){
        	errMeg += "第"+i+"行与主被保险人关系不能为空；";
        }
        if("".equals(ExcelUtil.getCellValue(row.getCell(7)))){
        	errMeg += "第"+i+"行主被保险人姓名不能为空；";
        }
        if("".equals(ExcelUtil.getCellValue(row.getCell(8)))){
        	errMeg += "第"+i+"行主被保险人证件类型不能为空；";
        }
        if("".equals(ExcelUtil.getCellValue(row.getCell(9)))){
        	errMeg += "第"+i+"行主被保险人证件号不能为空；";
        }
        if("".equals(ExcelUtil.getCellValue(row.getCell(10)))){
        	errMeg += "第"+i+"行增员生效日不能为空；";
        }
        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(11)))){
        	errMeg +=  "第"+i+"行保障计划编码不能为空；";
        }
        if("".equals(ExcelUtil.getCellValue(row.getCell(12)))){
        	errMeg += "第"+i+"行职业类型不能为空；";
        }
        if("".equals(ExcelUtil.getCellValue(row.getCell(13)))){
        	errMeg += "第"+i+"行职业代码不能为空；";
        }
        //缺少投保医疗保险或门急诊医疗保险时（含意外医疗保险），“有无医保”为必填项校验
        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(14)))){
        	errMeg +=  "第"+i+"行有无医保不能为空；";
        }
        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(15)))){
        	errMeg +=  "第"+i+"行手机号不能为空；";
        }
        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(16)))){
        	errMeg +=  "第"+i+"行缴费方式不能为空；";
        }else{
        	if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(17)))){
        		errMeg +=  "第"+i+"行公司缴费金额不能为空；";
        	}
            String payMethod = fdCodeMapper.selectKeyByCodeName("PayPremType",ExcelUtil.getCellValue(row.getCell(16)));
            if(payMethod.equals("3")){
                if(payMethod.equals("3")){
                    if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(18)))){
                    	errMeg +=  "第"+i+"行扣款缴费银行不能为空；";
                    }
                    if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(19)))){
                    	errMeg +=  "第"+i+"行扣款缴费账户名不能为空；";
                    }
                    if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(20)))){
                    	errMeg +=  "第"+i+"行扣款缴费银行账号不能为空；";
                    }
                }
            }
        }
        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(21)))){
        	errMeg +=  "第"+i+"行身故受益人姓名不能为空；";
        }
        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(22)))){
        	errMeg +=  "第"+i+"行身故受益人与被保险人关系不能为空；";
        }
        if(!errMeg.equals("")) {
        	errMeg = "家属清单中："+errMeg;
        }
        return errMeg;
    }


    //校验家属证件类型、职业类别、职业编码、银行码表规则
    public String checkFamilyCodeKey(int i, Row row, List<String> idTypeList,
                                     List<String> occupationTypeList, List<String> occupationCodeList,
                                     List<String> openBankList ){
        String mainIdType=ExcelUtil.getCellValue(row.getCell(8));
        String idType=ExcelUtil.getCellValue(row.getCell(2));
        String occupationType=ExcelUtil.getCellValue(row.getCell(12));
        String occupationCode=ExcelUtil.getCellValue(row.getCell(13));
        String openBank=ExcelUtil.getCellValue(row.getCell(18));
        if(!idTypeList.contains(mainIdType)){
            return 	"第" + i + "行主被保险人证件类型录入错误";
        }
        if (!idTypeList.contains(idType)) {
            return "第" + i + "行证件类型录入错误";
        }
        if (!occupationTypeList.contains(occupationType)) {
            return "第" + i + "行职业类别录入错误";
        }
        if (!occupationCodeList.contains(occupationCode)) {
            return "第" + i + "行职业代码录入错误";
        }
        if(!"".equals(openBank)){
            if (!openBankList.contains(openBank)) {
                return "第" + i + "行扣款缴费银行录入错误";
            }
        }
        return "";
    }
    // 校验金额为数字或者小数点后两位 且 大于0
    public static boolean isNumber(String str) {
        // 判断小数点后2位的数字的正则表达式
        Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$");
        Matcher match = pattern.matcher(str);
        if (match.matches() == false) {
            return false;
        } else if (Double.valueOf(str) > 0) {
            return true;
        }
        return false;
    }
    //计算保费
    public static Map<String,String> calculationPrem(Double planPrem,Double comPayment,String payType){
    	Map<String,String> map = new HashMap<>();	
    	String errmsg = "";
        Double perPayment = 0.0;
        if(payType.equals("2") || payType.equals("3")) {
        	if(planPrem >= comPayment) {
        		perPayment = planPrem - comPayment;
        	}else {
        		comPayment = planPrem;
        	}
        }else if(payType.equals("1")) {
        	if(planPrem > comPayment) {
        		errmsg = "企业缴费应大于等于计划保费";
        	}else {
        		comPayment = planPrem;
        	}
        }
        map.put("errmsg", errmsg);
        map.put("comPayment",String.valueOf(comPayment));
        map.put("perPayment",String.valueOf(perPayment));
        return map;
    }
    //判断码值是否正确
    public Boolean checkCode_Vlue(String codeType,String code,String value) {
    	if(StringUtils.isBlank(codeType) || (StringUtils.isBlank(code) &&  StringUtils.isBlank(value))) {
    		return false;
    	}else {
    		if(StringUtils.isNotBlank(code)) {
    			if(StringUtils.isBlank(fdCodeMapper.selectNameByCode(codeType,code))) {
    				return false;
    			}
    		}else if(StringUtils.isNotBlank(value)) {
    			if(StringUtils.isBlank(fdCodeMapper.selectKeyByCodeName(codeType,value))) {
    				return false;
    			}
    		}
    	}
    	return true;
    }
}
