package com.sinosoft.eflex.service.edor;

import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.FCEdorPlanInfoMapper;
import com.sinosoft.eflex.model.Body;
import com.sinosoft.eflex.model.FCEdorReduInsured;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.RemoteDelegate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @ClassName: EdorZTBL
 * @Auther: hhw
 * @Date: 2019/3/8 10:37:40
 * @Description: TODO 减少被保人试算类
 * @Version: 1.0
 */
@Service
public class EdorZTBL {

    // 工具类加载
    private static final Logger log = LoggerFactory.getLogger(EdorNIBL.class);
    @Autowired
    private MyProps myProps;

    @Autowired
    private FCEdorPlanInfoMapper fcEdorPlanInfoMapper;

    private static final String RESULTFLAG = "resultFlag";

    private static final String ERRMSG = "errMsg";

    private static final String EDORAPPNO = "edorAppNo";


    /**
     * 外部调用本类的业务处理接口
     * @param fcEdorReduInsuredList
     * @param batch
     * @param grpContNo
     * @return
     */
    public Map<String, Object> submitData(List<FCEdorReduInsured> fcEdorReduInsuredList, String batch, String grpContNo) {
        Map<String, Object> resultMap =  new HashMap<String, Object>();
        resultMap = checkData(fcEdorReduInsuredList, grpContNo);
        if (!(boolean)resultMap.get(RESULTFLAG)) {
            return resultMap;
        }
        /*resultMap = checkData();
        if (!(boolean)resultMap.get(RESULTFLAG)) {
            return resultMap;
        }*/
        return dealData(fcEdorReduInsuredList, batch, grpContNo);
    }

    /**
     * 从输入数据中得到所有对象
     * @param fcEdorReduInsuredList
     * @param grpContNo
     * @return
     */
    private Map<String, Object> checkData(List<FCEdorReduInsured> fcEdorReduInsuredList , String grpContNo){
        Map<String, Object> resultMap = new HashMap<String, Object>();
        boolean resultFlag = true;
        List<String> errMsg = new ArrayList<String>();
        if(fcEdorReduInsuredList == null || fcEdorReduInsuredList.size() == 0 ){
            errMsg.add("减少被保险人试算: 请至少录入一条被保人信息！");
            resultFlag = false;
        }
        if(grpContNo == null || "".equals(grpContNo) ){
            errMsg.add("团体保单号不能为空！");
            resultFlag = false;
        }
        resultMap.put(RESULTFLAG, resultFlag);
        resultMap.put(ERRMSG, errMsg);
        return resultMap;
    }

    /**
     * 业务数据校验
     * @return
     */
    /*private Map<String, Object> checkData(){
        Map<String, Object> resultMap = new HashMap<String, Object>();
        boolean resultFlag = true;
        List<String> errMsg = new ArrayList<String>();
        *//****************************** 这里校验各个保全项目的必录项 start ******************************************************//*
        log.info("被保人变更试算：开始校验数据的合法性 ............");
        // 被保人资料变更校验

        log.info("被保人变更试算：数据合法性校验通过..........");
        *//**************************************** 这里校验各个保全项目的必录项 end   ******************************************************//*
        resultMap.put(RESULTFLAG, resultFlag);
        resultMap.put(ERRMSG, errMsg);
        return resultMap;
    }*/


    private Map<String, Object> dealData(List<FCEdorReduInsured> fcEdorReduInsuredList,String batch, String grpContNo){
        return decInsuredTrial(fcEdorReduInsuredList, grpContNo);
    }


    //调用核心减少被保险人试算接口
    public Map<String,Object> decInsuredTrial(List<FCEdorReduInsured> fcEdorReduInsuredList,String grpContNo){
        Map<String, Object> resultMap = new HashMap<String, Object>();
        boolean resultFlag = false;
        List<String> errMsg = new ArrayList<String>();
        String esViewXml="";
        String insuredXml="";
        try{
            RemoteDelegate rd = RemoteDelegate.getInstance();
            for(FCEdorReduInsured fcEdorReduInsured:fcEdorReduInsuredList){
                insuredXml += "\t\t\t<Insured>\n"+
                    "\t\t\t\t<Name>"+fcEdorReduInsured.getName()+"</Name>\n"+
                    "\t\t\t\t<Sex>"+fcEdorReduInsured.getSex()+"</Sex>\n"+
                    "\t\t\t\t<Birthday>"+fcEdorReduInsured.getBirthDay()+"</Birthday>\n"+
                    "\t\t\t\t<IDType>"+fcEdorReduInsured.getIdType()+"</IDType>\n"+
                    "\t\t\t\t<IDNo>"+fcEdorReduInsured.getIdNo()+"</IDNo>\n"+
                    "\t\t\t\t<ZtaliDate>"+fcEdorReduInsured.getZtaliDate()+"</ZtaliDate>\n"+
                    "\t\t\t\t<FeeFrom>"+String.valueOf((int)Double.parseDouble(fcEdorReduInsured.getRefundInstruct()))+"</FeeFrom>\n"+
                "\t\t\t</Insured>\n";
            }
            //后期加影像信息
            esViewXml="\t\t<ESViewList>\n"+
                    "\t\t<ESView>\n" +
                    "\t\t<SubType></SubType>\n" +
                    "\t\t<PageNum></PageNum>\n" +
                    "\t\t<PageList>\n" +
                    "\t\t</Page>\n" +
                    "\t\t<ImageUrl></ImageUrl>\n" +
                    "\t\t<ImageName></ImageName>\n" +
                    "\t\t<PageCode></PageCode>\n" +
                    "\t\t</Page>\n" +
                    "\t\t</PageList>\n" +
                    "\t\t</ESView>\n" +
                    "\t\t</ESViewList>\n";
            String requestXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                    "<RequestInfo>\n" +
                    "\t<HEAD>\n" +
					/* 交易流水号 */
                    "\t\t<TransRefGUID>"+ UUID.randomUUID().toString().replaceAll("-", "")+"</TransRefGUID>\n" +
					/* 接口交易类型 */
                    "\t\t<TransType>ZT0001</TransType>\n" +
					/* 交易日期 */
                    "\t\t<TransExeDate>"+ DateTimeUtil.getCurrentDate()+"</TransExeDate>\n" +
					/* 交易时间 */
                    "\t\t<TransExeTime>"+DateTimeUtil.getCurrentTime()+"</TransExeTime>\n" +
                    "\t</HEAD>\n" +
                    "\t<BODY>\n" +
                    "\t\t<GrpContNo>"+grpContNo+"</GrpContNo>\n"+
                    "\t\t<EdorType>ZT</EdorType>\n"+
                    "\t\t<EdorAppDate>"+DateTimeUtil.getCurrentDate()+"</EdorAppDate>\n"+
                    "\t\t<FeeFlag>"+"N"+"</FeeFlag>\n"+
                    "\t\t<InsuredList>\n"+
                    insuredXml+
                    "\t\t</InsuredList>\n"+
                    //esViewXml+
                    "\t</BODY>\n" +
                    "</RequestInfo>";
            log.info("减少被保人试算接口请求报文：" + requestXml);
            long startStamp = System.currentTimeMillis();

            boolean success = rd.submitData(myProps.getServiceInfo().get("serviceUrl"), myProps.getServiceInfo().get("operation"), "BenefitService", requestXml);
            long endStamp = System.currentTimeMillis();
            log.info("调用接口用时：" +  (endStamp - startStamp));
            if (success){
                Map<String, Object> responseXml=rd.getResult();
                Body body = (Body) responseXml.get("Body");
                log.info("减少被保人试算接口返回报文：" + JSON.toJSONString(responseXml));
                if("0".equals(body.getEdorFlag())&&body.getInsuredList().size()>0){
                    resultFlag = true;
                    resultMap.put("ZTInsuredList",body.getInsuredList());
                    resultMap.put("edorAppNo", body.getEdorAppNo());
                    log.info("减人试算接口： success .....");
                }else{
                    resultFlag = false;
                    errMsg.add(body.getEdorMark());
                    log.info("减人试算接口 失败：" + body.getEdorMark());
                }
            }else {
                resultFlag = false;
                errMsg.add("减少被保险人调用核心失败");
                log.info("减少被保险人试算：调用核心失败");
            }
        }catch (Exception e){
            log.info("错误异常"+e.getMessage());
            errMsg.add("减少被保险人试算: 失败");
        }finally {
            resultMap.put(RESULTFLAG, resultFlag);
            resultMap.put(ERRMSG, errMsg);
            return resultMap;
        }
    }







}
