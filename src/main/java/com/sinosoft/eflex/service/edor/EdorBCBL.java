package com.sinosoft.eflex.service.edor;/**
 * @DESCRIPTION
 * @create 2019-02-28 10:23
 **/

import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.model.Body;
import com.sinosoft.eflex.model.edor.BnfChgTrialIO;
import com.sinosoft.eflex.model.edor.BnfInsured;
import com.sinosoft.eflex.model.edor.InsuredChgTrialIO;
import com.sinosoft.eflex.model.edor.Insureds;
import com.sinosoft.eflex.model.edor.Insurers;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.RemoteDelegate;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @ClassName: EdorBCBL
 * @Auther: hhw
 * @Date: 2019/2/28 10:23
 * @Description: TODO 受益人资料变更保全试算类
 * @Version: 1.0
 */
@Service
public class EdorBCBL {

    // 工具类加载
    private static final Logger log = LoggerFactory.getLogger(EdorBCBL.class);

    @Autowired
    private MyProps myProps;

    private static final String RESULTFLAG = "resultFlag";

    private static final String ERRMSG = "errMsg";

    private static final String EDORAPPNO = "edorAppNo";

    /**
     * 外部调用本类的业务处理接口
     * @param bnfChgTrialIO
     * @param grpContNo
     * @return
     */
    public Map<String,Object> submitData(BnfChgTrialIO bnfChgTrialIO, String grpContNo) {
        Map<String, Object> resultMap =  new HashMap<String, Object>();
        resultMap = getInputData(bnfChgTrialIO, grpContNo);
        if (!(boolean)resultMap.get(RESULTFLAG)) {
            return resultMap;
        }
        resultMap = checkData(bnfChgTrialIO, grpContNo);
        if (!(boolean)resultMap.get(RESULTFLAG)) {
            return resultMap;
        }
        return dealData(bnfChgTrialIO, grpContNo);

    }

    /**
     * 从输入数据中得到所有对象
     * @param bnfChgTrialIO
     * @param grpContNo
     * @return
     */
    private Map<String,Object> getInputData(BnfChgTrialIO bnfChgTrialIO , String grpContNo){
        Map<String, Object> resultMap = new HashMap<String, Object>();
        boolean resultFlag = true;
        List<String> errMsg = new ArrayList<String>();
        if(bnfChgTrialIO == null || bnfChgTrialIO.getInsuredList() == null || bnfChgTrialIO.getInsuredList().size() == 0 || bnfChgTrialIO.getInsuredList().get(0).getBnfList() == null || bnfChgTrialIO.getInsuredList().get(0).getBnfList().size() == 0 ){
            errMsg.add("受益人信息变更：请至少录入一条需要修改的受益人信息！");
            resultFlag = false;
        }
        if(grpContNo == null || "".equals(grpContNo) ){
            errMsg.add("受益人信息变更：团体保单号不能为空！");
            resultFlag = false;
        }
        resultMap.put(RESULTFLAG, resultFlag);
        resultMap.put(ERRMSG, errMsg);
        return resultMap;
    }

    /**
     * 业务数据校验
     * @return
     */
    private Map<String,Object> checkData(BnfChgTrialIO bnfChgTrialIO, String grpContNo){
        Map<String, Object> resultMap = new HashMap<String, Object>();
        boolean resultFlag = true;
        List<String> errMsg = new ArrayList<String>();
        /****************************** 这里校验各个保全项目的必录项 start ******************************************************/
        log.info("被保人变更试算：开始校验数据的合法性 ............");
        // 被保人资料变更校验
        List<Insureds> insuredList = bnfChgTrialIO.getInsuredList();
        if(insuredList == null || insuredList.size() == 0){
            errMsg.add("被保人资料变更需至少录入一条被保人变更信息！");
            resultFlag = false;
        }else {
            int num = 0;
            for(Insureds insurers : insuredList){
                num++;
                for (BnfInsured bnfInsured : insurers.getBnfList()) {
                    if(StringUtils.isBlank(bnfInsured.getBnfName())){
                        errMsg.add("受益人资料变更：被保人"+insurers.getName()+"下第 " + num + " 个受益人的姓名为必录项！");
                        resultFlag = false;
                    }
                    if(StringUtils.isBlank(bnfInsured.getBnfNativePlace())){
                        errMsg.add("受益人资料变更：被保人"+insurers.getName()+"下第 " + num + " 个受益人的国籍为必录项！");
                        resultFlag = false;
                    }
                    if(StringUtils.isBlank(bnfInsured.getBnfIdType())){
                        errMsg.add("受益人资料变更：被保人"+insurers.getName()+"下第 " + num + " 个受益人的证件类型为必录项！");
                        resultFlag = false;
                    }
                    if(StringUtils.isBlank(bnfInsured.getBnfIdNo())){
                        errMsg.add("受益人资料变更：被保人"+insurers.getName()+"下第 " + num + " 个受益人的证件号为必录项！");
                        resultFlag = false;
                    }
                    if(StringUtils.isBlank(bnfInsured.getBnfSex())){
                        errMsg.add("受益人资料变更：被保人"+insurers.getName()+"下第 " + num + " 个受益人的性别为必录项！");
                        resultFlag = false;
                    }
                    if(StringUtils.isBlank(bnfInsured.getBirthDay())){
                        errMsg.add("受益人资料变更：被保人"+insurers.getName()+"下第 " + num + " 个受益人的出生日期为必录项！");
                        resultFlag = false;
                    }
                    if(StringUtils.isBlank(bnfInsured.getRelationToInsured())){
                        errMsg.add("受益人资料变更：被保人"+insurers.getName()+"下第 " + num + " 个受益人的与被保险人的关系为必录项！");
                        resultFlag = false;
                    }
                    if(StringUtils.isBlank(bnfInsured.getBnfGrade())){
                        errMsg.add("受益人资料变更：被保人"+insurers.getName()+"下第 " + num + " 个受益人的受益顺序为必录项！");
                        resultFlag = false;
                    }
                    if(StringUtils.isBlank(bnfInsured.getBnfLot())){
                        errMsg.add("受益人资料变更：被保人"+insurers.getName()+"下第 " + num + " 个受益人的受益份额为必录项！");
                        resultFlag = false;
                    }
                }
            }
        }
        log.info("被保人变更试算：数据合法性校验通过..........");
        /**************************************** 这里校验各个保全项目的必录项 end   ******************************************************/
        resultMap.put(RESULTFLAG, resultFlag);
        resultMap.put(ERRMSG, errMsg);
        return resultMap;
    }


    private Map<String,Object> dealData(BnfChgTrialIO bnfChgTrialIO, String grpContNo){
        return bnfChgTrialIO(bnfChgTrialIO, grpContNo);
    }

    /**
     * 受益人资料变更试算接口
     * @return
     */
    public Map<String,Object> bnfChgTrialIO(BnfChgTrialIO bnfChgTrialIO, String grpContNo){
        Map<String, Object> resultMap = new HashMap<String, Object>();
        boolean resultFlag = false;
        List<String> errMsg = new ArrayList<String>();
        try {
            RemoteDelegate rd = RemoteDelegate.getInstance();
            log.info("受益人资料变更保全试算接口调用时间：" + DateTimeUtil.getCurrentDateTime());
            String insureStr = "";
            List<Insureds> insuredList= bnfChgTrialIO.getInsuredList();
            if (insuredList.size()>0){
                Integer insuredIndex = 0;
                HashSet<Map> insuredSetMap = new HashSet<>();
                for (Insureds insured:insuredList) {
                    insuredIndex++;
                    Map<String,Object> insuredMap = new HashMap<>();
                    insuredMap.put("name",insured.getName());
                    insuredMap.put("iDType",insured.getiDType());
                    insuredMap.put("idNo",insured.getIdNo());
                    if(insuredSetMap.contains(insuredMap)){
                        errMsg.add("第"+insuredIndex+"位被保险人资料信息已经录入，请重新录入其他被保险人资料");
                        resultMap.put(RESULTFLAG, false);
                        resultMap.put(ERRMSG, errMsg);
                        return resultMap;
                    }
                    insuredSetMap.add(insuredMap);
                    String BNFStr="";
                    Integer beneficiaryIndex = 0;
                    HashSet<Map> bngGradeSetMap = new HashSet<>();
                    for (BnfInsured beneficiary:insured.getBnfList()){
                        beneficiaryIndex++;
                        Map<String,Object> bngGradeMap = new HashMap<>();
                            if ("0".equals(beneficiary.getBnfIdType())){
                                if(!DateTimeUtil.isIDCard(beneficiary.getBnfIdNo())){
                                    errMsg.add("第"+insuredIndex+"位被保险人的第"+beneficiaryIndex+"位受益人身份证号录入错误，请重新录入。");
                                    resultMap.put(RESULTFLAG, false);
                                    resultMap.put(ERRMSG, errMsg);
                                    return resultMap;
                                }
                            }
                        bngGradeMap.put("bnfName",beneficiary.getBnfName());
                        bngGradeMap.put("bnfIdType",beneficiary.getBnfIdType());
                        bngGradeMap.put("bnfIdNo",beneficiary.getBnfIdNo());
                        if(bngGradeSetMap.contains(bngGradeMap)){
                            errMsg.add("第"+insuredIndex+"位被保险人的第"+beneficiaryIndex+"位受益人资料信息已经录入，请重新录入其他受益人资料");
                            resultMap.put(RESULTFLAG, false);
                            resultMap.put(ERRMSG, errMsg);
                            return resultMap;
                        }
                        bngGradeSetMap.add(bngGradeMap);
                        BNFStr +="\t\t\t\t\t<Bnf>\n"+
                            "\t\t\t\t\t\t<BnfType>1</BnfType>\n"+
                            "\t\t\t\t\t\t<BnfName>"+beneficiary.getBnfName()+"</BnfName>\n"+
                            "\t\t\t\t\t\t<BnfIdType>"+beneficiary.getBnfIdType()+"</BnfIdType>\n"+
                            "\t\t\t\t\t\t<BnfIdNo>"+beneficiary.getBnfIdNo()+"</BnfIdNo>\n"+
                            "\t\t\t\t\t\t<BnfNativePlace>"+beneficiary.getBnfNativePlace()+"</BnfNativePlace>\n"+
                            "\t\t\t\t\t\t<RelationToInsured>"+beneficiary.getRelationToInsured()+"</RelationToInsured>\n"+
                            "\t\t\t\t\t\t<BnfGrade>"+beneficiary.getBnfGrade()+"</BnfGrade>\n"+
                            "\t\t\t\t\t\t<BnfLot>"+beneficiary.getBnfLot()+"</BnfLot>\n"+
                            "\t\t\t\t\t\t<BnfSex>"+beneficiary.getBnfSex()+"</BnfSex>\n"+
                            "\t\t\t\t\t\t<BnfBirthday>"+beneficiary.getBirthDay()+"</BnfBirthday>\n"+
                            "\t\t\t\t\t</Bnf>\n";
                    }
                    insureStr = insureStr + "\t\t\t<Insured>\n"+
                            "\t\t\t\t<Name>"+insured.getName()+"</Name>\n"+
                            "\t\t\t\t<IDType>"+insured.getiDType()+"</IDType>\n"+
                            "\t\t\t\t<IDNo>"+insured.getIdNo()+"</IDNo>\n"+
                            "\t\t\t\t<BnfList>\n"+
                            BNFStr+
                            "\t\t\t\t</BnfList>\n"+
                            "\t\t\t</Insured>\n";
                }
            }else{
                insureStr = "\t\t\t<Insured>\n"+
                        "\t\t\t\t<Name></Name>\n"+
                        "\t\t\t\t<IDType></IDType>\n"+
                        "\t\t\t\t<IDNo></IDNo>\n"+
                        "\t\t\t\t<BnfList>\n"+
                        "\t\t\t\t\t<Bnf>\n"+
                        "\t\t\t\t\t\t<BnfType></BnfType>\n"+
                        "\t\t\t\t\t\t<BnfName></BnfName>\n"+
                        "\t\t\t\t\t\t<BnfIdType></BnfIdType>\n"+
                        "\t\t\t\t\t\t<BnfIdNo></BnfIdNo>\n"+
                        "\t\t\t\t\t\t<BnfNativePlace></BnfNativePlace>\n"+
                        "\t\t\t\t\t\t<RelationToInsured></RelationToInsured>\n"+
                        "\t\t\t\t\t\t<BnfGrade></BnfGrade>\n"+
                        "\t\t\t\t\t\t<BnfLot></BnfLot>\n"+
                        "\t\t\t\t\t\t<BnfSex></BnfSex>\n"+
                        "\t\t\t\t\t\t<BnfBirthday></BnfBirthday>\n"+
                        "\t\t\t\t\t</Bnf>\n"+
                        "\t\t\t\t</BnfList>\n"+
                        "\t\t\t</Insured>\n";
            }
            String reqXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                    "<RequestInfo>\n" +
                    "\t<HEAD>\n" +
                    "\t\t<TransRefGUID>"+ UUID.randomUUID().toString().replaceAll("-", "") +"</TransRefGUID>\n" +
                    "\t\t<TransType>BC0001</TransType>\n" +
                    "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
                    "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" +
                    "\t</HEAD>\n" +
                    "\t<BODY>\n" +
                    "\t\t<GrpContNo>"+grpContNo+"</GrpContNo>\n" +
                    "\t\t<EdorType>BC</EdorType>\n" +
                    "\t\t<EdorAppDate>"+DateTimeUtil.getCurrentDate()+"</EdorAppDate>\n" +
                    "\t\t<InsuredList>\n"+
                    insureStr+
                    "\t\t</InsuredList>\n"+
                    "\t</BODY>\n"+
                    "</RequestInfo>";
            log.info("调用接口请求报文：" + reqXml);
            long startStamp = System.currentTimeMillis();
            boolean success = rd.submitData(myProps.getServiceInfo().get("serviceUrl"), myProps.getServiceInfo().get("operation"), "", reqXml);
            long endStamp = System.currentTimeMillis();
            log.info("受益人资料变更接口用时：" +  (endStamp - startStamp));
            if (success) {
                Map<String, Object> responseXml = rd.getResult();
                Body body = (Body) responseXml.get("Body");
                log.info("受益人资料变更接口返回报文：" + JSON.toJSONString(responseXml));
                if("0".equals(body.getEdorFlag())){
                    resultFlag = true;
                    resultMap.put(EDORAPPNO, body.getEdorAppNo());
                    log.info("受益人资料变更成功，保全受理号 :" + body.getEdorAppNo());
                }else {
                    resultFlag = false;
                    errMsg.add("受益人资料变更失败 :"+body.getEdorMark());
                    log.info("受益人资料变更失败 ：" + body.getEdorMark());
                }
            }else {
                log.info("受益人资料变更失败,请重试！");
                errMsg.add("受益人资料变更失败,请重试！");
                resultFlag = false;
            }
        }catch (Exception e){
            log.info("受益人资料变更试算接口调用失败" + e.getMessage());
            resultMap.put("flag",false);
            resultMap.put("message","受益人资料变更试算失败, 系统异常！");
        }finally {
            resultMap.put(RESULTFLAG, resultFlag);
            resultMap.put(ERRMSG, errMsg);
            return resultMap;
        }
    }

}
