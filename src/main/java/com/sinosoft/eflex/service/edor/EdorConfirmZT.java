package com.sinosoft.eflex.service.edor;/**
 * @DESCRIPTION
 * @create 2019-03-18 10:15:33
 **/

import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.FCEdorReduInsuredMapper;
import com.sinosoft.eflex.model.Body;
import com.sinosoft.eflex.model.FCEdorReduInsured;
import com.sinosoft.eflex.model.FCEdoruploadfile;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.RemoteDelegate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @ClassName: EdorConfirmZT
 * @Auther: hhw
 * @Date: 2019/3/18 10:15:33
 * @Description: TODO
 * @Version: 1.0
 */
@Service
public class EdorConfirmZT {

    private static final Logger log = LoggerFactory.getLogger(EdorService.class);

    private static final String RESULTFLAG = "resultFlag";

    private static final String ERRMSG = "errMsg";

    private static final String EDORAPPNO = "edorAppNo";

    @Autowired
    private MyProps myProps;
    @Autowired
    private FCEdorReduInsuredMapper fcEdorReduInsuredMapper;


    //减少被保险人申请
    public Map<String,Object> decInsuredApply(Map<String,Object> descInsuredMap, String edorAppNo, String grpContNo, List<FCEdoruploadfile> uploadfilelist){
        Map<String,Object> resultMap=new HashMap<>();
        boolean resultFlag = false;
        List<String> errMsg = new ArrayList<String>();
        RemoteDelegate rd = RemoteDelegate.getInstance();
        //影像报文
        String esViewXml="";
        //被保人报文
        String insuredXml="";
        try{
            List<FCEdorReduInsured> fcEdorReduInsuredList = fcEdorReduInsuredMapper.getDecInsuredInfo(descInsuredMap);
            if(fcEdorReduInsuredList.size()>0){
                for(FCEdorReduInsured fcEdorReduInsured:fcEdorReduInsuredList){
                    insuredXml+=
                            "\t\t\t<Insured>\n"+
                                    "\t\t\t\t<Name>"+fcEdorReduInsured.getName()+"</Name>\n"+
                                    "\t\t\t\t<Sex>"+fcEdorReduInsured.getSex()+"</Sex>\n"+
                                    "\t\t\t\t<Birthday>"+fcEdorReduInsured.getBirthDay()+"</Birthday>\n"+
                                    "\t\t\t\t<IDType>"+fcEdorReduInsured.getIdType()+"</IDType>\n"+
                                    "\t\t\t\t<IDNo>"+fcEdorReduInsured.getIdNo()+"</IDNo>\n"+
                                    "\t\t\t\t<ZtaliDate>"+fcEdorReduInsured.getZtaliDate()+"</ZtaliDate>\n"+
                                    "\t\t\t\t<FeeFrom>"+String.valueOf((int)Double.parseDouble(fcEdorReduInsured.getRefundInstruct()))+"</FeeFrom>\n"+
                                    "\t\t\t</Insured>\n";
                }
            }else{
                insuredXml= "\t\t\t<Insured>\n"+
                        "\t\t\t\t<Name></Name>\n"+
                        "\t\t\t\t<Sex></Sex>\n"+
                        "\t\t\t\t<Birthday></Birthday>\n"+
                        "\t\t\t\t<IDType></IDType>\n"+
                        "\t\t\t\t<IDNo></IDNo>\n"+
                        "\t\t\t\t<ZtaliDate ></ZtaliDate >\n"+
                        "\t\t\t\t<FeeFrom></FeeFrom>\n"+
                        "\t\t\t</Insured>\n";
            }
            //获取保全减人影像件
            String fileXml = "";
            for (int i = 0; i < uploadfilelist.size(); i++) {
                //影像文件列表
                String ftpPath = uploadfilelist.get(i).getFtpPath().substring(0,uploadfilelist.get(i).getFtpPath().lastIndexOf("/")+1);
                fileXml +=
                        "\t\t<Page>\n" +
                                "\t\t<ImageUrl>"+ftpPath+"</ImageUrl>\n" +
                                "\t\t<ImageName>"+ uploadfilelist.get(i).getFileName() +"</ImageName>\n" +
                                "\t\t<PageCode>"+ i +"</PageCode>\n" +
                                "\t\t</Page>\n";

            }
            if (uploadfilelist.size()>0){
                esViewXml="\t\t<ESViewList>\n"+
                        //影像件循环节点
                        "\t\t<ESView>\n" +
                        "\t\t<SubType>312011</SubType>\n" +
                        "\t\t<PageNum>1</PageNum>\n" +
                        "\t\t<PageList>\n" +
                        fileXml +
                        "\t\t</PageList>\n"+
                        "\t\t</ESView>\n" +
                        "\t\t</ESViewList>\n";
            }else{
                esViewXml="\t\t<ESViewList>\n"+
                        "\t\t</ESViewList>\n";
            }
            String requestXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                    "<RequestInfo>\n" +
                    "\t<HEAD>\n" +
					/* 交易流水号 */
                    "\t\t<TransRefGUID>"+ UUID.randomUUID().toString().replaceAll("-", "")+"</TransRefGUID>\n" +
					/* 接口交易类型 */
                    "\t\t<TransType>ZT0002</TransType>\n" +
					/* 交易日期 */
                    "\t\t<TransExeDate>"+ DateTimeUtil.getCurrentDate()+"</TransExeDate>\n" +
					/* 交易时间 */
                    "\t\t<TransExeTime>"+DateTimeUtil.getCurrentTime()+"</TransExeTime>\n" +
                    "\t</HEAD>\n" +
                    "\t<BODY>\n" +
                    "\t\t<GrpContNo>"+grpContNo+"</GrpContNo>\n"+
                    "\t\t<EdorAppNo>"+edorAppNo+"</EdorAppNo>\n"+
                    "\t\t<EdorType>ZT</EdorType>\n"+
                    "\t\t<EdorAppDate>"+DateTimeUtil.getCurrentDate()+"</EdorAppDate>\n"+
                    "\t\t<FeeFlag>N</FeeFlag>\n"+
                    "\t\t<InsuredList>\n"+
                    insuredXml+
                    "\t\t</InsuredList>\n"+
                    esViewXml+
                    "\t</BODY>\n" +
                    "</RequestInfo>";
            log.info("减少被保人申请接口请求报文：" + requestXml);
            long startStamp = System.currentTimeMillis();
            boolean success = rd.submitData(myProps.getServiceInfo().get("serviceUrl"), myProps.getServiceInfo().get("operation"), "BenefitService",requestXml);
            long endStamp = System.currentTimeMillis();
            log.info("调用接口用时：" +  (endStamp - startStamp));
            if (success){
                Map<String, Object> responseXml = rd.getResult();
                Body body = (Body) responseXml.get("Body");
                log.info("减少被保人申请接口返回报文：" + JSON.toJSONString(responseXml));
                if ("0".equals(body.getEdorFlag())){
                    resultFlag = true;
                    resultMap.put("ZTInsuredBody",body);
                }else{
                    resultFlag = false;
                    errMsg.add("减少被保人："+body.getEdorMark());
                }
               /* resultMap.put("data",body);
                resultMap.put("success", true);
                resultMap.put("code", "200");
                resultMap.put("message", "减少保险人申请接口调用完成！");*/
            }else {
                resultFlag = false;
                errMsg.add("减少被保险人调用核心失败");
                log.info("减少被保险人申请：调用核心失败");
            }
        }catch (Exception e){
            resultFlag = false;
            log.info("错误异常", e);
            errMsg.add("减少保险人申请失败");
        }finally {
            resultMap.put(RESULTFLAG, resultFlag);
            resultMap.put(ERRMSG, errMsg);
            return resultMap;
        }
    }
}
