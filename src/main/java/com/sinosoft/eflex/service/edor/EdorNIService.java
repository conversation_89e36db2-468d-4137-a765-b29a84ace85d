package com.sinosoft.eflex.service.edor;

import java.util.List;

import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.model.FCPlanRiskDuty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.sinosoft.eflex.model.FCEdorAddInsured;
import com.sinosoft.eflex.model.FCEnsure;
import com.sinosoft.eflex.model.FCGrpOrder;
import com.sinosoft.eflex.util.CommonUtil;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.enums.StateEnum;
import com.sinosoft.eflex.enums.TrialStateEnum;

/**
 * <AUTHOR>
 * @create 2021/9/13
 * @desc 保全增人处理类
 */
@Service
public class EdorNIService {

    @Autowired
    private FCEdorAddInsuredMapper fcEdorAddInsuredMapper;
    @Autowired
    private FCEnsurePlanMapper fcEnsurePlanMapper;
    @Autowired
    private FCGrpOrderMapper fcGrpOrderMapper;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private FCPlanRiskDutyMapper fcPlanRiskDutyMapper;

    /**
     * 保费试算
     */
    @Transactional
    public String edorNITrial(String grpContNo, String batch) {

        // 获取团单信息
        FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByGrpContNo(grpContNo);
        // 获取福利信息
        FCEnsure fcensure = fcEnsureMapper.getFcensureByGrpContNo(grpContNo);
        // 保单生效日期
        String cvaliDate = fcensure.getCvaliDate();
        // 保单截至日期
        String policyEndDate = fcensure.getPolicyEndDate();
        // 保险期间天数
        Long policyDays = DateTimeUtil.getDistanceDays(policyEndDate, cvaliDate) + 1;

        /**
         * 获取当前保全批次人员
         */
        FCEdorAddInsured fcEdorAddInsuredParam = new FCEdorAddInsured();
        fcEdorAddInsuredParam.setGrpContNo(grpContNo);
        fcEdorAddInsuredParam.setBatch(batch);
        List<FCEdorAddInsured> fcEdorAddInsureds = fcEdorAddInsuredMapper
                .selectEdorAddInsuredLst(fcEdorAddInsuredParam);

        /**
         * 试算逻辑
         */
        for (FCEdorAddInsured fcEdorAddInsured : fcEdorAddInsureds) {
            /**
             * 试算保费，每一个责任保费都保留两位小数，最后相加整体保费保留两位小数，责任层级
             */
            // 计划保费，核心之前提供的公式存在问题
//            double planPrem = fcEnsurePlanMapper.selectPlanPrem(fcGrpOrder.getEnsureCode(),
//                    fcEdorAddInsured.getPlanCode());
//            // 应收保费 = 年缴（期缴）保险费×（剩余保险天数÷总单保险期限），整体保留两位小数
//            // 剩余保险天数
//            Long residueDays = DateTimeUtil.getDistanceDays(policyEndDate, fcEdorAddInsured.getPlusEffectDate()) + 1;
//            // double actualPrem = planPrem * (residueDays / policyDays);
//            Double actualPrem = CommonUtil.mul(planPrem,
//                    CommonUtil.div(new Double(residueDays), new Double(policyDays), 8));
//            String actualPrem1 = String.format("%.2f", actualPrem);
//            if (planPrem != 0 && actualPrem == 0) {
//                fcEdorAddInsured.setIsError(StateEnum.VALID.getCode());
//                fcEdorAddInsured.setTrialStatus(TrialStateEnum.INSUREDPREPARE.getCode());
//                fcEdorAddInsured.setErrorDesc("保费试算结果为空！");
//            } else {
//                fcEdorAddInsured.setTrialStatus(TrialStateEnum.TRIALDONE.getCode());
//                fcEdorAddInsured.setIsError(StateEnum.INVALID.getCode());
//                fcEdorAddInsured.setPrem(actualPrem1);
//            }

            // 计划保费，核心之前提供的公式存在问题
            double planPrem = fcEnsurePlanMapper.selectPlanPrem(fcGrpOrder.getEnsureCode(), fcEdorAddInsured.getPlanCode());
            // 剩余保险天数
            Long residueDays = DateTimeUtil.getDistanceDays(policyEndDate, fcEdorAddInsured.getPlusEffectDate()) + 1;
            // 试算总保费
            Double actualTotalDutyPrem = 0.0;
            // 获取险种责任列表
            List<FCPlanRiskDuty> riskDutyList = fcPlanRiskDutyMapper.selectRiskDutyListByPlanCode(fcGrpOrder.getEnsureCode(), fcEdorAddInsured.getPlanCode());
            // 获取责任试算总保费
            for (FCPlanRiskDuty fcPlanRiskDuty : riskDutyList) {
                // 应收责任保费 = 责任保费×（剩余保险天数÷总单保险期限）
                Double actualDutyPrem1 = CommonUtil.mul(fcPlanRiskDuty.getPrem(), CommonUtil.div(new Double(residueDays), new Double(policyDays), 8),2);
                actualTotalDutyPrem = CommonUtil.add(actualTotalDutyPrem,actualDutyPrem1);
            }
            if (planPrem != 0 && (actualTotalDutyPrem == 0 || actualTotalDutyPrem == 0.0)) {
                fcEdorAddInsured.setIsError(StateEnum.VALID.getCode());
                fcEdorAddInsured.setTrialStatus(TrialStateEnum.INSUREDPREPARE.getCode());
                fcEdorAddInsured.setErrorDesc("保费试算结果为空！");
            } else {
                fcEdorAddInsured.setTrialStatus(TrialStateEnum.TRIALDONE.getCode());
                fcEdorAddInsured.setIsError(StateEnum.INVALID.getCode());
                fcEdorAddInsured.setPrem(actualTotalDutyPrem.toString());
            }

            /**
             * 更新保全增人信息表
             */
            // 缴费来源-1：企业，默认
            fcEdorAddInsured.setPremSource("1");
            // 结算方式-1：实时结算，默认
            fcEdorAddInsured.setAccountType("1");
            fcEdorAddInsured = CommonUtil.initObject(fcEdorAddInsured, "update");
            fcEdorAddInsuredMapper.updateEdorAddInsured(fcEdorAddInsured);
        }

        return "试算完成！";
    }

}
