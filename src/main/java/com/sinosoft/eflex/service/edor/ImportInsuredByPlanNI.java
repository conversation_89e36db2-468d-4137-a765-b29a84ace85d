package com.sinosoft.eflex.service.edor;/**
 * @DESCRIPTION
 * @create 2019-03-13 13:52:48
 **/

import com.hqins.common.helper.BeanCopier;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.enums.*;
import com.sinosoft.eflex.model.AddressEntity.CheckCustomerVO;
import com.sinosoft.eflex.model.AddressEntity.EvaluationCustomer;
import com.sinosoft.eflex.model.AddressEntity.IdCardVerifyRequest;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.service.AddressCheckService;
import com.sinosoft.eflex.service.MaxNoService;
import com.sinosoft.eflex.service.UserService;
import com.sinosoft.eflex.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName: ImportInsuredNI
 * @Auther: hhw
 * @Date: 2019/3/13 13:52:48
 * @Description: TODO  新增被保人导入清单解析类
 * @Version: 1.0
 */
@Service
public class ImportInsuredByPlanNI {

    /**
     * 日志打印工具
     */
    private static final Logger log = LoggerFactory.getLogger(EdorService.class);

    @Autowired
    private FDEdorItemMapper fdEdorItemMapper;
    @Autowired
    private FCGrpEdorConfigMapper fcGrpEdorConfigMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private FDCodeMapper fdCodeMapper;
    @Autowired
    private FCGrpOrderMapper fcGrpOrderMapper;
    @Autowired
    private FdUserMapper fdUserMapper;
    @Autowired
    private FCOrderInsuredMapper fcOrderInsuredMapper;
    @Autowired
    private FCPerInfoTempMapper fcPerInfoTempMapper;
    @Autowired
    private MaxNoService maxNoService;
    @Autowired
    private FCEdorAddInsuredMapper fcEdorAddInsuredMapper;
    @Autowired
    private FCEdorReduInsuredMapper fcEdorReduInsuredMapper;
    @Autowired
    private FDRiskInfoMapper fdRiskInfoMapper;
    @Autowired
    private FDRiskDutyInfoMapper fdRiskDutyInfoMapper;
    @Autowired
    private FCEdorPlanInfoMapper fcEdorPlanInfoMapper;
    @Autowired
    private FCGrpInfoMapper fcGrpInfoMapper;
    @Autowired
    private FcEdorItemMapper fcEdorItemMapper;
    @Autowired
    private AddressCheckService addressCheckService;


    //导入计划错误信息
    private String errorMsg = "";
    //导入人员清单的信息
    List<FCEdorAddInsured> listMap = new ArrayList<>();
    //员工五要素信息集合
    Map<String, Map<String, String>> staffMap = new HashMap<>();

    // 险种保费
    private double riskPrem;
    boolean bool = true;

    //解析增加被保险人清单excel    

    /**
     * 根据计划的增人模板处理逻辑
     */
    public Map<String, Object> dealPlusInsuredExcelByPlan(String token, Workbook wb, String batch, String grpContNo, String policyeffectDate, String policyendDate, List<String> planlist, Map<String, Double> planInfo) {
        GlobalInput globalInput = userService.getSession(token);
        String grpNo = globalInput.getGrpNo();
        System.out.println("grpNo:" + grpNo);
        //返回前台结果
        Map<String, Object> resultMap = new HashMap<>();
        //被保人解析结果
        Map<String, Object> insuredMap = new HashMap<>();
        listMap.clear();
        staffMap.clear();
        try {
            //解析之前增加一下关于该批次下的人员问题,提示会覆盖导入
            Map<String, Object> insuerdNumMap1 = new HashMap<>();
            insuerdNumMap1.put("grpNo", grpNo);
            insuerdNumMap1.put("batch", batch);
            int addInsuredNum1 = fcEdorAddInsuredMapper.selectInsuredCount(insuerdNumMap1);
            if (addInsuredNum1 != 0) {
                System.out.println("进来了");
                fcEdorAddInsuredMapper.deleteAddinsured(batch);
            }
            //删除之前的申请信息
            fcEdorItemMapper.deleteByPrimaryKey(batch);
            errorMsg = "";
            int sheetNum = sheetCirculation(wb);
            //解析Excepl
            List<EvaluationCustomer> checkCustomer = new ArrayList<>();
            for (int i = 0; i < sheetNum - 1; i++) {
                //处理人员清单信息
                if (i == 0 || i == 1) {
                    insuredMap = dealPlusInsuredInfoByPlan(token, wb, i, batch, grpContNo, policyeffectDate, policyendDate, planlist, planInfo, checkCustomer);
                    if (!insuredMap.isEmpty()) {
                        resultMap.put("success", false);
                        resultMap.put("code", "500");
                        resultMap.put("message", insuredMap.get("message"));
                        break;
                    }
                }
            }

            //解析完成后存入保全增人表
            if (resultMap.isEmpty()) {
                if (listMap.isEmpty()) {
                    resultMap.put("success", false);
                    resultMap.put("code", "500");
                    resultMap.put("message", "导入人员不能为空！");
                } else {
                    /**
                     * 循环每30条存储一次
                     */
                    if (listMap.size() <= 30) {
                        fcEdorAddInsuredMapper.insertNext(listMap);
                    } else {
                        // 声明存储的容器
                        List<FCEdorAddInsured> fcEdorAddInsuredList = new ArrayList<>();
                        for (int i = 0; i < listMap.size(); i++) {
                            fcEdorAddInsuredList.add(listMap.get(i));
                            if (fcEdorAddInsuredList.size() == 30 || (i == listMap.size() - 1)) {
                                int isSuc = fcEdorAddInsuredMapper.insertNext(fcEdorAddInsuredList);
                                log.info("\n存储保全增人被保险人成功与否：" + isSuc);
                                fcEdorAddInsuredList.clear();
                            }
                        }
                    }
                    Map<String, Object> insuerdNumMap = new HashMap<>();
                    insuerdNumMap.put("grpNo", grpNo);
                    insuerdNumMap.put("batch", batch);
                    int addInsuredNum = fcEdorAddInsuredMapper.selectInsuredCount(insuerdNumMap);
                    resultMap.put("InsuredNum", addInsuredNum);
                    resultMap.put("code", "200");
                    resultMap.put("message", "导入成功");
                }
            }
            return resultMap;
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("code", "500");
            resultMap.put("message", "导入失败");
            return resultMap;
        }
    }


    /**
     * 获取sheet页数
     *
     * @param wb
     * @return
     */
    public int sheetCirculation(Workbook wb) {
        int sheetCount = 0;
        sheetCount = wb.getNumberOfSheets();
        return sheetCount;
    }

    //处理被保险人清单
    public Map<String, Object> dealPlusInsuredInfoByPlan(String token, Workbook wb, int i, String batch, String grpContNo, String policyeffectDate, String policyendDate, List<String> planlist, Map<String, Double> planInfo, List<EvaluationCustomer> checkCustomer) {
        Map<String, Object> resultMap = new HashMap<>();
        GlobalInput globalInput = userService.getSession(token);
        String grpNo = globalInput.getGrpNo();
        //获取性别Value-Key
        List<HashMap<String, Object>> sexCodeList = fdCodeMapper.CodeInfo("Sex");
        //获取证件类型和证件号码的Value-Key
        List<HashMap<String, Object>> idTypeCodeList = fdCodeMapper.CodeInfo("IDType");
        // 获取团体订单信息
        FCGrpOrder fcGrpOrder = fcGrpOrderMapper.selectGrpOrderByGrpContNo(grpContNo);
        // 是否为平台团单
        Boolean isEflexGrpOrder = true;
        if (ObjectUtils.isEmpty(fcGrpOrder)) {
            isEflexGrpOrder = false;
        }

        //错误信息
        String errorMsg = "";
        //记录录入行数
        int count = 0;

        String personType = "";
        if (i == 0) {
            personType = "员工";
        } else {
            personType = "家属";
        }
        try {
            //增加校验保全增人表中第一张表和第二张表列数的逻辑
//            Sheet sheet = wb.getSheetAt(0);
//            // 获取最后一行的列数
//            int physicalNumberOfCells = sheet.getRow(sheet.getLastRowNum())
//                    .getPhysicalNumberOfCells();
//            if (physicalNumberOfCells != 14) {
//                errorMsg += "模板有误，请您重新下载！/";
//                resultMap.put("success", false);
//                resultMap.put("code", "500");
//                resultMap.put("message", errorMsg);
//                return resultMap;
//            }
//            Sheet sheet2 = wb.getSheetAt(1);
//            // 获取最后一行的列数
//            int physicalNumberOfCellsOne = sheet2.getRow(sheet2.getLastRowNum())
//                    .getPhysicalNumberOfCells();
//            if (physicalNumberOfCellsOne != 18) {
//                errorMsg += "模板有误，请您重新下载！/";
//                resultMap.put("success", false);
//                resultMap.put("code", "500");
//                resultMap.put("message", errorMsg);
//                return resultMap;
//            }
            List<String> idNoList = new ArrayList<>();
            List<Map<String, String>> mapList = new ArrayList<>();
            // 获取证件类型集合
            List<String> idTypeList = fcPerInfoTempMapper.getIdTypeList("01");
            // 获取职业类别集合
            List<String> occupationTypeList = fcPerInfoTempMapper.getOccupationTypeList("01");
            // 获取职业集合
            List<String> occupationCodeList = fcPerInfoTempMapper.getOccupationCodeList("01");
            //获取与投保人关系Value-Key
            List<HashMap<String, Object>> relationshipCodeList = fdCodeMapper.CodeInfo("PolicyholderRelationship");
            // 获取职业类别与职业代码对应关系
            Map<String, List<String>> occupationTypeCodeMap = getOccupationTypeCode();
            // 获取银行代码集合
            List<String> openBankList = fcPerInfoTempMapper.getOpenBankList("01");
            Sheet sheet = wb.getSheetAt(i);
            Row row = null;
            //被保险人--员工
            if (i == 0) {
                for (int j = 3; j <= sheet.getLastRowNum(); j++) {
                    log.info("正在处理员工清单第" + (j - 2) + "行数据！");
                    Map<String, String> hashMap = new HashMap<>();
                    Map<String, String> staffhashMap = new HashMap<>();
                    row = sheet.getRow(j);
                    if (ExcelUtil.isRowEmpty(row, 3)) {
                        log.info(personType + "清单第" + (j - 2) + "行是无效数据。");
                    } else {
                        count++;
                        //校验所录数据是否为空
                        errorMsg += checkIsEmpty(j + 1, row, token);
                        if (!errorMsg.equals("")) {
                            break;
                        }
                        String relationship = ExcelUtil.getCellFormula(row.getCell(14));
                        for (HashMap<String, Object> map : relationshipCodeList) {
                            if (relationship.equals(map.get("CodeName").toString())) {
                                relationship = map.get("CodeKey").toString();
                                break;
                            }
                        }
                        if (!checkScientifiNotation(ExcelUtil.getCellValue(row.getCell(13))) ||
                                !CheckUtils.checkMobilePhone(ExcelUtil.getCellValue(row.getCell(13)))) {
                            errorMsg += personType + "清单第" + (j - 2) + "行,手机号填写格式有误!";
                        }
                        if (!checkScientifiNotation(ExcelUtil.getCellValue(row
                                .getCell(4)))) {
                            errorMsg += personType + "清单第" + (j - 2) + "行,被保险人证件号填写格式有误!";
                        }
                        //String payMethod = fdCodeMapper.selectKeyByCodeName("PayPremType",ExcelUtil.getCellValue(row.getCell(14)));
                        String payMethod = "1";
                        if (!Arrays.asList(new String[]{"1", "2", "3"}).contains(payMethod)) {
                            errorMsg += personType + "清单第" + (j - 2) + "行,缴费方式不存在！";
                        }
                        //核心暂时不支持企业代缴、混合缴费 关闭相关缴费信息校验
                        /*if ("3".equals(payMethod)) {
                            if (!checkScientifiNotation(ExcelUtil.getCellValue(row.getCell(18)))) {
                                errorMsg += personType+"清单第" + (j - 2) + "行,扣款缴费银行账号填写格式有误!";
                            }
                        }
                        if(payMethod.equals("1") || payMethod.equals("2")){
                            if(StringUtil.isNotEmpty(ExcelUtil.getCellValue(row.getCell(16)))){
                            	errorMsg += personType+"清单第" + (j - 2) + "行,扣款缴费银行应为空；";
                            }
                            if(StringUtil.isNotEmpty(ExcelUtil.getCellValue(row.getCell(17)))){
                                errorMsg += personType+"清单第" + (j - 2) + "行,扣款缴费账户名应为空；";
                            }
                            if(StringUtil.isNotEmpty(ExcelUtil.getCellValue(row.getCell(18)))){
                            	errorMsg += personType+"清单第" + (j - 2) + "行,扣款缴费银行账号应为空；";
                            }
                        }*/
//                        if (StringUtils.isBlank(ExcelUtil.getCellValue(row.getCell(15)))){
//                            errorMsg += personType+"清单第" + (j - 2) + "行,公司缴费金额不可为空；";
//                        }
//                        if(!isNumber0(ExcelUtil.getCellValue(row.getCell(15)))){
//                        	errorMsg += personType+"清单第" + (j - 2) + "行,公司缴费金额必须大于等于0；";
//                        }
/*                        if (!StringUtils.isBlank(ExcelUtil.getCellValue(row.getCell(15)))){
                        	if(!isNumber0(ExcelUtil.getCellValue(row.getCell(15)))){
                            	errorMsg += personType+"清单第" + (j - 2) + "行,公司缴费金额如果录入，则必须为大于等于0的数字；";
                            }
                        }*/
                        //获取excel表中的数据
                        hashMap.put("serialNo", ExcelUtil.getCellValue(row.getCell(0)));
                        //获取被保险人姓名
                        String name = ExcelUtil.getCellValue(row.getCell(1));
                        hashMap.put("name", name);
                        //************
//                        校验姓名
                        String sz = "";
                        String idTypeName = ExcelUtil.getCellValue(row.getCell(3));
                        if (!StringUtil.isEmpty(idTypeName) && idTypeName.equals("外国公民护照")) {
//                            System.out.println("外国公民护照");
//                            System.out.println(name);
//                             sz = CheckUtils.checkEnglishName(name);
                        } else if (!StringUtil.isEmpty(idTypeName) && !idTypeName.equals("外国公民护照")) {
                            sz = CheckUtils.checkChineseName(name);
                        }
                        if (!StringUtil.isEmpty(sz)) {
                            errorMsg += personType + "清单第" + (j - 2) + "行姓名格式不正确:" + sz;
                        }
                        //************
                        // 校验证件类型代码、职业类别代码、职业代码、银行码表是否录入符合规则
                        errorMsg += checkCodeKey(j - 1, row, idTypeList,
                                occupationTypeList, occupationCodeList,
                                openBankList);
                        // 校验当前职业代码是否符合当前职业类别
                        if (!occupationTypeCodeMap.get(
                                ExcelUtil.getCellValue(row.getCell(10))).contains(
                                ExcelUtil.getCellValue(row.getCell(11)))) {
                            errorMsg += personType + "清单第" + (j - 2) + "行职业类别不包含所录职业；";
                        }
                        // 校验证件类型为身份证时，证件号，性别，出生日期是否录入正确
                        if (ExcelUtil.getCellValue(row.getCell(3)).equals("身份证")) {
                            errorMsg += checkIDCard(j - 2, row);
                        }


                        //----------------校验计划编码是否存在
                        String planCode = ExcelUtil.getCellValue(row.getCell(9));
                        if (!planlist.contains(planCode)) {
                            errorMsg += personType + "清单第" + (j - 2) + "行保单不包含此计划；";
                        }
                       /* FCEdorAddPlanInfo fcEdorAddPlanInfo = fcEdorAddPlanInfoMapper.getPlanInfoByPlanCode(batch,planCode);
                        //planObject  1--员工  2--家属
                        if (fcEdorAddPlanInfo == null){
                            errorMsg += personType+"清单第" + (j - 2) + "行未查询到员工所录计划；";
                        }else if (!"1".equals(fcEdorAddPlanInfo.getPlanObject())){
                            errorMsg += personType+"清单第" + (j - 2) + "行员工所录计划为家属计划，请重新录入；";
                        }*/
                        //校验增员生效日期应在保单的生效日期和截至日期之间
                        String strDate = ExcelUtil.getCellValue(row.getCell(8));
                        if (StringUtils.isNotBlank(strDate)) {
                            if (!(strDate.compareTo(policyeffectDate) >= 0 && policyendDate.compareTo(strDate) > 0)) {
                                errorMsg += personType + "清单第" + (j - 2) + "行增员生效日期应在保单的生效日期和截至日期之间；";
                            } else {
                                String endDate = DateTimeUtil.plusDay(30, DateTimeUtil.getCurrentDate());
                                String beginDate = DateTimeUtil.plusDay(-30, DateTimeUtil.getCurrentDate());
                                if (endDate.compareTo(strDate) < 0) {
                                    errorMsg += personType + "清单第" + (j - 2) + "行，增员生效日期应小于等于当前日期+30天";
                                }
                                if (beginDate.compareTo(strDate) > 0) {
                                    errorMsg += personType + "清单第" + (j - 2) + "行，增员生效日期应大于等于当前日期-30天";
                                }
                            }
                            // 格式转换为yyyy-mm-dd
                            strDate = DateTimeUtil.dateFormat(strDate);
                            if (StringUtils.isBlank(errorMsg)) {
                                //保险期间 add by 2022.3.14
                                Long days = DateTimeUtil.getDistanceDays(strDate, policyendDate);
                                if (days == 364 || days == 365) {
                                    hashMap.put("insuYear", "1");
                                    hashMap.put("insuYearFlag", "Y");
                                } else {
                                    String daysTwo = String.valueOf(days + 1);
                                    hashMap.put("insuYear", daysTwo);
                                    hashMap.put("insuYearFlag", "D");
                                }
                            }
                        } else {
                            errorMsg += personType + "清单第" + (j - 2) + "行，保全生效日期不能为空!";
                        }
                        //校验国籍
                        String nativeplaceKey = "";
                        if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(2)))) {
                            nativeplaceKey = fdCodeMapper.selectKeyByCodeName("nativeplace", ExcelUtil.getCellValue(row.getCell(2)));
                            if (StringUtils.isBlank(nativeplaceKey)) {
                                errorMsg += "员工清单第" + (i + 1) + "行,国籍填写有误!";
                            }
                        }
                        //被保险人国籍
                        hashMap.put("nativeplace", nativeplaceKey);
                        //证件类型码值转换
                        String idTypeExcel = ExcelUtil.getCellValue(row.getCell(3));
                        String idType = "";
                        //性别的公式值转换
                        String sexExcel = ExcelUtil.getCellFormula(row.getCell(6));
                        //性别码值转换
                        String sex = "";
                        Map<String, String> sexByIdTypeMap = getSexByIdType(idType, idTypeExcel, idTypeCodeList, sex, sexExcel, sexCodeList);
                        hashMap.put("idType", sexByIdTypeMap.get("idType"));
                        hashMap.put("sex", sexByIdTypeMap.get("sex"));
                        //获取证件号
                        String idNo = ExcelUtil.getCellValue(row.getCell(4));
                        hashMap.put("idNo", idNo);
                        hashMap.put("idTypeEndDate", ExcelUtil.getCellValue(row.getCell(5)));
                        //出生日期
                        String birthDayExcel = ExcelUtil.getCellFormula(row.getCell(7));
                        hashMap.put("birthDay", birthDayExcel);
                        hashMap.put("plusEffectDate", strDate);
                        hashMap.put("planCode", ExcelUtil.getCellValue(row.getCell(9)));
                        hashMap.put("jobType", ExcelUtil.getCellValue(row.getCell(10)));
                        hashMap.put("jobCode", ExcelUtil.getCellValue(row.getCell(11)));
                        //医保险种没有待定
                        String JoinMedProtect = "";
                        if ("是".equals(ExcelUtil.getCellValue(row.getCell(12)))) {
                            JoinMedProtect = "1";
                        } else {
                            JoinMedProtect = "0";
                        }
                        hashMap.put("medicareStatus", JoinMedProtect);
                        hashMap.put("relationToAppnt", relationship);
                        hashMap.put("mobile", ExcelUtil.getCellValue(row.getCell(13)));
                        //1-代表单位全缴 2-单位代扣 3-混合缴费
                        hashMap.put("payMethod", payMethod);
                        //根据计划保费算出个人保费
                        Double planPrem = planInfo.get(planCode) == null ? 0.00 : planInfo.get(planCode);
                        //Double comPayment = Double.valueOf(StringUtils.isBlank(ExcelUtil.getCellValue(row.getCell(15)))?"0.0":ExcelUtil.getCellValue(row.getCell(15)));
                        Double comPayment = null;
                        Map<String, String> map11 = calculationPrem(planPrem, comPayment, payMethod);
                        if (StringUtils.isNotBlank(map11.get("errmsg"))) {
                            errorMsg += personType + "清单第" + (j - 2) + "行" + map11.get("errmsg") + "；";
                        }
                        //校验人员--add by wudezhong
                        Map<String, String> map = new HashMap<>();
                        map.put("sign", "1");//1：员工 2：家属
                        map.put("idType", hashMap.get("idType"));//证件类型
                        map.put("idNo", hashMap.get("idNo"));//证件号
                        map.put("birthDay", birthDayExcel);//出生日期
                        map.put("sex", hashMap.get("sex"));//性别
                        map.put("nativeplace", nativeplaceKey);//国籍
                        map.put("idTypeEndDate", ExcelUtil.getCellValue(row.getCell(5)));//证件有效期
                        map.put("occupationCode", ExcelUtil.getCellValue(row.getCell(11)));//职业代码
                        map.put("ensurevaliDate", policyeffectDate);
                        String resultMsg = CheckUtils.checkSinglePeople(map);
                        //人员信息
                        checkCustomer.add(EvaluationCustomer.builder()
                                .name(name)
                                .idType(CoreIdType.getNameByCoreId(hashMap.get("idType")).name())
                                .idNo(hashMap.get("idNo"))
                                .gender(GenderType.getGenderByCoreId(hashMap.get("sex")).name())
                                .birthday(birthDayExcel)
                                .nationality(nativeplaceKey)
                                .build());
                        if (StringUtils.isNotBlank(resultMsg)) {
                            errorMsg += personType + "清单第" + (i + 1) + "行 " + name + resultMsg;
                        }
                        // 校验用户的唯一性
                        if (isEflexGrpOrder) {
                            FCOrderInsured fcOrderInsured = new FCOrderInsured();
                            fcOrderInsured.setGrpOrderNo(fcGrpOrder.getGrpOrderNo());
                            fcOrderInsured.setIDNo(idNo);
                            int insuredCount = fcOrderInsuredMapper.selectOrderInsured(fcOrderInsured);
                            if (insuredCount > 0) {
                                errorMsg += personType + "清单第" + (i + 1) + "行用户已存在。";
                            }
                        }
                        FCEdorAddInsured fcEdorAddInsured = new FCEdorAddInsured();
                        fcEdorAddInsured.setGrpContNo(grpContNo);
                        fcEdorAddInsured.setIdNo(idNo);
                        List<FCEdorAddInsured> fcEdorAddInsureds = fcEdorAddInsuredMapper
                                .selectEdorAddInsured(fcEdorAddInsured);
                        if (fcEdorAddInsureds.size() > 0) {
                            errorMsg += personType + "清单第" + (i + 1) + "行用户已存在。";
                        }
                        hashMap.put("comPayment", map11.get("comPayment"));
                        hashMap.put("perPayment", map11.get("perPayment"));
                        hashMap.put("relationship", relationship);

                        //核心暂不支持企业代缴 混合缴费  相关缴费信息字段落地暂时关闭
                        /*hashMap.put("debitPayBank", ExcelUtil.getCellValue(row.getCell(16)));
                        hashMap.put("debitPayName", ExcelUtil.getCellValue(row.getCell(17)));
                        hashMap.put("debitPayCode", ExcelUtil.getCellValue(row.getCell(18)));*/
                        hashMap.put("subsidiaryInsuredFlag", "1");
                        mapList.add(hashMap);
                        idNoList.add(idNo);
                        staffhashMap.put("idType", hashMap.get("idType"));
                        staffhashMap.put("idno", idNo);
                        staffhashMap.put("name", name);
                        staffhashMap.put("sex", hashMap.get("sex"));
                        staffMap.put(idNo, staffhashMap);
                    }
                }
            }
            //被保险人--家属
            if (i == 1) {
                for (int j = 3; j <= sheet.getLastRowNum(); j++) {
                    Map<String, String> hashMap = new HashMap<>();
                    row = sheet.getRow(j);
                    if (ExcelUtil.isRowEmpty(row, 4)) {
                        log.info(personType + "清单第" + (j - 2) + "行是无效数据。");
                    } else {
                        count++;
                        //校验所录数据是否为空
                        errorMsg += checkFamilyIsEmpty(j - 2, row, token, i);
                        if (!errorMsg.equals("")) {
                            break;
                        }
                        if (!checkCode_Vlue("Relation", null, ExcelUtil.getCellValue(row.getCell(8)))) {
                            errorMsg += personType + "清单第" + (j - 2) + "行,与主被保险人关系不存在!";
                        }
                        ;
                        if (!checkScientifiNotation(ExcelUtil.getCellValue(row.getCell(17))) ||
                                !CheckUtils.checkMobilePhone(ExcelUtil.getCellValue(row.getCell(17)))) {
                            errorMsg += personType + "清单第" + (j - 2) + "行,手机号填写格式有误!";
                        }
                        if (!checkScientifiNotation(ExcelUtil.getCellValue(row.getCell(11)))) {
                            errorMsg += personType + "清单第" + (j - 2) + "行,主被保险人证件号填写格式有误!";
                        }
                        if (!checkScientifiNotation(ExcelUtil.getCellValue(row.getCell(4)))) {
                            errorMsg += personType + "清单第" + (j - 2) + "行,证件号填写格式有误!";
                        }
                        String payMethod = "1";
                        if (!Arrays.asList(new String[]{"1", "2", "3"}).contains(payMethod)) {
                            errorMsg += personType + "清单第" + (j - 2) + "行,缴费方式不存在！";
                        }


                        String nameStaff = ExcelUtil.getCellValue(row.getCell(9));
                        String nameinsured = ExcelUtil.getCellValue(row.getCell(1));
                        String idTypeinsured = ExcelUtil.getCellValue(row.getCell(3));
                        String idTypeStaff = ExcelUtil.getCellValue(row.getCell(10));
                        String NameStafferr = "";
                        String Nameinsurederr = "";
                        if (!StringUtil.isEmpty(idTypeStaff) && !StringUtil.isEmpty(nameStaff) && !StringUtil.isEmpty(nameinsured) && !StringUtil.isEmpty(idTypeinsured)) {
                            if (idTypeStaff.equals("外国公民护照")) {
                            } else {
                                NameStafferr = CheckUtils.checkChineseName(nameStaff);
                            }
                            if (idTypeinsured.equals("外国公民护照")) {
                            } else {
                                Nameinsurederr = CheckUtils.checkChineseName(nameinsured);
                            }
                            if (!StringUtil.isEmpty(NameStafferr)) {
                                errorMsg += personType + "清单第" + (j - 2) + "行被保险人姓名格式有误：" + NameStafferr;
                            }
                            if (!StringUtil.isEmpty(Nameinsurederr)) {
                                errorMsg += personType + "清单第" + (j - 2) + "行被保险人姓名格式有误：" + Nameinsurederr;
                            }
                        }

                        //获取excel表中的数据
                        hashMap.put("serialNo", ExcelUtil.getCellValue(row.getCell(0)));
                        //主被保险人姓名
                        hashMap.put("staffName", ExcelUtil.getCellValue(row.getCell(9)));
                        //获取被保险人姓名
                        hashMap.put("name", ExcelUtil.getCellValue(row.getCell(1)));
                        //与主被保险人关系
                        hashMap.put("relation", ExcelUtil.getCellValue(row.getCell(8)));
                        //与投保人关系
                        hashMap.put("relationToAppnt", "06");
                        // 校验证件类型代码、职业类别代码、职业代码、银行码表是否录入符合规则
                        errorMsg += checkFamilyCodeKey(j - 1, row, idTypeList,
                                occupationTypeList, occupationCodeList,
                                openBankList);
                        // 校验当前职业代码是否符合当前职业类别
                        if (!occupationTypeCodeMap.get(
                                ExcelUtil.getCellValue(row.getCell(14))).contains(
                                ExcelUtil.getCellValue(row.getCell(15)))) {
                            errorMsg += personType + "清单第" + (j - 2) + "行职业类别不包含所录职业；";
                        }
                        // 校验被保人证件类型为身份证时，证件号，性别，出生日期是否录入正确
                        if (ExcelUtil.getCellValue(row.getCell(3)).equals("身份证")) {
                            errorMsg += checkFamilyIDCard(j - 2, row);
                        }
                        //校验计划编码是否存在
                        String planCode = ExcelUtil.getCellValue(row.getCell(13));
                        if (!planlist.contains(planCode)) {
                            errorMsg += personType + "清单第" + (j - 2) + "行保单不包含此计划；";
                        }
                        //校验增员生效日期应在保单的生效日期和截至日期之间
                        String strDate = ExcelUtil.getCellValue(row.getCell(12));
                        if (StringUtils.isNotBlank(strDate)) {
                            if (!(strDate.compareTo(policyeffectDate) >= 0 && policyendDate.compareTo(strDate) > 0)) {
                                errorMsg += personType + "清单第" + (j - 2) + "行增员生效日期应在保单的生效日期和截至日期之间；";
                            } else {
                                String endDate = DateTimeUtil.plusDay(30, DateTimeUtil.getCurrentDate());
                                String beginDate = DateTimeUtil.plusDay(-30, DateTimeUtil.getCurrentDate());
                                if (endDate.compareTo(strDate) < 0) {
                                    errorMsg += personType + "清单第" + (j - 2) + "行，增员生效日期应小于等于当前日期+30天";
                                }
                                if (beginDate.compareTo(strDate) > 0) {
                                    errorMsg += personType + "清单第" + (j - 2) + "行，增员生效日期应大于等于当前日期-30天";
                                }
                            }
                            // 格式转换为yyyy-mm-dd
                            strDate = DateTimeUtil.dateFormat(strDate);
                            if (StringUtils.isBlank(errorMsg)) {
                                //保险期间 add by 2022.3.14
                                Long days = DateTimeUtil.getDistanceDays(strDate, policyendDate);
                                if (days == 364 || days == 365) {
                                    hashMap.put("insuYear", "1");
                                    hashMap.put("insuYearFlag", "Y");
                                } else {
                                    String daysTwo = String.valueOf(days + 1);
                                    hashMap.put("insuYear", daysTwo);
                                    hashMap.put("insuYearFlag", "D");
                                }
                            }
                        }
                        //校验国籍
                        String nativeplaceKey = "";
                        if (StringUtils.isNotBlank(ExcelUtil.getCellValue(row.getCell(2)))) {
                            nativeplaceKey = fdCodeMapper.selectKeyByCodeName("nativeplace", ExcelUtil.getCellValue(row.getCell(2)));
                            if (StringUtils.isBlank(nativeplaceKey)) {
                                errorMsg += "员工清单第" + (i + 1) + "行,国籍填写有误!；";
                            }
                        }
                        //被保险人国籍
                        hashMap.put("nativeplace", nativeplaceKey);
                        //证件类型码值转换
                        String idTypeExcel = ExcelUtil.getCellValue(row.getCell(3));
                        String idType = "";
                        //性别码值转换
                        String sexExcel = ExcelUtil.getCellFormula(row.getCell(6));
                        String sex = "";
                        //转码方法
                        Map<String, String> sexByIdTypeMap = getSexByIdType(idType, idTypeExcel, idTypeCodeList, sex, sexExcel, sexCodeList);
                        hashMap.put("idType", sexByIdTypeMap.get("idType"));
                        hashMap.put("sex", sexByIdTypeMap.get("sex"));
                        //主被保人证件类型码值转换
                        String mainIdTypeExcel = ExcelUtil.getCellValue(row.getCell(10));
                        String mainIdType = "";
                        for (HashMap<String, Object> hashMap1 : idTypeCodeList) {
                            if (mainIdTypeExcel.equals(hashMap1.get("CodeName").toString())) {
                                mainIdType = hashMap1.get("CodeKey").toString();
                                break;
                            }
                        }
                        hashMap.put("mainIdType", mainIdType);
                        hashMap.put("mainIdNo", ExcelUtil.getCellValue(row.getCell(11)));
                        //获取被保人证件号
                        String idNo = ExcelUtil.getCellValue(row.getCell(4));
                        hashMap.put("idNo", idNo);//性别的公式值转换
                        hashMap.put("idTypeEndDate", ExcelUtil.getCellValue(row.getCell(5)));
                        //出生日期
                        String birthDayExcel = ExcelUtil.getCellFormula(row.getCell(7));
                        hashMap.put("birthDay", birthDayExcel);
                        hashMap.put("plusEffectDate", strDate);
                        hashMap.put("jobType", ExcelUtil.getCellValue(row.getCell(14)));
                        hashMap.put("jobCode", ExcelUtil.getCellValue(row.getCell(15)));
                        //医保险种没有待定
                        String JoinMedProtect = "";
                        if ("是".equals(ExcelUtil.getCellValue(row.getCell(16)))) {
                            JoinMedProtect = "1";
                        } else {
                            JoinMedProtect = "0";
                        }
                        hashMap.put("medicareStatus", JoinMedProtect);
                        hashMap.put("mobile", ExcelUtil.getCellValue(row.getCell(17)));
                        hashMap.put("planCode", ExcelUtil.getCellValue(row.getCell(13)));
                        //1-代表单位全缴 2-单位代扣 3-混合缴费
                        hashMap.put("payMethod", payMethod);
                        //根据计划保费算出个人保费
                        Double planPrem = planInfo.get(planCode) == null ? 0.00 : planInfo.get(planCode);
                        //Double comPayment = Double.valueOf(StringUtils.isBlank(ExcelUtil.getCellValue(row.getCell(19)))?"0.0":ExcelUtil.getCellValue(row.getCell(19)));
                        Double comPayment = null;
                        Map<String, String> map11 = calculationPrem(planPrem, comPayment, payMethod);
                        if (StringUtils.isNotBlank(map11.get("errmsg"))) {
                            errorMsg += personType + "清单第" + (j - 2) + "行" + map11.get("errmsg") + "；";
                        }
                        //校验人员--add by wudezhong
                        Map<String, String> map = new HashMap<>();
                        map.put("sign", "2");//1：员工 2：家属
                        map.put("idType", hashMap.get("idType"));//证件类型
                        map.put("idNo", hashMap.get("idNo"));//证件号
                        map.put("idTypeEndDate", hashMap.get("idTypeEndDate"));//证件号
                        map.put("birthDay", birthDayExcel);//出生日期
                        map.put("sex", hashMap.get("sex"));//性别
                        map.put("nativeplace", nativeplaceKey);//国籍
                        map.put("idTypeEndDate", ExcelUtil.getCellValue(row.getCell(5)));//证件有效期
                        map.put("occupationCode", ExcelUtil.getCellValue(row.getCell(15)));//职业代码
                        //福利生效日期 map.put("ensurevaliDate", fcEnsure.getCvaliDate());
                        String resultMsg = CheckUtils.checkSinglePeople(map);
                        checkCustomer.add(EvaluationCustomer.builder()
                                .name(ExcelUtil.getCellValue(row.getCell(1)))
                                .idType(CoreIdType.getNameByCoreId(hashMap.get("idType")).name())
                                .idNo(hashMap.get("idNo"))
                                .gender(GenderType.getGenderByCoreId(hashMap.get("sex")).name())
                                .birthday(birthDayExcel)
                                .nationality(nativeplaceKey)
                                .build());
                        if (StringUtils.isNotBlank(resultMsg)) {
                            errorMsg += personType + "清单第" + (i + 1) + "行 " + hashMap.get("name") + resultMsg + "/";
                        }
                        hashMap.put("comPayment", map11.get("comPayment"));
                        hashMap.put("perPayment", map11.get("perPayment"));
                        hashMap.put("subsidiaryInsuredFlag", "1");
                        mapList.add(hashMap);
                        idNoList.add(idNo);
                    }
                }
            }
            // 判断模板中是否存在相同的证件号员工以及家属
            HashSet<String> set = new HashSet<>(idNoList);
            if (idNoList.size() != set.size()) {
                // 获得list与set的差集
                Collection rs = CollectionUtils.disjunction(idNoList, set);
                // 将collection转换为list
                List<String> list1 = new ArrayList<>(rs);
                for (String str : list1) {
                    String serialNo = "";
                    for (Map<String, String> hashMap : mapList) {
                        if (hashMap.get("idNo").equals(str)) {
                            serialNo += Double.valueOf(hashMap.get("serialNo")).intValue() + ",";
                        }
                    }
                    errorMsg += "第" + serialNo.substring(0, serialNo.length() - 1) + "行证件号重复；";
                }
            }
            List<IdCardVerifyRequest.Verify> verifies = BeanCopier.copyList(checkCustomer, IdCardVerifyRequest.Verify.class);
            //校验二要素
            String failVerifies = addressCheckService.checkIdCard(verifies);
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(failVerifies)) {
                errorMsg += failVerifies;
            }
            CheckCustomerVO checkLevelVO = addressCheckService.checkCustomerLevel(InterfaceType.NATURAL_PERSONS.name(), checkCustomer);
            String privateRiskLevel = checkLevelVO.getPrivateRiskLevel();
            String publicRiskLevel = checkLevelVO.getPublicRiskLevel();
            if (StringUtils.isNotEmpty(privateRiskLevel)) {
                errorMsg += "尊敬的客户，您本次提交的保险业务申请，因客户" + privateRiskLevel + "的身份信息需要进一步确认，暂不接受线上申请！如有疑问，可联系您的横琴人寿对接人员或客服热线400-69-12345咨询。";
            }
            if (StringUtils.isNotEmpty(publicRiskLevel)) {
                errorMsg += "尊敬的客户，您本次提交的保险业务申请，因客户" + publicRiskLevel + "的身份信息需要进一步确认，暂不接受线上申请！如有疑问，可联系您的横琴人寿对接人员或客服热线400-69-12345咨询。";
            }

            //新增人员
            for (Map<String, String> map : mapList) {
                if (i == 1) {
                    //校验证件号
                    Map<String, String> staffmap = staffMap.get(map.get("mainIdNo"));
                    if (staffmap == null || staffmap.isEmpty()) {
                        map.put("subsidiaryInsuredFlag", "3");
                    } else {
                        map.put("subsidiaryInsuredFlag", "2");
                        //主被保险人姓名
                        if (!staffmap.get("name").equals(map.get("staffName"))) {
                            errorMsg += "被保险人" + map.get("name") + "所属主被保险人姓名不正确！";
                        }
                        //主被保人证件类型
                        if (!staffmap.get("idType").equals(map.get("mainIdType"))) {
                            errorMsg += "被保险人" + map.get("name") + "所属主被保险人证件类型不正确！";
                        }
                        ;
                    }
                }
                FCEdorAddInsured fcEdorAddInsured = new FCEdorAddInsured();
                String plusInsuredSN = maxNoService.createMaxNo("PlusInsuredSN", null, 20);
                //最大号
                fcEdorAddInsured.setPlusInsuredSN(plusInsuredSN);
                //企业编号
                fcEdorAddInsured.setGrpNo(grpNo);
                //待定
                fcEdorAddInsured.setBatch(batch);
                //被保险人姓名
                fcEdorAddInsured.setName(map.get("name"));
                //被保险人国籍
                fcEdorAddInsured.setNativeplace(map.get("nativeplace"));
                //出生日期
                fcEdorAddInsured.setBirthday(map.get("birthDay"));
                //证件类型
                fcEdorAddInsured.setIdType(map.get("idType"));
                //证件有效期
                if (StringUtils.isNotBlank(map.get("idTypeEndDate"))) {
                    fcEdorAddInsured.setIdTypeEndDate(map.get("idTypeEndDate"));
                }
                //证件号
                fcEdorAddInsured.setIdNo(map.get("idNo"));
                //性别
                fcEdorAddInsured.setSex(map.get("sex"));
                //增员生效日期
                fcEdorAddInsured.setPlusEffectDate(map.get("plusEffectDate"));
                //计划编码
                fcEdorAddInsured.setPlanCode(map.get("planCode"));
                //职业代码
                fcEdorAddInsured.setJobCode(map.get("jobCode"));
                //职业类型
                fcEdorAddInsured.setJobType(map.get("jobType"));
                //是否有医保
                fcEdorAddInsured.setMedicareStatus(map.get("medicareStatus"));
                //手机号
                fcEdorAddInsured.setMobile(map.get("mobile"));
                //缴费方式
                fcEdorAddInsured.setPayMethod(map.get("payMethod"));
                //企业缴费
                String comPayment = map.get("comPayment");
                if (!"".equals(comPayment)) {
                    double ComPayment = Double.parseDouble(comPayment);
                    fcEdorAddInsured.setComPayment(ComPayment);
                }
                //个人缴费
                String perPayment = map.get("perPayment");
                if (!"".equals(perPayment)) {
                    double PerPayment = Double.parseDouble(perPayment);
                    fcEdorAddInsured.setPerPayment(PerPayment);
                }
                //核心不支持 暂时关闭
                /*fcEdorInsured.setDebitPayBank(map.get("debitPayBank"));
                fcEdorInsured.setDebitPayCode(map.get("debitPayCode"));
                fcEdorInsured.setDebitPayName(map.get("debitPayName"));*/
                //受益人
                fcEdorAddInsured.setDeathBenefiName(map.get("deathBenefiName"));
                //受益人与被保人关系
                fcEdorAddInsured.setDeathBenefiRelation(map.get("deathBenefiRelation"));
                if (i == 0) {
                    fcEdorAddInsured.setRelation(RelationMarkEnum.MYSELF.getCode());
                    //与投保人关系
                    fcEdorAddInsured.setRelationToAppnt(map.get("relationToAppnt"));
                }
                if (i == 1) {
                    //主被保人证件类型
                    fcEdorAddInsured.setMainIdType(map.get("mainIdType"));
                    //主被保险人证件号
                    fcEdorAddInsured.setMainIdNo(map.get("mainIdNo"));
                    // 员工关系，试算后再进行封装
                    String Realtion = map.get("relation");
                    Map<String, String> relationMap = new HashMap<>();
                    relationMap.put("codeType", "Relation");
                    relationMap.put("codeName", Realtion);
                    FDCode fdCode = fdCodeMapper.selectByCodeName(relationMap);
                    fcEdorAddInsured.setRelation(fdCode.getCodeKey());
                    //员工姓名
                    fcEdorAddInsured.setStaffName(map.get("staffName"));
                    //与投保人关系
                    fcEdorAddInsured.setRelationToAppnt("06");
                }
                //备注
                fcEdorAddInsured.setRemarks(map.get("remarks"));
                //核心人员身份
                fcEdorAddInsured.setSubsidiaryInsuredFlag(map.get("subsidiaryInsuredFlag"));
                //试算状态0-未提交1-已提交2-申请完成
                fcEdorAddInsured.setTrialStatus(TrialStateEnum.INSUREDPREPARE.getCode());
                //团体保单号
                fcEdorAddInsured.setGrpContNo(grpContNo);
                //保全类型
                fcEdorAddInsured.setEdorType("NT");
                //保险期间 add by 2022.3.14
                fcEdorAddInsured.setInsuYear(map.get("insuYear"));
                fcEdorAddInsured.setInsuYearFlag(map.get("insuYearFlag"));
                fcEdorAddInsured.setOperator(globalInput.getUserNo());
                fcEdorAddInsured = CommonUtil.initObject(fcEdorAddInsured, "INSERT");
                listMap.add(fcEdorAddInsured);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info(errorMsg);
        } finally {
            if (!errorMsg.equals("")) {
                resultMap.put("success", false);
                resultMap.put("code", "500");
                resultMap.put("message", errorMsg);
                return resultMap;
            }
        }
        return resultMap;
    }

    //获取职业类型和职业代码
    public Map<String, List<String>> getOccupationTypeCode() {
        Map<String, List<String>> occupationMap = new HashMap<>();
        List<Map<String, String>> list = fcPerInfoTempMapper.selectOccupationList("01");
        if (list != null && list.size() > 0) {
            for (Map typeMap : list) {
                List<String> codeList = new ArrayList<String>();
                String codeKey = typeMap.get("occupationType").toString().trim();
                List<Map<String, String>> codelist = fcPerInfoTempMapper
                        .selectOccupationCode(codeKey);
                if (codelist != null && codelist.size() > 0) {
                    for (int i = 0; i < codelist.size(); i++) {
                        codeList.add(codelist.get(i).get("occupationCode")
                                .toString().trim());
                    }
                }
                occupationMap.put(typeMap.get("occupationType").toString().trim(),
                        codeList);
            }
        }
        return occupationMap;
    }

    //校验员工被保险人证件号
    public String checkIDCard(int i, Row row) {
        DateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            String idno = ExcelUtil.getCellValue(row.getCell(4));
            String sex = ExcelUtil.getCellFormula(row.getCell(6));
            String birthday = ExcelUtil.getCellFormula(row.getCell(7));
            if (!IDCardUtil.isIDCard(idno)) {
                return "第" + i + "行身份证号格式错误：" + idno;
            } else if (idno.length() != 18) {
                // 经由核心、运营确认，身份证号仅能为18位 add by wudezhong 2021.5.27
                return "第" + i + "行证件号码长度应为18位！";
            } else {
                date = format1.parse(birthday);
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                String dateString = formatter.format(date);
                String idBir = IDCardUtil.dateOfBirth(idno);
                if (!dateString.equals(idBir)) {
                    return "第" + i + "行出生日期与身份证不符";
                }
                if (!getGenderByIdCard(idno).equals(sex)) {
                    return "第" + i + "行性别与身份证不符";
                }
            }
        } catch (ParseException e) {
            return "第" + i + "行数据异常";
        }
        return "";
    }

    //校验家属被保险人证件号
    public String checkFamilyIDCard(int i, Row row) {
        DateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            String idno = ExcelUtil.getCellValue(row.getCell(4));
            String sex = ExcelUtil.getCellFormula(row.getCell(6));
            String birthday = ExcelUtil.getCellFormula(row.getCell(7));
            if (!IDCardUtil.isIDCard(idno)) {
                return "第" + i + "行身份证号格式错误：" + idno;
            }
            // 经由核心、运营确认，身份证号仅能为18位 add by wudezhong 2021.5.27
            if (idno.length() != 18) {
                return "第" + i + "行证件号码长度应为18位！";
            }
            date = format1.parse(birthday);
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            String dateString = formatter.format(date);
            String idBir = IDCardUtil.dateOfBirth(idno);
            if (!dateString.equals(idBir)) {
                return "第" + i + "行出生日期与身份证不符";
            }
            if (!getGenderByIdCard(idno).equals(sex)) {
                return "第" + i + "行性别与身份证不符";
            }
        } catch (ParseException e) {
            return "第" + i + "行数据异常";
        }
        return "";
    }

    //科学记数法
    public boolean checkScientifiNotation(String str) {
        if (str.contains("E") || str.contains(".")) {
            return false;
        }
        return true;
    }

    //校验性别及证件号公式
    public static String getGenderByIdCard(String idCard) {
        String sGender = "未知";
        String sCardNum = IDCardUtil.sex(idCard);
        if (Integer.parseInt(sCardNum) == 0) {
            sGender = "男";
        } else {
            sGender = "女";
        }
        return sGender;
    }

    //证件类型和性别转码
    public static Map<String, String> getSexByIdType(String idType, String idTypeExcel, List<HashMap<String, Object>> idTypeCodeList,
                                                     String sex, String sexExcel, List<HashMap<String, Object>> sexCodeList) {
        Map<String, String> map = new HashMap<>();
        for (HashMap<String, Object> hashMap1 : idTypeCodeList) {
            if (idTypeExcel.equals(hashMap1.get("CodeName").toString())) {
                idType = hashMap1.get("CodeKey").toString();
                break;
            }
        }
        map.put("idType", idType);
        for (HashMap<String, Object> hashMap2 : sexCodeList) {
            if (sexExcel.equals(hashMap2.get("CodeName").toString())) {
                sex = hashMap2.get("CodeKey").toString();
                break;
            }
        }
        map.put("sex", sex);
        return map;
    }

    //校验员工所录数据是否为空
    public String checkIsEmpty(int i, Row row, String token) {
        String errMsg = "";
        GlobalInput globalInput = userService.getSession(token);
        String grpNo = globalInput.getGrpNo();
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(1)))) {
            errMsg += "第" + i + "行被保险人姓名不能为空；";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(2)))) {
            errMsg += "第" + i + "行被保险人国籍不能为空；";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(3)))) {
            errMsg += "第" + i + "行被保险人证件类型不能为空；";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(4)))) {
            errMsg += "第" + i + "行被保险人证件号码不能为空；";
        }
        // if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(5)))) {
        // errMsg += "第" + i + "行被保险人证件有效期不能为空；";
        // }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(6)))) {
            errMsg += "第" + i + "行被保险人性别不能为空；";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(7)))) {
            errMsg += "第" + i + "行被保险人出生日期不能为空；";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(9)))) {
            errMsg += "第" + i + "行保障计划编码不能为空；";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(10)))) {
            errMsg += "第" + i + "行职业类别不能为空；";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(11)))) {
            errMsg += "第" + i + "行职业代码不能为空；";
        }
        //缺少投保医疗保险或门急诊医疗保险时（含意外医疗保险），“有无医保”为必填项校验
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(12)))) {
            errMsg += "第" + i + "行有无医保不能为空；";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(13)))) {
            errMsg += "第" + i + "行手机号不能为空；";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(14)))) {
            errMsg += "第" + i + "行与投保人关系不能为空；";
        }
/*        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(14)))){
        	errMsg +=  "第"+i+"行缴费方式不能为空；";
        }*//*else{
        	if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(15)))){
        		errMsg +=  "第"+i+"行公司缴费金额不能为空；";
            }
            String payMethod = fdCodeMapper.selectKeyByCodeName("PayPremType",ExcelUtil.getCellValue(row.getCell(14)));
            if(payMethod.equals("3")){
                if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(16)))){
                	errMsg +=  "第"+i+"行扣款缴费银行不能为空；";
                }
                if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(17)))){
                	errMsg +=  "第"+i+"行扣款缴费账户名不能为空；";
                }
                if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(18)))){
                	errMsg +=  "第"+i+"行扣款缴费银行账号不能为空；";
                }
            }
        }*/
        if (!errMsg.equals("")) {
            errMsg = "员工清单中：" + errMsg;
        }
        return errMsg;
    }

    //校验员工证件类型、职业类别、职业编码、银行码表规则
    public String checkCodeKey(int i, Row row, List<String> idTypeList,
                               List<String> occupationTypeList, List<String> occupationCodeList,
                               List<String> openBankList) {
        String idType = ExcelUtil.getCellValue(row.getCell(3));
        String occupationType = ExcelUtil.getCellValue(row.getCell(10));
        String occupationCode = ExcelUtil.getCellValue(row.getCell(11));
        String openBank = ExcelUtil.getCellValue(row.getCell(16));
        if (!"".equals(openBank)) {
            if (!openBankList.contains(openBank)) {
                return "员工清单第" + i + "行扣款缴费银行录入错误";
            }
        }
        if (!idTypeList.contains(idType)) {
            return "员工清单第" + i + "行证件类型录入错误";
        }
        if (!occupationTypeList.contains(occupationType)) {
            return "员工清单第" + i + "行职业类别录入错误";
        }
        if (!occupationCodeList.contains(occupationCode)) {
            return "员工清单第" + i + "行职业代码录入错误";
        }

        return "";
    }

    //校验家属录入数据是否为空
    public String checkFamilyIsEmpty(int i, Row row, String token, int j) {
        String errMeg = "";
        GlobalInput globalInput = userService.getSession(token);
        String grpNo = globalInput.getGrpNo();
        //获取企业信息
        FCGrpInfo fcGrpInfo = fcGrpInfoMapper.selectByPrimaryKey(grpNo);
        String grpType = fcGrpInfo.getGrpType();
        if ("".equals(ExcelUtil.getCellValue(row.getCell(1)))) {
            errMeg += "第" + i + "行被保险人姓名不能为空；";
        }
        if ("".equals(ExcelUtil.getCellValue(row.getCell(2)))) {
            errMeg += "第" + i + "行被保险人国籍不能为空；";
        }
        if ("".equals(ExcelUtil.getCellValue(row.getCell(3)))) {
            errMeg += "第" + i + "行被保险人证件类型不能为空；";
        }
        if ("".equals(ExcelUtil.getCellValue(row.getCell(4)))) {
            errMeg += "第" + i + "行被保险人证件号不能为空；";
        }
        if ("".equals(ExcelUtil.getCellFormula(row.getCell(6)))) {
            errMeg += "第" + i + "行被保险人性别不能为空；";
        }
        if ("".equals(ExcelUtil.getCellFormula(row.getCell(7)))) {
            errMeg += "第" + i + "行被保险人出生日期不能为空；";
        }
        if ("".equals(ExcelUtil.getCellValue(row.getCell(8)))) {
            errMeg += "第" + i + "行与主被保险人关系不能为空；";
        }
        if ("".equals(ExcelUtil.getCellValue(row.getCell(9)))) {
            errMeg += "第" + i + "行主被保险人姓名不能为空；";
        }
        if ("".equals(ExcelUtil.getCellValue(row.getCell(10)))) {
            errMeg += "第" + i + "行主被保险人证件类型不能为空；";
        }
        if ("".equals(ExcelUtil.getCellValue(row.getCell(11)))) {
            errMeg += "第" + i + "行主被保险人证件号不能为空；";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(13)))) {
            errMeg += "第" + i + "行保障计划编码不能为空；";
        }
        if ("".equals(ExcelUtil.getCellValue(row.getCell(14)))) {
            errMeg += "第" + i + "行职业类型不能为空；";
        }
        if ("".equals(ExcelUtil.getCellValue(row.getCell(15)))) {
            errMeg += "第" + i + "行职业代码不能为空；";
        }
        //缺少投保医疗保险或门急诊医疗保险时（含意外医疗保险），“有无医保”为必填项校验
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(16)))) {
            errMeg += "第" + i + "行有无医保不能为空；";
        }
        if (StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(17)))) {
            errMeg += "第" + i + "行手机号不能为空；";
        }
/*        if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(18)))){
        	errMeg +=  "第"+i+"行缴费方式不能为空；";
        }*//*else{
        	if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(19)))){
        		errMeg +=  "第"+i+"行公司缴费金额不能为空；";
        	}
            String payMethod = fdCodeMapper.selectKeyByCodeName("PayPremType",ExcelUtil.getCellValue(row.getCell(18)));
            if(payMethod.equals("3")){
                if(payMethod.equals("3")){
                    if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(20)))){
                    	errMeg +=  "第"+i+"行扣款缴费银行不能为空；";
                    }
                    if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(21)))){
                    	errMeg +=  "第"+i+"行扣款缴费账户名不能为空；";
                    }
                    if(StringUtil.isEmpty(ExcelUtil.getCellValue(row.getCell(22)))){
                    	errMeg +=  "第"+i+"行扣款缴费银行账号不能为空；";
                    }
                }
            }
        }*/
        if (!errMeg.equals("")) {
            errMeg = "家属清单中：" + errMeg;
        }
        return errMeg;
    }


    //校验家属证件类型、职业类别、职业编码、银行码表规则
    public String checkFamilyCodeKey(int i, Row row, List<String> idTypeList,
                                     List<String> occupationTypeList, List<String> occupationCodeList,
                                     List<String> openBankList) {
        String mainIdType = ExcelUtil.getCellValue(row.getCell(10));
        String idType = ExcelUtil.getCellValue(row.getCell(3));
        String occupationType = ExcelUtil.getCellValue(row.getCell(14));
        String occupationCode = ExcelUtil.getCellValue(row.getCell(15));
        String openBank = ExcelUtil.getCellValue(row.getCell(20));
        if (!idTypeList.contains(mainIdType)) {
            return "家属清单第" + i + "行主被保险人证件类型录入错误";
        }
        if (!idTypeList.contains(idType)) {
            return "家属清单第" + i + "行证件类型录入错误";
        }
        if (!occupationTypeList.contains(occupationType)) {
            return "家属清单第" + i + "行职业类别录入错误";
        }
        if (!occupationCodeList.contains(occupationCode)) {
            return "家属清单第" + i + "行职业代码录入错误";
        }
        if (!"".equals(openBank)) {
            if (!openBankList.contains(openBank)) {
                return "家属清单第" + i + "行扣款缴费银行录入错误";
            }
        }
        return "";
    }

    // 校验金额为数字或者小数点后两位 且 大于0
    public static boolean isNumber(String str) {
        // 判断小数点后2位的数字的正则表达式
        Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$");
        Matcher match = pattern.matcher(str);
        if (match.matches() == false) {
            return false;
        } else if (Double.valueOf(str) > 0) {
            return true;
        }
        return false;
    }

    //计算保费
    public static Map<String, String> calculationPrem(Double planPrem, Double comPayment, String payType) {
        //暂时设置为单位全交
        Map<String, String> map = new HashMap<>();
        String errmsg = "";
        Double perPayment = 0.0;
//        if(payType.equals("2") || payType.equals("3")) {
//        	if(planPrem >= comPayment) {
//        		perPayment = planPrem - comPayment;
//        	}else {
//        		comPayment = planPrem;
//        	}
//        }else if(payType.equals("1")) {
//        	if(planPrem > comPayment) {
//        		errmsg = "企业缴费应大于等于计划保费";
//        	}else {
//        		comPayment = planPrem;
//        	}
//        }
        map.put("errmsg", errmsg);
//        map.put("comPayment",String.valueOf(comPayment));
//        map.put("perPayment",String.valueOf(perPayment));
        map.put("comPayment", String.valueOf(planPrem));
        map.put("perPayment", String.valueOf(0.0));
        return map;
    }

    // 校验金额为数字或者小数点后两位 且 大等于0
    public boolean isNumber0(String str) {
        // 判断小数点后2位的数字的正则表达式
        Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$");
        Matcher match = pattern.matcher(str);
        if (match.matches() == false) {
            return false;
        } else if (Double.valueOf(str) >= 0) {
            return true;
        }
        return false;
    }

    //判断码值是否正确
    public Boolean checkCode_Vlue(String codeType, String code, String value) {
        if (StringUtils.isBlank(codeType) || (StringUtils.isBlank(code) && StringUtils.isBlank(value))) {
            return false;
        } else {
            if (StringUtils.isNotBlank(code)) {
                if (StringUtils.isBlank(fdCodeMapper.selectNameByCode(codeType, code))) {
                    return false;
                }
            } else if (StringUtils.isNotBlank(value)) {
                if (StringUtils.isBlank(fdCodeMapper.selectKeyByCodeName(codeType, value))) {
                    return false;
                }
            }
        }
        return true;
    }
}
