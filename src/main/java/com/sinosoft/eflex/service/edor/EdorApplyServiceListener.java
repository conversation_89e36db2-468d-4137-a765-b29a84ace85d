package com.sinosoft.eflex.service.edor;

import com.sinosoft.eflex.framework.aop.EdorTypeHandler;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021/9/14
 * @desc 保全申请类监听，装配枚举类与处理类的映射对象 1、观察者设计模式的体现。
 * 2、当所有的bean都初始化完成并被成功装载后会触发该事件，从而完成调用前的数据装配。
 */
@Component
public class EdorApplyServiceListener implements ApplicationListener<ContextRefreshedEvent> {

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        Map<String, Object> beans = event.getApplicationContext().getBeansWithAnnotation(EdorTypeHandler.class);
        EdorApplyServiceContext edorApplyServiceContext = event.getApplicationContext()
                .getBean(EdorApplyServiceContext.class);
        beans.forEach((name, bean) -> {
            EdorTypeHandler typeHandler = bean.getClass().getAnnotation(EdorTypeHandler.class);
            edorApplyServiceContext.putEdorApplyService(typeHandler.value().code, (EdorApplyService) bean);
        });
    }

}
