package com.sinosoft.eflex.service.edor;

import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.dao.FCEdorACInfoMapper;
import com.sinosoft.eflex.dao.FCGrpOrderMapper;
import com.sinosoft.eflex.dao.FDCodeMapper;
import com.sinosoft.eflex.model.Body;
import com.sinosoft.eflex.model.FCGrpInfo;
import com.sinosoft.eflex.model.edor.PolicyHolderChgTrialIO;
import com.sinosoft.eflex.model.edor.PolicyHolderInfo;
import com.sinosoft.eflex.util.CheckUtils;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.RemoteDelegate;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @ClassName: EdorACBL
 * @Auther: hhw
 * @Date: 2019/2/27 16:16
 * @Description: TODO  投保人资料变更保全试算类
 * @Version: 1.0
 */
@Service
public class EdorACBL {
    // 工具类加载
    private static final Logger log = LoggerFactory.getLogger(EdorACBL.class);

    @Autowired
    private MyProps myProps;
    @Autowired
    private EdorService edorService;
    @Autowired
    private FCGrpOrderMapper fcGrpOrderMapper;
    @Autowired
    private FCEdorACInfoMapper fcEdorACInfoMapper;
    @Autowired
    private FDCodeMapper fdCodeMapper;

    private static final String RESULTFLAG = "resultFlag";

    private static final String ERRMSG = "errMsg";

    private static final String EDORAPPNO = "edorAppNo";

   /* // 返回结果容器
    public Map<String,Object> resultMap=new HashMap<>();

    private List<String> errMsg;

    private String edorAppNo;

    // 参数对象
    private PolicyHolderChgTrialIO policyHolderChgTrialIO = new PolicyHolderChgTrialIO();

    private String grpContNo = null;

    private FCGrpInfo fcGrpInfo = new FCGrpInfo();*/

    /*public Map<String,Object> outputData(){
        resultMap.put("edorAppNo", edorAppNo);
        resultMap.put("errMsg", errMsg);
        return resultMap;
    }*/

    /**
     * 外部调用本类的业务处理接口
     *
     * @param policyHolderChgTrialIO
     * @param grpContNo
     * @return
     */
    public Map<String, Object> submitData(PolicyHolderChgTrialIO policyHolderChgTrialIO, String grpContNo, FCGrpInfo fcGrpInfo) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap = getInputData(policyHolderChgTrialIO, grpContNo, fcGrpInfo);
        if (!(boolean) resultMap.get(RESULTFLAG)) {
            return resultMap;
        }
        resultMap = checkData(policyHolderChgTrialIO, grpContNo);
        if (!(boolean) resultMap.get(RESULTFLAG)) {
            return resultMap;
        }
        return dealData(policyHolderChgTrialIO, grpContNo, fcGrpInfo);
    }

    /**
     * 从输入数据中得到所有对象
     *
     * @param policyHolderChgTrialIO
     * @param grpContNo
     * @return
     */
    private Map<String, Object> getInputData(PolicyHolderChgTrialIO policyHolderChgTrialIO, String grpContNo, FCGrpInfo fcGrpInfo) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        boolean resultFlag = true;
        List<String> errMsg = new ArrayList<String>();
        if (policyHolderChgTrialIO == null || policyHolderChgTrialIO.getPolicyHolderInfo() == null) {
            errMsg.add("投保人资料变更：投保人信息不能为空！");
            resultFlag = false;
        }
        if (grpContNo == null || "".equals(grpContNo)) {
            errMsg.add("投保人资料变更：团体保单号不能为空！");
            resultFlag = false;
        }
        resultMap.put(RESULTFLAG, resultFlag);
        resultMap.put(ERRMSG, errMsg);
        return resultMap;
    }

    /**
     * 业务数据校验
     * 员福平台的保单投保人都是法人（企业投保）。
     *
     * @return
     */
    private Map<String, Object> checkData(PolicyHolderChgTrialIO policyHolderChgTrialIO, String grpContNo) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        boolean resultFlag = true;
        List<String> errMsg = new ArrayList<String>();
        /****************************** 这里校验各个保全项目的必录项 start ******************************************************/
        log.info("投保人资料变更试算：开始校验数据的合法性 ............");
        // 被保人资料变更校验
        PolicyHolderInfo policyHolderInfo = policyHolderChgTrialIO.getPolicyHolderInfo();
        if (policyHolderInfo.getGrpName() == null || "".equals(policyHolderInfo.getGrpName())) {
            errMsg.add("投保人资料变更：投保企业名称不能为空！");
            resultFlag = false;
        }
        if (StringUtils.isNotBlank(policyHolderChgTrialIO.getPolicyHolderInfo().getMobilePhone1())) {
            if (!CheckUtils.checkMobilePhone(policyHolderChgTrialIO.getPolicyHolderInfo().getMobilePhone1())) {
                errMsg.add("投保人资料变更：联系人手机格式错误！");
                resultFlag = false;
            }
        }
       /* if(policyHolderInfo.getGrpNature() == null || "".equals(policyHolderInfo.getGrpNature())){
            errMsg.add("投保人资料变更：投保单位性质不能为空！");
            resultFlag = false;
        }*/
        log.info("投保人资料变更试算：数据合法性校验通过..........");
        /**************************************** 这里校验各个保全项目的必录项 end   ******************************************************/
        resultMap.put(RESULTFLAG, resultFlag);
        resultMap.put(ERRMSG, errMsg);
        return resultMap;
    }


    private Map<String, Object> dealData(PolicyHolderChgTrialIO policyHolderChgTrialIO, String grpContNo, FCGrpInfo fcGrpInfo) {
        return policyHolderChgTrialIO(policyHolderChgTrialIO, grpContNo, fcGrpInfo);
    }

    /**
     * 投保人资料变更保全试算接口
     *
     * @return
     */
    public Map<String, Object> policyHolderChgTrialIO(PolicyHolderChgTrialIO policyHolderChgTrialIO, String grpContNo, FCGrpInfo fcGrpInfo) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        boolean resultFlag = false;
        List<String> errMsg = new ArrayList<String>();

        //判断法人是否存在
        policyHolderChgTrialIO.getPolicyHolderInfo().setBusinessType(fcGrpInfo.getTrade());
        policyHolderChgTrialIO.getPolicyHolderInfo().setGrpNature(fcGrpInfo.getGrpType());
        if (StringUtils.isNotBlank(policyHolderChgTrialIO.getPolicyHolderInfo().getGrpZipCode())) {
            Map<String, Object> resultZipMap = edorService.ZipCodeCheck(policyHolderChgTrialIO.getPolicyHolderInfo().getGrpZipCode());
            if (!(boolean) resultZipMap.get("success")) {
                errMsg.add(String.valueOf(resultZipMap.get("message")));
                resultMap.put(RESULTFLAG, resultZipMap.get("success"));
                resultMap.put(ERRMSG, errMsg);
                return resultMap;
            }
            ;
        }
        // 投保单位性质  见码表1核心提供码表
        String grpNature1 = fdCodeMapper.selectOtherSign("GrpNature", fcGrpInfo.getGrpType());
        //后期加影像信息
        String esViewXml = "";
        esViewXml = "\t\t<ESViewList>\n" +
                "\t\t\t<ESView>\n" +
                "\t\t\t\t<SubType></SubType>\n" +
                "\t\t\t\t<PageNum></PageNum>\n" +
                "\t\t\t\t<PageList>\n" +
                "\t\t\t\t\t<Page></Page>\n" +
                "\t\t\t\t\t<ImageUrl></ImageUrl>\n" +
                "\t\t\t\t\t<ImageName></ImageName>\n" +
                "\t\t\t\t\t<PageCode></PageCode>\n" +
                "\t\t\t\t</PageList>\n" +
                "\t\t\t</ESView>\n" +
                "\t\t</ESViewList>\n";
        String requestXml = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                "<RequestInfo>\n" +
                "\t<HEAD>\n" +
                /* 交易流水号 */
                "\t\t<TransRefGUID>" + UUID.randomUUID().toString().replaceAll("-", "") + "</TransRefGUID>\n" +
                /* 接口交易类型 */
                "\t\t<TransType>YUG003</TransType>\n" +
                /* 交易日期 */
                "\t\t<TransExeDate>" + DateTimeUtil.getCurrentDate() + "</TransExeDate>\n" +
                /* 交易时间 */
                "\t\t<TransExeTime>" + DateTimeUtil.getCurrentTime() + "</TransExeTime>\n" +
                "\t</HEAD>\n" +
                "\t<BODY>\n" +
                "\t\t<GrpContNo>" + grpContNo + "</GrpContNo>\n" +
                "\t\t<EdorType>AC</EdorType>\n" +
                "\t\t<EdorAppDate>" + DateTimeUtil.getCurrentDate() + "</EdorAppDate>\n" +
                "\t\t<Appnt>\n" +
                "\t\t\t<GrpName>" + policyHolderChgTrialIO.getPolicyHolderInfo().getGrpName() + "</GrpName>\n" +
                "\t\t\t<GrpNature1>" + grpNature1 + "</GrpNature1>\n" +
                "\t\t\t<GrpNature>" + policyHolderChgTrialIO.getPolicyHolderInfo().getGrpNature() + "</GrpNature>\n" +
                "\t\t\t<BusinessType>" + policyHolderChgTrialIO.getPolicyHolderInfo().getBusinessType() + "</BusinessType>\n" +

                /***团单类型：法人：不需要录入***/
                "\t\t\t<Sex></Sex>\n" +
                "\t\t\t<BirthDay></BirthDay>\n" +   //  字段说明为BirthDay
                "\t\t\t<IdType></IdType>\n" +
                "\t\t\t<IdNo></IdNo>\n" +
                "\t\t\t<OccupationCode></OccupationCode>\n" +
                // "\t\t\t<BirthDate></BirthDate>\n" +  // 没有这个
                "\t\t\t<Email>" + (policyHolderChgTrialIO.getPolicyHolderInfo().getEmail() == null ? "" : policyHolderChgTrialIO.getPolicyHolderInfo().getEmail()) + "</Email>\n" +
                "\t\t\t<AppntNativePlace></AppntNativePlace>\n" +

                "\t\t\t<Phone></Phone>\n" +
                "\t\t\t<Fax></Fax>\n" +
                "\t\t\t<FoundDate></FoundDate>\n" +
                "\t\t\t<GetFlag>" + (policyHolderChgTrialIO.getPolicyHolderInfo().getGetFlag() == null ? "" : policyHolderChgTrialIO.getPolicyHolderInfo().getGetFlag()) + "</GetFlag>\n" +
                "\t\t\t<BankCode>" + policyHolderChgTrialIO.getPolicyHolderInfo().getBankCode() + "</BankCode>\n" +
                "\t\t\t<BankAccNo>" + policyHolderChgTrialIO.getPolicyHolderInfo().getBankAccNo() + "</BankAccNo>\n" +
                "\t\t\t<AppIDPeriodOfValidityType></AppIDPeriodOfValidityType>\n" +
                // 营业执照有效期止期 AppIDPeriodOfValidity  YYYY-MM-DD  非必录
                "\t\t\t<AppIDPeriodOfValidity></AppIDPeriodOfValidity>\n" +
                "\t\t\t<HodingPeoples></HodingPeoples>\n" +
                "\t\t\t<Corporation>" + (policyHolderChgTrialIO.getPolicyHolderInfo().getCorporation() == null ? "" : policyHolderChgTrialIO.getPolicyHolderInfo().getCorporation()) + "</Corporation>\n" +
                "\t\t\t<GrpAddress>" + policyHolderChgTrialIO.getPolicyHolderInfo().getGrpAddress() + "</GrpAddress>\n" +
                "\t\t\t<GrpZipCode>" + policyHolderChgTrialIO.getPolicyHolderInfo().getGrpZipCode() + "</GrpZipCode>\n" +

                /**** 保险联系人  **********/
                "\t\t\t<LinkMan1>" + (policyHolderChgTrialIO.getPolicyHolderInfo().getLinkMan1() == null ? "" : policyHolderChgTrialIO.getPolicyHolderInfo().getLinkMan1()) + "</LinkMan1>\n" +
                /*****************非必录项******************************************/
                "\t\t\t<InsContIDType1></InsContIDType1>\n" +
                "\t\t\t<InsContIDNo1></InsContIDNo1>\n" +
                "\t\t\t<InsContIDPeriodOfValidityType1></InsContIDPeriodOfValidityType1>\n" +
                "\t\t\t<InsContIDPeriodOfValidity1></InsContIDPeriodOfValidity1>\n" +

                //"\t\t\t<Peoples></Peoples>\n" +
                "\t\t\t<InterFaceType1>" + (StringUtils.isBlank(policyHolderChgTrialIO.getPolicyHolderInfo().getMobilePhone1()) ? "" : "2") + "</InterFaceType1>\n" +
                "\t\t\t<MobilePhone1>" + (policyHolderChgTrialIO.getPolicyHolderInfo().getMobilePhone1() == null ? "" : policyHolderChgTrialIO.getPolicyHolderInfo().getMobilePhone1()) + "</MobilePhone1>\n" +
                "\t\t\t<Phone1></Phone1>\n" +
                "\t\t\t<NZProportion>" + (policyHolderChgTrialIO.getPolicyHolderInfo().getNzProportion() == null ? "" : policyHolderChgTrialIO.getPolicyHolderInfo().getNzProportion()) + "</NZProportion>\n" +
                "\t\t</Appnt>\n" +
                esViewXml +
                "\t</BODY>\n" +
                "</RequestInfo>";
        log.info("投保人资料变更试算请求报文：" + requestXml);
        long startTime = System.currentTimeMillis();
        // 调用核心接口
        RemoteDelegate remoteDelegate = RemoteDelegate.getInstance();
        boolean success = remoteDelegate.submitData(myProps.getServiceInfo().get("serviceUrl"),
                myProps.getServiceInfo().get("operation"), "", requestXml);
        long endTime = System.currentTimeMillis();
        log.info("连接核心接口所用时间" + ((endTime - startTime) / 1000.0) + "秒");
        if (success) {
            Map<String, Object> responseXml = remoteDelegate.getResult();
            Body body = (Body) responseXml.get("Body");
            log.info("投保企业试算返回报文：" + JSON.toJSONString(responseXml));
            if ("1".equals(body.getEdorFlag())) {
                log.info("投保人资料变更失败 :" + body.getEdorMark());
                errMsg.add("投保人资料变更失败 :" + body.getEdorMark());
                resultFlag = false;
            } else {
                resultFlag = true;
                resultMap.put("edorAppNo", body.getEdorAppNo());
                log.info("投保人资料变更成功，保全受理号 :" + body.getEdorAppNo());
            }
        } else {
            log.info("投保人资料变更试算失败,请重试！");
            errMsg.add("投保人资料变更试算失败,请重试！");
            resultFlag = false;
        }
        resultMap.put(RESULTFLAG, resultFlag);
        resultMap.put(ERRMSG, errMsg);
        return resultMap;
    }


}
