package com.sinosoft.eflex.service;

import com.sinosoft.eflex.model.OssEntity.OssEntity;
import org.springframework.web.multipart.MultipartFile;

/**
 * Oss 上传*
 *
 * <AUTHOR>
 */
public interface OssService {

    /**
     * 文件上传*
     *
     * @param file
     * @param flag
     * @return
     */
    OssEntity uploadFile(MultipartFile file, String flag);


    /**
     * 链接上传*
     *
     * @param path
     * @return
     */
    String uploadUrl(String path);
}
