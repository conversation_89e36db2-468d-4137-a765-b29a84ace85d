package com.sinosoft.eflex.service;

import ch.qos.logback.classic.Logger;
import com.hqins.common.utils.HttpUtils;
import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.model.OrgAnization.OrgResponse;
import com.sinosoft.eflex.model.OrgAnization.OrgVO;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/3/28 11:33
 */
@Service
public class InvokeService {
    /**
     * 日志
     */
    private static Logger Log = (Logger) LoggerFactory.getLogger(InvokeService.class);

    @Autowired
    private MyProps myProps;

    private static final String BAOXIANJINGJI = "06";
    private static final String YIHANGYOUZHENG = "05";
    private static final String QITAJIANZHI = "04";
    private static final String BAOXIANZHUANYE = "03";
    /**
     * 渠道商类型: 1，保险经纪业务；10，公司直销； 2，银行邮政业务；3， 其他兼业代理；4， 保险专业代理
     *
     * 1对应06   2对应05    3对应04    4对应03
     *
     * @return
     */
    public OrgResponse queryOrgCode(String channel) {
        String request ="";
        switch (channel){
            case BAOXIANJINGJI :
                request ="1";
                break;
            case YIHANGYOUZHENG :
                request ="2";
                break;
            case QITAJIANZHI :
                request ="3";
                break;
            case BAOXIANZHUANYE :
                request ="4";
                break;
            default:
                break;
        }
        try {
            Log.info("queryOrgCode::::requestURl:{}",myProps.getOrganization()+request);
            String response = HttpUtils.get(myProps.getOrganization()+request);
            OrgResponse orgResponse = JsonUtil.fromJSON(response, OrgResponse.class);
            List<OrgVO> data = orgResponse.getData();
            if (CollectionUtils.isNotEmpty(data)){
                data.forEach(x->x.setName(x.getName()+"["+x.getCode()+"]"));
            }
            return orgResponse;
        } catch (IOException e) {
            e.printStackTrace();
            return OrgResponse.builder().build();
        }
    }
}
