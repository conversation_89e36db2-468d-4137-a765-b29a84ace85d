package com.sinosoft.eflex.service;

import ch.qos.logback.classic.Logger;
import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.dao.FCgrpQuestionMapper;
import com.sinosoft.eflex.model.FCgrpQuestion;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2018年10月17日
 */

@Service("QuestionAndAnswerService")
@SuppressWarnings("unused")
public class QuestionAndAnswerService {

    /**
     * 日志
     */
    private static Logger Log = (Logger) LoggerFactory.getLogger(QuestionAndAnswerService.class);

    /**
     * 注入Dao层
     */
    @Autowired
    private FCgrpQuestionMapper fcgrpQuestionMapper;

    /**
     * 获取问题及答案
     */
    @Transactional
    public String getQuestionInfo() {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<FCgrpQuestion> questionInfo = fcgrpQuestionMapper.getQuestionInfo("01");
        resultMap.put("data", questionInfo);
        resultMap.put("success", true);
        resultMap.put("code", "200");
        resultMap.put("message", "获取问题及答案信息成功");
        return JSON.toJSONString(resultMap);
    }
}
