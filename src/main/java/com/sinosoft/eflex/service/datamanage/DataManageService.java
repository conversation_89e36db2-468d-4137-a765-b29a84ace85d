package com.sinosoft.eflex.service.datamanage;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.sinosoft.eflex.config.EasyexcelConfig;
import com.sinosoft.eflex.dao.FCEnsureMapper;
import com.sinosoft.eflex.dao.admin.EnsureAuditMapper;
import com.sinosoft.eflex.enums.DocTypeEnum;
import com.sinosoft.eflex.model.GlobalInput;
import com.sinosoft.eflex.model.PageResp;
import com.sinosoft.eflex.model.datamanage.EnsureInfo;
import com.sinosoft.eflex.model.datamanage.EnsureInsureInfo;
import com.sinosoft.eflex.model.datamanage.GetEnsureInsureDataReq;
import com.sinosoft.eflex.model.datamanage.SelectEnsureInfoListReq;
import com.sinosoft.eflex.service.FileService;
import com.sinosoft.eflex.service.UserService;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.FileUtil;
import com.sinosoft.eflex.util.IpUtil;
import com.sinosoft.eflex.util.rest.exception.SystemException;
import com.sinosoft.eflex.util.rest.util.ResponseResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/8/4
 * @desc 数据管理
 */
@Service
@Slf4j
public class DataManageService {

    // 引入公共的资源
    @Autowired
    private UserService userService;
    @Autowired
    private FileService fileService;
    @Autowired
    private FCEnsureMapper fcEnsureMapper;
    @Autowired
    private EnsureAuditMapper ensureAuditMapper;

    /**
     * 查询可导出的福利信息
     *
     * @param token
     * @param selectEnsureInfoListReq
     * @return
     */
    public String selectEnsureInfoList(String token, SelectEnsureInfoListReq selectEnsureInfoListReq) {
        {
            try {
                // 获取token信息
                GlobalInput globalInput = userService.getSession(token);
                String manageCom = globalInput.getManageCom();
                //设置人员查询范围
                if (StringUtils.isEmpty(manageCom)) {
                    throw new SystemException("审核用户管理机构为空！");
                } else {
                    selectEnsureInfoListReq.setManageCom(manageCom);
                }
                //设置分页
                PageHelper.startPage(selectEnsureInfoListReq.getPageNo(), selectEnsureInfoListReq.getPageSize());
                List<EnsureInfo> ensureInfos = ensureAuditMapper.selectEnsureInfoList(selectEnsureInfoListReq);
                return JSONObject.toJSONString(ResponseResultUtil.success(PageResp.restPage(ensureInfos)));
            } catch (Exception e) {
                log.info("获取计划信息失败:", e);
                throw new SystemException("福利信息查询！");
            }
        }
    }


    /**
     * 导出福利投保信息
     *
     * @param token
     * @param getEnsureInsureDataReq
     */
    public ResponseEntity<InputStreamResource> getEnsureInsureData(HttpServletRequest request, String token,
                                                                   GetEnsureInsureDataReq getEnsureInsureDataReq) {

        /**
         * 获取登录信息
         */
        GlobalInput globalInput = userService.getSession(token);
        log.info("用户终端IP: {}，用户编号: {}，用户姓名: {}，{}获取了福利相关数据。", IpUtil.getIpAddr(request), globalInput.getUserNo(),
                globalInput.getName(), DateTimeUtil.getCurrentDateTime());
        String manageCom = globalInput.getManageCom();
        //设置人员查询范围
        if (StringUtils.isEmpty(manageCom)) {
            throw new SystemException("审核用户管理机构为空！");
        } else {
            getEnsureInsureDataReq.setManageCom(manageCom);
        }

        /**
         * 配置基础参数
         */
        String path = FileUtil.getLocalPath(DocTypeEnum.ENSUREINSUREINFOEXPORT.getCode());
        String fileName = "EnsureInfo-" + System.currentTimeMillis() + ".xlsx";
        String filePath = path + fileName;
        /**
         * 设置单元格的样式
         */
        // 头的样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景设置
        WriteFont headWriteFont = new WriteFont();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.ROYAL_BLUE.getIndex());
        headWriteFont.setFontHeightInPoints((short) 12);
        headWriteFont.setColor(IndexedColors.WHITE.getIndex());
        headWriteCellStyle.setWriteFont(headWriteFont);
        // 内容的样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        // 设置 垂直居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置 水平居中
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle,
                contentWriteCellStyle);

        /**
         * 写入文件
         */
        // 查询福利相关投保相关数据
        List<EnsureInsureInfo> ensureInsureInfos = fcEnsureMapper.selectExportEnsureInsureInfo(getEnsureInsureDataReq);

        EasyExcel.write(filePath).head(EasyexcelConfig.head1()).registerWriteHandler(new CustomCellWriteHandler()).registerWriteHandler(horizontalCellStyleStrategy)
                .sheet("福利投保信息").doWrite(ensureInsureInfos);
        /**
         * 支持文件下载
         */
        return fileService.downloadAppointFilePath(request, filePath, fileName);

    }

}
