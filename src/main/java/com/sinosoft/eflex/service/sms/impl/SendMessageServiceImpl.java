package com.sinosoft.eflex.service.sms.impl;

import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.model.sendmessage.SendEmailReq;
import com.sinosoft.eflex.model.sendmessage.SendMessageResp;
import com.sinosoft.eflex.model.sendmessage.SendSMSContent;
import com.sinosoft.eflex.model.sendmessage.SendSMSReq;
import com.sinosoft.eflex.service.sms.SendMessageService;
import com.sinosoft.eflex.util.HttpUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2020/12/21
 * 短信、邮件发送处理类
 */
@Service
public class SendMessageServiceImpl implements SendMessageService {

    // 日志打印
    private static final Logger log = LoggerFactory.getLogger(SendMessageService.class);
    // 获取配置文件中自定义参数
    @Autowired
    private MyProps myProps;


    /**
     * 短信发送
     * @param sendSMSReq
     * @return
     */
    @Override
    public SendMessageResp sendSMS(SendSMSReq sendSMSReq) {
        SendMessageResp sendMessageResp = new SendMessageResp();
        try {
            /**
             * 设置初始参数
             */
            //产品名称
            sendSMSReq.setProduct(myProps.getSendMessageProduct());
            //应用名称
            sendSMSReq.setApplication(myProps.getSendMessageApplication());
            //秘钥
            sendSMSReq.setAppKey(myProps.getSendMessageAppKey());
            //重新封装
            sendSMSReq.setContent(new SendSMSContent(sendSMSReq.getPhones()));
            /**
             * 调用短信平台
             */
            log.info("\n调用满天星系统发送短信请求报文: {}",JSONObject.toJSONString(sendSMSReq));
            String result = HttpUtil.postHttpRequestJson(myProps.getSendMessageUrl()+"/sendSms", JSONObject.toJSONString(sendSMSReq));
            log.info("\n调用满天星系统发送短信返回报文: {}",result);
            sendMessageResp = JSONObject.parseObject(result, SendMessageResp.class);
        } catch (Exception e) {
            log.info("\n系统内部错误：请求发送短信服务异常！" + e.getMessage());
        }
        return sendMessageResp;
    }

    /**
     * 邮件发送
     * @param sendEmailReq
     * @return
     */
    @Override
    public SendMessageResp sendEmail(SendEmailReq sendEmailReq) {
        SendMessageResp sendMessageResp = new SendMessageResp();
        try {
            /**
             * 设置初始参数
             */
            //产品名称
            sendEmailReq.setProduct(myProps.getSendMessageProduct());
            //应用名称
            sendEmailReq.setApplication(myProps.getSendMessageApplication());
            //秘钥
            sendEmailReq.setAppKey(myProps.getSendMessageAppKey());
            /**
             * 调用邮件平台
             */
            log.info("\n调用满天星系统发送邮件请求报文: {}",JSONObject.toJSONString(sendEmailReq));
            String result = HttpUtil.postHttpRequestJson(myProps.getSendMessageUrl()+"/sendMail", JSONObject.toJSONString(sendEmailReq));
            log.info("\n调用满天星系统发送邮件返回报文: {}",result);
            sendMessageResp = JSONObject.parseObject(result, SendMessageResp.class);
        } catch (Exception e) {
            log.info("\n系统内部错误：请求发送短信服务异常！" + e.getMessage());
        }
        return sendMessageResp;
    }

}
