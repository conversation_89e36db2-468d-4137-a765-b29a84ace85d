package com.sinosoft.eflex.service.sms;

import com.sinosoft.eflex.model.sendmessage.SendEmailReq;
import com.sinosoft.eflex.model.sendmessage.SendMessageResp;
import com.sinosoft.eflex.model.sendmessage.SendSMSReq;
import org.springframework.stereotype.Service;

/**
 * 短信发送
 */
public interface SendMessageService {

    /**
     * 发送短信（满天星平台）
     * @param sendSMSReq
     * @return
     */
    SendMessageResp sendSMS(SendSMSReq sendSMSReq);

    /**
     * 发送邮件（满天星平台）
     * @param sendEmailReq
     * @return
     */
    SendMessageResp sendEmail(SendEmailReq sendEmailReq);

}
