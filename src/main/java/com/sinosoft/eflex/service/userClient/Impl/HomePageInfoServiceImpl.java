package com.sinosoft.eflex.service.userClient.Impl;

import com.hqins.common.base.utils.AssertUtil;
import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.framework.exception.EFlexServiceException;
import com.sinosoft.eflex.framework.exception.EFlexServiceExceptionEnum;
import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.HomePageInfo.FcPerNameInfo;
import com.sinosoft.eflex.model.HomePageInfo.PerFamilyInfo;
import com.sinosoft.eflex.model.HomePageInfo.RiskTypeInfo;
import com.sinosoft.eflex.model.HomePageInfo.convert.PerFamilyInfoConvert;
import com.sinosoft.eflex.model.HomePageInfo.convert.RiskInfoConvert;
import com.sinosoft.eflex.service.EnsureMakeService;
import com.sinosoft.eflex.service.UserService;
import com.sinosoft.eflex.service.userClient.HomePageInfoService;
import com.sinosoft.eflex.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

import static com.sinosoft.eflex.enums.status.OrderStatusEnum.INSURE_SUCCESS;

/**
 * 官微员福首页信息展示*
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class HomePageInfoServiceImpl implements HomePageInfoService {

    private final UserService userService;
    private final EnsureMakeService ensureMakeService;

    private final FCPerInfoMapper fcPerInfoMapper;
    private final EmpAndFamilyMapper empAndFamilyMapper;
    private final FCEnsureMapper fcEnsureMapper;
    private final FCPersonMapper fcPersonMapper;
    private final FcInsureEflexPlanMapper fcInsureEflexPlanMapper;
    private final FDRiskInfoMapper fdRiskInfoMapper;
    private final FCOrderMapper fcOrderMapper;


    @Override
    public List<FcPerNameInfo> getHomePageInfo(String authorization) {
        // 查询登陆信息
        GlobalInput globalInput = userService.getSession(authorization);
        String perNo = globalInput.getCustomNo();

        // 查询企业信息
        List<Map<String, String>> fcPerInfos = fcPerInfoMapper.selectgrpListByPerNo2(perNo);
        AssertUtil.notEmpty(fcPerInfos, new EFlexServiceException(EFlexServiceExceptionEnum.PER_INFO_ERROR));
        List<FcPerNameInfo> fcPerInfoList = JsonUtil.toList(JsonUtil.toJSON(fcPerInfos), List.class, FcPerNameInfo.class);
        // 查询家庭信息成员
        for (FcPerNameInfo fcPerInfo : fcPerInfoList) {
            String newPerNo = fcPerInfoMapper.selectperNobyPerNo(globalInput.getCustomNo(), fcPerInfo.getGrpNo());
            log.info("HomePageInfoServiceImpl.getHomePageInfo, grpNo:{},preNo:{}", fcPerInfo.getGrpNo(), newPerNo);
            // 查询是否可以操作
            boolean show = this.isShow(fcPerInfo.getGrpNo(), newPerNo);
            fcPerInfo.setShow(show);
            // 家庭成员信息
            List<PerFamilyInfo> perFamilyInfoList = this.selectFamilyInfo(newPerNo);
            fcPerInfo.setPerFamilyInfoList(perFamilyInfoList);
            if (show) {
                // 查询个人险种信息
                perFamilyInfoList.stream().filter(p -> "0".equals(p.getRelation())).findFirst().ifPresent(perFamilyInfo -> fcPerInfo.setRiskInfoList(this.getRiskInfos(perFamilyInfo.getPersonId())));
            }
            // 投保结果查询
            List<FCOrder> orderList = fcOrderMapper.selectByOrder(new FCOrder(INSURE_SUCCESS.getCode(), fcPerInfo.getGrpNo(), fcPerInfo.getPerNo()));
            fcPerInfo.setInsureState(CollectionUtils.isNotEmpty(orderList));

        }
        log.info("HomePageInfoServiceImpl.getHomePageInfo response:{}", JsonUtil.toJSON(fcPerInfoList));
        return fcPerInfoList;
    }


    public List<PerFamilyInfo> selectFamilyInfo(String perNo) {
        FCPerInfo fcperinfo = fcPerInfoMapper.selectByPrimaryKey(perNo);
        if (Objects.isNull(fcperinfo)) {
            return new ArrayList<>();
        }
        // 查询是否存在相同证件号的员工最新的员工信息
        List<String> idNoList = empAndFamilyMapper.selectFamilyIDNO(perNo);
        List<String> personIdList = new ArrayList<>();
        for (String idNo : idNoList) {
            Map<String, String> map1 = new HashMap<>(2);
            map1.put("IdNo", idNo);
            map1.put("perNo", perNo);
            String personId = empAndFamilyMapper.selectNewFamilyPersonid(map1);
            if (StringUtils.isNotBlank(personId)) {
                personIdList.add(personId);
            }
        }
        Map<String, Object> paraMap = new HashMap<>();
        paraMap.put("perosnidlist", personIdList);
        paraMap.put("idNo", fcperinfo.getIDNo());
        List<FCPerson> familyList = empAndFamilyMapper.selectAllFamilyInfo(paraMap);
        List<PerFamilyInfo> perFamilyInfoList = PerFamilyInfoConvert.convert(fcperinfo, familyList);
        for (PerFamilyInfo perFamilyInfo : perFamilyInfoList) {
            Integer count = fcEnsureMapper.selectFamTempStudentCount(perFamilyInfo.getPersonId());
            if (count > 0) {
                perFamilyInfo.setIsHasStuRule("Y");
            } else {
                perFamilyInfo.setIsHasStuRule("N");
            }
        }
        return perFamilyInfoList;
    }


    public List<RiskTypeInfo> getRiskInfos(String personId) {
        // 默认0
        List<RiskTypeInfo> riskInfoList = RiskInfoConvert.convert();
        if (StringUtil.isEmpty(personId)) {
            return riskInfoList;
        }
        //弹性福利险种信息
        List<String> personList = fcPersonMapper.getAllPerson(personId);
        for (String person : personList) {
            List<Map<String, String>> fcInsureList = fcInsureEflexPlanMapper.selectByPersonId(person);
            for (Map<String, String> insureMap : fcInsureList) {
                String orderItemDetailNo = insureMap.get("OrderItemDetailNo");
                //得到投保必选责任档次编码以及详细信息
                List<Map<String, String>> fcInsureEflexPlanList = fcInsureEflexPlanMapper.selectByorderItemDetailNo(orderItemDetailNo);
                for (Map<String, String> insureEflexMap : fcInsureEflexPlanList) {
                    //得到每一个险种编号与保费
                    String riskCode = insureEflexMap.get("riskCode");
                    Double Amnt = 0.00;
                    Double amnt = Double.valueOf(insureEflexMap.get("amnt"));
                    Amnt += amnt;
                    //查询险种对应的险种类型
                    FDRiskInfo fdRiskInfo = fdRiskInfoMapper.selectByPrimaryKey(riskCode);
                    String riskType = fdRiskInfo.getRiskType();
                    //获取已投保险种总称
                    for (RiskTypeInfo riskInfo : riskInfoList) {
                        if (riskInfo.getRiskType().equals(riskType)) {
                            riskInfo.setIsRisk("1");
                            riskInfo.setAMnt(new BigDecimal(Amnt));
                            riskInfoList.add(riskInfo);
                        }
                    }
                }
            }
        }
        return riskInfoList;
    }


    public boolean isShow(String grpNo, String newPerNo) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("perNo", newPerNo);
        params.put("grpNo", grpNo);
        List<FCEnsureGrpInfo> ensureInfoList = fcEnsureMapper.selectAllEnsureandGrpNo(params);
        for (FCEnsureGrpInfo fcEnsureGrpInfo : ensureInfoList) {
            String checkRiskStopSale = ensureMakeService.checkRiskStopSale(fcEnsureGrpInfo.getEnsureCode());
            if (StringUtil.isEmpty(checkRiskStopSale)) {
                log.info("HomePageInfoServiceImpl.isShow riskCode:{}", checkRiskStopSale);
                return true;
            }
        }
        return false;
    }

}
