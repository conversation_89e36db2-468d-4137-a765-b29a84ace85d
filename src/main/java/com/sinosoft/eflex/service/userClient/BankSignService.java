package com.sinosoft.eflex.service.userClient;

import com.sinosoft.eflex.model.sign.apply.SignApplyReq;
import com.sinosoft.eflex.model.sign.confirm.SignConfirmRequest;

/**
 * 银行卡签单*
 *
 * <AUTHOR>
 */
public interface BankSignService {


    /**
     * 银行卡签单
     *
     * @param token        登录态
     * @param signApplyReq 请求
     * @return 业务号
     */
    String signApply(String token, SignApplyReq signApplyReq);

    /**
     * 银行签约申请确认
     *
     * @param signConfirmRequest
     */
    void signConfirm(SignConfirmRequest signConfirmRequest);
}
