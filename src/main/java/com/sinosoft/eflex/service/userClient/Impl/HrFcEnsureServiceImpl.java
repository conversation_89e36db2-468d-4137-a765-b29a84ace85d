package com.sinosoft.eflex.service.userClient.Impl;

import com.github.pagehelper.PageHelper;
import com.sinosoft.eflex.dao.*;
import com.sinosoft.eflex.model.EnsureMake.EnsureRequest;
import com.sinosoft.eflex.model.EnsureMake.FCEnsureResponse;
import com.sinosoft.eflex.model.EnsureMake.GrpEnsureResponse;
import com.sinosoft.eflex.model.EnsureMake.convert.EnsureMakeConvert;
import com.sinosoft.eflex.model.EnsureMake.convert.FcEnsureResponseConvert;
import com.sinosoft.eflex.model.FCEnsure;
import com.sinosoft.eflex.model.GlobalInput;
import com.sinosoft.eflex.service.UserService;
import com.sinosoft.eflex.service.userClient.HrFcEnsureService;
import com.sinosoft.eflex.util.PageHelperUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * Hr 手机端 * *
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class HrFcEnsureServiceImpl implements HrFcEnsureService {


    private final UserService userService;
    private final FCEnsureMapper fcEnsureMapper;
    private final FCPerInfoTempMapper fcPerInfoTempMapper;
    private final FCOrderMapper fcOrderMapper;
    private final FCOrderItemMapper fcOrderItemMapper;
    private final FCPerRegistDayMapper fcPerRegistDayMapper;


    /**
     * 手机端查询企业福利信息情况*
     *
     * @param authorization
     * @param ensureRequest
     * @return
     */
    @Override
    public GrpEnsureResponse grpEnsureList(String authorization, EnsureRequest ensureRequest) {
        // 查询登陆信息
        GlobalInput globalInput = userService.getSession(authorization);

        // 分页查询福利信息
        PageHelper.startPage(ensureRequest.getPage(), ensureRequest.getRows());
        List<FCEnsure> fcEnsureList = fcEnsureMapper.findEnsureListByPagePhone(EnsureMakeConvert.convert(ensureRequest, globalInput.getGrpNo()));
        PageHelperUtil<FCEnsure> teamPageInfo = new PageHelperUtil<>(fcEnsureList);
        // 统计福利下人员信息
        List<FCEnsureResponse> fcEnsureResList = new ArrayList<>(fcEnsureList.size());
        for (FCEnsure fcEnsure : fcEnsureList) {
            String ensureCode = fcEnsure.getEnsureCode();

            // 该福利下导入员工总数
            Integer ensureCount = fcPerInfoTempMapper.selectHas(ensureCode);

            // 员工登录平台投保人数
            Integer platStaCount = fcOrderMapper.selectLoginOrderCountByEnsureCode(ensureCode);

            // 获取总投保人数(员工+家属)
            Integer staFamilyCount = fcOrderItemMapper.selectOrderItemCountByEnsureCode(ensureCode);

            // 获取总投保人数(家属)
            Integer familyCount = fcOrderItemMapper.selectFamilyCountByEnsureCode(ensureCode);

            // 未投保人数
            Integer staCount = fcPerRegistDayMapper.selectStaCount(ensureCode);
            fcEnsureResList.add(FcEnsureResponseConvert.convert(fcEnsure, staCount, staFamilyCount, familyCount <= 1 ? 0 : familyCount - 1, platStaCount, ensureCount));
        }

        return new GrpEnsureResponse(fcEnsureResList, teamPageInfo.getTotal());
    }

}