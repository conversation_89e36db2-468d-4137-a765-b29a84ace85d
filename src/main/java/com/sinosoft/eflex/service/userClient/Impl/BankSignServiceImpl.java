package com.sinosoft.eflex.service.userClient.Impl;

import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.dao.FCBatchPayBankInfoMapper;
import com.sinosoft.eflex.dao.FCEnsureMapper;
import com.sinosoft.eflex.dao.FCOrderMapper;
import com.sinosoft.eflex.framework.exception.EFlexServiceException;
import com.sinosoft.eflex.framework.exception.EFlexServiceExceptionEnum;
import com.sinosoft.eflex.model.FcBatchPayBankInfo;
import com.sinosoft.eflex.model.GlobalInput;
import com.sinosoft.eflex.model.convert.FcBatchPayBankInfoConvert;
import com.sinosoft.eflex.model.sign.apply.SignApplyReq;
import com.sinosoft.eflex.model.sign.apply.SignApplyRespData;
import com.sinosoft.eflex.model.sign.confirm.SignConfirmRequest;
import com.sinosoft.eflex.model.sign.confirm.SignConfirmRespData;
import com.sinosoft.eflex.rpc.model.BankSign.BankSignConvert;
import com.sinosoft.eflex.rpc.service.CoreBankSignService;
import com.sinosoft.eflex.service.MaxNoService;
import com.sinosoft.eflex.service.UserService;
import com.sinosoft.eflex.service.userClient.BankSignService;
import com.sinosoft.eflex.util.DateTimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 银行卡签约*
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BankSignServiceImpl implements BankSignService {


    private final CoreBankSignService coreBankSignService;

    private final UserService userService;
    private final MaxNoService maxNoService;
    private final FCEnsureMapper fcEnsureMapper;
    private final FCOrderMapper fcOrderMapper;
    private final FCBatchPayBankInfoMapper fcBatchPayBankInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String signApply(String token, SignApplyReq signApplyReq) {
        // 获取token信息
        GlobalInput globalInput = userService.getSession(token);
        //增加订单号
        String businessNo = maxNoService.createMaxNo("OrderNo", DateTimeUtil.getTimeString().substring(0, 4), 8);
        // 交易流水号，唯一，如：1000000001609990196484
        String signSn = "SN" + maxNoService.createMaxNo("BankSign", "", 30);
        // 银行卡签约
        SignApplyRespData signApplyRespData = coreBankSignService.bankSignApply(BankSignConvert.convert(signApplyReq, signSn, businessNo));
        // 存储支付信息
        fcEnsureMapper.saveFcBatchPayBankInfo(FcBatchPayBankInfoConvert.convert(globalInput, signApplyReq, signApplyRespData, businessNo));
        return businessNo;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void signConfirm(SignConfirmRequest signConfirmRequest) {
        log.info("BankSignServiceImpl signConfirm request:{}", JsonUtil.toJSON(signConfirmRequest));
        List<FcBatchPayBankInfo> selectFcBatchPayBankInfo = fcBatchPayBankInfoMapper.selectBatchPayBankInfo(FcBatchPayBankInfoConvert.convert(signConfirmRequest));
        FcBatchPayBankInfo fcBatchPayBankInfo = selectFcBatchPayBankInfo.stream().filter(s -> s.getOrderNo().equals(signConfirmRequest.getOrderNo())).findFirst().orElse(null);

        if (CollectionUtils.isEmpty(selectFcBatchPayBankInfo) || Objects.isNull(fcBatchPayBankInfo)) {
            throw new EFlexServiceException(EFlexServiceExceptionEnum.SIGN_CONFIRM_CODE_ERROR);
        }

        String signSnc = "SNC" + maxNoService.createMaxNo("BankSign", "", 30);
        // 签约确认
        SignConfirmRespData signConfirmRespData = coreBankSignService.bankSignConfirm(BankSignConvert.convert(signSnc, signConfirmRequest.getVerifyCode(), fcBatchPayBankInfo));

        // 删除修改前的银行卡信息
        for (FcBatchPayBankInfo batchPayBankInfo : selectFcBatchPayBankInfo) {
            if (!batchPayBankInfo.getOrderNo().equals(signConfirmRequest.getOrderNo())) {
                fcBatchPayBankInfoMapper.deleteFcBatchPayBankInfoByOrderNo(batchPayBankInfo.getOrderNo());
            }
        }
        // 调用签约确认成功，保存信息
        fcBatchPayBankInfoMapper.updateByPrimaryKeySelective(FcBatchPayBankInfoConvert.convertInsert(signConfirmRequest, signConfirmRespData.getBankAccNo()));
        // 更新订单状态为待生效（此批扣方式为核心批扣）
        fcOrderMapper.updateFcorder("08", signConfirmRequest.getOrderNo(), DateTimeUtil.getCurrentDate(), DateTimeUtil.getCurrentTime());
        log.info("签约成功 ！！");
    }

}
