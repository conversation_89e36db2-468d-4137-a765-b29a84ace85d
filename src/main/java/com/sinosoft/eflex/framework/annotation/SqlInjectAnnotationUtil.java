package com.sinosoft.eflex.framework.annotation;

import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.web.method.HandlerMethod;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @describe
 * @date 2023/2/2
 */
public final class SqlInjectAnnotationUtil {

    private SqlInjectAnnotationUtil() {

    }

    /**
     * 查找注解
     */
    public static <A extends Annotation> A findAnnotation(Object object, Class<A> annotationType) {

        if (object == null || annotationType == null) {
            return null;
        }

        A result = null;

        if (object instanceof HandlerMethod) {
            //如果传入的是拦截器方法
            HandlerMethod handlerMethod = (HandlerMethod) object;
            result = AnnotationUtils.findAnnotation(handlerMethod.getMethod(), annotationType);
            if (result == null) {
                //方法上没有，则从类上取
                result = AnnotationUtils.findAnnotation(handlerMethod.getBeanType(), annotationType);
            }
        } else if (object instanceof Method) {
            //如果传入的是普通方法
            Method method = (Method) object;
            result = AnnotationUtils.findAnnotation(method, annotationType);
            if (result == null) {
                //方法上没有，则从类上取
                result = AnnotationUtils.findAnnotation(method.getDeclaringClass(), annotationType);
            }
        } else if (object instanceof Class) {
            //如果传入的是类
            Class<?> cls = (Class<?>) object;
            result = AnnotationUtils.findAnnotation(cls, annotationType);
        } else {
            //如果传入的是bean
            result = AnnotationUtils.findAnnotation(object.getClass(), annotationType);
        }

        return result;
    }

}
