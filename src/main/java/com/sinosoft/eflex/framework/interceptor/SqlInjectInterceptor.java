package com.sinosoft.eflex.framework.interceptor;

import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.constants.RegexConstants;
import com.sinosoft.eflex.framework.annotation.SqlInject;
import com.sinosoft.eflex.framework.annotation.SqlInjectAnnotationUtil;
import com.sinosoft.eflex.framework.exception.EFlexServiceException;
import com.sinosoft.eflex.framework.exception.EFlexServiceExceptionEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Enumeration;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.stream.Collectors;


/**
 * Sql注解 实现*
 *
 * <AUTHOR>
 */
@Slf4j
public class SqlInjectInterceptor extends HandlerInterceptorAdapter {


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        SqlInject sqlInject = SqlInjectAnnotationUtil.findAnnotation(handler, SqlInject.class);
        if (null == sqlInject || !sqlInject.KeywordPrevent()) {
            return true;
        }
        Enumeration<String> parameterNames = request.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            String value = request.getParameter(parameterNames.nextElement());
            if (RegexConstants.KEY_WORD.stream().anyMatch(s -> s.equals(value))) {
                continue;
            }
            Matcher matcher = RegexConstants.SQL_INJECT.matcher(value);
            boolean matches = matcher.find();
            if (matches) {
                throw new EFlexServiceException(EFlexServiceExceptionEnum.SQL_INJECT_ERROR);
            }
        }

        RepeatableHttpServletRequestWrapper requestWrapper = null;
        try {
            requestWrapper = new RepeatableHttpServletRequestWrapper(request);
            String value = requestWrapper.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
            if (StringUtils.isNotEmpty(value)) {
                Map<String, Object> map = JsonUtil.fromJSON(value, Map.class);
                Set<String> set = map.keySet();
                for (String key : set) {
                    if (RegexConstants.KEY_WORD.stream().anyMatch(s -> s.equals(value))) {
                        continue;
                    }
                    Matcher matcher = RegexConstants.SQL_INJECT.matcher(String.valueOf(map.get(key)));
                    boolean matches = matcher.find();
                    if (matches) {
                        throw new EFlexServiceException(EFlexServiceExceptionEnum.SQL_INJECT_ERROR);
                    }
                }
            }
        } catch (IOException e) {
            log.error("getRequestBodyStr io error", e);
        }
        return super.preHandle(requestWrapper, response, handler);
    }
}
