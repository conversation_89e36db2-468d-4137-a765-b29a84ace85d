package com.sinosoft.eflex.framework.log.trace;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.web.filter.GenericFilterBean;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @describe {@link org.springframework.cloud.sleuth.log.Slf4jSpanLogger}
 * @describe {@link https://help.aliyun.com/document_detail/91409.htm?spm=a2c4g.11186623.0.0.3db64c51ubpDQW#concept-91409-zh}
 * @date 2022/12/26
 */
@Slf4j
@Order(LogInputFilter.ORDER)
@WebFilter(urlPatterns = "/*", filterName = "logInputFilter")
public class LogInputFilter extends GenericFilterBean {


    public static final int ORDER = Ordered.HIGHEST_PRECEDENCE + 5;
    protected static final String TRACE_ID = "TRACE_ID";
    private static final String EAGLE_EYE_TRACE_ID = "EagleEye-TraceID";

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        if (servletRequest instanceof HttpServletRequest && servletResponse instanceof HttpServletResponse) {
            MDC.remove(TRACE_ID);
            try {
                String traceId = this.getTraceId((HttpServletRequest) servletRequest);

                MDC.put(TRACE_ID, traceId);

                HttpServletResponse httpResponse = (HttpServletResponse) servletResponse;
                httpResponse.setHeader(TRACE_ID, traceId);
            } catch (Exception exception) {
                log.warn("LogInputFilter.doFilter error", exception);
            }
        }
        filterChain.doFilter(servletRequest, servletResponse);
    }

    @Override
    public void destroy() {
        try {
            MDC.remove(TRACE_ID);
        } catch (Exception exception) {
            log.warn("LogInputFilter.destroy ", exception);
        }
    }

    /**
     * 获取请求traceId
     *
     * @param httpRequest req
     * @return traceId
     */
    private String getTraceId(HttpServletRequest httpRequest) {
        String traceId = httpRequest.getHeader(TRACE_ID);
        if (StringUtils.isBlank(traceId)) {
            // 前端调用后端
            traceId = httpRequest.getHeader(EAGLE_EYE_TRACE_ID);
        } else {
            // 后端调用后端
            return traceId;
        }
        // 应该不会出现这种情况，除非是外部请求（没有依赖该jar也没有使用arms）
        return StringUtils.isBlank(traceId) ? TraceIdCreateService.createId() : traceId;
    }

}