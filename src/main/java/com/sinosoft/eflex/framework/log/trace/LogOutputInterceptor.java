package com.sinosoft.eflex.framework.log.trace;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import static com.sinosoft.eflex.framework.log.trace.LogInputFilter.TRACE_ID;


/**
 * <AUTHOR>
 * @describe
 * @date 2022/12/26
 */
@Slf4j
public class LogOutputInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        try {
            String tid = MDC.get(TRACE_ID);
            if (StringUtils.isBlank(tid)) {
                return;
            }
            requestTemplate.header(TRACE_ID, tid);
        } catch (Exception exception) {
            log.warn("LogOutputInterceptor #apply ", exception);
        }
    }
}