package com.sinosoft.eflex.framework.threadPool;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @describe 增加线程隔离
 * 1. todo HANDLER需要增加实现类
 * 2. todo 异常的抛出目前没有
 * @date 2021/7/19
 */
@Configuration
public class EFlexThreadPool {

    private static final long KEEP_ALIVE_TIME = 5;
    private static final RejectedExecutionHandler HANDLER = new ThreadPoolExecutor.CallerRunsPolicy();


    @Bean("eFlexExecutor-0")
    public Executor eFlexExecutor0() {
        return new EFlexThreadPoolTaskExecutor(20, 200, KEEP_ALIVE_TIME, TimeUnit.SECONDS, new ArrayBlockingQueue<>(50), new ThreadFactoryBuilder().setNameFormat("======THREAD-POOL-0-%d").build(), HANDLER);
    }

    @Bean("eFlexExecutor-1")
    public Executor eFlexExecutor1() {
        return new EFlexThreadPoolTaskExecutor(5, 30, KEEP_ALIVE_TIME, TimeUnit.SECONDS, new ArrayBlockingQueue<>(50), new ThreadFactoryBuilder().setNameFormat("======THREAD-POOL-1-%d").build(), HANDLER);
    }

    @Bean("eFlexExecutor-2")
    public Executor eFlexExecutor2() {
        return new EFlexThreadPoolTaskExecutor(5, 10, KEEP_ALIVE_TIME, TimeUnit.SECONDS, new ArrayBlockingQueue<>(20), new ThreadFactoryBuilder().setNameFormat("======THREAD-POOL-2-%d").build(), HANDLER);
    }

}
