package com.sinosoft.eflex.framework.threadPool;

import com.sinosoft.eflex.framework.log.trace.ThreadMdcUtil;
import org.slf4j.MDC;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @describe
 * @date 2022/12/26
 */
public class EFlexThreadPoolTaskExecutor extends ThreadPoolExecutor {

    public EFlexThreadPoolTaskExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, ThreadFactory threadFactory, RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
    }

    @Override
    public void execute(Runnable task) {
        super.execute(ThreadMdcUtil.wrap(task, MDC.getCopyOfContextMap()));
    }


    @Override
    public <T> Future<T> submit(Callable<T> task) {
        return super.submit(ThreadMdcUtil.wrap(task, MDC.getCopyOfContextMap()));
    }

    @Override
    public Future<?> submit(Runnable task) {
        return super.submit(ThreadMdcUtil.wrap(task, MDC.getCopyOfContextMap()));
    }

}