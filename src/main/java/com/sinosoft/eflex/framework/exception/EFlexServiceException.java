package com.sinosoft.eflex.framework.exception;

import com.hqins.common.base.errors.ApiException;

/**
 * <AUTHOR>
 * @describe
 * @date 2022/8/19
 */
public class EFlexServiceException extends ApiException {


    public EFlexServiceException(String message) {
        super(message);
    }


    public EFlexServiceException(EFlexServiceExceptionEnum serviceExceptionEnum) {
        super(serviceExceptionEnum.getHttpStatus(), serviceExceptionEnum.getCode(), serviceExceptionEnum.getMessage());
    }

    public EFlexServiceException(EFlexServiceExceptionEnum serviceExceptionEnum, String key, String message) {
        super(serviceExceptionEnum.getHttpStatus(), serviceExceptionEnum.getCode(), serviceExceptionEnum.getMessage().replace(key, message));
    }

    public EFlexServiceException(int httpStatus, String code, String message) {
        super(httpStatus, code, message);
    }

    public EFlexServiceException(int httpStatus, String message) {
        super(httpStatus, message);
    }


    public EFlexServiceException(String code, String message) {
        super(code, message);
    }
}
