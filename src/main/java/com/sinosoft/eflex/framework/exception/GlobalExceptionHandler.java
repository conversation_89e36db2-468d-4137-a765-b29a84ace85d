package com.sinosoft.eflex.framework.exception;

import com.hqins.common.base.ApiResult;
import com.hqins.common.base.errors.ApiException;
import com.sinosoft.eflex.util.rest.ResultCodeEnum;
import com.sinosoft.eflex.util.rest.exception.BusinessException;
import com.sinosoft.eflex.util.rest.exception.SystemException;
import com.sinosoft.eflex.util.rest.util.ResponseResult;
import com.sinosoft.eflex.util.rest.util.ResponseResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.dao.DataAccessException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.sql.SQLException;
import java.util.stream.Collectors;


/**
 * @date:2024/04/917:40
 * @author:xu
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理请求参数格式错误 @RequestBody上使用@Valid 实体上使用@NotNull等，验证失败后抛出的异常是MethodArgumentNotValidException异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public ApiResult<String> methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
        log.warn("GlobalExceptionHandler.MethodArgumentNotValidExceptionHandler handler exception:", e);
        String message = e.getBindingResult().getAllErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.joining());
        return ApiResult.fail(message);
    }

    /**
     * 处理数据库异常
     */
    @ExceptionHandler({DataAccessException.class, SQLException.class})
    public ResponseResult handleSqlException(Exception exception) {
        log.error("GlobalExceptionHandler.handleSQLException handler exception:", exception);
        ResultCodeEnum resultCodeEnum;
        resultCodeEnum = ResultCodeEnum.BUSINESS_EXCEPTION;
        resultCodeEnum.setMessage(getConstraintViolationErrMsg(exception));
        log.error("sql error: {}", resultCodeEnum.getMessage());
        return ResponseResultUtil.failure(resultCodeEnum);
    }


    /**
     * 异常捕获
     *
     * @param e 捕获的异常
     * @return 封装的返回对象
     **/
    @ExceptionHandler(Exception.class)
    public ResponseResult handlerException(Exception e) {
        ResultCodeEnum resultCodeEnum;
        // 自定义异常
        if (e instanceof BusinessException) {
            resultCodeEnum = ResultCodeEnum.BUSINESS_EXCEPTION;
            resultCodeEnum.setMessage(getConstraintViolationErrMsg(e));
            log.error("业务异常: {}", resultCodeEnum.getMessage());
        } else if (e instanceof SystemException) {
            resultCodeEnum = ResultCodeEnum.CODE_EXCEPTION;
            resultCodeEnum.setMessage(getConstraintViolationErrMsg(e));
            log.error("代码异常: {}", resultCodeEnum.getMessage());
        } else {
            // 其他异常，当我们定义了多个异常时，这里可以增加判断和记录，来提示对应的编码
            resultCodeEnum = ResultCodeEnum.SERVER_ERROR;
            resultCodeEnum.setMessage(e.getMessage());
            log.error("handlerException error", e);
        }
        return ResponseResultUtil.failure(resultCodeEnum);
    }


    @ExceptionHandler(EFlexServiceException.class)
    public ApiResult<T> eFlexServiceException(EFlexServiceException e) {
        log.error("GlobalExceptionHandler.eFlexServiceException handler exception:", e);
        return ApiResult.fail(e);
    }

    /**
     * 获取错误信息
     *
     * @param ex
     * @return
     */
    private String getConstraintViolationErrMsg(Exception ex) {
        // validTest1.id: id必须为正数, validTest1.name: 长度必须在有效范围内
        String message = ex.getMessage();
        try {
            int startIdx = message.indexOf(": ");
            if (startIdx < 0) {
                startIdx = 0;
            }
            int endIdx = message.indexOf(", ");
            if (endIdx < 0) {
                endIdx = message.length();
            }
            message = message.substring(startIdx, endIdx);
            return message;
        } catch (Throwable throwable) {
            log.info("ex caught", throwable);
            return message;
        }
    }

}