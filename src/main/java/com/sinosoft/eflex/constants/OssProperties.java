package com.sinosoft.eflex.constants;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;


/**
 * 阿里云 OSS 配置*
 *
 * <AUTHOR>
 */
@Component
@Data
@ConfigurationProperties(prefix = "myProps.aliyun.oss")
@RefreshScope
public class OssProperties {

    private String endpointOut;
    private String endpointIn;
    private String accessKeyId;
    private String accessKeySecret;
    private String bucketName;
    private String catalogue;
}
