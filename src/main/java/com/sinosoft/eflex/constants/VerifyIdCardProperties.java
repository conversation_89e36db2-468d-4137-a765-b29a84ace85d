package com.sinosoft.eflex.constants;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;


/**
 * 公安二要素 配置*
 *
 * <AUTHOR>
 */
@Component
@Data
@ConfigurationProperties(prefix = "VerifyIdCard")
@RefreshScope
public class VerifyIdCardProperties {

    private Integer cuntDownSize;

    private Integer dataVolume;

    private String checkIdCard;
}
