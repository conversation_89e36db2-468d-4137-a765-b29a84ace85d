package com.sinosoft.eflex.constants;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 正则表达式*
 *
 * <AUTHOR>
 */
public final class RegexConstants {


    /**
     * sql 注入正则*
     */
    public static final Pattern SQL_INJECT = Pattern.compile("select|update|and|or|delete|insert|truncate|xor|substr|ascii|exec|count|master|into|drop|execute|table|" +
            "char|declare|sitename|xp_cmdshell|like|from|grant|use|group_concat|column_name|" +
            "information_schema.columns|table_schema|union|where|order|by|" +
            "'\\*|\\;|\\--|\\+|\\,|\\//|\\/|\\%|\\#|\\&", Pattern.CASE_INSENSITIVE);

    public static final List<String> KEY_WORD = Arrays.asList("formal");
}
