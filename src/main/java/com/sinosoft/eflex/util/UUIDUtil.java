package com.sinosoft.eflex.util;

import java.util.UUID;

public class UUIDUtil {
	//生成UUID
    public static String genUUID() {
        return UUID.randomUUID().toString().replace("-", "").toUpperCase();
    }
    public static String getFileCodeByUUId() {
        int machineId = 1;//最大支持1-9个集群机器部署
        int hashCodeV = UUID.randomUUID().toString().hashCode();
        if(hashCodeV < 0) {//有可能是负数
            hashCodeV = - hashCodeV;
        }
        return machineId + String.format("%014d", hashCodeV);
    }
}
