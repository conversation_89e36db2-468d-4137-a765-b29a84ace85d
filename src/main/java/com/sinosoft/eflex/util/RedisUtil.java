package com.sinosoft.eflex.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.TimeUnit;

@Service("RedisUtil")
public class RedisUtil {
	
	
    @Autowired
    protected RedisTemplate<String, String> redisTemplate;
    

    public Long generateId(String key,Date date) {
		RedisAtomicLong counter = new RedisAtomicLong(key, redisTemplate.getConnectionFactory());
//		counter.expireAt(date);
		return counter.incrementAndGet();		
	}
    
  
    /**
     * 添加
     *
     * @param key    key
     * @param value  值
     * @param expire 过期时间(单位:秒),传入 -1 时表示不设置过期时间
     */
    public void put(String key, String value, long expire) {
    	ValueOperations<String, String> operations = redisTemplate.opsForValue();
    	operations.set(key, value);
        if (expire != -1) {
            redisTemplate.expire(key, expire, TimeUnit.SECONDS);
        }
    }
    
	/**
	 * 更新过期时间
	 *
	 * @param key
	 *            key
	 * @param expire
	 *            过期时间(单位:秒),传入 -1 时表示不设置过期时间
	 */
	public void expire(String key, long expire) {
		redisTemplate.expire(key, expire, TimeUnit.SECONDS);
	}
	/**
	 * 更新过期时间
	 *
	 * @param key
	 *            key
	 * @param expire
	 *            过期时间(单位:天),传入 -1 时表示不设置过期时间
	 */
	public void expireDay(String key, long expire) {
		redisTemplate.expire(key, expire, TimeUnit.DAYS);
	}
	/**
	 * 添加
	 *
	 * @param key    key
	 * @param value  值
	 * @param expire 过期时间(单位:天),传入 -1 时表示不设置过期时间
	 */
	public void putDay(String key, String value, long expire) {
		ValueOperations<String, String> operations = redisTemplate.opsForValue();
		operations.set(key, value);
		if (expire != -1) {
			redisTemplate.expire(key, expire, TimeUnit.DAYS);
		}
	}

    /**
     * 删除
     *
     * @param key 传入key的名称
     */
    public void remove(String key) {
    	redisTemplate.delete(key);
    }

    /**
     * 查询
     *
     * @param key 查询的key
     * @return
     */
    public String get(String key) {
    	ValueOperations<String, String> operations = redisTemplate.opsForValue();
        return (String) operations.get(key);
    }

}