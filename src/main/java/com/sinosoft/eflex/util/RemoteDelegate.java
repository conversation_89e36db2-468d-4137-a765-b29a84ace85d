package com.sinosoft.eflex.util;

import java.util.Map;

import javax.xml.namespace.QName;

import org.apache.axis.client.Call;
import org.slf4j.LoggerFactory;

import com.sinosoft.eflex.model.Message;

import ch.qos.logback.classic.Logger;

/**
 * Title: 远程服务调用转发接口
 * 
 * Description: 该类用于调用第三方提供的webservice服务
 * 
 * <AUTHOR>
 * 
 */
public class RemoteDelegate {

	private static Logger Log = (Logger) LoggerFactory.getLogger(RemoteDelegate.class);
	private String resultInfo = "";
	private Message mMessage = new Message();

	public static RemoteDelegate getInstance() {
		return new RemoteDelegate();
	}

	public boolean submitData(String serviceUrl, String operation, String serviceName, String requstData) {

		String targetNamespace = "http://services.webservice.sinosoft.com";
		// Log.info("请求远程服务报文：" + requstData);
		org.apache.axis.client.Service service = new org.apache.axis.client.Service();
		try {
			byte[] reqbyte = requstData.getBytes("GBK");
			Call call = (Call) service.createCall();
			call.setTimeout(new Integer(1000 * 60 * 30));
			call.setTargetEndpointAddress(new java.net.URL(serviceUrl));
			call.setOperationName(new QName(targetNamespace, operation));
			call.setUseSOAPAction(true);
			call.addParameter(new QName(targetNamespace, "cData"), org.apache.axis.encoding.XMLType.XSD_BASE64,
					javax.xml.rpc.ParameterMode.IN);
			call.setReturnType(org.apache.axis.encoding.XMLType.XSD_BASE64);
			byte[] resbyte = (byte[]) call.invoke(new Object[] { reqbyte });
			resultInfo = new String(resbyte, "GBK");
			Log.info("远程服务返回报文：" + resultInfo);
			mMessage = XmlUtil.parseResponseXml(resultInfo);
			return mMessage.isSuccess();
		} catch (Exception e) {
			Log.info("调用Webservice出错：", e);
			mMessage.setmSuccess(false);
			mMessage.setmCErrors("调用Webservice出错");
			return false;
		}
	}

	/**
	 * 请求服务返回结果
	 * 
	 * @return resultInfo
	 */
	public Map<String, Object> getResult() {
		return this.mMessage.getmResult();
	}

	/**
	 * 请求服务返回错误信息
	 * 
	 * @return errorMsg
	 */
	public String getErrorMsg() {
		return this.mMessage.getmCErrors();
	}
}
