package com.sinosoft.eflex.util;


import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 身份证验证的工具（支持15位或18位省份证）
 * 身份证号码结构：
 * 17位数字和1位校验码：6位地址码数字，8位生日数字，3位出生时间顺序号，1位校验码。
 * 地址码（前6位）：表示对象常住户口所在县（市、镇、区）的行政区划代码，按GB/T2260的规定执行。
 * 出生日期码，（第七位 至十四位）：表示编码对象出生年、月、日，按GB按GB/T7408的规定执行，年、月、日代码之间不用分隔符。
 * 顺序码（第十五位至十七位）：表示在同一地址码所标示的区域范围内，对同年、同月、同日出生的人编订的顺序号，
 * 顺序码的奇数分配给男性，偶数分配给女性。
 * 校验码（第十八位数）：
 * 十七位数字本体码加权求和公式 s = sum(Ai*Wi), i = 0,,16，先对前17位数字的权求和；
 * Ai:表示第i位置上的身份证号码数字值.Wi:表示第i位置上的加权因.Wi: 7 9 10 5 8 4 2 1 6 3 7 9 10 5 8 4 2；
 * 计算模 Y = mod(S, 11)
 * 通过模得到对应的校验码 Y: 0 1 2 3 4 5 6 7 8 9 10 校验码: 1 0 X 9 8 7 6 5 4 3 2
 */
@Slf4j
public class IDCardUtil {
    final static Map<Integer, String> zoneNum = new HashMap<>();

    static {
        zoneNum.put(11, "北京");
        zoneNum.put(12, "天津");
        zoneNum.put(13, "河北");
        zoneNum.put(14, "山西");
        zoneNum.put(15, "内蒙古");
        zoneNum.put(21, "辽宁");
        zoneNum.put(22, "吉林");
        zoneNum.put(23, "黑龙江");
        zoneNum.put(31, "上海");
        zoneNum.put(32, "江苏");
        zoneNum.put(33, "浙江");
        zoneNum.put(34, "安徽");
        zoneNum.put(35, "福建");
        zoneNum.put(36, "江西");
        zoneNum.put(37, "山东");
        zoneNum.put(41, "河南");
        zoneNum.put(42, "湖北");
        zoneNum.put(43, "湖南");
        zoneNum.put(44, "广东");
        zoneNum.put(45, "广西");
        zoneNum.put(46, "海南");
        zoneNum.put(50, "重庆");
        zoneNum.put(51, "四川");
        zoneNum.put(52, "贵州");
        zoneNum.put(53, "云南");
        zoneNum.put(54, "西藏");
        zoneNum.put(61, "陕西");
        zoneNum.put(62, "甘肃");
        zoneNum.put(63, "青海");
        zoneNum.put(64, "宁夏");
        zoneNum.put(65, "新疆");
        zoneNum.put(71, "台湾");
        zoneNum.put(81, "香港");
        zoneNum.put(82, "澳门");
        zoneNum.put(91, "外国");
    }

    private final static int[] PARITYBIT = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};
    private final static int[] POWER_LIST = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};

    /**
     * 身份证验证
     *
     * @param certNo 号码内容
     * @return 是否有效 null和"" 都是false
     */
    public static boolean isIDCard(String certNo) {
        if (certNo == null || (certNo.length() != 15 && certNo.length() != 18))
            return false;
        final char[] cs = certNo.toUpperCase().toCharArray();
        //校验位数
        int power = 0;
        for (int i = 0; i < cs.length; i++) {
            if (i == cs.length - 1 && cs[i] == 'X') {
                break;//最后一位可以 是X或x
            }
            if (cs[i] < '0' || cs[i] > '9') {
                return false;
            }
            if (i < cs.length - 1) {
                power += (cs[i] - '0') * POWER_LIST[i];
            }
        }

        //校验区位码
        if (!zoneNum.containsKey(Integer.valueOf(certNo.substring(0, 2)))) {
            return false;
        }

        //校验年份
        String year = certNo.length() == 15 ? "19" + certNo.substring(6, 8) : certNo.substring(6, 10);

        final int iyear = Integer.parseInt(year);
        //1900年的PASS，超过今年的PASS
        if (iyear < 1900 || iyear > Calendar.getInstance().get(Calendar.YEAR)) {
            return false;
        }

        //校验月份
        String month = certNo.length() == 15 ? certNo.substring(8, 10) : certNo.substring(10, 12);
        final int imonth = Integer.parseInt(month);
        if (imonth < 1 || imonth > 12) {
            return false;
        }

        //校验天数      
        String day = certNo.length() == 15 ? certNo.substring(10, 12) : certNo.substring(12, 14);
        final int iday = Integer.parseInt(day);
        if (iday < 1 || iday > 31) {
            return false;
        }

        //校验"校验码"
        if (certNo.length() == 15) {
            return true;
        }
        return cs[cs.length - 1] == PARITYBIT[power % 11];
    }


    /**
     * 判断性别
     *
     * @param id 身份证号码
     * @return "1":性别为女."0":性别为男
     */
    public static String sex(String id) {
        char[] strinigAll = id.toCharArray();
        if (id.length() == 15) {
            if (strinigAll[14] % 2 == 0) {
                return "1";
            } else {
                return "0";
            }
        } else {
            if (strinigAll[16] % 2 == 0) {
                return "1";
            } else {
                return "0";
            }
        }
    }

    /**
     * 返回出生日期
     *
     * @param idno
     * @return
     */
    public static String dateOfBirth(String idno) {
        String birthday;
        if (idno.length() == 15) {
            birthday = "19" + idno.substring(6, 8) + "-" + idno.substring(8, 10) + "-" + idno.substring(10, 12);
        } else {
            birthday = idno.substring(6, 10) + "-" + idno.substring(10, 12) + "-" + idno.substring(12, 14);
        }
        return birthday;
    }

    //校验证件号码和出生日期、xx
    public static String checkIDCard(String idNo, String sex, String birthday) {
        //定义返回错误信息
        String errorDesc = "";
        //校验身份证号
        if (IDCardUtil.isIDCard(idNo)) {
            // 经由核心、运营确认，身份证号仅能为18位 add by wudezhong 2021.5.27
            if (idNo.length() != 18) {
                errorDesc = "证件号码长度应为18位！";
            } else {
                // 判断性别
                String idcard_sex = IDCardUtil.sex(idNo);
                if (!idcard_sex.equals(sex)) {
                    errorDesc = "证件号与性别不匹配！";
                }
                // 判断出生日期
                String idcard_birthday = IDCardUtil.dateOfBirth(idNo);
                if (!idcard_birthday.equals(birthday)) {
                    if (!StringUtils.isEmpty(errorDesc)) {
                        errorDesc += "；";
                    }
                    errorDesc += "证件号与出生日期不匹配！";
                }
            }
        } else {
            errorDesc = "证件号有误！";
        }
        return errorDesc;
    }

    /**
     * 获取年龄
     *
     * @param birthday
     */
    public static int calculateAge(String birthday, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        String date = sdf.format(new Date());
        try {
            Date birthdayDate = sdf.parse(birthday);
            Date nowDate = sdf.parse(date);
            long millis = nowDate.getTime() - birthdayDate.getTime();
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(millis);
            int year = calendar.get(Calendar.YEAR);
            return year - 1970;
        } catch (ParseException parseException) {
            log.error("IdCardUtil #calculateAge ", parseException);
        }
        return 0;
    }
}
