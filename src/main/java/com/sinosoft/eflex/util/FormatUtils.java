package com.sinosoft.eflex.util;

public class FormatUtils {

    /**
     * 格式化保单号（前5后5，中间3个*）
     */
    public static String formatPolicyNo(String policyNo) {
        return formatMiddleAsterisk(policyNo, 3, 3);
    }

    /**
     * 格式化投保单号（前6后6，中间3个*）
     */
    public static String formatProposalNo(String proposalNo) {
        return formatMiddleAsterisk(proposalNo, 6, 6);
    }

    /**
     * 格式化保全号（前5后5，中间3个*）
     */
    public static String formatPreservationNo(String preservationNo) {
        return formatMiddleAsterisk(preservationNo, 5, 5);
    }

    /**
     * 格式化报案号（前4后4，中间3个*）
     */
    public static String formatReportNo(String reportNo) {
        return formatMiddleAsterisk(reportNo, 4, 4);
    }

    /**
     * 通用方法：中间用 *** 替换
     * @param str 原始字符串
     * @param prefixLen 前几位显示
     * @param suffixLen 后几位显示
     */
    private static String formatMiddleAsterisk(String str, int prefixLen, int suffixLen) {
        if (str == null || str.length() < (prefixLen + suffixLen)) {
            return str; // 如果长度不足，直接返回原字符串
        }
        String prefix = str.substring(0, prefixLen);
        String suffix = str.substring(str.length() - suffixLen);
        return prefix + "***" + suffix;
    }

    /**
     * 格式化手机号（第6位替换为*）
     */
    public static String formatPhone(String phone) {
        if (phone == null || phone.length() < 6) {
            return phone;
        }
        return phone.substring(0, 5) + "*" + phone.substring(6);
    }

    /**
     * 格式化热线号码（去掉 "-"）
     */
    public static String formatHotline(String hotline) {
        if (hotline == null) {
            return null;
        }
        return hotline.replaceAll("-", "");
    }

    /**
     * 格式化日期（"-" 或 "/" 替换为 "年月日"）
     */
    public static String formatDate(String date) {
        if (date == null) {
            return null;
        }
        String[] parts = date.split("[-/]");
        if (parts.length != 3) {
            return date; // 如果不是标准日期格式，直接返回
        }
        return parts[0] + "年" + Integer.parseInt(parts[1]) + "月" + Integer.parseInt(parts[2]) + "日";
    }

    // 示例用法
    public static void main(String[] args) {
        // 保单号（前5后5）
        System.out.println(formatPolicyNo("P442530052")); // P4425***30052

        // 投保单号（前6后6）
        System.out.println(formatProposalNo("A11135126241")); // A11135***126241

        // 保全号（前5后5）
        System.out.println(formatPreservationNo("3P97200003")); // 3P972***00003

        // 报案号（前4后4）
        System.out.println(formatReportNo("544301")); // 544***301

        // 手机号（第6位替换为*）
        System.out.println(formatPhone("13410226704")); // 13410*226704

        // 热线号码（去掉 "-"）
        System.out.println(formatHotline("400-69-12346")); // 4006912346

        // 日期（替换为 "年月日"）
        System.out.println(formatDate("2024-11-03")); // 2024年11月3日
        System.out.println(formatDate("2024/11/03")); // 2024年11月3日
    }
}