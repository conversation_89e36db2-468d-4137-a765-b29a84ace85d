package com.sinosoft.eflex.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @DESCRIPTION
 * @create 2018-08-09 10:38
 **/
public class CommonUtil {

    private static final Logger log = LoggerFactory.getLogger(CommonUtil.class);

    /**
     * 判断是否为正整数 是返回true 否则返回false
     *
     * @param string
     * @return
     */
    public static boolean isPureDigital(String string) {
        String regEx1 = "\\d+";
        Pattern p;
        Matcher m;
        p = Pattern.compile(regEx1);
        m = p.matcher(string);
        if (m.matches()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断是否为0-356的整数 是返回true 否则返回false
     *
     * @param string
     * @return
     */
    public static boolean isPureDigitals(String string) {
        String regEx1 = "[0-9]|[1-9][0-9]|[12][0-9][0-9]|3(?:[0-5][0-9]|6[0-5])";
        Pattern p;
        Matcher m;
        p = Pattern.compile(regEx1);
        m = p.matcher(string);
        if (m.matches()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 两个Double数相加 v1+v2
     */
    public static Double add(Double v1, Double v2) {
        BigDecimal b1 = new BigDecimal(v1.toString());
        BigDecimal b2 = new BigDecimal(v2.toString());
        return b1.add(b2).doubleValue();
    }

    /**
     * 两个Double数相减 v1-v2
     */
    public static Double sub(Double v1, Double v2) {
        BigDecimal b1 = new BigDecimal(v1.toString());
        BigDecimal b2 = new BigDecimal(v2.toString());
        return b1.subtract(b2).doubleValue();
    }

    /**
     * 两个Double数相乘 v1*v2
     */
    public static Double mul(Double v1, Double v2) {
        BigDecimal b1 = new BigDecimal(v1.toString());
        BigDecimal b2 = new BigDecimal(v2.toString());
        return b1.multiply(b2).doubleValue();
    }

    /**
     * 两个Double数相除，并保留scale位小数 v1 ÷ v2
     */
    public static Double mul(Double v1, Double v2, int scale) {
        BigDecimal b1 = new BigDecimal(v1.toString());
        BigDecimal b2 = new BigDecimal(v2.toString());
        return b1.multiply(b2).setScale(scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 两个Double数相除，并保留scale位小数 v1 ÷ v2
     */
    public static Double div(Double v1, Double v2, int scale) {
        if ((v1 != null && v2 != null) && (v1 == 0 || v2 == 0)) {
            return 0.0;
        }
        BigDecimal b1 = new BigDecimal(v1.toString());
        BigDecimal b2 = new BigDecimal(v2.toString());
        return b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 数据库表通用字段赋值 makeDate ， makeTime ， modifyDate ，modifyTime
     *
     * @param obj
     * @return
     */
    public static <T> T initObject(T obj, String operationType) {
        Class<?> objClass = obj.getClass();
        try {
            String date = DateTimeUtil.getCurrentDate();
            String time = DateTimeUtil.getCurrentTime();
            if ("INSERT".equalsIgnoreCase(operationType) || "ADD".equalsIgnoreCase(operationType)) {
                Method setMakeDate = objClass.getMethod("setMakeDate", String.class);
                setMakeDate.invoke(obj, date);
                Method setMakeTime = objClass.getMethod("setMakeTime", String.class);
                setMakeTime.invoke(obj, time);
            }
            if ("INSERT".equalsIgnoreCase(operationType) || "ADD".equalsIgnoreCase(operationType) || "UPDATE".equalsIgnoreCase(operationType)) {
                Method setModifyDate = objClass.getMethod("setModifyDate", String.class);
                setModifyDate.invoke(obj, date);
                Method setModifyTime = objClass.getMethod("setModifyTime", String.class);
                setModifyTime.invoke(obj, time);
            }
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            log.error("initObject error", e);
        }
        return obj;
    }

    public static String mappingPlanObject(String name) {
        if ("员工".equals(name)) {
            return "1";
        } else if ("家属".equals(name)) {
            return "2";
        } else {
            return "3";
        }
    }

    /**
     * 生成UUID
     *
     * @return
     */
    public static String getUUID() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    public static boolean isNumeric(String str) {
        String reg = "^[0-9]+(.[0-9]+)?$";
        return str.matches(reg);
    }
}
