package com.sinosoft.eflex.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Calendar;
import java.nio.file.Files;

import org.apache.commons.io.IOUtils;
import org.springframework.web.multipart.MultipartFile;

import com.sinosoft.eflex.config.SysConst;

public class FileUtil {
	
	/**
	 * 生成文件目录，生成规则：
	 * AppFileRoot + Year + Month + Day + DocType
	 * 如：  D:/uploadfile/2018/9/6/0101/
	 * @param docType 文件类型
	 * @return 文件目录，以'/'结束
	 */
	public static String getLocalPath(String docType) {
		//系统文件默认根目录
		StringBuilder sbDirPath = new StringBuilder();
		sbDirPath.append(SysConst.AppFileRoot);
		Calendar cal = Calendar.getInstance();
		int year = cal.get(Calendar.YEAR);
		int month = cal.get(Calendar.MONTH)+1;
		int day = cal.get(Calendar.DAY_OF_MONTH);
		sbDirPath.append(year).append("/")
				 .append(month).append("/")
				 .append(day).append("/")
				 .append(docType).append("/");
		File dir = new File(sbDirPath.toString());
		if(! dir.exists()) {
			dir.mkdirs();
		}
		return sbDirPath.toString();
	}
	public static String getLocalPath(String docType,String batch) {
		//系统文件默认根目录
		StringBuilder sbDirPath = new StringBuilder();
		sbDirPath.append(SysConst.AppFileRoot);
		Calendar cal = Calendar.getInstance();
		int year = cal.get(Calendar.YEAR);
		int month = cal.get(Calendar.MONTH)+1;
		int day = cal.get(Calendar.DAY_OF_MONTH);
		sbDirPath.append(year).append("/")
		.append(month).append("/")
		.append(day).append("/")
		.append(docType).append("/")
		.append(batch).append("/");
		File dir = new File(sbDirPath.toString());
		if(! dir.exists()) {
			dir.mkdirs();
		}
		return sbDirPath.toString();
	}
	
	/**
	 * 生成文件目录，生成规则：
	 * FtpFileRoot + Year + Month + Day + DocType
	 * 如：  D:/uploadfile/2018/9/6/0101/
	 * @param docType 文件类型
	 * @return 文件目录，以'/'结束
	 */
	public static String getFtpPath(String docType, String ftpRootPath) {
		//系统文件默认根目录
		StringBuilder sbDirPath = new StringBuilder();
		sbDirPath.append(ftpRootPath);
		Calendar cal = Calendar.getInstance();
		int year = cal.get(Calendar.YEAR);
		int month = cal.get(Calendar.MONTH)+1;
		int day = cal.get(Calendar.DAY_OF_MONTH);
		sbDirPath.append(year).append("/")
				 .append(month).append("/")
				 .append(day).append("/")
				 .append(docType).append("/");
		File dir = new File(sbDirPath.toString());
		if(! dir.exists()) {
			dir.mkdirs();
		}
		return sbDirPath.toString();
	}

	//删除文件夹
	public static void delete(File file) {
		if(!file.exists()) return;
		
		if(file.isFile() || file.list()==null) {
			file.delete();
			System.out.println("删除单个文件："+file.getName());
		}else {
			File[] files = file.listFiles();
			for(File a:files) {
				delete(a);					
			}
			file.delete();
			System.out.println("删除目录文件夹："+file.getName());
		}
		
	}
	public static String getLocalPath(String docType,String orderItemNo,String relation) {
		//系统文件默认根目录
		StringBuilder sbDirPath = new StringBuilder();
		sbDirPath.append(SysConst.AppFileRoot);
		sbDirPath.append("dailyFileUpload").append("/")
				 .append(orderItemNo).append("/")
				 .append(relation).append("/")
				 .append(docType).append("/");
		File dir = new File(sbDirPath.toString());
		if(! dir.exists()) {
			dir.mkdirs();
		}
		return sbDirPath.toString();
	}
	public static String getFtpPath(String ftprootpath,String docType,String orderItemNo,String relation) {
		//系统文件默认根目录
		StringBuilder sbDirPath = new StringBuilder();
		sbDirPath.append(ftprootpath);
		sbDirPath.append("dailyFileUpload").append("/")
		.append(orderItemNo).append("/")
		.append(relation).append("/")
		.append(docType).append("/");
		File dir = new File(sbDirPath.toString());
		if(! dir.exists()) {
			dir.mkdirs();
		}
		return sbDirPath.toString();
	}
	public static String getLocalPathForFtp(String docType,String orderItemNo,String relation) {
		//系统文件默认根目录
		StringBuilder sbDirPath = new StringBuilder();
		sbDirPath.append("dailyFileUpload").append("/")
		.append(orderItemNo).append("/")
		.append(relation).append("/")
		.append(docType).append("/");
		File dir = new File(sbDirPath.toString());
		if(! dir.exists()) {
			dir.mkdirs();
		}
		return sbDirPath.toString();
	}
	public static void copy(String oldpath, String newpath) throws IOException {
	        File oldpaths = new File(oldpath);
	        File newpaths = new File(newpath);
	        if (!newpaths.exists()) {
	            Files.copy(oldpaths.toPath(), newpaths.toPath());
	        } else {
	            newpaths.delete();
	            Files.copy(oldpaths.toPath(), newpaths.toPath());
	        }
	    }

	
}
