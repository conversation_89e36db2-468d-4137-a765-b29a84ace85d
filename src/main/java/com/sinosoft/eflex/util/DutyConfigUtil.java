package com.sinosoft.eflex.util;

import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.model.FcDutyGradeOptionalAmountInfo;
import com.sinosoft.eflex.model.FcDutyGroupDeductible;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Array;
import java.math.BigDecimal;
import java.util.*;

/**
 * Created by 苍山 on 2019/6/27.
 */
@Data
public class DutyConfigUtil {
    //一类险种  需设置必选责任保额档次
    private List<String> oneRiskList = Arrays.asList("12020","15030","16040","16490");
    //二类险种 需设置必选责任保额档次、选择和设置可选责任保额档次
    private List<String> twoRiskList = Arrays.asList("15070");
    //三类险种	需设置必选责任保额档次、险种免赔额、赔付比例
    private List<String> threeRiskList = Arrays.asList("17030","15040","17010","17050");
    //四类险种（含有津贴的险种）  需设置必选责任保额档次、选择和设置可选责任保额档次、最大赔付天数
    private List<String> fourRiskList = Arrays.asList("15060","17020");

    public List<String> getFourRiskList() {
        return fourRiskList;
    }

    public String RiskCheck(Map<String,Object> map){
        String error = "";
        String regex = "^(([1-9]{1}\\d*)|(0{1}))(\\.\\d{0,1})?$";
        String riskCode = String.valueOf(map.get("riskCode"));
        double discountRatio = Double.parseDouble(String.valueOf(map.get("discountRatio")));
        if (!(discountRatio >= 0 && discountRatio <= 100) || !String.valueOf(map.get("discountRatio")).matches("^\\d+$")){
            error = "折扣比例应为0-100之间的整数";
            return error;
        }
        if (oneRiskList.contains(riskCode)){
            double Amnt = Double.parseDouble(String.valueOf(map.get("amnt")));
            if (!String.valueOf(map.get("amnt")).matches(regex) || Amnt == 0){
                error = "保额需大于0且只能有一位小数";
                return error;
            }
        }else if (twoRiskList.contains(riskCode)){
            //校验必选责任
            String manDutyCode = String.valueOf(map.get("dutyCode"));
            double Amnt = Double.parseDouble(String.valueOf(map.get("amnt")));
            double number = Amnt % 10;
            if ("GD0050".equals(manDutyCode) || "GD0051".equals(manDutyCode) || "GD0052".equals(manDutyCode) || "GD0053".equals(manDutyCode) || "GD0054".equals(manDutyCode)){
                if (!String.valueOf(map.get("amnt")).matches(regex) || Amnt == 0){
                    error = "必选责任保额需大于0且只能有一位小数";
                    return error;
                }
            }else if ("GD0029".equals(manDutyCode) || "GD0032".equals(manDutyCode)){
                if (!(Amnt >= 10) ||  number != 0){
                    error = "必选责任保额最低为10元每天且以10递增";
                    return error;
                }
            }

            Object object = map.get("optDutyInfoList");
            List<Map<String,String>> optDutyInfoList = (List<Map<String,String>>)object;
            //检验可选责任
            for (Map optDutyInfo:optDutyInfoList){
                FcDutyGradeOptionalAmountInfo fcDutyGradeOptionalAmountInfo = JSON.parseObject(JSON.toJSONString(optDutyInfo), FcDutyGradeOptionalAmountInfo.class);
                String optDutyCode = fcDutyGradeOptionalAmountInfo.getOptDutyCode();
                if (StringUtils.isNotBlank(optDutyCode)){
                    double optAmnt =  fcDutyGradeOptionalAmountInfo.getAmnt();
                    double optNumber =  optAmnt%10;
                    if ("GD0030".equals(optDutyCode) || "GD0033".equals(optDutyCode) || "GD0034".equals(optDutyCode)){
                        if (!(optAmnt >= 10) || optNumber != 0){
                            error = "可选责任保额最低为10元每天且以10递增";
                            return error;
                        }
                    }else if("GD0035".equals(optDutyCode)){
                        optNumber = optAmnt%1000;
                        if (!(optAmnt >= 5000) || optNumber != 0){
                            error = "手术医疗保额最低为5000元且以千元递增";
                            return error;
                        }
                    }else if ("GD0055".equals(optDutyCode)  || "GD0056".equals(optDutyCode) || "GD0057".equals(optDutyCode) || "GD0058".equals(optDutyCode) || "GD0059".equals(optDutyCode)){
                        if (!String.valueOf(optAmnt).matches(regex) || optAmnt == 0){
                            error = "可选责任保额需大于0且只能有一位小数";
                            return error;
                        }
                    }
                }
            }
        }else if (threeRiskList.contains(riskCode)){
            String Amnt = String.valueOf(map.get("amnt"));
            if ("17030".equals(riskCode) || "17010".equals(riskCode)){
                if(!Arrays.asList("0.2","0.3","0.4","0.5","0.6","0.7","0.8","0.9","1","2","3").contains(Amnt)){
                    error = "险种编码为:"+riskCode+"的险种保额只可录入0.2、0.3、0.4、0.5、0.6、0.7、0.8、0.9、1、2、3（单位：万元）";
                    return error;
                }
            }else if ("17050".equals(riskCode)){
                if(!Arrays.asList("100","200","400").contains(Amnt)){
                    error = "尊享团体补充医疗保险保额只可录入100、200、400（单位：万元）";
                    return error;
                }

            }else if ("15040".equals(riskCode)){
                if (!String.valueOf(Amnt).matches(regex) || Double.parseDouble(Amnt) == 0){
                    error = "可选责任保额需大于0且只能有一位小数";
                    return error;
                }
            }
            return error;
        } else if (fourRiskList.contains(riskCode)){
            //校验必选责任
            String manDutyCode = String.valueOf(map.get("dutyCode"));
            double Amnt = Double.parseDouble(String.valueOf(map.get("amnt")));
            double number = Amnt % 10;
            if ("GD0050".equals(manDutyCode) || "GD0051".equals(manDutyCode) || "GD0052".equals(manDutyCode) || "GD0053".equals(manDutyCode) || "GD0054".equals(manDutyCode)){
                if (!String.valueOf(map.get("amnt")).matches(regex) || Amnt == 0){
                    error = "必选责任保额需大于0且只能有一位小数";
                    return error;
                }
            }else if ("GD0029".equals(manDutyCode) || "GD0032".equals(manDutyCode)){
                if (!(Amnt >= 10) ||  number != 0){
                    error = "必选责任保额最低为10元每天且以10递增";
                    return error;
                }
            }

            Object object = map.get("optDutyInfoList");
            List<Map<String,String>> optDutyInfoList = (List<Map<String,String>>)object;
            //检验可选责任
            for (Map optDutyInfo:optDutyInfoList){
                FcDutyGradeOptionalAmountInfo fcDutyGradeOptionalAmountInfo = JSON.parseObject(JSON.toJSONString(optDutyInfo), FcDutyGradeOptionalAmountInfo.class);
                String optDutyCode = fcDutyGradeOptionalAmountInfo.getOptDutyCode();
                if (StringUtils.isNotBlank(optDutyCode)){
                    double optAmnt =  fcDutyGradeOptionalAmountInfo.getAmnt();
                    double optNumber =  optAmnt%10;
                    if ("GD0030".equals(optDutyCode) || "GD0033".equals(optDutyCode) || "GD0034".equals(optDutyCode)){
                        if (!(optAmnt >= 10) || optNumber != 0){
                            error = "可选责任保额最低为10元每天且以10递增";
                            return error;
                        }
                    }else if("GD0035".equals(optDutyCode)){
                        optNumber = optAmnt%1000;
                        if (!(optAmnt >= 5000) || optNumber != 0){
                            error = "手术医疗保额最低为5000元且以千元递增";
                            return error;
                        }
                    }else if ("GD0055".equals(optDutyCode)  || "GD0056".equals(optDutyCode) || "GD0057".equals(optDutyCode) || "GD0058".equals(optDutyCode) || "GD0059".equals(optDutyCode)){
                        if (!String.valueOf(optAmnt).matches(regex) || optAmnt == 0){
                            error = "可选责任保额需大于0且只能有一位小数";
                            return error;
                        }
                    }
                }
            }
        }else {
            error = "当前险种未配置，请联系平台维护人员进行相关配置。";
            return error;
        }
        return error;
    }

}
