package com.sinosoft.eflex.util;

import java.awt.AlphaComposite;
import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.RenderingHints;
import java.awt.Transparency;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.imageio.ImageIO;
import javax.swing.ImageIcon;

//import com.sun.image.codec.jpeg.JPEGCodec;
//import com.sun.image.codec.jpeg.JPEGImageEncoder;
import org.slf4j.LoggerFactory;

import com.sinosoft.eflex.config.SysConst;
import com.sinosoft.eflex.model.FDFTPInfo;

import ch.qos.logback.classic.Logger;

public class CreateWaterMarkPdf {
	private static Logger log = (Logger) LoggerFactory.getLogger(CreateWaterMarkPdf.class);

	/**
	 * 设置文字水印
	 * 
	 * @param sourceImg 源图片路径（fcfiledocmain模板文件路径）
	 * @param targetImg 保存的图片路径（保存到服务器，同时需上传至SFTP服务器）
	 * @param watermark 文字水印内容（1、税收居民文件：个人保单号，fcorderitem的contNo字段；2、投保提示书：传空）
	 * @param watermarkUrl 图片水印内容（主被保人签名图片服务器路径）
	 * @param type 文件类型，税收：1；投保提示书：2
	 * @throws IOException
	 */
	public static void addWatermark(String sourceImg, String targetImg, String watermark, Map<String, String> watermarkUrl, String type,FDFTPInfo fdftpInfo)
			throws IOException {
		Font font = new Font("宋体", Font.PLAIN, 40);// 字体
		File srcImgFile = new File(sourceImg);
		Image srcImg = ImageIO.read(srcImgFile);
		int srcImgWidth = srcImg.getWidth(null);
		int srcImgHeight = srcImg.getHeight(null);
		BufferedImage bufImg = new BufferedImage(srcImgWidth, srcImgHeight, BufferedImage.TYPE_INT_RGB);
		
		/***设置文字水印***/
		Graphics2D gText = bufImg.createGraphics();
		gText.drawImage(srcImg, 0, 0, srcImgWidth, srcImgHeight, null);
		gText.setColor(Color.BLACK);
		gText.setFont(font);
		// 设置水印的坐标
		if(type.equals("1")) {
			gText.drawString(watermark, 1730, 660); // 加水印
			gText.drawString(getTimeString("             "), 1430, 3140); // 加水印
		}else if(type.equals("2")) {
			gText.drawString(getTimeString("          "), 1730, 3020); // 加水印
		}
		
		/***设置图片水印***/
		if(type.equals("1")) {
			ImageIcon imgIcon = new ImageIcon(resize(watermarkUrl.get("insuredSign"),fdftpInfo));
			Image con = imgIcon.getImage();
			float clarity = 0.6f;// 透明度
			gText.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, clarity));
			gText.drawImage(con, 500, 600, null);// 水印的位置
			gText.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER));
			
			ImageIcon imgIcon2 = new ImageIcon(resize(watermarkUrl.get("mainInsuredSign"),fdftpInfo));
			Image con2 = imgIcon2.getImage();
			float clarity2 = 0.6f;// 透明度
			gText.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, clarity2));
			gText.drawImage(con2, 630, 3070, null);// 水印的位置
			gText.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER));
		}else if (type.equals("2")) {
			ImageIcon imgIcon2 = new ImageIcon(resize(watermarkUrl.get("mainInsuredSign"),fdftpInfo));
			Image con2 = imgIcon2.getImage();
			float clarity2 = 0.6f;// 透明度
			gText.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, clarity2));
			gText.drawImage(con2, 600, 2950, null);// 水印的位置
			gText.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER));
		}
		gText.dispose();
		// 输出图片
		FileOutputStream outImgStream = new FileOutputStream(targetImg);
		ImageIO.write(bufImg, "jpg", outImgStream);
		log.info("添加水印完成");
		outImgStream.flush();
		outImgStream.close();
		//上传至文件服务器
		SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(), fdftpInfo.getUsername(),fdftpInfo.getPassword());
		sFtp.uploadFile(targetImg.replace(SysConst.AppFileRoot, fdftpInfo.getFtprootpath()), targetImg);
		sFtp.close();
		FileUtil.delete(new File(targetImg));
	}
	//压缩图片
	public static BufferedImage resize(String sourceImg,FDFTPInfo fdftpInfo) throws IOException{
		String tarImg = sourceImg.replace(fdftpInfo.getFtprootpath(), SysConst.AppFileRoot);
		File file=new File(tarImg);
		File fileParent = file.getParentFile();
		if(!fileParent.exists()){
			fileParent.mkdirs();
		}
		file.createNewFile();
		SFtpClientUtil sFtp = new SFtpClientUtil(fdftpInfo.getIpaddress(), fdftpInfo.getPort(), fdftpInfo.getUsername(), fdftpInfo.getPassword());
		sFtp.download(sourceImg, tarImg);
		sFtp.close();
		File srcImgFile = new File(tarImg);
		Image srcImg = ImageIO.read(srcImgFile);
		int srcImgWidth = srcImg.getWidth(null);
		int srcImgHeight = srcImg.getHeight(null);
		BufferedImage bufImg = new BufferedImage(srcImgWidth, srcImgHeight, BufferedImage.TYPE_INT_RGB);
		
        int imgWidth = bufImg.getWidth();
        int imgHeight = 120;
        
        //构建新的图片
        BufferedImage resizedImg = new BufferedImage(imgWidth,imgHeight,BufferedImage.TYPE_INT_RGB);
        //将原图放大或缩小后画下来:并且保持png图片放大或缩小后背景色是透明的而不是黑色
        Graphics2D resizedG = resizedImg.createGraphics();
        resizedImg = resizedG.getDeviceConfiguration().createCompatibleImage(imgWidth,imgHeight,Transparency.TRANSLUCENT);
        resizedG.dispose();
        resizedG = resizedImg.createGraphics();
        Image from = srcImg.getScaledInstance(imgWidth, imgHeight, srcImg.SCALE_AREA_AVERAGING);
        resizedG.drawImage(from, 0, 0, null);
        resizedG.dispose();           
        return resizedImg;
    }	
	 /**
     * 获取图片宽度和高度
     * 
     * @return 返回图片的宽度
     */
    public static int[] getImgWidthHeight(File file) {
        InputStream is = null;
        BufferedImage src = null;
        int result[] = { 0, 0 };
        try {
            // 获得文件输入流
            is = new FileInputStream(file);
            // 从流里将图片写入缓冲图片区
            src = ImageIO.read(is);
            result[0] =src.getWidth(null); // 得到源图片宽
            result[1] =src.getHeight(null);// 得到源图片高
            is.close();  //关闭输入流
        } catch (Exception ef) {
            ef.printStackTrace();
        }

        return result;
    }
	
	public static String getTimeString(String blank) {
		SimpleDateFormat df = new SimpleDateFormat("yyyy"+blank+"MM"+blank+"dd");
		Date today = new Date();
		String todayStr = df.format(today);
		return todayStr;
	}
}
