package com.sinosoft.eflex.util;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> cat）~~~
 */
@Deprecated
public class ResultUtil {

    //返回成功
    public static Map<String,Object> success(String message){
        Map<String,Object> map = new HashMap<>();
        map.put("success",true);
        map.put("code","200");
        map.put("message",message);
        return map;
    }

    //返回数据
    public static Map<String,Object> success(String message,Object data){
        Map<String,Object> map = new HashMap<>();
        map.put("success",true);
        map.put("code","200");
        map.put("message",message);
        map.put("data",data);
        return map;
    }

    public static Map<String,Object> success(String message,Object data,String code){
        Map<String,Object> map = new HashMap<>();
        map.put("success",true);
        map.put("code",code);
        map.put("message",message);
        map.put("data",data);
        return map;
    }

    //出错500
    public static Map<String,Object> error(String message){
        Map<String,Object> map = new HashMap<>();
        map.put("success",false);
        map.put("code","500");
        map.put("message",message);
        return map;
    }

    //自定义错误
    public static Map<String,Object> error(String message,String code){
        Map<String,Object> map = new HashMap<>();
        map.put("success",false);
        map.put("code",code);
        map.put("message",message);
        return map;
    }

    //自定义错误
    public static Map<String,Object> error(Object message,String code){
        Map<String,Object> map = new HashMap<>();
        map.put("success",false);
        map.put("code",code);
        map.put("message",message);
        return map;
    }
}
