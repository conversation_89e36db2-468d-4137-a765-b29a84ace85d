package com.sinosoft.eflex.util;/**
 * @DESCRIPTION
 * @create 2018-11-19 11:06
 **/

/**
 * @ClassName: ConstantUtil
 * @Auther: hhw
 * @Date: 2018/11/19 11:06
 * @Version: 1.0
 */
@Deprecated
public class ConstantUtil {

    /*************** EnsureState 福利定制状态*********************************/
    // 未定制
    public static String EnsureState_0 = "0";
    //待后台审核
    public static String EnsureState_02 = "02";
    //后台审核中
    public static String EnsureState_03 = "03";
    //退回HR定制 （固定及弹性）
    public static String EnsureState_04 = "04";
    //待复核确认
    public static String EnsureState_05 = "05";
    //复核确认中
    public static String EnsureState_06 = "06";
    //后台定制中
    public static String EnsureState_07 = "07";
    //待HR定制
    public static String EnsureState_08 = "08";
    //HR定制中
    public static String EnsureState_09 = "09";
    //HR退回后台定制 （弹性）
    public static String EnsureState_010 = "010";
    //复核退回后台定制（固定及弹性）
    public static String EnsureState_011 = "011";
    //定制完成（复核完成）
    public static String EnsureState_1 = "1";
    //日常计划初审定制中
    public static String EnsureState_012 = "012";
    //代收费签单
    public static String EnsureState_014 = "014";
    //日常计划签单成功 已承保
    public static String EnsureState_015 = "015";
    //复核退回 日常计划
    public static String EnsureState_017 = "017";
    //已提交核心 日常计划
    public static String EnsureState_020 = "020";
    //承保失败 日常计划
    public static String EnsureState_018 = "018";

    /******************* PolicyState 福利表保单状态**************************/
    // 投保中
    public static String PolicyState_1 = "1";

    /******************* Sex 性别 ********************************************/
    public static String Sex_0 = "男";
    public static String Sex_1 = "女";

    /******************* 员工与家属关系 ********************************************/
    public static String relation_0 = "0";
    /****************员工类型*****************************************************/
    //员工
    public static  String CustomType_1="1";
    //hr
    public static  String CustomType_2="2";


}
