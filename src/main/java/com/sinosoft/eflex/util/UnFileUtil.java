package com.sinosoft.eflex.util;

import java.io.*;

import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;


import ch.qos.logback.classic.Logger;
import com.sinosoft.eflex.service.FileService;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.tools.zip.ZipEntry;

import org.apache.tools.zip.ZipFile;
import org.slf4j.LoggerFactory;

public class UnFileUtil {

	// 日志
	private static Logger Log = (Logger) LoggerFactory.getLogger(FileService.class);
	
	public static void unZipFiles(File zipFile, String descDir) throws IOException {
		//创建解压后的文件
		FileInputStream fileInputStream = new FileInputStream(zipFile);
		BufferedInputStream bufferedInputStream = new BufferedInputStream(fileInputStream);
		ZipArchiveInputStream inputStream = new ZipArchiveInputStream(bufferedInputStream,"GBK");
		try{
		    //修改压缩包文件存储的路径
            String unZipFileSavePath = zipFile.getPath().substring(0,zipFile.getPath().lastIndexOf(".")).concat("/");
			File pathFile = new File(unZipFileSavePath);
			if (!pathFile.exists()) {
				pathFile.mkdirs();
			}
			ZipArchiveEntry entry = null;
			while ((entry = inputStream.getNextZipEntry()) != null) {
				String entryName = entry.getName();
				if (entry.isDirectory()) {
					File directory = new File(unZipFileSavePath, entryName);
					directory.mkdirs();
				} else {
					OutputStream os = null;
					try {
						File entryPath = new File(unZipFileSavePath, entryName);
						File path = new File(entryPath.getParent());
						if (!path.exists()) {
							path.mkdirs();
						}
						os = new BufferedOutputStream(new FileOutputStream(new File(unZipFileSavePath, entryName)));
						//输出文件路径信息
						Log.info("解压文件的当前路径为:{}", unZipFileSavePath + entryName);
						IOUtils.copy(inputStream, os);
					} finally {
						IOUtils.closeQuietly(os);
					}
				}
			}
			final File[] files = pathFile.listFiles();
			if (files != null && files.length == 1 && files[0].isDirectory()) {
				// 说明只有一个文件夹
				FileUtils.copyDirectory(files[0], pathFile);
				//免得删除错误， 删除的文件必须在/data/demand/目录下。
				boolean isValid = files[0].getPath().contains("/data/www/");
				if (isValid) {
					FileUtils.forceDelete(files[0]);
				}
			}
			Log.info("******************解压完毕********************");
		}catch (Exception e){
			e.printStackTrace();
		}finally {
			if(inputStream!=null) {
				inputStream.close();
			}
			if(bufferedInputStream!=null) {
				bufferedInputStream.close();
			}
			if(fileInputStream!=null) {
				fileInputStream.close();
			}
		}

		/*ZipFile zip = new ZipFile(zipFile,"GBK");
		for (Enumeration entries = zip.getEntries(); entries.hasMoreElements();) {
			ZipEntry entry = (ZipEntry) entries.nextElement();
			String zipEntryName = entry.getName();
			InputStream in = zip.getInputStream(entry);
			String outPath = (descDir + zipEntryName).replaceAll("\\*", "/");
			// 判断路径是否存在,不存在则创建文件路径
			File file = new File(outPath.substring(0, outPath.lastIndexOf('/')));
			if (!file.exists()) {
				file.mkdirs();
			}
			// 判断文件全路径是否为文件夹,如果是上面已经上传,不需要解压
			if (new File(outPath).isDirectory()) {
				continue;
			}
			// 输出文件路径信息
			System.out.println(outPath);
			//写入文件
			OutputStream out = new FileOutputStream(outPath);
			byte[] buf1 = new byte[1024];
			int len;
			while ((len = in.read(buf1)) > 0) {
				out.write(buf1, 0, len);
			}
			in.close();
			out.close();
		}*/
	}

	private static ZipArchiveInputStream getZipFile(File zipFile) throws Exception {
		return new ZipArchiveInputStream(new BufferedInputStream(new FileInputStream(zipFile)),"GBK");
	}

	// 判断文件夹是否存在
	public static void judeDirExists(File file) {
		if (file.exists()) {
			if (file.isDirectory()) {
				System.out.println("目录存在");
	        } else {
	            System.out.println("存在同名文件，无法创建目录");
	        }
	    } else {
	    	System.out.println("目录不存在，开始创建");
	        file.mkdir();
	    }
   }
	/**
	 * 调用WINRAR解压RAR文件
	 */
	public static void unRarFile(String inputFilePath,String outputFilePath) {
		judeDirExists(new File(outputFilePath));
		Process process = null;
		BufferedReader input = null;
		String cmd = "C:/Program Files (x86)/WinRAR/WinRAR.exe  X -o+ " + inputFilePath + " " + outputFilePath;
		try {
			process = Runtime.getRuntime().exec(cmd);
			printMessage(process.getInputStream());
			printMessage(process.getErrorStream());
			int value = process.waitFor();
			// 记录执行命令是否成功
			if (value == 0) {
				System.out.println("WinRAR exec success:" + cmd);
			} else {
				System.out.println("WinRAR exec failure:" + cmd);
			}
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println("WinRAR exec failure:"+e);
		} finally {
			if (input != null) {
				try {
					input.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}
    /**
	 * 打印命令执行的结果
	 * 
	 * @param input
	 */
	private static void printMessage(final InputStream input) {
		new Thread(new Runnable() {
			public void run() {
				InputStreamReader reader = new InputStreamReader(input);
				BufferedReader bf = new BufferedReader(reader);
				String line = null;
				try {
					while ((line = bf.readLine()) != null) {
						System.out.println("WinRAR/WinRAR.exe:" + line);
					}
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}).start();
	}
}
