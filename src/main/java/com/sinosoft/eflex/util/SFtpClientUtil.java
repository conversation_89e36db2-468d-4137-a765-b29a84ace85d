package com.sinosoft.eflex.util;

import com.jcraft.jsch.*;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.model.FCGrpInfo;
import com.sinosoft.eflex.model.FCStaffFamilyRela;
import com.sinosoft.eflex.model.HrRegist;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.util.List;
import java.util.Map;

/**
 * @Title:SFTP工具类
 * @Description: SFTP连接管理工具 (使用jar包 jsch-0.1.24.jar )
 * <p>
 * 功能：通过SFTP服务器实现文件的上传下载功能
 * @author:
 */
@Slf4j
public class SFtpClientUtil {

    private ChannelSftp sftp = null;
    private final String serverIP;
    private final int port;
    private final String userName;
    private final String userPassword;

    public SFtpClientUtil(String serverIP, int port, String userName, String userPassword) {
        this.serverIP = serverIP;
        this.port = port;
        this.userName = userName;
        this.userPassword = userPassword;
    }

    public MyProps myProps;

    /**
     * 获取SFTP连接
     *
     * @return
     */
    public boolean open() {
        String info = "serverIP:" + serverIP + "  userName:" + userName + "  pwd:" + userPassword;
        try {
            if (sftp != null && sftp.isConnected()) {
                return true;
            }
            JSch j = new JSch();
            Session ssh = j.getSession(userName, serverIP, port);
            ssh.setConfig("userauth.gssapi-with-mic", "no");
            ssh.setConfig("StrictHostKeyChecking", "no");
            ssh.setPassword(userPassword);
            ssh.connect();
            Channel channel = ssh.openChannel("sftp");
            channel.connect();
            sftp = (ChannelSftp) channel;
            log.info("获取SFTP连接成功-" + info);
        } catch (Exception e) {
            log.info("获取SFTP连接失败-" + info);
            log.info("", e);
            return false;
        }
        return true;
    }

    /**
     * 上传文件到SFTP服务器
     *
     * @param ftpFilePathAndName   上传到远程服务器上文件保存目录及文件名
     * @param localFilePathAndName 本地需要上传的文件目录及文件名
     * @return
     */
    public boolean uploadFile(String ftpFilePathAndName, String localFilePathAndName) {
        if (!open()) {
            return false;
        }
        log.info("请求上传的文件：" + localFilePathAndName);

        try {
            ftpFilePathAndName = formatPath(ftpFilePathAndName);
            localFilePathAndName = formatPath(localFilePathAndName);

            String ftpFileName = ftpFilePathAndName.substring(ftpFilePathAndName.lastIndexOf("/") + 1);
            String ftpFilePath = ftpFilePathAndName.substring(0, ftpFilePathAndName.lastIndexOf("/") + 1);

            mkdir(ftpFilePath);
            File localFile = new File(localFilePathAndName);
            InputStream in = new FileInputStream(localFile);
            log.info(sftp.pwd());
            sftp.put(in, localFile.getName());
            // 重命名文件 注意这里若同一目录下新上传的文件名已经存在，则会报错
            sftp.rename(localFile.getName(), ftpFileName);
            log.info("成功将文件:" + localFilePathAndName + "  上传到SFTP服务上：" + ftpFilePathAndName);
            log.info(sftp.pwd());
            in.close();
            return true;
        } catch (Exception e) {
            close();
            log.error("SFTP上传文件失败：", e);
            return false;
        } finally {
            close();
        }
    }


    /**
     * 上传文件到SFTP服务器
     *
     * @param ftpFilePathAndName 上传到远程服务器上文件保存目录及文件名
     * @param urlPath            本地需要上传的文件目录及文件名
     * @return
     */
    public String uploadFileUrl(String ftpFilePathAndName, String urlPath, String fileName) {
        if (!open()) {
            return null;
        }
        log.info("请求上传的文件：" + urlPath);
        try {
            String ftpFileName = ftpFilePathAndName.substring(ftpFilePathAndName.lastIndexOf("/") + 1);
            String ftpFilePath = ftpFilePathAndName.substring(0, ftpFilePathAndName.lastIndexOf("/") + 1);
            mkdir(ftpFilePath);
            URL url = new URL(urlPath);
            log.info(sftp.pwd());
            InputStream inputStream = url.openStream();
            sftp.put(url.openStream(), fileName);
            // 重命名文件 注意这里若同一目录下新上传的文件名已经存在，则会报错
            sftp.rename(fileName, ftpFileName);
            log.info("成功将文件:" + urlPath + "  上传到SFTP服务上：" + ftpFilePathAndName);
            log.info(sftp.pwd());
            inputStream.close();
            return ftpFileName;
        } catch (Exception e) {
            close();
            log.error("SFTP上传文件失败：", e);
            return null;
        } finally {
            close();
        }
    }

    /**
     * 下载SFTP服务器文件
     *
     * @param ftpFilePathAndName   远程服务器上文件保存目录及文件名
     * @param localFilePathAndName 下载到本地的文件目录及文件名
     */
    public boolean download(String ftpFilePathAndName, String localFilePathAndName) {
        try {
            if (!open()) {
                return false;
            }
            ftpFilePathAndName = formatPath(ftpFilePathAndName);
            localFilePathAndName = formatPath(localFilePathAndName);

            String ftpFileName = ftpFilePathAndName.substring(ftpFilePathAndName.lastIndexOf("/") + 1);
            String ftpFilePath = ftpFilePathAndName.substring(0, ftpFilePathAndName.lastIndexOf("/") + 1);
            mkdir(ftpFilePath);
            File localFile = new File(localFilePathAndName);
            sftp.get(ftpFileName, new FileOutputStream(localFile));
            return true;
        } catch (Exception e) {
            close();
            log.info("", e);
            return false;
        } finally {
            close();
        }
    }

    /**
     * 删除ftp服务器上的文件
     *
     * @param dir   远程目录
     * @param dfile 需要删除的文件
     */
    public boolean delete(String dir, String dfile) {
        if (!open()) {
            return false;
        }
        try {
            sftp.cd(dir);
            sftp.rm(dfile);
        } catch (Exception e) {
            close();
            log.info("", e);
            return false;
        } finally {
            close();
        }
        return true;
    }

    /**
     * 循环全路径，不存在的目录则创建，存在则进入该路径
     *
     * @param ftpFilePath
     * @throws SftpException
     */
    public void mkdir(String ftpFilePath) throws SftpException {
        log.info("切换前目录：" + sftp.pwd());
        // 先切换会根目录
        sftp.cd("/");
        String[] pathArray = ftpFilePath.split("/");
        for (String path : pathArray) {
            if ("".equals(path)) {
                continue;
            }
            if (!isDirExist(path)) {
                // 建立目录
                this.sftp.mkdir(path);
                // 进入并设置为当前目录
            }
            this.sftp.cd(path);
        }
        log.info("切换后目录:{}", sftp.pwd());
    }

    /**
     * 判断目录是否存在
     *
     * @param directory
     * @return
     * @throws SftpException
     */
    private boolean isDirExist(String directory) {
        try {
            SftpATTRS sftpATTRS = sftp.lstat(directory);
            return sftpATTRS.isDir();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 将路径中的斜杠统一
     *
     * @param path
     * @return
     */
    private String formatPath(String path) {
        // 将路径中的斜杠统一
        char[] chars = path.toCharArray();
        StringBuffer sbStr = new StringBuffer(256);
        for (int i = 0; i < chars.length; i++) {
            if ('\\' == chars[i]) {
                sbStr.append('/');
            } else {
                sbStr.append(chars[i]);
            }
        }
        path = sbStr.toString();
        return path;
    }

    /**
     * 关闭SFTP连接
     */
    public boolean close() {
        try {
            if (sftp == null) {
                return true;
            }
            sftp.getSession().disconnect();
            sftp.disconnect();
            sftp.exit();
            log.info("SFTP连接关闭！");
            return true;
        } catch (Exception e) {
            log.info("关闭SFTP连接异常：", e);
            return false;
        }
    }

    public Map<String, Object> uploadSftp(Map<String, MultipartFile> files, String ftpFilePath, Map<String, Object> filePathMap) {
        try {
            if (!open()) {
                filePathMap.put("message", "sFtp服务器连接失败。");
                return filePathMap;
            }
            mkdir(ftpFilePath);
            for (Map.Entry<String, MultipartFile> multipartFile : files.entrySet()) {
                MultipartFile multipartFileInfo = multipartFile.getValue();
                if (multipartFileInfo.isEmpty()) {
                    continue;
                }
                String fileName = multipartFileInfo.getOriginalFilename();
                log.info("fileName-------------" + fileName);
                int one = fileName.lastIndexOf(".");
                String uuid = UUIDUtil.genUUID();
                try {
                    InputStream in = multipartFileInfo.getInputStream();
                    log.info(sftp.pwd());
                    sftp.put(in, fileName);
                    sftp.rename(fileName, uuid + "." + fileName.substring((one + 1)));
                    log.info(sftp.pwd());
                    in.close();
                    log.info("文件：" + ftpFilePath + fileName + "上传成功！");
                } catch (Exception e) {
                    filePathMap.put("message", "文件上传失败。");
                    return filePathMap;
                }
                filePathMap.put(multipartFile.getKey(), ftpFilePath + uuid + "." + fileName.substring((one + 1)));
            }
            filePathMap.put("code", "200");
        } catch (Exception e) {
            log.info("实现文件上传:", e);
        }
        return filePathMap;
    }

    public List<HrRegist> uploadHrSftpBase(String ftpFilePath, List<HrRegist> allGrpHrInfo) {
        try {
            if (!open()) {
            }
            mkdir(ftpFilePath);
            for (HrRegist hrRegist : allGrpHrInfo) {
                if (!StringUtil.isEmpty(hrRegist.getIdImage1()) && !hrRegist.getIdImage1().startsWith("http")) {
                    hrRegist.setIdImage1(base64ToMultipart(ftpFilePath, hrRegist.getIdImage1()));
                }
                if (!StringUtil.isEmpty(hrRegist.getIdImage2()) && !hrRegist.getIdImage2().startsWith("http")) {
                    hrRegist.setIdImage2(base64ToMultipart(ftpFilePath, hrRegist.getIdImage2()));
                }
            }
        } catch (Exception e) {
            log.info("实现文件上传:", e);
        }
        return allGrpHrInfo;
    }

    public List<FCGrpInfo> uploadSftpBase(String ftpFilePath, List<FCGrpInfo> allGrpInfo) {
        try {
            if (!open()) {
            }
            mkdir(ftpFilePath);
            for (FCGrpInfo fcGrpInfo : allGrpInfo) {
                if (!StringUtil.isEmpty(fcGrpInfo.getGrpIDImage1()) && !fcGrpInfo.getGrpIDImage1().startsWith("http")) {
                    fcGrpInfo.setGrpIDImage1(base64ToMultipart(ftpFilePath, fcGrpInfo.getGrpIDImage1()));
                }
                if (!StringUtil.isEmpty(fcGrpInfo.getGrpIDImage2()) && !fcGrpInfo.getGrpIDImage2().startsWith("http")) {
                    fcGrpInfo.setGrpIDImage2(base64ToMultipart(ftpFilePath, fcGrpInfo.getGrpIDImage2()));
                }
                if (!StringUtil.isEmpty(fcGrpInfo.getLegIDImage1()) && !fcGrpInfo.getLegIDImage1().startsWith("http")) {
                    fcGrpInfo.setLegIDImage1(base64ToMultipart(ftpFilePath, fcGrpInfo.getLegIDImage1()));
                }
                if (!StringUtil.isEmpty(fcGrpInfo.getLegIDImage2()) && !fcGrpInfo.getLegIDImage2().startsWith("http")) {
                    fcGrpInfo.setLegIDImage2(base64ToMultipart(ftpFilePath, fcGrpInfo.getLegIDImage2()));
                }
            }
        } catch (Exception e) {
            log.info("实现文件上传:", e);
        }
        return allGrpInfo;
    }

    public List<FCStaffFamilyRela> uploadRelationproveSftpBase(String ftpFilePath, List<FCStaffFamilyRela> fcStaffFamilyRelaList) {
        try {
            if (!open()) {
            }
            mkdir(ftpFilePath);
            for (FCStaffFamilyRela fcStaffFamilyRela : fcStaffFamilyRelaList) {
                if (!StringUtil.isEmpty(fcStaffFamilyRela.getRelationProve())) {
                    fcStaffFamilyRela.setRelationProve(base64ToMultipart(ftpFilePath, fcStaffFamilyRela.getRelationProve()));
                }
            }
        } catch (Exception e) {
            log.info("实现文件上传:", e);
        }
        return fcStaffFamilyRelaList;
    }

    // 上传文件
    public String base64ToMultipart(String ftpFilePath, String base) {
        MultipartFile multipartFileInfo = BASE64DecodedMultipartFile.base64ToMultipart(base);
        String fileName = multipartFileInfo.getOriginalFilename();
        log.info("fileName-------------" + fileName);
        int one = fileName.lastIndexOf(".");
        StringBuilder time = new StringBuilder(DateTimeUtil.getTimeString());
        String uuid = UUIDUtil.genUUID();
        try {
            InputStream in = multipartFileInfo.getInputStream();
            log.info(sftp.pwd());
            sftp.put(in, fileName);
            // 随机数重命名文件 防止文件名冲突 若同一目录下新上传的文件名已经存在，则会报错
            time.append((int) (1 + Math.random() * (100 - 1 + 1)));
            sftp.rename(fileName, uuid + "." + fileName.substring((one + 1)));
            log.info(sftp.pwd());
            in.close();
            log.info("文件：" + ftpFilePath + fileName + "上传成功！");
        } catch (Exception e) {
            log.error("base64ToMultipart error", e);
        }
        //获取环境请求展示的变量
        String fileDisplayPath = myProps.getFileDisplayPath();
        return fileDisplayPath + ftpFilePath + uuid + "." + fileName.substring((one + 1));
    }

    public MultipartFile FileDisplay2(String ftpFilePathAndName) {
        MultipartFile multipartFile = null;
        try {
            open();
            String ftpFileName = ftpFilePathAndName.substring(ftpFilePathAndName.lastIndexOf("/") + 1);
            String ftpFilePath = ftpFilePathAndName.substring(0, ftpFilePathAndName.lastIndexOf("/") + 1);
            mkdir(ftpFilePath);
            InputStream in = sftp.get(ftpFileName);
            multipartFile = FileUtils.inputStreamToMultipartFile(ftpFileName, in);
            close();
        } catch (IOException | SftpException e) {
            close();
            log.error("文件读取错误", e);
            return null;
        }
        return multipartFile;
    }

    public boolean FileDisplay(HttpServletResponse response, String sftpPath) {
        try {
            open();
            InputStream in = sftp.get(sftpPath);
            BufferedImage bi = ImageIO.read(in);
            ServletOutputStream out = response.getOutputStream();
            ImageIO.write(bi, "png", out);
            try {
                out.flush();
            } finally {
                out.close();
            }
            close();
        } catch (IOException | SftpException e) {
            close();
            log.error("文件读取错误。");
            return false;
        }
        return true;
    }

    public boolean FileFtpDisplay(MultipartFile multipartFileInfo, String sftpPath, String fileName) {
        try {
            if (!open()) {
                return false;
            }
            log.info("sftpPath-------------" + sftpPath);
            mkdir(sftpPath);
            log.info("fileName-------------" + fileName);
            try {
                InputStream in = multipartFileInfo.getInputStream();
                log.info(sftp.pwd());
                sftp.put(in, fileName);
                log.info(sftp.pwd());
                in.close();
                log.info("文件：" + sftpPath + fileName + "上传成功！");
            } catch (Exception e) {
                log.error("FileFtpDisplay error", e);
                return false;
            }
        } catch (Exception e) {
            log.info("实现文件上传:", e);
        }
        return true;
    }
}
