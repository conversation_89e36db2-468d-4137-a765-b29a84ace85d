package com.sinosoft.eflex.util;


import com.sinosoft.eflex.util.rest.exception.SystemException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DateTimeUtil {

    private static final Logger log = LoggerFactory.getLogger(DateTimeUtil.class);

    /**
     * 获取当前系统日期
     *
     * @return 当前日期字符串，格式为："yyyy-MM-dd"
     * <AUTHOR>
     */
    public static String getCurrentDate() {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date today = new Date();
        String todayStr = df.format(today);
        return todayStr;
    }

    /**
     * 获取系统当前年度
     *
     * @return 当前日期字符串，格式为："yyyy" 例如：当前日期为2018年10月27日，则返回2018
     * <AUTHOR>
     */
    public static String getCurrentYear() {
        SimpleDateFormat df = new SimpleDateFormat("yyyy");
        Date today = new Date();
        String todayStr = df.format(today);
        return todayStr;
    }

    /**
     * 获取系统当前月份
     *
     * @return 当前日期字符串，格式为："MM" 例如：当前日期为2018年10月27日，则返回10
     * <AUTHOR>
     */
    public static String getCurrentMonth() {
        SimpleDateFormat df = new SimpleDateFormat("MM");
        Date today = new Date();
        String monthStr = df.format(today);
        return monthStr;
    }

    /**
     * 获取系统当前日期天数 dd
     *
     * @return 当前日期字符串，格式为："dd"。 例如：当前日期为2018年10月27日，则返回27
     * <AUTHOR>
     */
    public static String getCurrentDay() {
        SimpleDateFormat df = new SimpleDateFormat("dd");
        Date today = new Date();
        String datStr = df.format(today);
        return datStr;
    }


    /**
     * 获取当前系统时间
     *
     * @return 当前日期字符串，格式为："HH:mm:ss"
     * <AUTHOR>
     */
    public static String getCurrentTime() {
        SimpleDateFormat df = new SimpleDateFormat("HH:mm:ss");
        Date now = new Date();
        String nowStr = df.format(now);
        return nowStr;
    }

    /**
     * 获取当前系统时间
     *
     * @return 当前日期字符串，格式为："yyyy-MM-dd HH:mm:ss"
     * <AUTHOR>
     */
    public static String getCurrentDateTime() {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date today = new Date();
        String todayStr = df.format(today);
        return todayStr;
    }

    /**
     * 获取当前时间字符串
     *
     * @return 当前时间字符串，格式为："yyyyMMddHHmmss"
     * <AUTHOR>
     */
    public static String getTimeString() {
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        Date today = new Date();
        String todayStr = df.format(today);
        return todayStr;
    }

    /**
     * 日期字符串转日期类型
     *
     * @param dateStr   日期字符串
     * @param formatStr 格式化样式 例：yyyy-MM-dd
     * @return date
     * <AUTHOR>
     */
    public static Date strToDate(String dateStr, String formatStr) {
        if (formatStr == null || "".equals(formatStr)) {
            formatStr = "yyyy-MM-dd";
        }
        SimpleDateFormat format = new SimpleDateFormat(formatStr);
        Date date = null;
        try {
            date = format.parse(dateStr);
        } catch (ParseException e) {
            log.info("", e);
        }
        return date;
    }

    /**
     * 获取出生年月
     */
    public static String getBirthdayDate(String age) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date dt = new Date();
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(dt);
        rightNow.add(Calendar.YEAR, -Integer.parseInt(age));// 出生日期
        Date dt1 = rightNow.getTime();
        String reStr = sdf.format(dt1);
        return reStr;
    }

    /**
     * 获取年龄
     */
    public static String getAge(String str) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date birthday = null;
        try {
            birthday = sdf.parse(str);
        } catch (ParseException e) {
            log.info("", e);
        }
        Calendar calendarBirthday = Calendar.getInstance();
        calendarBirthday.setTime(birthday);
        int birthdayTime = calendarBirthday.get(Calendar.YEAR);
        Calendar calendarNow = Calendar.getInstance();
        calendarNow.setTime(new Date());
        int nowTime = calendarNow.get(Calendar.YEAR);
        return (nowTime - birthdayTime) + "";
    }

    public static Integer getAgeInt(String str) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date birthday = null;
        try {
            birthday = sdf.parse(str);
        } catch (ParseException e) {
            log.info("", e);
        }
        Calendar calendarBirthday = Calendar.getInstance();
        calendarBirthday.setTime(birthday);
        int birthdayTime = calendarBirthday.get(Calendar.YEAR);
        Calendar calendarNow = Calendar.getInstance();
        calendarNow.setTime(new Date());
        int nowTime = calendarNow.get(Calendar.YEAR);
        return (nowTime - birthdayTime);
    }


    /**
     * 获取周岁
     *
     * @param birthday
     * @return
     */
    public static int getCurrentAge(String birthday, String date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Calendar curr = Calendar.getInstance();
            Calendar born = Calendar.getInstance();
            curr.setTime(sdf.parse(date));
            born.setTime(sdf.parse(birthday));
            int age = curr.get(Calendar.YEAR) - born.get(Calendar.YEAR);
            if (age <= 0) {
                return 0;
            }
            int currMonth = curr.get(Calendar.MONTH);
            int currDay = curr.get(Calendar.DAY_OF_MONTH);
            int bornMonth = born.get(Calendar.MONTH);
            int bornDay = born.get(Calendar.DAY_OF_MONTH);
            if ((currMonth < bornMonth) || (currMonth == bornMonth && currDay < bornDay)) {
                age--;
            }
            return age < 0 ? 0 : age;
        } catch (ParseException e) {
            log.info("计算周岁失败", e);
            return -2;
        }
    }

    /**
     * 将时间转换为时间戳
     */
    public static String dateToStamp(String s) {
        String res;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date;
        long ts = 0;
        try {
            date = simpleDateFormat.parse(s);
            ts = date.getTime();
        } catch (ParseException e) {
            log.info("", e);
        }
        res = String.valueOf(ts);
        return res;
    }

    /*
     * 时间戳转换为日期格式字符串
     *
     */
    public static String timeStamp2Date(String timeStamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(new Date(Long.valueOf(timeStamp + "000")));
    }


    /**
     * 两个时间之间相差距离多少天
     *
     * @param str1 时间参数 1：
     * @param str2 时间参数 2：
     * @return 相差天数
     */
    public static long getDistanceDays(String str1, String str2) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date one;
        Date two;
        long days = 0;
        try {
            one = df.parse(str1);
            two = df.parse(str2);
            long time1 = one.getTime();
            long time2 = two.getTime();
            long diff;
            if (time1 < time2) {
                diff = time2 - time1;
            } else {
                diff = time1 - time2;
            }
            days = diff / (1000 * 60 * 60 * 24);
        } catch (ParseException e) {
            log.info("", e);
        }
        return days;
    }

    /**
     * 日期字符串加一年
     *
     * @param dateStr
     * @return
     */
    public static String dateAddOneYear(String dateStr) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date today = strToDate(dateStr, "yyyy-MM-dd");
        //System.out.println("今天是:" + df.format(today));
        Calendar c = Calendar.getInstance();
        c.setTime(today);
        c.add(Calendar.YEAR, 1);// 今天+1天
        Date nextYear = c.getTime();
        String nextYearStr = df.format(nextYear);
        //System.out.println("明年是:" + nextYearStr);
        return nextYearStr;
    }

    /**
     * 日期字符串减一天
     *
     * @param dateStr
     * @return
     */
    public static String dateSubOneDay(String dateStr) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date today = strToDate(dateStr, "yyyy-MM-dd");
        //System.out.println("今天是:" + df.format(today));
        Calendar c = Calendar.getInstance();
        c.setTime(today);
        // 日期减一天
        c.set(Calendar.DATE, c.get(Calendar.DATE) - 1);
        String nextYearStr = df.format(c.getTime());
        return nextYearStr;
    }

    /**
     * 获取日期年月日格式，可增加减少天数
     *
     * @param amount 天数，为负数时为减少的天数，为正整数时为增加的天数
     * @return
     */
    public static String getdateYMD(int amount) {
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DAY_OF_MONTH, amount);
        int year = c.get(Calendar.YEAR);
        int month = c.get(Calendar.MONTH) + 1;
        int day = c.get(Calendar.DAY_OF_MONTH);
        String nextYearStr = year + "年" + month + "月" + day + "日";
        return nextYearStr;
    }


    //校验未来日期
    public static boolean checkFutureDate(String strDate) {
        //判断是否为未来日期
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");//可以方便地修改日期格式
        String nowdate = dateFormat.format(new Date());
//		String strdate = strDate.replace("/","-");
        if (nowdate.compareTo(strDate) > 0) {
            return false;//不是未来日期
        }
        return true;//未来日期（含当前日期）
    }

    //比较日期 前>后：false
    public static boolean checkDate(String strDate, String strDate1) {
        if (strDate.compareTo(strDate1) > 0) {
            return false;
        }
        return true;
    }

    //比较日期 前>=后：false
    public static boolean checkDate1(String strDate, String strDate1) {
        if (strDate.compareTo(strDate1) >= 0) {
            return false;
        }
        return true;
    }


    //获取指定时间
    public static String getAppointDate(Date date, int value, int DateParm) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        switch (DateParm) {
            case 1:
                calendar.add(calendar.YEAR, value);//把日期往后增加一年.整数往后推,负数往前移动
                break;
            case 2:
                calendar.add(calendar.MONTH, value);// 把日期往后增加一月.整数往后推,负数往前移动
                break;
            case 3:
                calendar.add(calendar.DAY_OF_MONTH, value);//把日期往后增加一天.整数往后推,负数往前移动
                break;
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String newStr = df.format(calendar.getTime());
        return newStr;
    }


    //校验身份证号码
    private final static int[] PARITYBIT = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};
    private final static int[] POWER_LIST = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};

    public static boolean isIDCard(String certNo) {
        if (certNo == null || (certNo.length() != 15 && certNo.length() != 18))
            return false;
        final char[] cs = certNo.toUpperCase().toCharArray();
        //校验位数
        int power = 0;
        for (int i = 0; i < cs.length; i++) {
            if (i == cs.length - 1 && cs[i] == 'X')
                break;//最后一位可以 是X或x
            if (cs[i] < '0' || cs[i] > '9')
                return false;
            if (i < cs.length - 1) {
                power += (cs[i] - '0') * POWER_LIST[i];
            }
        }

        /*//校验区位码
        if(!zoneNum.containsKey(Integer.valueOf(certNo.substring(0,2)))){
            return false;
        }*/

        //校验年份
        String year = certNo.length() == 15 ? "19" + certNo.substring(6, 8) : certNo.substring(6, 10);

        final int iyear = Integer.parseInt(year);
        if (iyear < 1900 || iyear > Calendar.getInstance().get(Calendar.YEAR))
            return false;//1900年的PASS，超过今年的PASS

        //校验月份
        String month = certNo.length() == 15 ? certNo.substring(8, 10) : certNo.substring(10, 12);
        final int imonth = Integer.parseInt(month);
        if (imonth < 1 || imonth > 12) {
            return false;
        }

        //校验天数
        String day = certNo.length() == 15 ? certNo.substring(10, 12) : certNo.substring(12, 14);
        final int iday = Integer.parseInt(day);
        if (iday < 1 || iday > 31)
            return false;

        //校验"校验码"
        if (certNo.length() == 15)
            return true;
        return cs[cs.length - 1] == PARITYBIT[power % 11];
    }

    //校验日期的合法性
    public static boolean isDate(String date) {
        String rexp = "^((\\d{2}(([02468][048])|([13579][26]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))";
        Pattern pat = Pattern.compile(rexp);
        Matcher mat = pat.matcher(date);
        boolean dateType = mat.matches();
        return dateType;
    }

    //指定日期+指定天数
    public static String plusDay(int num, String appointDate) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date currdate = format.parse(appointDate);
        Calendar ca = Calendar.getInstance();
        ca.setTime(currdate);
        ca.add(Calendar.DATE, num);
        currdate = ca.getTime();
        String enddate = format.format(currdate);
        return enddate;
    }

    //日期补0，yyyyn年mm月dd日
    public static String fillDateWillZero(String dateStr) {
        int start = dateStr.indexOf("/");
        int end = dateStr.lastIndexOf("/");
        String month = dateStr.substring(start + 1, end);
        if (month.length() == 1) {
            month = "0" + month;
        }
        String day = dateStr.substring(end + 1);
        if (day.length() == 1) {
            day = "0" + day;
        }
        return dateStr.substring(0, start) + "年" + month + "月" + day;
    }

    /**
     * 两个时间之间相差距离多少天
     *
     * @param str1 时间参数 1：
     * @param str2 时间参数 2：
     * @return 相差天数
     */
    public static long getDistanceDays(String str1, String str2, DateFormat dateFormat) throws Exception {
        if (ObjectUtils.isEmpty(dateFormat)) {
            dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        }
        Date one;
        Date two;
        long days = 0;
        try {
            one = dateFormat.parse(str1);
            two = dateFormat.parse(str2);
            long time1 = one.getTime();
            long time2 = two.getTime();
            long diff;
            if (time1 < time2) {
                diff = time2 - time1;
            } else {
                diff = time1 - time2;
            }
            days = diff / (1000 * 60 * 60 * 24);
        } catch (ParseException e) {
            log.info("", e);
        }
        return days;
    }


    //获取指定时间
    public static String getAppointDate(Date date, int value, String dateUnit) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        switch (dateUnit) {
            case "Y":
                // 把日期往后增加一年.整数往后推,负数往前移动
                calendar.add(Calendar.YEAR, value);
                break;
            case "M":
                // 把日期往后增加一月.整数往后推,负数往前移动
                calendar.add(Calendar.MONTH, value);
                break;
            case "D":
                // 把日期往后增加一天.整数往后推,负数往前移动
                calendar.add(Calendar.DAY_OF_MONTH, value);
                break;
            default:
                break;
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String newStr = df.format(calendar.getTime());
        return newStr;
    }

    // 获取指定时间，返回日期+时间
    public static String getAppointDateReturnDateTime(Date date, int value, String dateUnit) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        switch (dateUnit) {
            case "Y":
                calendar.add(Calendar.YEAR, value);// 把日期往后增加一年.整数往后推,负数往前移动
                break;
            case "M":
                calendar.add(Calendar.MONTH, value);// 把日期往后增加一月.整数往后推,负数往前移动
                break;
            case "D":
                calendar.add(Calendar.DAY_OF_MONTH, value);// 把日期往后增加一天.整数往后推,负数往前移动
                break;
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String newStr = df.format(calendar.getTime());
        return newStr;
    }

    // 日期格式转换得到yyyy-mm-dd的字符串格式的值
    public static String dateFormat(String dateString) {
        if (StringUtils.isEmpty(dateString)) {
            return null;
        }
        Date date = null;

        try {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            if (dateString.contains("-")) {
                date = df.parse(dateString);
                return df.format(date);
            } else if (dateString.contains("/")) {
                SimpleDateFormat df1 = new SimpleDateFormat("yyyy/MM/dd");
                date = df1.parse(dateString);
                return df.format(date);
            } else {
                return null;
            }
        } catch (Exception e) {
            throw new SystemException("日期格式转换失败！");
        }
    }

    /**
     * 加减对应时间后的日期
     *
     * @param date   需要加减时间的日期
     * @param amount 加减的时间(毫秒)
     * @return 加减对应时间后的日期
     */
    public static String addTime(Date date, int amount) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String strTime = sdf.format(date.getTime() + amount);
            return strTime;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "0";
    }

    /**
     * 两个日期时间差
     *
     * @param str1 时间参数 1：
     * @param str2 时间参数 2：
     * @return 相差
     */
    public static long getDistanceTimes(String str1, String str2) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long diff = 0;
        try {
            Date one = sdf.parse(str1);
            Date two = sdf.parse(str2);
            long time1 = one.getTime();
            long time2 = two.getTime();
            if (time1 < time2) {
                diff = time2 - time1;
            } else {
                diff = time1 - time2;
            }
        } catch (ParseException e) {
            log.info("", e);
        }
        return diff;
    }

}
