package com.sinosoft.eflex.util.oss;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.sinosoft.eflex.constants.OssProperties;
import com.sinosoft.eflex.model.OssEntity.OssEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.security.SecureRandom;
import java.util.Date;

/**
 * 阿里云配置*
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
@Deprecated
public class OssUtils {

    private final OssProperties ossProperties;

    /**
     * 文件上传
     *
     * @param file 文件
     * @param flag 标签(类别)
     * @return
     */
    public OssEntity uploadFile(MultipartFile file, String flag) {
        try {
            OSS ossClientOut = new OSSClientBuilder().build(ossProperties.getEndpointOut(), ossProperties.getAccessKeyId(), ossProperties.getAccessKeySecret());
            OSS ossClientIn = new OSSClientBuilder().build(ossProperties.getEndpointIn(), ossProperties.getAccessKeyId(), ossProperties.getAccessKeySecret());
            // 获取文件名
            String fileName = file.getOriginalFilename();
            // 获取文件后缀名
            String suffixName = fileName.substring(fileName.lastIndexOf("."));
            // 最后上传生成的文件名
            String finalFileName = System.currentTimeMillis() + "" + new SecureRandom().nextInt(0x0400) + suffixName;
            // oss中的文件夹名
            String objectName = ossProperties.getCatalogue() + "/" + flag + "_" + finalFileName;
            // 创建上传文件的元信息，可以通过文件元信息设置HTTP header(设置了才能通过返回的链接直接访问)。
            ObjectMetadata objectMetadata = new ObjectMetadata();

            objectMetadata.setHeader("x-oss-object-acl", "public-read");
            // 文件上传

            PutObjectRequest putOutObjectRequest = new PutObjectRequest(ossProperties.getBucketName(), objectName, file.getInputStream());
            PutObjectRequest putInObjectRequest = new PutObjectRequest(ossProperties.getBucketName(), objectName, file.getInputStream());
            // 设置该属性可以返回response。如果不设置，则返回的response为空。
            putOutObjectRequest.setProcess("true");
            putOutObjectRequest.setMetadata(objectMetadata);
            ossClientOut.putObject(putOutObjectRequest);
            putInObjectRequest.setProcess("true");
            putInObjectRequest.setMetadata(objectMetadata);
            ossClientIn.putObject(putInObjectRequest);
            // 设置URL过期时间为10年。
            Date expiration = new Date(System.currentTimeMillis() + 3600L * 1000 * 24 * 365 * 10);
            String outUrl = ossClientOut.generatePresignedUrl(ossProperties.getBucketName(), objectName, expiration).toString();
            String inUrl = ossClientIn.generatePresignedUrl(ossProperties.getBucketName(), objectName, expiration).toString();
            ossClientOut.shutdown();
            ossClientIn.shutdown();
            OssEntity ossEntity = new OssEntity();
            ossEntity.setOutUrl(outUrl);
            ossEntity.setInUrl(inUrl);
            log.info("阿里云OSS的文件url:{}", ossEntity);
            return ossEntity;
        } catch (Exception e) {
            log.error("uploadFile error", e);
        }
        return null;
    }


}
