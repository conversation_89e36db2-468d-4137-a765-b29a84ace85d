package com.sinosoft.eflex.util;

import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * excel03导出工具，目前只支持单元格内容为String类型格式导出
 *
 * <AUTHOR>
 * @param <T>
 */
@SuppressWarnings("deprecation")
public class ExportExcelUtil {

    private static final Logger log = LoggerFactory.getLogger(ExportExcelUtil.class);

    /**
     * 默认sheet页名字为Sheet1
     *
     * @param header
     *            数组表头,格式为二维数组,如new String[][]{{"id", "学号"},{"name", "姓名"},{"age",
     *            "年龄"},{"sex", "性别"},{"birthday", "出生日期"}};
     * @param dataList
     *            数据集合，List<Object>或List<Map<String,String>>
     * @param out
     */
    public static <T> void exportExcel(String[][] header, List<T> dataList, OutputStream out) {
        exportExcel("Sheet1", header, dataList, out);
    }

    /**
     *
     * @param title
     *            sheet页名
     * @param headers
     *            数据表头,格式为二维数组,如new String[][]{{"id", "学号"},{"name", "姓名"},{"age",
     *            "年龄"},{"sex", "性别"},{"birthday", "出生日期"}};
     * @param dataset
     *            数据集合，List<Object>或List<Map<String,String>>
     * @param out
     */
    public static <T> void exportExcel(String title, String[][] headers, Collection<T> dataset, OutputStream out) {
        // 声明一个工作薄
        XSSFWorkbook workbook = new XSSFWorkbook();
        // 生成一个表格
        XSSFSheet sheet = workbook.createSheet(title);
        // 设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 15);
        // 生成一个样式，表头字体格式12号，加粗
        XSSFCellStyle style = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setFontHeightInPoints((short) 12);
        font.setBold(true);
        // 把字体应用到当前的样式
        style.setFont(font);
        // 生成并设置另一个样式
        XSSFCellStyle style2 = workbook.createCellStyle();
        // 生成另一个字体
        XSSFFont font2 = workbook.createFont();
        font2.setBold(true);
        // 把字体应用到当前的样式
        style2.setFont(font2);

        XSSFRow row = sheet.createRow(0);
        if (headers != null) {
            // 产生表格标题行
            for (short i = 0; i < headers.length; i++) {
                XSSFCell cell = row.createCell(i);
                cell.setCellStyle(style);
                cell.setCellValue(headers[i][1]);

            }
        }
        // 遍历集合数据，产生数据行
        Iterator<T> it = dataset.iterator();
        int index = 0;
        while (it.hasNext()) {
            index++;
            row = sheet.createRow(index);
            T t = (T) it.next();
            // 通过表头数组获取对应的单元格内容
            for (short i = 0; i < headers.length; i++) {
                XSSFCell cell = row.createCell(i);
                cell.setCellStyle(style2);
                String textValue = getValue(t, headers, i);
                cell.setCellValue(textValue);
            }
        }
        try {
            workbook.write(out);
        } catch (IOException e) {
            log.info("", e);
        }
    }

    /**
     *
     * @param t
     * @param header
     *            表头数组
     * @param i
     *            for循环遍历下标值
     * @return
     */
    private static String getValue(Object t, String[][] header, short i) {
        String fieldName = header[i][0];
        // 获取Map中对应的值
        if (t instanceof Map) {
            Map<String, Object> data = (Map<String, Object>) t;
            Object value = data.get(header[i][0]);
            // 判断空值
            if (value == null || "****".equals(value) || "************".equals(value)) {
                value = "";
            }
            String textValue = value.toString();
            return textValue;
        }
        // 获取对象属性的get方法名
        String getMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
        try {
            Class<? extends Object> tCls = t.getClass();
            Method getMethod = tCls.getMethod(getMethodName, new Class[] {});
            Object value = getMethod.invoke(t, new Object[] {});
            // 判断空值
            if (value == null || "****".equals(value) || "************".equals(value)) {
                value = "";
            }
            String textValue = value.toString();
            return textValue;
        } catch (NoSuchMethodException e) {
            log.info("", e);
        } catch (IllegalAccessException e) {
            log.info("", e);
        } catch (IllegalArgumentException e) {
            log.info("", e);
        } catch (InvocationTargetException e) {
            log.info("", e);
        } finally {

        }
        return "";
    }
}
