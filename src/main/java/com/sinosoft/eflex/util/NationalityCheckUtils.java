package com.sinosoft.eflex.util;


import com.hqins.common.base.constants.Strings;
import com.hqins.common.utils.StringUtil;
import com.sinosoft.eflex.enums.CertTypeEnums;
import com.sinosoft.eflex.enums.NationalityEnums;
import com.sinosoft.eflex.framework.exception.EFlexServiceException;
import com.sinosoft.eflex.framework.exception.EFlexServiceExceptionEnum;
import lombok.experimental.UtilityClass;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * <AUTHOR>
 * @describe 国籍校验工具类
 * @date 2024/12/11
 */
@UtilityClass
public final class NationalityCheckUtils {

    private static final Logger logger = LoggerFactory.getLogger(NationalityCheckUtils.class);

    private final String CERT_NO_HGK_STR = "81";
    private final String CERT_NO_MO_STR = "82";
    private final String HGK_H_STR = "H";
    private final String MAC_M_STR = "M";

    public void validateNationality(String nationality, String certificateType, String certNo) {
        switch (Objects.requireNonNull(CertTypeEnums.getEnumByCode(certificateType))) {
            case ID:
            case HOUSEHOLD_REGISTER:
            case BIRTH_CERTIFICATE:
            case CHINA_PASSPORT:
                validateNationalityWithCHN(nationality);
                break;
            case ID_TAIWAN:
            case TAIWAN_CN_PASS:
                validateNationalityWithTW(nationality);
                break;
            case FOREIGNER_PASSPORT:
            case ID_FOREIGNER:
                validateNationalityWithOther(nationality);
                break;
            case ID_HK_MACAO:
            case HK_MACAO_CN_PASS:
                validateNationalityWithHGK(nationality, certNo);
                break;
            default:
                break;
        }
    }

    /**
     * 处理默认国籍
     *
     * @param certType 证件类型
     * @param certNo   证件号
     * @return 国籍编码
     */
    public String defaultNationality(String certType, String certNo) {
        switch (Objects.requireNonNull(CertTypeEnums.getEnumByCode(certType))) {
            case ID:
            case HOUSEHOLD_REGISTER:
            case BIRTH_CERTIFICATE:
            case CHINA_PASSPORT:
                return NationalityEnums.CHN.getCode();
            case ID_TAIWAN:
            case TAIWAN_CN_PASS:
                return NationalityEnums.TW.getCode();
            case ID_HK_MACAO:
            case HK_MACAO_CN_PASS:
                if (certNo.startsWith(CERT_NO_HGK_STR) || certNo.startsWith(HGK_H_STR)) {
                    return NationalityEnums.HK.getCode();
                }
                if (certNo.startsWith(CERT_NO_MO_STR) || certNo.startsWith(MAC_M_STR)) {
                    return NationalityEnums.MO.getCode();
                }
            default:
                return Strings.EMPTY;
        }
    }

    /**
     * 当证件类型为"0-身份证、4-户口本、7-出生证"时、“J-中国护照”，"国籍"默认为"中国"，且不允许修改
     *
     * @param nationality 国籍
     */
    private void validateNationalityWithCHN(String nationality) {
        if (StringUtil.isBlank(nationality)) {
            return;
        }
        if (!NationalityEnums.CHN.getCode().equals(nationality)) {
            logger.error("NationalityCheckUtils #validateNationalityWithCHN  nationality:{}", nationality);
            throw new EFlexServiceException(EFlexServiceExceptionEnum.NATIONALITY_INVALID);
        }
    }

    /**
     * 校验国籍是台湾
     *
     * @param nationality 国籍
     */
    private void validateNationalityWithTW(String nationality) {
        if (StringUtil.isBlank(nationality)) {
            return;
        }
        if (!NationalityEnums.TW.getCode().equals(nationality)) {
            logger.warn("NationalityCheckUtils #validateNationalityWithTW  nationality:{}", nationality);
            throw new EFlexServiceException(EFlexServiceExceptionEnum.NATIONALITY_INVALID);
        }
    }


    /**
     * 当证件类型为"G-港澳居民居住证"且证件号码为"81"开头时，"国籍"默认为"中国香港"，且不允许修改；
     * 当证件类型为"G-港澳居民居住证"且证件号码为"82"开头时，"国籍"默认为"中国澳门"，且不允许修改；
     * 当证件类型为"B-港澳居民来往内地通行证"且证件号码为"H"开头时，"国籍"默认为"中国香港"，且不允许修改；
     * 当证件类型为"B-港澳居民来往大陆通行证"且证件号码为"M"开头时，"国籍"默认为"中国澳门"，且不允许修改；
     *
     * @param nationality 国籍
     */
    private void validateNationalityWithHGK(String nationality, String certNo) {
        if (StringUtil.isBlank(nationality) || StringUtil.isBlank(certNo)) {
            return;
        }
        if (!certNo.startsWith(CERT_NO_HGK_STR) && !certNo.startsWith(HGK_H_STR)) {
            if (NationalityEnums.HK.getCode().equals(nationality)) {
                logger.warn("NationalityCheckUtils #validateNationalityWithMO  nationality:{} certNo:{}", nationality, certNo);
                throw new EFlexServiceException(EFlexServiceExceptionEnum.NATIONALITY_INVALID);
            }
        }
        if (certNo.startsWith(CERT_NO_HGK_STR) || certNo.startsWith(HGK_H_STR)) {
            if (!NationalityEnums.HK.getCode().equals(nationality)) {
                logger.warn("NationalityCheckUtils #validateNationalityWithMO  nationality:{} certNo:{}", nationality, certNo);
                throw new EFlexServiceException(EFlexServiceExceptionEnum.NATIONALITY_INVALID);
            }
        }

        if (certNo.startsWith(CERT_NO_MO_STR) || certNo.startsWith(MAC_M_STR)) {
            if (!NationalityEnums.MO.getCode().equals(nationality)) {
                logger.warn("NationalityCheckUtils #validateNationalityWithMO  nationality:{} certNo:{}", nationality, certNo);
                throw new EFlexServiceException(EFlexServiceExceptionEnum.NATIONALITY_INVALID);
            }
        }
        if (!certNo.startsWith(CERT_NO_MO_STR) && !certNo.startsWith(MAC_M_STR)) {
            if (NationalityEnums.MO.getCode().equals(nationality)) {
                logger.warn("NationalityCheckUtils #validateNationalityWithMO  nationality:{} certNo:{}", nationality, certNo);
                throw new EFlexServiceException(EFlexServiceExceptionEnum.NATIONALITY_INVALID);
            }
        }


    }


    /**
     * 如果证件为外国公民护照，那么国籍不能为中国，中国香港，中国澳门，中国台湾
     *
     * @param nationality 国籍
     */
    private void validateNationalityWithOther(String nationality) {
        if (StringUtil.isBlank(nationality)) {
            return;
        }
        if (NationalityEnums.TW.getCode().equals(nationality) || NationalityEnums.HK.getCode().equals(nationality) || NationalityEnums.MO.getCode().equals(nationality) || NationalityEnums.CHN.getCode().equals(nationality)) {
            logger.warn("NationalityCheckUtils #validateNationalityWithOther  nationality:{}", nationality);
            throw new EFlexServiceException(EFlexServiceExceptionEnum.NATIONALITY_INVALID);
        }
    }
}
