package com.sinosoft.eflex.util;

/**
 * @Author: zhang<PERSON>iAng
 * @Date: 2024/08/06/16:18
 */
public class MaskUtils {

    /**
     * 证件号脱敏
     */
    public static String maskIDCard(String idCard) {
        if (idCard == null) {
            return idCard;
        }
        if (idCard.length() == 18) {
            // 将身份证号码最后4位替换为****
            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append(idCard, 0, 4).append("**********").append(idCard.substring(idCard.length() - 4));
            return stringBuffer.toString();
        } else {
            int length = idCard.length() / 3;
            // 将身份证号码最后4位替换为****
            StringBuffer stringBuffer = new StringBuffer();
            StringBuffer maskString = new StringBuffer();
            for (int i = 0; i < length; i++) {
                maskString.append("*");
            }
            stringBuffer.append(idCard, 0, length).append(maskString).append(idCard.substring(idCard.length() - length));
            return stringBuffer.toString();
        }

    }

    /**
     * 证件号脱敏
     */
    public static String maskPhone(String phone) {
        if (phone == null || phone.length() < 11) {
            return phone;
        }
        // 将身份证号码最后4位替换为****
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(phone, 0, 3).append("*****").append(phone.substring(phone.length() - 3));
        return stringBuffer.toString();
    }
}
