package com.sinosoft.eflex.util;

import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Date;

/**
 * @DESCRIPTION
 * @create 2018-08-13 18:51
 **/
public class ExcelUtil {


    public static Workbook initWorkbook(String filePath) throws IOException {
        Workbook wb = null;
        File file = new File(filePath);
        if (filePath.matches("^.+\\.(?i)(xlsx)$")) {
            wb = new XSSFWorkbook(new FileInputStream(file));
        } else if(filePath.matches("^.+\\.(?i)(xls)$")){
            wb = new HSSFWorkbook(new FileInputStream(file));
        }
        return wb;
    }

    public static boolean isRowEmpty(Row row,int t) {
        //判断行是否为空 为空 无效
        if (row == null){
            return true;
        }
    	//适用计划
    	if(t==1) {
            //第一列是序号，忽略；
            for (int c = row.getFirstCellNum()+1; c < row.getLastCellNum()-1; c++) {
                Cell cell = row.getCell(c);
                if (cell != null && cell.getCellType() != Cell.CELL_TYPE_BLANK ) {
                    return false;
                }
            }
        //适用员工
    	}else if(t==2){
            //最后两个条件的目的是判断公式中的值是否为空  add by wudezhong
            String sexText = getCellFormula(row.getCell(7));
            String birthdayText = getCellFormula(row.getCell(8));
            //第一列是序号，忽略；
            return  isSexByBirthday(sexText,birthdayText,row);
    	}
    	//员工被保险人
        else if (t==3) {
            String sexText = getCellFormula(row.getCell(4));
            String birthdayText = getCellFormula(row.getCell(5));
            return isSexByBirthday(sexText,birthdayText,row);
        }
        else if(t==4){
            String sexText = getCellFormula(row.getCell(4));
            String birthdayText = getCellFormula(row.getCell(5));
            return isSexByBirthday(sexText,birthdayText,row);
        }
        else if(t==5){
    	    String sexText=getCellFormula(row.getCell(2));
    	    String birthdayText=getCellFormula(row.getCell(3));
    	    return isSexByBirthday(sexText,birthdayText,row);
        }//模版导入家属
        else if (t==6){
            String sexText=getCellFormula(row.getCell(7));
            String birthdayText=getCellFormula(row.getCell(8));
            return isSexByBirthday(sexText,birthdayText,row);
        }//适用于学生
        else if (t==7){
            String sexText=getCellFormula(row.getCell(6));
            String birthdayText=getCellFormula(row.getCell(7));
            return isSexByBirthday(sexText,birthdayText,row);
        }//同质风险加减人
        else if(t==8) {
            String oldsexText=getCellFormula(row.getCell(4));
            String oldbirthdayText=getCellFormula(row.getCell(5));
            String newsexText=getCellFormula(row.getCell(9));
            String newbirthdayText=getCellFormula(row.getCell(10));
            if((!oldsexText.equals("") && oldsexText != null)
                    || (!oldbirthdayText.equals("") && oldbirthdayText != null)
                    || (!newsexText.equals("") && newsexText != null)
                    || (!newbirthdayText.equals("") && newbirthdayText != null)) {
                return false;
            }
            for (int c = row.getFirstCellNum()+1; c < row.getLastCellNum(); c++) {
                Cell cell = row.getCell(c);
                String value = ExcelUtil.getCellValue(cell);
                if (value != null && !"".equals(value)) {
                    return false;
                }
            }
            return true;
        }
        return true;
    }
    public static boolean isSexByBirthday(String sexText,String birthdayText,Row row){
        if((!sexText.equals("") && sexText != null) || (!birthdayText.equals("") && birthdayText != null)) {
            return false;
        }
        for (int c = row.getFirstCellNum()+1; c < row.getLastCellNum(); c++) {
            Cell cell = row.getCell(c);
            String value = ExcelUtil.getCellValue(cell);
            if (value != null && !"".equals(value)) {
                return false;
            }
        }
        return true;
    }
    public static boolean isCellEmpty(Cell cell){
        if (cell != null && cell.getCellType() != Cell.CELL_TYPE_BLANK) {
            String str = ExcelUtil.getCellValue(cell);
            if(str == null || "".equals(str.trim())){
                return true;
            }
            return false;
        }else{
            return true;
        }
    }

    /**
     * 获取单元格的值
     * @param cell
     * @return
     */
    public static String getCellValue(Cell cell){
        String cellValue = "";
        if(cell == null) {
            return "";
        }else if(cell.getCellType() == Cell.CELL_TYPE_STRING){
            cellValue = StringUtil.trim(cell.getStringCellValue());
            return cellValue;
        }else if(cell.getCellType() == Cell.CELL_TYPE_BOOLEAN){
            cellValue = String.valueOf(cell.getBooleanCellValue());
            return cellValue;
        }else if(cell.getCellType() == Cell.CELL_TYPE_FORMULA){
            try {
                cellValue = cell.getStringCellValue();
            } catch (IllegalStateException e) {
                cellValue = String.valueOf(cell.getNumericCellValue());
            }
            cellValue = StringUtil.trim(cellValue);
            return cellValue;
        }else if(cell.getCellType() == Cell.CELL_TYPE_NUMERIC){
            if (HSSFDateUtil.isCellDateFormatted(cell)) {
                Date date = cell.getDateCellValue();
                cellValue = String.valueOf(DateFormatUtils.format(date, "yyyy-MM-dd"));
                cellValue = StringUtil.trim(cellValue);
                return cellValue;
            } else {
                cellValue = String.valueOf(cell.getNumericCellValue());
                cellValue = StringUtil.trim(cellValue);
                return cellValue;
            }
        }
        return "";
    }
   
    /**
     * 获取单元格中公式的值
     * @param cell
     * @return
     */
    public static String getCellFormula(Cell cell){
    	if(cell == null) {
    		return "";
    	}else {
    		if(cell.getCellType() == cell.CELL_TYPE_FORMULA) {
    			return String.valueOf(cell.getRichStringCellValue());
    		}else {
    			return ExcelUtil.getCellValue(cell);
    		}	
    	}
    }
}
