package com.sinosoft.eflex.model.checkPerson;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/2 14:43
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckPersonRequest  implements Serializable {
    private String relationProve;
    private String openBank;
    private String isHasStuRule;
    private String sex;
    private String occupationName;
    @JsonProperty("JoinMedProtect")
    private String JoinMedProtect;
    @JsonProperty("iDType")
    private String IDType;
    @JsonProperty("iDNo")
    private String IDNo;
    private String birthDate;
    private String relation;
    private String occupationType;
    private String relationName;
    private String mobilePhone;
    private String occupationCode;
    private String isPlan;
    private String name;
    private String personId;
    private String nativeplace;
    private String nativeplaceName;
    private String idTypeEndDate;
    private String openAccount;
    private String email;
    private String staffGrpPrem;
    private String familyGrpPrem;

    private List<String> errorList;
}
