package com.sinosoft.eflex.model;

import java.util.Date;

public class FDEdorItem {
    /**
     * 
     */
    private String edorcode;

    /**
     * 
     */
    private String edorName;

    /**
     * 
     */
    private String isValid;

    /**
     * 
     */
    private String operator;

    /**
     * 
     */
    private String operatorCom;

    /**
     * 
     */
    private Date makeDate;

    /**
     * 
     */
    private String makeTime;

    /**
     * 
     */
    private Date modifyDate;

    /**
     * 
     */
    private String modifyTime;

    /**
     * 
     * @return edorcode 
     */
    public String getEdorcode() {
        return edorcode;
    }

    /**
     * 
     * @param edorcode 
     */
    public void setEdorcode(String edorcode) {
        this.edorcode = edorcode;
    }

    /**
     * 
     * @return edorName 
     */
    public String getEdorName() {
        return edorName;
    }

    /**
     * 
     * @param edorName 
     */
    public void setEdorName(String edorName) {
        this.edorName = edorName;
    }

    /**
     * 
     * @return IsValid 
     */
    public String getIsValid() {
        return isValid;
    }

    /**
     * 
     * @param isValid 
     */
    public void setIsValid(String isValid) {
        this.isValid = isValid;
    }

    /**
     * 
     * @return Operator 
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 
     * @param operator 
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 
     * @return OperatorCom 
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 
     * @param operatorCom 
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 
     * @return makeDate 
     */
    public Date getMakeDate() {
        return makeDate;
    }

    /**
     * 
     * @param makeDate 
     */
    public void setMakeDate(Date makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 
     * @return makeTime 
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 
     * @param makeTime 
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 
     * @return modifyDate 
     */
    public Date getModifyDate() {
        return modifyDate;
    }

    /**
     * 
     * @param modifyDate 
     */
    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 
     * @return modifyTime 
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 
     * @param modifyTime 
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}