package com.sinosoft.eflex.model;

public class FDPwdHist {
    /**
     * 登录密码流水号
     */
    private String passWordSN;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 用户密码
     */
    private String passWord;

    /**
     * 用户密码失效时间
     */
    private String passWordInvalidTime;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 入机日期
     */
    private String makeDate;

    /**
     * 入机时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 登录密码流水号
     * @return PassWordSN 登录密码流水号
     */
    public String getPassWordSN() {
        return passWordSN;
    }

    /**
     * 登录密码流水号
     * @param passWordSN 登录密码流水号
     */
    public void setPassWordSN(String passWordSN) {
        this.passWordSN = passWordSN;
    }

    /**
     * 用户编号
     * @return UserNo 用户编号
     */
    public String getUserNo() {
        return userNo;
    }

    /**
     * 用户编号
     * @param userNo 用户编号
     */
    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    /**
     * 用户密码
     * @return PassWord 用户密码
     */
    public String getPassWord() {
        return passWord;
    }

    /**
     * 用户密码
     * @param passWord 用户密码
     */
    public void setPassWord(String passWord) {
        this.passWord = passWord;
    }

    /**
     * 用户密码失效时间
     * 
     * @return PassWordInvalidTime 用户密码失效时间
     */
    public String getPassWordInvalidTime() {
        return passWordInvalidTime;
    }

    /**
     * 用户密码失效时间
     * 
     * @param passWordInvalidTime
     *            用户密码失效时间
     */
    public void setPassWordInvalidTime(String passWordInvalidTime) {
        this.passWordInvalidTime = passWordInvalidTime;
    }

    /**
     * 操作机构
     * @return OperatorCom 操作机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作机构
     * @param operatorCom 操作机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 入机日期
     * 
     * @return MakeDate 入机日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 入机日期
     * 
     * @param makeDate
     *            入机日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 入机时间
     * @return MakeTime 入机时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 入机时间
     * @param makeTime 入机时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * 
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * 
     * @param modifyDate
     *            修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}