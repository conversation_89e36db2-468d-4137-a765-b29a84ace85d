package com.sinosoft.eflex.model;

/**
 * <AUTHOR> wenying <PERSON>a
 * @date : 2020-07-30 15:47
 **/
public class FcOrderItemShareRela {
    /**
     * 分享流水号
     */
    private String shareDetalNo;

    /**
     * 子订单号
     */
    private String orderItemNo;

    /**
     * 分享链接状态 0--有效 1--无效
     */
    private String shareState;

    /**
     * 分享链接是否锁定 0-未锁定  1-已锁定
     */
    private String isLock;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 管理机构
     */
    private String operatorCom;

    /**
     * 入机日期
     */
    private String makeDate;

    /**
     * 入机时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    public String getIsLock() {
        return isLock;
    }

    public void setIsLock(String isLock) {
        this.isLock = isLock;
    }

    public String getShareDetalNo() {
        return shareDetalNo;
    }

    public void setShareDetalNo(String shareDetalNo) {
        this.shareDetalNo = shareDetalNo;
    }

    public String getOrderItemNo() {
        return orderItemNo;
    }

    public void setOrderItemNo(String orderItemNo) {
        this.orderItemNo = orderItemNo;
    }

    public String getShareState() {
        return shareState;
    }

    public void setShareState(String shareState) {
        this.shareState = shareState;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperatorCom() {
        return operatorCom;
    }

    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    public String getMakeDate() {
        return makeDate;
    }

    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    public String getMakeTime() {
        return makeTime;
    }

    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    public String getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}
