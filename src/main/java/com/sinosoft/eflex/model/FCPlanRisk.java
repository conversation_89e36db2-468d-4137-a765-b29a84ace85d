package com.sinosoft.eflex.model;

import lombok.*;

import java.util.List;


@Data
@ToString(callSuper = true)
@NoArgsConstructor
public class FCPlanRisk extends FCPlanRiskKey {
    /**
     * 险种名称
     */
    private String riskName;

    /**
     * 分保标记
     */
    private String reinsuranceMark;

    /**
     * 赠险标记
     */
    private String giftInsureSign;

    /**
     * 原保费
     */
    private Double originalPrem;

    /**
     * 手续费比率
     */
    private Double feeRatio;

    /**
     * 佣金/服务津贴率
     */
    private Double commissionOrAllowanceRatio;

    /**
     * 阅读 状态 0 未读 1已读
     */
    private Integer protocolReading;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 生成日期
     */
    private String makeDate;

    /**
     * 生成时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 计划险种责任
     */
    private List<FCPlanRiskDuty> fcPlanRiskDuties;


    public FCPlanRisk(String ensureCode, String riskCode, Integer protocolReading) {
        super(ensureCode, riskCode);
        this.protocolReading = protocolReading;
    }

    public FCPlanRisk(String ensureCode) {
        super(ensureCode, null);
    }
}