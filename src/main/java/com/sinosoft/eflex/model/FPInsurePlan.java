package com.sinosoft.eflex.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class FPInsurePlan implements Serializable {
    /**
     * 个人投保行为编号
     */
    private String insurePlanNo;

    /**
     * 福利编号
     */
    private String ensureCode;

    /**
     * 投保年度
     */
    private String appntYear;

    /**
     * 投保计划编码
     */
    private String planCode;

    /**
     * 投单状态 0-未提交订单表 1-已提交订单表 2-核心承保成功
     */
    private String insureState;

    /**
     * 个人投保人客户号
     */
    private String perno;

    /**
     * 被保人ID
     */
    private String personId;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 生成日期
     */
    private String makeDate;

    /**
     * 生成时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;
    /**
     * 是否确认提交 0 未确认提交  1  确认提交
     */
    private String isCommit;

    /**
     * 个人投保行为编号
     * 
     * @return InsurePlanNo 个人投保行为编号
     */
    public String getInsurePlanNo() {
        return insurePlanNo;
    }

    /**
     * 个人投保行为编号
     * 
     * @param insurePlanNo
     *            个人投保行为编号
     */
    public void setInsurePlanNo(String insurePlanNo) {
        this.insurePlanNo = insurePlanNo;
    }

    /**
     * 福利编号
     * 
     * @return EnsureCode 福利编号
     */
    public String getEnsureCode() {
        return ensureCode;
    }

    /**
     * 福利编号
     * 
     * @param ensureCode
     *            福利编号
     */
    public void setEnsureCode(String ensureCode) {
        this.ensureCode = ensureCode;
    }

    /**
     * 投保年度
     * 
     * @return AppntYear 投保年度
     */
    public String getAppntYear() {
        return appntYear;
    }

    /**
     * 投保年度
     * 
     * @param appntYear
     *            投保年度
     */
    public void setAppntYear(String appntYear) {
        this.appntYear = appntYear;
    }

    /**
     * 投保计划编码
     * 
     * @return PlanCode 投保计划编码
     */
    public String getPlanCode() {
        return planCode;
    }

    /**
     * 投保计划编码
     * 
     * @param planCode
     *            投保计划编码
     */
    public void setPlanCode(String planCode) {
        this.planCode = planCode;
    }

    /**
     * 投单状态 0-未提交订单表 1-已提交订单表 2-核心承保成功
     * 
     * @return InsureState 投单状态
     */
    public String getInsureState() {
        return insureState;
    }

    /**
     * 投单状态 0-未提交订单表 1-已提交订单表 2-核心承保成功
     * 
     * @param insureState
     *            投单状态
     */
    public void setInsureState(String insureState) {
        this.insureState = insureState;
    }

    /**
     * 个人投保人客户号
     * 
     * @return Perno 个人投保人客户号
     */
    public String getPerno() {
        return perno;
    }

    /**
     * 个人投保人客户号
     * 
     * @param perno
     *            个人投保人客户号
     */
    public void setPerno(String perno) {
        this.perno = perno;
    }

    /**
     * 被保人ID
     * 
     * @return personId 被保人ID
     */
    public String getPersonId() {
        return personId;
    }

    /**
     * 被保人ID
     * 
     * @param personId
     *            被保人ID
     */
    public void setPersonId(String personId) {
        this.personId = personId;
    }

    /**
     * 操作员
     * 
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * 
     * @param operator
     *            操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 操作机构
     * 
     * @return OperatorCom 操作机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作机构
     * 
     * @param operatorCom
     *            操作机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 生成日期
     * 
     * @return MakeDate 生成日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 生成日期
     * 
     * @param makeDate
     *            生成日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 生成时间
     * 
     * @return MakeTime 生成时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 生成时间
     * 
     * @param makeTime
     *            生成时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * 
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * 
     * @param modifyDate
     *            修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * 
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * 
     * @param modifyTime
     *            修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "FPInsurePlan [insurePlanNo=" + insurePlanNo + ", ensureCode=" + ensureCode + ", appntYear=" + appntYear
                + ", planCode=" + planCode + ", insureState=" + insureState + ", perno=" + perno + ", personId="
                + personId + ", operator=" + operator + ", operatorCom=" + operatorCom + ", makeDate=" + makeDate
                + ", makeTime=" + makeTime + ", modifyDate=" + modifyDate + ", modifyTime=" + modifyTime + "]";
    }

    public String getIsCommit() {
        return isCommit;
    }

    public void setIsCommit(String isCommit) {
        this.isCommit = isCommit;
    }
}