package com.sinosoft.eflex.model;

import java.util.Map;

public class Message {
	/**
	 * 操作是否成功
	 */
	private boolean mSuccess = true;
	/**
	 * 处理时错误信息
	 */
	private String mCErrors = null;
	/**
	 * 处理结果
	 */
	private Map<String, Object> mResult = null;
	
	public boolean isSuccess() {
		return mSuccess;
	}
	public void setmSuccess(boolean mSuccess) {
		this.mSuccess = mSuccess;
	}
	public String getmCErrors() {
		return mCErrors;
	}
	public void setmCErrors(String mCErrors) {
		this.mCErrors = mCErrors;
	}
	public Map<String, Object> getmResult() {
		return mResult;
	}
	public void setmResult(Map<String, Object> mResult) {
		this.mResult = mResult;
	}

}
