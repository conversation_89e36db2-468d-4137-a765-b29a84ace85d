package com.sinosoft.eflex.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 被保人表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FCOrderInsured implements Serializable {

	private static final long serialVersionUID = -7830948614196740079L;
	private String oldIdNo;
	private String edorType;
	/**
	 * 订单项编号
	 */
	private String orderItemNo;

	/**
	 * 订单号
	 */
	private String orderNo;

	/**
	 * 团体订单号
	 */
	private String grpOrderNo;

	/**
	 * 被保险人ID
	 */
	private String personID;

	/**
	 * 姓名
	 */
	private String name;

	/**
	 * 性别: 0 男 1 女
	 */
	private String sex;

	/**
	 * 出生日期
	 */
	private String birthday;

	/**
	 * 证件类型
	 */
	private String IDType;

	/**
	 * 证件号
	 */
	private String IDNo;

    /**
     * 国籍
     */
    private String Nativeplace;

    /**
	 * 手机号
	 */
	private String mobilePhone;

	/**
	 * 固定电话
	 */
	private String phone;

	/**
	 * 部门
	 */
	private String department;

	/**
	 * 职业类别
	 */
	private String occupationType;

	/**
	 * 职业代码
	 */
	private String occupationCode;

	/**
	 * 是否参加医保: 0-否 1-是
	 */
	private String joinMedProtect;

	/**
	 * 医保类型
	 */
	private String medProtectType;

	/**
	 * 邮箱
	 */
	private String EMail;
	
	private String grpPrem;
	private String empPrem;

	/**
	 * 邮编
	 */
	private String zipCode;

	/**
	 * 通讯地址
	 */
	private String address;

	/**
	 * 操作员
	 */
	private String operator;

	/**
	 * 操作机构
	 */
	private String operatorCom;

	//省
	private String province;
	//市
	private String city;
	//县
	private String county;
	//详细地址
	private String detaileAddress;
	//主被保人省
	private String mainProvince;
	//主被保人市
	private String mainCity;
	//主被保人县
	private String mainCounty;
	//主被保人详细地址
	private String mainDetaileAddress;
	//主被保人手机号码
	private String mainMobilePhone;
	//主被保人邮编
	private String mainZipCode;
	//主被保人邮箱
	private String mainEMail;
	//主被保人年收入
	private String mainYearSalary;

	/**
	 * 生成日期
	 */
	private String makeDate;

	/**
	 * 生成时间
	 */
	private String makeTime;

	/**
	 * 修改日期
	 */
	private String modifyDate;

	/**
	 * 修改时间
	 */
	private String modifyTime;

	/**
	 * 子订单表
	 */
	private List<FCOrderItem> fcOrderItemList;

	/**
	 * 员工家属信息关联表
	 */
	private FCStaffFamilyRela fcStaffFamilyRela;

	
	public List<FCOrderItem> getFcOrderItemList() {
		return fcOrderItemList;
	}

	public void setFcOrderItemList(List<FCOrderItem> fcOrderItemList) {
		this.fcOrderItemList = fcOrderItemList;
	}

	/**
	 * 订单项编号
	 * 
	 * @return OrderItemNo 订单项编号
	 */
	public String getOrderItemNo() {
		return orderItemNo;
	}

	/**
	 * 订单项编号
	 * 
	 * @param orderItemNo
	 *            订单项编号
	 */
	public void setOrderItemNo(String orderItemNo) {
		this.orderItemNo = orderItemNo;
	}

	/**
	 * 订单号
	 * 
	 * @return OrderNo 订单号
	 */
	public String getOrderNo() {
		return orderNo;
	}

	/**
	 * 订单号
	 * 
	 * @param orderNo
	 *            订单号
	 */
	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	/**
	 * 团体订单号
	 * 
	 * @return GrpOrderNo 团体订单号
	 */
	public String getGrpOrderNo() {
		return grpOrderNo;
	}

	/**
	 * 团体订单号
	 * 
	 * @param grpOrderNo
	 *            团体订单号
	 */
	public void setGrpOrderNo(String grpOrderNo) {
		this.grpOrderNo = grpOrderNo;
	}

	/**
	 * 被保险人ID
	 * 
	 * @return PersonID 被保险人ID
	 */
	public String getPersonID() {
		return personID;
	}

	/**
	 * 被保险人ID
	 * 
	 * @param personID
	 *            被保险人ID
	 */
	public void setPersonID(String personID) {
		this.personID = personID;
	}

	/**
	 * 姓名
	 * 
	 * @return Name 姓名
	 */
	public String getName() {
		return name;
	}

	/**
	 * 姓名
	 * 
	 * @param name
	 *            姓名
	 */
	public void setName(String name) {
		this.name = name;
	}

	/**
	 * 性别: 0 男 1 女
	 * 
	 * @return Sex 性别: 0 男 1 女
	 */
	public String getSex() {
		return sex;
	}

	/**
	 * 性别: 0 男 1 女
	 * 
	 * @param sex
	 *            性别: 0 男 1 女
	 */
	public void setSex(String sex) {
		this.sex = sex;
	}

	/**
	 * 出生日期
	 * 
	 * @return Birthday 出生日期
	 */
	public String getBirthDay() {
		return birthday;
	}

	/**
	 * 出生日期
	 * 
	 * @param birthday
	 *            出生日期
	 */
	public void setBirthDay(String birthday) {
		this.birthday = birthday;
	}

	/**
	 * 证件类型
	 * 
	 * @return IDType 证件类型
	 */
	public String getIDType() {
		return IDType;
	}

	/**
	 * 证件类型
	 * 
	 * @param IDType
	 *            证件类型
	 */
	public void setIDType(String IDType) {
		this.IDType = IDType;
	}

	/**
	 * 证件号
	 * 
	 * @return IDNo 证件号
	 */
	public String getIDNo() {
		return IDNo;
	}

	/**
	 * 证件号
	 * 
	 * @param IDNo
	 *            证件号
	 */
	public void setIDNo(String IDNo) {
		this.IDNo = IDNo;
	}

	/**
	 * 手机号
	 * 
	 * @return MobilePhone 手机号
	 */
	public String getMobilePhone() {
		return mobilePhone;
	}

	/**
	 * 手机号
	 * 
	 * @param mobilePhone
	 *            手机号
	 */
	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}

	/**
	 * 固定电话
	 * 
	 * @return Phone 固定电话
	 */
	public String getPhone() {
		return phone;
	}

	/**
	 * 固定电话
	 * 
	 * @param phone
	 *            固定电话
	 */
	public void setPhone(String phone) {
		this.phone = phone;
	}

	/**
	 * 部门
	 * 
	 * @return department 部门
	 */
	public String getDepartment() {
		return department;
	}

	/**
	 * 部门
	 * 
	 * @param department
	 *            部门
	 */
	public void setDepartment(String department) {
		this.department = department;
	}

	/**
	 * 职业类别
	 * 
	 * @return OccupationType 职业类别
	 */
	public String getOccupationType() {
		return occupationType;
	}

	/**
	 * 职业类别
	 * 
	 * @param occupationType
	 *            职业类别
	 */
	public void setOccupationType(String occupationType) {
		this.occupationType = occupationType;
	}

	/**
	 * 职业代码
	 * 
	 * @return OccupationCode 职业代码
	 */
	public String getOccupationCode() {
		return occupationCode;
	}

	/**
	 * 职业代码
	 * 
	 * @param occupationCode
	 *            职业代码
	 */
	public void setOccupationCode(String occupationCode) {
		this.occupationCode = occupationCode;
	}

	/**
	 * 是否参加医保: 0-否 1-是
	 * 
	 * @return JoinMedProtect 是否参加医保: 0-否 1-是
	 */
	public String getJoinMedProtect() {
		return joinMedProtect;
	}

	/**
	 * 是否参加医保: 0-否 1-是
	 * 
	 * @param joinMedProtect
	 *            是否参加医保: 0-否 1-是
	 */
	public void setJoinMedProtect(String joinMedProtect) {
		this.joinMedProtect = joinMedProtect;
	}

	/**
	 * 医保类型
	 * 
	 * @return MedProtectType 医保类型
	 */
	public String getMedProtectType() {
		return medProtectType;
	}

	/**
	 * 医保类型
	 * 
	 * @param medProtectType
	 *            医保类型
	 */
	public void setMedProtectType(String medProtectType) {
		this.medProtectType = medProtectType;
	}

	/**
	 * 邮箱
	 * 
	 * @return EMail 邮箱
	 */
	public String getEMail() {
		return EMail;
	}

	/**
	 * 邮箱
	 * 
	 * @param EMail
	 *            邮箱
	 */
	public void setEMail(String EMail) {
		this.EMail = EMail;
	}

	/**
	 * 邮编
	 * 
	 * @return ZipCode 邮编
	 */
	public String getZipCode() {
		return zipCode;
	}

	/**
	 * 邮编
	 * 
	 * @param zipCode
	 *            邮编
	 */
	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}

	/**
	 * 通讯地址
	 * 
	 * @return Address 通讯地址
	 */
	public String getAddress() {
		return address;
	}

	/**
	 * 通讯地址
	 * 
	 * @param address
	 *            通讯地址
	 */
	public void setAddress(String address) {
		this.address = address;
	}

	/**
	 * 操作员
	 * 
	 * @return Operator 操作员
	 */
	public String getOperator() {
		return operator;
	}

	/**
	 * 操作员
	 * 
	 * @param operator
	 *            操作员
	 */
	public void setOperator(String operator) {
		this.operator = operator;
	}

	/**
	 * 操作机构
	 * 
	 * @return OperatorCom 操作机构
	 */
	public String getOperatorCom() {
		return operatorCom;
	}

	/**
	 * 操作机构
	 * 
	 * @param operatorCom
	 *            操作机构
	 */
	public void setOperatorCom(String operatorCom) {
		this.operatorCom = operatorCom;
	}

	/**
	 * 生成日期
	 * 
	 * @return MakeDate 生成日期
	 */
	public String getMakeDate() {
		return makeDate;
	}

	/**
	 * 生成日期
	 * 
	 * @param makeDate
	 *            生成日期
	 */
	public void setMakeDate(String makeDate) {
		this.makeDate = makeDate;
	}

	/**
	 * 生成时间
	 * 
	 * @return MakeTime 生成时间
	 */
	public String getMakeTime() {
		return makeTime;
	}

	/**
	 * 生成时间
	 * 
	 * @param makeTime
	 *            生成时间
	 */
	public void setMakeTime(String makeTime) {
		this.makeTime = makeTime;
	}

	/**
	 * 修改日期
	 * 
	 * @return ModifyDate 修改日期
	 */
	public String getModifyDate() {
		return modifyDate;
	}

	/**
	 * 修改日期
	 * 
	 * @param modifyDate
	 *            修改日期
	 */
	public void setModifyDate(String modifyDate) {
		this.modifyDate = modifyDate;
	}

	/**
	 * 修改时间
	 * 
	 * @return ModifyTime 修改时间
	 */
	public String getModifyTime() {
		return modifyTime;
	}

	/**
	 * 修改时间
	 * 
	 * @param modifyTime
	 *            修改时间
	 */
	public void setModifyTime(String modifyTime) {
		this.modifyTime = modifyTime;
	}

	
	/**
	 * @return the fcStaffFamilyRela
	 */
	public FCStaffFamilyRela getFcStaffFamilyRela() {
		return fcStaffFamilyRela;
	}

	/**
	 * @param fcStaffFamilyRela the fcStaffFamilyRela to set
	 */
	public void setFcStaffFamilyRela(FCStaffFamilyRela fcStaffFamilyRela) {
		this.fcStaffFamilyRela = fcStaffFamilyRela;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see java.lang.Object#hashCode()
	 */
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((EMail == null) ? 0 : EMail.hashCode());
		result = prime * result + ((IDNo == null) ? 0 : IDNo.hashCode());
		result = prime * result + ((IDType == null) ? 0 : IDType.hashCode());
		result = prime * result + ((address == null) ? 0 : address.hashCode());
		result = prime * result + ((birthday == null) ? 0 : birthday.hashCode());
		result = prime * result + ((department == null) ? 0 : department.hashCode());
		result = prime * result + ((grpOrderNo == null) ? 0 : grpOrderNo.hashCode());
		result = prime * result + ((joinMedProtect == null) ? 0 : joinMedProtect.hashCode());
		result = prime * result + ((makeDate == null) ? 0 : makeDate.hashCode());
		result = prime * result + ((makeTime == null) ? 0 : makeTime.hashCode());
		result = prime * result + ((medProtectType == null) ? 0 : medProtectType.hashCode());
		result = prime * result + ((mobilePhone == null) ? 0 : mobilePhone.hashCode());
		result = prime * result + ((modifyDate == null) ? 0 : modifyDate.hashCode());
		result = prime * result + ((modifyTime == null) ? 0 : modifyTime.hashCode());
		result = prime * result + ((name == null) ? 0 : name.hashCode());
		result = prime * result + ((occupationCode == null) ? 0 : occupationCode.hashCode());
		result = prime * result + ((occupationType == null) ? 0 : occupationType.hashCode());
		result = prime * result + ((operator == null) ? 0 : operator.hashCode());
		result = prime * result + ((operatorCom == null) ? 0 : operatorCom.hashCode());
		result = prime * result + ((orderItemNo == null) ? 0 : orderItemNo.hashCode());
		result = prime * result + ((orderNo == null) ? 0 : orderNo.hashCode());
		result = prime * result + ((personID == null) ? 0 : personID.hashCode());
		result = prime * result + ((phone == null) ? 0 : phone.hashCode());
		result = prime * result + ((sex == null) ? 0 : sex.hashCode());
		result = prime * result + ((zipCode == null) ? 0 : zipCode.hashCode());
		return result;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see java.lang.Object#equals(java.lang.Object)
	 */
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (!(obj instanceof FCOrderInsured))
			return false;
		FCOrderInsured other = (FCOrderInsured) obj;
		if (EMail == null) {
			if (other.EMail != null)
				return false;
		} else if (!EMail.equals(other.EMail))
			return false;
		if (IDNo == null) {
			if (other.IDNo != null)
				return false;
		} else if (!IDNo.equals(other.IDNo))
			return false;
		if (IDType == null) {
			if (other.IDType != null)
				return false;
		} else if (!IDType.equals(other.IDType))
			return false;
		if (address == null) {
			if (other.address != null)
				return false;
		} else if (!address.equals(other.address))
			return false;
		if (birthday == null) {
			if (other.birthday != null)
				return false;
		} else if (!birthday.equals(other.birthday))
			return false;
		if (department == null) {
			if (other.department != null)
				return false;
		} else if (!department.equals(other.department))
			return false;
		if (grpOrderNo == null) {
			if (other.grpOrderNo != null)
				return false;
		} else if (!grpOrderNo.equals(other.grpOrderNo))
			return false;
		if (joinMedProtect == null) {
			if (other.joinMedProtect != null)
				return false;
		} else if (!joinMedProtect.equals(other.joinMedProtect))
			return false;
		if (makeDate == null) {
			if (other.makeDate != null)
				return false;
		} else if (!makeDate.equals(other.makeDate))
			return false;
		if (makeTime == null) {
			if (other.makeTime != null)
				return false;
		} else if (!makeTime.equals(other.makeTime))
			return false;
		if (medProtectType == null) {
			if (other.medProtectType != null)
				return false;
		} else if (!medProtectType.equals(other.medProtectType))
			return false;
		if (mobilePhone == null) {
			if (other.mobilePhone != null)
				return false;
		} else if (!mobilePhone.equals(other.mobilePhone))
			return false;
		if (modifyDate == null) {
			if (other.modifyDate != null)
				return false;
		} else if (!modifyDate.equals(other.modifyDate))
			return false;
		if (modifyTime == null) {
			if (other.modifyTime != null)
				return false;
		} else if (!modifyTime.equals(other.modifyTime))
			return false;
		if (name == null) {
			if (other.name != null)
				return false;
		} else if (!name.equals(other.name))
			return false;
		if (occupationCode == null) {
			if (other.occupationCode != null)
				return false;
		} else if (!occupationCode.equals(other.occupationCode))
			return false;
		if (occupationType == null) {
			if (other.occupationType != null)
				return false;
		} else if (!occupationType.equals(other.occupationType))
			return false;
		if (operator == null) {
			if (other.operator != null)
				return false;
		} else if (!operator.equals(other.operator))
			return false;
		if (operatorCom == null) {
			if (other.operatorCom != null)
				return false;
		} else if (!operatorCom.equals(other.operatorCom))
			return false;
		if (orderItemNo == null) {
			if (other.orderItemNo != null)
				return false;
		} else if (!orderItemNo.equals(other.orderItemNo))
			return false;
		if (orderNo == null) {
			if (other.orderNo != null)
				return false;
		} else if (!orderNo.equals(other.orderNo))
			return false;
		if (personID == null) {
			if (other.personID != null)
				return false;
		} else if (!personID.equals(other.personID))
			return false;
		if (phone == null) {
			if (other.phone != null)
				return false;
		} else if (!phone.equals(other.phone))
			return false;
		if (sex == null) {
			if (other.sex != null)
				return false;
		} else if (!sex.equals(other.sex))
			return false;
		if (zipCode == null) {
			if (other.zipCode != null)
				return false;
		} else if (!zipCode.equals(other.zipCode))
			return false;
		return true;
	}

	public String getGrpPrem() {
		return grpPrem;
	}

	public void setGrpPrem(String grpPrem) {
		this.grpPrem = grpPrem;
	}

	public String getEmpPrem() {
		return empPrem;
	}

	public void setEmpPrem(String empPrem) {
		this.empPrem = empPrem;
	}

	public String getBirthday() {
		return birthday;
	}

	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	public String getDetaileAddress() {
		return detaileAddress;
	}

	public void setDetaileAddress(String detaileAddress) {
		this.detaileAddress = detaileAddress;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getMainProvince() {
		return mainProvince;
	}

	public void setMainProvince(String mainProvince) {
		this.mainProvince = mainProvince;
	}

	public String getMainCity() {
		return mainCity;
	}

	public void setMainCity(String mainCity) {
		this.mainCity = mainCity;
	}

	public String getMainCounty() {
		return mainCounty;
	}

	public void setMainCounty(String mainCounty) {
		this.mainCounty = mainCounty;
	}

	public String getMainDetaileAddress() {
		return mainDetaileAddress;
	}

	public void setMainDetaileAddress(String mainDetaileAddress) {
		this.mainDetaileAddress = mainDetaileAddress;
	}

	public String getMainMobilePhone() {
		return mainMobilePhone;
	}

	public void setMainMobilePhone(String mainMobilePhone) {
		this.mainMobilePhone = mainMobilePhone;
	}

	public String getMainZipCode() {
		return mainZipCode;
	}

	public void setMainZipCode(String mainZipCode) {
		this.mainZipCode = mainZipCode;
	}

	public String getMainEMail() {
		return mainEMail;
	}

	public void setMainEMail(String mainEMail) {
		this.mainEMail = mainEMail;
	}

	public String getMainYearSalary() {
		return mainYearSalary;
	}

	public void setMainYearSalary(String mainYearSalary) {
		this.mainYearSalary = mainYearSalary;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return "FCOrderInsured [orderItemNo=" + orderItemNo + ", orderNo=" + orderNo + ", grpOrderNo=" + grpOrderNo
				+ ", personID=" + personID + ", name=" + name + ", sex=" + sex + ", birthday=" + birthday + ", IDType="
				+ IDType + ", IDNo=" + IDNo + ", mobilePhone=" + mobilePhone + ", phone=" + phone + ", department="
				+ department + ", occupationType=" + occupationType + ", occupationCode=" + occupationCode
				+ ", joinMedProtect=" + joinMedProtect + ", medProtectType=" + medProtectType + ", EMail=" + EMail
				+ ", zipCode=" + zipCode + ", address=" + address + ", operator=" + operator + ", operatorCom="
				+ operatorCom + ", makeDate=" + makeDate + ", makeTime=" + makeTime + ", modifyDate=" + modifyDate
				+ ", modifyTime=" + modifyTime + "]";
	}

}