package com.sinosoft.eflex.model.edor;

import com.alibaba.fastjson.annotation.JSONField;

public class EdoraTrialInfo {
	/**
	 *  团体保单号
	 */
	private String grpContNo;

	/**
	 * 被保人信息变更
	 */
	private InsuredChgTrialIO insuredChgTrialIO;

	/**
	 * 投保人信息变更
	 */
	private PolicyHolderChgTrialIO policyHolderChgTrialIO;

	/**
	 * 受益人资料变更试算接口
	 */
	private BnfChgTrialIO bnfChgTrialIO;

	/**
	 * 增人标记
	 */
	private boolean NIFlag;

	/**
	 * 减人标记
	 */
	private boolean ZTFlag;
	
	/**
	 * 同质风险加减人标记
	 */
	private boolean HNIFlag;
	
	public boolean isHNIFlag() {
		return HNIFlag;
	}

	public void setHNIFlag(boolean hNIFlag) {
		HNIFlag = hNIFlag;
	}

	/**
	 * 减人批次号
	 */
	private String decBatch;

	/**
	 * 增加人数
	 */
	private int InsuredPeoples;

	/**
	 * 增人批次号
	 */
	private String addBatch;
	
	/**
	 * 同质风险加减人批次号
	 */
	private String HrarBatch;
	
	public String getHrarBatch() {
		return HrarBatch;
	}

	public void setHrarBatch(String hrarBatch) {
		HrarBatch = hrarBatch;
	}

	@JSONField(name = "NIFlag")
	public boolean isNIFlag() {
		return NIFlag;
	}

	public void setNIFlag(boolean NIFlag) {
		this.NIFlag = NIFlag;
	}

	@JSONField(name = "ZTFlag")
	public boolean isZTFlag() {
		return ZTFlag;
	}

	public void setZTFlag(boolean ZTFlag) {
		this.ZTFlag = ZTFlag;
	}

	public String getAddBatch() {
		return addBatch;
	}

	public void setAddBatch(String addBatch) {
		this.addBatch = addBatch;
	}

	public int getInsuredPeoples() {
		return InsuredPeoples;
	}

	public void setInsuredPeoples(int insuredPeoples) {
		InsuredPeoples = insuredPeoples;
	}

	public String getGrpContNo() {
		return grpContNo;
	}

	public void setGrpContNo(String grpContNo) {
		this.grpContNo = grpContNo;
	}

	public String getDecBatch() {
		return decBatch;
	}

	public void setDecBatch(String decBatch) {
		this.decBatch = decBatch;
	}

	public InsuredChgTrialIO getInsuredChgTrialIO() {
		return insuredChgTrialIO;
	}

	public void setInsuredChgTrialIO(InsuredChgTrialIO insuredChgTrialIO) {
		this.insuredChgTrialIO = insuredChgTrialIO;
	}

	public PolicyHolderChgTrialIO getPolicyHolderChgTrialIO() {
		return policyHolderChgTrialIO;
	}

	public void setPolicyHolderChgTrialIO(PolicyHolderChgTrialIO policyHolderChgTrialIO) {
		this.policyHolderChgTrialIO = policyHolderChgTrialIO;
	}

	public BnfChgTrialIO getBnfChgTrialIO() {
		return bnfChgTrialIO;
	}

	public void setBnfChgTrialIO(BnfChgTrialIO bnfChgTrialIO) {
		this.bnfChgTrialIO = bnfChgTrialIO;
	}
}
