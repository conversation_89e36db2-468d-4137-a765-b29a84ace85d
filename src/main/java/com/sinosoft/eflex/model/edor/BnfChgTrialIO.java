package com.sinosoft.eflex.model.edor;

import com.sinosoft.eflex.model.Insured;

import java.util.List;

/**
 * Create 2018/12/6.
 */
public class BnfChgTrialIO {

    private boolean edorFlag;

    private String edorAppNo;

    private List<Insureds> insuredList;

    public boolean isEdorFlag() { return edorFlag; }

    public void setEdorFlag(boolean edorFlag) { this.edorFlag = edorFlag; }

    public String getEdorAppNo() {
        return edorAppNo;
    }

    public void setEdorAppNo(String edorAppNo) {
        this.edorAppNo = edorAppNo;
    }

    public List<Insureds> getInsuredList() {
        return insuredList;
    }

    public void setInsuredList(List<Insureds> insuredList) {
        this.insuredList = insuredList;
    }
}
