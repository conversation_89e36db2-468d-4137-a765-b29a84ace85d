package com.sinosoft.eflex.model.edor;

/**
 * <AUTHOR>
 * @create 2022/3/11
 * @desc 调用核心保单查询请求体
 */
public class GrpContQuery {

    /**
     * 企业证件类型-Y
     */
    private String GrpIdType;

    /**
     * 企业证件类型名称-Y
     */
    private String GrpIdTypeName;

    /**
     * 企业证件号-Y
     */
    private String GrpIdNo;

    /**
     * 保单号-N
     */
    private String GrpContNo;

    /**
     * 保单生效日期-N
     */
    private String EffectDate;

    /**
     * 保单截至日期-N
     */
    private String EndDate;

    /**
     * 保单状态-N
     */
    private String Status;

    /**
     * 页码-Y
     */
    private String PageNum;

    /**
     * 每页条数-Y
     */
    private String PageSize;

    public String getGrpIdType() {
        return GrpIdType;
    }

    public void setGrpIdType(String grpIdType) {
        GrpIdType = grpIdType;
    }

    public String getGrpIdTypeName() {
        return GrpIdTypeName;
    }

    public void setGrpIdTypeName(String grpIdTypeName) {
        GrpIdTypeName = grpIdTypeName;
    }

    public String getGrpIdNo() {
        return GrpIdNo;
    }

    public void setGrpIdNo(String grpIdNo) {
        GrpIdNo = grpIdNo;
    }

    public String getGrpContNo() {
        return GrpContNo;
    }

    public void setGrpContNo(String grpContNo) {
        GrpContNo = grpContNo;
    }

    public String getEffectDate() {
        return EffectDate;
    }

    public void setEffectDate(String effectDate) {
        EffectDate = effectDate;
    }

    public String getEndDate() {
        return EndDate;
    }

    public void setEndDate(String endDate) {
        EndDate = endDate;
    }

    public String getStatus() {
        return Status;
    }

    public void setStatus(String status) {
        Status = status;
    }

    public String getPageNum() {
        return PageNum;
    }

    public void setPageNum(String pageNum) {
        PageNum = pageNum;
    }

    public String getPageSize() {
        return PageSize;
    }

    public void setPageSize(String pageSize) {
        PageSize = pageSize;
    }
}
