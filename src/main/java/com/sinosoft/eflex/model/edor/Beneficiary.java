package com.sinosoft.eflex.model.edor;

/**
 * Created  2018/12/6.
 */
public class Beneficiary {
    private String bnfName;
    private String bnfType;
    private String bnfIdNo;
    private String bnfIdType;
    private String relationToInsured;
    private String bnfGrade;
    private String bnfLot;
    private String bnfSex;
    private String bnfBirthday;

    public String getBnfName() {
        return bnfName;
    }

    public void setBnfName(String bnfName) {
        this.bnfName = bnfName;
    }

    public String getBnfType() {
        return bnfType;
    }

    public void setBnfType(String bnfType) {
        this.bnfType = bnfType;
    }

    public String getBnfIdNo() {
        return bnfIdNo;
    }

    public void setBnfIdNo(String bnfIdNo) {
        this.bnfIdNo = bnfIdNo;
    }

    public String getBnfIdType() {
        return bnfIdType;
    }

    public void setBnfIdType(String bnfIdType) {
        this.bnfIdType = bnfIdType;
    }

    public String getRelationToInsured() {
        return relationToInsured;
    }

    public void setRelationToInsured(String relationToInsured) {
        this.relationToInsured = relationToInsured;
    }

    public String getBnfGrade() {
        return bnfGrade;
    }

    public void setBnfGrade(String bnfGrade) {
        this.bnfGrade = bnfGrade;
    }

    public String getBnfLot() {
        return bnfLot;
    }

    public void setBnfLot(String bnfLot) {
        this.bnfLot = bnfLot;
    }

    public String getBnfSex() {
        return bnfSex;
    }

    public void setBnfSex(String bnfSex) {
        this.bnfSex = bnfSex;
    }

    public String getBnfBirthday() {
        return bnfBirthday;
    }

    public void setBnfBirthday(String bnfBirthday) {
        this.bnfBirthday = bnfBirthday;
    }
}
