package com.sinosoft.eflex.model.edor;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/9/7
 */
@Data
public class CoreInsuredInfo {

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private String sex;

    /**
     * 出生日期
     */
    private String birthday;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 主被保险人姓名
     */
    private String mainInsuredName;

    /**
     * 主被保险人客户号
     */
    private String mainInsuredNo;

    /**
     * 与主被保险人关系
     */
    private String mainRelation;

    /**
     * 补交保费
     */
    private String prem;

    /**
     * 结算类型
     */
    private String accountType;

}
