package com.sinosoft.eflex.model.edor;

import java.util.List;

/**
 * @DESCRIPTION
 * @create 2018-12-05 15:51
 **/
public class ESViewInsure {
	private String subType;
	private String pageNum;
	private List<PageInsure> page;
	public String getSubType() {
		return subType==null?"":subType;
	}
	public void setSubType(String subType) {
		this.subType = subType;
	}
	public String getPageNum() {
		return pageNum==null?"":pageNum;
	}
	public void setPageNum(String pageNum) {
		this.pageNum = pageNum;
	}
	public List<PageInsure> getPage() {
		return page;
	}
	public void setPage(List<PageInsure> page) {
		this.page = page;
	}
	
}
