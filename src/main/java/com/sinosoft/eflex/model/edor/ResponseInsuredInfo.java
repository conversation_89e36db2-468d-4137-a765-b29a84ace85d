package com.sinosoft.eflex.model.edor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-12-13 18:46
 */
public class ResponseInsuredInfo {
    private String edorFlag;
    private String edorMark;
    private String grpContNo;
    private String edorAppNo;
    private String edorCvaliDate;
    private List<addInsuredInfo > addInsuredInfoList;
    public String getEdorFlag() {
        return edorFlag;
    }

    public void setEdorFlag(String edorFlag) {
        this.edorFlag = edorFlag;
    }

    public String getEdorMark() {
        return edorMark;
    }

    public void setEdorMark(String edorMark) {
        this.edorMark = edorMark;
    }

    public String getGrpContNo() {
        return grpContNo;
    }

    public void setGrpContNo(String grpContNo) {
        this.grpContNo = grpContNo;
    }

    public String getEdorAppNo() {
        return edorAppNo;
    }

    public void setEdorAppNo(String edorAppNo) {
        this.edorAppNo = edorAppNo;
    }

    public String getEdorCvaliDate() {
        return edorCvaliDate;
    }

    public void setEdorCvaliDate(String edorCvaliDate) {
        this.edorCvaliDate = edorCvaliDate;
    }

    public List<addInsuredInfo> getAddInsuredInfoList() {
        return addInsuredInfoList;
    }

    public void setAddInsuredInfoList(List<addInsuredInfo> addInsuredInfoList) {
        this.addInsuredInfoList = addInsuredInfoList;
    }
}
