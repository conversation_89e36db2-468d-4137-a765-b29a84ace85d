package com.sinosoft.eflex.model.edor;

/**
 * <AUTHOR>
 * @date 2019-01-03 15:05
 */
public class Insurers {

    private String  name;

    private String newName;

    private String  sex;

    private String birthday;

    private String nationality;

    private String newNativePlace;

    private String iDType;

    private String idNo;

    private String newIdType;

    private String newIdNo;


    private String occupationType;

    private String occupationCode;

    private String mobile;

    private String medicareStatus;

    private String email;

    private String idExpDate;

    public String getIdExpDate() {
        return idExpDate;
    }

    public void setIdExpDate(String idExpDate) {
        this.idExpDate = idExpDate;
    }

    public String getNewIdType() {
        return newIdType;
    }

    public void setNewIdType(String newIdType) {
        this.newIdType = newIdType;
    }

    public String getNewIdNo() {
        return newIdNo;
    }

    public void setNewIdNo(String newIdNo) {
        this.newIdNo = newIdNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getiDType() {
        return iDType;
    }

    public void setiDType(String iDType) {
        this.iDType = iDType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getOccupationType() {
        return occupationType;
    }

    public void setOccupationType(String occupationType) {
        this.occupationType = occupationType;
    }

    public String getOccupationCode() {
        return occupationCode;
    }

    public void setOccupationCode(String occupationCode) {
        this.occupationCode = occupationCode;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMedicareStatus() {
        return medicareStatus;
    }

    public void setMedicareStatus(String medicareStatus) {
        this.medicareStatus = medicareStatus;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getNewName() {
        return newName;
    }

    public void setNewName(String newName) {
        this.newName = newName;
    }

    public String getNewNativePlace() {
        return newNativePlace;
    }

    public void setNewNativePlace(String newNativePlace) {
        this.newNativePlace = newNativePlace;
    }
}
