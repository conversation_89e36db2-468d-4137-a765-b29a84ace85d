package com.sinosoft.eflex.model.edor;

import com.sinosoft.eflex.model.FCEdorReduInsured;

import java.util.List;

public class EdoraApplyConfirmInfo {
	private InsuredChgTrialIO insuredChgTrialIO;//被保人信息变更
	private PolicyHolderChgTrialIO policyHolderChgTrialIO;//投保人信息变更
	private BnfChgTrialIO bnfChgTrialIO;//受益人资料变更保全申请接口
	private String grpContNo;
	/**
	 * 增人标记
	 */
	private boolean NIFlag;

	/**
	 * 减人标记
	 */
	private boolean ZTFlag;
	
	/**
	 * 同质风险加减人标记
	 */
	private boolean HNIFlag;
	

	/**
	 * 增人批次
	 */
	private String addBatch;

	/**
	 * 减人批次
	 */
	private String decBatch;
	
	/**
	 * 同质风险加减人批次
	 */
	private String hrarBatch;
	
	/**
	 * 增加被保险人保全受理号
	 */
	private String edorAppNI;
	/**
	 * 减少被保险人保全受理号
	 */
	private String edorAppZT;
	/**
	 * 同质风险加减人保全受理号
	 */
	private String edorAppHNI;

	public boolean isHNIFlag() {
		return HNIFlag;
	}

	public void setHNIFlag(boolean hNIFlag) {
		HNIFlag = hNIFlag;
	}

	public String getHrarBatch() {
		return hrarBatch;
	}

	public void setHrarBatch(String hrarBatch) {
		this.hrarBatch = hrarBatch;
	}

	public String getEdorAppHNI() {
		return edorAppHNI;
	}

	public void setEdorAppHNI(String edorAppHNI) {
		this.edorAppHNI = edorAppHNI;
	}

	public String getAddBatch() {
		return addBatch;
	}

	public void setAddBatch(String addBatch) {
		this.addBatch = addBatch;
	}

	private List<FCEdorReduInsured> fcEdorReduInsuredList;


	public String getDecBatch() {
		return decBatch;
	}

	public void setDecBatch(String decBatch) {
		this.decBatch = decBatch;
	}

	public List<FCEdorReduInsured> getFcEdorReduInsuredList() {
		return fcEdorReduInsuredList;
	}

	public void setFcEdorReduInsuredList(List<FCEdorReduInsured> fcEdorReduInsuredList) {
		this.fcEdorReduInsuredList = fcEdorReduInsuredList;
	}


	public String getGrpContNo() {
		return grpContNo;
	}

	public void setGrpContNo(String grpContNo) {
		this.grpContNo = grpContNo;
	}


	public InsuredChgTrialIO getInsuredChgTrialIO() {
		return insuredChgTrialIO;
	}

	public void setInsuredChgTrialIO(InsuredChgTrialIO insuredChgTrialIO) {
		this.insuredChgTrialIO = insuredChgTrialIO;
	}

	public PolicyHolderChgTrialIO getPolicyHolderChgTrialIO() {
		return policyHolderChgTrialIO;
	}

	public void setPolicyHolderChgTrialIO(PolicyHolderChgTrialIO policyHolderChgTrialIO) {
		this.policyHolderChgTrialIO = policyHolderChgTrialIO;
	}

	public BnfChgTrialIO getBnfChgTrialIO() {
		return bnfChgTrialIO;
	}

	public void setBnfChgTrialIO(BnfChgTrialIO bnfChgTrialIO) {
		this.bnfChgTrialIO = bnfChgTrialIO;
	}

	public boolean isNIFlag() {
		return NIFlag;
	}

	public void setNIFlag(boolean NIFlag) {
		this.NIFlag = NIFlag;
	}

	public boolean isZTFlag() {
		return ZTFlag;
	}

	public void setZTFlag(boolean ZTFlag) {
		this.ZTFlag = ZTFlag;
	}

	public String getEdorAppNI() {
		return edorAppNI;
	}

	public void setEdorAppNI(String edorAppNI) {
		this.edorAppNI = edorAppNI;
	}

	public String getEdorAppZT() {
		return edorAppZT;
	}

	public void setEdorAppZT(String edorAppZT) {
		this.edorAppZT = edorAppZT;
	}
}
