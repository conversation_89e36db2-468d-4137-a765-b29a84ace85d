package com.sinosoft.eflex.model.edor;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2020/11/20
 */
@Data
public class SelectEdorHomogeneousRiskInsuredReq {


    /**
     * 同质风险加减人批次
     */
    private String hrarBatch;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 条数
     */
    private Integer pageSize;

    /**
     * 企业编号
     */
    private String grpNo;

    /**
     * 原被保人姓名
     */
    private String oldName;

    /**
     * 原被保人证件类型
     */
    private String oldIdType;

    /**
     * 原被保人证件号
     */
    private String oldIdNo;

    /**
     * 新被保人姓名
     */
    private String newName;

    /**
     * 新被保人证件类型
     */
    private String newIdType;

    /**
     * 新被保人证件号
     */
    private String newIdNo;

}
