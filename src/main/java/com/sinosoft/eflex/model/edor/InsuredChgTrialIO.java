package com.sinosoft.eflex.model.edor;

import java.util.List;

/**
 * @DESCRIPTION
 * @create 2018-12-05 15:51
 **/
public class InsuredChgTrialIO {

	private boolean edorFlag;

	private String edorAppNo;

	private List<Insurers> insurersList;

	public boolean isEdorFlag() { return edorFlag; }

	public void setEdorFlag(boolean edorFlag) { this.edorFlag = edorFlag; }

	public String getEdorAppNo() {
		return edorAppNo;
	}

	public void setEdorAppNo(String edorAppNo) {
		this.edorAppNo = edorAppNo;
	}

	public List<Insurers> getInsurersList() {
		return insurersList;
	}

	public void setInsurersList(List<Insurers> insurersList) {
		this.insurersList = insurersList;
	}
}
