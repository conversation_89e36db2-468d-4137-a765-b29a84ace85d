package com.sinosoft.eflex.model.edor;

/**
 * <AUTHOR>
 * @date 2019-01-03 11:18
 */
public class BnfInsured {


    private String bnfName;

    private String bnfIdType;

    private String bnfIdNo;

    private String bnfNativePlace;

    private String relationToInsured;

    private String bnfGrade;

    private String bnfLot;

    private String bnfSex;

    private String idTimeEnd;

    private String birthDay;

    public String getIdTimeEnd() {
        return idTimeEnd;
    }

    public void setIdTimeEnd(String idTimeEnd) {
        this.idTimeEnd = idTimeEnd;
    }

    public String getBnfName() {
        return bnfName;
    }

    public void setBnfName(String bnfName) {
        this.bnfName = bnfName;
    }

    public String getBnfIdType() {
        return bnfIdType;
    }

    public void setBnfIdType(String bnfIdType) {
        this.bnfIdType = bnfIdType;
    }

    public String getBnfIdNo() {
        return bnfIdNo;
    }

    public void setBnfIdNo(String bnfIdNo) {
        this.bnfIdNo = bnfIdNo;
    }

    public String getRelationToInsured() {
        return relationToInsured;
    }

    public void setRelationToInsured(String relationToInsured) {
        this.relationToInsured = relationToInsured;
    }

    public String getBnfGrade() {
        return bnfGrade;
    }

    public void setBnfGrade(String bnfGrade) {
        this.bnfGrade = bnfGrade;
    }

    public String getBnfLot() {
        return bnfLot;
    }

    public void setBnfLot(String bnfLot) {
        this.bnfLot = bnfLot;
    }

    public String getBnfSex() {
        return bnfSex;
    }

    public void setBnfSex(String bnfSex) {
        this.bnfSex = bnfSex;
    }

    public String getBirthDay() {
        return birthDay;
    }

    public void setBirthDay(String birthDay) {
        this.birthDay = birthDay;
    }

    public String getBnfNativePlace() {
        return bnfNativePlace;
    }

    public void setBnfNativePlace(String bnfNativePlace) {
        this.bnfNativePlace = bnfNativePlace;
    }
}
