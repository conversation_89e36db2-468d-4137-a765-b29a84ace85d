package com.sinosoft.eflex.model.edor;

/**
 * @DESCRIPTION
 * @create 2018-12-05 15:51
 **/
public class PolicyHolderChgTrialIO {

	private boolean edorFlag;

	private String edorAppNo;

	private PolicyHolderInfo policyHolderInfo;

	public boolean isEdorFlag() { return edorFlag; }

	public void setEdorFlag(boolean edorFlag) { this.edorFlag = edorFlag; }

	public String getEdorAppNo() {
		return edorAppNo;
	}

	public void setEdorAppNo(String edorAppNo) {
		this.edorAppNo = edorAppNo;
	}

	public PolicyHolderInfo getPolicyHolderInfo() {
		return policyHolderInfo;
	}

	public void setPolicyHolderInfo(PolicyHolderInfo policyHolderInfo) {
		this.policyHolderInfo = policyHolderInfo;
	}
}
