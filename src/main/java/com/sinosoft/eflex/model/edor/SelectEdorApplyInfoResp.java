package com.sinosoft.eflex.model.edor;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2021/9/8
 * @desc 查询保全申请状态
 */
@Data
public class SelectEdorApplyInfoResp {

    /**
     * 业务类型，参考 AsyncBusinessTypeEnum
     */
    private String businessType;

    /**
     * 状态00：未进行 01：处理中 02：处理完成，成功 03：处理完成，失败
     */
    private String edorApplyState;

    /**
     * 处理结论
     */
    private String edorApplyMsg;

}
