package com.sinosoft.eflex.model;

import java.util.Date;

public class FdPerImpart {
    /**
     * 
     */
    private String impartVer;

    /**
     * 
     */
    private String impartCode;

    /**
     * 
     */
    private String impartContent;

    /**
     * 状态 0 停用 1 正常
     */
    private String state;

    /**
     * 
     */
    private String impartParamModle;

    /**
     * 
     */
    private String uwClaimFlag;

    /**
     * 
     */
    private String haveParamFlag;

    /**
     * 
     */
    private String prtFlag;

    /**
     * 
     */
    private String remark;

    /**
     * 题目类型 1 需要录入 2 标题
     */
    private String impartType;

    /**
     * 
     */
    private String upLevel;

    /**
     * 
     */
    private Date makeDate;

    /**
     * 
     */
    private String makeTime;

    /**
     * 
     */
    private Date modifyDate;

    /**
     * 
     */
    private String modifyTime;

    /**
     * 
     * @return ImpartVer
     */
    public String getImpartVer() {
        return impartVer;
    }

    /**
     * 
     * @param impartVer
     */
    public void setImpartVer(String impartVer) {
        this.impartVer = impartVer;
    }

    /**
     * 
     * @return ImpartCode
     */
    public String getImpartCode() {
        return impartCode;
    }

    /**
     * 
     * @param impartCode
     */
    public void setImpartCode(String impartCode) {
        this.impartCode = impartCode;
    }

    /**
     * 
     * @return ImpartContent
     */
    public String getImpartContent() {
        return impartContent;
    }

    /**
     * 
     * @param impartContent
     */
    public void setImpartContent(String impartContent) {
        this.impartContent = impartContent;
    }

    /**
     * 状态 0 停用 1 正常
     * 
     * @return State 状态 0 停用 1 正常
     */
    public String getState() {
        return state;
    }

    /**
     * 状态 0 停用 1 正常
     * 
     * @param state
     *            状态 0 停用 1 正常
     */
    public void setState(String state) {
        this.state = state;
    }

    /**
     * 
     * @return ImpartParamModle
     */
    public String getImpartParamModle() {
        return impartParamModle;
    }

    /**
     * 
     * @param impartParamModle
     */
    public void setImpartParamModle(String impartParamModle) {
        this.impartParamModle = impartParamModle;
    }

    /**
     * 
     * @return UwClaimFlag
     */
    public String getUwClaimFlag() {
        return uwClaimFlag;
    }

    /**
     * 
     * @param uwClaimFlag
     */
    public void setUwClaimFlag(String uwClaimFlag) {
        this.uwClaimFlag = uwClaimFlag;
    }

    /**
     * 
     * @return HaveParamFlag
     */
    public String getHaveParamFlag() {
        return haveParamFlag;
    }

    /**
     * 
     * @param haveParamFlag
     */
    public void setHaveParamFlag(String haveParamFlag) {
        this.haveParamFlag = haveParamFlag;
    }

    /**
     * 
     * @return PrtFlag
     */
    public String getPrtFlag() {
        return prtFlag;
    }

    /**
     * 
     * @param prtFlag
     */
    public void setPrtFlag(String prtFlag) {
        this.prtFlag = prtFlag;
    }

    /**
     * 
     * @return Remark
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 
     * @param remark
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 题目类型 1 需要录入 2 标题
     * 
     * @return ImpartType 题目类型 1 需要录入 2 标题
     */
    public String getImpartType() {
        return impartType;
    }

    /**
     * 题目类型 1 需要录入 2 标题
     * 
     * @param impartType
     *            题目类型 1 需要录入 2 标题
     */
    public void setImpartType(String impartType) {
        this.impartType = impartType;
    }

    /**
     * 
     * @return UpLevel
     */
    public String getUpLevel() {
        return upLevel;
    }

    /**
     * 
     * @param upLevel
     */
    public void setUpLevel(String upLevel) {
        this.upLevel = upLevel;
    }

    /**
     * 
     * @return MakeDate
     */
    public Date getMakeDate() {
        return makeDate;
    }

    /**
     * 
     * @param makeDate
     */
    public void setMakeDate(Date makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 
     * @return MakeTime
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 
     * @param makeTime
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 
     * @return ModifyDate
     */
    public Date getModifyDate() {
        return modifyDate;
    }

    /**
     * 
     * @param modifyDate
     */
    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 
     * @return ModifyTime
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 
     * @param modifyTime
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}