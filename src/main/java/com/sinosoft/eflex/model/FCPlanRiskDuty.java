package com.sinosoft.eflex.model;


import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class FCPlanRiskDuty extends FCPlanRiskDutyKey {

    /**
     * FDRiskDutyInfo
     */
    private List<FDRiskDutyInfo> fdRiskDutyInfo;
    /**
     * 计划名称
     */
    private String planName;

    /**
     * 险种名称
     */
    private String riskName;

    /**
     * 责任名称
     */
    private String dutyName;

    /**
     * 保额
     */
    private Double amnt;

    /**
     * 保费
     */
    private Double prem;

    /**
     * 免赔额
     */
    private Double getLimit;

    /**
     * 免赔额属性 1-按次免赔 2-按年免赔
     */
    private String getLimitType;

    /**
     * 赔付比例
     */
    private Double getRatio;

    /**
     * 最大赔付天数
     */
    private BigDecimal maxGetDay;

    /**
     * 免赔额数组
     */
    private List<String> getLimitList;

    /**
     * 赔付比例集合
     */
    private List<String> getRateList;
    /**
     * 保额集合
     */
    private List<String> amntList;
    /**
     * 免赔额属性集合 1-按次免赔 2-按年免赔
     */
    private List<String> getLimitTypeList;
    /**
     * 计划对象
     */
    private String planObject;

    /**
     * 计划重点
     */
    private String planKey;

    /**
     * 判断新增修改标识字段 0--新增  1--修改
     */
    private String operating;

    /**
     * 标识字段（不存入数据库）
     */
    private String logo;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 生成日期
     */
    private String makeDate;

    /**
     * 生成时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;
}