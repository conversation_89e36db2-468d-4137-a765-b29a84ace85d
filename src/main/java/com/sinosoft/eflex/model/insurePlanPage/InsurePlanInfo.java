package com.sinosoft.eflex.model.insurePlanPage;

import com.sinosoft.eflex.model.PlanConfigExplain;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/3/29
 */
@Data
public class InsurePlanInfo {

    // 是否已经投保 1-投过 0-未投
    private String isPlan;
    /**
     * 福利编码
     */
    private String ensureCode;

    /**
     * 计划编码
     */
    private String planCode;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 计划重点
     */
    private String planKey;

    /**
     * 计划对象
     */
    private String planObject;

    /**
     * 计划保费
     */
    private Double totalPrem;

    /**
     * 计划保障说明
     */
    private PlanConfigExplain planConfigExplain;

    /**
     * 险种责任信息
     */
    private List<RiskDutyInfo> riskDutyInfoList;

}
