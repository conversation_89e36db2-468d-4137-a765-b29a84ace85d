package com.sinosoft.eflex.model.insurePlanPage;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2021/3/26
 */
@Data
public class PersonInfo {

    /**
     * 个人编码
     */
    private String personID;
    /**
     * perinfo表对应证件号
     */
    private String perIDNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private String sex;

    /**
     * 国籍
     */
    private String nativeplace;

    private String nativeplaceName;
    /**
     * 出生日期
     */
    private String birthDate;

    /**
     * 证件类型
     */
    private String idType;
    private String idTypeName;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 证件有效期
     */
    private String idTypeEndDate;

    /**
     * 移动电话
     */
    private String mobilePhone;

    /**
     * 固定电话
     */
    private String phone;

    /**
     * 职业类别
     */
    private String occupationType;
    private String occupationTypeName;

    /**
     * 职业代码
     */
    private String occupationCode;
    private String occupationName;
    /**
     * 是否参加医保: 0-否 1-是
     */
    private String joinMedProtect;

    /**
     * 医保类型
     */
    private String medProtectType;

    /**
     * 邮箱
     */

    @JsonProperty("email")
    private String EMail;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 开户银行
     */
    private String openBank;

    /**
     * 开户账户
     */
    private String openAccount;

    /**
     *
     */
    private String operator;

    /**
     *
     */
    private String operatorCom;

    /**
     *
     */
    private String makeDate;

    /**
     *
     */
    private String makeTime;

    /**
     *
     */
    private String modifyDate;

    /**
     *
     */
    private String modifyTime;

    /**
     * 与员工关系
     */
    private String relation;
    private String relationName;

    /**
     * 关系证明
     */
    private String relationProve;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 县
     */
    private String county;

    /**
     * 详细地址
     */
    private String detaileAddress;

    /**
     * 投保人年收入(万元）
     */
    private String yearSalary;

    /**
     * 职级
     */
    private String levelCode;
    /**
     * 是否退休
     */
    private String retirement;
    /**
     * 是否为学生投保
     */
    private String isHasStuRule;
    /**
     * 是否投过保
     */
    private String isPlan;
    /**
     * 员工福利额度
     */
    private Double staffGrpPrem;
    /**
     * 家属福利额度
     */
    private Double familyGrpPrem;

}
