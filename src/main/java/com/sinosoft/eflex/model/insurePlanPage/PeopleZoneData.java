package com.sinosoft.eflex.model.insurePlanPage;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/3/26
 */
@Data
public class PeopleZoneData {

    // 人员信息
    private PersonInfo personInfo;

    // 允许投保计划列表
    private List<InsurePlanInfo> planInfoList;

    private List<InsurePlanInfo> planInfoDataList;
    // 是否已经投保 1-投过 0-未投
    private String isPlan;

    // 默认计划编码
    private String defaultPlanCode;
}
