package com.sinosoft.eflex.model.EnsureMake.convert;

import com.sinosoft.eflex.model.EnsureMake.EnsureMake;
import com.sinosoft.eflex.model.EnsureMake.EnsureRequest;

/**
 * <AUTHOR>
 **/
public class EnsureMakeConvert {


    public static EnsureMake convert(EnsureRequest ensureRequest, String grpNo) {
        EnsureMake ensureMake = new EnsureMake();
        ensureMake.setEnsureName(ensureRequest.getEnsureName());
        ensureMake.setGrpNo(grpNo);
        ensureMake.setEnsureCode(ensureRequest.getEnsureCode());
        ensureMake.setEnsureState(ensureRequest.getEnsureState());
        ensureMake.setPage(ensureRequest.getPage());
        ensureMake.setRows(ensureRequest.getRows());
        return ensureMake;
    }
}
