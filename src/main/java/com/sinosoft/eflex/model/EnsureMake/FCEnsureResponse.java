package com.sinosoft.eflex.model.EnsureMake;

import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 企业弹性福利保障表实体类
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FCEnsureResponse implements Serializable {


    /**
     * 福利编号
     */
    private String ensureCode;


    /**
     * 福利名称
     */
    private String ensureName;


    /**
     * 团险计划内员工总数
     */
    private Integer ensureCount;


    /**
     * 总投保人数含家属
     */
    private Integer staFamilyCount;


    /**
     * 已投保员工数
     */
    private Integer planStaCount;


    /**
     * 为家人投保员工数
     */
    private Integer familyCount;


    /**
     * 未投保员工数
     */
    private Integer staCount;


    /**
     * 投保开放日期
     */
    private String startAppntDate;

    /**
     * 投保结束日期
     */
    private String endAppntDate;

}