package com.sinosoft.eflex.model.EnsureMake.convert;

import com.sinosoft.eflex.model.EnsureMake.FCEnsureResponse;
import com.sinosoft.eflex.model.FCEnsure;

/**
 * <AUTHOR>
 **/
public class FcEnsureResponseConvert {


    public static FCEnsureResponse convert(FCEnsure fcEnsure, Integer staCount, Integer staFamilyCount, Integer familyCount, Integer planStaCount, Integer ensureCount) {
        FCEnsureResponse fcEnsureResponse = new FCEnsureResponse();
        fcEnsureResponse.setEnsureCode(fcEnsure.getEnsureCode());
        fcEnsureResponse.setEnsureName(fcEnsure.getEnsureName());
        fcEnsureResponse.setStaCount(staCount);
        fcEnsureResponse.setStaFamilyCount(staFamilyCount);
        fcEnsureResponse.setEnsureCount(ensureCount);
        fcEnsureResponse.setPlanStaCount(planStaCount);
        fcEnsureResponse.setFamilyCount(familyCount);
        fcEnsureResponse.setStartAppntDate(fcEnsure.getStartAppntDate());
        fcEnsureResponse.setEndAppntDate(fcEnsure.getEndAppntDate());
        return fcEnsureResponse;

    }
}
