package com.sinosoft.eflex.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
public class FCOrderItemDetail extends FCOrderItemDetailKey implements Serializable {
    /**
     * 产品要素值
     */
    private String value;

    /**
     * 产品责任编码
     */
    private String dutyCode;

    /**
     * 产品责任组合编码
     */
    private String dutyGroupCode;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 生成日期
     */
    private String makeDate;

    /**
     * 生成时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 保障计划表
     */
    private FCEnsurePlan fcEnsurePlan;

    /**
     * 保额
     */
    @JsonProperty("insuredAmount")
    private double InsuredAmount;

    /**
     * 保险期间  1-保至85周岁  2-终身
     */
    @JsonProperty("insurePeriod")
    private String InsurePeriod;

    /**
     * 交费频次   1-趸交  2-年交
     */
    @JsonProperty("payFrequency")
    private String PayFrequency;

    /**
     * 缴费期间  1-一次性交清  2-5年交   3-10年交  4-20年交
     */
    @JsonProperty("payPeriod")
    private String PayPeriod;


    /**
     * 产品要素值
     *
     * @return Value 产品要素值
     */
    public String getValue() {
        return value;
    }

    /**
     * 产品要素值
     *
     * @param value 产品要素值
     */
    public void setValue(String value) {
        this.value = value;
    }

    /**
     * 产品责任编码
     *
     * @return DutyCode 产品责任编码
     */
    public String getDutyCode() {
        return dutyCode;
    }

    /**
     * 产品责任编码
     *
     * @param dutyCode 产品责任编码
     */
    public void setDutyCode(String dutyCode) {
        this.dutyCode = dutyCode;
    }

    /**
     * 产品责任组合编码
     *
     * @return DutyGroupCode 产品责任组合编码
     */
    public String getDutyGroupCode() {
        return dutyGroupCode;
    }

    /**
     * 产品责任组合编码
     *
     * @param dutyGroupCode 产品责任组合编码
     */
    public void setDutyGroupCode(String dutyGroupCode) {
        this.dutyGroupCode = dutyGroupCode;
    }

    /**
     * 操作员
     *
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     *
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 操作机构
     *
     * @return OperatorCom 操作机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 生成日期
     *
     * @return MakeDate 生成日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 操作机构
     *
     * @param operatorCom 操作机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 生成日期
     *
     * @param makeDate 生成日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 生成时间
     *
     * @return MakeTime 生成时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 修改日期
     *
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 生成时间
     *
     * @param makeTime 生成时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     *
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     *
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     *
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    /**
     * @return the fcEnsurePlan
     */
    public FCEnsurePlan getFcEnsurePlan() {
        return fcEnsurePlan;
    }

    /**
     * @param fcEnsurePlan the fcEnsurePlan to set
     */
    public void setFcEnsurePlan(FCEnsurePlan fcEnsurePlan) {
        this.fcEnsurePlan = fcEnsurePlan;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#toString()
     */
    @Override
    public String toString() {
        return "FCOrderItemDetail [value=" + value + ", dutyCode=" + dutyCode + ", dutyGroupCode=" + dutyGroupCode
                + ", operator=" + operator + ", operatorCom=" + operatorCom + ", makeDate=" + makeDate + ", makeTime="
                + makeTime + ", modifyDate=" + modifyDate + ", modifyTime=" + modifyTime + ", fcEnsurePlan="
                + fcEnsurePlan + "]";
    }

	public double getInsuredAmount() {
		return InsuredAmount;
	}

	public void setInsuredAmount(double insuredAmount) {
		InsuredAmount = insuredAmount;
	}

	public String getInsurePeriod() {
		return InsurePeriod;
	}

	public void setInsurePeriod(String insurePeriod) {
		InsurePeriod = insurePeriod;
	}

	public String getPayFrequency() {
		return PayFrequency;
	}

	public void setPayFrequency(String payFrequency) {
		PayFrequency = payFrequency;
	}

	public String getPayPeriod() {
		return PayPeriod;
	}

	public void setPayPeriod(String payPeriod) {
		PayPeriod = payPeriod;
	}
	@JsonProperty("ensureCode")
	private String ensureCode;

	public String getEnsureCode() {
		return ensureCode;
	}

	public void setEnsureCode(String ensureCode) {
		this.ensureCode = ensureCode;
	}

}