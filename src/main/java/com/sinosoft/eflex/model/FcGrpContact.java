package com.sinosoft.eflex.model;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class FcGrpContact {
    /**
     * 联系人编号
     */
    private String contactNo;

    /**
     * 联系人类型 01-第一联系人 02-第二联系人
     */
    private String contactType;

    /**
     * 企业客户号
     */
    private String grpNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private String sex;

    /**
     * 国籍
     */
    private String nativeplace;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 证件号码
     */
    private String idNo;

    /**
     * 证件有效起期
     */
    private String idTypeStartDate;

    /**
     * 证件有效止期
     */
    private String idTypeEndDate;

    /**
     * 手机号码
     */
    private String mobilePhone;

    /**
     * 出生日期
     */
    private String birthDay;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 证件图片1
     */
    private String idImage1;

    /**
     * 证件图片2
     */
    private String idImage2;

    private String idCardFront;

    /**
     * 证件图片2
     */
    private String idCardBack;


    /**
     * 所属部门
     */
    private String department;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 生成日期
     */
    private String makeDate;

    /**
     * 生成时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 联系人编号
     *
     * @return ContactNo 联系人编号
     */
    public String getContactNo() {
        return contactNo;
    }

    /**
     * 联系人编号
     *
     * @param contactNo 联系人编号
     */
    public void setContactNo(String contactNo) {
        this.contactNo = contactNo;
    }

    /**
     * 联系人类型 01-第一联系人 02-第二联系人
     *
     * @return ContactType 联系人类型 01-第一联系人 02-第二联系人
     */
    public String getContactType() {
        return contactType;
    }

    /**
     * 联系人类型 01-第一联系人 02-第二联系人
     *
     * @param contactType 联系人类型 01-第一联系人 02-第二联系人
     */
    public void setContactType(String contactType) {
        this.contactType = contactType;
    }

    /**
     * 企业客户号
     *
     * @return grpNo 企业客户号
     */
    public String getGrpNo() {
        return grpNo;
    }

    /**
     * 企业客户号
     *
     * @param grpNo 企业客户号
     */
    public void setGrpNo(String grpNo) {
        this.grpNo = grpNo;
    }

    /**
     * 姓名
     *
     * @return name 姓名
     */
    public String getName() {
        return name;
    }

    /**
     * 姓名
     *
     * @param name 姓名
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 性别
     *
     * @return sex 性别
     */
    public String getSex() {
        return sex;
    }

    /**
     * 性别
     *
     * @param sex 性别
     */
    public void setSex(String sex) {
        this.sex = sex;
    }

    /**
     * 国籍
     *
     * @return nativeplace 国籍
     */
    public String getNativeplace() {
        return nativeplace;
    }

    /**
     * 国籍
     *
     * @param nativeplace 国籍
     */
    public void setNativeplace(String nativeplace) {
        this.nativeplace = nativeplace;
    }

    /**
     * 证件类型
     *
     * @return idType 证件类型
     */
    public String getIdType() {
        return idType;
    }

    /**
     * 证件类型
     *
     * @param idType 证件类型
     */
    public void setIdType(String idType) {
        this.idType = idType;
    }

    /**
     * 证件号码
     *
     * @return idNo 证件号码
     */
    public String getIdNo() {
        return idNo;
    }

    /**
     * 证件号码
     *
     * @param idNo 证件号码
     */
    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    /**
     * 证件有效起期
     *
     * @return idTypeStartDate 证件有效起期
     */
    public String getIdTypeStartDate() {
        return idTypeStartDate;
    }

    /**
     * 证件有效起期
     *
     * @param idTypeStartDate 证件有效起期
     */
    public void setIdTypeStartDate(String idTypeStartDate) {
        this.idTypeStartDate = idTypeStartDate;
    }

    /**
     * 证件有效止期
     *
     * @return idTypeEndDate 证件有效止期
     */
    public String getIdTypeEndDate() {
        return idTypeEndDate;
    }

    /**
     * 证件有效止期
     *
     * @param idTypeEndDate 证件有效止期
     */
    public void setIdTypeEndDate(String idTypeEndDate) {
        this.idTypeEndDate = idTypeEndDate;
    }

    /**
     * 手机号码
     *
     * @return mobilePhone 手机号码
     */
    public String getMobilePhone() {
        return mobilePhone;
    }

    /**
     * 手机号码
     *
     * @param mobilePhone 手机号码
     */
    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    /**
     * 出生日期
     *
     * @return birthDay 出生日期
     */
    public String getBirthDay() {
        return birthDay;
    }

    /**
     * 出生日期
     *
     * @param birthDay 出生日期
     */
    public void setBirthDay(String birthDay) {
        this.birthDay = birthDay;
    }

    /**
     * 邮箱
     *
     * @return email 邮箱
     */
    public String getEmail() {
        return email;
    }

    /**
     * 邮箱
     *
     * @param email 邮箱
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * 证件图片1
     *
     * @return idImage1 证件图片1
     */
    public String getIdImage1() {
        return idImage1;
    }

    /**
     * 证件图片1
     *
     * @param idImage1 证件图片1
     */
    public void setIdImage1(String idImage1) {
        this.idImage1 = idImage1;
    }

    /**
     * 证件图片2
     *
     * @return idImage2 证件图片2
     */
    public String getIdImage2() {
        return idImage2;
    }

    /**
     * 证件图片2
     *
     * @param idImage2 证件图片2
     */
    public void setIdImage2(String idImage2) {
        this.idImage2 = idImage2;
    }

    /**
     * 所属部门
     *
     * @return department 所属部门
     */
    public String getDepartment() {
        return department;
    }

    /**
     * 所属部门
     *
     * @param department 所属部门
     */
    public void setDepartment(String department) {
        this.department = department;
    }

    /**
     * 操作员
     *
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     *
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 操作机构
     *
     * @return OperatorCom 操作机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作机构
     *
     * @param operatorCom 操作机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 生成日期
     *
     * @return MakeDate 生成日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 生成日期
     *
     * @param makeDate 生成日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 生成时间
     *
     * @return MakeTime 生成时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 生成时间
     *
     * @param makeTime 生成时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     *
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     *
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     *
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     *
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public FcGrpContact(String idType, String idNo) {
        this.idType = idType;
        this.idNo = idNo;
    }
}