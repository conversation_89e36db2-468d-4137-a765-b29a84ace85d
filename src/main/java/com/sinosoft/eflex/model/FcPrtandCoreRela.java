package com.sinosoft.eflex.model;

public class FcPrtandCoreRela {
    /**
     * 
     */
    private String relaSn;

    /**
     * 
     */
    private String prtNo;

    /**
     * 
     */
    private String tPrtNo;

    /**
     * 
     */
    private String status;

    /**
     * 
     */
    private String describe;

    /**
     * 
     */
    private String makeDate;

    /**
     * 
     */
    private String makeTime;

    /**
     * 
     * @return RelaSn
     */
    public String getRelaSn() {
        return relaSn;
    }

    /**
     * 
     * @param relaSn
     */
    public void setRelaSn(String relaSn) {
        this.relaSn = relaSn;
    }

    /**
     * 
     * @return PrtNo
     */
    public String getPrtNo() {
        return prtNo;
    }

    /**
     * 
     * @param prtNo
     */
    public void setPrtNo(String prtNo) {
        this.prtNo = prtNo;
    }

    /**
     * 
     * @return tPrtNo
     */
    public String gettPrtNo() {
        return tPrtNo;
    }

    /**
     * 
     * @param tPrtNo
     */
    public void settPrtNo(String tPrtNo) {
        this.tPrtNo = tPrtNo;
    }

    /**
     * 
     * @return Status
     */
    public String getStatus() {
        return status;
    }

    /**
     * 
     * @param status
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 
     * @return Describe
     */
    public String getDescribe() {
        return describe;
    }

    /**
     * 
     * @param describe
     */
    public void setDescribe(String describe) {
        this.describe = describe;
    }

    /**
     * 
     * @return MakeDate
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 
     * @param makeDate
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 
     * @return MakeTime
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 
     * @param makeTime
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }
}