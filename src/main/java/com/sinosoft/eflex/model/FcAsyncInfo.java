package com.sinosoft.eflex.model;

public class FcAsyncInfo {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 业务编号
     */
    private String businessId;

    /**
     * 处理状态 0 处理完成 1 处理中
     */
    private String dealState;

    /**
     * 处理结果 0 成功 1 失败
     */
    private String resultCode;

    /**
     * 异常描述
     */
    private String resultMessage;

    /**
     * 处理时长毫秒
     */
    private String dealTime;

    /**
     * 预估处理时长
     */
    private String estimateDealTime;

    /**
     * 预估处理时间点
     */
    private String estimateCompleteTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 存当前登录用户ID
     */
    private String operator;

    /**
     * 存当前操作用户的管理机构
     */
    private String operatorCom;

    /**
     * 创建日期
     */
    private String makeDate;

    /**
     * 创建时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 主键
     * @return Id 主键
     */
    public Integer getId() {
        return id;
    }

    /**
     * 主键
     * 
     * @param id
     *            主键
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 业务类型
     * @return BusinessType 业务类型
     */
    public String getBusinessType() {
        return businessType;
    }

    /**
     * 业务类型
     * 
     * @param businessType
     *            业务类型
     */
    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    /**
     * 业务编号
     * @return BusinessId 业务编号
     */
    public String getBusinessId() {
        return businessId;
    }

    /**
     * 业务编号
     * 
     * @param businessId
     *            业务编号
     */
    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    /**
     * 处理状态 0 处理完成 1 处理中
     * 
     * @return DealState 处理状态 0 处理完成 1 处理中
     */
    public String getDealState() {
        return dealState;
    }

    /**
     * 处理状态 0 处理完成 1 处理中
     * 
     * @param dealState
     *            处理状态 0 处理完成 1 处理中
     */
    public void setDealState(String dealState) {
        this.dealState = dealState;
    }

    /**
     * 处理结果 0 成功 1 失败
     * 
     * @return ResultCode 处理结果 0 成功 1 失败
     */
    public String getResultCode() {
        return resultCode;
    }

    /**
     * 处理结果 0 成功 1 失败
     * 
     * @param resultCode
     *            处理结果 0 成功 1 失败
     */
    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    /**
     * 异常描述
     * @return ResultMessage 异常描述
     */
    public String getResultMessage() {
        return resultMessage;
    }

    /**
     * 异常描述
     * 
     * @param resultMessage
     *            异常描述
     */
    public void setResultMessage(String resultMessage) {
        this.resultMessage = resultMessage;
    }

    /**
     * 处理时长毫秒
     * @return DealTime 处理时长毫秒
     */
    public String getDealTime() {
        return dealTime;
    }

    /**
     * 处理时长毫秒
     * 
     * @param dealTime
     *            处理时长毫秒
     */
    public void setDealTime(String dealTime) {
        this.dealTime = dealTime;
    }

    /**
     * 预估处理时长
     * @return EstimateDealTime 预估处理时长
     */
    public String getEstimateDealTime() {
        return estimateDealTime;
    }

    /**
     * 预估处理时长
     * 
     * @param estimateDealTime
     *            预估处理时长
     */
    public void setEstimateDealTime(String estimateDealTime) {
        this.estimateDealTime = estimateDealTime;
    }

    /**
     * 预估处理时间点
     * 
     * @return EstimateCompleteTime 预估处理时间点
     */
    public String getEstimateCompleteTime() {
        return estimateCompleteTime;
    }

    /**
     * 预估处理时间点
     * 
     * @param estimateCompleteTime
     *            预估处理时间点
     */
    public void setEstimateCompleteTime(String estimateCompleteTime) {
        this.estimateCompleteTime = estimateCompleteTime;
    }

    /**
     * 备注
     * @return Remark 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 备注
     * 
     * @param remark
     *            备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 存当前登录用户ID
     * @return Operator 存当前登录用户ID
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 存当前登录用户ID
     * 
     * @param operator
     *            存当前登录用户ID
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 存当前操作用户的管理机构
     * @return OperatorCom 存当前操作用户的管理机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 存当前操作用户的管理机构
     * 
     * @param operatorCom
     *            存当前操作用户的管理机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 创建日期
     * @return MakeDate 创建日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 创建日期
     * 
     * @param makeDate
     *            创建日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 创建时间
     * @return MakeTime 创建时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 创建时间
     * 
     * @param makeTime
     *            创建时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * 
     * @param modifyDate
     *            修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * 
     * @param modifyTime
     *            修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}