package com.sinosoft.eflex.model;

/**
 * <AUTHOR>
 * @date 2018-12-26 19:22
 */
public class ClaimInfo {

    private String ClaimNo;
    private String CertiType;
    private String CertiCode;
    private String InsuredName;
    private String ClaimType;
    private String ClaimAmount;
    private String RealPay;
    private String IsCheck;
    private String Status;
    private String Remark;

    public String getClaimNo() {
        return this.ClaimNo;
    }

    public void setClaimNo(String ClaimNo) {
        this.ClaimNo = ClaimNo;
    }

    public String getCertiType() {
        return CertiType;
    }

    public void setCertiType(String certiType) {
        CertiType = certiType;
    }

    public String getCertiCode() {
        return CertiCode;
    }

    public void setCertiCode(String certiCode) {
        CertiCode = certiCode;
    }

    public String getInsuredName() {
        return InsuredName;
    }

    public void setInsuredName(String insuredName) {
        InsuredName = insuredName;
    }

    public String getClaimType() {
        return ClaimType;
    }

    public void setClaimType(String claimType) {
        ClaimType = claimType;
    }

    public String getClaimAmount() {
        return ClaimAmount;
    }

    public void setClaimAmount(String claimAmount) {
        ClaimAmount = claimAmount;
    }

    public String getRealPay() {
        return RealPay;
    }

    public void setRealPay(String realPay) {
        RealPay = realPay;
    }

    public String getIsCheck() {
        return IsCheck;
    }

    public void setIsCheck(String isCheck) {
        IsCheck = isCheck;
    }

    public String getStatus() {
        return Status;
    }

    public void setStatus(String status) {
        Status = status;
    }

    public String getRemark() {
        return Remark;
    }

    public void setRemark(String remark) {
        Remark = remark;
    }

    @Override
    public boolean equals(Object arg0) {
        ClaimInfo claimInfo = (ClaimInfo) arg0;
        return InsuredName.equals(claimInfo.InsuredName) && CertiCode.equals(claimInfo.CertiCode);
    }

    @Override
    public int hashCode() {
        String str = InsuredName + CertiCode;
        return str.hashCode();
    }
}
