package com.sinosoft.eflex.model;

import java.util.Date;

public class FCMailInfo {
    /**
     * 发票寄送信息流水号
     */
    private String invoiceInfoSN;

    /**
     * 企业编号
     */
    private String grpNo;

    /**
     * 个人客户号
     */
    private String personId;

    /**
     * 客户类型
     */
    private String customType;

    /**
     * 发票类型
     */
    private String invoiceType;

    /**
     * 发票金额
     */
    private Double invoiceAmount;

    /**
     * 收件人
     */
    private String receiver;

    /**
     * 手机号码
     */
    private String telPhone;

    /**
     * 邮政编码
     */
    private String zipcode;

    /**
     * 付款人
     */
    private String payName;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 所在地区
     */
    private String location;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 生成日期
     */
    private String makeDate;

    /**
     * 生成时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    //福利编号
    private String ensureCode;
    //地区
    private String area;
    //省
    private String province;
    //市
    private String city;
    //申请人
    private String name;

    private String applicantName;

    //申请时间
    private String dateTime;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDateTime() {
        return dateTime;
    }

    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getEnsureCode() {
        return ensureCode;
    }

    public void setEnsureCode(String ensureCode) {
        this.ensureCode = ensureCode;
    }

    /**
     * 发票寄送信息流水号
     * @return InvoiceInfoSN 发票寄送信息流水号
     */
    public String getInvoiceInfoSN() {
        return invoiceInfoSN;
    }

    /**
     * 发票寄送信息流水号
     * @param invoiceInfoSN 发票寄送信息流水号
     */
    public void setInvoiceInfoSN(String invoiceInfoSN) {
        this.invoiceInfoSN = invoiceInfoSN;
    }

    /**
     * 企业编号
     * @return GrpNo 企业编号
     */
    public String getGrpNo() {
        return grpNo;
    }

    /**
     * 企业编号
     * @param grpNo 企业编号
     */
    public void setGrpNo(String grpNo) {
        this.grpNo = grpNo;
    }

    /**
     * 个人客户号
     * @return PersonId 个人客户号
     */
    public String getPersonId() {
        return personId;
    }

    /**
     * 个人客户号
     * @param personId 个人客户号
     */
    public void setPersonId(String personId) {
        this.personId = personId;
    }

    /**
     * 客户类型
     * @return CustomType 客户类型
     */
    public String getCustomType() {
        return customType;
    }

    /**
     * 客户类型
     * @param customType 客户类型
     */
    public void setCustomType(String customType) {
        this.customType = customType;
    }

    /**
     * 发票类型
     * @return InvoiceType 发票类型
     */
    public String getInvoiceType() {
        return invoiceType;
    }

    /**
     * 发票类型
     * @param invoiceType 发票类型
     */
    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
    }

    /**
     * 发票金额
     * @return InvoiceAmount 发票金额
     */
    public Double getInvoiceAmount() {
        return invoiceAmount;
    }

    /**
     * 发票金额
     * @param invoiceAmount 发票金额
     */
    public void setInvoiceAmount(Double invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    /**
     * 收件人
     * @return Receiver 收件人
     */
    public String getReceiver() {
        return receiver;
    }

    /**
     * 收件人
     * @param receiver 收件人
     */
    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    /**
     * 手机号码
     * @return TelPhone 手机号码
     */
    public String getTelPhone() {
        return telPhone;
    }

    /**
     * 手机号码
     * @param telPhone 手机号码
     */
    public void setTelPhone(String telPhone) {
        this.telPhone = telPhone;
    }

    /**
     * 邮政编码
     * @return Zipcode 邮政编码
     */
    public String getZipcode() {
        return zipcode;
    }

    /**
     * 邮政编码
     * @param zipcode 邮政编码
     */
    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }

    /**
     * 付款人
     * @return PayName 付款人
     */
    public String getPayName() {
        return payName;
    }

    /**
     * 付款人
     * @param payName 付款人
     */
    public void setPayName(String payName) {
        this.payName = payName;
    }

    /**
     * 电子邮箱
     * @return Email 电子邮箱
     */
    public String getEmail() {
        return email;
    }

    /**
     * 电子邮箱
     * @param email 电子邮箱
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * 所在地区
     * @return Location 所在地区
     */
    public String getLocation() {
        return location;
    }

    /**
     * 所在地区
     * @param location 所在地区
     */
    public void setLocation(String location) {
        this.location = location;
    }

    /**
     * 详细地址
     * @return Address 详细地址
     */
    public String getAddress() {
        return address;
    }

    /**
     * 详细地址
     * @param address 详细地址
     */
    public void setAddress(String address) {
        this.address = address;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 操作机构
     * @return OperatorCom 操作机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作机构
     * @param operatorCom 操作机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 生成日期
     * @return MakeDate 生成日期
     */

    public String getMakeDate() {
        return makeDate;
    }

    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    public String getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    /**
     * 生成时间
     * @return MakeTime 生成时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 生成时间
     * @param makeTime 生成时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }


    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */


    public String getApplicantName() {
        return applicantName;
    }

    public void setApplicantName(String applicantName) {
        this.applicantName = applicantName;
    }
}