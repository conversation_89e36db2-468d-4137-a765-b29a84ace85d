package com.sinosoft.eflex.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2018-08-13 17:43
 */
@Data
public class RegInfo {
    /*审核状态*/
    private String auditResult;
    /*审核意见*/
    private String auditOpinion;
    /*员工等待期*/
    private String employeeWait;
    /*家属等待期*/
    private String familyWait;
    /*医疗机构*/
    private String medicaInstitutions;
    /*责任说明*/
    private String responsibility;
    /*既往症*/
    private String anamnesis;
    /*其他约定*/
    private String appointments;
    /*股东业务*/
    private String shareBusiness;
    /*股东名称*/
    private String shareholdersName;
    /*法人代表*/
    private String corporationMan;
    /*注册电话*/
    private String telphone;

    public String getShareBusiness() {
        return shareBusiness;
    }

    public void setShareBusiness(String shareBusiness) {
        this.shareBusiness = shareBusiness;
    }

    public String getShareholdersName() {
        return shareholdersName;
    }

    public void setShareholdersName(String shareholdersName) {
        this.shareholdersName = shareholdersName;
    }

    public String getAuditResult() {
        return auditResult;
    }

    public void setAuditResult(String auditResult) {
        this.auditResult = auditResult;
    }

    public String getAuditOpinion() {
        return auditOpinion;
    }

    public void setAuditOpinion(String auditOpinion) {
        this.auditOpinion = auditOpinion;
    }

    public String getEmployeeWait() {
        return employeeWait;
    }

    public void setEmployeeWait(String employeeWait) {
        this.employeeWait = employeeWait;
    }

    public String getFamilyWait() {
        return familyWait;
    }

    public void setFamilyWait(String familyWait) {
        this.familyWait = familyWait;
    }

    public String getMedicaInstitutions() {
        return medicaInstitutions;
    }

    public void setMedicaInstitutions(String medicaInstitutions) {
        this.medicaInstitutions = medicaInstitutions;
    }

    public String getResponsibility() {
        return responsibility;
    }

    public void setResponsibility(String responsibility) {
        this.responsibility = responsibility;
    }

    public String getAnamnesis() {
        return anamnesis;
    }

    public void setAnamnesis(String anamnesis) {
        this.anamnesis = anamnesis;
    }

    public String getAppointments() {
        return appointments;
    }

    public void setAppointments(String appointments) {
        this.appointments = appointments;
    }

    public String getCorporationMan() {
        return corporationMan;
    }

    public void setCorporationMan(String corporationMan) {
        this.corporationMan = corporationMan;
    }

    public String getTelphone() {
        return telphone;
    }

    public void setTelphone(String telphone) {
        this.telphone = telphone;
    }

    @Override
    public String toString() {
        return "RegInfo{" +
                "auditResult='" + auditResult + '\'' +
                ", auditOpinion='" + auditOpinion + '\'' +
                ", employeeWait='" + employeeWait + '\'' +
                ", familyWait='" + familyWait + '\'' +
                ", medicaInstitutions='" + medicaInstitutions + '\'' +
                ", responsibility='" + responsibility + '\'' +
                ", anamnesis='" + anamnesis + '\'' +
                ", appointments='" + appointments + '\'' +
                '}';
    }
}
