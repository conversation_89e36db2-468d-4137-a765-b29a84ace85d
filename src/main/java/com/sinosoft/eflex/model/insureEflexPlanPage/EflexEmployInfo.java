package com.sinosoft.eflex.model.insureEflexPlanPage;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2021/5/12
 * @desc 档次信息
 */
@Data
public class EflexEmployInfo {

    /**
     * 险种编码
     */
    private String riskCode;

    /**
     * 险种名称
     */
    private String riskName;

    /**
     * 责任编码
     */
    private String dutyCode;

    /**
     * 责任名称
     */
    private String dutyName;

    /**
     * 险种类型
     */
    private String riskType;

    /**
     * 档次编号
     */
    private String amountGrageCode;

    /**
     * 是否为默认档次 0--否 1--是
     */
    private String isDefaultFlag;

    /**
     * 免赔额
     */
    private String defaultDeductible;

    /**
     * 赔付比例
     */
    private String defaultCompensationRatio;

    /**
     * 投保人数
     */
    private int insuredNumber;

    /**
     * 按次/按年免赔
     */
    private String annualTimeDeduction;

    /**
     * 保单生效日期
     */
    private String cvaliDate;

    /**
     * 保额
     */
    private String amnt;

}
