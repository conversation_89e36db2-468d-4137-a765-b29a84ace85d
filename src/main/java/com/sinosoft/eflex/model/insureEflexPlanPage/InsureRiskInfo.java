package com.sinosoft.eflex.model.insureEflexPlanPage;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/5/12
 * @desc 档次详情信息
 */
@Data
public class InsureRiskInfo {

    /**
     * 险种编码
     */
    private String riskCode;

    /**
     * 险种名称
     */
    private String riskName;

    /**
     * 险种类型
     */
    private String riskType;

    /**
     * 责任编码
     */
    private String dutyCode;

    /**
     * 责任名称
     */
    private String dutyName;

    /**
     * 保额档次信息
     */
    private List<AmountGrageInfo> insureAmountGradeInfoList;

}
