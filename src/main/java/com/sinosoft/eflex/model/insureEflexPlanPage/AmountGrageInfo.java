package com.sinosoft.eflex.model.insureEflexPlanPage;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021/5/12
 * @desc 档次信息
 */
@Data
public class AmountGrageInfo {

    /**
     * 档次编号
     */
    private String amountGrageCode;

    /**
     * 险种编码
     */
    private String riskCode;

    /**
     * 险种名称
     */
    private String riskName;

    /**
     * 险种类型
     */
    private String riskType;

    /**
     * 责任编码
     */
    private String dutyCode;

    /**
     * 责任名称
     */
    private String dutyName;

    /**
     * 保额
     */
    private String amnt;

    /**
     * 保费
     */
    private String prem;

    /**
     * 按次/按年免赔
     */
    private String annualTimeDeduction;

    /**
     * 免赔额列表
     */
    private List<DeductibleInfo> deductibleList = new ArrayList<>();

    /**
     * 赔付比例列表
     */
    private List<CompensationRatioInfo> compensationRatioList = new ArrayList<>();

    /**
     * 可选责任信息，不为空：免赔额、赔付比例展示在可选责任后面，为空则展示在必选责任后面
     */
    private List<OptDutyCodeInfo> optDutyCodeList = new ArrayList<>();

    /**
     * 是否为默认档次 1：是 0：否
     */
    private String isDefaultFlag = "0";

    /**
     * 是否选中 1：是 0：否
     */
    private String isChecked = "0";

}
