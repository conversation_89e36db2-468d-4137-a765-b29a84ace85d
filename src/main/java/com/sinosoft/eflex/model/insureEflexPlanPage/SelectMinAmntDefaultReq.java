package com.sinosoft.eflex.model.insureEflexPlanPage;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2021/5/13
 * @desc
 */
@Data
public class SelectMinAmntDefaultReq {

    /**
     * 福利编码
     */
    private String ensureCode;

    /**
     * 险种编码
     */
    private String riskCode;

    /**
     * 企业客户号
     */
    private String grpNo;

    /**
     * 出生日期
     */
    private String birthDay;

    /**
     * 职级编码
     */
    private String levelCode;

    /**
     * 职业类别
     */
    private String occupationType;

    /**
     * 性别
     */
    private String sex;

    /**
     * 服务年限
     */
    private String serviceTerm;

    /**
     * 是否退休
     */
    private String retirement;

    /**
     * 关系
     */
    private String relation;

    /**
     * 有无医保
     */
    private String joinMedProtect;

    /**
     * 投保人数
     */
    private Integer insureCount;

    /**
     * 免赔额
     */
    private String defaultDeductible;

    /**
     * 赔付比例
     */
    private String defaultCompensationRatio;

    /**
     * 档次编号
     */
    private String amountGrageCode;

    /**
     * 福利生效日期
     */
    private String cvaliDate;

    /**
     * 被保人类型 0-员工 1-家属
     */
    private String insuredType;

}
