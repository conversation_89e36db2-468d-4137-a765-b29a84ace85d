package com.sinosoft.eflex.model.insureEflexPlanPage;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2021/5/13
 * @desc
 */
@Data
public class GetInsureRiskInfoResp {

    /**
     * 险种编码
     */
    private String riskCode;

    /**
     * 险种名称
     */
    private String riskName;

    /**
     * 险种类别 01 民航班机 02 轨道交通工具 03 水运公共交通工具 04 公路公共交通工具
     */
    private String riskType;

    /**
     * 责任编码
     */
    private String dutyCode;

    /**
     * 保额
     */
    private String amnt;

    /**
     * 保费
     */
    private String prem;

    /**
     * 投保必选责任档次
     */
    private String amountGrageCode;

    /**
     * 1-必选保额档次;2-可选保额档次
     */
    private String isChoice;

    /**
     * 免赔额
     */
    private String deductible;

    /**
     * 赔付比例
     */
    private String compensationRatio;

}
