package com.sinosoft.eflex.model.insureEflexPlanPage;

import java.util.List;

import com.sinosoft.eflex.model.insurePlanPage.InsurePlanInfo;
import com.sinosoft.eflex.model.insurePlanPage.PersonInfo;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2021/3/26
 */
@Data
public class EflexPeopleZoneData {

    /**
     * 人员信息
     */
    private PersonInfo personInfo;

    /**
     * 是否保存过保额档次信息 1-投过 0-未投
     */
    private String isExists;

    /**
     * 险种信息
     */
    private List<InsureRiskInfo> riskList;

    /**
     * 总保费
     */
    private String totalPrem;

}
