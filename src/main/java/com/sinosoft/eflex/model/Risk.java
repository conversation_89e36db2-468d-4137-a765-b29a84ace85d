package com.sinosoft.eflex.model;
/** 
 * <AUTHOR>
 * @version 创建时间: 2019年7月3日 下午2:58:34 
 * <AUTHOR>
 * @date 2019年7月3日 
*/

import java.util.List;

import com.sinosoft.eflex.model.BatchInsureInterface.DutyInfo;

public class Risk {
	private String RiskCode;  
	private String RiskName;  
	private String RiskAmnt;
	private String RiskPrem;
	private List<Duty> DutyList;

	public String getRiskCode() {
		return RiskCode;
	}

	public void setRiskCode(String riskCode) {
		RiskCode = riskCode;
	}

	public String getRiskName() {
		return RiskName;
	}

	public void setRiskName(String riskName) {
		RiskName = riskName;
	}

	public String getRiskAmnt() {
		return RiskAmnt;
	}

	public void setRiskAmnt(String riskAmnt) {
		RiskAmnt = riskAmnt;
	}

	public String getRiskPrem() {
		return RiskPrem;
	}

	public void setRiskPrem(String riskPrem) {
		RiskPrem = riskPrem;
	}

	public List<Duty> getDutyList() {
		return DutyList;
	}

	public void setDutyList(List<Duty> dutyList) {
		DutyList = dutyList;
	}

	
	
}
