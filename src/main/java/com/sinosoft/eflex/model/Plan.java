package com.sinosoft.eflex.model;
/** 
 * <AUTHOR>
 * @version 创建时间: 2019年7月3日 下午2:58:34 
 * <AUTHOR>
 * @date 2019年7月3日 
*/

import java.util.List;

public class Plan {
	private String Plancode;  
	private String PlanName;
	private String PlanObject;
	private String TotalPrem;
	
	private List<Risk> RiskList;

	public String getPlancode() {
		return Plancode;
	}

	public void setPlancode(String plancode) {
		Plancode = plancode;
	}

	public String getPlanName() {
		return PlanName;
	}

	public void setPlanName(String planName) {
		PlanName = planName;
	}

	public String getTotalPrem() {
		return TotalPrem;
	}

	public void setTotalPrem(String totalPrem) {
		TotalPrem = totalPrem;
	}

	public List<Risk> getRiskList() {
		return RiskList;
	}

	public void setRiskList(List<Risk> riskList) {
		RiskList = riskList;
	}

	public String getPlanObject() {
		return PlanObject;
	}

	public void setPlanObject(String planObject) {
		PlanObject = planObject;
	}
}
