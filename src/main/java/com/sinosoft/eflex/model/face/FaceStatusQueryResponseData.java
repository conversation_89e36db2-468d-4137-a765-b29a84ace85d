package com.sinosoft.eflex.model.face;

public class FaceStatusQueryResponseData {
	private String requestId;
	private String errCode;
	private String errMsg;
	private String idCard;
	private String name;
	private String ocrNation;
	private String ocrAddress;
	private String ocrBirth;
	private String ocrAuthority;
	private String ocrValidDate;
	private String ocrName;
	private String ocrIdCard;
	private String ocrGender;
	private String liveStatus;
	private String liveMsg;
	private String comparestatus;
	private String comparemsg;
	private String location;
	private String extra;
	private String ocrFront;
	private String ocrBack;
	private String bestFrame;
	private String livenessVideo;
	private String livenessCode;
	private String livenessMsg;
	private String reqTime;
	private String sim;
	public String getRequestId() {
		return requestId;
	}
	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}
	public String getErrCode() {
		return errCode;
	}
	public void setErrCode(String errCode) {
		this.errCode = errCode;
	}
	public String getErrMsg() {
		return errMsg;
	}
	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}
	public String getIdCard() {
		return idCard;
	}
	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getOcrNation() {
		return ocrNation;
	}
	public void setOcrNation(String ocrNation) {
		this.ocrNation = ocrNation;
	}
	public String getOcrAddress() {
		return ocrAddress;
	}
	public void setOcrAddress(String ocrAddress) {
		this.ocrAddress = ocrAddress;
	}
	public String getOcrBirth() {
		return ocrBirth;
	}
	public void setOcrBirth(String ocrBirth) {
		this.ocrBirth = ocrBirth;
	}
	public String getOcrAuthority() {
		return ocrAuthority;
	}
	public void setOcrAuthority(String ocrAuthority) {
		this.ocrAuthority = ocrAuthority;
	}
	public String getOcrValidDate() {
		return ocrValidDate;
	}
	public void setOcrValidDate(String ocrValidDate) {
		this.ocrValidDate = ocrValidDate;
	}
	public String getOcrName() {
		return ocrName;
	}
	public void setOcrName(String ocrName) {
		this.ocrName = ocrName;
	}
	public String getOcrIdCard() {
		return ocrIdCard;
	}
	public void setOcrIdCard(String ocrIdCard) {
		this.ocrIdCard = ocrIdCard;
	}
	public String getOcrGender() {
		return ocrGender;
	}
	public void setOcrGender(String ocrGender) {
		this.ocrGender = ocrGender;
	}
	public String getLiveStatus() {
		return liveStatus;
	}
	public void setLiveStatus(String liveStatus) {
		this.liveStatus = liveStatus;
	}
	public String getLiveMsg() {
		return liveMsg;
	}
	public void setLiveMsg(String liveMsg) {
		this.liveMsg = liveMsg;
	}
	public String getComparestatus() {
		return comparestatus;
	}
	public void setComparestatus(String comparestatus) {
		this.comparestatus = comparestatus;
	}
	public String getComparemsg() {
		return comparemsg;
	}
	public void setComparemsg(String comparemsg) {
		this.comparemsg = comparemsg;
	}
	public String getLocation() {
		return location;
	}
	public void setLocation(String location) {
		this.location = location;
	}
	public String getExtra() {
		return extra;
	}
	public void setExtra(String extra) {
		this.extra = extra;
	}
	public String getOcrFront() {
		return ocrFront;
	}
	public void setOcrFront(String ocrFront) {
		this.ocrFront = ocrFront;
	}
	public String getOcrBack() {
		return ocrBack;
	}
	public void setOcrBack(String ocrBack) {
		this.ocrBack = ocrBack;
	}
	public String getBestFrame() {
		return bestFrame;
	}
	public void setBestFrame(String bestFrame) {
		this.bestFrame = bestFrame;
	}
	public String getLivenessVideo() {
		return livenessVideo;
	}
	public void setLivenessVideo(String livenessVideo) {
		this.livenessVideo = livenessVideo;
	}
	public String getLivenessCode() {
		return livenessCode;
	}
	public void setLivenessCode(String livenessCode) {
		this.livenessCode = livenessCode;
	}
	public String getLivenessMsg() {
		return livenessMsg;
	}
	public void setLivenessMsg(String livenessMsg) {
		this.livenessMsg = livenessMsg;
	}
	public String getReqTime() {
		return reqTime;
	}
	public void setReqTime(String reqTime) {
		this.reqTime = reqTime;
	}
	public String getSim() {
		return sim;
	}
	public void setSim(String sim) {
		this.sim = sim;
	}
}
