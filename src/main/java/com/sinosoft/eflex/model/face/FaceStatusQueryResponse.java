package com.sinosoft.eflex.model.face;

public class FaceStatusQueryResponse {
	private String code;//响应编码
	private String message;//响应信息
	private boolean success;//成功标识
	private FaceStatusQueryResponseData data;//业务内容
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public boolean isSuccess() {
		return success;
	}
	public void setSuccess(boolean success) {
		this.success = success;
	}
	public FaceStatusQueryResponseData getData() {
		return data;
	}
	public void setData(FaceStatusQueryResponseData data) {
		this.data = data;
	}
}
