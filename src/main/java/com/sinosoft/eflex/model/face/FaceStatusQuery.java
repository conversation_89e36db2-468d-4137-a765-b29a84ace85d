package com.sinosoft.eflex.model.face;

public class FaceStatusQuery {
	private String transCode;//交易编码
	private String channelSource;//渠道来源
	private String actionType;//活体接入方式
	private String infoType;//结果查询类型
	private String bizToken;//业务流水号
	public String getTransCode() {
		return transCode;
	}
	public void setTransCode(String transCode) {
		this.transCode = transCode;
	}
	public String getChannelSource() {
		return channelSource;
	}
	public void setChannelSource(String channelSource) {
		this.channelSource = channelSource;
	}
	public String getActionType() {
		return actionType;
	}
	public void setActionType(String actionType) {
		this.actionType = actionType;
	}
	public String getInfoType() {
		return infoType;
	}
	public void setInfoType(String infoType) {
		this.infoType = infoType;
	}
	public String getBizToken() {
		return bizToken;
	}
	public void setBizToken(String bizToken) {
		this.bizToken = bizToken;
	}
}
