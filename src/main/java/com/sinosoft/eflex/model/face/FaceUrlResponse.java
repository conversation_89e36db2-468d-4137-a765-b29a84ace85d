package com.sinosoft.eflex.model.face;

public class FaceUrlResponse {
	private String code;//响应编码
	private String message;//响应信息
	private boolean success;//成功标识
	private FaceUrlResponseData data;//业务内容
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public boolean isSuccess() {
		return success;
	}
	public void setSuccess(boolean success) {
		this.success = success;
	}
	public FaceUrlResponseData getData() {
		return data;
	}
	public void setData(FaceUrlResponseData data) {
		this.data = data;
	}
}
