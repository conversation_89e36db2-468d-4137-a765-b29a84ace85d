package com.sinosoft.eflex.model;

import java.util.Date;
import java.util.List;

public class FcPlanRiskInfo {
    /**
     * 福利编号
     */
    private String ensureCode;

    /**
     * 险种编码
     */
    private String riskCode;

    /**
     * 险种类别 只针对15070险种（其他险种存01）
     */
    private String riskType;

    /**
     * 险种名称
     */
    private String riskName;

    /**
     * 险种范围
     */
    private String RiskRange;

    /**
     * 分保标记
     */
    private String reinsuranceMark;

    /**
     * 手续费比率
     */
    private Double feeRatio;

    /**
     * 佣金/服务津贴率
     */
    private Double commissionOrAllowanceRatio;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 管理机构
     */
    private String operatorCom;

    /**
     * 入机日期
     */
    private String makeDate;

    /**
     * 入机时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 保额档次集合
     */
    private List<FcDutyAmountGrade> fcDutyAmountGradeList;
    /**
     * 福利编号
     * @return EnsureCode 福利编号
     */
    public String getEnsureCode() {
        return ensureCode;
    }

    /**
     * 福利编号
     * @param ensureCode 福利编号
     */
    public void setEnsureCode(String ensureCode) {
        this.ensureCode = ensureCode;
    }

    /**
     * 险种编码
     * @return RiskCode 险种编码
     */
    public String getRiskCode() {
        return riskCode;
    }

    /**
     * 险种编码
     * @param riskCode 险种编码
     */
    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    /**
     * 险种类别 只针对15070险种（其他险种存01）
     * @return RiskType 险种类别 只针对15070险种（其他险种存01）
     */
    public String getRiskType() {
        return riskType;
    }

    /**
     * 险种类别 只针对15070险种（其他险种存01）
     * @param riskType 险种类别 只针对15070险种（其他险种存01）
     */
    public void setRiskType(String riskType) {
        this.riskType = riskType;
    }

    public String getRiskName() {
        return riskName;
    }

    public void setRiskName(String riskName) {
        this.riskName = riskName;
    }

    public String getRiskRange() {
        return RiskRange;
    }

    public void setRiskRange(String riskRange) {
        RiskRange = riskRange;
    }

    /**
     * 分保标记
     * @return reinsuranceMark 分保标记
     */
    public String getReinsuranceMark() {
        return reinsuranceMark;
    }

    /**
     * 分保标记
     * @param reinsuranceMark 分保标记
     */
    public void setReinsuranceMark(String reinsuranceMark) {
        this.reinsuranceMark = reinsuranceMark;
    }

    /**
     * 手续费比率
     * @return feeRatio 手续费比率
     */
    public Double getFeeRatio() {
        return feeRatio;
    }

    /**
     * 手续费比率
     * @param feeRatio 手续费比率
     */
    public void setFeeRatio(Double feeRatio) {
        this.feeRatio = feeRatio;
    }

    /**
     * 佣金/服务津贴率
     * @return commissionOrAllowanceRatio 佣金/服务津贴率
     */
    public Double getCommissionOrAllowanceRatio() {
        return commissionOrAllowanceRatio;
    }

    /**
     * 佣金/服务津贴率
     * @param commissionOrAllowanceRatio 佣金/服务津贴率
     */
    public void setCommissionOrAllowanceRatio(Double commissionOrAllowanceRatio) {
        this.commissionOrAllowanceRatio = commissionOrAllowanceRatio;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 管理机构
     * @return OperatorCom 管理机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 管理机构
     * @param operatorCom 管理机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 入机日期
     * @return MakeDate 入机日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 入机日期
     * @param makeDate 入机日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 入机时间
     * @return MakeTime 入机时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 入机时间
     * @param makeTime 入机时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public List<FcDutyAmountGrade> getFcDutyAmountGradeList() {
        return fcDutyAmountGradeList;
    }

    public void setFcDutyAmountGradeList(List<FcDutyAmountGrade> fcDutyAmountGradeList) {
        this.fcDutyAmountGradeList = fcDutyAmountGradeList;
    }
}