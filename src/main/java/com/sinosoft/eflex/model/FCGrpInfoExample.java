package com.sinosoft.eflex.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class FCGrpInfoExample {
    /**
     * fcgrpinfo
     */
    protected String orderByClause;

    /**
     * fcgrpinfo
     */
    protected boolean distinct;

    /**
     * fcgrpinfo
     */
    protected List<Criteria> oredCriteria;

    public FCGrpInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * fcgrpinfo null
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andGrpNoIsNull() {
            addCriterion("GrpNo is null");
            return (Criteria) this;
        }

        public Criteria andGrpNoIsNotNull() {
            addCriterion("GrpNo is not null");
            return (Criteria) this;
        }

        public Criteria andGrpNoEqualTo(String value) {
            addCriterion("GrpNo =", value, "grpNo");
            return (Criteria) this;
        }

        public Criteria andGrpNoNotEqualTo(String value) {
            addCriterion("GrpNo <>", value, "grpNo");
            return (Criteria) this;
        }

        public Criteria andGrpNoGreaterThan(String value) {
            addCriterion("GrpNo >", value, "grpNo");
            return (Criteria) this;
        }

        public Criteria andGrpNoGreaterThanOrEqualTo(String value) {
            addCriterion("GrpNo >=", value, "grpNo");
            return (Criteria) this;
        }

        public Criteria andGrpNoLessThan(String value) {
            addCriterion("GrpNo <", value, "grpNo");
            return (Criteria) this;
        }

        public Criteria andGrpNoLessThanOrEqualTo(String value) {
            addCriterion("GrpNo <=", value, "grpNo");
            return (Criteria) this;
        }

        public Criteria andGrpNoLike(String value) {
            addCriterion("GrpNo like", value, "grpNo");
            return (Criteria) this;
        }

        public Criteria andGrpNoNotLike(String value) {
            addCriterion("GrpNo not like", value, "grpNo");
            return (Criteria) this;
        }

        public Criteria andGrpNoIn(List<String> values) {
            addCriterion("GrpNo in", values, "grpNo");
            return (Criteria) this;
        }

        public Criteria andGrpNoNotIn(List<String> values) {
            addCriterion("GrpNo not in", values, "grpNo");
            return (Criteria) this;
        }

        public Criteria andGrpNoBetween(String value1, String value2) {
            addCriterion("GrpNo between", value1, value2, "grpNo");
            return (Criteria) this;
        }

        public Criteria andGrpNoNotBetween(String value1, String value2) {
            addCriterion("GrpNo not between", value1, value2, "grpNo");
            return (Criteria) this;
        }

        public Criteria andCGrpNoIsNull() {
            addCriterion("CGrpNo is null");
            return (Criteria) this;
        }

        public Criteria andCGrpNoIsNotNull() {
            addCriterion("CGrpNo is not null");
            return (Criteria) this;
        }

        public Criteria andCGrpNoEqualTo(String value) {
            addCriterion("CGrpNo =", value, "CGrpNo");
            return (Criteria) this;
        }

        public Criteria andCGrpNoNotEqualTo(String value) {
            addCriterion("CGrpNo <>", value, "CGrpNo");
            return (Criteria) this;
        }

        public Criteria andCGrpNoGreaterThan(String value) {
            addCriterion("CGrpNo >", value, "CGrpNo");
            return (Criteria) this;
        }

        public Criteria andCGrpNoGreaterThanOrEqualTo(String value) {
            addCriterion("CGrpNo >=", value, "CGrpNo");
            return (Criteria) this;
        }

        public Criteria andCGrpNoLessThan(String value) {
            addCriterion("CGrpNo <", value, "CGrpNo");
            return (Criteria) this;
        }

        public Criteria andCGrpNoLessThanOrEqualTo(String value) {
            addCriterion("CGrpNo <=", value, "CGrpNo");
            return (Criteria) this;
        }

        public Criteria andCGrpNoLike(String value) {
            addCriterion("CGrpNo like", value, "CGrpNo");
            return (Criteria) this;
        }

        public Criteria andCGrpNoNotLike(String value) {
            addCriterion("CGrpNo not like", value, "CGrpNo");
            return (Criteria) this;
        }

        public Criteria andCGrpNoIn(List<String> values) {
            addCriterion("CGrpNo in", values, "CGrpNo");
            return (Criteria) this;
        }

        public Criteria andCGrpNoNotIn(List<String> values) {
            addCriterion("CGrpNo not in", values, "CGrpNo");
            return (Criteria) this;
        }

        public Criteria andCGrpNoBetween(String value1, String value2) {
            addCriterion("CGrpNo between", value1, value2, "CGrpNo");
            return (Criteria) this;
        }

        public Criteria andCGrpNoNotBetween(String value1, String value2) {
            addCriterion("CGrpNo not between", value1, value2, "CGrpNo");
            return (Criteria) this;
        }

        public Criteria andGrpNameIsNull() {
            addCriterion("GrpName is null");
            return (Criteria) this;
        }

        public Criteria andGrpNameIsNotNull() {
            addCriterion("GrpName is not null");
            return (Criteria) this;
        }

        public Criteria andGrpNameEqualTo(String value) {
            addCriterion("GrpName =", value, "grpName");
            return (Criteria) this;
        }

        public Criteria andGrpNameNotEqualTo(String value) {
            addCriterion("GrpName <>", value, "grpName");
            return (Criteria) this;
        }

        public Criteria andGrpNameGreaterThan(String value) {
            addCriterion("GrpName >", value, "grpName");
            return (Criteria) this;
        }

        public Criteria andGrpNameGreaterThanOrEqualTo(String value) {
            addCriterion("GrpName >=", value, "grpName");
            return (Criteria) this;
        }

        public Criteria andGrpNameLessThan(String value) {
            addCriterion("GrpName <", value, "grpName");
            return (Criteria) this;
        }

        public Criteria andGrpNameLessThanOrEqualTo(String value) {
            addCriterion("GrpName <=", value, "grpName");
            return (Criteria) this;
        }

        public Criteria andGrpNameLike(String value) {
            addCriterion("GrpName like", value, "grpName");
            return (Criteria) this;
        }

        public Criteria andGrpNameNotLike(String value) {
            addCriterion("GrpName not like", value, "grpName");
            return (Criteria) this;
        }

        public Criteria andGrpNameIn(List<String> values) {
            addCriterion("GrpName in", values, "grpName");
            return (Criteria) this;
        }

        public Criteria andGrpNameNotIn(List<String> values) {
            addCriterion("GrpName not in", values, "grpName");
            return (Criteria) this;
        }

        public Criteria andGrpNameBetween(String value1, String value2) {
            addCriterion("GrpName between", value1, value2, "grpName");
            return (Criteria) this;
        }

        public Criteria andGrpNameNotBetween(String value1, String value2) {
            addCriterion("GrpName not between", value1, value2, "grpName");
            return (Criteria) this;
        }

        public Criteria andGrpAddRessIsNull() {
            addCriterion("GrpAddRess is null");
            return (Criteria) this;
        }

        public Criteria andGrpAddRessIsNotNull() {
            addCriterion("GrpAddRess is not null");
            return (Criteria) this;
        }

        public Criteria andGrpAddRessEqualTo(String value) {
            addCriterion("GrpAddRess =", value, "grpAddRess");
            return (Criteria) this;
        }

        public Criteria andGrpAddRessNotEqualTo(String value) {
            addCriterion("GrpAddRess <>", value, "grpAddRess");
            return (Criteria) this;
        }

        public Criteria andGrpAddRessGreaterThan(String value) {
            addCriterion("GrpAddRess >", value, "grpAddRess");
            return (Criteria) this;
        }

        public Criteria andGrpAddRessGreaterThanOrEqualTo(String value) {
            addCriterion("GrpAddRess >=", value, "grpAddRess");
            return (Criteria) this;
        }

        public Criteria andGrpAddRessLessThan(String value) {
            addCriterion("GrpAddRess <", value, "grpAddRess");
            return (Criteria) this;
        }

        public Criteria andGrpAddRessLessThanOrEqualTo(String value) {
            addCriterion("GrpAddRess <=", value, "grpAddRess");
            return (Criteria) this;
        }

        public Criteria andGrpAddRessLike(String value) {
            addCriterion("GrpAddRess like", value, "grpAddRess");
            return (Criteria) this;
        }

        public Criteria andGrpAddRessNotLike(String value) {
            addCriterion("GrpAddRess not like", value, "grpAddRess");
            return (Criteria) this;
        }

        public Criteria andGrpAddRessIn(List<String> values) {
            addCriterion("GrpAddRess in", values, "grpAddRess");
            return (Criteria) this;
        }

        public Criteria andGrpAddRessNotIn(List<String> values) {
            addCriterion("GrpAddRess not in", values, "grpAddRess");
            return (Criteria) this;
        }

        public Criteria andGrpAddRessBetween(String value1, String value2) {
            addCriterion("GrpAddRess between", value1, value2, "grpAddRess");
            return (Criteria) this;
        }

        public Criteria andGrpAddRessNotBetween(String value1, String value2) {
            addCriterion("GrpAddRess not between", value1, value2, "grpAddRess");
            return (Criteria) this;
        }

        public Criteria andZipCodeIsNull() {
            addCriterion("ZipCode is null");
            return (Criteria) this;
        }

        public Criteria andZipCodeIsNotNull() {
            addCriterion("ZipCode is not null");
            return (Criteria) this;
        }

        public Criteria andZipCodeEqualTo(String value) {
            addCriterion("ZipCode =", value, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeNotEqualTo(String value) {
            addCriterion("ZipCode <>", value, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeGreaterThan(String value) {
            addCriterion("ZipCode >", value, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeGreaterThanOrEqualTo(String value) {
            addCriterion("ZipCode >=", value, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeLessThan(String value) {
            addCriterion("ZipCode <", value, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeLessThanOrEqualTo(String value) {
            addCriterion("ZipCode <=", value, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeLike(String value) {
            addCriterion("ZipCode like", value, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeNotLike(String value) {
            addCriterion("ZipCode not like", value, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeIn(List<String> values) {
            addCriterion("ZipCode in", values, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeNotIn(List<String> values) {
            addCriterion("ZipCode not in", values, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeBetween(String value1, String value2) {
            addCriterion("ZipCode between", value1, value2, "zipCode");
            return (Criteria) this;
        }

        public Criteria andZipCodeNotBetween(String value1, String value2) {
            addCriterion("ZipCode not between", value1, value2, "zipCode");
            return (Criteria) this;
        }

        public Criteria andUnifiedsociCodeIsNull() {
            addCriterion("UnifiedsociCode is null");
            return (Criteria) this;
        }

        public Criteria andUnifiedsociCodeIsNotNull() {
            addCriterion("UnifiedsociCode is not null");
            return (Criteria) this;
        }

        public Criteria andUnifiedsociCodeEqualTo(String value) {
            addCriterion("UnifiedsociCode =", value, "unifiedsociCode");
            return (Criteria) this;
        }

        public Criteria andUnifiedsociCodeNotEqualTo(String value) {
            addCriterion("UnifiedsociCode <>", value, "unifiedsociCode");
            return (Criteria) this;
        }

        public Criteria andUnifiedsociCodeGreaterThan(String value) {
            addCriterion("UnifiedsociCode >", value, "unifiedsociCode");
            return (Criteria) this;
        }

        public Criteria andUnifiedsociCodeGreaterThanOrEqualTo(String value) {
            addCriterion("UnifiedsociCode >=", value, "unifiedsociCode");
            return (Criteria) this;
        }

        public Criteria andUnifiedsociCodeLessThan(String value) {
            addCriterion("UnifiedsociCode <", value, "unifiedsociCode");
            return (Criteria) this;
        }

        public Criteria andUnifiedsociCodeLessThanOrEqualTo(String value) {
            addCriterion("UnifiedsociCode <=", value, "unifiedsociCode");
            return (Criteria) this;
        }

        public Criteria andUnifiedsociCodeLike(String value) {
            addCriterion("UnifiedsociCode like", value, "unifiedsociCode");
            return (Criteria) this;
        }

        public Criteria andUnifiedsociCodeNotLike(String value) {
            addCriterion("UnifiedsociCode not like", value, "unifiedsociCode");
            return (Criteria) this;
        }

        public Criteria andUnifiedsociCodeIn(List<String> values) {
            addCriterion("UnifiedsociCode in", values, "unifiedsociCode");
            return (Criteria) this;
        }

        public Criteria andUnifiedsociCodeNotIn(List<String> values) {
            addCriterion("UnifiedsociCode not in", values, "unifiedsociCode");
            return (Criteria) this;
        }

        public Criteria andUnifiedsociCodeBetween(String value1, String value2) {
            addCriterion("UnifiedsociCode between", value1, value2, "unifiedsociCode");
            return (Criteria) this;
        }

        public Criteria andUnifiedsociCodeNotBetween(String value1, String value2) {
            addCriterion("UnifiedsociCode not between", value1, value2, "unifiedsociCode");
            return (Criteria) this;
        }

        public Criteria andGrpIdTypeIsNull() {
            addCriterion("GrpIdType is null");
            return (Criteria) this;
        }

        public Criteria andGrpIdTypeIsNotNull() {
            addCriterion("GrpIdType is not null");
            return (Criteria) this;
        }

        public Criteria andGrpIdTypeEqualTo(String value) {
            addCriterion("GrpIdType =", value, "grpIdType");
            return (Criteria) this;
        }

        public Criteria andGrpIdTypeNotEqualTo(String value) {
            addCriterion("GrpIdType <>", value, "grpIdType");
            return (Criteria) this;
        }

        public Criteria andGrpIdTypeGreaterThan(String value) {
            addCriterion("GrpIdType >", value, "grpIdType");
            return (Criteria) this;
        }

        public Criteria andGrpIdTypeGreaterThanOrEqualTo(String value) {
            addCriterion("GrpIdType >=", value, "grpIdType");
            return (Criteria) this;
        }

        public Criteria andGrpIdTypeLessThan(String value) {
            addCriterion("GrpIdType <", value, "grpIdType");
            return (Criteria) this;
        }

        public Criteria andGrpIdTypeLessThanOrEqualTo(String value) {
            addCriterion("GrpIdType <=", value, "grpIdType");
            return (Criteria) this;
        }

        public Criteria andGrpIdTypeLike(String value) {
            addCriterion("GrpIdType like", value, "grpIdType");
            return (Criteria) this;
        }

        public Criteria andGrpIdTypeNotLike(String value) {
            addCriterion("GrpIdType not like", value, "grpIdType");
            return (Criteria) this;
        }

        public Criteria andGrpIdTypeIn(List<String> values) {
            addCriterion("GrpIdType in", values, "grpIdType");
            return (Criteria) this;
        }

        public Criteria andGrpIdTypeNotIn(List<String> values) {
            addCriterion("GrpIdType not in", values, "grpIdType");
            return (Criteria) this;
        }

        public Criteria andGrpIdTypeBetween(String value1, String value2) {
            addCriterion("GrpIdType between", value1, value2, "grpIdType");
            return (Criteria) this;
        }

        public Criteria andGrpIdTypeNotBetween(String value1, String value2) {
            addCriterion("GrpIdType not between", value1, value2, "grpIdType");
            return (Criteria) this;
        }

        public Criteria andGrpIdNoIsNull() {
            addCriterion("GrpIdNo is null");
            return (Criteria) this;
        }

        public Criteria andGrpIdNoIsNotNull() {
            addCriterion("GrpIdNo is not null");
            return (Criteria) this;
        }

        public Criteria andGrpIdNoEqualTo(String value) {
            addCriterion("GrpIdNo =", value, "grpIdNo");
            return (Criteria) this;
        }

        public Criteria andGrpIdNoNotEqualTo(String value) {
            addCriterion("GrpIdNo <>", value, "grpIdNo");
            return (Criteria) this;
        }

        public Criteria andGrpIdNoGreaterThan(String value) {
            addCriterion("GrpIdNo >", value, "grpIdNo");
            return (Criteria) this;
        }

        public Criteria andGrpIdNoGreaterThanOrEqualTo(String value) {
            addCriterion("GrpIdNo >=", value, "grpIdNo");
            return (Criteria) this;
        }

        public Criteria andGrpIdNoLessThan(String value) {
            addCriterion("GrpIdNo <", value, "grpIdNo");
            return (Criteria) this;
        }

        public Criteria andGrpIdNoLessThanOrEqualTo(String value) {
            addCriterion("GrpIdNo <=", value, "grpIdNo");
            return (Criteria) this;
        }

        public Criteria andGrpIdNoLike(String value) {
            addCriterion("GrpIdNo like", value, "grpIdNo");
            return (Criteria) this;
        }

        public Criteria andGrpIdNoNotLike(String value) {
            addCriterion("GrpIdNo not like", value, "grpIdNo");
            return (Criteria) this;
        }

        public Criteria andGrpIdNoIn(List<String> values) {
            addCriterion("GrpIdNo in", values, "grpIdNo");
            return (Criteria) this;
        }

        public Criteria andGrpIdNoNotIn(List<String> values) {
            addCriterion("GrpIdNo not in", values, "grpIdNo");
            return (Criteria) this;
        }

        public Criteria andGrpIdNoBetween(String value1, String value2) {
            addCriterion("GrpIdNo between", value1, value2, "grpIdNo");
            return (Criteria) this;
        }

        public Criteria andGrpIdNoNotBetween(String value1, String value2) {
            addCriterion("GrpIdNo not between", value1, value2, "grpIdNo");
            return (Criteria) this;
        }

        public Criteria andGrpTypeIsNull() {
            addCriterion("GrpType is null");
            return (Criteria) this;
        }

        public Criteria andGrpTypeIsNotNull() {
            addCriterion("GrpType is not null");
            return (Criteria) this;
        }

        public Criteria andGrpTypeEqualTo(String value) {
            addCriterion("GrpType =", value, "grpType");
            return (Criteria) this;
        }

        public Criteria andGrpTypeNotEqualTo(String value) {
            addCriterion("GrpType <>", value, "grpType");
            return (Criteria) this;
        }

        public Criteria andGrpTypeGreaterThan(String value) {
            addCriterion("GrpType >", value, "grpType");
            return (Criteria) this;
        }

        public Criteria andGrpTypeGreaterThanOrEqualTo(String value) {
            addCriterion("GrpType >=", value, "grpType");
            return (Criteria) this;
        }

        public Criteria andGrpTypeLessThan(String value) {
            addCriterion("GrpType <", value, "grpType");
            return (Criteria) this;
        }

        public Criteria andGrpTypeLessThanOrEqualTo(String value) {
            addCriterion("GrpType <=", value, "grpType");
            return (Criteria) this;
        }

        public Criteria andGrpTypeLike(String value) {
            addCriterion("GrpType like", value, "grpType");
            return (Criteria) this;
        }

        public Criteria andGrpTypeNotLike(String value) {
            addCriterion("GrpType not like", value, "grpType");
            return (Criteria) this;
        }

        public Criteria andGrpTypeIn(List<String> values) {
            addCriterion("GrpType in", values, "grpType");
            return (Criteria) this;
        }

        public Criteria andGrpTypeNotIn(List<String> values) {
            addCriterion("GrpType not in", values, "grpType");
            return (Criteria) this;
        }

        public Criteria andGrpTypeBetween(String value1, String value2) {
            addCriterion("GrpType between", value1, value2, "grpType");
            return (Criteria) this;
        }

        public Criteria andGrpTypeNotBetween(String value1, String value2) {
            addCriterion("GrpType not between", value1, value2, "grpType");
            return (Criteria) this;
        }

        public Criteria andAccNameIsNull() {
            addCriterion("AccName is null");
            return (Criteria) this;
        }

        public Criteria andAccNameIsNotNull() {
            addCriterion("AccName is not null");
            return (Criteria) this;
        }

        public Criteria andAccNameEqualTo(String value) {
            addCriterion("AccName =", value, "accName");
            return (Criteria) this;
        }

        public Criteria andAccNameNotEqualTo(String value) {
            addCriterion("AccName <>", value, "accName");
            return (Criteria) this;
        }

        public Criteria andAccNameGreaterThan(String value) {
            addCriterion("AccName >", value, "accName");
            return (Criteria) this;
        }

        public Criteria andAccNameGreaterThanOrEqualTo(String value) {
            addCriterion("AccName >=", value, "accName");
            return (Criteria) this;
        }

        public Criteria andAccNameLessThan(String value) {
            addCriterion("AccName <", value, "accName");
            return (Criteria) this;
        }

        public Criteria andAccNameLessThanOrEqualTo(String value) {
            addCriterion("AccName <=", value, "accName");
            return (Criteria) this;
        }

        public Criteria andAccNameLike(String value) {
            addCriterion("AccName like", value, "accName");
            return (Criteria) this;
        }

        public Criteria andAccNameNotLike(String value) {
            addCriterion("AccName not like", value, "accName");
            return (Criteria) this;
        }

        public Criteria andAccNameIn(List<String> values) {
            addCriterion("AccName in", values, "accName");
            return (Criteria) this;
        }

        public Criteria andAccNameNotIn(List<String> values) {
            addCriterion("AccName not in", values, "accName");
            return (Criteria) this;
        }

        public Criteria andAccNameBetween(String value1, String value2) {
            addCriterion("AccName between", value1, value2, "accName");
            return (Criteria) this;
        }

        public Criteria andAccNameNotBetween(String value1, String value2) {
            addCriterion("AccName not between", value1, value2, "accName");
            return (Criteria) this;
        }

        public Criteria andGrpBankCodeIsNull() {
            addCriterion("grpBankCode is null");
            return (Criteria) this;
        }

        public Criteria andGrpBankCodeIsNotNull() {
            addCriterion("grpBankCode is not null");
            return (Criteria) this;
        }

        public Criteria andGrpBankCodeEqualTo(String value) {
            addCriterion("grpBankCode =", value, "grpBankCode");
            return (Criteria) this;
        }

        public Criteria andGrpBankCodeNotEqualTo(String value) {
            addCriterion("grpBankCode <>", value, "grpBankCode");
            return (Criteria) this;
        }

        public Criteria andGrpBankCodeGreaterThan(String value) {
            addCriterion("grpBankCode >", value, "grpBankCode");
            return (Criteria) this;
        }

        public Criteria andGrpBankCodeGreaterThanOrEqualTo(String value) {
            addCriterion("grpBankCode >=", value, "grpBankCode");
            return (Criteria) this;
        }

        public Criteria andGrpBankCodeLessThan(String value) {
            addCriterion("grpBankCode <", value, "grpBankCode");
            return (Criteria) this;
        }

        public Criteria andGrpBankCodeLessThanOrEqualTo(String value) {
            addCriterion("grpBankCode <=", value, "grpBankCode");
            return (Criteria) this;
        }

        public Criteria andGrpBankCodeLike(String value) {
            addCriterion("grpBankCode like", value, "grpBankCode");
            return (Criteria) this;
        }

        public Criteria andGrpBankCodeNotLike(String value) {
            addCriterion("grpBankCode not like", value, "grpBankCode");
            return (Criteria) this;
        }

        public Criteria andGrpBankCodeIn(List<String> values) {
            addCriterion("grpBankCode in", values, "grpBankCode");
            return (Criteria) this;
        }

        public Criteria andGrpBankCodeNotIn(List<String> values) {
            addCriterion("grpBankCode not in", values, "grpBankCode");
            return (Criteria) this;
        }

        public Criteria andGrpBankCodeBetween(String value1, String value2) {
            addCriterion("grpBankCode between", value1, value2, "grpBankCode");
            return (Criteria) this;
        }

        public Criteria andGrpBankCodeNotBetween(String value1, String value2) {
            addCriterion("grpBankCode not between", value1, value2, "grpBankCode");
            return (Criteria) this;
        }

        public Criteria andGrpBankAccNoIsNull() {
            addCriterion("grpBankAccNo is null");
            return (Criteria) this;
        }

        public Criteria andGrpBankAccNoIsNotNull() {
            addCriterion("grpBankAccNo is not null");
            return (Criteria) this;
        }

        public Criteria andGrpBankAccNoEqualTo(String value) {
            addCriterion("grpBankAccNo =", value, "grpBankAccNo");
            return (Criteria) this;
        }

        public Criteria andGrpBankAccNoNotEqualTo(String value) {
            addCriterion("grpBankAccNo <>", value, "grpBankAccNo");
            return (Criteria) this;
        }

        public Criteria andGrpBankAccNoGreaterThan(String value) {
            addCriterion("grpBankAccNo >", value, "grpBankAccNo");
            return (Criteria) this;
        }

        public Criteria andGrpBankAccNoGreaterThanOrEqualTo(String value) {
            addCriterion("grpBankAccNo >=", value, "grpBankAccNo");
            return (Criteria) this;
        }

        public Criteria andGrpBankAccNoLessThan(String value) {
            addCriterion("grpBankAccNo <", value, "grpBankAccNo");
            return (Criteria) this;
        }

        public Criteria andGrpBankAccNoLessThanOrEqualTo(String value) {
            addCriterion("grpBankAccNo <=", value, "grpBankAccNo");
            return (Criteria) this;
        }

        public Criteria andGrpBankAccNoLike(String value) {
            addCriterion("grpBankAccNo like", value, "grpBankAccNo");
            return (Criteria) this;
        }

        public Criteria andGrpBankAccNoNotLike(String value) {
            addCriterion("grpBankAccNo not like", value, "grpBankAccNo");
            return (Criteria) this;
        }

        public Criteria andGrpBankAccNoIn(List<String> values) {
            addCriterion("grpBankAccNo in", values, "grpBankAccNo");
            return (Criteria) this;
        }

        public Criteria andGrpBankAccNoNotIn(List<String> values) {
            addCriterion("grpBankAccNo not in", values, "grpBankAccNo");
            return (Criteria) this;
        }

        public Criteria andGrpBankAccNoBetween(String value1, String value2) {
            addCriterion("grpBankAccNo between", value1, value2, "grpBankAccNo");
            return (Criteria) this;
        }

        public Criteria andGrpBankAccNoNotBetween(String value1, String value2) {
            addCriterion("grpBankAccNo not between", value1, value2, "grpBankAccNo");
            return (Criteria) this;
        }

        public Criteria andPeoplesIsNull() {
            addCriterion("Peoples is null");
            return (Criteria) this;
        }

        public Criteria andPeoplesIsNotNull() {
            addCriterion("Peoples is not null");
            return (Criteria) this;
        }

        public Criteria andPeoplesEqualTo(Integer value) {
            addCriterion("Peoples =", value, "peoples");
            return (Criteria) this;
        }

        public Criteria andPeoplesNotEqualTo(Integer value) {
            addCriterion("Peoples <>", value, "peoples");
            return (Criteria) this;
        }

        public Criteria andPeoplesGreaterThan(Integer value) {
            addCriterion("Peoples >", value, "peoples");
            return (Criteria) this;
        }

        public Criteria andPeoplesGreaterThanOrEqualTo(Integer value) {
            addCriterion("Peoples >=", value, "peoples");
            return (Criteria) this;
        }

        public Criteria andPeoplesLessThan(Integer value) {
            addCriterion("Peoples <", value, "peoples");
            return (Criteria) this;
        }

        public Criteria andPeoplesLessThanOrEqualTo(Integer value) {
            addCriterion("Peoples <=", value, "peoples");
            return (Criteria) this;
        }

        public Criteria andPeoplesIn(List<Integer> values) {
            addCriterion("Peoples in", values, "peoples");
            return (Criteria) this;
        }

        public Criteria andPeoplesNotIn(List<Integer> values) {
            addCriterion("Peoples not in", values, "peoples");
            return (Criteria) this;
        }

        public Criteria andPeoplesBetween(Integer value1, Integer value2) {
            addCriterion("Peoples between", value1, value2, "peoples");
            return (Criteria) this;
        }

        public Criteria andPeoplesNotBetween(Integer value1, Integer value2) {
            addCriterion("Peoples not between", value1, value2, "peoples");
            return (Criteria) this;
        }

        public Criteria andCorporationManIsNull() {
            addCriterion("CorporationMan is null");
            return (Criteria) this;
        }

        public Criteria andCorporationManIsNotNull() {
            addCriterion("CorporationMan is not null");
            return (Criteria) this;
        }

        public Criteria andCorporationManEqualTo(String value) {
            addCriterion("CorporationMan =", value, "corporationMan");
            return (Criteria) this;
        }

        public Criteria andCorporationManNotEqualTo(String value) {
            addCriterion("CorporationMan <>", value, "corporationMan");
            return (Criteria) this;
        }

        public Criteria andCorporationManGreaterThan(String value) {
            addCriterion("CorporationMan >", value, "corporationMan");
            return (Criteria) this;
        }

        public Criteria andCorporationManGreaterThanOrEqualTo(String value) {
            addCriterion("CorporationMan >=", value, "corporationMan");
            return (Criteria) this;
        }

        public Criteria andCorporationManLessThan(String value) {
            addCriterion("CorporationMan <", value, "corporationMan");
            return (Criteria) this;
        }

        public Criteria andCorporationManLessThanOrEqualTo(String value) {
            addCriterion("CorporationMan <=", value, "corporationMan");
            return (Criteria) this;
        }

        public Criteria andCorporationManLike(String value) {
            addCriterion("CorporationMan like", value, "corporationMan");
            return (Criteria) this;
        }

        public Criteria andCorporationManNotLike(String value) {
            addCriterion("CorporationMan not like", value, "corporationMan");
            return (Criteria) this;
        }

        public Criteria andCorporationManIn(List<String> values) {
            addCriterion("CorporationMan in", values, "corporationMan");
            return (Criteria) this;
        }

        public Criteria andCorporationManNotIn(List<String> values) {
            addCriterion("CorporationMan not in", values, "corporationMan");
            return (Criteria) this;
        }

        public Criteria andCorporationManBetween(String value1, String value2) {
            addCriterion("CorporationMan between", value1, value2, "corporationMan");
            return (Criteria) this;
        }

        public Criteria andCorporationManNotBetween(String value1, String value2) {
            addCriterion("CorporationMan not between", value1, value2, "corporationMan");
            return (Criteria) this;
        }

        public Criteria andTelphoneIsNull() {
            addCriterion("telphone is null");
            return (Criteria) this;
        }

        public Criteria andTelphoneIsNotNull() {
            addCriterion("telphone is not null");
            return (Criteria) this;
        }

        public Criteria andTelphoneEqualTo(String value) {
            addCriterion("telphone =", value, "telphone");
            return (Criteria) this;
        }

        public Criteria andTelphoneNotEqualTo(String value) {
            addCriterion("telphone <>", value, "telphone");
            return (Criteria) this;
        }

        public Criteria andTelphoneGreaterThan(String value) {
            addCriterion("telphone >", value, "telphone");
            return (Criteria) this;
        }

        public Criteria andTelphoneGreaterThanOrEqualTo(String value) {
            addCriterion("telphone >=", value, "telphone");
            return (Criteria) this;
        }

        public Criteria andTelphoneLessThan(String value) {
            addCriterion("telphone <", value, "telphone");
            return (Criteria) this;
        }

        public Criteria andTelphoneLessThanOrEqualTo(String value) {
            addCriterion("telphone <=", value, "telphone");
            return (Criteria) this;
        }

        public Criteria andTelphoneLike(String value) {
            addCriterion("telphone like", value, "telphone");
            return (Criteria) this;
        }

        public Criteria andTelphoneNotLike(String value) {
            addCriterion("telphone not like", value, "telphone");
            return (Criteria) this;
        }

        public Criteria andTelphoneIn(List<String> values) {
            addCriterion("telphone in", values, "telphone");
            return (Criteria) this;
        }

        public Criteria andTelphoneNotIn(List<String> values) {
            addCriterion("telphone not in", values, "telphone");
            return (Criteria) this;
        }

        public Criteria andTelphoneBetween(String value1, String value2) {
            addCriterion("telphone between", value1, value2, "telphone");
            return (Criteria) this;
        }

        public Criteria andTelphoneNotBetween(String value1, String value2) {
            addCriterion("telphone not between", value1, value2, "telphone");
            return (Criteria) this;
        }

        public Criteria andRegaddressIsNull() {
            addCriterion("regaddress is null");
            return (Criteria) this;
        }

        public Criteria andRegaddressIsNotNull() {
            addCriterion("regaddress is not null");
            return (Criteria) this;
        }

        public Criteria andRegaddressEqualTo(String value) {
            addCriterion("regaddress =", value, "regaddress");
            return (Criteria) this;
        }

        public Criteria andRegaddressNotEqualTo(String value) {
            addCriterion("regaddress <>", value, "regaddress");
            return (Criteria) this;
        }

        public Criteria andRegaddressGreaterThan(String value) {
            addCriterion("regaddress >", value, "regaddress");
            return (Criteria) this;
        }

        public Criteria andRegaddressGreaterThanOrEqualTo(String value) {
            addCriterion("regaddress >=", value, "regaddress");
            return (Criteria) this;
        }

        public Criteria andRegaddressLessThan(String value) {
            addCriterion("regaddress <", value, "regaddress");
            return (Criteria) this;
        }

        public Criteria andRegaddressLessThanOrEqualTo(String value) {
            addCriterion("regaddress <=", value, "regaddress");
            return (Criteria) this;
        }

        public Criteria andRegaddressLike(String value) {
            addCriterion("regaddress like", value, "regaddress");
            return (Criteria) this;
        }

        public Criteria andRegaddressNotLike(String value) {
            addCriterion("regaddress not like", value, "regaddress");
            return (Criteria) this;
        }

        public Criteria andRegaddressIn(List<String> values) {
            addCriterion("regaddress in", values, "regaddress");
            return (Criteria) this;
        }

        public Criteria andRegaddressNotIn(List<String> values) {
            addCriterion("regaddress not in", values, "regaddress");
            return (Criteria) this;
        }

        public Criteria andRegaddressBetween(String value1, String value2) {
            addCriterion("regaddress between", value1, value2, "regaddress");
            return (Criteria) this;
        }

        public Criteria andRegaddressNotBetween(String value1, String value2) {
            addCriterion("regaddress not between", value1, value2, "regaddress");
            return (Criteria) this;
        }

        public Criteria andEmailIsNull() {
            addCriterion("Email is null");
            return (Criteria) this;
        }

        public Criteria andEmailIsNotNull() {
            addCriterion("Email is not null");
            return (Criteria) this;
        }

        public Criteria andEmailEqualTo(String value) {
            addCriterion("Email =", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotEqualTo(String value) {
            addCriterion("Email <>", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThan(String value) {
            addCriterion("Email >", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThanOrEqualTo(String value) {
            addCriterion("Email >=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThan(String value) {
            addCriterion("Email <", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThanOrEqualTo(String value) {
            addCriterion("Email <=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLike(String value) {
            addCriterion("Email like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotLike(String value) {
            addCriterion("Email not like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailIn(List<String> values) {
            addCriterion("Email in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotIn(List<String> values) {
            addCriterion("Email not in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailBetween(String value1, String value2) {
            addCriterion("Email between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotBetween(String value1, String value2) {
            addCriterion("Email not between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andClientnoIsNull() {
            addCriterion("Clientno is null");
            return (Criteria) this;
        }

        public Criteria andClientnoIsNotNull() {
            addCriterion("Clientno is not null");
            return (Criteria) this;
        }

        public Criteria andClientnoEqualTo(String value) {
            addCriterion("Clientno =", value, "clientno");
            return (Criteria) this;
        }

        public Criteria andClientnoNotEqualTo(String value) {
            addCriterion("Clientno <>", value, "clientno");
            return (Criteria) this;
        }

        public Criteria andClientnoGreaterThan(String value) {
            addCriterion("Clientno >", value, "clientno");
            return (Criteria) this;
        }

        public Criteria andClientnoGreaterThanOrEqualTo(String value) {
            addCriterion("Clientno >=", value, "clientno");
            return (Criteria) this;
        }

        public Criteria andClientnoLessThan(String value) {
            addCriterion("Clientno <", value, "clientno");
            return (Criteria) this;
        }

        public Criteria andClientnoLessThanOrEqualTo(String value) {
            addCriterion("Clientno <=", value, "clientno");
            return (Criteria) this;
        }

        public Criteria andClientnoLike(String value) {
            addCriterion("Clientno like", value, "clientno");
            return (Criteria) this;
        }

        public Criteria andClientnoNotLike(String value) {
            addCriterion("Clientno not like", value, "clientno");
            return (Criteria) this;
        }

        public Criteria andClientnoIn(List<String> values) {
            addCriterion("Clientno in", values, "clientno");
            return (Criteria) this;
        }

        public Criteria andClientnoNotIn(List<String> values) {
            addCriterion("Clientno not in", values, "clientno");
            return (Criteria) this;
        }

        public Criteria andClientnoBetween(String value1, String value2) {
            addCriterion("Clientno between", value1, value2, "clientno");
            return (Criteria) this;
        }

        public Criteria andClientnoNotBetween(String value1, String value2) {
            addCriterion("Clientno not between", value1, value2, "clientno");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNull() {
            addCriterion("Operator is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNotNull() {
            addCriterion("Operator is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorEqualTo(String value) {
            addCriterion("Operator =", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotEqualTo(String value) {
            addCriterion("Operator <>", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThan(String value) {
            addCriterion("Operator >", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThanOrEqualTo(String value) {
            addCriterion("Operator >=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThan(String value) {
            addCriterion("Operator <", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThanOrEqualTo(String value) {
            addCriterion("Operator <=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLike(String value) {
            addCriterion("Operator like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotLike(String value) {
            addCriterion("Operator not like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorIn(List<String> values) {
            addCriterion("Operator in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotIn(List<String> values) {
            addCriterion("Operator not in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorBetween(String value1, String value2) {
            addCriterion("Operator between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotBetween(String value1, String value2) {
            addCriterion("Operator not between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorComIsNull() {
            addCriterion("OperatorCom is null");
            return (Criteria) this;
        }

        public Criteria andOperatorComIsNotNull() {
            addCriterion("OperatorCom is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorComEqualTo(String value) {
            addCriterion("OperatorCom =", value, "operatorCom");
            return (Criteria) this;
        }

        public Criteria andOperatorComNotEqualTo(String value) {
            addCriterion("OperatorCom <>", value, "operatorCom");
            return (Criteria) this;
        }

        public Criteria andOperatorComGreaterThan(String value) {
            addCriterion("OperatorCom >", value, "operatorCom");
            return (Criteria) this;
        }

        public Criteria andOperatorComGreaterThanOrEqualTo(String value) {
            addCriterion("OperatorCom >=", value, "operatorCom");
            return (Criteria) this;
        }

        public Criteria andOperatorComLessThan(String value) {
            addCriterion("OperatorCom <", value, "operatorCom");
            return (Criteria) this;
        }

        public Criteria andOperatorComLessThanOrEqualTo(String value) {
            addCriterion("OperatorCom <=", value, "operatorCom");
            return (Criteria) this;
        }

        public Criteria andOperatorComLike(String value) {
            addCriterion("OperatorCom like", value, "operatorCom");
            return (Criteria) this;
        }

        public Criteria andOperatorComNotLike(String value) {
            addCriterion("OperatorCom not like", value, "operatorCom");
            return (Criteria) this;
        }

        public Criteria andOperatorComIn(List<String> values) {
            addCriterion("OperatorCom in", values, "operatorCom");
            return (Criteria) this;
        }

        public Criteria andOperatorComNotIn(List<String> values) {
            addCriterion("OperatorCom not in", values, "operatorCom");
            return (Criteria) this;
        }

        public Criteria andOperatorComBetween(String value1, String value2) {
            addCriterion("OperatorCom between", value1, value2, "operatorCom");
            return (Criteria) this;
        }

        public Criteria andOperatorComNotBetween(String value1, String value2) {
            addCriterion("OperatorCom not between", value1, value2, "operatorCom");
            return (Criteria) this;
        }

        public Criteria andMakeDateIsNull() {
            addCriterion("MakeDate is null");
            return (Criteria) this;
        }

        public Criteria andMakeDateIsNotNull() {
            addCriterion("MakeDate is not null");
            return (Criteria) this;
        }

        public Criteria andMakeDateEqualTo(Date value) {
            addCriterionForJDBCDate("MakeDate =", value, "makeDate");
            return (Criteria) this;
        }

        public Criteria andMakeDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("MakeDate <>", value, "makeDate");
            return (Criteria) this;
        }

        public Criteria andMakeDateGreaterThan(Date value) {
            addCriterionForJDBCDate("MakeDate >", value, "makeDate");
            return (Criteria) this;
        }

        public Criteria andMakeDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("MakeDate >=", value, "makeDate");
            return (Criteria) this;
        }

        public Criteria andMakeDateLessThan(Date value) {
            addCriterionForJDBCDate("MakeDate <", value, "makeDate");
            return (Criteria) this;
        }

        public Criteria andMakeDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("MakeDate <=", value, "makeDate");
            return (Criteria) this;
        }

        public Criteria andMakeDateIn(List<Date> values) {
            addCriterionForJDBCDate("MakeDate in", values, "makeDate");
            return (Criteria) this;
        }

        public Criteria andMakeDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("MakeDate not in", values, "makeDate");
            return (Criteria) this;
        }

        public Criteria andMakeDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("MakeDate between", value1, value2, "makeDate");
            return (Criteria) this;
        }

        public Criteria andMakeDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("MakeDate not between", value1, value2, "makeDate");
            return (Criteria) this;
        }

        public Criteria andMakeTimeIsNull() {
            addCriterion("MakeTime is null");
            return (Criteria) this;
        }

        public Criteria andMakeTimeIsNotNull() {
            addCriterion("MakeTime is not null");
            return (Criteria) this;
        }

        public Criteria andMakeTimeEqualTo(String value) {
            addCriterion("MakeTime =", value, "makeTime");
            return (Criteria) this;
        }

        public Criteria andMakeTimeNotEqualTo(String value) {
            addCriterion("MakeTime <>", value, "makeTime");
            return (Criteria) this;
        }

        public Criteria andMakeTimeGreaterThan(String value) {
            addCriterion("MakeTime >", value, "makeTime");
            return (Criteria) this;
        }

        public Criteria andMakeTimeGreaterThanOrEqualTo(String value) {
            addCriterion("MakeTime >=", value, "makeTime");
            return (Criteria) this;
        }

        public Criteria andMakeTimeLessThan(String value) {
            addCriterion("MakeTime <", value, "makeTime");
            return (Criteria) this;
        }

        public Criteria andMakeTimeLessThanOrEqualTo(String value) {
            addCriterion("MakeTime <=", value, "makeTime");
            return (Criteria) this;
        }

        public Criteria andMakeTimeLike(String value) {
            addCriterion("MakeTime like", value, "makeTime");
            return (Criteria) this;
        }

        public Criteria andMakeTimeNotLike(String value) {
            addCriterion("MakeTime not like", value, "makeTime");
            return (Criteria) this;
        }

        public Criteria andMakeTimeIn(List<String> values) {
            addCriterion("MakeTime in", values, "makeTime");
            return (Criteria) this;
        }

        public Criteria andMakeTimeNotIn(List<String> values) {
            addCriterion("MakeTime not in", values, "makeTime");
            return (Criteria) this;
        }

        public Criteria andMakeTimeBetween(String value1, String value2) {
            addCriterion("MakeTime between", value1, value2, "makeTime");
            return (Criteria) this;
        }

        public Criteria andMakeTimeNotBetween(String value1, String value2) {
            addCriterion("MakeTime not between", value1, value2, "makeTime");
            return (Criteria) this;
        }

        public Criteria andModifyDateIsNull() {
            addCriterion("ModifyDate is null");
            return (Criteria) this;
        }

        public Criteria andModifyDateIsNotNull() {
            addCriterion("ModifyDate is not null");
            return (Criteria) this;
        }

        public Criteria andModifyDateEqualTo(Date value) {
            addCriterionForJDBCDate("ModifyDate =", value, "modifyDate");
            return (Criteria) this;
        }

        public Criteria andModifyDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("ModifyDate <>", value, "modifyDate");
            return (Criteria) this;
        }

        public Criteria andModifyDateGreaterThan(Date value) {
            addCriterionForJDBCDate("ModifyDate >", value, "modifyDate");
            return (Criteria) this;
        }

        public Criteria andModifyDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("ModifyDate >=", value, "modifyDate");
            return (Criteria) this;
        }

        public Criteria andModifyDateLessThan(Date value) {
            addCriterionForJDBCDate("ModifyDate <", value, "modifyDate");
            return (Criteria) this;
        }

        public Criteria andModifyDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("ModifyDate <=", value, "modifyDate");
            return (Criteria) this;
        }

        public Criteria andModifyDateIn(List<Date> values) {
            addCriterionForJDBCDate("ModifyDate in", values, "modifyDate");
            return (Criteria) this;
        }

        public Criteria andModifyDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("ModifyDate not in", values, "modifyDate");
            return (Criteria) this;
        }

        public Criteria andModifyDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("ModifyDate between", value1, value2, "modifyDate");
            return (Criteria) this;
        }

        public Criteria andModifyDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("ModifyDate not between", value1, value2, "modifyDate");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("ModifyTime is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("ModifyTime is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(String value) {
            addCriterion("ModifyTime =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(String value) {
            addCriterion("ModifyTime <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(String value) {
            addCriterion("ModifyTime >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(String value) {
            addCriterion("ModifyTime >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(String value) {
            addCriterion("ModifyTime <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(String value) {
            addCriterion("ModifyTime <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLike(String value) {
            addCriterion("ModifyTime like", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotLike(String value) {
            addCriterion("ModifyTime not like", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<String> values) {
            addCriterion("ModifyTime in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<String> values) {
            addCriterion("ModifyTime not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(String value1, String value2) {
            addCriterion("ModifyTime between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(String value1, String value2) {
            addCriterion("ModifyTime not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    /**
     * fcgrpinfo
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * fcgrpinfo null
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}