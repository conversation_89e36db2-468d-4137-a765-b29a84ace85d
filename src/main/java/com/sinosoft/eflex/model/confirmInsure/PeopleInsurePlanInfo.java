package com.sinosoft.eflex.model.confirmInsure;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2021/3/31 请求报文
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PeopleInsurePlanInfo {

    // 人员姓名
    private String name;

    // 人员ID
    private String personId;

    // 投保计划
    private String planCode;

    //grpNo
    private String grpNo;

    private String perNo;
    /**
     * 订单号
     */
    private String ensureCode;





}
