package com.sinosoft.eflex.model.confirmInsure;

import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/12/14
 * @desc 固定计划确认提交请求参数
 */
@Data
public class InsureConfirmSubmitReq implements Serializable {

    // 签约短信验证码
    private String verifyCode;

    // 是否校验付款信息
    private String isCheck;

    // 订单来源
    private String orderSource;

    // 订单号
    private String orderNo;

    private List<PeopleInsurePlanInfo> peopleInsurePlanInfos;
    /**
     * 签名
     */
    private String signatureFile;

}
