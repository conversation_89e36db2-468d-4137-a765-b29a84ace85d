package com.sinosoft.eflex.model.confirmInsure;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2021/4/1 未成年人投保确认信息，仅展示需要确认的未成年人信息
 */
@Data
public class JuvenilesConfirmationInfo {

    // 人员ID
    private String personId;

    // 计划编码
    private String planCode;

    // 员工姓名
    private String staffName;

    // 员工证件号
    private String staffIDNo;

    // 被保人姓名
    private String insuredName;

    // 关系
    private String relation;

    // 身故保险金
    private Double deathAmnt;

    // 累计风险保额
    private Double deathAmntCount;

    //
    private String year;

    //
    private String month;

    //
    private String day;

}
