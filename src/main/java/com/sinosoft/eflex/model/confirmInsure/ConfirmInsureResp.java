package com.sinosoft.eflex.model.confirmInsure;

import java.util.List;

import com.sinosoft.eflex.model.FCPerInfo;
import com.sinosoft.eflex.model.confirmInsureEflex.EflexTotalInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2021/4/1 投保确认返回信息
 */
@Data
public class ConfirmInsureResp {

    //订单号
    private String orderNo;

    // 是否需要支付 0-不需要支付 1-需要支付
    private String isSelfPay;

    // 判断需要展示员工之前录入的银行信息
    private String isCheck;

    // 员工付款信息
    private FCPerInfo perInfo;

    // 投保汇总信息
    private EflexTotalInfo totalInfo;


    // 已投保人员信息
    private List<PeopleInsureInfo> peopleInsureInfoList;

    // 个人健康告知确认信息
    private List<HealthNoticeInfo> healthNoticeList;

    // 未成年人投保确认信息
    private List<JuvenilesConfirmationInfo> juvenilesConfirmationInfoList;

    // 校验规则信息
    private CheckTBResultInfo checkTBResultInfo;

}
