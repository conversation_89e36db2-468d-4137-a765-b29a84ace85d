package com.sinosoft.eflex.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FCEdorAddInsured {
    /**
     * 被保人流水编号
     */
    private String plusInsuredSN;

    private String relationship;

    /**
     * 保全编号
     */
    private String grpContNo;

    /**
     * 企业编号
     */
    private String grpNo;

    /**
     * 批次
     */
    private String batch;

    /**
     * 被保险人姓名
     */
    private String name;

    /**
     * 国籍
     */
    private String nativeplace;

    /**
     * 出生日期
     */
    private String birthday;

    /**
     * 性别
     */
    private String sex;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 证件有效期
     */
    private String idTypeEndDate;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 与被保人关系
     */
    private String relationToAppnt;

    /**
     * 增人生效日期
     */
    private String plusEffectDate;

    /**
     * 保险期间
     */
    private String insuYear;

    /**
     * 保险期间类型
     */
    private String insuYearFlag;

    /**
     * 计划编码
     */
    private String planCode;

    /**
     * 是否拥有社保
     */
    private String medicareStatus;

    /**
     * 保全类型
     */
    private String edorType;

    /**
     * 职业类别
     */
    private String jobType;

    /**
     * 职业代码
     */
    private String jobCode;

    /**
     * 主被保人姓名
     */
    private String staffName;

    /**
     * 主被保人证件号
     */
    private String mainIdNo;

    /**
     * 主被保人证件类型
     */
    private String mainIdType;

    /**
     * 与主被保险人关系
     */
    private String relation;

    /**
     * 缴费方式
     */
    private String payMethod;

    /**
     * 公司缴费
     */
    private Double comPayment;

    /**
     * 个人缴费
     */
    private Double perPayment;

    /**
     * 扣款缴费银行
     */
    private String debitPayBank;

    /**
     * 扣款缴费银行账号
     */
    private String debitPayCode;

    /**
     * 扣款缴费帐户名
     */
    private String debitPayName;

    /**
     * 受益人姓名
     */
    private String deathBenefiName;

    /**
     * 受益人与被保人关系
     */
    private String deathBenefiRelation;

    /**
     * 被保人签字
     */
    private String autoInsured;

    /**
     * 核心人员身份
     */
    private String subsidiaryInsuredFlag;

    /**
     * 提交试算状态0-未提交1-已提交
     */
    private String trialStatus;

    /**
     * 是否有误
     */
    private String isError;

    /**
     * 错误描述
     */
    private String errorDesc;

    /**
     * 保费来源
     */
    private String premSource;

    /**
     * 结算类型
     */
    private String accountType;

    /**
     * 试算保费
     */
    private String prem;

    /**
     * 分单号
     */
    private String contNo;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 已投保身故保额总和（航空险除外）
     */
    private Double deathBenefiPrem;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 生成日期
     */
    private String makeDate;

    /**
     * 生成时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 被保人流水编号
     * @return PlusInsuredSN 被保人流水编号
     */
    public String getPlusInsuredSN() {
        return plusInsuredSN;
    }

    /**
     * 被保人流水编号
     * @param plusInsuredSN 被保人流水编号
     */
    public void setPlusInsuredSN(String plusInsuredSN) {
        this.plusInsuredSN = plusInsuredSN;
    }

    /**
     * 保全编号
     * @return GrpContNo 保全编号
     */
    public String getGrpContNo() {
        return grpContNo;
    }

    /**
     * 保全编号
     * @param grpContNo 保全编号
     */
    public void setGrpContNo(String grpContNo) {
        this.grpContNo = grpContNo;
    }

    /**
     * 企业编号
     * @return GrpNo 企业编号
     */
    public String getGrpNo() {
        return grpNo;
    }

    /**
     * 企业编号
     * @param grpNo 企业编号
     */
    public void setGrpNo(String grpNo) {
        this.grpNo = grpNo;
    }

    /**
     * 批次
     * @return Batch 批次
     */
    public String getBatch() {
        return batch;
    }

    /**
     * 批次
     *
     * @param batch 批次
     */
    public void setBatch(String batch) {
        this.batch = batch;
    }

    /**
     * 被保险人姓名
     * @return Name 被保险人姓名
     */
    public String getName() {
        return name;
    }

    /**
     * 被保险人姓名
     * @param name 被保险人姓名
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 国籍
     * @return Nativeplace 国籍
     */
    public String getNativeplace() {
        return nativeplace;
    }

    /**
     * 国籍
     * @param nativeplace 国籍
     */
    public void setNativeplace(String nativeplace) {
        this.nativeplace = nativeplace;
    }

    /**
     * 出生日期
     * @return Birthday 出生日期
     */
    public String getBirthday() {
        return birthday;
    }

    /**
     * 出生日期
     * @param birthday 出生日期
     */
    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    /**
     * 性别
     * @return Sex 性别
     */
    public String getSex() {
        return sex;
    }

    /**
     * 性别
     * @param sex 性别
     */
    public void setSex(String sex) {
        this.sex = sex;
    }

    /**
     * 证件类型
     * @return IdType 证件类型
     */
    public String getIdType() {
        return idType;
    }

    /**
     * 证件类型
     * @param idType 证件类型
     */
    public void setIdType(String idType) {
        this.idType = idType;
    }

    /**
     * 证件号
     * @return IdNo 证件号
     */
    public String getIdNo() {
        return idNo;
    }

    /**
     * 证件号
     * @param idNo 证件号
     */
    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    /**
     * 证件有效期
     * @return idTypeEndDate 证件有效期
     */
    public String getIdTypeEndDate() {
        return idTypeEndDate;
    }

    /**
     * 证件有效期
     * @param idTypeEndDate 证件有效期
     */
    public void setIdTypeEndDate(String idTypeEndDate) {
        this.idTypeEndDate = idTypeEndDate;
    }

    /**
     * 手机号
     * @return mobile 手机号
     */
    public String getMobile() {
        return mobile;
    }

    /**
     * 手机号
     * @param mobile 手机号
     */
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    /**
     * 与被保人关系
     * @return RelationToAppnt 与被保人关系
     */
    public String getRelationToAppnt() {
        return relationToAppnt;
    }

    /**
     * 与被保人关系
     * @param relationToAppnt 与被保人关系
     */
    public void setRelationToAppnt(String relationToAppnt) {
        this.relationToAppnt = relationToAppnt;
    }

    /**
     * 增人生效日期
     * @return PlusEffectDate 增人生效日期
     */
    public String getPlusEffectDate() {
        return plusEffectDate;
    }

    /**
     * 增人生效日期
     *
     * @param plusEffectDate 增人生效日期
     */
    public void setPlusEffectDate(String plusEffectDate) {
        this.plusEffectDate = plusEffectDate;
    }

    /**
     * 保险期间
     *
     * @return InsuYear 保险期间
     */
    public String getInsuYear() {
        return insuYear;
    }

    /**
     * 保险期间
     *
     * @param insuYear 保险期间
     */
    public void setInsuYear(String insuYear) {
        this.insuYear = insuYear;
    }

    /**
     * 保险期间类型
     *
     * @return InsuYearFlag 保险期间类型
     */
    public String getInsuYearFlag() {
        return insuYearFlag;
    }

    /**
     * 保险期间类型
     *
     * @param insuYearFlag 保险期间类型
     */
    public void setInsuYearFlag(String insuYearFlag) {
        this.insuYearFlag = insuYearFlag;
    }

    /**
     * 计划编码
     *
     * @return PlanCode 计划编码
     */
    public String getPlanCode() {
        return planCode;
    }

    /**
     * 计划编码
     * @param planCode 计划编码
     */
    public void setPlanCode(String planCode) {
        this.planCode = planCode;
    }

    /**
     * 是否拥有社保
     * @return MedicareStatus 是否拥有社保
     */
    public String getMedicareStatus() {
        return medicareStatus;
    }

    /**
     * 是否拥有社保
     * @param medicareStatus 是否拥有社保
     */
    public void setMedicareStatus(String medicareStatus) {
        this.medicareStatus = medicareStatus;
    }

    /**
     * 保全类型
     * @return EdorType 保全类型
     */
    public String getEdorType() {
        return edorType;
    }

    /**
     * 保全类型
     * @param edorType 保全类型
     */
    public void setEdorType(String edorType) {
        this.edorType = edorType;
    }

    /**
     * 职业类别
     * @return JobType 职业类别
     */
    public String getJobType() {
        return jobType;
    }

    /**
     * 职业类别
     * @param jobType 职业类别
     */
    public void setJobType(String jobType) {
        this.jobType = jobType;
    }

    /**
     * 职业代码
     * @return JobCode 职业代码
     */
    public String getJobCode() {
        return jobCode;
    }

    /**
     * 职业代码
     * @param jobCode 职业代码
     */
    public void setJobCode(String jobCode) {
        this.jobCode = jobCode;
    }

    /**
     * 主被保人姓名
     * @return staffName 主被保人姓名
     */
    public String getStaffName() {
        return staffName;
    }

    /**
     * 主被保人姓名
     * @param staffName 主被保人姓名
     */
    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    /**
     * 主被保人证件号
     * @return MainIdNo 主被保人证件号
     */
    public String getMainIdNo() {
        return mainIdNo;
    }

    /**
     * 主被保人证件号
     * @param mainIdNo 主被保人证件号
     */
    public void setMainIdNo(String mainIdNo) {
        this.mainIdNo = mainIdNo;
    }

    /**
     * 主被保人证件类型
     * @return MainIdType 主被保人证件类型
     */
    public String getMainIdType() {
        return mainIdType;
    }

    /**
     * 主被保人证件类型
     * @param mainIdType 主被保人证件类型
     */
    public void setMainIdType(String mainIdType) {
        this.mainIdType = mainIdType;
    }

    /**
     * 与主被保险人关系
     * @return relation 与主被保险人关系
     */
    public String getRelation() {
        return relation;
    }

    /**
     * 与主被保险人关系
     * @param relation 与主被保险人关系
     */
    public void setRelation(String relation) {
        this.relation = relation;
    }

    /**
     * 缴费方式
     * @return PayMethod 缴费方式
     */
    public String getPayMethod() {
        return payMethod;
    }

    /**
     * 缴费方式
     * @param payMethod 缴费方式
     */
    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    /**
     * 公司缴费
     * @return ComPayment 公司缴费
     */
    public Double getComPayment() {
        return comPayment;
    }

    /**
     * 公司缴费
     * @param comPayment 公司缴费
     */
    public void setComPayment(Double comPayment) {
        this.comPayment = comPayment;
    }

    /**
     * 个人缴费
     * @return PerPayment 个人缴费
     */
    public Double getPerPayment() {
        return perPayment;
    }

    /**
     * 个人缴费
     * @param perPayment 个人缴费
     */
    public void setPerPayment(Double perPayment) {
        this.perPayment = perPayment;
    }

    /**
     * 扣款缴费银行
     * @return DebitPayBank 扣款缴费银行
     */
    public String getDebitPayBank() {
        return debitPayBank;
    }

    /**
     * 扣款缴费银行
     * @param debitPayBank 扣款缴费银行
     */
    public void setDebitPayBank(String debitPayBank) {
        this.debitPayBank = debitPayBank;
    }

    /**
     * 扣款缴费银行账号
     * @return DebitPayCode 扣款缴费银行账号
     */
    public String getDebitPayCode() {
        return debitPayCode;
    }

    /**
     * 扣款缴费银行账号
     * @param debitPayCode 扣款缴费银行账号
     */
    public void setDebitPayCode(String debitPayCode) {
        this.debitPayCode = debitPayCode;
    }

    /**
     * 扣款缴费帐户名
     * @return DebitPayName 扣款缴费帐户名
     */
    public String getDebitPayName() {
        return debitPayName;
    }

    /**
     * 扣款缴费帐户名
     * @param debitPayName 扣款缴费帐户名
     */
    public void setDebitPayName(String debitPayName) {
        this.debitPayName = debitPayName;
    }

    /**
     * 受益人姓名
     * @return DeathBenefiName 受益人姓名
     */
    public String getDeathBenefiName() {
        return deathBenefiName;
    }

    /**
     * 受益人姓名
     * @param deathBenefiName 受益人姓名
     */
    public void setDeathBenefiName(String deathBenefiName) {
        this.deathBenefiName = deathBenefiName;
    }

    /**
     * 受益人与被保人关系
     * @return DeathBenefiRelation 受益人与被保人关系
     */
    public String getDeathBenefiRelation() {
        return deathBenefiRelation;
    }

    /**
     * 受益人与被保人关系
     * @param deathBenefiRelation 受益人与被保人关系
     */
    public void setDeathBenefiRelation(String deathBenefiRelation) {
        this.deathBenefiRelation = deathBenefiRelation;
    }

    /**
     * 被保人签字
     * @return AutoInsured 被保人签字
     */
    public String getAutoInsured() {
        return autoInsured;
    }

    /**
     * 被保人签字
     * @param autoInsured 被保人签字
     */
    public void setAutoInsured(String autoInsured) {
        this.autoInsured = autoInsured;
    }

    /**
     * 核心人员身份
     * @return subsidiaryInsuredFlag 核心人员身份
     */
    public String getSubsidiaryInsuredFlag() {
        return subsidiaryInsuredFlag;
    }

    /**
     * 核心人员身份
     * @param subsidiaryInsuredFlag 核心人员身份
     */
    public void setSubsidiaryInsuredFlag(String subsidiaryInsuredFlag) {
        this.subsidiaryInsuredFlag = subsidiaryInsuredFlag;
    }

    /**
     * 提交试算状态0-未提交1-已提交
     * @return TrialStatus 提交试算状态0-未提交1-已提交
     */
    public String getTrialStatus() {
        return trialStatus;
    }

    /**
     * 提交试算状态0-未提交1-已提交
     * @param trialStatus 提交试算状态0-未提交1-已提交
     */
    public void setTrialStatus(String trialStatus) {
        this.trialStatus = trialStatus;
    }

    /**
     * 是否有误
     * @return IsError 是否有误
     */
    public String getIsError() {
        return isError;
    }

    /**
     * 是否有误
     * @param isError 是否有误
     */
    public void setIsError(String isError) {
        this.isError = isError;
    }

    /**
     * 错误描述
     * @return ErrorDesc 错误描述
     */
    public String getErrorDesc() {
        return errorDesc;
    }

    /**
     * 错误描述
     * @param errorDesc 错误描述
     */
    public void setErrorDesc(String errorDesc) {
        this.errorDesc = errorDesc;
    }

    /**
     * 保费来源
     * @return PremSource 保费来源
     */
    public String getPremSource() {
        return premSource;
    }

    /**
     * 保费来源
     * @param premSource 保费来源
     */
    public void setPremSource(String premSource) {
        this.premSource = premSource;
    }

    /**
     * 结算类型
     * @return AccountType 结算类型
     */
    public String getAccountType() {
        return accountType;
    }

    /**
     * 结算类型
     * @param accountType 结算类型
     */
    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    /**
     * 试算保费
     * @return Prem 试算保费
     */
    public String getPrem() {
        return prem;
    }

    /**
     * 试算保费
     * @param prem 试算保费
     */
    public void setPrem(String prem) {
        this.prem = prem;
    }

    /**
     * 分单号
     * @return ContNo 分单号
     */
    public String getContNo() {
        return contNo;
    }

    /**
     * 分单号
     * @param contNo 分单号
     */
    public void setContNo(String contNo) {
        this.contNo = contNo;
    }

    /**
     * 备注
     * @return remarks 备注
     */
    public String getRemarks() {
        return remarks;
    }

    /**
     * 备注
     *
     * @param remarks 备注
     */
    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    /**
     * 已投保身故保额总和（航空险除外）
     *
     * @return DeathBenefiPrem 已投保身故保额总和（航空险除外）
     */
    public Double getDeathBenefiPrem() {
        return deathBenefiPrem;
    }

    /**
     * 已投保身故保额总和（航空险除外）
     *
     * @param deathBenefiPrem 已投保身故保额总和（航空险除外）
     */
    public void setDeathBenefiPrem(Double deathBenefiPrem) {
        this.deathBenefiPrem = deathBenefiPrem;
    }

    /**
     * 操作员
     *
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 操作机构
     * @return OperatorCom 操作机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作机构
     * @param operatorCom 操作机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 生成日期
     * @return MakeDate 生成日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 生成日期
     * @param makeDate 生成日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 生成时间
     * @return MakeTime 生成时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 生成时间
     * @param makeTime 生成时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}