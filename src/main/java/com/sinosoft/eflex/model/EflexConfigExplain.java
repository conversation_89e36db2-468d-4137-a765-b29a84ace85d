package com.sinosoft.eflex.model;

import com.sinosoft.eflex.model.insureEflexPlanPage.WaitingPeriodInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/3/26
 */
@Data
public class EflexConfigExplain {

    // 医疗机构
    private String medicalInstitution;

    // 特别约定
    private String specialAgreement;

    // 既往症
    private String anamnesis;

    // 其他约定
    private String otherConventions;

    // 等待期
    private List<WaitingPeriodInfo> waitingPeriodInfoList;

}