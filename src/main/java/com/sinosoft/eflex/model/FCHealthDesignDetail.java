package com.sinosoft.eflex.model;

import java.util.Date;

public class FCHealthDesignDetail {
    /**
     * 健康方案编号
     */
    private String healthDesignNo;

    /**
     * 险种编码
     */
    private String riskCode;

    /**
     * 职级上限
     */
    private String gradeLevelTopLimit;

    /**
     * 职级下限
     */
    private String gradeLevelLowLimit;

    /**
     * 保额上限
     */
    private Double amntTopLimit;

    /**
     * 保额下限
     */
    private Double amntLowLimit;

    /**
     * 年龄上限
     */
    private String ageTopLimit;

    /**
     * 年龄下限
     */
    private String ageLowLimit;

    /**
     * 性别
     */
    private String sex;

    /**
     * 被保人类别 01-本人  02-配偶  03-子女  04-父母
     */
    private String insuredType;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 管理机构
     */
    private String operatorCom;

    /**
     * 入机日期
     */
    private String makeDate;

    /**
     * 入机时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 健康方案编号
     * @return HealthDesignNo 健康方案编号
     */
    public String getHealthDesignNo() {
        return healthDesignNo;
    }

    /**
     * 健康方案编号
     * @param healthDesignNo 健康方案编号
     */
    public void setHealthDesignNo(String healthDesignNo) {
        this.healthDesignNo = healthDesignNo;
    }

    /**
     * 险种编码
     * @return RiskCode 险种编码
     */
    public String getRiskCode() {
        return riskCode;
    }

    /**
     * 险种编码
     * @param riskCode 险种编码
     */
    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    /**
     * 职级上限
     * @return GradeLevelTopLimit 职级上限
     */
    public String getGradeLevelTopLimit() {
        return gradeLevelTopLimit;
    }

    /**
     * 职级上限
     * @param gradeLevelTopLimit 职级上限
     */
    public void setGradeLevelTopLimit(String gradeLevelTopLimit) {
        this.gradeLevelTopLimit = gradeLevelTopLimit;
    }

    /**
     * 职级下限
     * @return GradeLevelLowLimit 职级下限
     */
    public String getGradeLevelLowLimit() {
        return gradeLevelLowLimit;
    }

    /**
     * 职级下限
     * @param gradeLevelLowLimit 职级下限
     */
    public void setGradeLevelLowLimit(String gradeLevelLowLimit) {
        this.gradeLevelLowLimit = gradeLevelLowLimit;
    }

    /**
     * 保额上限
     * @return AmntTopLimit 保额上限
     */
    public Double getAmntTopLimit() {
        return amntTopLimit;
    }

    /**
     * 保额上限
     * @param amntTopLimit 保额上限
     */
    public void setAmntTopLimit(Double amntTopLimit) {
        this.amntTopLimit = amntTopLimit;
    }

    /**
     * 保额下限
     * @return AmntLowLimit 保额下限
     */
    public Double getAmntLowLimit() {
        return amntLowLimit;
    }

    /**
     * 保额下限
     * @param amntLowLimit 保额下限
     */
    public void setAmntLowLimit(Double amntLowLimit) {
        this.amntLowLimit = amntLowLimit;
    }

    /**
     * 年龄上限
     * @return AgeTopLimit 年龄上限
     */
    public String getAgeTopLimit() {
        return ageTopLimit;
    }

    /**
     * 年龄上限
     * @param ageTopLimit 年龄上限
     */
    public void setAgeTopLimit(String ageTopLimit) {
        this.ageTopLimit = ageTopLimit;
    }

    /**
     * 年龄下限
     * @return AgeLowLimit 年龄下限
     */
    public String getAgeLowLimit() {
        return ageLowLimit;
    }

    /**
     * 年龄下限
     * @param ageLowLimit 年龄下限
     */
    public void setAgeLowLimit(String ageLowLimit) {
        this.ageLowLimit = ageLowLimit;
    }

    /**
     * 性别
     * @return Sex 性别
     */
    public String getSex() {
        return sex;
    }

    /**
     * 性别
     * @param sex 性别
     */
    public void setSex(String sex) {
        this.sex = sex;
    }

    /**
     * 被保人类别 01-本人  02-配偶  03-子女  04-父母
     * @return InsuredType 被保人类别 01-本人  02-配偶  03-子女  04-父母
     */
    public String getInsuredType() {
        return insuredType;
    }

    /**
     * 被保人类别 01-本人  02-配偶  03-子女  04-父母
     * @param insuredType 被保人类别 01-本人  02-配偶  03-子女  04-父母
     */
    public void setInsuredType(String insuredType) {
        this.insuredType = insuredType;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 管理机构
     * @return OperatorCom 管理机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 管理机构
     * @param operatorCom 管理机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 入机日期
     * @return MakeDate 入机日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 入机日期
     * @param makeDate 入机日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 入机时间
     * @return MakeTime 入机时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 入机时间
     * @param makeTime 入机时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}