package com.sinosoft.eflex.model;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 创建时间: 2019年6月4日 下午4:53:24 
 * <AUTHOR>
 * @date 2019年6月4日 
*/
@Data
public class FCEnsureGrpInfo {

    /**
     * 个人客户号
     */
    private String perNo;

    /**
     * 保单状态
     */
	private String policyState;

    /**
     * 计划类型
     */
    private String planType;

    /**
     * 固定计划投保类型
     */
	private String ensureType;

    /**
     * 福利生效日期（即保单生效日期）
     */
    private String cvaliDate;

    /**
     * 福利终止日期（即保单终止日期）
     */
    private String policyEndDate;

    /**
     * 福利编码
     */
	private String ensureCode;

    /**
     * 福利名称
     */
    private String ensureName;

    /**
     * 企业客户号
     */
    private String grpNo;

    /**
     * 企业名称
     */
    private String grpName;

    /**
     * 订单状态
     */
    private String orderStatus;


    /**
     * 保险期间
     */
    private String insuredPeriod;

    /**
     * 保险期间单位
     */
    private String insuredPeriodUnit;

    /**
     * 保险期间描述
     */
    private String insuredPeriodMsg;

    /**
     * 福利下计划投保类型并集
     */
    private String insureObject;

    /**
     * 福利下计划计划投保类型描述
     */
    private String insureObjectMsg;

}
