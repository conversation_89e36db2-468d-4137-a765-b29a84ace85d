package com.sinosoft.eflex.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PersonsInsure {
    @ApiModelProperty(value = "序号")
    private String serialNumber;

    /** 被保人客户号 */
    private String insuredNo;
    /** 主被保险人客户号 */
    private String mainInsuredNo;

    @ApiModelProperty(value = "印刷号")
    private String printNumber;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "证件类型")
    private String idType;

    @ApiModelProperty(value = "证件号码")
    private String idNumber;

    @ApiModelProperty(value = "主被保险人姓名")
    private String policyHolderName;

    @ApiModelProperty(value = "与主被保险人关系")
    private String relationship;

    @ApiModelProperty(value = "险种名称")
    private String insuranceName;

    @ApiModelProperty(value = "保险计划编码")
    private String planCode;

    @ApiModelProperty(value = "保险期间")
    private String insurancePeriod;

    @ApiModelProperty(value = "缴费方式")
    private String paymentMethod;

    @ApiModelProperty(value = "保费")
    private String premium;

    @ApiModelProperty(value = "公司缴纳金额")
    private String companyPayment;

    @ApiModelProperty(value = "个人缴纳金额")
    private String personalPayment;

    @ApiModelProperty(value = "保额")
    private String insuredAmount;

    @ApiModelProperty(value = "生效日期")
    private String effectDate;


}