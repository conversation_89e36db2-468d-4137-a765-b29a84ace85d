package com.sinosoft.eflex.model;


public class FCOrderBnfRela {
    /**
     * 子订单号
     */
    private String orderItemNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 受益人编号
     */
    private String bnfNo;

    /**
     * 受益人类型
0-法定受益人
1-自选受益人
如果是法定则在受益人表里面没有数据，
     */
    private String bnfType;

    /**
     * 与被保人关系
00-	本人
01-	父母
02-	配偶
03-	子女
04-	祖孙
05-	监护
06-	其他
07-	保单服务人员
08-	直系亲属

     */
    private String relation;

    /**
     * 受益人类别
0-生存受益人
1-身故受益人
2-除身故保险金以外的其他保险金受益人

     */
    private String bnfKind;

    /**
     * 受益顺序
     */
    private Integer bnfOrder;

    /**
     * 受益比例
     */
    private Double bnfRatio;

    /**
     * 受益人形态
     */
    private String bnfForm;

    /**
     * 
     */
    private String operator;

    /**
     * 
     */
    private String operatorCom;

    /**
     * 
     */
    private String makeDate;

    /**
     * 
     */
    private String makeTime;

    /**
     * 
     */
    private String modifyDate;

    /**
     * 
     */
    private String modifyTime;

    /**
     * 子订单号
     * @return OrderItemNo 子订单号
     */
    public String getOrderItemNo() {
        return orderItemNo;
    }

    /**
     * 子订单号
     * @param orderItemNo 子订单号
     */
    public void setOrderItemNo(String orderItemNo) {
        this.orderItemNo = orderItemNo;
    }

    /**
     * 订单号
     * @return OrderNo 订单号
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * 订单号
     * @param orderNo 订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 受益人编号
     * @return BnfNo 受益人编号
     */
    public String getBnfNo() {
        return bnfNo;
    }

    /**
     * 受益人编号
     * @param bnfNo 受益人编号
     */
    public void setBnfNo(String bnfNo) {
        this.bnfNo = bnfNo;
    }

    /**
     * 受益人类型
0-法定受益人
1-自选受益人
如果是法定则在受益人表里面没有数据，
     * @return BnfType 受益人类型
0-法定受益人
1-自选受益人
如果是法定则在受益人表里面没有数据，
     */
    public String getBnfType() {
        return bnfType;
    }

    /**
     * 受益人类型
0-法定受益人
1-自选受益人
如果是法定则在受益人表里面没有数据，
     * @param bnfType 受益人类型
0-法定受益人
1-自选受益人
如果是法定则在受益人表里面没有数据，
     */
    public void setBnfType(String bnfType) {
        this.bnfType = bnfType;
    }

    /**
     * 与被保人关系
00-	本人
01-	父母
02-	配偶
03-	子女
04-	祖孙
05-	监护
06-	其他
07-	保单服务人员
08-	直系亲属

     * @return Relation 与被保人关系
00-	本人
01-	父母
02-	配偶
03-	子女
04-	祖孙
05-	监护
06-	其他
07-	保单服务人员
08-	直系亲属

     */
    public String getRelation() {
        return relation;
    }

    /**
     * 与被保人关系
00-	本人
01-	父母
02-	配偶
03-	子女
04-	祖孙
05-	监护
06-	其他
07-	保单服务人员
08-	直系亲属

     * @param relation 与被保人关系
00-	本人
01-	父母
02-	配偶
03-	子女
04-	祖孙
05-	监护
06-	其他
07-	保单服务人员
08-	直系亲属

     */
    public void setRelation(String relation) {
        this.relation = relation;
    }

    /**
     * 受益人类别
0-生存受益人
1-身故受益人
2-除身故保险金以外的其他保险金受益人

     * @return BnfKind 受益人类别
0-生存受益人
1-身故受益人
2-除身故保险金以外的其他保险金受益人

     */
    public String getBnfKind() {
        return bnfKind;
    }

    /**
     * 受益人类别
0-生存受益人
1-身故受益人
2-除身故保险金以外的其他保险金受益人

     * @param bnfKind 受益人类别
0-生存受益人
1-身故受益人
2-除身故保险金以外的其他保险金受益人

     */
    public void setBnfKind(String bnfKind) {
        this.bnfKind = bnfKind;
    }

    /**
     * 受益顺序
     * @return BnfOrder 受益顺序
     */
    public Integer getBnfOrder() {
        return bnfOrder;
    }

    /**
     * 受益顺序
     * @param bnfOrder 受益顺序
     */
    public void setBnfOrder(Integer bnfOrder) {
        this.bnfOrder = bnfOrder;
    }

    /**
     * 受益比例
     * @return BnfRatio 受益比例
     */
    public Double getBnfRatio() {
        return bnfRatio;
    }

    /**
     * 受益比例
     * @param bnfRatio 受益比例
     */
    public void setBnfRatio(Double bnfRatio) {
        this.bnfRatio = bnfRatio;
    }

    /**
     * 受益人形态
     * @return BnfForm 受益人形态
     */
    public String getBnfForm() {
        return bnfForm;
    }

    /**
     * 受益人形态
     * @param bnfForm 受益人形态
     */
    public void setBnfForm(String bnfForm) {
        this.bnfForm = bnfForm;
    }

    /**
     * 
     * @return Operator 
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 
     * @param operator 
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 
     * @return OperatorCom 
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 
     * @param operatorCom 
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 
     * @return MakeDate 
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 
     * @param makeDate 
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 
     * @return MakeTime 
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 
     * @param makeTime 
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 
     * @return ModifyDate 
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 
     * @param modifyDate 
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 
     * @return ModifyTime 
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 
     * @param modifyTime 
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}