package com.sinosoft.eflex.model;


public class FCEdorPlanInfo {
    /**
     * 保全计划流水号
     */
    private String edorPlanSN;

    /**
     * 计划编码
     */
    private String planCode;

    /**
     * 批次号
     */
    private String batch;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 使用人
     */
    private String planObject;

    /**
     * 险种名称
     */
    private String riskName;

    /**
     * 险种编码
     */
    private String riskCode;

    /**
     * 责任名称
     */
    private String dutyName;

    /**
     * 责任编码
     */
    private String dutyCode;

    /**
     * 保额
     */
    private Double amnt;

    /**
     * 保费
     */
    private Double prem;

    /**
     * 免赔额
     */
    private Double getLimit;

    /**
     * 赔付比例
     */
    private Double getRatio;

    /**
     * 备用字段1
     */
    private String spare1;

    /**
     * 备用字段2
     */
    private String spare2;

    /**
     * 备用字段3
     */
    private String spare3;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 创建日期
     */
    private String makeDate;

    /**
     * 创建时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 免赔额属性
     */
    private String getLimitType;

    /**
     * 保全计划流水号
     * @return edorPlanSN 保全计划流水号
     */
    public String getEdorPlanSN() {
        return edorPlanSN;
    }

    /**
     * 保全计划流水号
     * @param edorPlanSN 保全计划流水号
     */
    public void setEdorPlanSN(String edorPlanSN) {
        this.edorPlanSN = edorPlanSN;
    }

    /**
     * 计划编码
     * @return planCode 计划编码
     */
    public String getPlanCode() {
        return planCode;
    }

    /**
     * 计划编码
     * @param planCode 计划编码
     */
    public void setPlanCode(String planCode) {
        this.planCode = planCode;
    }

    /**
     * 批次号
     * @return batch 批次号
     */
    public String getBatch() {
        return batch;
    }

    /**
     * 批次号
     * @param batch 批次号
     */
    public void setBatch(String batch) {
        this.batch = batch;
    }

    /**
     * 计划名称
     * @return planName 计划名称
     */
    public String getPlanName() {
        return planName;
    }

    /**
     * 计划名称
     * @param planName 计划名称
     */
    public void setPlanName(String planName) {
        this.planName = planName;
    }

    /**
     * 使用人
     * @return planObject 使用人
     */
    public String getPlanObject() {
        return planObject;
    }

    /**
     * 使用人
     * @param planObject 使用人
     */
    public void setPlanObject(String planObject) {
        this.planObject = planObject;
    }

    /**
     * 险种名称
     * @return riskName 险种名称
     */
    public String getRiskName() {
        return riskName;
    }

    /**
     * 险种名称
     * @param riskName 险种名称
     */
    public void setRiskName(String riskName) {
        this.riskName = riskName;
    }

    /**
     * 险种编码
     * @return riskCode 险种编码
     */
    public String getRiskCode() {
        return riskCode;
    }

    /**
     * 险种编码
     * @param riskCode 险种编码
     */
    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    /**
     * 责任名称
     * @return dutyName 责任名称
     */
    public String getDutyName() {
        return dutyName;
    }

    /**
     * 责任名称
     * @param dutyName 责任名称
     */
    public void setDutyName(String dutyName) {
        this.dutyName = dutyName;
    }

    /**
     * 责任编码
     * @return dutyCode 责任编码
     */
    public String getDutyCode() {
        return dutyCode;
    }

    /**
     * 责任编码
     * @param dutyCode 责任编码
     */
    public void setDutyCode(String dutyCode) {
        this.dutyCode = dutyCode;
    }

    /**
     * 保额
     * @return Amnt 保额
     */
    public Double getAmnt() {
        return amnt;
    }

    /**
     * 保额
     * @param amnt 保额
     */
    public void setAmnt(Double amnt) {
        this.amnt = amnt;
    }

    /**
     * 保费
     * @return prem 保费
     */
    public Double getPrem() {
        return prem;
    }

    /**
     * 保费
     * @param prem 保费
     */
    public void setPrem(Double prem) {
        this.prem = prem;
    }

    /**
     * 免赔额
     * @return GetLimit 免赔额
     */
    public Double getGetLimit() {
        return getLimit;
    }

    /**
     * 免赔额
     * @param getLimit 免赔额
     */
    public void setGetLimit(Double getLimit) {
        this.getLimit = getLimit;
    }

    /**
     * 赔付比例
     * @return GetRatio 赔付比例
     */
    public Double getGetRatio() {
        return getRatio;
    }

    /**
     * 赔付比例
     * @param getRatio 赔付比例
     */
    public void setGetRatio(Double getRatio) {
        this.getRatio = getRatio;
    }

    /**
     * 备用字段1
     * @return spare1 备用字段1
     */
    public String getSpare1() {
        return spare1;
    }

    /**
     * 备用字段1
     * @param spare1 备用字段1
     */
    public void setSpare1(String spare1) {
        this.spare1 = spare1;
    }

    /**
     * 备用字段2
     * @return spare2 备用字段2
     */
    public String getSpare2() {
        return spare2;
    }

    /**
     * 备用字段2
     * @param spare2 备用字段2
     */
    public void setSpare2(String spare2) {
        this.spare2 = spare2;
    }

    /**
     * 备用字段3
     * @return spare3 备用字段3
     */
    public String getSpare3() {
        return spare3;
    }

    /**
     * 备用字段3
     * @param spare3 备用字段3
     */
    public void setSpare3(String spare3) {
        this.spare3 = spare3;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 操作机构
     * @return OperatorCom 操作机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作机构
     * @param operatorCom 操作机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 创建日期
     * @return MakeDate 创建日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 创建日期
     * @param makeDate 创建日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 创建时间
     * @return MakeTime 创建时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 创建时间
     * @param makeTime 创建时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    /**
     * 免赔额属性
     * @return GetLimitType 免赔额属性
     */
    public String getGetLimitType() {
        return getLimitType;
    }

    /**
     * 免赔额属性
     * @param getLimitType 免赔额属性
     */
    public void setGetLimitType(String getLimitType) {
        this.getLimitType = getLimitType;
    }
    
    @Override
	public String toString() {
		return "FCEdorPlanInfo [edorPlanSN=" + edorPlanSN + ", planCode=" + planCode + ", batch=" + batch
				+ ", planName=" + planName + ", planObject=" + planObject + ", riskName=" + riskName + ", riskCode="
				+ riskCode + ", dutyName=" + dutyName + ", dutyCode=" + dutyCode + ", amnt=" + amnt + ", prem=" + prem
				+ ", getLimit=" + getLimit + ", getRatio=" + getRatio + ", spare1=" + spare1 + ", spare2=" + spare2
				+ ", spare3=" + spare3 + ", operator=" + operator + ", operatorCom=" + operatorCom + ", makeDate="
				+ makeDate + ", makeTime=" + makeTime + ", modifyDate=" + modifyDate + ", modifyTime=" + modifyTime
				+ ", getLimitType=" + getLimitType + "]";
	}
}