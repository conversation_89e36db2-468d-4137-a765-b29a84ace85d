package com.sinosoft.eflex.model.dailyplan;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/25 15:11
 * @desc
 */

@Data
public class AppntInfo {

    /* 姓名 */
    private String name;
    /* 性别 */
    private String sex;
    /* 出生日期 */
    private String birthday;
    /* 证件类型 */
    private String idType;
    /* 证件号码 */
    private String idNo;
    /* 地址 */
    private String address;
    /* 邮编 */
    private String zipCode;
    //手机号
    private String mobile;
    //邮箱
    private String email;
    /* 职业代码 */
    private String jobCode;
    //工作单位
    private String company;
    //国籍
    private String nationality;
    //投保人年收入
    private String yearSalary;
    //投保人证件有效期
    private String idExpDate;
    //纳税身份
    private String taxpayerType;

    //投保人告知信息对象  默认空
    private List<AppntImpart> appntImpartList = new ArrayList<>();

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public String getBirthday() {
		return birthday;
	}

	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}

	public String getIdType() {
		return idType;
	}

	public void setIdType(String idType) {
		this.idType = idType;
	}

	public String getIdNo() {
		return idNo;
	}

	public void setIdNo(String idNo) {
		this.idNo = idNo;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getZipCode() {
		return zipCode;
	}

	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getJobCode() {
		return jobCode;
	}

	public void setJobCode(String jobCode) {
		this.jobCode = jobCode;
	}

	public String getCompany() {
		return company;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	public String getNationality() {
		return nationality;
	}

	public void setNationality(String nationality) {
		this.nationality = nationality;
	}

	public String getYearSalary() {
		return yearSalary;
	}

	public void setYearSalary(String yearSalary) {
		this.yearSalary = yearSalary;
	}

	public String getIdExpDate() {
		return idExpDate;
	}

	public void setIdExpDate(String idExpDate) {
		this.idExpDate = idExpDate;
	}

	public String getTaxpayerType() {
		return taxpayerType;
	}

	public void setTaxpayerType(String taxpayerType) {
		this.taxpayerType = taxpayerType;
	}

	public List<AppntImpart> getAppntImpartList() {
		return appntImpartList;
	}

	public void setAppntImpartList(List<AppntImpart> appntImpartList) {
		this.appntImpartList = appntImpartList;
	}



}
