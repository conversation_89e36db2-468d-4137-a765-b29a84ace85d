package com.sinosoft.eflex.model.dailyplan;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2020/5/22 13:58
 * @desc
 */

@Data
public class ImageReq {
    //图片类型
    private String imageType;
    //图片顺序
    private String imageOrder;
    //文件
    private MultipartFile file;
	public String getImageType() {
		return imageType;
	}
	public void setImageType(String imageType) {
		this.imageType = imageType;
	}
	public String getImageOrder() {
		return imageOrder;
	}
	public void setImageOrder(String imageOrder) {
		this.imageOrder = imageOrder;
	}
	public MultipartFile getFile() {
		return file;
	}
	public void setFile(MultipartFile file) {
		this.file = file;
	}

}
