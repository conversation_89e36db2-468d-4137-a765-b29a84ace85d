package com.sinosoft.eflex.model.dailyplan;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/5/6 18:15
 * @desc
 */

@Data
public class AppntImpart {
    /**
     * 告知版别
     */
    private String impartVer;
    /**
     * 告知编码
     */
    private String impartCode;
    /**
     * 告知内容
     */
    private String impartContent;
    /**
     * 详细描述
     */
    private String impartReply;
    /**
     * 说明内容
     */
    private String introDuctions = "";
	public String getImpartVer() {
		return impartVer;
	}
	public void setImpartVer(String impartVer) {
		this.impartVer = impartVer;
	}
	public String getImpartCode() {
		return impartCode;
	}
	public void setImpartCode(String impartCode) {
		this.impartCode = impartCode;
	}
	public String getImpartContent() {
		return impartContent;
	}
	public void setImpartContent(String impartContent) {
		this.impartContent = impartContent;
	}
	public String getImpartReply() {
		return impartReply;
	}
	public void setImpartReply(String impartReply) {
		this.impartReply = impartReply;
	}
	public String getIntroDuctions() {
		return introDuctions;
	}
	public void setIntroDuctions(String introDuctions) {
		this.introDuctions = introDuctions;
	}


}
