package com.sinosoft.eflex.model.dailyplan;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/6 18:53
 * @desc
 */

@Data
public class DailyInsuredInfo {

    /* 姓名 */
    private String name;
    /* 性别 */
    private String sex;
    /* 出生日期 */
    private String birthday;
    /* 证件类型 */
    private String idType;
    /* 证件号码 */
    private String idNo;
    /* 投保人证件有效期 */
    private String idExpDate;
	//投保单号  分单号
    private String contNo;
    /* 与主被保人关系 */
    private String mainRelation;
    /* 职业代码 */
    private String occupationCode;
    /* 职业类别 */
    private String occupationType;
    //国籍
    private String nationality;
    //地址
    private String address;
    //邮编
    private String zipCode;
    //邮箱
    private String email;
    /* 入司日期 */
    private String joinCompanyDate;
    /* 工资 */
    private String salary;
    /* 移动电话 */
    private String mobile;
    //身高
    private String stature;
    //体重
    private String weight;
    /* 社保标志 */
    private String socialInsuFlag;
    /* 保险计划 */
    private String contPlanCode;

    /* 首期交费方式代码 */
    private String newPayMode;
    /* 银行账号 */
    private String accNo;
    /* 账户户名 */
    private String accName;
    /* 银行代码 */
    private String accBankCode;
    /**
     * 开户行所在省代码
     */
    private String accBankProvince;

    private List<AppntImpart> insuredImpartList = new ArrayList<>();

    /* 险种信息 */
    private List<DailyRiskInfo> riskList;

    //受益人信息
    private List<BnfInfo> bnfList;
}
