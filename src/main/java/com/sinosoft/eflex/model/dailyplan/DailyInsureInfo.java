package com.sinosoft.eflex.model.dailyplan;

import lombok.Data;
/**
 * <AUTHOR> wu<PERSON><PERSON><PERSON><PERSON>
 * @date : 2021-02-25 11:33
 **/
@Data
public class DailyInsureInfo {

    /**
     * 险种编码
     */
    private String riskCode;

    /**
     * 出生日期
     */
    private String birthDay;

    /**
     * 年龄
     */
    private String age;

    /**
     *保险期间
     */
    private String dailyInsurePeriodType;

    /**
     * 交费频次
     */
    private String paymentFrequencyType;

    /**
     * 投保日期
     */
    private String insureDate;

    /**
     * 生效日期
     */
    private String CvaliDate ;

}
