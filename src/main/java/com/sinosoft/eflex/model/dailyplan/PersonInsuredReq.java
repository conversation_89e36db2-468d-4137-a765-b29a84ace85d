package com.sinosoft.eflex.model.dailyplan;

import com.sinosoft.eflex.model.FCOrderItemDetail;
import com.sinosoft.eflex.model.FCPerson;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/15 17:17
 * @desc
 */

@Data
public class PersonInsuredReq {
    /**
     * 福利编号
     */
    private String ensureCode;
    /**
     * 员工客户号
     */
    private String perNo;
    /**
     * 与员工关系
     */
    private String relation;

    /**
     * 子订单号
     */
    private String orderItemNo;
    //员工信息
    private FCPerson staffInfo;
    //家属信息
    private FCPerson staffFamilyInfo;
    //子订单详情
    private FCOrderItemDetail fcOrderItemDetail;
    //受益人
    private List<Map<String,String>> fcOrderBnfs;

    /**
     * 保费
     */
    private Double prem;
	//受益人类型
	private String bnfType;
	public String getEnsureCode() {
		return ensureCode;
	}

	public void setEnsureCode(String ensureCode) {
		this.ensureCode = ensureCode;
	}

	public String getPerNo() {
		return perNo;
	}

	public void setPerNo(String perNo) {
		this.perNo = perNo;
	}

	public String getRelation() {
		return relation;
	}

	public void setRelation(String relation) {
		this.relation = relation;
	}

	public String getOrderItemNo() {
		return orderItemNo;
	}

	public void setOrderItemNo(String orderItemNo) {
		this.orderItemNo = orderItemNo;
	}

	public FCPerson getStaffInfo() {
		return staffInfo;
	}

	public void setStaffInfo(FCPerson staffInfo) {
		this.staffInfo = staffInfo;
	}

	public FCPerson getStaffFamilyInfo() {
		return staffFamilyInfo;
	}

	public void setStaffFamilyInfo(FCPerson staffFamilyInfo) {
		this.staffFamilyInfo = staffFamilyInfo;
	}

	public FCOrderItemDetail getFcOrderItemDetail() {
		return fcOrderItemDetail;
	}

	public void setFcOrderItemDetail(FCOrderItemDetail fcOrderItemDetail) {
		this.fcOrderItemDetail = fcOrderItemDetail;
	}

	public List<Map<String, String>> getFcOrderBnfs() {
		return fcOrderBnfs;
	}

	public void setFcOrderBnfs(List<Map<String, String>> fcOrderBnfs) {
		this.fcOrderBnfs = fcOrderBnfs;
	}

	public Double getPrem() {
		return prem;
	}

	public void setPrem(Double prem) {
		this.prem = prem;
	}

	public String getBnfType() {
		return bnfType;
	}

	public void setBnfType(String bnfType) {
		this.bnfType = bnfType;
	}

}
