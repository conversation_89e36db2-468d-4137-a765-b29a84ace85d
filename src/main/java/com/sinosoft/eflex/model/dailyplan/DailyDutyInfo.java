package com.sinosoft.eflex.model.dailyplan;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/5/6 19:07
 * @desc
 */

@Data
public class DailyDutyInfo {
    /* 责任编码 */
    private String dutyCode;
    /* 保额 */
    private Double amnt;
    /* 保费 */
    private Double prem;
    /* 免赔额 */
    private Double getLimit;
    /* 赔付比例 */
    private Double getRate;
    /* 计算规则 */
    private String calRule;
	public String getDutyCode() {
		return dutyCode;
	}
	public void setDutyCode(String dutyCode) {
		this.dutyCode = dutyCode;
	}
	public Double getAmnt() {
		return amnt;
	}
	public void setAmnt(Double amnt) {
		this.amnt = amnt;
	}
	public Double getPrem() {
		return prem;
	}
	public void setPrem(Double prem) {
		this.prem = prem;
	}
	public Double getGetLimit() {
		return getLimit;
	}
	public void setGetLimit(Double getLimit) {
		this.getLimit = getLimit;
	}
	public Double getGetRate() {
		return getRate;
	}
	public void setGetRate(Double getRate) {
		this.getRate = getRate;
	}
	public String getCalRule() {
		return calRule;
	}
	public void setCalRule(String calRule) {
		this.calRule = calRule;
	}
    
}
