package com.sinosoft.eflex.model.dailyplan.convert;

import com.sinosoft.eflex.model.FCEnsure;
import com.sinosoft.eflex.model.FDAgentInfo;
import com.sinosoft.eflex.model.dailyplan.DailyContInfo;

/**
 * *
 *
 * <AUTHOR>
 */
public class DailyContInfoConvert {


    public static DailyContInfo convert(String tPrtNo, FCEnsure fcEnsure, FDAgentInfo fdAgentInfo, String specContent) {

        DailyContInfo dailyContInfo = new DailyContInfo();
        dailyContInfo.setGrpPrtNo(tPrtNo);
        dailyContInfo.setManageCom(fdAgentInfo.getManageCom());
        dailyContInfo.setSaleChnl(fdAgentInfo.getBranchType2());
        dailyContInfo.setSellType("01");
        dailyContInfo.setAgentType("245");
        dailyContInfo.setPolApplyDate(fcEnsure.getStartAppntDate());
        dailyContInfo.setContTypeFlag("1");
        dailyContInfo.setAgentCode(fdAgentInfo.getAgentCode());
        dailyContInfo.setAgentName(fdAgentInfo.getName());
        dailyContInfo.setAgentManageCom(fdAgentInfo.getManageCom());
        dailyContInfo.setBranchAttr(fdAgentInfo.getBranchCode());
        dailyContInfo.setGrpPayMode("1");
        dailyContInfo.setSpecContent(specContent);
        dailyContInfo.setChargeFeeRate(0.00);
        dailyContInfo.setGreenInsureFlag(fcEnsure.getGreenInsurance() ? "Y" : "N");
        dailyContInfo.setGetFlag("D");
//        dailyContInfo.setCommRate();
//        dailyContInfo.setFaceFlag();
//        dailyContInfo.setImageFlag();
//        dailyContInfo.setAgentImpartList();
//        dailyContInfo.setAppntInfo();
//        dailyContInfo.setInsuredInfoList();
//        dailyContInfo.setPlanList();
//        dailyContInfo.setEsViewList(EsViewConvert.convert(PageConvert.convert(fcGrpInfo)))
//        dailyContInfo.setCvalidate();
//        dailyContInfo.setGrpContNo();
//        dailyContInfo.setReportNo();
//        dailyContInfo.setGudongContFlag();
//        dailyContInfo.setPrtNo();
//        dailyContInfo.setGrpBankCode();
//        dailyContInfo.setGrpBankAccName();
//        dailyContInfo.setGrpBankAccNo();
//        dailyContInfo.setBatchFlag();
        return dailyContInfo;
    }
}
