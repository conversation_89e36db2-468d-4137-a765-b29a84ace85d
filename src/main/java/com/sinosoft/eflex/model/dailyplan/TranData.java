package com.sinosoft.eflex.model.dailyplan;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/5/6 17:45
 * @desc
 */

@Data
public class TranData extends DailyContInfo {


    private DailyHead head;

    private DailyBody body;

	public DailyHead getHead() {
		return head;
	}

	public void setHead(DailyHead head) {
		this.head = head;
	}

	public DailyBody getBody() {
		return body;
	}

	public void setBody(DailyBody body) {
		this.body = body;
	}

}
