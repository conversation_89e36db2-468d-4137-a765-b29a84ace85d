package com.sinosoft.eflex.model.dailyplan.convert;

import com.sinosoft.eflex.model.BatchInsureInterface.ESView;
import com.sinosoft.eflex.model.BatchInsureInterface.Page;

import java.util.ArrayList;
import java.util.List;

/**
 * 转换
 *
 * <AUTHOR> *
 */
public class EsViewConvert {


    public static List<ESView> convert(List<Page> pageList) {
        List<ESView> esViewList = new ArrayList<>();
        ESView esView = new ESView();
        esView.setPageList(pageList);
        esView.setPageNum(String.valueOf(pageList.size()));
        // 单证类型待确认
        esView.setSubType("111251");
        esViewList.add(esView);
        return esViewList;
    }
}
