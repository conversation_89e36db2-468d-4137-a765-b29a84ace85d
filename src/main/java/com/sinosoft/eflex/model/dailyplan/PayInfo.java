package com.sinosoft.eflex.model.dailyplan;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/5/7 19:54
 * @desc
 */

@Data
public class PayInfo {
    /**
     * 企业缴费方式
     */
    private String groupPayMode;
    /**
     * 金额
     */
    private Double groupPayMoney;
    /**
     * 个人缴费方式
     */
    private String personPayMode;
    /**
     * 金额
     */
    private Double personPayMoney;
	public String getGroupPayMode() {
		return groupPayMode;
	}
	public void setGroupPayMode(String groupPayMode) {
		this.groupPayMode = groupPayMode;
	}
	public Double getGroupPayMoney() {
		return groupPayMoney;
	}
	public void setGroupPayMoney(Double groupPayMoney) {
		this.groupPayMoney = groupPayMoney;
	}
	public String getPersonPayMode() {
		return personPayMode;
	}
	public void setPersonPayMode(String personPayMode) {
		this.personPayMode = personPayMode;
	}
	public Double getPersonPayMoney() {
		return personPayMoney;
	}
	public void setPersonPayMoney(Double personPayMoney) {
		this.personPayMoney = personPayMoney;
	}
    
    
}
