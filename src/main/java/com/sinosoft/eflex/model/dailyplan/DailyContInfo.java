package com.sinosoft.eflex.model.dailyplan;

import com.sinosoft.eflex.model.BatchInsureInterface.ESView;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/6 17:56
 * @desc
 */

@Data
public class DailyContInfo {
    /**
     * 投保单号
     * 基础单新生成，自然人投保取原基础单团体投保单号
     */
    private String grpPrtNo;
    //团体合同号
    private String grpContNo;
    /*呈报件号*/
    private String reportNo;
    /*管理机构*/
    private String manageCom;
    /*一级销售渠道*/
    private String saleChnl;
    /*二级销售渠道*/
    private String sellType;
    /*三级销售渠道*/
    private String agentType;
    /*投保申请日期*/
    private String polApplyDate;
    /*保单生效日期*/
    private String cvalidate;
    /*股东业务*/
    private String gudongContFlag;
    /**
     * 手续费比率
     */
    private Double ChargeFeeRate;
    /**
     * 佣金/服务津贴率
     */
    private Double CommRate;
    /**
     * 绿色保单标志
     */
    private String GreenInsureFlag;

    /**
     * 合同类型 基础单投保接口，统一传1，自然人投保传2
     */
    private String contTypeFlag;

    /*业务员代码*/
    private String agentCode;
    /*业务员姓名*/
    private String agentName;
    /*所属机构*/
    private String agentManageCom;
    /*所属分部*/
    private String branchAttr;
    //投保单号 自然人投保必填 暂传合同号
    private String prtNo;
    /**
     * 缴费方式 1-企业全缴 2-企业代缴 基础单必填
     */
    private String grpPayMode;
    /* 付款方式 */
    private String GetFlag;
    /**
     * 扣款缴费银行编码 可空 缴费方式为1、2时不传 为3时必传 银行编码见码表
     */
    private String grpBankCode;

    /**
     * 1-赠险，其余情况默认赋值非赠险*
     */
    private String complimentaryFlag;

    //扣款缴费账户名
    private String grpBankAccName;
    //扣款缴费银行账号
    private String grpBankAccNo;

    //自然人投保YF0004接口必填。核保接口YF0004处理这个字段，核保通过且批扣标识为1批扣，核心会先批扣再走核心签单逻辑
    private String batchFlag;

    //特约信息
    private String specContent;

    //人脸识别标志（1表示人脸识别不通过）
    private String faceFlag;

    //影像标志（1表示含病例资料）
    private String imageFlag;
    //业务员告知信息对象  默认空
    private List<AppntImpart> agentImpartList = new ArrayList<>();

    //投保人信息 自然人必传
    private AppntInfo appntInfo;

    //公司信息
    private DailyCompanyInfo companyInfo;

    //被保人信息
    private List<DailyInsuredInfo> insuredInfoList;

    //保险计划信息对象
    private List<DailyPlanInfo> planList;

    //影像件信息
    private List<ESView> esViewList = new ArrayList<>();
}
