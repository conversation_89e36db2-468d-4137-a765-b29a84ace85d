package com.sinosoft.eflex.model.dailyplan;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/6 18:42
 * @desc
 */

@Data
public class DailyCompanyInfo {


    /* 企业规模类型 */
    private String BusSizeType;
    /**
     * 主营业务
     */
    private String MainBussiness;
    /* 投保单位名称 */
    private String grpName;
    /* 证件类型 */
    private String comType;
    /* 证件号码 */
    private String comNo;
    //证件有效期
    private String comNoExpDate;
    /* 投保单位性质 */
    private String grpNature;
    /**
     * 企业组织形式*
     */
    private String GrpNatureType;
    private String grpNature1;
    /* 行业类别 */
    private String businessType;
    /* 单位员工总数 */
    private String peoples;
    /* 投保人数 */
    private String peoples2;
    /* 单位法人代表 */
    private String corporation;
    /**
     * 法人性别
     */
    private String corporationGender;
    /**
     * 法人生日
     */
    private String corporationBirthday;
    /**
     * 法人国籍
     */
    private String corporationNationality;
    /**
     * 法人证件类型
     */
    private String corporationIDType;
    /**
     * 法人证件号
     */
    private String corporationIDNo;
    /**
     * 法人证件有效期起
     */
    private String corporationIDStartPeriod;
    /**
     * 法人证件有效止期
     */
    private String corporationIDPeriodOfValidity;
    /* 同质风险减人百分比 */
    private String nZProportion;
    /* 单位地址 */
    private String grpAddress;
    /* 邮政编码 */
    private String grpZipCode;
    /**
     * 成立日期
     */
    private String foundDate;
    /**
     * 证件有效期起期
     */
    private String comNoStartDate;
    /**
     * 注册地
     */
    private String regestedPlace;

    /**
     * *  联系人
     */
    private DailyContacts contacts;
    /**
     * *  投保人告知
     */
    private List<AppntImpart> appntImpartInfo = new ArrayList<>();

    /**
     * 赋值为Q-免收，其余情况默认不传值。*
     */
    private String getFlag;
}
