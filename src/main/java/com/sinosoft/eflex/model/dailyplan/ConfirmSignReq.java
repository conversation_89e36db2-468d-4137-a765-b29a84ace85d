package com.sinosoft.eflex.model.dailyplan;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/20 16:48
 * @desc
 */

@Data
public class ConfirmSignReq {

    private String ensureCode;
    private String perNo;
    private String personId;
    private String payPersonId;//付款人personid   如果是企业的话需要注意
    private String relation;
    private String orderItemNo;
    //支付方式
    private String payType;
    private Double prem;
    //员工证件类型
    private List<ImageReq> staffImage;
    //家属证件类型
    private List<ImageReq> staffFamilyImage;
	public String getEnsureCode() {
		return ensureCode;
	}
	public void setEnsureCode(String ensureCode) {
		this.ensureCode = ensureCode;
	}
	public String getPerNo() {
		return perNo;
	}
	public void setPerNo(String perNo) {
		this.perNo = perNo;
	}
	public String getPersonId() {
		return personId;
	}
	public void setPersonId(String personId) {
		this.personId = personId;
	}
	public String getRelation() {
		return relation;
	}
	public void setRelation(String relation) {
		this.relation = relation;
	}
	public String getOrderItemNo() {
		return orderItemNo;
	}
	public void setOrderItemNo(String orderItemNo) {
		this.orderItemNo = orderItemNo;
	}
	public String getPayType() {
		return payType;
	}
	public void setPayType(String payType) {
		this.payType = payType;
	}
	public Double getPrem() {
		return prem;
	}
	public void setPrem(Double prem) {
		this.prem = prem;
	}
	public List<ImageReq> getStaffImage() {
		return staffImage;
	}
	public void setStaffImage(List<ImageReq> staffImage) {
		this.staffImage = staffImage;
	}
	public List<ImageReq> getStaffFamilyImage() {
		return staffFamilyImage;
	}
	public void setStaffFamilyImage(List<ImageReq> staffFamilyImage) {
		this.staffFamilyImage = staffFamilyImage;
	}


}
