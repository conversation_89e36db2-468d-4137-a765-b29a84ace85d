package com.sinosoft.eflex.model.dailyplan;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/5/6 19:02
 * @desc
 */

@Data
public class DailyRiskInfo {
    /* 险种编码 */
    private String riskCode;
    /* 主险编码 */
    private String mainRiskCode;
    /* 交费间隔 */
    private String payIntv;
    /* 保险年期 */
    private String years;
    /* 保险期间 */
    private String insuYear;
    /* 保险期间单位 */
    private String insuYearFlag;
    /* 交费年期 */
    private String payYears;
    /* 交费期间 */
    private String payEndYear;
    /* 交费期间单位 */
    private String payEndYearFlag;
    /* 领取间隔 */
    private String getIntv;
    /* 领取期间 */
    private String getYear;
    /* 领取期间单位 */
    private String getYearFlag;
    /* 红利领取方式 */
    private String bonusGetMode;
    /* 份数 */
    private String mult;
    /* 保费 */
    private Double prem;
    /* 保额 */
    private Double amnt;
    /**
     * 年金领取方式
     */
    private String liveGetMode;

    /**
     * 交费信息
     */
    private PayInfo pay;

    /* 责任信息 */
    private List<DailyDutyInfo> dutyList;

	public String getRiskCode() {
		return riskCode;
	}

	public void setRiskCode(String riskCode) {
		this.riskCode = riskCode;
	}

	public String getMainRiskCode() {
		return mainRiskCode;
	}

	public void setMainRiskCode(String mainRiskCode) {
		this.mainRiskCode = mainRiskCode;
	}

	public String getPayIntv() {
		return payIntv;
	}

	public void setPayIntv(String payIntv) {
		this.payIntv = payIntv;
	}

	public String getYears() {
		return years;
	}

	public void setYears(String years) {
		this.years = years;
	}

	public String getInsuYear() {
		return insuYear;
	}

	public void setInsuYear(String insuYear) {
		this.insuYear = insuYear;
	}

	public String getInsuYearFlag() {
		return insuYearFlag;
	}

	public void setInsuYearFlag(String insuYearFlag) {
		this.insuYearFlag = insuYearFlag;
	}

	public String getPayYears() {
		return payYears;
	}

	public void setPayYears(String payYears) {
		this.payYears = payYears;
	}

	public String getPayEndYear() {
		return payEndYear;
	}

	public void setPayEndYear(String payEndYear) {
		this.payEndYear = payEndYear;
	}

	public String getPayEndYearFlag() {
		return payEndYearFlag;
	}

	public void setPayEndYearFlag(String payEndYearFlag) {
		this.payEndYearFlag = payEndYearFlag;
	}

	public String getGetIntv() {
		return getIntv;
	}

	public void setGetIntv(String getIntv) {
		this.getIntv = getIntv;
	}

	public String getGetYear() {
		return getYear;
	}

	public void setGetYear(String getYear) {
		this.getYear = getYear;
	}

	public String getGetYearFlag() {
		return getYearFlag;
	}

	public void setGetYearFlag(String getYearFlag) {
		this.getYearFlag = getYearFlag;
	}

	public String getBonusGetMode() {
		return bonusGetMode;
	}

	public void setBonusGetMode(String bonusGetMode) {
		this.bonusGetMode = bonusGetMode;
	}

	public String getMult() {
		return mult;
	}

	public void setMult(String mult) {
		this.mult = mult;
	}

	public Double getPrem() {
		return prem;
	}

	public void setPrem(Double prem) {
		this.prem = prem;
	}

	public Double getAmnt() {
		return amnt;
	}

	public void setAmnt(Double amnt) {
		this.amnt = amnt;
	}

	public PayInfo getPay() {
		return pay;
	}

	public void setPay(PayInfo pay) {
		this.pay = pay;
	}

	public List<DailyDutyInfo> getDutyList() {
		return dutyList;
	}

	public void setDutyList(List<DailyDutyInfo> dutyList) {
		this.dutyList = dutyList;
	}
    
    
}
