package com.sinosoft.eflex.model.dailyplan.convert;

import com.sinosoft.eflex.model.DailyAmountTrail;
import com.sinosoft.eflex.model.FCOrderInsured;
import com.sinosoft.eflex.model.FCOrderItem;
import com.sinosoft.eflex.model.FCOrderItemDetail;
import com.sinosoft.eflex.util.CommonUtil;

/**
 * 转换*
 *
 * <AUTHOR> *
 */
public class DailyAmountTrailConvert {

    public static DailyAmountTrail convert(String riskCode, FCOrderItemDetail fcOrderItemDetail, FCOrderInsured fcOrderInsured, FCOrderItem fcOrderItem, String startAppntDate) {
        DailyAmountTrail dailyAmountTrail = new DailyAmountTrail();
        dailyAmountTrail.setRiskCode(riskCode);
        dailyAmountTrail.setBirthDay(fcOrderInsured.getBirthday());
        dailyAmountTrail.setInsurePeriod(fcOrderItemDetail.getInsurePeriod());
        dailyAmountTrail.setPayPeriod(fcOrderItemDetail.getPayPeriod());
        dailyAmountTrail.setGender(fcOrderInsured.getSex());
        dailyAmountTrail.setInsureDate(startAppntDate);
        // 保费
        Double prem = CommonUtil.add(fcOrderItem.getSelfPrem(), fcOrderItem.getGrpPrem());
        dailyAmountTrail.setPrem(String.valueOf(prem));
        return dailyAmountTrail;

    }

}
