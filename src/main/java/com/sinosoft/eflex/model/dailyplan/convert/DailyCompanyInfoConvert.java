package com.sinosoft.eflex.model.dailyplan.convert;

import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.dailyplan.AppntImpart;
import com.sinosoft.eflex.model.dailyplan.DailyCompanyInfo;
import com.sinosoft.eflex.model.dailyplan.DailyContacts;

import java.util.List;

/**
 * 转换*
 *
 * <AUTHOR>
 */
public class DailyCompanyInfoConvert {


    public static DailyCompanyInfo convert(FCGrpInfo fcGrpInfo, String insuredPeoples, List<AppntImpart> appntImpartList, DailyContacts contacts,String grpNature) {
        DailyCompanyInfo dailyCompanyInfo = new DailyCompanyInfo();
        dailyCompanyInfo.setGrpName(fcGrpInfo.getGrpName());
        dailyCompanyInfo.setComType("1");
        dailyCompanyInfo.setComNo(fcGrpInfo.getGrpIdNo());
        dailyCompanyInfo.setComNoExpDate(fcGrpInfo.getGrpTypeEndDate());
        dailyCompanyInfo.setGrpNature(fcGrpInfo.getGrpType());
        dailyCompanyInfo.setGrpNatureType(fcGrpInfo.getGrpNatureType());
        dailyCompanyInfo.setBusinessType(fcGrpInfo.getTrade());
        dailyCompanyInfo.setPeoples(String.valueOf(fcGrpInfo.getPeoples()));
        dailyCompanyInfo.setPeoples2(insuredPeoples);
        dailyCompanyInfo.setCorporation(fcGrpInfo.getCorporationMan());
        dailyCompanyInfo.setNZProportion("50");
        dailyCompanyInfo.setGrpAddress(fcGrpInfo.getGrpAddRess());
        dailyCompanyInfo.setGrpZipCode(fcGrpInfo.getZipCode());
        dailyCompanyInfo.setContacts(contacts);
        dailyCompanyInfo.setAppntImpartInfo(appntImpartList);
        dailyCompanyInfo.setCorporationIDNo(fcGrpInfo.getLegID());
        dailyCompanyInfo.setCorporationIDType(fcGrpInfo.getLegIDType());
        dailyCompanyInfo.setCorporationIDStartPeriod(fcGrpInfo.getLegIDStartDate());
        dailyCompanyInfo.setCorporationIDPeriodOfValidity(fcGrpInfo.getLegIDEndDate());
        dailyCompanyInfo.setCorporationGender(fcGrpInfo.getLegSex());
        dailyCompanyInfo.setCorporationBirthday(fcGrpInfo.getLegBirthday());
        dailyCompanyInfo.setCorporationNationality(fcGrpInfo.getLegNationality());
//        dailyCompanyInfo.setnZProportion();
        dailyCompanyInfo.setFoundDate(fcGrpInfo.getGrpEstablishDate());
        dailyCompanyInfo.setComNoStartDate(fcGrpInfo.getGrpTypeStartDate());
        dailyCompanyInfo.setRegestedPlace(fcGrpInfo.getGrpRegisterAddress());
        dailyCompanyInfo.setGrpNature1(grpNature);
        dailyCompanyInfo.setBusSizeType(fcGrpInfo.getGrpScaleType());
        dailyCompanyInfo.setMainBussiness(fcGrpInfo.getBusinesses());
        return dailyCompanyInfo;
    }
}
