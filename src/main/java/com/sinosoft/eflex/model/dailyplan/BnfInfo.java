package com.sinosoft.eflex.model.dailyplan;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/5/25 16:08
 * @desc
 */

@Data
public class BnfInfo {

    //受益人类别
    private String type;
    //受益顺序
    private String grade;
    //与被保人关系
    private String relation;
    private String name;
    private String sex;
    private String idType;
    private String idNo;
    //证件有效期
    private String idExpDate;
    private String birthday;
    //受益比例
    private String rate;
    //电话
    private String mobile;
    //联系地址
    private String address;
    //国籍
    private String nativePlace;
    //受益人形态
    private String orgFlag;
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getGrade() {
		return grade;
	}
	public void setGrade(String grade) {
		this.grade = grade;
	}
	public String getRelation() {
		return relation;
	}
	public void setRelation(String relation) {
		this.relation = relation;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getSex() {
		return sex;
	}
	public void setSex(String sex) {
		this.sex = sex;
	}
	public String getIdType() {
		return idType;
	}
	public void setIdType(String idType) {
		this.idType = idType;
	}
	public String getIdNo() {
		return idNo;
	}
	public void setIdNo(String idNo) {
		this.idNo = idNo;
	}
	public String getIdExpDate() {
		return idExpDate;
	}
	public void setIdExpDate(String idExpDate) {
		this.idExpDate = idExpDate;
	}
	public String getBirthday() {
		return birthday;
	}
	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}
	public String getRate() {
		return rate;
	}
	public void setRate(String rate) {
		this.rate = rate;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public String getOrgFlag() {
		return orgFlag;
	}
	public void setOrgFlag(String orgFlag) {
		this.orgFlag = orgFlag;
	}
	public String getNativePlace() {
		return nativePlace;
	}
	public void setNativePlace(String nativePlace) {
		this.nativePlace = nativePlace;
	}
}
