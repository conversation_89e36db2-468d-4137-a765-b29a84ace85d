package com.sinosoft.eflex.model.dailyplan;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/5/6 17:47
 * @desc
 */

@Data
public class DailyHead {
    /**
     * 交易ID
     */
    private String transRefGUID;
    /**
     * 交易日期 格式YYYY-MM-DD
     */
    private String tranDate;
    /**
     * TranTime 格式HH:MM:SS
     */
    private String tranTime;
    /**
     * 交易编号
     * 基础单预核保：YF0001
     * 基础单签单：YF0002
     * 自然人预核：YF0003
     * 自然人核保：YF0004
     * 自然人签单：YF0005
     */
    private String funcFlag;
    /**
     * 交易来源  固定值，核心提供
     */
    private String source;
    /**
     * 交易子来源 固定值
     */
    private String subSource;
	public String getTransRefGUID() {
		return transRefGUID;
	}
	public void setTransRefGUID(String transRefGUID) {
		this.transRefGUID = transRefGUID;
	}
	public String getTranDate() {
		return tranDate;
	}
	public void setTranDate(String tranDate) {
		this.tranDate = tranDate;
	}
	public String getTranTime() {
		return tranTime;
	}
	public void setTranTime(String tranTime) {
		this.tranTime = tranTime;
	}
	public String getFuncFlag() {
		return funcFlag;
	}
	public void setFuncFlag(String funcFlag) {
		this.funcFlag = funcFlag;
	}
	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}
	public String getSubSource() {
		return subSource;
	}
	public void setSubSource(String subSource) {
		this.subSource = subSource;
	}
    
    
}
