package com.sinosoft.eflex.model.review;

import java.util.List;

public class DataContentValueReview {
	/** 人核结论
	 *  1-拒保
		2-延期
		3-加费承保
		4-特约承保
		5-未通过自动核保
		9-标准承保
		a-撤保
		z-核保订正

	 */
	private String approved;
	private String applyName;//投保人姓名
	private String currentAmtTotal;//合同当期总体保额
	private String modalPremTotal;//合同期交总体保费
	private String uwFinishTime;//核保完成时间
	private String policyCode;//保单号
	private String applyCode;//投保单号
	private String failedCause;//核保不通过原因：文字描述
	private String agentCode;//代理人员编码
	private String agentName;//代理人姓名
	private List<Coverage> coverage;//险种核保信息
	public String getApproved() {
		return approved;
	}
	public void setApproved(String approved) {
		this.approved = approved;
	}
	public String getApplyName() {
		return applyName;
	}
	public void setApplyName(String applyName) {
		this.applyName = applyName;
	}
	public String getCurrentAmtTotal() {
		return currentAmtTotal;
	}
	public void setCurrentAmtTotal(String currentAmtTotal) {
		this.currentAmtTotal = currentAmtTotal;
	}
	public String getModalPremTotal() {
		return modalPremTotal;
	}
	public void setModalPremTotal(String modalPremTotal) {
		this.modalPremTotal = modalPremTotal;
	}
	public String getUwFinishTime() {
		return uwFinishTime;
	}
	public void setUwFinishTime(String uwFinishTime) {
		this.uwFinishTime = uwFinishTime;
	}
	public String getPolicyCode() {
		return policyCode;
	}
	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}
	public String getApplyCode() {
		return applyCode;
	}
	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}
	public String getFailedCause() {
		return failedCause;
	}
	public void setFailedCause(String failedCause) {
		this.failedCause = failedCause;
	}
	public String getAgentCode() {
		return agentCode;
	}
	public void setAgentCode(String agentCode) {
		this.agentCode = agentCode;
	}
	public String getAgentName() {
		return agentName;
	}
	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}
	public List<Coverage> getCoverage() {
		return coverage;
	}
	public void setCoverage(List<Coverage> coverage) {
		this.coverage = coverage;
	}
}
