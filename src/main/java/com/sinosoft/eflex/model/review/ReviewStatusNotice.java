package com.sinosoft.eflex.model.review;

public class ReviewStatusNotice {
//	private DataContentReview dataContent;
//	private DataHeadReview dataHead;
//	public DataContentReview getDataContent() {
//		return dataContent;
//	}
//	public void setDataContent(DataContentReview dataContent) {
//		this.dataContent = dataContent;
//	}
//	public DataHeadReview getDataHead() {
//		return dataHead;
//	}
//	public void setDataHead(DataHeadReview dataHead) {
//		this.dataHead = dataHead;
//	}

    /**
     * 2020/6/8
     * 自然人投保-核保结论通知接口  修改
     * dataContent
     * dataHead  两部分合并到body中
     */
    private DataBodyReview body;
    private String code;
    private String message;

    public DataBodyReview getBody() {
        return body;
    }

    public void setBody(DataBodyReview body) {
        this.body = body;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
