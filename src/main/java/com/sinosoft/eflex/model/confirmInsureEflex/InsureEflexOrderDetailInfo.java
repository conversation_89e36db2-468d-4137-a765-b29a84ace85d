package com.sinosoft.eflex.model.confirmInsureEflex;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2021/6/11
 * @desc 保额档次的投保详情信息
 */
@Data
public class InsureEflexOrderDetailInfo {

    /**
     * 保额档次编码
     */
    private String amountGrageCode;

    /**
     * 险种编码
     */
    private String riskCode;

    /**
     * 险种类型
     */
    private String riskType;

    /**
     * 险种名称
     */
    private String riskName;

    /**
     * 责任编码
     */
    private String dutyCode;

    /**
     * 责任名称
     */
    private String dutyName;

    /**
     * 保额
     */
    private Double amount;

    /**
     * 保费
     */
    private String prem;

}
