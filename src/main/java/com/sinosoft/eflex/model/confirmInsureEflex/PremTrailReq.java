package com.sinosoft.eflex.model.confirmInsureEflex;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2021/6/24
 * @desc 保费试算请求报文
 */
@Data
public class PremTrailReq {

    /**
     * 福利编码
     */
    private String ensureCode;

    /**
     * 保额档次编码
     */
    private String amountGrageCode;

    /**
     * 险种编码
     */
    private String riskCode;

    /**
     * 出生日期
     */
    private String birthDay;

    /**
     * 性别
     */
    private String sex;

    /**
     * 保单生效日期
     */
    private String cvaliDate;

    /**
     * 免赔额
     */
    private String deductible;

    /**
     * 赔付比例
     */
    private String compensationRatio;


    /**
     * 是否参加医保
     */
    private String joinMedProtect;

    /**
     * 职业类别
     */
    private String occupationType;

    /**
     * 可选责任编码
     */
    private String optDutyCode;

    /**
     * 是否全部选中
     */
    private String isAmount;

}
