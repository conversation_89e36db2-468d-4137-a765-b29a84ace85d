package com.sinosoft.eflex.model.confirmInsureEflex;

import com.sinosoft.eflex.model.FCPerInfo;
import com.sinosoft.eflex.model.confirmInsure.CheckTBResultInfo;
import com.sinosoft.eflex.model.confirmInsure.HealthNoticeInfo;
import com.sinosoft.eflex.model.confirmInsure.JuvenilesConfirmationInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/6/7
 * @desc
 */
@Data
public class EflexConfirmInsureResp {

    // 是否需要支付 0-不需要支付 1-需要支付
    private String isSelfPay;

    // 判断需要展示员工付款信息
    private String isCheck;

    // 已投保人员信息
    private List<EflexPeopleInsureInfo> peopleInsureInfoList;

    // 个人健康告知确认信息
    private List<HealthNoticeInfo> healthNoticeList;

    // 未成年人投保确认信息
    private List<JuvenilesConfirmationInfo> juvenilesConfirmationInfoList;

    // 员工付款信息
    private FCPerInfo perInfo;

    // 汇总信息
    private EflexTotalInfo totalInfo;

    // 校验规则信息
    private CheckTBResultInfo checkTBResultInfo;

}
