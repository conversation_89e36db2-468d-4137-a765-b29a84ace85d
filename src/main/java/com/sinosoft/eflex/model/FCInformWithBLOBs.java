package com.sinosoft.eflex.model;

/**
 * 健康告知表
 * <AUTHOR>
 *
 */
public class FCInformWithBLOBs extends FCInform {
    /**
     * 告知内容
     */
    private String informContent;

    /**
     * 详细描述
     */
    private String informDescribe;

    /**
     * 告知内容
     * @return InformContent 告知内容
     */
    public String getInformContent() {
        return informContent;
    }

    /**
     * 告知内容
     * @param informContent 告知内容
     */
    public void setInformContent(String informContent) {
        this.informContent = informContent;
    }

    /**
     * 详细描述
     * @return InformDescribe 详细描述
     */
    public String getInformDescribe() {
        return informDescribe;
    }

    /**
     * 详细描述
     * @param informDescribe 详细描述
     */
    public void setInformDescribe(String informDescribe) {
        this.informDescribe = informDescribe;
    }
}