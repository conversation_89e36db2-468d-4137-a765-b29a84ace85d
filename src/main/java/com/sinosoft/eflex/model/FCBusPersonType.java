package com.sinosoft.eflex.model;

import lombok.Data;
@Data
public class FCBusPersonType {
    /**
     * 福利编号
     */
    private String ensureCode;

    /**
     * 企业客户号
     */
    private String grpNo;

    /**
     * 职级编号
     */
    private String gradeLevelCode;

    /**
     * 职级描述
     */
    private String gradeDesc;

    /**
     * 职级顺序号
     */
    private String orderNum;

    /**
     * 计划类型 0- 固定计划 1- 弹性计划 2- 日常投保
     */
    private String planType;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 管理机构
     */
    private String operatorCom;

    /**
     * 入机日期
     */
    private String  makeDate;

    /**
     * 入机时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String  modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 福利编号
     * @return EnsureCode 福利编号
     */
    public String getEnsureCode() {
        return ensureCode;
    }

    /**
     * 福利编号
     * @param ensureCode 福利编号
     */
    public void setEnsureCode(String ensureCode) {
        this.ensureCode = ensureCode;
    }

    /**
     * 企业客户号
     * @return GrpNo 企业客户号
     */
    public String getGrpNo() {
        return grpNo;
    }

    /**
     * 企业客户号
     * @param grpNo 企业客户号
     */
    public void setGrpNo(String grpNo) {
        this.grpNo = grpNo;
    }

    /**
     * 职级编号
     * @return GradeLevelCode 职级编号
     */
    public String getGradeLevelCode() {
        return gradeLevelCode;
    }

    /**
     * 职级编号
     * @param gradeLevelCode 职级编号
     */
    public void setGradeLevelCode(String gradeLevelCode) {
        this.gradeLevelCode = gradeLevelCode;
    }

    /**
     * 职级描述
     * @return GradeDesc 职级描述
     */
    public String getGradeDesc() {
        return gradeDesc;
    }

    /**
     * 职级描述
     * @param gradeDesc 职级描述
     */
    public void setGradeDesc(String gradeDesc) {
        this.gradeDesc = gradeDesc;
    }

    /**
     * 职级顺序号
     * @return OrderNum 职级顺序号
     */
    public String getOrderNum() {
        return orderNum;
    }

    /**
     * 职级顺序号
     * @param orderNum 职级顺序号
     */
    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 管理机构
     * @return OperatorCom 管理机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 管理机构
     * @param operatorCom 管理机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 入机日期
     * @return MakeDate 入机日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 入机日期
     * @param makeDate 入机日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 入机时间
     * @return MakeTime 入机时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 入机时间
     * @param makeTime 入机时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

	public String getPlanType() {
		return planType;
	}

	public void setPlanType(String planType) {
		this.planType = planType;
	}
    
    
}