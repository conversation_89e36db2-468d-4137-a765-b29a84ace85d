package com.sinosoft.eflex.model;
/** 
 * <AUTHOR>
 * @version 创建时间: 2019年7月3日 下午2:58:34 
 * <AUTHOR>
 * @date 2019年7月3日 
*/

import java.util.List;

import com.sinosoft.eflex.model.BatchInsureInterface.DutyInfo;

public class Duty { 
	private String DutyCode;
	private String DutyName;
	private String DutyAmnt;
	private String DutyPrem;
	private String GetLimit;
	private String GetRate;
	public String getDutyCode() {
		return DutyCode;
	}
	public void setDutyCode(String dutyCode) {
		DutyCode = dutyCode;
	}
	public String getDutyName() {
		return DutyName;
	}
	public void setDutyName(String dutyName) {
		DutyName = dutyName;
	}
	public String getDutyAmnt() {
		return DutyAmnt;
	}
	public void setDutyAmnt(String dutyAmnt) {
		DutyAmnt = dutyAmnt;
	}
	public String getDutyPrem() {
		return DutyPrem;
	}
	public void setDutyPrem(String dutyPrem) {
		DutyPrem = dutyPrem;
	}
	public String getGetLimit() {
		return GetLimit;
	}
	public void setGetLimit(String getLimit) {
		GetLimit = getLimit;
	}
	public String getGetRate() {
		return GetRate;
	}
	public void setGetRate(String getRate) {
		GetRate = getRate;
	}
	
	
	
}
