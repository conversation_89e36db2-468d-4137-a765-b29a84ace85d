package com.sinosoft.eflex.model;

public class FCEdorHomogeneousRiskInsured {
    /**
     * 同质被保险人流水号
     */
    private String homogeneousRiskInsuredSN;

    /**
     * 保单号
     */
    private String grpContNo;

    /**
     * 批次号
     */
    private String batch;

    /**
     * 企业号
     */
    private String grpNo;

    /**
     * 原被保险人姓名
     */
    private String oldName;

    /**
     * 原被保险人证件类型
     */
    private String oldIdType;

    /**
     * 原被保险人证件号
     */
    private String oldIdNo;

    /**
     * 原被保险人性别
     */
    private String oldSex;

    /**
     * 原被保险人出生日期
     */
    private String oldBirthday;

    /**
     * 新被保险人姓名
     */
    private String newName;

    /**
     * 新被保险人国籍
     */
    private String newNativeplace;

    /**
     * 新被保险人证件类型
     */
    private String newIdType;

    /**
     * 新被保险人证件号
     */
    private String newIdNo;

    /**
     * 新被保险人证件有效期
     */
    private String newIdTypeEndDate;

    /**
     * 新被保险人性别
     */
    private String newSex;

    /**
     * 新被保险人出生日期
     */
    private String newBirthday;

    /**
     * 新被保险人手机号
     */
    private String newMobilePhone;

    /**
     * 新被保险人职业类别
     */
    private String newJobType;

    /**
     * 新被保险人职业代码
     */
    private String newJobCode;

    /**
     * 新被保险人医保标记1-是0-否
     */
    private String newJoinMedProtect;

    /**
     * 同质风险加减人生效日期
     */
    private String nzValidate;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 创建日期
     */
    private String makeDate;

    /**
     * 创建时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 同质被保险人流水号
     * @return HomogeneousRiskInsuredSN 同质被保险人流水号
     */
    public String getHomogeneousRiskInsuredSN() {
        return homogeneousRiskInsuredSN;
    }

    /**
     * 同质被保险人流水号
     * @param homogeneousRiskInsuredSN 同质被保险人流水号
     */
    public void setHomogeneousRiskInsuredSN(String homogeneousRiskInsuredSN) {
        this.homogeneousRiskInsuredSN = homogeneousRiskInsuredSN;
    }

    /**
     * 保单号
     * @return GrpContNo 保单号
     */
    public String getGrpContNo() {
        return grpContNo;
    }

    /**
     * 保单号
     * @param grpContNo 保单号
     */
    public void setGrpContNo(String grpContNo) {
        this.grpContNo = grpContNo;
    }

    /**
     * 批次号
     * @return Batch 批次号
     */
    public String getBatch() {
        return batch;
    }

    /**
     * 批次号
     * @param batch 批次号
     */
    public void setBatch(String batch) {
        this.batch = batch;
    }

    /**
     * 企业号
     * @return GrpNo 企业号
     */
    public String getGrpNo() {
        return grpNo;
    }

    /**
     * 企业号
     * @param grpNo 企业号
     */
    public void setGrpNo(String grpNo) {
        this.grpNo = grpNo;
    }

    /**
     * 原被保险人姓名
     * @return oldName 原被保险人姓名
     */
    public String getOldName() {
        return oldName;
    }

    /**
     * 原被保险人姓名
     * @param oldName 原被保险人姓名
     */
    public void setOldName(String oldName) {
        this.oldName = oldName;
    }

    /**
     * 原被保险人证件类型
     * @return oldIdType 原被保险人证件类型
     */
    public String getOldIdType() {
        return oldIdType;
    }

    /**
     * 原被保险人证件类型
     * @param oldIdType 原被保险人证件类型
     */
    public void setOldIdType(String oldIdType) {
        this.oldIdType = oldIdType;
    }

    /**
     * 原被保险人证件号
     * @return oldIdNo 原被保险人证件号
     */
    public String getOldIdNo() {
        return oldIdNo;
    }

    /**
     * 原被保险人证件号
     * @param oldIdNo 原被保险人证件号
     */
    public void setOldIdNo(String oldIdNo) {
        this.oldIdNo = oldIdNo;
    }

    /**
     * 原被保险人性别
     * @return oldSex 原被保险人性别
     */
    public String getOldSex() {
        return oldSex;
    }

    /**
     * 原被保险人性别
     * @param oldSex 原被保险人性别
     */
    public void setOldSex(String oldSex) {
        this.oldSex = oldSex;
    }

    /**
     * 原被保险人出生日期
     * @return oldBirthday 原被保险人出生日期
     */
    public String getOldBirthday() {
        return oldBirthday;
    }

    /**
     * 原被保险人出生日期
     * @param oldBirthday 原被保险人出生日期
     */
    public void setOldBirthday(String oldBirthday) {
        this.oldBirthday = oldBirthday;
    }

    /**
     * 新被保险人姓名
     * @return newName 新被保险人姓名
     */
    public String getNewName() {
        return newName;
    }

    /**
     * 新被保险人姓名
     * @param newName 新被保险人姓名
     */
    public void setNewName(String newName) {
        this.newName = newName;
    }

    /**
     * 新被保险人国籍
     * @return newNativeplace 新被保险人国籍
     */
    public String getNewNativeplace() {
        return newNativeplace;
    }

    /**
     * 新被保险人国籍
     * @param newNativeplace 新被保险人国籍
     */
    public void setNewNativeplace(String newNativeplace) {
        this.newNativeplace = newNativeplace;
    }

    /**
     * 新被保险人证件类型
     * @return newIdType 新被保险人证件类型
     */
    public String getNewIdType() {
        return newIdType;
    }

    /**
     * 新被保险人证件类型
     * @param newIdType 新被保险人证件类型
     */
    public void setNewIdType(String newIdType) {
        this.newIdType = newIdType;
    }

    /**
     * 新被保险人证件号
     * @return newIdNo 新被保险人证件号
     */
    public String getNewIdNo() {
        return newIdNo;
    }

    /**
     * 新被保险人证件号
     * @param newIdNo 新被保险人证件号
     */
    public void setNewIdNo(String newIdNo) {
        this.newIdNo = newIdNo;
    }

    /**
     * 新被保险人证件有效期
     * @return newIdTypeEndDate 新被保险人证件有效期
     */
    public String getNewIdTypeEndDate() {
        return newIdTypeEndDate;
    }

    /**
     * 新被保险人证件有效期
     * @param newIdTypeEndDate 新被保险人证件有效期
     */
    public void setNewIdTypeEndDate(String newIdTypeEndDate) {
        this.newIdTypeEndDate = newIdTypeEndDate;
    }

    /**
     * 新被保险人性别
     * @return newSex 新被保险人性别
     */
    public String getNewSex() {
        return newSex;
    }

    /**
     * 新被保险人性别
     * @param newSex 新被保险人性别
     */
    public void setNewSex(String newSex) {
        this.newSex = newSex;
    }

    /**
     * 新被保险人出生日期
     * @return newBirthday 新被保险人出生日期
     */
    public String getNewBirthday() {
        return newBirthday;
    }

    /**
     * 新被保险人出生日期
     * @param newBirthday 新被保险人出生日期
     */
    public void setNewBirthday(String newBirthday) {
        this.newBirthday = newBirthday;
    }

    /**
     * 新被保险人手机号
     * @return newMobilePhone 新被保险人手机号
     */
    public String getNewMobilePhone() {
        return newMobilePhone;
    }

    /**
     * 新被保险人手机号
     * @param newMobilePhone 新被保险人手机号
     */
    public void setNewMobilePhone(String newMobilePhone) {
        this.newMobilePhone = newMobilePhone;
    }

    /**
     * 新被保险人职业类别
     * @return newJobType 新被保险人职业类别
     */
    public String getNewJobType() {
        return newJobType;
    }

    /**
     * 新被保险人职业类别
     * @param newJobType 新被保险人职业类别
     */
    public void setNewJobType(String newJobType) {
        this.newJobType = newJobType;
    }

    /**
     * 新被保险人职业代码
     * @return newJobCode 新被保险人职业代码
     */
    public String getNewJobCode() {
        return newJobCode;
    }

    /**
     * 新被保险人职业代码
     * @param newJobCode 新被保险人职业代码
     */
    public void setNewJobCode(String newJobCode) {
        this.newJobCode = newJobCode;
    }

    /**
     * 新被保险人医保标记1-是0-否
     * @return newJoinMedProtect 新被保险人医保标记1-是0-否
     */
    public String getNewJoinMedProtect() {
        return newJoinMedProtect;
    }

    /**
     * 新被保险人医保标记1-是0-否
     * @param newJoinMedProtect 新被保险人医保标记1-是0-否
     */
    public void setNewJoinMedProtect(String newJoinMedProtect) {
        this.newJoinMedProtect = newJoinMedProtect;
    }


    public String getNzValidate() {
        return nzValidate;
    }

    public void setNzValidate(String nzValidate) {
        this.nzValidate = nzValidate;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 操作机构
     * @return OperatorCom 操作机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作机构
     * @param operatorCom 操作机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 创建日期
     * @return MakeDate 创建日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 创建日期
     * @param makeDate 创建日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 创建时间
     * @return MakeTime 创建时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 创建时间
     * @param makeTime 创建时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}