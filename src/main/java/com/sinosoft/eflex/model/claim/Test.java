package com.sinosoft.eflex.model.claim;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;
import org.apache.commons.lang.StringUtils;

import com.sinosoft.eflex.dao.FCEdorPlanInfoMapper;
import com.sinosoft.eflex.model.FCPlanRisk;
import com.sinosoft.eflex.util.CommonUtil;
import com.sinosoft.eflex.util.DateTimeUtil;
import com.sinosoft.eflex.util.ExcelUtil;

/**
 * @DESCRIPTION
 * @create 2018-11-05 17:51
 **/
public class Test {
	@SuppressWarnings("unlikely-arg-type")
	public static <E> void main(String[] args) {
		
//		Double s2 = 2.0;
//		double s1 = s2;
//		Double s3 = s1;
//		String d = null;
//		System.out.println(s3);
////		s1=null;
//		s2=null;
//		System.out.println(s2);
//		FCPlanRisk fcPlanRisk = new FCPlanRisk();
//		FCPlanRisk fcPlanRisk1 = getType(fcPlanRisk);
//		System.out.println(fcPlanRisk1.getMakeDate());
//		String s = "2";
//		if(s.equals(1|2)) {
//			System.out.println("qq");
//		}
		
//		Map<String,String> resultMap=new HashMap<>();
//		String s1 = "";
//		String s2 = null;
//		resultMap.put(s2,s2);
//		if(resultMap.isEmpty()) {
//			System.out.println("222");
//		}
//		resultMap.put("serialNo","102");
//		String s = resultMap.get("serialNo");
//		String s1 = "10";
//		System.out.println(s1.indexOf("."));
//		System.out.println(s1.substring(0, s1.length() - 1));
//		System.out.println(Integer.valueOf(s1));
//		System.out.println(Integer.valueOf(s1));
//		int ss = Double.valueOf(resultMap.get("serialNo")).intValue();
//		System.out.println(ss);
		
//		System.out.println(checkFutureDate("2018-04-16"));
		String s3 = "s";
	    String s2 = "99";
	    String s4 = "1";
		String regex =  "^[0-9A-Za-z]{1,100}$";
		if(s3.matches("^[a-z]{2}$")) {//两位数字
			System.out.println(true);	
		}else {
			System.out.println("No");
		}
		if(s2.matches("^\\d+$")) {//正整数+0   "^[0-9]*[1-9][0-9]*$"--》正整数
			System.out.println("ddd");
		}
		if(s4.matches("^0\\.[0-9]{1,2}$|^0{1}$|^1{1}$|^1\\.[0]{1,2}$")) {//0-1的小数
			System.out.println("111");
		}
		
//		String strDate = "2018-10-01";
//		String s1 = "2018-10-10";
//		String s2 = "2019-10-10";
//		String ss = "WEr";
//		System.out.println(s1.compareTo(strDate) > 0);  //正确的时候返回 false,反之true
//    	System.out.println(strDate.compareTo(s2) > 0);  //正确的时候返回false
	
//		System.out.println(strDate.compareTo(s1) >= 0);  //正确的时候返回 false,反之true
//    	System.out.println(s2.compareTo(strDate) > 0);  //正确的时候返回false,反之true
		
		
//        if(!(strDate.compareTo(s1) >= 0 && s2.compareTo(strDate) > 0)) {
//        	System.out.println("ewewe");
//        }
//		
	
//		if(ss.equalsIgnoreCase("wer")) {
//			System.out.println(ss);
//		}
		
		
	}
	
	//校验未来日期
		public static boolean checkFutureDate(String strDate){
			//判断是否为未来日期
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");//可以方便地修改日期格式
			String nowdate = dateFormat.format(new Date()); 
//			String strdate = strDate.replace("/","-");
			if(nowdate.compareTo(strDate)>0){
				return false;
			}
			return true;
		}
		
	

	private static <T> T getType(T obj){
		Class<? extends Object> cls = obj.getClass();
		String date = DateTimeUtil.getCurrentDate();
        String time = DateTimeUtil.getCurrentTime();
		Method setMakeDate;
		try {
			setMakeDate = cls.getMethod("setMakeDate",String.class);
			setMakeDate.invoke(obj, date);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return obj;
		
	}
}
