package com.sinosoft.eflex.model.claim;

import com.sinosoft.eflex.model.BatchInsureInterface.DutyInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-12-06 10:18
 */
public class ClainRiskInfo {
    /* 险种编码 */
    private String RiskCode;
    /* 主险编码 */
    private String MainRiskCode;
    /* 交费间隔 */
    private String PayIntv;
    /* 保险年期 */
    private String Years;
    /* 保险期间 */
    private String InsuYear;
    /* 保险期间单位 */
    private String InsuYearFlag;
    /* 交费年期 */
    private String PayYears;
    /* 交费期间 */
    private String PayEndYear;
    /* 交费期间单位 */
    private String PayEndYearFlag;
    /* 领取间隔 */
    private String GetIntv;
    /* 领取期间 */
    private String GetYear;
    /* 领取期间单位 */
    private String GetYearFlag;
    /* 红利领取方式 */
    private String BonusGetMode;
    /* 份数 */
    private String Mult;
    /* 保费 */
    private String Prem;
    /* 保额 */
    private String Amnt;
    /* 缴费来源 */
    private String PaySource;
    /* 责任信息 */
    private List<DutyInfo> DutyList;
    /* 风险保额类型 */
    private String AmountType;
    /* 风险保额 */
    private String Amount;
    /*赔付人次*/
    private String getCount;
    /*险种名称*/
    private String riskName;
    /*赔付金额*/
    private String payAmount;

    public String getGetCount() {
        return getCount;
    }

    public void setGetCount(String getCount) {
        this.getCount = getCount;
    }

    public String getRiskName() {
        return riskName;
    }

    public void setRiskName(String riskName) {
        this.riskName = riskName;
    }

    public String getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(String payAmount) {
        this.payAmount = payAmount;
    }

    public String getAmountType() {
        return AmountType;
    }

    public void setAmountType(String amountType) {
        AmountType = amountType;
    }

    public String getAmount() {
        return Amount;
    }

    public void setAmount(String amount) {
        Amount = amount;
    }

    public String getRiskCode() {
        return RiskCode;
    }

    public void setRiskCode(String riskCode) {
        RiskCode = riskCode;
    }

    public String getMainRiskCode() {
        return MainRiskCode;
    }

    public void setMainRiskCode(String mainRiskCode) {
        MainRiskCode = mainRiskCode;
    }

    public String getPayIntv() {
        return PayIntv;
    }

    public void setPayIntv(String payIntv) {
        PayIntv = payIntv;
    }

    public String getYears() {
        return Years;
    }

    public void setYears(String years) {
        Years = years;
    }

    public String getInsuYear() {
        return InsuYear;
    }

    public void setInsuYear(String insuYear) {
        InsuYear = insuYear;
    }

    public String getInsuYearFlag() {
        return InsuYearFlag;
    }

    public void setInsuYearFlag(String insuYearFlag) {
        InsuYearFlag = insuYearFlag;
    }

    public String getPayYears() {
        return PayYears;
    }

    public void setPayYears(String payYears) {
        PayYears = payYears;
    }

    public String getPayEndYear() {
        return PayEndYear;
    }

    public void setPayEndYear(String payEndYear) {
        PayEndYear = payEndYear;
    }

    public String getPayEndYearFlag() {
        return PayEndYearFlag;
    }

    public void setPayEndYearFlag(String payEndYearFlag) {
        PayEndYearFlag = payEndYearFlag;
    }

    public String getGetIntv() {
        return GetIntv;
    }

    public void setGetIntv(String getIntv) {
        GetIntv = getIntv;
    }

    public String getGetYear() {
        return GetYear;
    }

    public void setGetYear(String getYear) {
        GetYear = getYear;
    }

    public String getGetYearFlag() {
        return GetYearFlag;
    }

    public void setGetYearFlag(String getYearFlag) {
        GetYearFlag = getYearFlag;
    }

    public String getBonusGetMode() {
        return BonusGetMode;
    }

    public void setBonusGetMode(String bonusGetMode) {
        BonusGetMode = bonusGetMode;
    }

    public String getMult() {
        return Mult;
    }

    public void setMult(String mult) {
        Mult = mult;
    }

    public String getPrem() {
        return Prem;
    }

    public void setPrem(String prem) {
        Prem = prem;
    }

    public String getAmnt() {
        return Amnt;
    }

    public void setAmnt(String amnt) {
        Amnt = amnt;
    }

    public String getPaySource() {
        return PaySource;
    }

    public void setPaySource(String paySource) {
        PaySource = paySource;
    }

    public List<DutyInfo> getDutyList() {
        return DutyList;
    }

    public void setDutyList(List<DutyInfo> dutyList) {
        DutyList = dutyList;
    }
}
