package com.sinosoft.eflex.model.policy;

public class InsureStatusNotice {
//	private PolicyDataContent dataContent;
//	private DataHead dataHead;
//	public PolicyDataContent getDataContent() {
//		return dataContent;
//	}
//	public void setDataContent(PolicyDataContent dataContent) {
//		this.dataContent = dataContent;
//	}
//	public DataHead getDataHead() {
//		return dataHead;
//	}
//	public void setDataHead(DataHead dataHead) {
//		this.dataHead = dataHead;
//	}
	/**
	 *   2020/6/8
	 *   自然人投保-承保反馈接口  修改
	 *       dataContent
	 *       dataHead  两部分合并到body中
	 */
    private DataBody body;
    private String code;
    private String message;

	public DataBody getBody() {
		return body;
	}

	public void setBody(DataBody body) {
		this.body = body;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}
}
