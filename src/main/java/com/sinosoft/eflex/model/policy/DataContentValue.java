package com.sinosoft.eflex.model.policy;

public class DataContentValue {
	private String applyName;
	private String policyCode;
	private String applyCode;
	private String pStateId;
	private String signDate;
	private String validateDate;
	private String endDate;
	private String insName;
	private String insFee;
	private String operateTime;
	private String insAmnt;
	
	public String getApplyName() {
		return applyName;
	}
	public void setApplyName(String applyName) {
		this.applyName = applyName;
	}
	public String getPolicyCode() {
		return policyCode;
	}
	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}
	public String getApplyCode() {
		return applyCode;
	}
	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}
	public String getpStateId() {
		return pStateId;
	}
	public void setpStateId(String pStateId) {
		this.pStateId = pStateId;
	}
	public String getSignDate() {
		return signDate;
	}
	public void setSignDate(String signDate) {
		this.signDate = signDate;
	}
	public String getValidateDate() {
		return validateDate;
	}
	public void setValidateDate(String validateDate) {
		this.validateDate = validateDate;
	}
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	public String getInsName() {
		return insName;
	}
	public void setInsName(String insName) {
		this.insName = insName;
	}
	public String getInsFee() {
		return insFee;
	}
	public void setInsFee(String insFee) {
		this.insFee = insFee;
	}
	public String getOperateTime() {
		return operateTime;
	}
	public void setOperateTime(String operateTime) {
		this.operateTime = operateTime;
	}
	public String getInsAmnt() {
		return insAmnt;
	}
	public void setInsAmnt(String insAmnt) {
		this.insAmnt = insAmnt;
	}
}
