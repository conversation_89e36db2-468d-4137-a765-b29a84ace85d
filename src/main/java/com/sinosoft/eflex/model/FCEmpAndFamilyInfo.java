package com.sinosoft.eflex.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2018-08-21 12:42
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FCEmpAndFamilyInfo {

    private String name;

    private String sex;

    private String relationship;

    private String birthDay;

    private String IDNo;

    private String mobilePhone;
    
    //国籍
    private String nativeplace;
    
    //证件有效期
    private String idTypeEndDate;

    private String IDType;

    private String occupationType;

    private String occupationCode;

   private String openBank;

   private String openAccount;

   private String email;

   private String relation;

   private String relationProve;
   
   private String joinMedProtect;

    public String getJoinMedProtect() {
	return joinMedProtect;
}

public void setJoinMedProtect(String joinMedProtect) {
	this.joinMedProtect = joinMedProtect;
}

	public String getRelationProve() {
        return relationProve;
    }

    public void setRelationProve(String relationProve) {
        this.relationProve = relationProve;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getBirthDay() {
        return birthDay;
    }

    public void setBirthDay(String birthDay) {
        this.birthDay = birthDay;
    }

    public String getIDNo() {
        return IDNo;
    }

    public void setIDNo(String IDNo) {
        this.IDNo = IDNo;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getIDType() {
        return IDType;
    }

    public void setIDType(String IDType) {
        this.IDType = IDType;
    }

    public String getOccupationType() {
        return occupationType;
    }

    public void setOccupationType(String occupationType) {
        this.occupationType = occupationType;
    }

    public String getOccupationCode() {
        return occupationCode;
    }

    public void setOccupationCode(String occupationCode) {
        this.occupationCode = occupationCode;
    }

    public String getOpenBank() {
        return openBank;
    }

    public void setOpenBank(String openBank) {
        this.openBank = openBank;
    }

    public String getOpenAccount() {
        return openAccount;
    }

    public void setOpenAccount(String openAccount) {
        this.openAccount = openAccount;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }
    

    public String getNativeplace() {
		return nativeplace;
	}

	public void setNativeplace(String nativeplace) {
		this.nativeplace = nativeplace;
	}

	public String getIdTypeEndDate() {
		return idTypeEndDate;
	}

	public void setIdTypeEndDate(String idTypeEndDate) {
		this.idTypeEndDate = idTypeEndDate;
	}

	@Override
    public String toString() {
        return "FCEmpAndFamilyInfo{" +
                "name='" + name + '\'' +
                ", sex='" + sex + '\'' +
                ", nativeplace='" + nativeplace + '\'' +
                ", birthDay='" + birthDay + '\'' +
                ", IDNo='" + IDNo + '\'' +
                ", idTypeEndDate='" + idTypeEndDate + '\'' +
                ", mobilePhone='" + mobilePhone + '\'' +
                ", IDType='" + IDType + '\'' +
                ", occupationType='" + occupationType + '\'' +
                ", occupationCode='" + occupationCode + '\'' +
                ", openBank='" + openBank + '\'' +
                ", openAccount='" + openAccount + '\'' +
                ", email='" + email + '\'' +
                ", relation='" + relation + '\'' +
                '}';
    }
}
