package com.sinosoft.eflex.model;

import java.util.Date;

public class FCUserToken {
    /**
     * 
     */
    private String token;

    /**
     * 
     */
    private String userNo;

    /**
     * 
     */
    private String isValid;

    /**
     * 
     */
    private String makeDate;

    /**
     * 
     */
    private String makeTime;

    /**
     * 
     */
    private String modifyDate;

    /**
     * 
     */
    private String modifyTime;

    /**
     * 
     * @return token 
     */
    public String getToken() {
        return token;
    }

    /**
     * 
     * @param token 
     */
    public void setToken(String token) {
        this.token = token;
    }

    /**
     * 
     * @return userNo 
     */
    public String getUserNo() {
        return userNo;
    }

    /**
     * 
     * @param userNo 
     */
    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    /**
     * 
     * @return IsValid 
     */
    public String getIsValid() {
        return isValid;
    }

    /**
     * 
     * @param isValid 
     */
    public void setIsValid(String isValid) {
        this.isValid = isValid;
    }

    public String getMakeDate() {
        return makeDate;
    }

    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    public String getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 
     * @return MakeTime 
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 
     * @param makeTime 
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }



    /**
     * 
     * @return ModifyTime 
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 
     * @param modifyTime 
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}