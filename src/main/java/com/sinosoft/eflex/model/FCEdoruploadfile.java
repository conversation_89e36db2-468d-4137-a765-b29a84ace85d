package com.sinosoft.eflex.model;

public class FCEdoruploadfile {
    /**
     * 保单号
     */
    private String grpContNo;

    /**
     * 批次号
     */
    private String batch;

    /**
     * 文件类型
     */
    private String docType;

    /**
     * 文件名称
     */
    private String fileName;

	/**
     * 本地路径
     */
    private String localPath;

    /**
     * FTP路径
     */
    private String ftpPath;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 创建日期
     */
    private String makeDate;

    /**
     * 创建时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 保单号
     * @return GrpContNo 保单号
     */
    public String getGrpContNo() {
        return grpContNo;
    }

    /**
     * 保单号
     * @param grpContNo 保单号
     */
    public void setGrpContNo(String grpContNo) {
        this.grpContNo = grpContNo;
    }

    /**
     * 批次号
     * @return Batch 批次号
     */
    public String getBatch() {
        return batch;
    }

    /**
     * 批次号
     * @param batch 批次号
     */
    public void setBatch(String batch) {
        this.batch = batch;
    }

    /**
     * 文件类型
     * @return DocType 文件类型
     */
    public String getDocType() {
        return docType;
    }

    /**
     * 文件类型
     * @param docType 文件类型
     */
    public void setDocType(String docType) {
        this.docType = docType;
    }

    /**
     * 文件名称
     * @param fileName 文件名称
     */
    public String getFileName() {
		return fileName;
	}
    
    /**
     * 文件名称
     * @param fileName 文件名称
     */

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
	
    /**
     * 本地路径
     * @return localPath 本地路径
     */
    public String getLocalPath() {
        return localPath;
    }

    /**
     * 本地路径
     * @param localPath 本地路径
     */
    public void setLocalPath(String localPath) {
        this.localPath = localPath;
    }

    /**
     * FTP路径
     * @return ftpPath FTP路径
     */
    public String getFtpPath() {
        return ftpPath;
    }

    /**
     * FTP路径
     * @param ftpPath FTP路径
     */
    public void setFtpPath(String ftpPath) {
        this.ftpPath = ftpPath;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 操作机构
     * @return OperatorCom 操作机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作机构
     * @param operatorCom 操作机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 创建日期
     * @return MakeDate 创建日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 创建日期
     * @param makeDate 创建日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 创建时间
     * @return MakeTime 创建时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 创建时间
     * @param makeTime 创建时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}