package com.sinosoft.eflex.model;

public class FcGrpRegAudit {
    /**
     * 企业注册流水号
     */
    private String grpRegNo;

    /**
     * HR客户号
     */
    private String contactNo;

    /**
     * 企业客户号
     */
    private String grpNo;

    /**
     * 审核结论
     */
    private String auditResult;
    
    /**
     *审核结论码值
     */
    private String auditResultName;
    
    

    public String getAuditResultName() {
		return auditResultName;
	}

	public void setAuditResultName(String auditResultName) {
		this.auditResultName = auditResultName;
	}

	/**
     * 审核意见
     */
    private String auditOpinion;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 录入日期
     */
    private String makeDate;

    /**
     * 录入时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 企业注册流水号
     * 
     * @return grpRegNo 企业注册流水号
     */
    public String getGrpRegNo() {
        return grpRegNo;
    }

    /**
     * 企业注册流水号
     * 
     * @param grpRegNo
     *            企业注册流水号
     */
    public void setGrpRegNo(String grpRegNo) {
        this.grpRegNo = grpRegNo;
    }

    /**
     * HR客户号
     * 
     * @return ContactNo HR客户号
     */
    public String getContactNo() {
        return contactNo;
    }

    /**
     * HR客户号
     * 
     * @param contactNo
     *            HR客户号
     */
    public void setContactNo(String contactNo) {
        this.contactNo = contactNo;
    }

    /**
     * 企业客户号
     * 
     * @return GrpNo 企业客户号
     */
    public String getGrpNo() {
        return grpNo;
    }

    /**
     * 企业客户号
     * 
     * @param grpNo
     *            企业客户号
     */
    public void setGrpNo(String grpNo) {
        this.grpNo = grpNo;
    }

    /**
     * 审核结论
     * 
     * @return AuditResult 审核结论
     */
    public String getAuditResult() {
        return auditResult;
    }

    /**
     * 审核结论
     * 
     * @param auditResult
     *            审核结论
     */
    public void setAuditResult(String auditResult) {
        this.auditResult = auditResult;
    }

    /**
     * 审核意见
     * 
     * @return AuditOpinion 审核意见
     */
    public String getAuditOpinion() {
        return auditOpinion;
    }

    /**
     * 审核意见
     * 
     * @param auditOpinion
     *            审核意见
     */
    public void setAuditOpinion(String auditOpinion) {
        this.auditOpinion = auditOpinion;
    }

    /**
     * 操作员
     * 
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * 
     * @param operator
     *            操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 操作机构
     * 
     * @return OperatorCom 操作机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作机构
     * 
     * @param operatorCom
     *            操作机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 录入时间
     * 
     * @return MakeTime 录入时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 录入时间
     * 
     * @param makeTime
     *            录入时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改时间
     * 
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * 
     * @param modifyTime
     *            修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getMakeDate() {
        return makeDate;
    }

    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    public String getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }
}