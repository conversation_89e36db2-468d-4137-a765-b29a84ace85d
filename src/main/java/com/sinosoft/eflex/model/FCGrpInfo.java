package com.sinosoft.eflex.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FCGrpInfo {
    /**
     * 企业客户号
     */
    private String grpNo;

    /**
     * 企业证件图片1
     */
    private String grpIDImage1;

    /**
     * 企业证件图片2
     */
    private String grpIDImage2;


    /**
     * 企业证件图片1
     */
    private String grpImageFront;

    /**
     * 企业证件图片2
     */
    private String grpImageBack;
    /**
     * 核心客户号
     */
    private String CGrpNo;

    /**
     * 企业名称
     */
    private String grpName;

    /**
     * 企业地址
     */
    private String grpAddRess;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 统一社会信用代码
     */
    private String unifiedsociCode;

    /**
     * 单位证件类型
     */
    private String grpIdType;

    private String grpIdTypeName;

    /**
     * 单位证件号码
     */
    private String grpIdNo;

    /**
     * 企业性质，投保人性质
     */
    private String grpType;

    /**
     * 企业组织形式
     */
    private String grpNatureType;

    /**
     * 企业性质名称
     */
    private String grpTypeName;

    /**
     * 开户账户名名称
     */
    private String accName;

    /**
     * 开户银行编码
     */
    private String grpBankCode;

    /**
     * 开户银行账号
     */
    private String grpBankAccNo;

    /**
     * 企业总人数
     */
    private Integer peoples;

    /**
     * 企业法人代表
     */
    private String corporationMan;

    /**
     * 法人证件图1
     */
    private String legIDImage1;

    /**
     * 法人证件图2
     */
    private String legIDImage2;

    /**
     * 法人影像件反面*
     */
    private String legalImgBack;
    /**
     * 法人影像件正面*
     */
    private String legalImgFront;

    /**
     * 企业注册电话
     */
    private String telphone;

    /**
     * 企业注册地址
     */
    private String regaddress;
    /**
     * * *
     * *企业注册地
     * *
     */
    private String grpRegisterAddress;

    /**
     * 营业期限
     */
    private String businessTerm;
    /**
     * 主营业务
     */
    private String businesses;

    /**
     * 所属行业
     */
    private String trade;

    /**
     * 所属行业名称
     */
    private String tradeName;

    /**
     * 企业邮箱
     */
    private String email;

    /**
     * 业务员工号
     */
    private String clientno;

    /**
     * 业务员名称
     */
    private String clientName;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 入机日期
     */
    private String makeDate;

    /**
     * 入机时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 证件有效起期
     */
    private String grpTypeStartDate;

    /**
     * 证件有效止期
     */
    private String grpTypeEndDate;

    /**
     * 企业成立日期
     */
    private String grpEstablishDate;

    /**
     * 企业规模类型
     */
    private String grpScaleType;

    private String grpScaleTypeName;

    /**
     * 参加社会统筹标志
     */
    private String sociologyPlanSign;

    private String sociologyPlanSignName;

    /**
     * 注册资本
     */
    private String registeredCapital;
    /**
     * 客户类别
     */
    private String grpCategory;
    /**
     * 企业法定代表人证件号
     */
    private String legID;
    /**
     * 企业法定代表人证件类型
     */
    private String legIDType;
    /**
     * 企业法定代表人性别
     */
    private String legSex;
    /**
     * 企业法定代表人证出生日期
     */
    private String legBirthday;
    /**
     * 企业法定代表人国籍
     */
    private String legNationality;
    /**
     * 企业法定代表人证件有效起期
     */
    private String legIDStartDate;
    /**
     * 企业法定代表人证件有效止期
     */
    private String legIDEndDate;

    public FCGrpInfo(String grpNo, Integer peoples) {
        this.grpNo = grpNo;
        this.peoples = peoples;
    }
}