package com.sinosoft.eflex.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(description = "企业经办人")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GrpContactInsertReq implements Serializable {


    @ApiModelProperty("企业编号")
    @NotBlank(message = "企业编号不能为空！")
    private String grpNo;

    @ApiModelProperty("姓名")
    @NotBlank(message = "姓名不能为空！")
    private String name;

    @ApiModelProperty("生日")
    @NotBlank(message = "出生日期不能为空！")
    private String birthDay;

    @ApiModelProperty("性别")
    @NotBlank(message = "性别不能为空！")
    private String sex;

    @ApiModelProperty("证件编码")
    @NotBlank(message = "证件编码不能为空！")
    private String idNo;

    @ApiModelProperty("证件类型")
    @NotBlank(message = "证件类型不能为空！")
    private String idType;

    @ApiModelProperty("手机号")
    @NotBlank(message = "手机号不能为空！")
    private String mobilePhone;

    @ApiModelProperty("手机号")
    @NotBlank(message = "手机号不能为空！")
    private String mobile;

    @ApiModelProperty("国籍")
    @NotBlank(message = "国籍不能为空！")
    private String nativeplace;

    @ApiModelProperty("有效启期")
    @NotBlank(message = "有效启期不能为空！")
    private String idTypeStartDate;

    @ApiModelProperty("有效止期")
    @NotBlank(message = "有效止期不能为空！")
    private String idTypeEndDate;

    @ApiModelProperty("邮箱")
    @NotBlank(message = "邮箱不能为空！")
    private String email;

    @ApiModelProperty("正面影像")
    @NotBlank(message = "正面影像不能为空！")
    private String idImage1;

    @ApiModelProperty("反面影像")
    @NotBlank(message = "反面影像不能为空！")
    private String idImage2;


    @ApiModelProperty("操作人员类别")
    @NotBlank(message = "sign不能为空！")
    private String sign;


}
