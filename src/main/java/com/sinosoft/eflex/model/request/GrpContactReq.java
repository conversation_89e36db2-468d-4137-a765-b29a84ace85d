package com.sinosoft.eflex.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(description = "企业经办人")
@Data
public class GrpContactReq implements Serializable {


    @ApiModelProperty("联系人编号")
    @NotBlank(message = "联系人编号不能为空！")
    private String contactNo;

    @ApiModelProperty("企业编号")
    @NotBlank(message = "企业编号不能为空！")
    private String grpNo;


}
