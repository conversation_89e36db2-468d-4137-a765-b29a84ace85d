package com.sinosoft.eflex.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 初审 审核 险种配置*
 *
 * <AUTHOR> *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "险种配置请求")
public class RiskConfigReq implements Serializable {


    @ApiModelProperty("福利编码")
    @NotBlank(message = "福利编码不能为空！")
    private String ensureCode;


    @ApiModelProperty("险种编码")
    @NotNull(message = "险种编码不能为空！")
    private String riskCode;

    @ApiModelProperty("福利编码")
    @NotNull(message = "手续费比率不能为空！")
    @Min(0)
    @Max(100)
    private BigDecimal feeRatio;

    @ApiModelProperty("佣金/服务津贴率")
    @NotNull(message = "佣金/服务津贴率不能为空！！")
    @Min(0)
    @Max(100)
    private BigDecimal commissionOrAllowanceRatio;


    @ApiModelProperty("是否有效类型")
    @NotBlank(message = "是否有效类型不能为空！")
    private String giftInsureSign;

    @ApiModelProperty("原保费")
    private BigDecimal originalPrem;

    @ApiModelProperty("分保标记")
    private String reinsuranceMark;

}
