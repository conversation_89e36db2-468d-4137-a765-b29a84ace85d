package com.sinosoft.eflex.model.request;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * 审核请求参数*
 *
 * <AUTHOR>
 */
@Data
public class ToReviewAdoptRequest implements Serializable {

    @NotBlank(message = "grpNo不能为空！")
    private String grpNo;

    @NotBlank(message = "customNo不能为空！")
    private String customNo;

    @NotBlank(message = "福利编码不能为空！")
    private String ensureCode;

    private Boolean greenInsurance;
}
