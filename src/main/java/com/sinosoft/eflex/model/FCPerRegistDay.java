package com.sinosoft.eflex.model;



public class FCPerRegistDay {
    /**
     * 注册期流水号
     */
    private String registDayNo;

    /**
     * 弹性福利编码
     */
    private String ensureCode;

    /**
     * 个人客户号
     */
    private String perNo;

    /**
     * 注册期开放日
     */
    private String  openDay;

    /**
     * 注册期结束日
     */
    private String closeDay;

    /**
     * 被开放人身份
     */
    private String personType;

    /**
     * 注册期是否有效 0--无效
 1--有效
     */
    private String isValidy;

    /**
     * 生效日
     */
    private String validydate;

    /**
     * 保单截止日
     */
    private String endDate;

    /**
     * 生效日类型 0--保单生效日
 1--保全生效日
            0--在保单生效日之前进入系统的员工
        1--保单生效之后，保全进来的员工或家人
     */
    private String validydateType;

    /**
     * 注册期做用人编号
     */
    private String registDayToManNo;

    /**
     * 员工补助保费
     */
    private Double staffGrpPrem;

    /**
     * 家属补助总保费
     */
    private Double familyGrpPrem;
    /**
     * 学生补助总保费
     */
    private Double studentGrpPrem;

    /**
     * 员工职级
     */
    private String levelCode;

    /**
     * 员工禁用 启用/禁用员工  0-启用状态  1-禁用状态；
     */
    private String lockState;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 生成日期
     */
    private String makeDate;

    /**
     * 生成时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 注册期流水号
     * @return RegistDayNo 注册期流水号
     */
    public String getRegistDayNo() {
        return registDayNo;
    }

    /**
     * 注册期流水号
     * @param registDayNo 注册期流水号
     */
    public void setRegistDayNo(String registDayNo) {
        this.registDayNo = registDayNo;
    }

    /**
     * 弹性福利编码
     * @return EnsureCode 弹性福利编码
     */
    public String getEnsureCode() {
        return ensureCode;
    }

    /**
     * 弹性福利编码
     * @param ensureCode 弹性福利编码
     */
    public void setEnsureCode(String ensureCode) {
        this.ensureCode = ensureCode;
    }

    /**
     * 个人客户号
     * @return PerNo 个人客户号
     */
    public String getPerNo() {
        return perNo;
    }

    /**
     * 个人客户号
     * @param perNo 个人客户号
     */
    public void setPerNo(String perNo) {
        this.perNo = perNo;
    }

    /**
     * 注册期开放日
     * @return OpenDay 注册期开放日
     */
    public String getOpenDay() {
        return openDay;
    }

    public void setOpenDay(String openDay) {
        this.openDay = openDay;
    }

    public String getCloseDay() {
        return closeDay;
    }

    public void setCloseDay(String closeDay) {
        this.closeDay = closeDay;
    }
    /**
     * 注册期开放日
     * @param openDay 注册期开放日
     */


    /**
     * 注册期结束日
     * @return CloseDay 注册期结束日
     */


    /**
     * 注册期结束日
     * @param closeDay 注册期结束日
     */


    /**
     * 被开放人身份
     * @return PersonType 被开放人身份
     */
    public String getPersonType() {
        return personType;
    }

    /**
     * 被开放人身份
     * @param personType 被开放人身份
     */
    public void setPersonType(String personType) {
        this.personType = personType;
    }

    /**
     * 注册期是否有效 0--无效
 1--有效
     * @return IsValidy 注册期是否有效 0--无效
 1--有效
     */
    public String getIsValidy() {
        return isValidy;
    }

    /**
     * 注册期是否有效 0--无效
 1--有效
     * @param isValidy 注册期是否有效 0--无效
 1--有效
     */
    public void setIsValidy(String isValidy) {
        this.isValidy = isValidy;
    }

    /**
     * 生效日
     * @return Validydate 生效日
     */


    /**
     * 生效日
     * @param validydate 生效日
     */


    /**
     * 保单截止日
     * @return EndDate 保单截止日
     */


    /**
     * 保单截止日
     * @param endDate 保单截止日
     */


    /**
     * 生效日类型 0--保单生效日
 1--保全生效日
            0--在保单生效日之前进入系统的员工
        1--保单生效之后，保全进来的员工或家人
     * @return ValidydateType 生效日类型 0--保单生效日
 1--保全生效日
            0--在保单生效日之前进入系统的员工
        1--保单生效之后，保全进来的员工或家人
     */
    public String getValidydateType() {
        return validydateType;
    }

    /**
     * 生效日类型 0--保单生效日
 1--保全生效日
            0--在保单生效日之前进入系统的员工
        1--保单生效之后，保全进来的员工或家人
     * @param validydateType 生效日类型 0--保单生效日
 1--保全生效日
            0--在保单生效日之前进入系统的员工
        1--保单生效之后，保全进来的员工或家人
     */
    public void setValidydateType(String validydateType) {
        this.validydateType = validydateType;
    }

    /**
     * 注册期做用人编号
     * @return RegistDayToManNo 注册期做用人编号
     */
    public String getRegistDayToManNo() {
        return registDayToManNo;
    }

    /**
     * 注册期做用人编号
     * @param registDayToManNo 注册期做用人编号
     */
    public void setRegistDayToManNo(String registDayToManNo) {
        this.registDayToManNo = registDayToManNo;
    }

    /**
     * 员工补助保费
     * @return StaffGrpPrem 员工补助保费
     */
    public Double getStaffGrpPrem() {
        return staffGrpPrem;
    }

    /**
     * 员工补助保费
     * @param staffGrpPrem 员工补助保费
     */
    public void setStaffGrpPrem(Double staffGrpPrem) {
        this.staffGrpPrem = staffGrpPrem;
    }

    /**
     * 家属补助总保费
     * @return FamilyGrpPrem 家属补助总保费
     */
    public Double getFamilyGrpPrem() {
        return familyGrpPrem;
    }

    /**
     * 家属补助总保费
     * @param familyGrpPrem 家属补助总保费
     */
    public void setFamilyGrpPrem(Double familyGrpPrem) {
        this.familyGrpPrem = familyGrpPrem;
    }

    public Double getStudentGrpPrem() {
		return studentGrpPrem;
	}

	public void setStudentGrpPrem(Double studentGrpPrem) {
		this.studentGrpPrem = studentGrpPrem;
	}

    /**
     * 员工职级
     * @return levelCode
     */
    public String getLevelCode() {
        return levelCode;
    }

    public void setLevelCode(String levelCode) {
        this.levelCode = levelCode;
    }




    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 操作机构
     * @return OperatorCom 操作机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作机构
     * @param operatorCom 操作机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 生成日期
     * @return MakeDate 生成日期
     */


    /**
     * 生成日期
     * @param makeDate 生成日期
     */


    /**
     * 生成时间
     * @return MakeTime 生成时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 生成时间
     * @param makeTime 生成时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return modifyDate 修改日期
     */
    public String getValidydate() {
        return validydate;
    }

    public void setValidydate(String validydate) {
        this.validydate = validydate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getMakeDate() {
        return makeDate;
    }

    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
    public String getLockState() {
        return lockState;
    }

    public void setLockState(String lockState) {
        this.lockState = lockState;
    }

}