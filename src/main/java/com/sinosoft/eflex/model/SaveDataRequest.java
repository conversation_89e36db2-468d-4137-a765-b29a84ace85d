package com.sinosoft.eflex.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/9/15 17:53
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SaveDataRequest  implements Serializable {
    private String activityCapitalCode;
    private String activityCapitalName;
    private int activityCapitalType;
    private PolicyAnalysisCapitalField policyAnalysisCapitalField;

}
