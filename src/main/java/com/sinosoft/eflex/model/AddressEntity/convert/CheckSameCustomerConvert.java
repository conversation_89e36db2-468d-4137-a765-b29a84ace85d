package com.sinosoft.eflex.model.AddressEntity.convert;

import com.sinosoft.eflex.model.AddressEntity.CheckSameCustomerRequest;
import com.sinosoft.eflex.model.FCEmpAndFamilyInfo;
import com.sinosoft.eflex.model.FCOrderInsured;
import com.sinosoft.eflex.model.FCPerson;
import com.sinosoft.eflex.rpc.model.CheckSameCustomerReqDTO;
import com.sinosoft.eflex.rpc.model.CoreCustomerInfoDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * 转换*
 *
 * <AUTHOR> *
 */
public class CheckSameCustomerConvert {

    public static CheckSameCustomerReqDTO convert(List<FCOrderInsured> insuredSet) {
        List<CoreCustomerInfoDTO> customerInfoList = new ArrayList<>();
        for (FCOrderInsured fcOrderInsured : insuredSet) {
            CoreCustomerInfoDTO coreCustomerInfoDTO = new CoreCustomerInfoDTO();
            coreCustomerInfoDTO.setCustomerName(fcOrderInsured.getName());
            coreCustomerInfoDTO.setCustomerSex(fcOrderInsured.getSex());
            coreCustomerInfoDTO.setCustomerBirthday(fcOrderInsured.getBirthday());
            coreCustomerInfoDTO.setCustomerIDType(fcOrderInsured.getIDType());
            coreCustomerInfoDTO.setCustomerIDNo(fcOrderInsured.getIDNo());
            coreCustomerInfoDTO.setMobile(fcOrderInsured.getMobilePhone());
            customerInfoList.add(coreCustomerInfoDTO);
        }
        return new CheckSameCustomerReqDTO(customerInfoList);
    }


    public static CheckSameCustomerRequest convert(FCPerson fcPerson) {
        return CheckSameCustomerRequest.builder()
                .customerBirthday(fcPerson.getBirthDate())
                .customerIDNo(fcPerson.getIDNo())
                .customerIDType(fcPerson.getIDType())
                .customerName(fcPerson.getName())
                .customerSex(fcPerson.getSex())
                .build();
    }

    public static CheckSameCustomerReqDTO convertFCPerson(FCPerson fcPerson) {
        List<CoreCustomerInfoDTO> customerInfoList = new ArrayList<>();
        CoreCustomerInfoDTO coreCustomerInfoDTO = new CoreCustomerInfoDTO();
        coreCustomerInfoDTO.setCustomerName(fcPerson.getName());
        coreCustomerInfoDTO.setCustomerSex(fcPerson.getSex());
        coreCustomerInfoDTO.setCustomerBirthday(fcPerson.getBirthDate());
        coreCustomerInfoDTO.setCustomerIDType(fcPerson.getIDType());
        coreCustomerInfoDTO.setCustomerIDNo(fcPerson.getIDNo());
        coreCustomerInfoDTO.setMobile(fcPerson.getMobilePhone());
        customerInfoList.add(coreCustomerInfoDTO);
        return new CheckSameCustomerReqDTO(customerInfoList);
    }
    public static CheckSameCustomerReqDTO convertFCEmpAndFamilyInfo(FCEmpAndFamilyInfo fcEmpAndFamilyInfo) {
        List<CoreCustomerInfoDTO> customerInfoList = new ArrayList<>();
        CoreCustomerInfoDTO coreCustomerInfoDTO = new CoreCustomerInfoDTO();
        coreCustomerInfoDTO.setCustomerName(fcEmpAndFamilyInfo.getName());
        coreCustomerInfoDTO.setCustomerSex(fcEmpAndFamilyInfo.getSex());
        coreCustomerInfoDTO.setCustomerBirthday(fcEmpAndFamilyInfo.getBirthDay());
        coreCustomerInfoDTO.setCustomerIDType(fcEmpAndFamilyInfo.getIDType());
        coreCustomerInfoDTO.setCustomerIDNo(fcEmpAndFamilyInfo.getIDNo());
        coreCustomerInfoDTO.setMobile(fcEmpAndFamilyInfo.getMobilePhone());
        customerInfoList.add(coreCustomerInfoDTO);
        return new CheckSameCustomerReqDTO(customerInfoList);
    }
}