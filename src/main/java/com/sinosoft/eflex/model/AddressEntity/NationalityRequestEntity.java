package com.sinosoft.eflex.model.AddressEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/1/12 14:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NationalityRequestEntity  implements Serializable {

    private final String transCode = "PRODUCT-SERVICE-HIGHRISKCOUNTRY-CHECK";

    /**
     * 国籍
     */
    private  String nativePlace ;

}
