package com.sinosoft.eflex.model.AddressEntity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/** 请求地址码表返回实体
 * <AUTHOR>
 * @Date 2023/1/12 15:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AddressDetail {
    /**
     * 码值
     */
    private String areaCode;
    private String areaName;
    private String areaLevelCode;
    private String areaLevelName;
    private String parentAreaCode;
    private String parentAreaName;
    private String topAreaCode;

}
