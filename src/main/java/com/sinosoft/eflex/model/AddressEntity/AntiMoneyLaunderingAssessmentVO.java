package com.sinosoft.eflex.model.AddressEntity;


import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/8/16 14:45
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AntiMoneyLaunderingAssessmentVO implements Serializable {
    /**
     * 是否是疑似反洗钱黑名单
     */
    @JsonProperty("suspectedBlackListCustomer")
    private Boolean suspectedBlackListCustomer;
    /**
     * 是否是反洗钱黑名单
     */
    @JsonProperty("blackListCustomer")
    private Boolean blackListCustomer;
    @JsonProperty("suspectedMatches")
    private Integer suspectedMatches;
    @JSONField(serializeUsing = ToStringSerializer.class)
    @JsonProperty("name")
    private String name;
    @JsonProperty("gender")
    private String gender;
    @JsonProperty("birthday")
    private String birthday;
    @JsonProperty("idType")
    private String idType;
    @JsonProperty("idNo")
    private String idNo;
    @JsonProperty("nationality")
    private String nationality;
    /**
     *当接口类型为非自然人时使用
     */
    @JsonProperty("privateRiskLevelDescribe")
    private String privateRiskLevelDescribe;
    /**
     *当接口类型为非自然人时使用
     */
    @JsonProperty("publicRiskLevelDescribe")
    private String publicRiskLevelDescribe;
    /**
     * 风险等级编码(同上两个字段)
     */
    @JsonProperty("privateRiskLevel")
    private String privateRiskLevel;
    @JsonProperty("publicRiskLevel")
    private String publicRiskLevel;

}