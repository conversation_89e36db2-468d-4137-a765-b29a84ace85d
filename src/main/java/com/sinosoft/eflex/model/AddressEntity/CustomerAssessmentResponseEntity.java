package com.sinosoft.eflex.model.AddressEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/8/16 14:44
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CustomerAssessmentResponseEntity implements Serializable {
    private String code;
    private CustomerAssessmentResponseDataEntity data;
    private String message;
    private long timestamp;
}
