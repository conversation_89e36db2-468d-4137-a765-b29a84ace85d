package com.sinosoft.eflex.model.AddressEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/8/18 10:21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CheckCustomerVO  implements Serializable {
    /**
     * 疑似黑名单
     */
    private  String suspectedBlack;
    /**
     * 黑名单
     */
    private  String black;

    /**
     * 自然人风险等级
     */
    private  String privateRiskLevel;
    /**
     * 非自然人风险等级
     */
    private  String publicRiskLevel;



}
