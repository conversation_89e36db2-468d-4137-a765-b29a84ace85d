package com.sinosoft.eflex.model.AddressEntity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/16 14:45
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CustomerAssessmentResponseDataEntity implements Serializable {
    private String businessCode;
    private String manageOrg;
    @JsonProperty("antiMoneyLaunderingAssessmentList")
    private List<AntiMoneyLaunderingAssessmentVO> antiMoneyLaunderingAssessmentList;


}
