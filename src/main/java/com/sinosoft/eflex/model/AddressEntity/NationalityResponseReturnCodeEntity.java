package com.sinosoft.eflex.model.AddressEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/7/11 09:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NationalityResponseReturnCodeEntity implements Serializable {

    private String name;

    private String desc;

    private String code;

    private String service;

}
