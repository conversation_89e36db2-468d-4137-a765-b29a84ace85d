package com.sinosoft.eflex.model.AddressEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/11/7 14:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CheckSameCustomerResponse implements Serializable {

    private ReturnCode returnCode;
    private String body;
    private String cid;
    private String systime;
    private String elapsed;
    private String code;
    private String message;


}
