package com.sinosoft.eflex.model.AddressEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/12/21 09:24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CoreRequestBody implements Serializable {
    /**
     * 网关校验参数,交易编码
     */
    private String transCode;
    /**
     * 网关校验参数，交易流水号
     */
    private String transNo;
    /**
     * 网关校验参数，交易来源
     */
    private String transSource;
    /**
     * 网关交易参数，交易时间
     */
    private String transTime;
    private String transSN;
}
