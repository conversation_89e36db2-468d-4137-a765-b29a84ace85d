package com.sinosoft.eflex.model.AddressEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/8/16 14:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EvaluationCustomer implements Serializable {

    private String name;
    private String gender;
    private String birthday;
    private String idType;
    private String idNo;
    private String nationality;
    private String businessNo;
}
