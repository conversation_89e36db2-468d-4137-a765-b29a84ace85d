package com.sinosoft.eflex.model.AddressEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/7/11 09:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NationalityResponseEntity implements Serializable {

    private NationalityResponseReturnCodeEntity returnCode;

    private String body;

    private String cid;

    private String systime;

    private String elapsed;

    private String code;

    private String message;

}
