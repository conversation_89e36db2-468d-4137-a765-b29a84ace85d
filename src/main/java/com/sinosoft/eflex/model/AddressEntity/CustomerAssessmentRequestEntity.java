package com.sinosoft.eflex.model.AddressEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/16 14:27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CustomerAssessmentRequestEntity  implements Serializable {
    private String requestId;
    private String userChannel;
    private String businessCode;
    private String riskManagementTypes;
    private String manageOrg;
    private List<EvaluationCustomer> evaluationCustomerList;
}
