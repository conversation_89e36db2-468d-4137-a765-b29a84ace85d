package com.sinosoft.eflex.model.AddressEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VerificationResponse implements Serializable {
    private boolean success; 
    private String message; 
    private String code; 
    private DataResult data;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DataResult implements Serializable{
        private boolean allPass;
        private boolean allNoPass;
        private List<FailVerify> failVerifies;
        private List<FailVerify> successVerifies;

    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class FailVerify implements Serializable{
        private String result; 
        private String description; 
        private String requestId; 
        private String name; 
        private String idCard; 
        private String gender; 
        private String nation; 
        private String birthday; 
        private String address;
        }

}