package com.sinosoft.eflex.model;

import java.util.Date;

public class FCOrderPay {
    /**
     * 订单缴费明细号
     */
    private String payNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 缴费类型  01-企业代扣代缴 02-个人实时批扣 03-个人批扣 04-企业缴纳
     */
    private String payType;

    /**
     * 总保费
     */
    private Double totalPrem;

    /**
     * 企业缴纳
     */
    private Double grpPrem;

    /**
     * 个人缴费
     */
    private Double personPrem;

    /**
     * 批扣状态 01-已发起个人批扣； 02-批扣成功； 03-批扣失败
     */
    private String batchPayStatus;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 管理机构
     */
    private String operatorCom;

    /**
     * 入机日期
     */
    private Date makeDate;

    /**
     * 入机时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private Date modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 订单缴费明细号
     * @return PayNo 订单缴费明细号
     */
    public String getPayNo() {
        return payNo;
    }

    /**
     * 订单缴费明细号
     * @param payNo 订单缴费明细号
     */
    public void setPayNo(String payNo) {
        this.payNo = payNo;
    }

    /**
     * 订单号
     * @return OrderNo 订单号
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * 订单号
     * @param orderNo 订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 缴费类型  01-企业代扣代缴 02-个人实时批扣 03-个人批扣 04-企业缴纳
     * @return PayType 缴费类型  01-企业代扣代缴 02-个人实时批扣 03-个人批扣 04-企业缴纳
     */
    public String getPayType() {
        return payType;
    }

    /**
     * 缴费类型  01-企业代扣代缴 02-个人实时批扣 03-个人批扣 04-企业缴纳
     * @param payType 缴费类型  01-企业代扣代缴 02-个人实时批扣 03-个人批扣 04-企业缴纳
     */
    public void setPayType(String payType) {
        this.payType = payType;
    }

    /**
     * 总保费
     * @return TotalPrem 总保费
     */
    public Double getTotalPrem() {
        return totalPrem;
    }

    /**
     * 总保费
     * @param totalPrem 总保费
     */
    public void setTotalPrem(Double totalPrem) {
        this.totalPrem = totalPrem;
    }

    /**
     * 企业缴纳
     * @return GrpPrem 企业缴纳
     */
    public Double getGrpPrem() {
        return grpPrem;
    }

    /**
     * 企业缴纳
     * @param grpPrem 企业缴纳
     */
    public void setGrpPrem(Double grpPrem) {
        this.grpPrem = grpPrem;
    }

    /**
     * 个人缴费
     * @return PersonPrem 个人缴费
     */
    public Double getPersonPrem() {
        return personPrem;
    }

    /**
     * 个人缴费
     * @param personPrem 个人缴费
     */
    public void setPersonPrem(Double personPrem) {
        this.personPrem = personPrem;
    }

    /**
     * 批扣状态 01-已发起个人批扣； 02-批扣成功； 03-批扣失败
     * @return BatchPayStatus 批扣状态 01-已发起个人批扣； 02-批扣成功； 03-批扣失败
     */
    public String getBatchPayStatus() {
        return batchPayStatus;
    }

    /**
     * 批扣状态 01-已发起个人批扣； 02-批扣成功； 03-批扣失败
     * @param batchPayStatus 批扣状态 01-已发起个人批扣； 02-批扣成功； 03-批扣失败
     */
    public void setBatchPayStatus(String batchPayStatus) {
        this.batchPayStatus = batchPayStatus;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 管理机构
     * @return OperatorCom 管理机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 管理机构
     * @param operatorCom 管理机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 入机日期
     * @return MakeDate 入机日期
     */
    public Date getMakeDate() {
        return makeDate;
    }

    /**
     * 入机日期
     * @param makeDate 入机日期
     */
    public void setMakeDate(Date makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 入机时间
     * @return MakeTime 入机时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 入机时间
     * @param makeTime 入机时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public Date getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}