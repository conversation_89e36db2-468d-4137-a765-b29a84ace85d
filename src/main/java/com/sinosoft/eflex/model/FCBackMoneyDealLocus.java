package com.sinosoft.eflex.model;

import java.util.Date;

public class FCBackMoneyDealLocus {
    /**
     * 轨迹流水号
     */
    private String locusNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付流水号
     */
    private String payFlowNo;

    /**
     * 退款状态  0-退款成功  1-退款中； 2-退款失败； 3-银行卡退款中
     */
    private String payBackStatus;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 管理机构
     */
    private String operatorCom;

    /**
     * 入机日期
     */
    private Date makeDate;

    /**
     * 入机时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private Date modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 轨迹流水号
     * @return LocusNo 轨迹流水号
     */
    public String getLocusNo() {
        return locusNo;
    }

    /**
     * 轨迹流水号
     * @param locusNo 轨迹流水号
     */
    public void setLocusNo(String locusNo) {
        this.locusNo = locusNo;
    }

    /**
     * 订单号
     * @return OrderNo 订单号
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * 订单号
     * @param orderNo 订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 支付流水号
     * @return PayFlowNo 支付流水号
     */
    public String getPayFlowNo() {
        return payFlowNo;
    }

    /**
     * 支付流水号
     * @param payFlowNo 支付流水号
     */
    public void setPayFlowNo(String payFlowNo) {
        this.payFlowNo = payFlowNo;
    }

    /**
     * 退款状态  0-退款成功  1-退款中； 2-退款失败； 3-银行卡退款中
     * @return PayBackStatus 退款状态  0-退款成功  1-退款中； 2-退款失败； 3-银行卡退款中
     */
    public String getPayBackStatus() {
        return payBackStatus;
    }

    /**
     * 退款状态  0-退款成功  1-退款中； 2-退款失败； 3-银行卡退款中
     * @param payBackStatus 退款状态  0-退款成功  1-退款中； 2-退款失败； 3-银行卡退款中
     */
    public void setPayBackStatus(String payBackStatus) {
        this.payBackStatus = payBackStatus;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 管理机构
     * @return OperatorCom 管理机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 管理机构
     * @param operatorCom 管理机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 入机日期
     * @return MakeDate 入机日期
     */
    public Date getMakeDate() {
        return makeDate;
    }

    /**
     * 入机日期
     * @param makeDate 入机日期
     */
    public void setMakeDate(Date makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 入机时间
     * @return MakeTime 入机时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 入机时间
     * @param makeTime 入机时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public Date getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}