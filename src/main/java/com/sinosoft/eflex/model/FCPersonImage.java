package com.sinosoft.eflex.model;

import lombok.Data;

@Data
public class FCPersonImage {
    /**
     * 
     */
    private String imageNo;

    /**
     * 子订单号
     */
    private String orderItemNo;

    /**
     * 个人客户号
     */
    private String personId;

    /**
     * 与被保人关系
     */
    private String relation;

    /**
     * 图片类型  0801 证件0802 病历 0803 其他 0804 签名
     */
    private String imageType;

    /**
     * 图片顺序
     */
    private String imageOrder;

    /**
     * 图片路径
     */
    private String imageUrl;
    
    /**
     * 图片Ftp路径
     */
    private String imageFtpUrl;

    /**
     * 
     */
    private String operator;

    /**
     * 
     */
    private String operatorCom;

    /**
     * 
     */
    private String makeDate;

    /**
     * 
     */
    private String makeTime;

    /**
     * 
     */
    private String modifyDate;

    /**
     * 
     */
    private String modifyTime;
    
    /**
     * 
     */
    private String imageBase64;

    /**
     *签名影像生成的PrtNo
     */
    private String imageNumber;
    /**
     *签名影像生成的PrtNo
     */
    private Boolean status;

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    public String getImageNumber() {
        return imageNumber;
    }

    public void setImageNumber(String imageNumber) {
        this.imageNumber = imageNumber;
    }

    /**
     * 
     * @return ImageNo 
     */
    public String getImageNo() {
        return imageNo;
    }

    /**
     * 
     * @param imageNo 
     */
    public void setImageNo(String imageNo) {
        this.imageNo = imageNo;
    }

    /**
     * 子订单号
     * @return OrderItemNo 子订单号
     */
    public String getOrderItemNo() {
        return orderItemNo;
    }

    /**
     * 子订单号
     * @param orderItemNo 子订单号
     */
    public void setOrderItemNo(String orderItemNo) {
        this.orderItemNo = orderItemNo;
    }

    /**
     * 个人客户号
     * @return PersonId 个人客户号
     */
    public String getPersonId() {
        return personId;
    }

    /**
     * 个人客户号
     * @param personId 个人客户号
     */
    public void setPersonId(String personId) {
        this.personId = personId;
    }

    /**
     * 与被保人关系
     * @return Relation 与被保人关系
     */
    public String getRelation() {
        return relation;
    }

    /**
     * 与被保人关系
     * @param relation 与被保人关系
     */
    public void setRelation(String relation) {
        this.relation = relation;
    }

    /**
     * 图片类型  0801 证件0802 病历 0803 其他 0804 签名
     * @return ImageType 图片类型  0801 证件0802 病历 0803 其他 0804 签名
     */
    public String getImageType() {
        return imageType;
    }

    /**
     * 图片类型  0801 证件0802 病历 0803 其他 0804 签名
     * @param imageType 图片类型  0801 证件0802 病历 0803 其他 0804 签名
     */
    public void setImageType(String imageType) {
        this.imageType = imageType;
    }

    /**
     * 图片顺序
     * @return ImageOrder 图片顺序
     */
    public String getImageOrder() {
        return imageOrder;
    }

    /**
     * 图片顺序
     * @param imageOrder 图片顺序
     */
    public void setImageOrder(String imageOrder) {
        this.imageOrder = imageOrder;
    }

    /**
     * 图片路径
     * @return ImageUrl 图片路径
     */
    public String getImageUrl() {
        return imageUrl;
    }

    /**
     * 图片路径
     * @param imageUrl 图片路径
     */
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getImageFtpUrl() {
		return imageFtpUrl;
	}

	public void setImageFtpUrl(String imageFtpUrl) {
		this.imageFtpUrl = imageFtpUrl;
	}

	/**
     * 
     * @return Operator 
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 
     * @param operator 
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 
     * @return OperatorCom 
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 
     * @param operatorCom 
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 
     * @return MakeDate 
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 
     * @param makeDate 
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 
     * @return MakeTime 
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 
     * @param makeTime 
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 
     * @return ModifyDate 
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 
     * @param modifyDate 
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 
     * @return ModifyTime 
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 
     * @param modifyTime 
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

	public String getImageBase64() {
		return imageBase64;
	}

	public void setImageBase64(String imageBase64) {
		this.imageBase64 = imageBase64;
	}
    
}