package com.sinosoft.eflex.model.insurePlan;


import lombok.Data;

import java.util.Date;

/**
 * 计划书险种配置表
 */
@Data
public class InsurePlanRiskConfig {
    /**
     * 主键ID
     */
    private int id;
    /**
     * 险种编号
     */
    private String riskCode;
    /**
     * 险种名称
     */
    private String riskName;
    /**
     * 计划书编号
     */
    private String insureplanCode;
    /**
     * 基本保额
     */
    private double amount;

    /**
     * 保费
     */
    private double prem;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人ID
     */
    private String creatorId;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 修改人id
     */
    private String modifierId;
    /**
     * 修改人
     */
    private String modifier;
    /**
     * 是否删除(1:是，0:否)
     */
    private int isDeleted;


}
