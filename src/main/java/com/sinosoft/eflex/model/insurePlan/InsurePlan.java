package com.sinosoft.eflex.model.insurePlan;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * 团险赠险计划书基本信息表
 */

@Data
public class InsurePlan {

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 企业客户号
     */
    private String grpNo;
    /**
     * 计划书编号
     */
    private String insureplanCode;
    /**
     * 计划书名称
     */
    private String insureplanName;
    /**
     * 被保人年龄起始
     */
    private String insuredAgeStart;
    /**
     * 被保人年龄结束
     */
    private String insuredAgeEnd;
    /**
     * 职业类别
     */
    private String occupationType;
    /**
     * 条款
     */
    private String clause;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 状态(0：上架，1：下架)）
     */
    private int status;
    /**
     * 修改时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 创建人ID
     */
    private String creatorId;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 修改人id
     */
    private String modifierId;
    /**
     * 修改人
     */
    private String modifier;
    /**
     * 是否删除(1:是，0:否)
     */
    private int isDeleted;
    /**
     * 所有关联险种名称
     */
    private String riskNames;
    /**
     * 所有险种保费总和
     */
    private Double amountCount;

    /**
     * 保险期间类型 1-极短期，2-一年期
     */
    private String insuredPeriodType;

    /**
     * 保险期间
     */
    private String insuredPeriod;

    /**
     * 总保费
     */
    private Double premCount;
}
