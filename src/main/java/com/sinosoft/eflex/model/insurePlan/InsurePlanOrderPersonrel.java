package com.sinosoft.eflex.model.insurePlan;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 计划书订单人员关联表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InsurePlanOrderPersonrel {

    /**
     * 主键ID
     */
    private int id ;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * unionId
     */
    private String unionId;
    /**
     * openid
     */
    private String openId;

    /**
     * 计划书编号
     */
    private String insureplanCode;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 姓名
     */
    private String name;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 证件类型
     */
    private String credentialType;
    /**
     * 证件类型名称
     */
    private String credentialTypeName;
    /**
     * 证件号码
     */
    private String credentialNo;
    /**
     * 证件有效期
     */
    private String credentialPeriod;
    /**
     * 修改时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 创建人ID
     */
    private String creatorId;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 修改人id
     */
    private String modifierId;
    /**
     * 修改人
     */
    private String modifier;
    /**
     * 是否删除(1:是，0:否)
     */
    private int isDeleted;



}
