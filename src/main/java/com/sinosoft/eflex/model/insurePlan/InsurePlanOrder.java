package com.sinosoft.eflex.model.insurePlan;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * 计划书订单表
 */
@Data
public class InsurePlanOrder {

    /**
     * 主键ID
     */
    private int id;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 计划书编号
     */
    private String insureplanCode;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 订单关联状态（1：是，0：否）
     */
    private int status;
    /**
     * 修改时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 创建人ID
     */
    private String creatorId;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 修改人id
     */
    private String modifierId;
    /**
     * 修改人
     */
    private String modifier;
    /**
     * 是否删除(1:是，0:否)
     */
    private String isDeleted;

}
