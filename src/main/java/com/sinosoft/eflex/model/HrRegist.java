package com.sinosoft.eflex.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HrRegist implements Serializable {

    /**
     * 企业编号
     */
    private String grpNo;

    /**
     * 企业联系人编号
     */
    private String grpContactNo;

    /**
     * 旧企业名称
     */
    private String oldGrpName;

    /**
     * 旧法人代表
     */
    private String oldCorporationMan;

    /**
     * 旧企业证件类型
     */
    private String oldGrpIdType;

    /**
     * 旧企业证件号码
     */
    private String oldGrpIdNo;

    public String getOldGrpName() {
        return oldGrpName;
    }

    public void setOldGrpName(String oldGrpName) {
        this.oldGrpName = oldGrpName;
    }

    public String getOldCorporationMan() {
        return oldCorporationMan;
    }

    public void setOldCorporationMan(String oldCorporationMan) {
        this.oldCorporationMan = oldCorporationMan;
    }

    public String getOldGrpIdType() {
        return oldGrpIdType;
    }

    public void setOldGrpIdType(String oldGrpIdType) {
        this.oldGrpIdType = oldGrpIdType;
    }

    public String getOldGrpIdNo() {
        return oldGrpIdNo;
    }

    public void setOldGrpIdNo(String oldGrpIdNo) {
        this.oldGrpIdNo = oldGrpIdNo;
    }

    // 流水号
    private String registSN;
    /* 个人信息 */
    /* 姓名 */
    private String name;
    /* 手机号 */
    private String mobilePhone;
    /* 邮箱 */
    private String email;
    /* 证件类型 */
    private String idType;
    /* 证件号 */
    private String idNo;
    /* 性别 */
    private String sex;
    /* 出生日期 */
    private String birthday;
    /* 所属部门 */
    private String department;
    /* 企业信息 */
    /* 企业名称 */
    private String grpName;
    /* 邮编 */
    private String zipCode;
    /* 企业地址 */
    private String grpAddress;
    /* 企业注册地
     *  */
    private String grpRegisterAddress;
    /* 企业证件类型 */
    private String grpIdType;
    /* 企业证件号 */
    private String grpIdNo;
    /* 统一社会信用代码 */
    private String unifiedsociCode;
    /* 企业性质 */
    private String grpType;
    /**
     * 企业组织形式
     */
    private String grpNatureType;
    /* 开户名称 */
    private String accName;
    /* 开户编码 */
    private String grpBankcode;
    /* 开户账号 */
    private String grpBankaccno;
    /* 注册电话 */
    private String telphone;
    /* 注册地址 */
    private String regAddress;
    /* 业务员工号 */
    private String clientNo;
    // 证件图片1
    private String idImage1;
    // 证件图片2
    private String idImage2;
    //法人证件图1
    private String legIDImage1;
    //法人证件图2
    private String legIDImage2;
    // 企业证件图片1
    private String grpIDImage1;
    // 企业证件图片2
    private String grpIDImage2;
    /**
     * 企业证件图片1
     */
    private String grpImageFront;

    /**
     * 企业证件图片2
     */
    private String grpImageBack;

    private String idCardFront;

    /**
     * 法人影像件反面*
     */
    private String legalImgBack;
    /**
     * 法人影像件正面*
     */
    private String legalImgFront;

    /**
     * 证件图片2
     */
    private String idCardBack;

    // 企业法人代表
    private String corporationMan;
    /**
     * 营业期限
     */
    private String businessTerm;
    /**
     * 主营业务
     */
    private String businesses;

    /**
     * 所属行业
     */
    private String trade;

    // 审核状态
    private String auditResult;

    //oldIdno
    private String oldIdno;


    //国籍
    private String nativeplace;

    //证件有效期
    private String idTypeEndDate;

    //证件有效期
    private String idTypeStartDate;
    /**
     * 证件有效起期
     */
    private String grpTypeStartDate;

    /**
     * 证件有效止期
     */
    private String grpTypeEndDate;

    /**
     * 企业成立日期
     */
    private String grpEstablishDate;

    /**
     * 企业规模类型
     */
    private String grpScaleType;

    /**
     * 参加社会统筹标志
     */
    private String sociologyPlanSign;

    /**
     * 注册资本
     */
    private String registeredCapital;
    /**
     * 客户类别
     */
    private String grpCategory;


    /**
     * 企业法定代表人证件号
     */
    private String legID;
    /**
     * 企业法定代表人证件类型
     */
    private String legIDType;
    /**
     * 企业法定代表人性别
     */
    private String legSex;
    /**
     * 企业法定代表人证出生日期
     */
    private String legBirthday;
    /**
     * 企业法定代表人国籍
     */
    private String legNationality;
    /**
     * 企业法定代表人证件有效起期
     */
    private String legIDStartDate;
    /**
     * 企业法定代表人证件有效止期
     */
    private String legIDEndDate;

    public String getLegID() {
        return legID;
    }

    public void setLegID(String legID) {
        this.legID = legID;
    }

    public String getLegIDType() {
        return legIDType;
    }

    public void setLegIDType(String legIDType) {
        this.legIDType = legIDType;
    }

    public String getLegSex() {
        return legSex;
    }

    public void setLegSex(String legSex) {
        this.legSex = legSex;
    }

    public String getLegBirthday() {
        return legBirthday;
    }

    public void setLegBirthday(String legBirthday) {
        this.legBirthday = legBirthday;
    }

    public String getLegNationality() {
        return legNationality;
    }

    public void setLegNationality(String legNationality) {
        this.legNationality = legNationality;
    }

    public String getLegIDStartDate() {
        return legIDStartDate;
    }

    public void setLegIDStartDate(String legIDStartDate) {
        this.legIDStartDate = legIDStartDate;
    }

    public String getLegIDEndDate() {
        return legIDEndDate;
    }

    public void setLegIDEndDate(String legIDEndDate) {
        this.legIDEndDate = legIDEndDate;
    }

    public String getGrpTypeStartDate() {
        return grpTypeStartDate;
    }

    public void setGrpTypeStartDate(String grpTypeStartDate) {
        this.grpTypeStartDate = grpTypeStartDate;
    }

    public String getGrpTypeEndDate() {
        return grpTypeEndDate;
    }

    public void setGrpTypeEndDate(String grpTypeEndDate) {
        this.grpTypeEndDate = grpTypeEndDate;
    }

    public String getGrpEstablishDate() {
        return grpEstablishDate;
    }

    public void setGrpEstablishDate(String grpEstablishDate) {
        this.grpEstablishDate = grpEstablishDate;
    }

    public String getGrpScaleType() {
        return grpScaleType;
    }

    public void setGrpScaleType(String grpScaleType) {
        this.grpScaleType = grpScaleType;
    }

    public String getSociologyPlanSign() {
        return sociologyPlanSign;
    }

    public void setSociologyPlanSign(String sociologyPlanSign) {
        this.sociologyPlanSign = sociologyPlanSign;
    }

    public String getRegisteredCapital() {
        return registeredCapital;
    }

    public void setRegisteredCapital(String registeredCapital) {
        this.registeredCapital = registeredCapital;
    }

    public String getGrpCategory() {
        return grpCategory;
    }

    public void setGrpCategory(String grpCategory) {
        this.grpCategory = grpCategory;
    }

    public String getNativeplace() {
        return nativeplace;
    }

    public void setNativeplace(String nativeplace) {
        this.nativeplace = nativeplace;
    }

    public String getIdTypeEndDate() {
        return idTypeEndDate;
    }

    public void setIdTypeEndDate(String idTypeEndDate) {
        this.idTypeEndDate = idTypeEndDate;
    }

    public String getOldIdno() {
        return oldIdno;
    }

    public void setOldIdno(String oldIdno) {
        this.oldIdno = oldIdno;
    }

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 入机日期
     */
    private String makeDate;

    /**
     * 入机时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;
    /**
     * 修改时间
     */
    private String modifyTime;
    /*审核意见*/
    private String auditOpinion;
    /**
     * 审核结论码值
     */
    /*员工总人数*/
    private Integer peoples;
    /*股东业务*/
    private String shareBusiness;
    /*股东名称*/
    private String shareholdersName;

    public String getShareBusiness() {
        return shareBusiness;
    }

    public void setShareBusiness(String shareBusiness) {
        this.shareBusiness = shareBusiness;
    }

    public String getShareholdersName() {
        return shareholdersName;
    }

    public void setShareholdersName(String shareholdersName) {
        this.shareholdersName = shareholdersName;
    }

    public Integer getPeoples() {
        return peoples;
    }

    public void setPeoples(Integer peoples) {
        this.peoples = peoples;
    }

    private String auditResultName;

    public String getIdImage1() {
        return idImage1;
    }

    public void setIdImage1(String idImage1) {
        this.idImage1 = idImage1;
    }

    public String getIdImage2() {
        return idImage2;
    }

    public void setIdImage2(String idImage2) {
        this.idImage2 = idImage2;
    }

    public String getLegIDImage1() {
        return legIDImage1;
    }

    public void setLegIDImage1(String legIDImage1) {
        this.legIDImage1 = legIDImage1;
    }

    public String getLegIDImage2() {
        return legIDImage2;
    }

    public void setLegIDImage2(String legIDImage2) {
        this.legIDImage2 = legIDImage2;
    }

    public String getGrpIDImage1() {
        return grpIDImage1;
    }

    public void setGrpIDImage1(String grpIDImage1) {
        this.grpIDImage1 = grpIDImage1;
    }

    public String getGrpIDImage2() {
        return grpIDImage2;
    }

    public void setGrpIDImage2(String grpIDImage2) {
        this.grpIDImage2 = grpIDImage2;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getGrpName() {
        return grpName;
    }

    public void setGrpName(String grpName) {
        this.grpName = grpName;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getGrpAddress() {
        return grpAddress;
    }

    public void setGrpAddress(String grpAddress) {
        this.grpAddress = grpAddress;
    }

    public String getUnifiedsociCode() {
        return unifiedsociCode;
    }

    public void setUnifiedsociCode(String unifiedsociCode) {
        this.unifiedsociCode = unifiedsociCode;
    }

    public String getGrpType() {
        return grpType;
    }

    public void setGrpType(String grpType) {
        this.grpType = grpType;
    }

    public String getAccName() {
        return accName;
    }

    public void setAccName(String accName) {
        this.accName = accName;
    }

    public String getGrpBankcode() {
        return grpBankcode;
    }

    public void setGrpBankcode(String grpBankcode) {
        this.grpBankcode = grpBankcode;
    }

    public String getGrpBankaccno() {
        return grpBankaccno;
    }

    public void setGrpBankaccno(String grpBankaccno) {
        this.grpBankaccno = grpBankaccno;
    }

    public String getTelphone() {
        return telphone;
    }

    public void setTelphone(String telphone) {
        this.telphone = telphone;
    }

    public String getRegAddress() {
        return regAddress;
    }

    public void setRegAddress(String regAddress) {
        this.regAddress = regAddress;
    }

    public String getClientNo() {
        return clientNo;
    }

    public void setClientNo(String clientNo) {
        this.clientNo = clientNo;
    }

    public String getCorporationMan() {
        return corporationMan;
    }

    public void setCorporationMan(String corporationMan) {
        this.corporationMan = corporationMan;
    }

    public String getCheckStatus() {
        return auditResult;
    }

    public void setCheckStatus(String checkStatus) {
        this.auditResult = checkStatus;
    }

    public String getRegistSN() {
        return registSN;
    }

    public void setRegistSN(String registSN) {
        this.registSN = registSN;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperatorCom() {
        return operatorCom;
    }

    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    public String getMakeDate() {
        return makeDate;
    }

    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    public String getMakeTime() {
        return makeTime;
    }

    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    public String getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getAuditOpinion() {
        return auditOpinion;
    }

    public void setAuditOpinion(String auditOpinion) {
        this.auditOpinion = auditOpinion;
    }


    public String getAuditResultName() {
        return auditResultName;
    }

    @Override
    public String toString() {
        return "HrRegist{" +
                "registSN='" + registSN + '\'' +
                ", name='" + name + '\'' +
                ", mobilePhone='" + mobilePhone + '\'' +
                ", idType='" + idType + '\'' +
                ", idNo='" + idNo + '\'' +
                ", sex='" + sex + '\'' +
                ", birthday='" + birthday + '\'' +
                ", grpName='" + grpName + '\'' +
                ", zipCode='" + zipCode + '\'' +
                ", grpAddress='" + grpAddress + '\'' +
                ", unifiedsociCode='" + unifiedsociCode + '\'' +
                ", grpType='" + grpType + '\'' +
                ", accName='" + accName + '\'' +
                ", grpBankcode='" + grpBankcode + '\'' +
                ", grpBankaccno='" + grpBankaccno + '\'' +
                ", telphone='" + telphone + '\'' +
                ", regAddress='" + regAddress + '\'' +
                ", clientNo='" + clientNo + '\'' +
                ", idImage1='" + idImage1 + '\'' +
                ", idImage2='" + idImage2 + '\'' +
                ", legIDImage1='" + legIDImage1 + '\'' +
                ", legIDImage2='" + legIDImage2 + '\'' +
                ", grpIDImage1='" + grpIDImage1 + '\'' +
                ", grpIDImage2='" + grpIDImage2 + '\'' +
                ", corporationMan='" + corporationMan + '\'' +
                ", businessTerm='" + businessTerm + '\'' +
                ", trade='" + trade + '\'' +
                ", auditResult='" + auditResult + '\'' +
                ", oldIdno='" + oldIdno + '\'' +
                ", nativeplace='" + nativeplace + '\'' +
                ", idTypeEndDate='" + idTypeEndDate + '\'' +
                ", grpTypeStartDate='" + grpTypeStartDate + '\'' +
                ", grpTypeEndDate='" + grpTypeEndDate + '\'' +
                ", grpEstablishDate='" + grpEstablishDate + '\'' +
                ", grpScaleType='" + grpScaleType + '\'' +
                ", sociologyPlanSign='" + sociologyPlanSign + '\'' +
                ", registeredCapital='" + registeredCapital + '\'' +
                ", grpCategory='" + grpCategory + '\'' +
                ", operator='" + operator + '\'' +
                ", operatorCom='" + operatorCom + '\'' +
                ", makeDate='" + makeDate + '\'' +
                ", makeTime='" + makeTime + '\'' +
                ", modifyDate='" + modifyDate + '\'' +
                ", modifyTime='" + modifyTime + '\'' +
                ", auditOpinion='" + auditOpinion + '\'' +
                ", peoples=" + peoples +
                ", shareBusiness='" + shareBusiness + '\'' +
                ", shareholdersName='" + shareholdersName + '\'' +
                ", auditResultName='" + auditResultName + '\'' +
                ", isRightState='" + isRightState + '\'' +
                '}';
    }

    public void setAuditResultName(String auditResultName) {
        this.auditResultName = auditResultName;
    }

    public String getBusinessTerm() {
        return businessTerm;
    }

    public void setBusinessTerm(String businessTerm) {
        this.businessTerm = businessTerm;
    }

    public String getTrade() {
        return trade;
    }

    public void setTrade(String trade) {
        this.trade = trade;
    }

    private String isRightState;

    public String getAuditResult() {
        return auditResult;
    }

    public void setAuditResult(String auditResult) {
        this.auditResult = auditResult;
    }

    public String getIsRightState() {
        return isRightState;
    }

    public void setIsRightState(String isRightState) {
        this.isRightState = isRightState;
    }

    public String getGrpIdType() {
        return grpIdType;
    }

    public void setGrpIdType(String grpIdType) {
        this.grpIdType = grpIdType;
    }

    public String getGrpIdNo() {
        return grpIdNo;
    }

    public void setGrpIdNo(String grpIdNo) {
        this.grpIdNo = grpIdNo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getIdTypeStartDate() {
        return idTypeStartDate;
    }

    public void setIdTypeStartDate(String idTypeStartDate) {
        this.idTypeStartDate = idTypeStartDate;
    }

    public String getGrpNo() {
        return grpNo;
    }

    public void setGrpNo(String grpNo) {
        this.grpNo = grpNo;
    }

    public String getGrpContactNo() {
        return grpContactNo;
    }

    public void setGrpContactNo(String grpContactNo) {
        this.grpContactNo = grpContactNo;
    }

    public String getGrpRegisterAddress() {
        return grpRegisterAddress;
    }

    public void setGrpRegisterAddress(String grpRegisterAddress) {
        this.grpRegisterAddress = grpRegisterAddress;
    }
}
