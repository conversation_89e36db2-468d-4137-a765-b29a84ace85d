package com.sinosoft.eflex.model;

import java.math.BigDecimal;
import java.util.Date;

public class FcDutyGradeOptionalAmountInfo {
    /**
     * 保额档次编码
     */
    private String amountGrageCode;

    /**
     * 可选责任编码
     */
    private String optDutyCode;

    /**
     * 保额
     */
    private Double amnt;

    /**
     * 保费
     */
    private Double prem;

    /**
     * 最大赔付天数
     */
    private BigDecimal maxGetDay;

    /**
     * 管理员
     */
    private String operator;

    /**
     * 管理机构
     */
    private String operatorCom;

    /**
     * 入机日期
     */
    private String makeDate;

    /**
     * 入机时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 保额档次编码
     * @return AmountGrageCode 保额档次编码
     */
    public String getAmountGrageCode() {
        return amountGrageCode;
    }

    /**
     * 保额档次编码
     * @param amountGrageCode 保额档次编码
     */
    public void setAmountGrageCode(String amountGrageCode) {
        this.amountGrageCode = amountGrageCode;
    }

    /**
     * 可选责任编码
     * @return OptDutyCode 可选责任编码
     */
    public String getOptDutyCode() {
        return optDutyCode;
    }

    /**
     * 可选责任编码
     * @param optDutyCode 可选责任编码
     */
    public void setOptDutyCode(String optDutyCode) {
        this.optDutyCode = optDutyCode;
    }

    /**
     * 保额
     * @return Amnt 保额
     */
    public Double getAmnt() {
        return amnt;
    }

    /**
     * 保额
     * @param amnt 保额
     */
    public void setAmnt(Double amnt) {
        this.amnt = amnt;
    }

    /**
     * 保费
     * @return Prem 保费
     */
    public Double getPrem() {
        return prem;
    }

    /**
     * 保费
     * @param prem 保费
     */
    public void setPrem(Double prem) {
        this.prem = prem;
    }


    /**
     * 管理员
     * @return Operator 管理员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 管理员
     * @param operator 管理员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 管理机构
     * @return OperatorCom 管理机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 管理机构
     * @param operatorCom 管理机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 入机日期
     * @return MakeDate 入机日期
     */
    public String  getMakeDate() {
        return makeDate;
    }

    /**
     * 入机日期
     * @param makeDate 入机日期
     */
    public void setMakeDate(String  makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 入机时间
     * @return MakeTime 入机时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 入机时间
     * @param makeTime 入机时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String  getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public BigDecimal getMaxGetDay() {
        return maxGetDay;
    }

    public void setMaxGetDay(BigDecimal maxGetDay) {
        this.maxGetDay = maxGetDay;
    }
}