package com.sinosoft.eflex.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 订单表实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FCOrder implements Serializable {

	private static final long serialVersionUID = -2224276685899827188L;

	/**
	 * 订单号
	 */
	private String orderNo;

	/**
	 * 订单状态: 01-待提交核心 02-已提交核心
	 */
	private String orderStatus;

	/**
	 * 订单类型: 01固定套餐 02年度福利 03日常福利
	 */
	private String orderType;

	/**
	 * 注册期起始日
	 */
	private String openDay;

	/**
	 * 注册期结束日
	 */
	private String closeDay;

	/**
	 * 团体订单号
	 */
	private String grpOrderNo;

	/**
	 * 企业客户号
	 */
	private String grpNo;

	/**
	 * 个人客户号
	 */
	private String perNo;

	/**
	 * 个人投保人编号
	 */
	private String perAppNo;

	/**
	 * 消费明细号
	 */
	private String costNo;

	/**
	 * 订单公共要素详情编号
	 */
	private String pubEleNo;

	/**
	 * 订单提交日期
	 */
	private String commitDate;

	/**
	 * 订单生效日期
	 */
	private String effectDate;

	/**
	 * 是否填写个人告知: 0-可能需要填写 1-不需要填写个人告知 2-需要填写未填写完 3-已填写完 4-不需要填告知，需要温馨提示健康告知
	 */
	private String isInfo;

	/**
	 * 订单来源: 01-PC浏览器网页投保 02-微信端投保
	 */
	private String orderSource;

	/**
	 * 业务员工号
	 */
	private String clientNo;

	/**
	 * 订单是否锁定：0-未锁定  1-已锁定
	 */
	private String isLock;

	/**
	 * 订单标识  1-基础单生成的订单  2-移动端生成的订单
	 */
	private String orderSign;

	/**
	 * 操作员
	 */
	private String operator;

	/**
	 * 操作机构
	 */
	private String operatorCom;

	/**
	 * 生成日期
	 */
	private String makeDate;

	/**
	 * 生成时间
	 */
	private String makeTime;

	/**
	 * 修改日期
	 */
	private String modifyDate;

	/**
	 * 修改时间
	 */
	private String modifyTime;

	/**
	 * 子订单表
	 */
	private List<FCOrderItem> fcOrderItems;

	/**
	 * 个人客户信息表
	 */
	private FCPerInfo fcPerInfo;

	/**
	 * 订单号
	 * 
	 * @return OrderNo 订单号
	 */
	public String getOrderNo() {
		return orderNo;
	}

	/**
	 * 订单号
	 * 
	 * @param orderNo
	 *            订单号
	 */
	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	/**
	 * 订单状态: 01-待提交核心 02-已提交核心
	 * 
	 * @return OrderStatus 订单状态: 01-待提交核心 02-已提交核心
	 */
	public String getOrderStatus() {
		return orderStatus;
	}

	/**
	 * 订单状态: 01-待提交核心 02-已提交核心
	 * 
	 * @param orderStatus
	 *            订单状态: 01-待提交核心 02-已提交核心
	 */
	public void setOrderStatus(String orderStatus) {
		this.orderStatus = orderStatus;
	}

	/**
	 * 订单类型: 01固定套餐 02年度福利 03日常福利
	 * 
	 * @return OrderType 订单类型: 01固定套餐 02年度福利 03日常福利
	 */
	public String getOrderType() {
		return orderType;
	}

	/**
	 * 订单类型: 01固定套餐 02年度福利 03日常福利
	 * 
	 * @param orderType
	 *            订单类型: 01固定套餐 02年度福利 03日常福利
	 */
	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	/**
	 * 注册期起始日
	 * 
	 * @return OpenDay 注册期起始日
	 */
	public String getOpenDay() {
		return openDay;
	}

	/**
	 * 注册期起始日
	 * 
	 * @param openDay
	 *            注册期起始日
	 */
	public void setOpenDay(String openDay) {
		this.openDay = openDay;
	}

	/**
	 * 注册期结束日
	 * 
	 * @return CloseDay 注册期结束日
	 */
	public String getCloseDay() {
		return closeDay;
	}

	/**
	 * 注册期结束日
	 * 
	 * @param closeDay
	 *            注册期结束日
	 */
	public void setCloseDay(String closeDay) {
		this.closeDay = closeDay;
	}

	/**
	 * 团体订单号
	 * 
	 * @return GrpOrderNo 团体订单号
	 */
	public String getGrpOrderNo() {
		return grpOrderNo;
	}

	/**
	 * 团体订单号
	 * 
	 * @param grpOrderNo
	 *            团体订单号
	 */
	public void setGrpOrderNo(String grpOrderNo) {
		this.grpOrderNo = grpOrderNo;
	}

	/**
	 * 企业客户号
	 * 
	 * @return GrpNo 企业客户号
	 */
	public String getGrpNo() {
		return grpNo;
	}

	/**
	 * 企业客户号
	 * 
	 * @param grpNo
	 *            企业客户号
	 */
	public void setGrpNo(String grpNo) {
		this.grpNo = grpNo;
	}

	/**
	 * 个人客户号
	 * 
	 * @return PerNo 个人客户号
	 */
	public String getPerNo() {
		return perNo;
	}

	/**
	 * 个人客户号
	 * 
	 * @param perNo
	 *            个人客户号
	 */
	public void setPerNo(String perNo) {
		this.perNo = perNo;
	}

	/**
	 * 个人投保人编号
	 * 
	 * @return PerAppNo 个人投保人编号
	 */
	public String getPerAppNo() {
		return perAppNo;
	}

	/**
	 * 个人投保人编号
	 * 
	 * @param perAppNo
	 *            个人投保人编号
	 */
	public void setPerAppNo(String perAppNo) {
		this.perAppNo = perAppNo;
	}

	/**
	 * 消费明细号
	 * 
	 * @return CostNo 消费明细号
	 */
	public String getCostNo() {
		return costNo;
	}

	/**
	 * 消费明细号
	 * 
	 * @param costNo
	 *            消费明细号
	 */
	public void setCostNo(String costNo) {
		this.costNo = costNo;
	}

	/**
	 * 订单公共要素详情编号
	 * 
	 * @return PubEleNo 订单公共要素详情编号
	 */
	public String getPubEleNo() {
		return pubEleNo;
	}

	/**
	 * 订单公共要素详情编号
	 * 
	 * @param pubEleNo
	 *            订单公共要素详情编号
	 */
	public void setPubEleNo(String pubEleNo) {
		this.pubEleNo = pubEleNo;
	}

	/**
	 * 订单提交日期
	 * 
	 * @return CommitDate 订单提交日期
	 */
	public String getCommitDate() {
		return commitDate;
	}

	/**
	 * 订单提交日期
	 * 
	 * @param commitDate
	 *            订单提交日期
	 */
	public void setCommitDate(String commitDate) {
		this.commitDate = commitDate;
	}

	/**
	 * 订单生效日期
	 * 
	 * @return EffectDate 订单生效日期
	 */
	public String getEffectDate() {
		return effectDate;
	}

	/**
	 * 订单生效日期
	 * 
	 * @param effectDate
	 *            订单生效日期
	 */
	public void setEffectDate(String effectDate) {
		this.effectDate = effectDate;
	}

	/**
	 * 是否填写个人告知: 0-可能需要填写 1-不需要填写个人告知 2-需要填写未填写完 3-已填写完 4-不需要填告知，需要温馨提示健康告知
	 * 
	 * @return IsInfo 是否填写个人告知: 0-可能需要填写 1-不需要填写个人告知 2-需要填写未填写完 3-已填写完
	 *         4-不需要填告知，需要温馨提示健康告知
	 */
	public String getIsInfo() {
		return isInfo;
	}

	/**
	 * 是否填写个人告知: 0-可能需要填写 1-不需要填写个人告知 2-需要填写未填写完 3-已填写完 4-不需要填告知，需要温馨提示健康告知
	 * 
	 * @param isInfo
	 *            是否填写个人告知: 0-可能需要填写 1-不需要填写个人告知 2-需要填写未填写完 3-已填写完
	 *            4-不需要填告知，需要温馨提示健康告知
	 */
	public void setIsInfo(String isInfo) {
		this.isInfo = isInfo;
	}

	/**
	 * 订单来源: 01-PC浏览器网页投保 02-微信端投保
	 * 
	 * @return OrderSource 订单来源: 01-PC浏览器网页投保 02-微信端投保
	 */
	public String getOrderSource() {
		return orderSource;
	}

	/**
	 * 订单来源: 01-PC浏览器网页投保 02-微信端投保
	 * 
	 * @param orderSource
	 *            订单来源: 01-PC浏览器网页投保 02-微信端投保
	 */
	public void setOrderSource(String orderSource) {
		this.orderSource = orderSource;
	}

	/**
	 * 业务员工号
	 * 
	 * @return Clientno 业务员工号
	 */
	public String getClientNo() {
		return clientNo;
	}

	/**
	 * 业务员工号
	 * 
	 * @param clientNo
	 *            业务员工号
	 */
	public void setClientNo(String clientNo) {
		this.clientNo = clientNo;
	}

	/**
	 * 操作员
	 * 
	 * @return Operator 操作员
	 */
	public String getOperator() {
		return operator;
	}

	/**
	 * 操作员
	 * 
	 * @param operator
	 *            操作员
	 */
	public void setOperator(String operator) {
		this.operator = operator;
	}

	/**
	 * 操作机构
	 * 
	 * @return OperatorCom 操作机构
	 */
	public String getOperatorCom() {
		return operatorCom;
	}

	/**
	 * 操作机构
	 * 
	 * @param operatorCom
	 *            操作机构
	 */
	public void setOperatorCom(String operatorCom) {
		this.operatorCom = operatorCom;
	}

	/**
	 * 生成日期
	 * 
	 * @return MakeDate 生成日期
	 */
	public String getMakeDate() {
		return makeDate;
	}

	/**
	 * 生成日期
	 * 
	 * @param makeDate
	 *            生成日期
	 */
	public void setMakeDate(String makeDate) {
		this.makeDate = makeDate;
	}

	/**
	 * 生成时间
	 * 
	 * @return MakeTime 生成时间
	 */
	public String getMakeTime() {
		return makeTime;
	}

	/**
	 * 生成时间
	 * 
	 * @param makeTime
	 *            生成时间
	 */
	public void setMakeTime(String makeTime) {
		this.makeTime = makeTime;
	}

	/**
	 * 修改日期
	 * 
	 * @return ModifyDate 修改日期
	 */
	public String getModifyDate() {
		return modifyDate;
	}

	/**
	 * 修改日期
	 * 
	 * @param modifyDate
	 *            修改日期
	 */
	public void setModifyDate(String modifyDate) {
		this.modifyDate = modifyDate;
	}

	/**
	 * 修改时间
	 * 
	 * @return ModifyTime 修改时间
	 */
	public String getModifyTime() {
		return modifyTime;
	}

	/**
	 * 修改时间
	 * 
	 * @param modifyTime
	 *            修改时间
	 */
	public void setModifyTime(String modifyTime) {
		this.modifyTime = modifyTime;
	}

	/**
	 * @return the fcOrderItems
	 */
	public List<FCOrderItem> getFcOrderItems() {
		return fcOrderItems;
	}

	/**
	 * @param fcOrderItems
	 *            the fcOrderItems to set
	 */
	public void setFcOrderItems(List<FCOrderItem> fcOrderItems) {
		this.fcOrderItems = fcOrderItems;
	}

	/**
	 * @return the fcPerInfo
	 */
	public FCPerInfo getFcPerInfo() {
		return fcPerInfo;
	}

	/**
	 * @param fcPerInfo
	 *            the fcPerInfo to set
	 */
	public void setFcPerInfo(FCPerInfo fcPerInfo) {
		this.fcPerInfo = fcPerInfo;
	}

	/**
	 * toString
	 */
	@Override
	public String toString() {
		return "FCOrder [orderNo=" + orderNo + ", orderStatus=" + orderStatus + ", orderType=" + orderType
				+ ", openDay=" + openDay + ", closeDay=" + closeDay + ", grpOrderNo=" + grpOrderNo + ", grpNo=" + grpNo
				+ ", perNo=" + perNo + ", perAppNo=" + perAppNo + ", costNo=" + costNo + ", pubEleNo=" + pubEleNo
				+ ", commitDate=" + commitDate + ", effectDate=" + effectDate + ", isInfo=" + isInfo + ", orderSource="
				+ orderSource + ", clientNo=" + clientNo + ", operator=" + operator + ", operatorCom=" + operatorCom
				+ ", makeDate=" + makeDate + ", makeTime=" + makeTime + ", modifyDate=" + modifyDate + ", modifyTime="
				+ modifyTime + ", fcOrderItems=" + fcOrderItems + ", fcPerInfo=" + fcPerInfo + "]";
	}

	public String getIsLock() {
		return isLock;
	}

	public void setIsLock(String isLock) {
		this.isLock = isLock;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getOrderSign() {
		return orderSign;
	}

	public void setOrderSign(String orderSign) {
		this.orderSign = orderSign;
	}

	public FCOrder(String orderStatus, String grpNo, String perNo) {
		this.orderStatus = orderStatus;
		this.grpNo = grpNo;
		this.perNo = perNo;
	}
}