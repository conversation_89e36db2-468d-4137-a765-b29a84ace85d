package com.sinosoft.eflex.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FCPerson {
    private String edorType;
    private String oldIdNo;
    /**
     * 个人编码
     */
    private String personID;
    /**
     * 与投保人关系
     */
    private String relationship;
    /**
     * perinfo表对应证件号
     */
    private String perIDNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private String sex;
    
    /**
     * 国籍
     */
    private String nativeplace;
    
    private String nativeplaceName;

    public String getNativeplaceName() {
		return nativeplaceName;
	}

	public void setNativeplaceName(String nativeplaceName) {
		this.nativeplaceName = nativeplaceName;
	}

	public String getNativeplace() {
		return nativeplace;
	}

	public void setNativeplace(String nativeplace) {
		this.nativeplace = nativeplace;
	}

	/**
     * 出生日期
     */
    private String birthDate;

    /**
     * 证件类型
     */
    @JsonProperty("iDType")
    private String IDType;

    /**
     * 证件号
     */
    @JsonProperty("iDNo")
    private String IDNo;
    
    /**
     * 证件有效期
     */
    private String idTypeEndDate;

    /**
     * 移动电话
     */
    private String mobilePhone;

    /**
     * 固定电话
     */
    private String phone;

    /**
     * 职业类别
     */
    private String occupationType;
    private String occupationTypeName;


    /**
     * 职业代码
     */
    private String occupationCode;
    private String occupationName;
    /**
     * 是否参加医保:
            0-否
            1-是
     */
    private String joinMedProtect;

    /**
     * 医保类型
     */
    private String medProtectType;

    /**
     * 邮箱
     */

    @JsonProperty("email")
    private String EMail;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 开户银行
     */
    private String openBank;

    /**
     * 开户账户
     */
    private String openAccount;

    /**
     * 
     */
    private String operator;

    /**
     * 
     */
    private String operatorCom;

    /**
     * 
     */
    private String makeDate;

    /**
     * 
     */
    private String makeTime;

    /**
     * 
     */
    private String modifyDate;

    /**
     * 
     */
    private String modifyTime;


    private FCOrderInsured fcOrderInsured;

    /*与员工关系*/
    private String relation;
    /*与员工关系*/
    private String relationName;

    private String relationProve;
    /**
     * 证件类型名称
     */
    private String IDTypeName;
    //省
    private String province;
    //市
    private String city;
    //县
    private String county;
    //详细地址
    private String detaileAddress;
    //投保人年收入(万元）
    private String yearSalary;

    public String getOccupationTypeName() {
        return occupationTypeName;
    }

    public void setOccupationTypeName(String occupationTypeName) {
        this.occupationTypeName = occupationTypeName;
    }
    public String getRelationProve() {
        return relationProve;
    }

    public void setRelationProve(String relationProve) {
        this.relationProve = relationProve;
    }

    public String getOccupationName() {
        return occupationName;
    }

    public void setOccupationName(String occupationName) {
        this.occupationName = occupationName;
    }

    public String getIDTypeName() {
        return IDTypeName;
    }

    public void setIDTypeName(String IDTypeName) {
        this.IDTypeName = IDTypeName;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }

    public String getRelationName() {
        return relationName;
    }

    public void setRelationName(String relationName) {
        this.relationName = relationName;
    }

    public FCOrderInsured getFcOrderInsured() {
        return fcOrderInsured;
    }

    public void setFcOrderInsured(FCOrderInsured fcOrderInsured) {
        this.fcOrderInsured = fcOrderInsured;
    }

    /**
     * 个人编码
     * @return PersonID 个人编码
     */
    public String getPersonID() {
        return personID;
    }

    /**
     * 个人编码
     * @param personID 个人编码
     */
    public void setPersonID(String personID) {
        this.personID = personID;
    }

    /**
     * 姓名
     * @return Name 姓名
     */
    public String getName() {
        return name;
    }

    /**
     * 姓名
     * @param name 姓名
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 性别
     * @return Sex 性别
     */
    public String getSex() {
        return sex;
    }

    /**
     * 性别
     * @param sex 性别
     */
    public void setSex(String sex) {
        this.sex = sex;
    }

    /**
     * 出生日期
     * @return BirthDate 出生日期
     */
    public String getBirthDate() {
        return birthDate;
    }

    /**
     * 出生日期
     * @param birthDate 出生日期
     */
    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
    }

    /**
     * 证件类型
     * @return IDType 证件类型
     */
    public String getIDType() {
        return IDType;
    }

    /**
     * 证件类型
     * @param IDType 证件类型
     */
    public void setIDType(String IDType) {
        this.IDType = IDType;
    }

    /**
     * 证件号
     * @return IDNo 证件号
     */
    public String getIDNo() {
        return IDNo;
    }

    /**
     * 证件号
     * @param IDNo 证件号
     */
    public void setIDNo(String IDNo) {
        this.IDNo = IDNo;
    }

 		/**
     * 证件有效期
     * @return
     */
    public String getIdTypeEndDate() {
        return idTypeEndDate;
    }

    /**
     * 证件有效期
     * @param idTypeEndDate
     */
    public void setIdTypeEndDate(String idTypeEndDate) {
        this.idTypeEndDate = idTypeEndDate;
    }

    /**
     * 移动电话
     * @return MobilePhone 移动电话
     */
    public String getMobilePhone() {
        return mobilePhone;
    }

    /**
     * 移动电话
     * @param mobilePhone 移动电话
     */
    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    /**
     * 固定电话
     * @return Phone 固定电话
     */
    public String getPhone() {
        return phone;
    }

    /**
     * 固定电话
     * @param phone 固定电话
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * 职业类别
     * @return OccupationType 职业类别
     */
    public String getOccupationType() {
        return occupationType;
    }

    /**
     * 职业类别
     * @param occupationType 职业类别
     */
    public void setOccupationType(String occupationType) {
        this.occupationType = occupationType;
    }

    /**
     * 职业代码
     * @return OccupationCode 职业代码
     */
    public String getOccupationCode() {
        return occupationCode;
    }

    /**
     * 职业代码
     * @param occupationCode 职业代码
     */
    public void setOccupationCode(String occupationCode) {
        this.occupationCode = occupationCode;
    }

    /**
     * 是否参加医保:
            0-否
            1-是
     * @return JoinMedProtect 是否参加医保:
            0-否
            1-是
     */
    public String getJoinMedProtect() {
        return joinMedProtect;
    }

    /**
     * 是否参加医保:
            0-否
            1-是
     * @param joinMedProtect 是否参加医保:
            0-否
            1-是
     */
    public void setJoinMedProtect(String joinMedProtect) {
        this.joinMedProtect = joinMedProtect;
    }

    /**
     * 医保类型
     * @return MedProtectType 医保类型
     */
    public String getMedProtectType() {
        return medProtectType;
    }

    /**
     * 医保类型
     * @param medProtectType 医保类型
     */
    public void setMedProtectType(String medProtectType) {
        this.medProtectType = medProtectType;
    }

    /**
     * 邮箱
     * @return EMail 邮箱
     */
    public String getEMail() {
        return EMail;
    }

    /**
     * 邮箱
     * @param EMail 邮箱
     */
    public void setEMail(String EMail) {
        this.EMail = EMail;
    }

    /**
     * 邮编
     * @return ZipCode 邮编
     */
    public String getZipCode() {
        return zipCode;
    }

    /**
     * 邮编
     * @param zipCode 邮编
     */
    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    /**
     * 详细地址
     * @return Address 详细地址
     */
    public String getAddress() {
        return address;
    }

    /**
     * 详细地址
     * @param address 详细地址
     */
    public void setAddress(String address) {
        this.address = address;
    }

    /**
     * 开户银行
     * @return OpenBank 开户银行
     */
    public String getOpenBank() {
        return openBank;
    }

    /**
     * 开户银行
     * @param openBank 开户银行
     */
    public void setOpenBank(String openBank) {
        this.openBank = openBank;
    }

    /**
     * 开户账户
     * @return OpenAccount 开户账户
     */
    public String getOpenAccount() {
        return openAccount;
    }

    /**
     * 开户账户
     * @param openAccount 开户账户
     */
    public void setOpenAccount(String openAccount) {
        this.openAccount = openAccount;
    }

    /**
     * 
     * @return Operator 
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 
     * @param operator 
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 
     * @return OperatorCom 
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 
     * @param operatorCom 
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 
     * @return MakeDate 
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 
     * @param makeDate 
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 
     * @return MakeTime 
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 
     * @param makeTime 
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 
     * @return ModifyDate 
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 
     * @param modifyDate 
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 
     * @return ModifyTime 
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 
     * @param modifyTime 
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getPerIDNo() {
        return perIDNo;
    }

    public void setPerIDNo(String perIDNo) {
        this.perIDNo = perIDNo;
    }

    /**
     * 职级
     */
    private String levelCode;
    /**
     * 是否退休
     */
    private String retirement;

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	public String getDetaileAddress() {
		return detaileAddress;
	}

	public void setDetaileAddress(String detaileAddress) {
		this.detaileAddress = detaileAddress;
	}

	public String getLevelCode() {
		return levelCode;
	}

	public void setLevelCode(String levelCode) {
		this.levelCode = levelCode;
	}

	public String getRetirement() {
		return retirement;
	}

	public void setRetirement(String retirement) {
		this.retirement = retirement;
	}

	public String getYearSalary() {
		return yearSalary;
	}

	public void setYearSalary(String yearSalary) {
		this.yearSalary = yearSalary;
	}



    public FCPerson(String personID, String nativeplace, String nativeplaceName) {
        this.personID = personID;
        this.nativeplace = nativeplace;
        this.nativeplaceName = nativeplaceName;
    }
}