package com.sinosoft.eflex.model;

public class FCEdorReduInsured {
    /**
     * 删除被保人流水编号
     */
    private String decInsuredSn;

    /**
     * 团体保单号
     */
    private String grpContNo;

    /**
     * 企业代码
     */
    private String grpNo;

    /**
     * 批次
     */
    private String batch;

    /**
     * 被保险人性名
     */
    private String name;

    /**
     * 性别
     */
    private String sex;

    /**
     * 出生日期
     */
    private String birthDay;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 减保生效日期
     */
    private String ztaliDate;

    /**
     * 退费说明
     */
    private String refundInstruct;

    /**
     * 保全类型
     */
    private String edorType;

    /**
     * 提交试算状态0-未提交1-已提交
     */
    private String trialStatus;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 生成日期
     */
    private String makeDate;

    /**
     * 生成时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 删除被保人流水编号
     * @return DecInsuredSn 删除被保人流水编号
     */
    public String getDecInsuredSn() {
        return decInsuredSn;
    }

    /**
     * 删除被保人流水编号
     * @param decInsuredSn 删除被保人流水编号
     */
    public void setDecInsuredSn(String decInsuredSn) {
        this.decInsuredSn = decInsuredSn;
    }

    /**
     * 团体保单号
     * @return GrpContNo 团体保单号
     */
    public String getGrpContNo() {
        return grpContNo;
    }

    /**
     * 团体保单号
     * @param grpContNo 团体保单号
     */
    public void setGrpContNo(String grpContNo) {
        this.grpContNo = grpContNo;
    }

    /**
     * 企业代码
     * @return GrpNo 企业代码
     */
    public String getGrpNo() {
        return grpNo;
    }

    /**
     * 企业代码
     * @param grpNo 企业代码
     */
    public void setGrpNo(String grpNo) {
        this.grpNo = grpNo;
    }

    public String getBatch() {
        return batch;
    }

    public void setBatch(String batch) {
        this.batch = batch;
    }

    /**
     * 被保险人性名
     * @return Name 被保险人性名
     */
    public String getName() {
        return name;
    }

    /**
     * 被保险人性名
     * @param name 被保险人性名
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 性别
     * @return Sex 性别
     */
    public String getSex() {
        return sex;
    }

    /**
     * 性别
     * @param sex 性别
     */
    public void setSex(String sex) {
        this.sex = sex;
    }

    /**
     * 出生日期
     * @return BirthDay 出生日期
     */
    public String getBirthDay() {
        return birthDay;
    }

    /**
     * 出生日期
     * @param birthDay 出生日期
     */
    public void setBirthDay(String birthDay) {
        this.birthDay = birthDay;
    }

    /**
     * 证件号
     * @return IdNo 证件号
     */
    public String getIdNo() {
        return idNo;
    }

    /**
     * 证件号
     * @param idNo 证件号
     */
    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    /**
     * 证件类型
     * @return IdType 证件类型
     */
    public String getIdType() {
        return idType;
    }

    /**
     * 证件类型
     * @param idType 证件类型
     */
    public void setIdType(String idType) {
        this.idType = idType;
    }

    /**
     * 减保生效日期
     * @return ZtaliDate 减保生效日期
     */
    public String getZtaliDate() {
        return ztaliDate;
    }

    /**
     * 减保生效日期
     * @param ztaliDate 减保生效日期
     */
    public void setZtaliDate(String ztaliDate) {
        this.ztaliDate = ztaliDate;
    }

    /**
     * 退费说明
     * @return RefundInstruct 退费说明
     */
    public String getRefundInstruct() {
        return refundInstruct;
    }

    /**
     * 退费说明
     * @param refundInstruct 退费说明
     */
    public void setRefundInstruct(String refundInstruct) {
        this.refundInstruct = refundInstruct;
    }

    /**
     * 保全类型
     * @return EdorType 保全类型
     */
    public String getEdorType() {
        return edorType;
    }

    /**
     * 保全类型
     * @param edorType 保全类型
     */
    public void setEdorType(String edorType) {
        this.edorType = edorType;
    }

    /**
     * 提交试算状态0-未提交1-已提交
     * @return TrialStatus 提交试算状态0-未提交1-已提交
     */
    public String getTrialStatus() {
        return trialStatus;
    }

    /**
     * 提交试算状态0-未提交1-已提交
     * @param trialStatus 提交试算状态0-未提交1-已提交
     */
    public void setTrialStatus(String trialStatus) {
        this.trialStatus = trialStatus;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 操作机构
     * @return OperatorCom 操作机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作机构
     * @param operatorCom 操作机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 生成日期
     * @return MakeDate 生成日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 生成日期
     * @param makeDate 生成日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 生成时间
     * @return MakeTime 生成时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 生成时间
     * @param makeTime 生成时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}