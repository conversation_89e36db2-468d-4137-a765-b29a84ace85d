package com.sinosoft.eflex.model.BatchInsureInterface;

/**
 * @DESCRIPTION
 * @create 2018-08-29 16:44
 **/
public class DutyInfo {

    /* 责任编码 */
    private String DutyCode;
    /* 保额 */
    private String Amnt;
    /* 保费 */
    private String Prem;
    /* 免赔额 */
    private Double GetLimit;
    /* 免赔额属性 1-按次免赔 2-按年免赔 */
    private String GetLimitType;
    /* 赔付比例 */
    private Double GetRate;
    /* 计算规则 */
    private String CalRule;
    /* 保险期间 */
    private String InsuYear;
    /* 保险期间单位 */
    private String InsuYearFlag;
    /* 给付天数 */
    private String PayDay;
    /* 免赔天数 */
    private String NoGetDay;
    /* 费率/折扣 */
    private String FloatRate;
    /* 最大赔付天数 */
    private String MaxPayDay;

    public String getDutyCode() {
        return DutyCode;
    }

    public void setDutyCode(String dutyCode) {
        DutyCode = dutyCode;
    }

    public String getAmnt() {
        return Amnt;
    }

    public void setAmnt(String amnt) {
        Amnt = amnt;
    }

    public String getPrem() {
        return Prem;
    }

    public void setPrem(String prem) {
        Prem = prem;
    }

    public Double getGetLimit() {
        return GetLimit;
    }

    public void setGetLimit(Double getLimit) {
        GetLimit = getLimit;
    }

    public String getGetLimitType() {
        return GetLimitType;
    }

    public void setGetLimitType(String getLimitType) {
        GetLimitType = getLimitType;
    }

    public Double getGetRate() {
        return GetRate;
    }

    public void setGetRate(Double getRate) {
        GetRate = getRate;
    }

    public String getCalRule() {
        return CalRule;
    }

    public void setCalRule(String calRule) {
        CalRule = calRule;
    }

    public String getInsuYear() {
        return InsuYear;
    }

    public void setInsuYear(String insuYear) {
        InsuYear = insuYear;
    }

    public String getInsuYearFlag() {
        return InsuYearFlag;
    }

    public void setInsuYearFlag(String insuYearFlag) {
        InsuYearFlag = insuYearFlag;
    }

    public String getPayDay() {
        return PayDay;
    }

    public void setPayDay(String payDay) {
        PayDay = payDay;
    }

    public String getNoGetDay() {
        return NoGetDay;
    }

    public void setNoGetDay(String noGetDay) {
        NoGetDay = noGetDay;
    }

    public String getFloatRate() {
        return FloatRate;
    }

    public void setFloatRate(String floatRate) {
        FloatRate = floatRate;
    }

    public String getMaxPayDay() {
        return MaxPayDay;
    }

    public void setMaxPayDay(String maxPayDay) {
        MaxPayDay = maxPayDay;
    }
}
