package com.sinosoft.eflex.model.BatchInsureInterface;

import com.sinosoft.eflex.model.makeProposalForm.InsuredImpart;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @DESCRIPTION
 * @create 2018-08-29 16:42
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InsuredInfo {
    /* 顺序字段 */
    private String customerSeqNo;
    /* 与投保人关系 */
    private String RelationToAppnt;
	/* 被保人客户号 */
    private String InsuredNo;
    /* 姓名 */
    private String Name;
    /* 性别 */
    private String Sex;
    /* 证件类型 */
    private String IDType;
    /* 证件号码 */
    private String IDNo;
    /* 国籍 add by 2022.1.18 */
    private String NativePlace;
    /* 出生日期 */
    private String Birthday;
    /* 个人保单号 */
    private String ContNo;
    /* 主被保险人客户号 */
    private String MainInsuredNo;
    /* 与主被保人关系 */
    private String MainRelation;
    /* 工作证号 */
    private String WorkNo;
    /* 职业代码 */
    private String OccupationCode;
    /* 职业类别 */
    private String OccupationType;
    /* 入司日期 */
    private String JoinCompanyDate;
    /* 工资 */
    private String Salary;
    /* 移动电话 */
    private String Mobile;
    /* 被保人人数 */
    private String InsuredPeoples;
    /* 社保标志 */
    private String SocialInsuFlag;
    /* 保险计划 */
    private String ContPlanCode;
    /* 缴费方式 */
    private String GrpPayMode;
    /* 扣款缴费银行编码 */
    private String GrpBankCode;
    /* 扣款缴费账户名 */
    private String GrpBankAccName;
    /* 扣款缴费银行账号 */
    private String GrpBankAccNo;

    /* 险种信息 */
    private List<RiskInfo> RiskList;
    /* 影像件列表 */
    private List<ESView> ESViewList;
    /* 个人健康告知列表 */
    private List<InsuredImpart> InsuredImpartList;

    /*累计保额接口的证件类型*/
    private String CertiType;
    /*累计保额的证件号*/
    private String CertiCode;
    /*累计保额的姓名*/
    private String InsuredName;

    public String getInsuredNo() {
		return InsuredNo;
	}

	public void setInsuredNo(String insuredNo) {
		InsuredNo = insuredNo;
	}

	public String getCertiType() {
        return CertiType;
    }

    public void setCertiType(String certiType) {
        CertiType = certiType;
    }

    public String getCertiCode() {
        return CertiCode;
    }

    public void setCertiCode(String certiCode) {
        CertiCode = certiCode;
    }

    public String getInsuredName() {
        return InsuredName;
    }

    public void setInsuredName(String insuredName) {
        InsuredName = insuredName;
    }

    public String getName() {
        return Name;
    }

    public void setName(String name) {
        Name = name;
    }

    public String getSex() {
        return Sex;
    }

    public void setSex(String sex) {
        Sex = sex;
    }

    public String getIDType() {
        return IDType;
    }

    public void setIDType(String IDType) {
        this.IDType = IDType;
    }

    public String getIDNo() {
        return IDNo;
    }

    public void setIDNo(String IDNo) {
        this.IDNo = IDNo;
    }

    public String getNativePlace() {
        return NativePlace;
    }

    public void setNativePlace(String nativePlace) {
        NativePlace = nativePlace;
    }

    public String getBirthday() {
		return Birthday;
	}

	public void setBirthday(String birthday) {
		Birthday = birthday;
	}

	public String getContNo() {
        return ContNo;
    }

    public void setContNo(String contNo) {
        ContNo = contNo;
    }

    public String getMainInsuredNo() {
        return MainInsuredNo;
    }

    public void setMainInsuredNo(String mainInsuredNo) {
        MainInsuredNo = mainInsuredNo;
    }

    public String getMainRelation() {
        return MainRelation;
    }

    public void setMainRelation(String mainRelation) {
        MainRelation = mainRelation;
    }

    public String getWorkNo() {
        return WorkNo;
    }

    public void setWorkNo(String workNo) {
        WorkNo = workNo;
    }

    public String getOccupationCode() {
        return OccupationCode;
    }

    public void setOccupationCode(String occupationCode) {
        OccupationCode = occupationCode;
    }

    public String getOccupationType() {
        return OccupationType;
    }

    public void setOccupationType(String occupationType) {
        OccupationType = occupationType;
    }

    public String getJoinCompanyDate() {
        return JoinCompanyDate;
    }

    public void setJoinCompanyDate(String joinCompanyDate) {
        JoinCompanyDate = joinCompanyDate;
    }

    public String getSalary() {
        return Salary;
    }

    public void setSalary(String salary) {
        Salary = salary;
    }

    public String getMobile() {
        return Mobile;
    }

    public void setMobile(String mobile) {
        Mobile = mobile;
    }

    public String getInsuredPeoples() {
        return InsuredPeoples;
    }

    public void setInsuredPeoples(String insuredPeoples) {
        InsuredPeoples = insuredPeoples;
    }

    public String getSocialInsuFlag() {
        return SocialInsuFlag;
    }

    public void setSocialInsuFlag(String socialInsuFlag) {
        SocialInsuFlag = socialInsuFlag;
    }

    public String getContPlanCode() {
        return ContPlanCode;
    }

    public void setContPlanCode(String contPlanCode) {
        ContPlanCode = contPlanCode;
    }

    public String getGrpPayMode() {
        return GrpPayMode;
    }

    public void setGrpPayMode(String grpPayMode) {
        GrpPayMode = grpPayMode;
    }

    public String getGrpBankCode() {
        return GrpBankCode;
    }

    public void setGrpBankCode(String grpBankCode) {
        GrpBankCode = grpBankCode;
    }

    public String getGrpBankAccName() {
        return GrpBankAccName;
    }

    public void setGrpBankAccName(String grpBankAccName) {
        GrpBankAccName = grpBankAccName;
    }

    public String getGrpBankAccNo() {
        return GrpBankAccNo;
    }

    public void setGrpBankAccNo(String grpBankAccNo) {
        GrpBankAccNo = grpBankAccNo;
    }

    public List<RiskInfo> getRiskList() {
        return RiskList;
    }

    public void setRiskList(List<RiskInfo> riskList) {
        RiskList = riskList;
    }

    public List<ESView> getESViewList() {
        return ESViewList;
    }

    public void setESViewList(List<ESView> ESViewList) {
        this.ESViewList = ESViewList;
    }

    public List<InsuredImpart> getInsuredImpartList() {
        return InsuredImpartList;
    }

    public void setInsuredImpartList(List<InsuredImpart> insuredImpartList) {
        InsuredImpartList = insuredImpartList;
    }
}
