package com.sinosoft.eflex.model.BatchInsureInterface;

import java.util.List;

/**
 * @DESCRIPTION
 * @create 2018-08-29 16:44
 **/
public class PlanInfo {
    private String ContPlanCode;
    private String ContPlanName;
    private int Peoples3;
    private List<RiskInfo> RiskList;

    public String getContPlanCode() {
        return ContPlanCode;
    }

    public void setContPlanCode(String contPlanCode) {
        ContPlanCode = contPlanCode;
    }

    public String getContPlanName() {
        return ContPlanName;
    }

    public void setContPlanName(String contPlanName) {
        ContPlanName = contPlanName;
    }

    public int getPeoples3() {
        return Peoples3;
    }

    public void setPeoples3(int peoples3) {
        Peoples3 = peoples3;
    }

    public List<RiskInfo> getRiskList() {
        return RiskList;
    }

    public void setRiskList(List<RiskInfo> riskList) {
        RiskList = riskList;
    }
}
