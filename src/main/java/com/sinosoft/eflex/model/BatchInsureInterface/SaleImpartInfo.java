package com.sinosoft.eflex.model.BatchInsureInterface;

/**
 * @DESCRIPTION
 * @create 2018-09-14 16:29
 **/
public class SaleImpartInfo {
    private String ImpartVer;
    private String ImpartCode;
    private String ImpartContent;
    private String ImpartParamModle;

    public String getImpartVer() {
        return ImpartVer;
    }

    public void setImpartVer(String impartVer) {
        ImpartVer = impartVer;
    }

    public String getImpartCode() {
        return ImpartCode;
    }

    public void setImpartCode(String impartCode) {
        ImpartCode = impartCode;
    }

    public String getImpartContent() {
        return ImpartContent;
    }

    public void setImpartContent(String impartContent) {
        ImpartContent = impartContent;
    }

    public String getImpartParamModle() {
        return ImpartParamModle;
    }

    public void setImpartParamModle(String impartParamModle) {
        ImpartParamModle = impartParamModle;
    }
}
