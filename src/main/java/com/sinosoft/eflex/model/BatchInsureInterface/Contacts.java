package com.sinosoft.eflex.model.BatchInsureInterface;

/**
 * @DESCRIPTION
 * @create 2018-08-29 16:40
 **/
public class Contacts {
    /* 姓名 */
    private String LinkMan;
    /* 证件类型 */
    private String InsContIDType;
    /* 证件号码 */
    private String InsContIDNo;
    /* 证件有效期 */
    private String InsContIDPeriodOfValidityType;
    private String InsContIDStartPeriod;
    /* E-MAIL */
    private String E_Mail;
    /* 固定电话 */
    private String Phone;
    /* 手机号码 */
    private String Mobile;
    /* 所属部门 */
    private String Department;

    /* 国籍 */
    private String Nationality1;
    /* 性别 */
    private String Gender1;
    /* 出生日期 */
    private String Birthday1;


    public String getLinkMan() {
        return LinkMan;
    }

    public void setLinkMan(String linkMan) {
        LinkMan = linkMan;
    }

    public String getInsContIDType() {
        return InsContIDType;
    }

    public void setInsContIDType(String insContIDType) {
        InsContIDType = insContIDType;
    }

    public String getInsContIDNo() {
        return InsContIDNo;
    }

    public void setInsContIDNo(String insContIDNo) {
        InsContIDNo = insContIDNo;
    }

    public String getInsContIDPeriodOfValidityType() {
        return InsContIDPeriodOfValidityType;
    }

    public void setInsContIDPeriodOfValidityType(String insContIDPeriodOfValidityType) {
        InsContIDPeriodOfValidityType = insContIDPeriodOfValidityType;
    }

    public String getE_Mail() {
        return E_Mail;
    }

    public void setE_Mail(String e_Mail) {
        E_Mail = e_Mail;
    }

    public String getPhone() {
        return Phone;
    }

    public void setPhone(String phone) {
        Phone = phone;
    }

    public String getMobile() {
        return Mobile;
    }

    public void setMobile(String mobile) {
        Mobile = mobile;
    }

    public String getDepartment() {
        return Department;
    }

    public void setDepartment(String department) {
        Department = department;
    }

    public String getNationality1() {
        return Nationality1;
    }

    public void setNationality1(String nationality1) {
        Nationality1 = nationality1;
    }

    public String getGender1() {
        return Gender1;
    }

    public void setGender1(String gender1) {
        Gender1 = gender1;
    }

    public String getBirthday1() {
        return Birthday1;
    }

    public void setBirthday1(String birthday1) {
        Birthday1 = birthday1;
    }

    public String getInsContIDStartPeriod() {
        return InsContIDStartPeriod;
    }

    public void setInsContIDStartPeriod(String insContIDStartPeriod) {
        InsContIDStartPeriod = insContIDStartPeriod;
    }
}
