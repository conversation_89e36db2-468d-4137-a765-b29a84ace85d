package com.sinosoft.eflex.model.BatchInsureInterface;

import com.sinosoft.eflex.model.TransResult;

/**
 * @DESCRIPTION
 * @create 2018-09-07 16:22
 **/
public class Head {

    private String TransRefGUID;
    private String TransType;
    private String TransExeDate;
    private String TransExeTime;
    private TransResult TransResult;
    private String ResultCode;
    private String ResultInfo;
    private String Flag;
    private String Desc;
    private String Source;
    private String SubSource;


    public String getTransRefGUID() {
        return TransRefGUID;
    }

    public void setTransRefGUID(String transRefGUID) {
        TransRefGUID = transRefGUID;
    }

    public String getTransType() {
        return TransType;
    }

    public void setTransType(String transType) {
        TransType = transType;
    }

    public String getTransExeDate() {
        return TransExeDate;
    }

    public void setTransExeDate(String transExeDate) {
        TransExeDate = transExeDate;
    }

    public String getTransExeTime() {
        return TransExeTime;
    }

    public void setTransExeTime(String transExeTime) {
        TransExeTime = transExeTime;
    }

    public com.sinosoft.eflex.model.TransResult getTransResult() {
        return TransResult;
    }

    public void setTransResult(com.sinosoft.eflex.model.TransResult transResult) {
        TransResult = transResult;
    }

	public String getResultCode() {
		return ResultCode;
	}

	public void setResultCode(String resultCode) {
		ResultCode = resultCode;
	}

	public String getResultInfo() {
		return ResultInfo;
	}

	public void setResultInfo(String resultInfo) {
		ResultInfo = resultInfo;
	}

    public String getFlag() {
        return Flag;
    }

    public void setFlag(String flag) {
        Flag = flag;
    }

    public String getDesc() {
        return Desc;
    }

    public void setDesc(String desc) {
        Desc = desc;
    }

    public String getSource() {
        return Source;
    }

    public void setSource(String source) {
        Source = source;
    }

    public String getSubSource() {
        return SubSource;
    }

    public void setSubSource(String subSource) {
        SubSource = subSource;
    }
}
