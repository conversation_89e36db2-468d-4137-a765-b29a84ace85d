package com.sinosoft.eflex.model.BatchInsureInterface;

/**
 * @DESCRIPTION
 * @create 2018-08-29 16:42
 **/
public class AppntImpartInfo {
    /* 告知版别 */
    private String ImpartVer;
    /* 告知编码 */
    private String ImpartCode;
    /* 告知内容 */
    private String ImpartContent;
    /* 详细描述 */
    private String ImpartParamModle;

    public String getImpartVer() {
        return ImpartVer;
    }

    public void setImpartVer(String impartVer) {
        ImpartVer = impartVer;
    }

    public String getImpartCode() {
        return ImpartCode;
    }

    public void setImpartCode(String impartCode) {
        ImpartCode = impartCode;
    }

    public String getImpartContent() {
        return ImpartContent;
    }

    public void setImpartContent(String impartContent) {
        ImpartContent = impartContent;
    }

    public String getImpartParamModle() {
        return ImpartParamModle;
    }

    public void setImpartParamModle(String impartParamModle) {
        ImpartParamModle = impartParamModle;
    }
}
