package com.sinosoft.eflex.model.BatchInsureInterface;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @DESCRIPTION
 * @create 2018-08-29 16:40
 **/
@Data
public class ContInfo {
    /*投保单号*/
    private String PrtNo;
    /*呈报件号*/
    private String ReportNo;
    /*管理机构*/
    private String ManageCom;
    /*一级销售渠道*/
    private String SaleChnl;
    /*二级销售渠道*/
    private String SellType;
    /*三级销售渠道*/
    private String AgentType;
    /*中介机构代码*/
    private String ZJAgentComCode;
    /*中介机构名称*/
    private String ZJAgentComName;
    /*续保业务*/
    private String RnewFlag;
    /*投保申请日期*/
    private String PolApplyDate;
    /*保单生效日期*/
    private String Cvalidate;
    /*财务收费日期*/
    private String PayDate;
    /*保全定期结算*/
    private String BalanceFlag;
    /*定期结算周期*/
    private String BalanceZQ;
    /*定期结算金额上限*/
    private String BalanceMoney;
    /*所属集团投保单号*/
    private String RelaPrtNo;
    /*股东业务*/
    private String GudongContFlag;
    /*股东单位名称 1-珠海铧创 2--亨通集团 3--苏州环亚 4--明珠深投 5--中植集团*/
    private String ShareHolder;
    /*团单类型*/
    private String ContTypeFlag;
    /* 团单保单打印类型 1-电子保单 2-电子保单+纸质保单 默认电子打印 */
    private String ContPrintType;
    /*日期追溯备注*/
    private String BackDataRemark;
    /*业务员代码*/
    private String AgentCode;
    /*业务员姓名*/
    private String AgentName;
    /*所属机构*/
    private String AgentManageCom;
    /*所属分部*/
    private String BranchAttr;
    /* 客户类别 */
    private String PersonType;
    /* 家庭单标志 */
    private String FamilySign;

    /**
     * 绿色保单标志*
     */
    private String GreenInsureFlag;

    /**
     * 被续保保单编号*
     */
    private String ReNewGrpContNo;

    /* 公司信息 */
    private CompanyInfo CompanyInfo;
    private List<InsuredInfo> InsuredList;
    private List<PlanInfo> PlanList;
    //影像件信息
    private List<ESView> ESViewList = new ArrayList<>();

    public List<PlanInfo> getPlanList() {
        return PlanList;
    }

    public void setPlanList(List<PlanInfo> planList) {
        PlanList = planList;
    }

    public List<InsuredInfo> getInsuredInfoList() {
        return InsuredList;
    }

    public void setInsuredInfoList(List<InsuredInfo> insuredInfoList) {
        this.InsuredList = insuredInfoList;
    }

    public CompanyInfo getCompanyInfo() {
        return CompanyInfo;
    }

    public void setCompanyInfo(CompanyInfo companyInfo) {
        CompanyInfo = companyInfo;
    }

    public String getPrtNo() {
        return PrtNo;
    }

    public void setPrtNo(String prtNo) {
        PrtNo = prtNo;
    }

    public String getReportNo() {
        return ReportNo;
    }

    public void setReportNo(String reportNo) {
        ReportNo = reportNo;
    }

    public String getManageCom() {
        return ManageCom;
    }

    public void setManageCom(String manageCom) {
        ManageCom = manageCom;
    }

    public String getSaleChnl() {
        return SaleChnl;
    }

    public void setSaleChnl(String saleChnl) {
        SaleChnl = saleChnl;
    }

    public String getSellType() {
        return SellType;
    }

    public void setSellType(String sellType) {
        SellType = sellType;
    }

    public String getAgentType() {
        return AgentType;
    }

    public void setAgentType(String agentType) {
        AgentType = agentType;
    }

    public String getRnewFlag() {
        return RnewFlag;
    }

    public void setRnewFlag(String rnewFlag) {
        RnewFlag = rnewFlag;
    }

    public String getPolApplyDate() {
        return PolApplyDate;
    }

    public void setPolApplyDate(String polApplyDate) {
        PolApplyDate = polApplyDate;
    }

    public String getCvalidate() {
        return Cvalidate;
    }

    public void setCvalidate(String cvalidate) {
        Cvalidate = cvalidate;
    }

    public String getPayDate() {
        return PayDate;
    }

    public void setPayDate(String payDate) {
        PayDate = payDate;
    }

    public String getBalanceFlag() {
        return BalanceFlag;
    }

    public void setBalanceFlag(String balanceFlag) {
        BalanceFlag = balanceFlag;
    }

    public String getBalanceZQ() {
        return BalanceZQ;
    }

    public void setBalanceZQ(String balanceZQ) {
        BalanceZQ = balanceZQ;
    }

    public String getBalanceMoney() {
        return BalanceMoney;
    }

    public void setBalanceMoney(String balanceMoney) {
        BalanceMoney = balanceMoney;
    }

    public String getRelaPrtNo() {
        return RelaPrtNo;
    }

    public void setRelaPrtNo(String relaPrtNo) {
        RelaPrtNo = relaPrtNo;
    }

    public String getGudongContFlag() {
        return GudongContFlag;
    }

    public void setGudongContFlag(String gudongContFlag) {
        GudongContFlag = gudongContFlag;
    }

    public String getShareHolder() {
        return ShareHolder;
    }

    public void setShareHolder(String shareHolder) {
        ShareHolder = shareHolder;
    }

    public String getContTypeFlag() {
        return ContTypeFlag;
    }

    public void setContTypeFlag(String contTypeFlag) {
        ContTypeFlag = contTypeFlag;
    }

    public String getContPrintType() {
        return ContPrintType;
    }

    public void setContPrintType(String contPrintType) {
        ContPrintType = contPrintType;
    }

    public String getBackDataRemark() {
        return BackDataRemark;
    }

    public void setBackDataRemark(String backDataRemark) {
        BackDataRemark = backDataRemark;
    }

    public String getAgentCode() {
        return AgentCode;
    }

    public void setAgentCode(String agentCode) {
        AgentCode = agentCode;
    }

    public String getAgentName() {
        return AgentName;
    }

    public void setAgentName(String agentName) {
        AgentName = agentName;
    }

    public String getAgentManageCom() {
        return AgentManageCom;
    }

    public void setAgentManageCom(String agentManageCom) {
        AgentManageCom = agentManageCom;
    }

    public String getBranchAttr() {
        return BranchAttr;
    }

    public void setBranchAttr(String branchAttr) {
        BranchAttr = branchAttr;
    }

    public List<ESView> getESViewList() {
        return ESViewList;
    }

    public void setESViewList(List<ESView> eSViewList) {
        ESViewList = eSViewList;
    }

    public String getPersonType() {
        return PersonType;
    }

    public void setPersonType(String personType) {
        PersonType = personType;
    }

    public String getFamilySign() {
        return FamilySign;
    }

    public void setFamilySign(String familySign) {
        FamilySign = familySign;
    }

    public String getZJAgentComCode() {
        return ZJAgentComCode;
    }

    public void setZJAgentComCode(String ZJAgentComCode) {
        this.ZJAgentComCode = ZJAgentComCode;
    }

    public String getZJAgentComName() {
        return ZJAgentComName;
    }

    public void setZJAgentComName(String ZJAgentComName) {
        this.ZJAgentComName = ZJAgentComName;
    }

}
