package com.sinosoft.eflex.model.BatchInsureInterface;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @DESCRIPTION 公司信息
 * @create 2018-08-29 16:40
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CompanyInfo {
    /**
     * 主营业务
     */
    private String MainBussiness;
    /* 投保单位名称 */
    private String GrpName;
    /* 证件类型 */
    private String ComType;
    /* 证件号码 */
    private String ComNo;

    /* 证件有效起期 */
    private String BusliceStartDate;
    /* 证件有效止期 */
    private String BusliceDate;
    /* 企业成立日期 */
    private String FoundDate;
    /* 企业规模类型 */
    private String BusSizeType;
    /* 参加社会统筹标志 */
    private String JoinType;
    /* 注册资本（元）*/
    private String RgtMoney;
    /* 注册地址 */
    private String RegAddress;
    /**
     * 注册地
     */
    private String RegestedPlace;
    /* 企业电话号码 */
    private String Phone;

    /* 投保单位性质 */
    // 一类性质
    private String GrpNature1;
    // 二类性质
    private String GrpNature;
    /**
     *企业组织形式 *
     */
    private String GrpNatureType;
    /* 行业类别 */
    private String BusinessType;
    /* 营业期限 */
    private String PeriodOfValidity;
    /* 单位员工总数 */
    private String Peoples;
    /* 投保人数 */
    private String Peoples2;
    /* 单位法人代表 */
    private String Corporation;
    /**
     *法人性别
     */
    private String CorporationGender;
    /**
     *法人生日
     */
    private String CorporationBirthday;
    /**
     *法人国籍
     */
    private String CorporationNationality;
    /**
     *法人证件类型
     */
    private String CorporationIDType;
    /**
     *法人证件号
     */
    private String CorporationIDNo;
    /**
     *法人证件有效期起
     */
    private String CorporationIDStartPeriod;
    /**
     *法人证件有效止期
     */
    private String CorporationIDPeriodOfValidity;
    /* 同质风险减人百分比 */
    private String NZProportion;
    /* 单位地址 */
    private String GrpAddress;
    /* 邮政编码 */
    private String GrpZipCode;
    /* 是否按照续保单承保 Y-是 N-否 */
    private String RnewContFlag;
    /* 付款方式 */
    private String GetFlag;
    /* 备注 */
    private String Remark;
    /* 特殊险种类别 */
    private String riskflag;
    /* 特约内容 */
    private String SpecContent;
    /* 操作员 */
    private String Operator;
    /* 录入时间 */
    private String MakeDate;
    /* 联系人 */
    private Contacts Contacts;
    /* 投保人告知 */
    //private AppntImpartInfo AppntImpartInfo;
    private List<AppntImpartInfo> AppntImpartList;

    public List<AppntImpartInfo> getAppntImpartList() {
        return AppntImpartList;
    }

    public void setAppntImpartList(List<AppntImpartInfo> appntImpartList) {
        AppntImpartList = appntImpartList;
    }

    public String getGrpName() {
        return GrpName;
    }

    public void setGrpName(String grpName) {
        GrpName = grpName;
    }

    public String getComType() {
        return ComType;
    }

    public void setComType(String comType) {
        ComType = comType;
    }

    public String getComNo() {
        return ComNo;
    }

    public void setComNo(String comNo) {
        ComNo = comNo;
    }

    public String getGrpNature1() {
        return GrpNature1;
    }

    public void setGrpNature1(String grpNature1) {
        GrpNature1 = grpNature1;
    }

    public String getGrpNature() {
        return GrpNature;
    }

    public void setGrpNature(String grpNature) {
        GrpNature = grpNature;
    }

    public String getBusinessType() {
        return BusinessType;
    }

    public void setBusinessType(String businessType) {
        BusinessType = businessType;
    }

    public String getPeriodOfValidity() {
        return PeriodOfValidity;
    }

    public void setPeriodOfValidity(String periodOfValidity) {
        PeriodOfValidity = periodOfValidity;
    }

    public String getPeoples() {
        return Peoples;
    }

    public void setPeoples(String peoples) {
        Peoples = peoples;
    }

    public String getPeoples2() {
        return Peoples2;
    }

    public void setPeoples2(String peoples2) {
        Peoples2 = peoples2;
    }

    public String getCorporation() {
        return Corporation;
    }

    public void setCorporation(String corporation) {
        Corporation = corporation;
    }

    public String getNZProportion() {
        return NZProportion;
    }

    public void setNZProportion(String NZProportion) {
        this.NZProportion = NZProportion;
    }

    public String getGrpAddress() {
        return GrpAddress;
    }

    public void setGrpAddress(String grpAddress) {
        GrpAddress = grpAddress;
    }

    public String getGrpZipCode() {
        return GrpZipCode;
    }

    public void setGrpZipCode(String grpZipCode) {
        GrpZipCode = grpZipCode;
    }

    public String getRnewContFlag() {
        return RnewContFlag;
    }

    public void setRnewContFlag(String rnewContFlag) {
        RnewContFlag = rnewContFlag;
    }

    public String getGetFlag() {
        return GetFlag;
    }

    public void setGetFlag(String getFlag) {
        GetFlag = getFlag;
    }

    public String getRemark() {
        return Remark;
    }

    public void setRemark(String remark) {
        Remark = remark;
    }

    public String getRiskflag() {
        return riskflag;
    }

    public void setRiskflag(String riskflag) {
        this.riskflag = riskflag;
    }

    public String getSpecContent() {
        return SpecContent;
    }

    public void setSpecContent(String specContent) {
        SpecContent = specContent;
    }

    public String getOperator() {
        return Operator;
    }

    public void setOperator(String operator) {
        Operator = operator;
    }

    public String getMakeDate() {
        return MakeDate;
    }

    public void setMakeDate(String makeDate) {
        MakeDate = makeDate;
    }

    public Contacts getContacts() {
        return Contacts;
    }

    public void setContacts(Contacts contacts) {
        Contacts = contacts;
    }

    public String getBusliceStartDate() {
        return BusliceStartDate;
    }

    public void setBusliceStartDate(String busliceStartDate) {
        BusliceStartDate = busliceStartDate;
    }

    public String getBusliceDate() {
        return BusliceDate;
    }

    public void setBusliceDate(String busliceDate) {
        BusliceDate = busliceDate;
    }

    public String getFoundDate() {
        return FoundDate;
    }

    public void setFoundDate(String foundDate) {
        FoundDate = foundDate;
    }

    public String getBusSizeType() {
        return BusSizeType;
    }

    public void setBusSizeType(String busSizeType) {
        BusSizeType = busSizeType;
    }

    public String getJoinType() {
        return JoinType;
    }

    public void setJoinType(String joinType) {
        JoinType = joinType;
    }

    public String getRgtMoney() {
        return RgtMoney;
    }

    public void setRgtMoney(String rgtMoney) {
        RgtMoney = rgtMoney;
    }

    public String getRegAddress() {
        return RegAddress;
    }

    public void setRegAddress(String regAddress) {
        RegAddress = regAddress;
    }

    public String getPhone() {
        return Phone;
    }

    public void setPhone(String phone) {
        Phone = phone;
    }

    public String getRegestedPlace() {
        return RegestedPlace;
    }

    public void setRegestedPlace(String regestedPlace) {
        RegestedPlace = regestedPlace;
    }

    public String getCorporationGender() {
        return CorporationGender;
    }

    public void setCorporationGender(String corporationGender) {
        CorporationGender = corporationGender;
    }

    public String getCorporationBirthday() {
        return CorporationBirthday;
    }

    public void setCorporationBirthday(String corporationBirthday) {
        CorporationBirthday = corporationBirthday;
    }

    public String getCorporationNationality() {
        return CorporationNationality;
    }

    public void setCorporationNationality(String corporationNationality) {
        CorporationNationality = corporationNationality;
    }

    public String getCorporationIDType() {
        return CorporationIDType;
    }

    public void setCorporationIDType(String corporationIDType) {
        CorporationIDType = corporationIDType;
    }

    public String getCorporationIDNo() {
        return CorporationIDNo;
    }

    public void setCorporationIDNo(String corporationIDNo) {
        CorporationIDNo = corporationIDNo;
    }

    public String getCorporationIDStartPeriod() {
        return CorporationIDStartPeriod;
    }

    public void setCorporationIDStartPeriod(String corporationIDStartPeriod) {
        CorporationIDStartPeriod = corporationIDStartPeriod;
    }

    public String getCorporationIDPeriodOfValidity() {
        return CorporationIDPeriodOfValidity;
    }

    public void setCorporationIDPeriodOfValidity(String corporationIDPeriodOfValidity) {
        CorporationIDPeriodOfValidity = corporationIDPeriodOfValidity;
    }
}
