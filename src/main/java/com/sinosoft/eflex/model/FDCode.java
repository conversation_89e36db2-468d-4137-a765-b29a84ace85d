package com.sinosoft.eflex.model;

public class FDCode extends FDCodeKey {
    /**
     * 
     */
    private String coreName;

    /**
     * 
     */
    private String coreCode;

    /**
     * 代码名称
     */
    private String codeName;

    /**
     * 代码描述
     */
    private String codeDesc;

    /**
     * 其他标志
     */
    private String otherSign;

    /**
     * 代码顺序
     */
    private Integer codeOrder;

    /**
     * 
     * @return CoreName 
     */
    public String getCoreName() {
        return coreName;
    }

    /**
     * 
     * @param coreName 
     */
    public void setCoreName(String coreName) {
        this.coreName = coreName;
    }

    /**
     * 
     * @return CoreCode 
     */
    public String getCoreCode() {
        return coreCode;
    }

    /**
     * 
     * @param coreCode 
     */
    public void setCoreCode(String coreCode) {
        this.coreCode = coreCode;
    }

    /**
     * 代码名称
     * @return CodeName 代码名称
     */
    public String getCodeName() {
        return codeName;
    }

    /**
     * 代码名称
     * @param codeName 代码名称
     */
    public void setCodeName(String codeName) {
        this.codeName = codeName;
    }

    /**
     * 代码描述
     * @return CodeDesc 代码描述
     */
    public String getCodeDesc() {
        return codeDesc;
    }

    /**
     * 代码描述
     * @param codeDesc 代码描述
     */
    public void setCodeDesc(String codeDesc) {
        this.codeDesc = codeDesc;
    }

    /**
     * 其他标志
     * @return OtherSign 其他标志
     */
    public String getOtherSign() {
        return otherSign;
    }

    /**
     * 其他标志
     * @param otherSign 其他标志
     */
    public void setOtherSign(String otherSign) {
        this.otherSign = otherSign;
    }

    /**
     * 代码顺序
     * @return CodeOrder 代码顺序
     */
    public Integer getCodeOrder() {
        return codeOrder;
    }

    /**
     * 代码顺序
     * @param codeOrder 代码顺序
     */
    public void setCodeOrder(Integer codeOrder) {
        this.codeOrder = codeOrder;
    }
}