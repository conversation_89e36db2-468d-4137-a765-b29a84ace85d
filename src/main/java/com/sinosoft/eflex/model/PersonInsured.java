package com.sinosoft.eflex.model;

import com.sinosoft.eflex.model.BatchInsureInterface.ESView;
import com.sinosoft.eflex.model.BatchInsureInterface.RiskInfo;
import com.sinosoft.eflex.model.makeProposalForm.InsuredImpart;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/5/24 13:52
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PersonInsured {
    /** 被保人客户号 */
    private String insuredNo;
    /** 姓名 */
    private String name;
    /** 性别 */
    private String sex;
    /** *证件类型 */
    private String idType;
    /** *证件号码 */
    private String idNo;
    /** 国籍  */
    private String nativePlace;
    /** 出生日期 */
    private String birthday;
    /** 个人保单号 */
    private String contNo;
    /** 主被保险人客户号 */
    private String mainInsuredNo;
    /** 与主被保人关系 */
    private String mainRelation;


    /** 保险计划 */
    private String contPlanCode;
    /** 缴费方式 */
    private String grpPayMode;


    /** 险种信息 */
    private List<RiskInfo> riskList;

}
