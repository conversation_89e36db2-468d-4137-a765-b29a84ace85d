package com.sinosoft.eflex.model;

import lombok.Data;

/**
 * 险种信息表
 */
@Data
public class FDRiskInfo {
    /**
     * 险种编码
     */
    private String riskCode;

    /**
     * 险种名称
     */
    private String riskName;

    /**
     * 险种类别
     */
    private String riskType;

    /**
     * 险种范围
     * 用于弹性计划定制页面险种展示 0-除必选责任无需展示其他  1-除必选责任外展示可选责任  2-处必选责任外展示免赔额以及赔付比例
     */
    private String riskRange;

    /**
     * 红利领取方式
     */
    private String bonusGetMode;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 产品说明PDF url
     */
    private String productDescription;

    /**
     * 保险条款 url
     */
    private String insuranceTerms;


    /**
     * 产品说明PDF url
     */
    private String productDescriptionName;

    /**
     * 保险条款 url
     */
    private String insuranceTermsName;

    /**
     * pdf url
     */
    private String pdfUrl;

    /**
     * 生成日期
     */
    private String makeDate;

    /**
     * 生成时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

}