package com.sinosoft.eflex.model;

import lombok.Data;

@Data
public class IsensureInfo {
    /**
     * 姓名
     */
    private String name;
    /**
     * 证件类型
     */
    private String IDType;

    /**
     * 证件号
     */
    private String IDNo;

    /**
     * 证件有效期
     */
    private String idTypeEndDate;
    /**
     * 性别
     */
    private String sex;
    /**
     * 国籍
     */
    private String nativeplace;
    /**
     * 出生日期
     */
    private String birthDate;

    /**
     * 移动电话
     */
    private String mobilePhone;

    /**
     * 邮箱
     */
    private String EMail;

    /**
     * 职业类别
     */
    private String occupationType;
    private String occupationTypeName;


    /**
     * 职业代码
     */
    private String occupationCode;
    private String occupationName;


    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品责任编码
     */
    private String dutyCode;

    /**
     * 保额
     */
    private double InsuredAmount;


    /**
     * 保险期间  1-保至85周岁  2-终身
     */
    private String InsurePeriod;

    /**
     * 交费频次   1-趸交  2-年交
     */
    private String  PayFrequency;
    /**
     * 缴费期间  1-一次性交清  2-5年交   3-10年交  4-20年交
     */
    private String PayPeriod;



}