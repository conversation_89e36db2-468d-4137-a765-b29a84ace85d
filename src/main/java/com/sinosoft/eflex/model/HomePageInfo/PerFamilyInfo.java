package com.sinosoft.eflex.model.HomePageInfo;

import lombok.Data;

import java.io.Serializable;

/**
 * 公司信息*
 *
 * <AUTHOR>
 */
@Data
public class PerFamilyInfo implements Serializable {


    /**
     * 人员ID*
     */
    private String personId;

    /**
     * 姓名*
     */
    private String name;

    /**
     * 关系*
     */
    private String relation;

    /**
     * 关系证明*
     */
    private String relationProve;

    /**
     * 关系名称*
     */
    private String relationName;

    /**
     * 生日*
     */
    private String birthDate;

    /**
     * 性别*
     */
    private String sex;

    /**
     * 年龄*
     */
    private String age;

    /**
     * 证件号码*
     */
    private String idNo;

    /**
     * 证件类型*
     */
    private String idType;

    /**
     * 国籍*
     */
    private String nativePlace;

    /**
     * 国籍名称*
     */
    private String nativePlaceName;

    /**
     * 证件有效期*
     */
    private String idTypeEndDate;

    /**
     * 邮箱*
     */
    private String email;

    /**
     * 手机号*
     */
    private String mobilePhone;

    /**
     * 职级类别*
     */
    private String occupationCode;

    /**
     * 职级类别名称*
     */
    private String occupationName;

    /**
     * 职级类别类型*
     */
    private String occupationType;

    /**
     * 职级类别类型名称*
     */
    private String occupationTypeName;

    /**
     * 开户银行*
     */
    private String openBank;

    /**
     * 开户账号*
     */
    private String openAccount;

    /**
     * 新被保险人医保标记1*
     */
    private String joinMedProtect;

    /**
     * 查询家属在开放期内是否存有学生的身份*
     */
    private String isHasStuRule;


}
