package com.sinosoft.eflex.model.HomePageInfo.convert;

import com.sinosoft.eflex.model.HomePageInfo.RiskTypeInfo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 公司信息*
 *
 * <AUTHOR>
 */
@Data
public class RiskInfoConvert {

    public static List<RiskTypeInfo> convert() {
        List<RiskTypeInfo> riskInfoList = new ArrayList<>(6);
        for (int i = 1; i < 7; i++) {
            RiskTypeInfo riskInfo = new RiskTypeInfo();
            riskInfo.setIsRisk("0");
            riskInfo.setAMnt(new BigDecimal("0.00"));
            riskInfo.setRiskType(String.valueOf(i));
            riskInfoList.add(riskInfo);
        }
        return riskInfoList;
    }


}
