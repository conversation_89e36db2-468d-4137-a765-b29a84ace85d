package com.sinosoft.eflex.model;

import java.util.Date;

public class FcPlanConfig {
    /**
     * 福利配置流水号
     */
    private String serialNo;

    /**
     * 福利编号
     */
    private String ensureCode;

    /**
     * 计划编码
     */
    private String planCode;

    /**
     * 企业客户号
     */
    private String grpNo;

    /**
     * 保障配置项编号
001-员工等待期
002-家属等待期
003-医疗机构
004-责任说明
005-既往症
006-其他约定
007-股东业务
008-缴费方式
     */
    private String configNo;

    /**
     * 保障配置项值
     */
    private String configValue;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 生成日期
     */
    private String makeDate;

    /**
     * 生成时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 福利配置流水号
     * @return SerialNo 福利配置流水号
     */
    public String getSerialNo() {
        return serialNo;
    }

    /**
     * 福利配置流水号
     * @param serialNo 福利配置流水号
     */
    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    /**
     * 福利编号
     * @return EnsureCode 福利编号
     */
    public String getEnsureCode() {
        return ensureCode;
    }

    /**
     * 福利编号
     * @param ensureCode 福利编号
     */
    public void setEnsureCode(String ensureCode) {
        this.ensureCode = ensureCode;
    }

    /**
     * 计划编码
     * @return PlanCode 计划编码
     */
    public String getPlanCode() {
        return planCode;
    }

    /**
     * 计划编码
     * @param planCode 计划编码
     */
    public void setPlanCode(String planCode) {
        this.planCode = planCode;
    }

    /**
     * 企业客户号
     * @return GrpNo 企业客户号
     */
    public String getGrpNo() {
        return grpNo;
    }

    /**
     * 企业客户号
     * @param grpNo 企业客户号
     */
    public void setGrpNo(String grpNo) {
        this.grpNo = grpNo;
    }

    /**
     * 保障配置项编号
001-员工等待期
002-家属等待期
003-医疗机构
004-责任说明
005-既往症
006-其他约定
007-股东业务
008-缴费方式
     * @return ConfigNo 保障配置项编号
001-员工等待期
002-家属等待期
003-医疗机构
004-责任说明
005-既往症
006-其他约定
007-股东业务
008-缴费方式
     */
    public String getConfigNo() {
        return configNo;
    }

    /**
     * 保障配置项编号
001-员工等待期
002-家属等待期
003-医疗机构
004-责任说明
005-既往症
006-其他约定
007-股东业务
008-缴费方式
     * @param configNo 保障配置项编号
001-员工等待期
002-家属等待期
003-医疗机构
004-责任说明
005-既往症
006-其他约定
007-股东业务
008-缴费方式
     */
    public void setConfigNo(String configNo) {
        this.configNo = configNo;
    }

    /**
     * 保障配置项值
     * @return ConfigValue 保障配置项值
     */
    public String getConfigValue() {
        return configValue;
    }

    /**
     * 保障配置项值
     * @param configValue 保障配置项值
     */
    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 操作机构
     * @return OperatorCom 操作机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作机构
     * @param operatorCom 操作机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 生成日期
     * @return MakeDate 生成日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 生成时间
     * @return MakeTime 生成时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 生成时间
     * @param makeTime 生成时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}