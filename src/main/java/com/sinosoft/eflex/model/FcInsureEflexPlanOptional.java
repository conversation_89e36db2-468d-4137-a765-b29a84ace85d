package com.sinosoft.eflex.model;

import java.util.Date;

public class FcInsureEflexPlanOptional {
    /**
     * 子订单详情编号
     */
    private String orderItemDetailNo;

    /**
     * 投保必选责任档次
     */
    private String amountGrageCode;

    /**
     * 可选责任编码
     */
    private String optDutyCode;

    /**
     * 保费
     */
    private Double prem;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 管理机构
     */
    private String operatorCom;

    /**
     * 入机日期
     */
    private Date makeDate;

    /**
     * 入机时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private Date modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 子订单详情编号
     * @return OrderItemDetailNo 子订单详情编号
     */
    public String getOrderItemDetailNo() {
        return orderItemDetailNo;
    }

    /**
     * 子订单详情编号
     * @param orderItemDetailNo 子订单详情编号
     */
    public void setOrderItemDetailNo(String orderItemDetailNo) {
        this.orderItemDetailNo = orderItemDetailNo;
    }

    /**
     * 投保必选责任档次
     * @return AmountGrageCode 投保必选责任档次
     */
    public String getAmountGrageCode() {
        return amountGrageCode;
    }

    /**
     * 投保必选责任档次
     * @param amountGrageCode 投保必选责任档次
     */
    public void setAmountGrageCode(String amountGrageCode) {
        this.amountGrageCode = amountGrageCode;
    }

    /**
     * 可选责任编码
     * @return OptDutyCode 可选责任编码
     */
    public String getOptDutyCode() {
        return optDutyCode;
    }

    /**
     * 可选责任编码
     * @param optDutyCode 可选责任编码
     */
    public void setOptDutyCode(String optDutyCode) {
        this.optDutyCode = optDutyCode;
    }

    /**
     * 保费
     * @return prem 保费
     */
    public Double getPrem() {
        return prem;
    }

    /**
     * 保费
     * @param prem 保费
     */
    public void setPrem(Double prem) {
        this.prem = prem;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 管理机构
     * @return OperatorCom 管理机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 管理机构
     * @param operatorCom 管理机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 入机日期
     * @return MakeDate 入机日期
     */
    public Date getMakeDate() {
        return makeDate;
    }

    /**
     * 入机日期
     * @param makeDate 入机日期
     */
    public void setMakeDate(Date makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 入机时间
     * @return MakeTime 入机时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 入机时间
     * @param makeTime 入机时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public Date getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}