package com.sinosoft.eflex.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 子订单表
 */
@Data
public class FCOrderItem implements Serializable {

	private static final long serialVersionUID = -4109179796921392621L;

	/**
	 * 子订单号
	 */
	private String orderItemNo;

	/**
	 * 订单号
	 */
	private String orderNo;

	/**
	 * 消费明细号
	 */
	private String costNo;

	/**
	 * 订单项详情编号
	 */
	private String orderItemDetailNo;

	/**
	 * 对应核心团单号
	 */
	private String grpContNo;

	/**
	 * 对应核心个单号
	 */
	private String contNo;

	private String payPersonId;

    /**
     * 个人缴费
     */
    private Double selfPrem;

    /**
     * 公司缴费
     */
    private Double grpPrem;

    /**
     * 操作员
     */
    private String operator;

	/**
	 * 操作机构
	 */
	private String operatorCom;

	/**
	 * 生成日期
	 */
	private String makeDate;

	/**
	 * 生成时间
	 */
	private String makeTime;

	/**
	 * 修改日期
	 */
	private String modifyDate;

	/**
	 * 
	 */
	private String modifyTime;

	/**
	 * 子订单产品要素详情表
	 */
	private FCOrderItemDetail fcOrderItemDetail;

	/**
	 * 被保人表
	 */
	private FCOrderInsured fcOrderInsured;

	public FCOrderItemDetail getFcOrderItemDetail() {
		return fcOrderItemDetail;
	}

	public void setFcOrderItemDetail(FCOrderItemDetail fcOrderItemDetail) {
		this.fcOrderItemDetail = fcOrderItemDetail;
	}

	/**
	 * 子订单号
	 * 
	 * @return OrderItemNo 子订单号
	 */
	public String getOrderItemNo() {
		return orderItemNo;
	}

	/**
	 * 子订单号
	 * 
	 * @param orderItemNo
	 *            子订单号
	 */
	public void setOrderItemNo(String orderItemNo) {
		this.orderItemNo = orderItemNo;
	}

	/**
	 * 订单号
	 * 
	 * @return OrderNo 订单号
	 */
	public String getOrderNo() {
		return orderNo;
	}

	/**
	 * 订单号
	 * 
	 * @param orderNo
	 *            订单号
	 */
	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	/**
	 * 消费明细号
	 * 
	 * @return CostNo 消费明细号
	 */
	public String getCostNo() {
		return costNo;
	}

	/**
	 * 消费明细号
	 * 
	 * @param costNo
	 *            消费明细号
	 */
	public void setCostNo(String costNo) {
		this.costNo = costNo;
	}

	/**
	 * 订单项详情编号
	 * 
	 * @return OrderItemDetailNo 订单项详情编号
	 */
	public String getOrderItemDetailNo() {
		return orderItemDetailNo;
	}

	/**
	 * 订单项详情编号
	 * 
	 * @param orderItemDetailNo
	 *            订单项详情编号
	 */
	public void setOrderItemDetailNo(String orderItemDetailNo) {
		this.orderItemDetailNo = orderItemDetailNo;
	}

	/**
	 * 对应核心团单号
	 * 
	 * @return GrpContNo 对应核心团单号
	 */
	public String getGrpContNo() {
		return grpContNo;
	}

	/**
	 * 对应核心团单号
	 * 
	 * @param grpContNo
	 *            对应核心团单号
	 */
	public void setGrpContNo(String grpContNo) {
		this.grpContNo = grpContNo;
	}

	/**
	 * 对应核心个单号
	 * 
	 * @return ContNo 对应核心个单号
	 */
	public String getContNo() {
		return contNo;
	}

	/**
	 * 对应核心个单号
	 * 
	 * @param contNo
	 *            对应核心个单号
	 */
	public void setContNo(String contNo) {
		this.contNo = contNo;
	}

    /**
     * 
     * @return SelfPrem 
     */
    public Double getSelfPrem() {
        return selfPrem;
    }
	 
    /**
     * 
     * @param selfPrem 
     */
    public void setSelfPrem(Double selfPrem) {
        this.selfPrem = selfPrem;
    }

    /**
     * 0.00
     * @return GrpPrem 0.00
     */
    public Double getGrpPrem() {
        return grpPrem;
    }

    /**
     * 0.00
     * @param grpPrem 0.00
     */
    public void setGrpPrem(Double grpPrem) {
        this.grpPrem = grpPrem;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
	public String getOperator() {
		return operator;
	}

	/**
	 * 操作员
	 * 
	 * @param operator
	 *            操作员
	 */
	public void setOperator(String operator) {
		this.operator = operator;
	}

	/**
	 * 操作机构
	 * 
	 * @return OperatorCom 操作机构
	 */
	public String getOperatorCom() {
		return operatorCom;
	}

	/**
	 * 操作机构
	 * 
	 * @param operatorCom
	 *            操作机构
	 */
	public void setOperatorCom(String operatorCom) {
		this.operatorCom = operatorCom;
	}

	/**
	 * 生成日期
	 * 
	 * @return MakeDate 生成日期
	 */
	public String getMakeDate() {
		return makeDate;
	}

	/**
	 * 生成日期
	 * 
	 * @param makeDate
	 *            生成日期
	 */
	public void setMakeDate(String makeDate) {
		this.makeDate = makeDate;
	}

	/**
	 * 生成时间
	 * 
	 * @return MakeTime 生成时间
	 */
	public String getMakeTime() {
		return makeTime;
	}

	/**
	 * 生成时间
	 * 
	 * @param makeTime
	 *            生成时间
	 */
	public void setMakeTime(String makeTime) {
		this.makeTime = makeTime;
	}

	/**
	 * 修改日期
	 * 
	 * @return ModifyDate 修改日期
	 */
	public String getModifyDate() {
		return modifyDate;
	}

	/**
	 * 修改日期
	 * 
	 * @param modifyDate
	 *            修改日期
	 */
	public void setModifyDate(String modifyDate) {
		this.modifyDate = modifyDate;
	}

	/**
	 * 
	 * @return ModifyTime
	 */
	public String getModifyTime() {
		return modifyTime;
	}

	/**
	 * 
	 * @param modifyTime
	 */
	public void setModifyTime(String modifyTime) {
		this.modifyTime = modifyTime;
	}

	/**
	 * @return the fcOrderInsured
	 */
	public FCOrderInsured getFcOrderInsured() {
		return fcOrderInsured;
	}

	/**
	 * @param fcOrderInsured
	 *            the fcOrderInsured to set
	 */
	public void setFcOrderInsured(FCOrderInsured fcOrderInsured) {
		this.fcOrderInsured = fcOrderInsured;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see java.lang.Object#hashCode()
	 */
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((contNo == null) ? 0 : contNo.hashCode());
		result = prime * result + ((costNo == null) ? 0 : costNo.hashCode());
		result = prime * result + ((fcOrderInsured == null) ? 0 : fcOrderInsured.hashCode());
		result = prime * result + ((fcOrderItemDetail == null) ? 0 : fcOrderItemDetail.hashCode());
		result = prime * result + ((grpContNo == null) ? 0 : grpContNo.hashCode());
		result = prime * result + ((makeDate == null) ? 0 : makeDate.hashCode());
		result = prime * result + ((makeTime == null) ? 0 : makeTime.hashCode());
		result = prime * result + ((modifyDate == null) ? 0 : modifyDate.hashCode());
		result = prime * result + ((modifyTime == null) ? 0 : modifyTime.hashCode());
		result = prime * result + ((operator == null) ? 0 : operator.hashCode());
		result = prime * result + ((operatorCom == null) ? 0 : operatorCom.hashCode());
		result = prime * result + ((orderItemDetailNo == null) ? 0 : orderItemDetailNo.hashCode());
		result = prime * result + ((orderItemNo == null) ? 0 : orderItemNo.hashCode());
		result = prime * result + ((orderNo == null) ? 0 : orderNo.hashCode());
		return result;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see java.lang.Object#equals(java.lang.Object)
	 */
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		FCOrderItem other = (FCOrderItem) obj;
		if (contNo == null) {
			if (other.contNo != null)
				return false;
		} else if (!contNo.equals(other.contNo))
			return false;
		if (costNo == null) {
			if (other.costNo != null)
				return false;
		} else if (!costNo.equals(other.costNo))
			return false;
		if (fcOrderInsured == null) {
			if (other.fcOrderInsured != null)
				return false;
		} else if (!fcOrderInsured.equals(other.fcOrderInsured))
			return false;
		if (fcOrderItemDetail == null) {
			if (other.fcOrderItemDetail != null)
				return false;
		} else if (!fcOrderItemDetail.equals(other.fcOrderItemDetail))
			return false;
		if (grpContNo == null) {
			if (other.grpContNo != null)
				return false;
		} else if (!grpContNo.equals(other.grpContNo))
			return false;
		if (makeDate == null) {
			if (other.makeDate != null)
				return false;
		} else if (!makeDate.equals(other.makeDate))
			return false;
		if (makeTime == null) {
			if (other.makeTime != null)
				return false;
		} else if (!makeTime.equals(other.makeTime))
			return false;
		if (modifyDate == null) {
			if (other.modifyDate != null)
				return false;
		} else if (!modifyDate.equals(other.modifyDate))
			return false;
		if (modifyTime == null) {
			if (other.modifyTime != null)
				return false;
		} else if (!modifyTime.equals(other.modifyTime))
			return false;
		if (operator == null) {
			if (other.operator != null)
				return false;
		} else if (!operator.equals(other.operator))
			return false;
		if (operatorCom == null) {
			if (other.operatorCom != null)
				return false;
		} else if (!operatorCom.equals(other.operatorCom))
			return false;
		if (orderItemDetailNo == null) {
			if (other.orderItemDetailNo != null)
				return false;
		} else if (!orderItemDetailNo.equals(other.orderItemDetailNo))
			return false;
		if (orderItemNo == null) {
			if (other.orderItemNo != null)
				return false;
		} else if (!orderItemNo.equals(other.orderItemNo))
			return false;
		if (orderNo == null) {
			if (other.orderNo != null)
				return false;
		} else if (!orderNo.equals(other.orderNo))
			return false;
		return true;
	}

	public String getPayPersonId() {
		return payPersonId;
	}

	public void setPayPersonId(String payPersonId) {
		this.payPersonId = payPersonId;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return "FCOrderItem [orderItemNo=" + orderItemNo + ", orderNo=" + orderNo + ", costNo=" + costNo
				+ ", orderItemDetailNo=" + orderItemDetailNo + ", grpContNo=" + grpContNo + ", contNo=" + contNo
				+ ", operator=" + operator + ", operatorCom=" + operatorCom + ", makeDate=" + makeDate + ", makeTime="
				+ makeTime + ", modifyDate=" + modifyDate + ", modifyTime=" + modifyTime + ", fcOrderItemDetail="
				+ fcOrderItemDetail + ", fcOrderInsured=" + fcOrderInsured + "]";
	}

}