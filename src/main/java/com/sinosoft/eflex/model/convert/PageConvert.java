package com.sinosoft.eflex.model.convert;

import com.sinosoft.eflex.enums.GrpIdTypeEnum;
import com.sinosoft.eflex.model.BatchInsureInterface.Page;
import com.sinosoft.eflex.model.FCGrpInfo;
import com.sinosoft.eflex.model.FCPerInfo;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 转换
 *
 * <AUTHOR> * *
 */
public class PageConvert {


    public static List<Page> convert(FCGrpInfo fcGrpInfo, String policySignBaseAddress) {
        List<Page> pageList = new ArrayList<>();
        if (!StringUtils.isEmpty(fcGrpInfo.getGrpIDImage1())) {
            convert(fcGrpInfo.getGrpIDImage1(), pageList, policySignBaseAddress, GrpIdTypeEnum.BUSINESSLICENSE.getValue());
        }
        if (!StringUtils.isEmpty(fcGrpInfo.getGrpIDImage2())) {
            convert(fcGrpInfo.getGrpIDImage2(), pageList, policySignBaseAddress, GrpIdTypeEnum.BUSINESSLICENSE.getValue());
        }

        if (!StringUtils.isEmpty(fcGrpInfo.getLegIDImage1())) {
            convert(fcGrpInfo.getGrpIDImage2(), pageList, policySignBaseAddress, "法人证件影像");
        }
        if (!StringUtils.isEmpty(fcGrpInfo.getLegIDImage2())) {
            convert(fcGrpInfo.getGrpIDImage2(), pageList, policySignBaseAddress, "法人证件影像");
        }
        return pageList;
    }


    public static List<Page> convert(FCPerInfo fcPerInfo, String policySignBaseAddress, List<Page> pageList) {
        if (!StringUtils.isEmpty(fcPerInfo.getIdImage1())) {
            convert(fcPerInfo.getIdImage1(), pageList, policySignBaseAddress, "被保人" + fcPerInfo.getName() + "证件影像照");
        }
        if (!StringUtils.isEmpty(fcPerInfo.getIdImage2())) {
            convert(fcPerInfo.getIdImage2(), pageList, policySignBaseAddress, "被保人" + fcPerInfo.getName() + "证件影像照");
        }
        return pageList;
    }

    public static Page convert(String imageUrl, List<Page> pageList, String policySignBaseAddress, String imgName) {
        Page page = new Page();
        String imgUrl = imageUrl.substring(0, imageUrl.indexOf("?"));
        String coreImgUrl = imageUrl.substring(imgUrl.length() + 1);
        page.setImageUrl(policySignBaseAddress + coreImgUrl);
        page.setImageName(imgName);
        page.setPageCode(String.valueOf(pageList.size() + 1));
        pageList.add(page);
        return page;
    }
}
