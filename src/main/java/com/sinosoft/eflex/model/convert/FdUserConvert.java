package com.sinosoft.eflex.model.convert;

import com.sinosoft.eflex.model.FdUser;
import com.sinosoft.eflex.model.request.GrpContactInsertReq;
import com.sinosoft.eflex.util.CommonUtil;
import com.sinosoft.eflex.util.encrypt.LisIDEA;

/**
 * 转换*
 *
 * <AUTHOR> *
 */
public class FdUserConvert {

    public static FdUser insertFdUser(GrpContactInsertReq req, String userNo) {
        FdUser fdUser = new FdUser();
        LisIDEA encryPassword = new LisIDEA();
        //用户编号
        fdUser.setUserNo(userNo);
        //用户类型
        fdUser.setCustomType("2");
        //登陆姓名为证件号
        fdUser.setUserName(req.getIdNo());
        //昵称
        fdUser.setNickName(req.getName());
        //hr证件号
        fdUser.setIDNo(req.getIdNo());
        //用户密码为证件号后6位
        fdUser.setPassWord(encryPassword.encryptString(req.getIdNo().length() >= 6 ? req.getIdNo().substring(req.getIdNo().length() - 6) :
                req.getIdNo() + "000000".substring(0, 6 - req.getIdNo().length())));
        //手机号
        fdUser.setPhone(req.getMobile());
        //是否锁定
        fdUser.setIsLock("0");
        //是否是VIP
        fdUser.setIsVIP("N");
        //用户状态
        fdUser.setUserState("1");
        //登录失败次数
        fdUser.setLoginFailTimes(0);
        //操作员
        fdUser.setOperator(fdUser.getUserNo());
        return CommonUtil.initObject(fdUser, "INSERT");
    }
}
