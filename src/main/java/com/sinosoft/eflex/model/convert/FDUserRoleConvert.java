package com.sinosoft.eflex.model.convert;

import com.sinosoft.eflex.enums.MenuGrpCodeTypeEnum;
import com.sinosoft.eflex.model.FDUserRole;
import com.sinosoft.eflex.model.FDusertomenugrpKey;
import com.sinosoft.eflex.model.FdUser;
import com.sinosoft.eflex.util.CommonUtil;

/**
 * 转换*
 *
 * <AUTHOR> *
 */
public class FDUserRoleConvert {

    public static FDUserRole insertFDUserRole(FdUser fdUser, String userRoleSN) {
        FDUserRole fdUserRole = new FDUserRole();
        //用户角色流水号
        fdUserRole.setUserRoleSN(userRoleSN);
        //角色类型
        fdUserRole.setRoleType("2");
        //角色表用户编号
        fdUserRole.setUserNo(fdUser.getUserNo());
        //角色操作员
        fdUserRole.setOperator(fdUser.getUserNo());
        return CommonUtil.initObject(fdUserRole, "INSERT");
    }

    public static FDusertomenugrpKey initFDusertomenugrpKey(FdUser fdUser) {
        FDusertomenugrpKey fDusertomenugrpKey = new FDusertomenugrpKey();
        //菜单表的用户编号
        fDusertomenugrpKey.setUserNo(fdUser.getUserNo());
        //绑定菜单
        fDusertomenugrpKey.setMenuGrpCode(MenuGrpCodeTypeEnum.HR.getCode());
        return fDusertomenugrpKey;
    }

}
