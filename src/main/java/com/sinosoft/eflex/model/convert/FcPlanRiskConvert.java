package com.sinosoft.eflex.model.convert;

import com.sinosoft.eflex.model.FCPlanRisk;
import com.sinosoft.eflex.model.GlobalInput;
import com.sinosoft.eflex.model.request.RiskConfigReq;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 转换
 *
 * <AUTHOR> * *
 */
public class FcPlanRiskConvert {

    public static FCPlanRisk convert(RiskConfigReq risk, GlobalInput globalInput) {
        FCPlanRisk fcPlanRisk = new FCPlanRisk();
        fcPlanRisk.setReinsuranceMark(risk.getReinsuranceMark());
        fcPlanRisk.setGiftInsureSign(risk.getGiftInsureSign());
        fcPlanRisk.setOriginalPrem(null == risk.getOriginalPrem() ? null : risk.getOriginalPrem().doubleValue());
        fcPlanRisk.setFeeRatio(risk.getFeeRatio().doubleValue() / 100);
        fcPlanRisk.setCommissionOrAllowanceRatio(risk.getCommissionOrAllowanceRatio().doubleValue() / 100);
        fcPlanRisk.setOperator(globalInput.getUserNo());
        fcPlanRisk.setModifyDate(String.valueOf(LocalDate.now()));
        fcPlanRisk.setModifyTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        fcPlanRisk.setEnsureCode(risk.getEnsureCode());
        fcPlanRisk.setRiskCode(risk.getRiskCode());
        return fcPlanRisk;
    }
}
