package com.sinosoft.eflex.model.convert;

import com.sinosoft.eflex.enums.SMSTemplateNoEnum;
import com.sinosoft.eflex.model.FCEnsure;
import com.sinosoft.eflex.model.sendmessage.SendSMSReq;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class sendSMSReqConvert {

    public static SendSMSReq convert(String phone, FCEnsure fcEnsure) {
        SendSMSReq sendSMSReq = new SendSMSReq();
        sendSMSReq.setTemplateNo(SMSTemplateNoEnum.STAFF_BENEFITS_005.getCode());
        sendSMSReq.setPhones(phone);
        Map<String, Object> map = new HashMap<>(2);
        map.put("ensure_name", fcEnsure.getEnsureName());
        map.put("ensure_end_date", fcEnsure.getEndAppntDate());
        sendSMSReq.setParam(map);
        return sendSMSReq;
    }
}
