package com.sinosoft.eflex.model.convert;

import com.sinosoft.eflex.model.FCEnsure;
import com.sinosoft.eflex.util.CommonUtil;
import com.sinosoft.eflex.util.ConstantUtil;

import java.util.Objects;

/**
 * 转换
 *
 * <AUTHOR> * *
 */
public class FcEnsureConvert {

    public static FCEnsure convert(String ensureCode) {
        FCEnsure fcEnsure = new FCEnsure();
        fcEnsure.setEnsureCode(ensureCode);
        fcEnsure.setEnsureState(ConstantUtil.EnsureState_020);
        fcEnsure = CommonUtil.initObject(fcEnsure, "UPDATE");
        return fcEnsure;
    }


    public static FCEnsure convert(FCEnsure fcEnsure) {
        fcEnsure.setEnsureState(ConstantUtil.EnsureState_015);
        fcEnsure.setPolicyState("3");
        fcEnsure = CommonUtil.initObject(fcEnsure, "UPDATE");
        return fcEnsure;
    }


    public static FCEnsure convert(FCEnsure fcEnsure, String userNo, Boolean greenInsurance) {
        fcEnsure.setEnsureState(ConstantUtil.EnsureState_1);
        fcEnsure.setGreenInsurance(Objects.isNull(greenInsurance) ? fcEnsure.getGreenInsurance() : greenInsurance);
        fcEnsure.setPolicyState("1");
        fcEnsure.setOperator(userNo);
        fcEnsure = CommonUtil.initObject(fcEnsure, "UPDATE");
        return fcEnsure;

    }


}
