package com.sinosoft.eflex.model.convert;

import com.sinosoft.eflex.model.FDPwdHist;
import com.sinosoft.eflex.model.FdUser;
import com.sinosoft.eflex.util.CommonUtil;

/**
 * 转换*
 *
 * <AUTHOR> *
 */
public class FDPwdHistConvert {


    public static FDPwdHist convert(FdUser fdUser, String PassWordSN) {
        FDPwdHist fdPwdHist = new FDPwdHist();
        fdPwdHist.setPassWordSN(PassWordSN);
        fdPwdHist.setUserNo(fdUser.getUserNo());
        fdPwdHist.setPassWord(fdUser.getPassWord());
        fdPwdHist.setOperator(fdUser.getUserNo());
        return CommonUtil.initObject(fdPwdHist, "INSERT");
    }
}
