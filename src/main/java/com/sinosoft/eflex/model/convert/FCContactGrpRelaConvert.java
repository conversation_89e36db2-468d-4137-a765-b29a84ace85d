package com.sinosoft.eflex.model.convert;

import com.sinosoft.eflex.model.FCContactGrpRela;
import com.sinosoft.eflex.model.request.GrpContactReq;
import com.sinosoft.eflex.util.CommonUtil;

/**
 * 转换*
 *
 * <AUTHOR>
 */
public class FCContactGrpRelaConvert {

    public static FCContactGrpRela convert(GrpContactReq grpContactReq, String userNo) {
        FCContactGrpRela fcEnsureConfig = new FCContactGrpRela();
        fcEnsureConfig.setLockState("0");
        fcEnsureConfig.setOperator(userNo);
        fcEnsureConfig.setContactType("01");
        fcEnsureConfig.setContactNo(grpContactReq.getContactNo());
        fcEnsureConfig.setGrpNo(grpContactReq.getGrpNo());
        return CommonUtil.initObject(fcEnsureConfig, "INSERT");
    }


    public static FCContactGrpRela convert(String contactNo, String userNo, String grpNo) {
        FCContactGrpRela fcContactGrpRela = new FCContactGrpRela();
        fcContactGrpRela.setContactNo(contactNo);
        fcContactGrpRela.setGrpNo(grpNo);
        fcContactGrpRela.setContactType("01");
        fcContactGrpRela.setLockState("0");
        fcContactGrpRela.setOperator(userNo);
        return CommonUtil.initObject(fcContactGrpRela, "INSERT");
    }


}
