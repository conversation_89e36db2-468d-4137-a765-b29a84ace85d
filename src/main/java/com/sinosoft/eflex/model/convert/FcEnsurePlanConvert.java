package com.sinosoft.eflex.model.convert;

import com.sinosoft.eflex.enums.PlanStateEnum;
import com.sinosoft.eflex.model.FCEnsurePlan;
import com.sinosoft.eflex.model.request.FcEnsurePlanReq;
import com.sinosoft.eflex.util.CommonUtil;

/**
 * 转换
 *
 * <AUTHOR> * *
 */
public class FcEnsurePlanConvert {

    public static FCEnsurePlan convert(FcEnsurePlanReq fcEnsurePlanReq, String operationType) {
        FCEnsurePlan fcEnsurePlan = new FCEnsurePlan();
        fcEnsurePlan.setPlanCode(fcEnsurePlanReq.getPlanCode());
        fcEnsurePlan.setEnsureCode(fcEnsurePlanReq.getEnsureCode());
        fcEnsurePlan.setPlanName(fcEnsurePlanReq.getPlanName());
        fcEnsurePlan.setPlanObject(fcEnsurePlanReq.getPlanObject());
        fcEnsurePlan.setPlanKey(fcEnsurePlanReq.getPlanKey());
        fcEnsurePlan.setPlanState(fcEnsurePlanReq.getPlanState());
        fcEnsurePlan.setOperatorCom(fcEnsurePlanReq.getOperatorCom());
        fcEnsurePlan.setOperating(fcEnsurePlanReq.getOperating());
        fcEnsurePlan.setFcPlanRisks(fcEnsurePlanReq.getFcPlanRisks());
        fcEnsurePlan.setInsuredNumber(fcEnsurePlanReq.getInsuredNumber());
        fcEnsurePlan.setTotalPrem(fcEnsurePlanReq.getTotalPrem());
        fcEnsurePlan.setOperator(fcEnsurePlanReq.getOperator());
        CommonUtil.initObject(fcEnsurePlan, operationType);
        return fcEnsurePlan;
    }

    public static FcEnsurePlanReq convert(FcEnsurePlanReq fcEnsurePlanReq, Double totalPrem, String operator) {
        fcEnsurePlanReq.setPlanCode(fcEnsurePlanReq.getPlanCode());
        fcEnsurePlanReq.setEnsureCode(fcEnsurePlanReq.getEnsureCode());
        fcEnsurePlanReq.setPlanName(fcEnsurePlanReq.getPlanName());
        fcEnsurePlanReq.setPlanObject(fcEnsurePlanReq.getPlanObject());
        fcEnsurePlanReq.setPlanKey(fcEnsurePlanReq.getPlanKey());
        fcEnsurePlanReq.setPlanState(PlanStateEnum.MAKEDONE.getCode());
        fcEnsurePlanReq.setOperatorCom(fcEnsurePlanReq.getOperatorCom());
        fcEnsurePlanReq.setOperating(fcEnsurePlanReq.getOperating());
        fcEnsurePlanReq.setFcPlanRisks(fcEnsurePlanReq.getFcPlanRisks());
        fcEnsurePlanReq.setTotalPrem(totalPrem);
        fcEnsurePlanReq.setOperator(operator);
        fcEnsurePlanReq.setInsuredNumber(0);
        return fcEnsurePlanReq;
    }


}
