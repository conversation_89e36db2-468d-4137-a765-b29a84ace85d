package com.sinosoft.eflex.model.convert;

import com.sinosoft.eflex.model.FCGrpOrder;
import com.sinosoft.eflex.model.request.ToReviewAdoptRequest;
import com.sinosoft.eflex.util.CommonUtil;

/**
 * 转换
 *
 * <AUTHOR> * *
 */
public class FcGrpOrderConvert {


    public static FCGrpOrder convert(FCGrpOrder fcGrpOrder, String grpContNo) {
        // 团体订单状态 01-未提交 02-已提交 03-提交失败 04-投保成功 05-投保失败
        fcGrpOrder.setGrpOrderStatus("04");
        fcGrpOrder.setGrpContNo(grpContNo);
        fcGrpOrder = CommonUtil.initObject(fcGrpOrder, "UPDATE");
        return fcGrpOrder;
    }


    public static FCGrpOrder convert(ToReviewAdoptRequest reviewAdoptRequest, String grpOrderNo, String grpAppNo, String prtNo, String userNo) {
        FCGrpOrder fcGrpOrder = new FCGrpOrder();
        fcGrpOrder.setGrpOrderNo(grpOrderNo);
        fcGrpOrder.setEnsureCode(reviewAdoptRequest.getEnsureCode());
        fcGrpOrder.setGrpOrderType("01");
        fcGrpOrder.setGrpNo(reviewAdoptRequest.getGrpNo());
        fcGrpOrder.setGrpAppNo(grpAppNo);
        fcGrpOrder.setGrpOrderStatus("01");
        fcGrpOrder.setPrtNo(prtNo);
        fcGrpOrder.setOperator(userNo);
        CommonUtil.initObject(fcGrpOrder, "INSERT");
        return fcGrpOrder;
    }
}
