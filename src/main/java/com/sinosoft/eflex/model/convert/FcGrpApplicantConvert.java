package com.sinosoft.eflex.model.convert;

import com.sinosoft.eflex.model.FCGrpApplicant;
import com.sinosoft.eflex.model.FCGrpInfo;
import com.sinosoft.eflex.util.CommonUtil;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 */
public class FcGrpApplicantConvert {

    public static FCGrpApplicant convert(FCGrpInfo fcGrpInfo, String grpAppNo,String userNo) {
        FCGrpApplicant fcGrpApplicant = new FCGrpApplicant();
        fcGrpApplicant.setGrpAppNo(grpAppNo);
        BeanUtils.copyProperties(fcGrpInfo, fcGrpApplicant);
        fcGrpApplicant.setOperator(userNo);
        CommonUtil.initObject(fcGrpApplicant, "INSERT");
        return fcGrpApplicant;
    }
}
