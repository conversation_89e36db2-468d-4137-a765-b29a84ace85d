package com.sinosoft.eflex.model.convert;

import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.model.FCEnsure;
import com.sinosoft.eflex.model.FCEnsureConfig;
import com.sinosoft.eflex.model.core.CoreResponseDTO;
import com.sinosoft.eflex.util.CommonUtil;
import org.springframework.stereotype.Component;

/**
 * 转换*
 *
 * <AUTHOR> *
 */
@Component
public class FCEnsureConfigConvert {


    public static FCEnsureConfig convert(FCEnsure fcEnsure, String serialNo, CoreResponseDTO coreResponseDTO) {
        FCEnsureConfig fcEnsureConfig = new FCEnsureConfig();
        fcEnsureConfig.setEnsureCode(fcEnsure.getEnsureCode());
        fcEnsureConfig.setGrpNo(fcEnsure.getGrpNo());
        fcEnsureConfig.setOperator(fcEnsure.getOperator());
        // 增加查询 基础单签单结果
        fcEnsureConfig.setConfigNo("500");
        fcEnsureConfig.setConfigValue(JsonUtil.toJSON(coreResponseDTO));
        fcEnsureConfig.setSerialNo(serialNo);
        fcEnsureConfig = CommonUtil.initObject(fcEnsureConfig, "INSERT");
        return fcEnsureConfig;
    }

}

