package com.sinosoft.eflex.model;

public class FCPerAppnt {
    /**
     * 个人投保人编号
     */
    private String perAppNo;

    /**
     * 企业客户号
     */
    private String grpNo;

    /**
     * 个人客户号
     */
    private String perNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private String sex;

    /**
     * 证件类型
     */
    private String IDType;

    /**
     * 证件号
     */
    private String IDNo;

    /**
     * 出生日期
     */
    private String birthDay;

    /**
     * 职业类别
     */
    private String occupationType;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 入机时间
     */
    private String makeTime;

    /**
     * 入机日期
     */
    private String makeDate;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 个人投保人编号
     * @return PerAppNo 个人投保人编号
     */
    public String getPerAppNo() {
        return perAppNo;
    }

    /**
     * 个人投保人编号
     * @param perAppNo 个人投保人编号
     */
    public void setPerAppNo(String perAppNo) {
        this.perAppNo = perAppNo;
    }

    /**
     * 企业客户号
     * @return GrpNo 企业客户号
     */
    public String getGrpNo() {
        return grpNo;
    }

    /**
     * 企业客户号
     * @param grpNo 企业客户号
     */
    public void setGrpNo(String grpNo) {
        this.grpNo = grpNo;
    }

    /**
     * 个人客户号
     * @return PerNo 个人客户号
     */
    public String getPerNo() {
        return perNo;
    }

    /**
     * 个人客户号
     * @param perNo 个人客户号
     */
    public void setPerNo(String perNo) {
        this.perNo = perNo;
    }

    /**
     * 姓名
     * @return Name 姓名
     */
    public String getName() {
        return name;
    }

    /**
     * 姓名
     * @param name 姓名
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 性别
     * @return Sex 性别
     */
    public String getSex() {
        return sex;
    }

    /**
     * 性别
     * @param sex 性别
     */
    public void setSex(String sex) {
        this.sex = sex;
    }

    /**
     * 证件类型
     * @return IDType 证件类型
     */
    public String getIDType() {
        return IDType;
    }

    /**
     * 证件类型
     * @param IDType 证件类型
     */
    public void setIDType(String IDType) {
        this.IDType = IDType;
    }

    /**
     * 证件号
     * @return IDNo 证件号
     */
    public String getIDNo() {
        return IDNo;
    }

    /**
     * 证件号
     * @param IDNo 证件号
     */
    public void setIDNo(String IDNo) {
        this.IDNo = IDNo;
    }

    /**
     * 出生日期
     * @return BirthDay 出生日期
     */
    public String getBirthDay() {
        return birthDay;
    }

    /**
     * 出生日期
     * @param birthDay 出生日期
     */
    public void setBirthDay(String birthDay) {
        this.birthDay = birthDay;
    }

    /**
     * 职业类别
     * @return OccupationType 职业类别
     */
    public String getOccupationType() {
        return occupationType;
    }

    /**
     * 职业类别
     * @param occupationType 职业类别
     */
    public void setOccupationType(String occupationType) {
        this.occupationType = occupationType;
    }

    /**
     * 操作机构
     * @return OperatorCom 操作机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作机构
     * @param operatorCom 操作机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 入机时间
     * @return MakeTime 入机时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 入机时间
     * @param makeTime 入机时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 入机日期
     * @return MakeDate 入机日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 入机日期
     * @param makeDate 入机日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}