package com.sinosoft.eflex.model;

import java.util.Date;

public class FdCom {
    /**
     * 机构编码
     */
    private String manageCom;

    /**
     * 总公司
     */
    private String outComCode;

    /**
     * 机构名称
     */
    private String manageComName;

    /**
     * 机构简称
     */
    private String shortName;

    /**
     * 英文名称
     */
    private String englishName;

    /**
     * 机构地址
     */
    private String address;

    /**
     * 邮编
     */
    private String zipcode;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 传真
     */
    private String fax;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 网址
     */
    private String webAddress;

    /**
     * 总经理
     */
    private String satrapName;

    /**
     * 
     */
    private String comCodeisc;

    /**
     * 
     */
    private String otherComcode;

    /**
     * 
     */
    private String comNature;

    /**
     * 机构层级
     */
    private String comGrade;

    /**
     * 机构地区类型
     */
    private String comAreaType;

    /**
     * 上级机构
     */
    private String upComCode;

    /**
     * 机构所在省
     */
    private String province;

    /**
     * 机构所在市
     */
    private String city;

    /**
     * 机构所在县
     */
    private String county;

    /**
     * 
     */
    private String findb;

    /**
     * 
     */
    private String comCode;

    /**
     * 
     */
    private String serviceName;

    /**
     * 
     */
    private String servicePhone;

    /**
     * 
     */
    private String servicePostAddress;

    /**
     * 
     */
    private String prefectUralLevelcity;

    /**
     * 
     */
    private String segment1;

    /**
     * 
     */
    private String segment2;

    /**
     * 
     */
    private String segment3;

    /**
     * 
     */
    private String segment4;

    /**
     * 
     */
    private String segment5;

    /**
     * 
     */
    private Date makeDate;

    /**
     * 
     */
    private String makeTime;

    /**
     * 
     */
    private Date modifyDate;

    /**
     * 
     */
    private String modifyTime;

    /**
     * 机构编码
     * 
     * @return manageCom 机构编码
     */
    public String getManageCom() {
        return manageCom;
    }

    /**
     * 机构编码
     * 
     * @param manageCom
     *            机构编码
     */
    public void setManageCom(String manageCom) {
        this.manageCom = manageCom;
    }

    /**
     * 总公司
     * 
     * @return outComCode 总公司
     */
    public String getOutComCode() {
        return outComCode;
    }

    /**
     * 总公司
     * 
     * @param outComCode
     *            总公司
     */
    public void setOutComCode(String outComCode) {
        this.outComCode = outComCode;
    }

    /**
     * 机构名称
     * 
     * @return manageComName 机构名称
     */
    public String getManageComName() {
        return manageComName;
    }

    /**
     * 机构名称
     * 
     * @param manageComName
     *            机构名称
     */
    public void setManageComName(String manageComName) {
        this.manageComName = manageComName;
    }

    /**
     * 机构简称
     * 
     * @return shortName 机构简称
     */
    public String getShortName() {
        return shortName;
    }

    /**
     * 机构简称
     * 
     * @param shortName
     *            机构简称
     */
    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    /**
     * 英文名称
     * 
     * @return englishName 英文名称
     */
    public String getEnglishName() {
        return englishName;
    }

    /**
     * 英文名称
     * 
     * @param englishName
     *            英文名称
     */
    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    /**
     * 机构地址
     * 
     * @return address 机构地址
     */
    public String getAddress() {
        return address;
    }

    /**
     * 机构地址
     * 
     * @param address
     *            机构地址
     */
    public void setAddress(String address) {
        this.address = address;
    }

    /**
     * 邮编
     * 
     * @return zipcode 邮编
     */
    public String getZipcode() {
        return zipcode;
    }

    /**
     * 邮编
     * 
     * @param zipcode
     *            邮编
     */
    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }

    /**
     * 手机号
     * 
     * @return phone 手机号
     */
    public String getPhone() {
        return phone;
    }

    /**
     * 手机号
     * 
     * @param phone
     *            手机号
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * 传真
     * 
     * @return fax 传真
     */
    public String getFax() {
        return fax;
    }

    /**
     * 传真
     * 
     * @param fax
     *            传真
     */
    public void setFax(String fax) {
        this.fax = fax;
    }

    /**
     * 邮箱
     * 
     * @return email 邮箱
     */
    public String getEmail() {
        return email;
    }

    /**
     * 邮箱
     * 
     * @param email
     *            邮箱
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * 网址
     * 
     * @return webAddress 网址
     */
    public String getWebAddress() {
        return webAddress;
    }

    /**
     * 网址
     * 
     * @param webAddress
     *            网址
     */
    public void setWebAddress(String webAddress) {
        this.webAddress = webAddress;
    }

    /**
     * 总经理
     * 
     * @return satrapName 总经理
     */
    public String getSatrapName() {
        return satrapName;
    }

    /**
     * 总经理
     * 
     * @param satrapName
     *            总经理
     */
    public void setSatrapName(String satrapName) {
        this.satrapName = satrapName;
    }

    /**
     * 
     * @return comCodeisc
     */
    public String getComCodeisc() {
        return comCodeisc;
    }

    /**
     * 
     * @param comCodeisc
     */
    public void setComCodeisc(String comCodeisc) {
        this.comCodeisc = comCodeisc;
    }

    /**
     * 
     * @return otherComcode
     */
    public String getOtherComcode() {
        return otherComcode;
    }

    /**
     * 
     * @param otherComcode
     */
    public void setOtherComcode(String otherComcode) {
        this.otherComcode = otherComcode;
    }

    /**
     * 
     * @return comNature
     */
    public String getComNature() {
        return comNature;
    }

    /**
     * 
     * @param comNature
     */
    public void setComNature(String comNature) {
        this.comNature = comNature;
    }

    /**
     * 机构层级
     * 
     * @return comGrade 机构层级
     */
    public String getComGrade() {
        return comGrade;
    }

    /**
     * 机构层级
     * 
     * @param comGrade
     *            机构层级
     */
    public void setComGrade(String comGrade) {
        this.comGrade = comGrade;
    }

    /**
     * 机构地区类型
     * 
     * @return comAreaType 机构地区类型
     */
    public String getComAreaType() {
        return comAreaType;
    }

    /**
     * 机构地区类型
     * 
     * @param comAreaType
     *            机构地区类型
     */
    public void setComAreaType(String comAreaType) {
        this.comAreaType = comAreaType;
    }

    /**
     * 上级机构
     * 
     * @return upComCode 上级机构
     */
    public String getUpComCode() {
        return upComCode;
    }

    /**
     * 上级机构
     * 
     * @param upComCode
     *            上级机构
     */
    public void setUpComCode(String upComCode) {
        this.upComCode = upComCode;
    }

    /**
     * 机构所在省
     * 
     * @return province 机构所在省
     */
    public String getProvince() {
        return province;
    }

    /**
     * 机构所在省
     * 
     * @param province
     *            机构所在省
     */
    public void setProvince(String province) {
        this.province = province;
    }

    /**
     * 机构所在市
     * 
     * @return city 机构所在市
     */
    public String getCity() {
        return city;
    }

    /**
     * 机构所在市
     * 
     * @param city
     *            机构所在市
     */
    public void setCity(String city) {
        this.city = city;
    }

    /**
     * 机构所在县
     * 
     * @return county 机构所在县
     */
    public String getCounty() {
        return county;
    }

    /**
     * 机构所在县
     * 
     * @param county
     *            机构所在县
     */
    public void setCounty(String county) {
        this.county = county;
    }

    /**
     * 
     * @return findb
     */
    public String getFindb() {
        return findb;
    }

    /**
     * 
     * @param findb
     */
    public void setFindb(String findb) {
        this.findb = findb;
    }

    /**
     * 
     * @return comCode
     */
    public String getComCode() {
        return comCode;
    }

    /**
     * 
     * @param comCode
     */
    public void setComCode(String comCode) {
        this.comCode = comCode;
    }

    /**
     * 
     * @return serviceName
     */
    public String getServiceName() {
        return serviceName;
    }

    /**
     * 
     * @param serviceName
     */
    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    /**
     * 
     * @return servicePhone
     */
    public String getServicePhone() {
        return servicePhone;
    }

    /**
     * 
     * @param servicePhone
     */
    public void setServicePhone(String servicePhone) {
        this.servicePhone = servicePhone;
    }

    /**
     * 
     * @return servicePostAddress
     */
    public String getServicePostAddress() {
        return servicePostAddress;
    }

    /**
     * 
     * @param servicePostAddress
     */
    public void setServicePostAddress(String servicePostAddress) {
        this.servicePostAddress = servicePostAddress;
    }

    /**
     * 
     * @return prefectUralLevelcity
     */
    public String getPrefectUralLevelcity() {
        return prefectUralLevelcity;
    }

    /**
     * 
     * @param prefectUralLevelcity
     */
    public void setPrefectUralLevelcity(String prefectUralLevelcity) {
        this.prefectUralLevelcity = prefectUralLevelcity;
    }

    /**
     * 
     * @return segment1
     */
    public String getSegment1() {
        return segment1;
    }

    /**
     * 
     * @param segment1
     */
    public void setSegment1(String segment1) {
        this.segment1 = segment1;
    }

    /**
     * 
     * @return segment2
     */
    public String getSegment2() {
        return segment2;
    }

    /**
     * 
     * @param segment2
     */
    public void setSegment2(String segment2) {
        this.segment2 = segment2;
    }

    /**
     * 
     * @return segment3
     */
    public String getSegment3() {
        return segment3;
    }

    /**
     * 
     * @param segment3
     */
    public void setSegment3(String segment3) {
        this.segment3 = segment3;
    }

    /**
     * 
     * @return segment4
     */
    public String getSegment4() {
        return segment4;
    }

    /**
     * 
     * @param segment4
     */
    public void setSegment4(String segment4) {
        this.segment4 = segment4;
    }

    /**
     * 
     * @return segment5
     */
    public String getSegment5() {
        return segment5;
    }

    /**
     * 
     * @param segment5
     */
    public void setSegment5(String segment5) {
        this.segment5 = segment5;
    }

    /**
     * 
     * @return MakeDate
     */
    public Date getMakeDate() {
        return makeDate;
    }

    /**
     * 
     * @param makeDate
     */
    public void setMakeDate(Date makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 
     * @return MakeTime
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 
     * @param makeTime
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 
     * @return ModifyDate
     */
    public Date getModifyDate() {
        return modifyDate;
    }

    /**
     * 
     * @param modifyDate
     */
    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 
     * @return ModifyTime
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 
     * @param modifyTime
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}