package com.sinosoft.eflex.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 保障计划表
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@NoArgsConstructor
public class FCEnsurePlan implements Serializable {

    /**
     * 计划编码
     */
    private String planCode;

    /**
     * 福利编号
     */
    private String ensureCode;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 计划对象
     */
    private String planObject;

    /**
     * 计划重点
     */
    private String planKey;

    /**
     * 计划状态 01-定制完成 02-定制完成
     */
    private String planState;

    /**
     * 投保人数
     */
    private Integer insuredNumber;

    /**
     * 总保费
     */
    private Double totalPrem;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 生成日期
     */
    private String makeDate;

    /**
     * 生成时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * F
     */
    private String modifyTime;

    /**
     * 判断添加修改
     */
    private String operating;

    /**
     * 计划险种
     */
    private List<FCPlanRisk> fcPlanRisks;

}