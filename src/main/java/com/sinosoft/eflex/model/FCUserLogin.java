package com.sinosoft.eflex.model;


public class FCUserLogin {
    /**
     * 
     */
    private String loginserialno;

    /**
     * 
     */
    private String userno;

    /**
     * 
     */
    private String logindate;

    /**
     * 
     */
    private String logintime;

    /**
     * 
     */
    private String loginip;

    /**
     * 
     */
    private String logintype;

    /**
     * 
     */
    private String loginsource;

    /**
     * 
     */
    private String logoutdate;

    /**
     * 
     */
    private String logouttime;

    /**
     * 
     * @return LoginSerialNo 
     */
    public String getLoginserialno() {
        return loginserialno;
    }

    /**
     * 
     * @param loginserialno 
     */
    public void setLoginserialno(String loginserialno) {
        this.loginserialno = loginserialno;
    }

    /**
     * 
     * @return UserNo 
     */
    public String getUserno() {
        return userno;
    }

    /**
     * 
     * @param userno 
     */
    public void setUserno(String userno) {
        this.userno = userno;
    }

    /**
     * 
     * @return LoginDate 
     */
    public String getLogindate() {
        return logindate;
    }

    /**
     * 
     * @param logindate 
     */
    public void setLogindate(String logindate) {
        this.logindate = logindate;
    }

    /**
     * 
     * @return LoginTime 
     */
    public String getLogintime() {
        return logintime;
    }

    /**
     * 
     * @param logintime 
     */
    public void setLogintime(String logintime) {
        this.logintime = logintime;
    }

    /**
     * 
     * @return LoginIP 
     */
    public String getLoginip() {
        return loginip;
    }

    /**
     * 
     * @param loginip 
     */
    public void setLoginip(String loginip) {
        this.loginip = loginip;
    }

    /**
     * 
     * @return LoginType 
     */
    public String getLogintype() {
        return logintype;
    }

    /**
     * 
     * @param logintype 
     */
    public void setLogintype(String logintype) {
        this.logintype = logintype;
    }

    /**
     * 
     * @return LoginSource 
     */
    public String getLoginsource() {
        return loginsource;
    }

    /**
     * 
     * @param loginsource 
     */
    public void setLoginsource(String loginsource) {
        this.loginsource = loginsource;
    }

    /**
     * 
     * @return LogoutDate 
     */
    public String getLogoutdate() {
        return logoutdate;
    }

    /**
     * 
     * @param logoutdate 
     */
    public void setLogoutdate(String logoutdate) {
        this.logoutdate = logoutdate;
    }

    /**
     * 
     * @return LogoutTime 
     */
    public String getLogouttime() {
        return logouttime;
    }

    /**
     * 
     * @param logouttime 
     */
    public void setLogouttime(String logouttime) {
        this.logouttime = logouttime;
    }
}