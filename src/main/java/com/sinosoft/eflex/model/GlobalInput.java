package com.sinosoft.eflex.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class GlobalInput implements Serializable {

	private static final long serialVersionUID = -2845347619690321849L;
	/** 登录流水号 */
	public String loginSerialNo;
	/** 用户编号 */
	public String userNo;
	/** 用户名 */
	public String userName;
	/** 用户昵称 */
	public String nickName;
	/** 客户类型 */
	public String customType;
	/** 客户号 */
	public String customNo;
	/** 角色类型 */
	public String roleType;
	/** 所属企业编码 */
	public String grpNo;
	/** 真实姓名 **/
	public String name;
	/** 性别 */
	public String sex;
    /**
     * 管理机构
     */
    public String manageCom;
	/** 弹性福利编码 */
	public String ensureCode;
	/** 上次登录时间 */
	public String oldLoginTime;
	/** 上次登录日期 */
	public String oldLoginDate;

	public String getLoginSerialNo() {
		return loginSerialNo;
	}

	public void setLoginSerialNo(String loginSerialNo) {
		this.loginSerialNo = loginSerialNo;
	}

	public String getUserNo() {
		return userNo;
	}

	public void setUserNo(String userNo) {
		this.userNo = userNo;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getNickName() {
		return nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName;
	}

	public String getCustomType() {
		return customType;
	}

	public void setCustomType(String customType) {
		this.customType = customType;
	}

	public String getCustomNo() {
		return customNo;
	}

	public void setCustomNo(String customNo) {
		this.customNo = customNo;
	}

	public String getRoleType() {
		return roleType;
	}

	public void setRoleType(String roleType) {
		this.roleType = roleType;
	}

	public String getGrpNo() {
		return grpNo;
	}

	public void setGrpNo(String grpNo) {
		this.grpNo = grpNo;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public String getEnsureCode() {
		return ensureCode;
	}

	public void setEnsureCode(String ensureCode) {
		this.ensureCode = ensureCode;
	}

	public String getOldLoginTime() {
		return oldLoginTime;
	}

	public void setOldLoginTime(String oldLoginTime) {
		this.oldLoginTime = oldLoginTime;
	}

	public String getOldLoginDate() {
		return oldLoginDate;
	}

	public void setOldLoginDate(String oldLoginDate) {
		this.oldLoginDate = oldLoginDate;
	}

    public String getManageCom() {
        return manageCom;
    }

    public void setManageCom(String manageCom) {
        this.manageCom = manageCom;
    }
}
