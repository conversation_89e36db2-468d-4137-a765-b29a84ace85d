package com.sinosoft.eflex.model;

import java.util.Date;
import java.util.List;

public class FcBusinessProDutyGrpObject {
    /**
     * 流水号
     */
    private String serialNo;

    /**
     * 福利编号
     */
    private String ensureCode;

    /**
     * 保额档次编码
     */
    private String amountGrageCode;

    /**
     * 企业客户号
     */
    private String grpNo;

    /**
     * 责任名称
     */
    private String dutyName;

    /**
     * 职级上限
     */
    private String gradeLevelTopLimit;

    /**
     * 职级下限
     */
    private String gradeLevelLowLimit;

    private String gradeLevelLowLimitName;

    private String gradeLevelTopLimitName;

    /**
     * 职级类别上限
     */
    private String occupationTypeTopLimit;

    /**
     * 职级类别下限
     */
    private String occupationTypeLowLimit;

    /**
     * 投保年龄上限
     */
    private String ageTopLimit;

    /**
     * 投保年龄下限
     */
    private String ageLowLimit;

    /**
     * 服务年限上限
     */
    private String comeAgeTopLimit;

    /**
     * 服务年限下限
     */
    private String comeAgeLowLimit;

    /**
     * 性别
     */
    private String sex;

    /**
     * 是否退休
     */
    private String retirement;

    /**
     * 免赔额默认档次
     */
    private Double defaultDeductible;

    /**
     * 赔付比例默认档次
     */
    private Double defaultCompensationRatio;

    /**
     * 是否为默认档次
     */
    private String isDefaultFlag;

    /**
     * 保额
     */
    private Double amnt;

    /**
     * 保费
     */
    private Double prem;

    /**
     * 被保人类别 01-本人  02-配偶  03-子女  04-父母
     */
    private String insuredType;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 管理机构
     */
    private String operatorCom;

    /**
     * 入机日期
     */
    private String makeDate;

    /**
     * 入机时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 保额档次
     */
    private List<FcDutyAmountGrade> fcDutyAmountGradeList;

    /**
     * 流水号
     * @return SerialNo 流水号
     */
    public String getSerialNo() {
        return serialNo;
    }

    /**
     * 流水号
     * @param serialNo 流水号
     */
    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    /**
     * 福利编号
     * @return EnsureCode 福利编号
     */
    public String getEnsureCode() {
        return ensureCode;
    }

    /**
     * 福利编号
     * @param ensureCode 福利编号
     */
    public void setEnsureCode(String ensureCode) {
        this.ensureCode = ensureCode;
    }

    /**
     * 保额档次编码
     * @return AmountGrageCode 保额档次编码
     */
    public String getAmountGrageCode() {
        return amountGrageCode;
    }

    /**
     * 保额档次编码
     * @param amountGrageCode 保额档次编码
     */
    public void setAmountGrageCode(String amountGrageCode) {
        this.amountGrageCode = amountGrageCode;
    }

    /**
     * 企业客户号
     * @return GrpNo 企业客户号
     */
    public String getGrpNo() {
        return grpNo;
    }

    /**
     * 企业客户号
     * @param grpNo 企业客户号
     */
    public void setGrpNo(String grpNo) {
        this.grpNo = grpNo;
    }

    /**
     * 职级上限
     * @return GradeLevelTopLimit 职级上限
     */
    public String getGradeLevelTopLimit() {
        return gradeLevelTopLimit;
    }

    /**
     * 职级上限
     * @param gradeLevelTopLimit 职级上限
     */
    public void setGradeLevelTopLimit(String gradeLevelTopLimit) {
        this.gradeLevelTopLimit = gradeLevelTopLimit;
    }

    /**
     * 职级下限
     * @return GradeLevelLowLimit 职级下限
     */
    public String getGradeLevelLowLimit() {
        return gradeLevelLowLimit;
    }

    /**
     * 职级下限
     * @param gradeLevelLowLimit 职级下限
     */
    public void setGradeLevelLowLimit(String gradeLevelLowLimit) {
        this.gradeLevelLowLimit = gradeLevelLowLimit;
    }

    public String getGradeLevelLowLimitName() {
        return gradeLevelLowLimitName;
    }

    public void setGradeLevelLowLimitName(String gradeLevelLowLimitName) {
        this.gradeLevelLowLimitName = gradeLevelLowLimitName;
    }

    public String getGradeLevelTopLimitName() {
        return gradeLevelTopLimitName;
    }

    public void setGradeLevelTopLimitName(String gradeLevelTopLimitName) {
        this.gradeLevelTopLimitName = gradeLevelTopLimitName;
    }

    /**
     * 职级类别上限
     * @return OccupationTypeTopLimit 职级类别上限
     */
    public String getOccupationTypeTopLimit() {
        return occupationTypeTopLimit;
    }

    /**
     * 职级类别上限
     * @param occupationTypeTopLimit 职级类别上限
     */
    public void setOccupationTypeTopLimit(String occupationTypeTopLimit) {
        this.occupationTypeTopLimit = occupationTypeTopLimit;
    }

    /**
     * 职级类别下限
     * @return OccupationTypeLowLimit 职级类别下限
     */
    public String getOccupationTypeLowLimit() {
        return occupationTypeLowLimit;
    }

    /**
     * 职级类别下限
     * @param occupationTypeLowLimit 职级类别下限
     */
    public void setOccupationTypeLowLimit(String occupationTypeLowLimit) {
        this.occupationTypeLowLimit = occupationTypeLowLimit;
    }

    /**
     * 投保年龄上限
     * @return AgeTopLimit 投保年龄上限
     */
    public String getAgeTopLimit() {
        return ageTopLimit;
    }

    /**
     * 投保年龄上限
     * @param ageTopLimit 投保年龄上限
     */
    public void setAgeTopLimit(String ageTopLimit) {
        this.ageTopLimit = ageTopLimit;
    }

    /**
     * 投保年龄下限
     * @return AgeLowLimit 投保年龄下限
     */
    public String getAgeLowLimit() {
        return ageLowLimit;
    }

    /**
     * 投保年龄下限
     * @param ageLowLimit 投保年龄下限
     */
    public void setAgeLowLimit(String ageLowLimit) {
        this.ageLowLimit = ageLowLimit;
    }

    /**
     * 服务年限上限
     * @return ComeAgeTopLimit 服务年限上限
     */
    public String getComeAgeTopLimit() {
        return comeAgeTopLimit;
    }

    /**
     * 服务年限上限
     * @param comeAgeTopLimit 服务年限上限
     */
    public void setComeAgeTopLimit(String comeAgeTopLimit) {
        this.comeAgeTopLimit = comeAgeTopLimit;
    }

    /**
     * 服务年限下限
     * @return ComeAgeLowLimit 服务年限下限
     */
    public String getComeAgeLowLimit() {
        return comeAgeLowLimit;
    }

    /**
     * 服务年限下限
     * @param comeAgeLowLimit 服务年限下限
     */
    public void setComeAgeLowLimit(String comeAgeLowLimit) {
        this.comeAgeLowLimit = comeAgeLowLimit;
    }

    /**
     * 性别
     * @return Sex 性别
     */
    public String getSex() {
        return sex;
    }

    /**
     * 性别
     * @param sex 性别
     */
    public void setSex(String sex) {
        this.sex = sex;
    }

    /**
     * 是否退休
     * @return Retirement 是否退休
     */
    public String getRetirement() {
        return retirement;
    }

    /**
     * 是否退休
     * @param retirement 是否退休
     */
    public void setRetirement(String retirement) {
        this.retirement = retirement;
    }

    /**
     * 免赔额默认档次
     * @return DefaultDeductible 免赔额默认档次
     */
    public Double getDefaultDeductible() {
        return defaultDeductible;
    }

    /**
     * 免赔额默认档次
     * @param defaultDeductible 免赔额默认档次
     */
    public void setDefaultDeductible(Double defaultDeductible) {
        this.defaultDeductible = defaultDeductible;
    }

    /**
     * 赔付比例默认档次
     * @return DefaultCompensationRatio 赔付比例默认档次
     */
    public Double getDefaultCompensationRatio() {
        return defaultCompensationRatio;
    }

    /**
     * 赔付比例默认档次
     * @param defaultCompensationRatio 赔付比例默认档次
     */
    public void setDefaultCompensationRatio(Double defaultCompensationRatio) {
        this.defaultCompensationRatio = defaultCompensationRatio;
    }

    /**
     * 是否为默认档次
     * @return IsDefaultFlag 是否为默认档次
     */
    public String getIsDefaultFlag() {
        return isDefaultFlag;
    }

    /**
     * 是否为默认档次
     * @param isDefaultFlag 是否为默认档次
     */
    public void setIsDefaultFlag(String isDefaultFlag) {
        this.isDefaultFlag = isDefaultFlag;
    }

    /**
     * 保额
     * @return Amnt 保额
     */
    public Double getAmnt() {
        return amnt;
    }

    /**
     * 保额
     * @param amnt 保额
     */
    public void setAmnt(Double amnt) {
        this.amnt = amnt;
    }

    /**
     * 保费
     * @return Prem 保费
     */
    public Double getPrem() {
        return prem;
    }

    /**
     * 保费
     * @param prem 保费
     */
    public void setPrem(Double prem) {
        this.prem = prem;
    }

    /**
     * 被保人类别 01-本人  02-配偶  03-子女  04-父母
     * @return InsuredType 被保人类别 01-本人  02-配偶  03-子女  04-父母
     */
    public String getInsuredType() {
        return insuredType;
    }

    /**
     * 被保人类别 01-本人  02-配偶  03-子女  04-父母
     * @param insuredType 被保人类别 01-本人  02-配偶  03-子女  04-父母
     */
    public void setInsuredType(String insuredType) {
        this.insuredType = insuredType;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 管理机构
     * @return OperatorCom 管理机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 管理机构
     * @param operatorCom 管理机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 入机日期
     * @return MakeDate 入机日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 入机日期
     * @param makeDate 入机日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 入机时间
     * @return MakeTime 入机时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 入机时间
     * @param makeTime 入机时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public List<FcDutyAmountGrade> getFcDutyAmountGradeList() {
        return fcDutyAmountGradeList;
    }

    public void setFcDutyAmountGradeList(List<FcDutyAmountGrade> fcDutyAmountGradeList) {
        this.fcDutyAmountGradeList = fcDutyAmountGradeList;
    }

    public String getDutyName() {
        return dutyName;
    }

    public void setDutyName(String dutyName) {
        this.dutyName = dutyName;
    }
}