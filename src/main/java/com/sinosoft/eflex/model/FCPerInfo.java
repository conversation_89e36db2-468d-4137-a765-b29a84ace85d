package com.sinosoft.eflex.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FCPerInfo {
    private String edorType;
    private String oldIdNo;
    /**
     * 个人客户号
            来自EBA系统
     */
    private String perNo;

    /**
     * relationship 与投保人关系
     */
    private  String relationship;
    /**
     * 证件影像照1
     */
    private String idImage1;
    /**
     * 证件影像照2
     */
    private String idImage2;

    /**
     * 
     */
    private String CPerNo;

    /**
     * 所属企业客户号
     */
    private String grpNo;

    /**
     * 员工编号
     */
    private String staffNo;

    /**
     * 员工层级编码
     */
    private String levelCode;

    /**
     * 部门
     */
    private String department;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private String sex;

    /**
     * 国籍
     */
    private String nativeplace;
    
    public String getNativeplaceName() {
		return nativeplaceName;
	}

	public void setNativeplaceName(String nativeplaceName) {
		this.nativeplaceName = nativeplaceName;
	}

	private String nativeplaceName;

    public String getNativeplace() {
		return nativeplace;
	}

	public void setNativeplace(String nativeplace) {
		this.nativeplace = nativeplace;
	}

    private String sexName;

    /**
     * 0--身份证
            1--护照
            2--军官证
            3--驾照
            4--户口本
            5--学生证
            6--工作证
            8--其他
            9--无证件
            a--社保号
            （与eba保持一致）
     */
    @JsonProperty
    private String IDType;

    @JsonProperty
    private String IDTypeName;

    /**
     * 
     */
    @JsonProperty
    private String IDNo;

    
    private String idTypeEndDate;

	/**
     * 
     */
    private String birthDay;

    /**
     * 固定电话
     */
    private String phone;

    /**
     * 移动电话
     */
    private String mobilePhone;

    /**
     * 职业类别
     */
    private String occupationType;
    
    private String occupationTypeName;
    
    public String getOccupationTypeName() {
		return occupationTypeName;
	}

	public void setOccupationTypeName(String occupationTypeName) {
		this.occupationTypeName = occupationTypeName;
	}

	/**
     * 职业代码
     */
    private String occupationCode;
    
    private String occupationName;
     

    public String getOccupationName() {
		return occupationName;
	}

	public void setOccupationName(String occupationName) {
		this.occupationName = occupationName;
	}

	/**
     * 有无医保 0-否
  1-是
     */
    private String joinMedProtect;

    /**
     * 医保类型 01--城镇职工
 02--城镇居民
 03--新农合
     */
    private String medProtectType;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 地址
     */
    private String address;

    /**
     * 默认计划编码
     */
    private String defaultPlan;

    /**
     * 开户银行
     */
    private String openBank;

    /**
     * 开户账户
     */
    private String openAccount;

    /**
     * 
     */
    private String operatorCom;

    /**
     * 
     */
    private String operator;

    /**
     * 
     */
    private String makeDate;

    /**
     * 
     */
    private String makeTime;

    /**
     * 
     */
    private String modifyDate;

    /**
     * 
     */
    private String modifyTime;
    //省
    private String province;
    //市
    private String city;
    //县
    private String county;
    //详细地址
    private String detaileAddress;


    private FCStaffFamilyRela fcStaffFamilyRela;

    public FCStaffFamilyRela getFcStaffFamilyRela() {
        return fcStaffFamilyRela;
    }

    public void setFcStaffFamilyRela(FCStaffFamilyRela fcStaffFamilyRela) {
        this.fcStaffFamilyRela = fcStaffFamilyRela;
    }

    public String getIdImage1() {
        return idImage1;
    }

    public void setIdImage1(String idImage1) {
        this.idImage1 = idImage1;
    }

    public String getIdImage2() {
        return idImage2;
    }

    public void setIdImage2(String idImage2) {
        this.idImage2 = idImage2;
    }

    /**
     * 个人客户号
            来自EBA系统
     * @return PerNo 个人客户号
            来自EBA系统
     */
    public String getPerNo() {
        return perNo;
    }

    /**
     * 个人客户号
            来自EBA系统
     * @param perNo 个人客户号
            来自EBA系统
     */
    public void setPerNo(String perNo) {
        this.perNo = perNo;
    }

    /**
     * 
     * @return CPerNo 
     */
    public String getCPerNo() {
        return CPerNo;
    }

    /**
     * 
     * @param CPerNo 
     */
    public void setCPerNo(String CPerNo) {
        this.CPerNo = CPerNo;
    }

    /**
     * 所属企业客户号
     * @return GrpNo 所属企业客户号
     */
    public String getGrpNo() {
        return grpNo;
    }

    /**
     * 所属企业客户号
     * @param grpNo 所属企业客户号
     */
    public void setGrpNo(String grpNo) {
        this.grpNo = grpNo;
    }

    /**
     * 员工编号
     * @return StaffNo 员工编号
     */
    public String getStaffNo() {
        return staffNo;
    }

    /**
     * 员工编号
     * @param staffNo 员工编号
     */
    public void setStaffNo(String staffNo) {
        this.staffNo = staffNo;
    }

    /**
     * 员工层级编码
     * @return LevelCode 员工层级编码
     */
    public String getLevelCode() {
        return levelCode;
    }

    /**
     * 员工层级编码
     * @param levelCode 员工层级编码
     */
    public void setLevelCode(String levelCode) {
        this.levelCode = levelCode;
    }

    /**
     * 部门
     * @return department 部门
     */
    public String getDepartment() {
        return department;
    }

    /**
     * 部门
     * @param department 部门
     */
    public void setDepartment(String department) {
        this.department = department;
    }

    /**
     * 
     * @return Name 
     */
    public String getName() {
        return name;
    }

    /**
     * 
     * @param name 
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 
     * @return Sex 
     */
    public String getSex() {
        return sex;
    }

    /**
     * 
     * @param sex 
     */
    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    /**
     * 0--身份证
            1--护照
            2--军官证
            3--驾照
            4--户口本
            5--学生证
            6--工作证
            8--其他
            9--无证件
            a--社保号
            （与eba保持一致）
     * @return IDType 0--身份证
            1--护照
            2--军官证
            3--驾照
            4--户口本
            5--学生证
            6--工作证
            8--其他
            9--无证件
            a--社保号
            （与eba保持一致）
     */
    @JsonIgnore
    public String getIDType() {
        return IDType;
    }

    /**
     * 0--身份证
            1--护照
            2--军官证
            3--驾照
            4--户口本
            5--学生证
            6--工作证
            8--其他
            9--无证件
            a--社保号
            （与eba保持一致）
     * @param IDType 0--身份证
            1--护照
            2--军官证
            3--驾照
            4--户口本
            5--学生证
            6--工作证
            8--其他
            9--无证件
            a--社保号
            （与eba保持一致）
     */
    @JsonIgnore
    public void setIDType(String IDType) {
        this.IDType = IDType;
    }

    @JsonIgnore
    public String getIDTypeName() {
        return IDTypeName;
    }

    @JsonIgnore
    public void setIDTypeName(String IDTypeName) {
        this.IDTypeName = IDTypeName;
    }

    /**
     * 
     * @return IDNo 
     */
    @JsonIgnore
    public String getIDNo() {
        return IDNo;
    }

    /**
     * 
     * @param IDNo 
     */
    @JsonIgnore
    public void setIDNo(String IDNo) {
        this.IDNo = IDNo;
    }

    public String getIdTypeEndDate() {
		return idTypeEndDate;
	}

	public void setIdTypeEndDate(String idTypeEndDate) {
		this.idTypeEndDate = idTypeEndDate;
	}	
    
    /**
     * 
     * @return BirthDay 
     */
    public String getBirthDay() {
        return birthDay;
    }

    /**
     * 
     * @param birthDay 
     */
    public void setBirthDay(String birthDay) {
        this.birthDay = birthDay;
    }

    /**
     * 固定电话
     * @return Phone 固定电话
     */
    public String getPhone() {
        return phone;
    }

    /**
     * 固定电话
     * @param phone 固定电话
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * 移动电话
     * @return MobilePhone 移动电话
     */
    public String getMobilePhone() {
        return mobilePhone;
    }

    /**
     * 移动电话
     * @param mobilePhone 移动电话
     */
    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    /**
     * 职业类别
     * @return OccupationType 职业类别
     */
    public String getOccupationType() {
        return occupationType;
    }

    /**
     * 职业类别
     * @param occupationType 职业类别
     */
    public void setOccupationType(String occupationType) {
        this.occupationType = occupationType;
    }

    /**
     * 职业代码
     * @return OccupationCode 职业代码
     */
    public String getOccupationCode() {
        return occupationCode;
    }

    /**
     * 职业代码
     * @param occupationCode 职业代码
     */
    public void setOccupationCode(String occupationCode) {
        this.occupationCode = occupationCode;
    }

    /**
     * 有无医保 0-否
  1-是
     * @return JoinMedProtect 有无医保 0-否
  1-是
     */
    public String getJoinMedProtect() {
        return joinMedProtect;
    }

    /**
     * 有无医保 0-否
  1-是
     * @param joinMedProtect 有无医保 0-否
  1-是
     */
    public void setJoinMedProtect(String joinMedProtect) {
        this.joinMedProtect = joinMedProtect;
    }

    /**
     * 医保类型 01--城镇职工
 02--城镇居民
 03--新农合
     * @return MedProtectType 医保类型 01--城镇职工
 02--城镇居民
 03--新农合
     */
    public String getMedProtectType() {
        return medProtectType;
    }

    /**
     * 医保类型 01--城镇职工
 02--城镇居民
 03--新农合
     * @param medProtectType 医保类型 01--城镇职工
 02--城镇居民
 03--新农合
     */
    public void setMedProtectType(String medProtectType) {
        this.medProtectType = medProtectType;
    }

    /**
     * 邮箱
     * @return Email 邮箱
     */
    public String getEmail() {
        return email;
    }

    /**
     * 邮箱
     * @param email 邮箱
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * 邮编
     * @return ZipCode 邮编
     */
    public String getZipCode() {
        return zipCode;
    }

    /**
     * 邮编
     * @param zipCode 邮编
     */
    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    /**
     * 地址
     * @return Address 地址
     */
    public String getAddress() {
        return address;
    }

    /**
     * 地址
     * @param address 地址
     */
    public void setAddress(String address) {
        this.address = address;
    }

    /**
     * 默认计划编码
     * @return DefaultPlan 默认计划编码
     */
    public String getDefaultPlan() {
        return defaultPlan;
    }

    /**
     * 默认计划编码
     * @param defaultPlan 默认计划编码
     */
    public void setDefaultPlan(String defaultPlan) {
        this.defaultPlan = defaultPlan;
    }

    /**
     * 开户银行
     * @return OpenBank 开户银行
     */
    public String getOpenBank() {
        return openBank;
    }

    /**
     * 开户银行
     * @param openBank 开户银行
     */
    public void setOpenBank(String openBank) {
        this.openBank = openBank;
    }

    /**
     * 开户账户
     * @return OpenAccount 开户账户
     */
    public String getOpenAccount() {
        return openAccount;
    }

    /**
     * 开户账户
     * @param openAccount 开户账户
     */
    public void setOpenAccount(String openAccount) {
        this.openAccount = openAccount;
    }

    /**
     * 
     * @return OperatorCom 
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 
     * @param operatorCom 
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 
     * @return Operator 
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 
     * @param operator 
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 
     * @return MakeDate 
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 
     * @param makeDate 
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 
     * @return MakeTime 
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 
     * @param makeTime 
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 
     * @return ModifyDate 
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 
     * @param modifyDate 
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 
     * @return ModifyTime 
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 
     * @param modifyTime 
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
    private String serviceTerm;//服务年限
    private String retirement;//是否退休

	public String getServiceTerm() {
		return serviceTerm;
	}

	public void setServiceTerm(String serviceTerm) {
		this.serviceTerm = serviceTerm;
	}

	public String getRetirement() {
		return retirement;
	}

	public void setRetirement(String retirement) {
		this.retirement = retirement;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	public String getDetaileAddress() {
		return detaileAddress;
	}

	public void setDetaileAddress(String detaileAddress) {
		this.detaileAddress = detaileAddress;
	}


	
}