package com.sinosoft.eflex.model;

import com.sinosoft.eflex.model.BatchInsureInterface.RiskInfo;
import com.sinosoft.eflex.model.claim.ClainRiskInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-11-06 10:18
 */
public class GrpClaimCount {
    //本年度理赔人次
    private String TotalPersons;
    //本年度理赔总额
    private String TotalAmount;
    //险种信息对象
    private List<RiskInfo> RiskList;


    public String getTotalPersons() {
        return TotalPersons;
    }

    public void setTotalPersons(String totalPersons) {
        TotalPersons = totalPersons;
    }

    public String getTotalAmount() {
        return TotalAmount;
    }

    public void setTotalAmount(String totalAmount) {
        TotalAmount = totalAmount;
    }

    public List<RiskInfo> getRiskList() {
        return RiskList;
    }

    public void setRiskList(List<RiskInfo> riskList) {
        RiskList = riskList;
    }
}
