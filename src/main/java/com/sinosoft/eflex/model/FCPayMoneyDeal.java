package com.sinosoft.eflex.model;

import java.util.Date;

public class FCPayMoneyDeal {
    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付流水号
     */
    private String payFlowNo;

    /**
     * 支付金额
     */
    private Double amount;

    /**
     * 支付状态  0-有效；1-无效
     */
    private String payStatus;

    /**
     * 收银台收单号
     */
    private String paPayDeskFlowNo;

    /**
     * 支付方式 01-微信；02-支付宝；03-银行卡；
     */
    private String payType;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 管理机构
     */
    private String operatorCom;

    /**
     * 入机日期
     */
    private Date makeDate;

    /**
     * 入机时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private Date modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 订单号
     * @return OrderNo 订单号
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * 订单号
     * @param orderNo 订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * 支付流水号
     * @return PayFlowNo 支付流水号
     */
    public String getPayFlowNo() {
        return payFlowNo;
    }

    /**
     * 支付流水号
     * @param payFlowNo 支付流水号
     */
    public void setPayFlowNo(String payFlowNo) {
        this.payFlowNo = payFlowNo;
    }

    /**
     * 支付金额
     * @return Amount 支付金额
     */
    public Double getAmount() {
        return amount;
    }

    /**
     * 支付金额
     * @param amount 支付金额
     */
    public void setAmount(Double amount) {
        this.amount = amount;
    }

    /**
     * 支付状态  0-有效；1-无效
     * @return PayStatus 支付状态  0-有效；1-无效
     */
    public String getPayStatus() {
        return payStatus;
    }

    /**
     * 支付状态  0-有效；1-无效
     * @param payStatus 支付状态  0-有效；1-无效
     */
    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }

    /**
     * 收银台收单号
     * @return PaPayDeskFlowNo 收银台收单号
     */
    public String getPaPayDeskFlowNo() {
        return paPayDeskFlowNo;
    }

    /**
     * 收银台收单号
     * @param paPayDeskFlowNo 收银台收单号
     */
    public void setPaPayDeskFlowNo(String paPayDeskFlowNo) {
        this.paPayDeskFlowNo = paPayDeskFlowNo;
    }

    /**
     * 支付方式 01-微信；02-支付宝；03-银行卡；
     * @return PayType 支付方式 01-微信；02-支付宝；03-银行卡；
     */
    public String getPayType() {
        return payType;
    }

    /**
     * 支付方式 01-微信；02-支付宝；03-银行卡；
     * @param payType 支付方式 01-微信；02-支付宝；03-银行卡；
     */
    public void setPayType(String payType) {
        this.payType = payType;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 管理机构
     * @return OperatorCom 管理机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 管理机构
     * @param operatorCom 管理机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 入机日期
     * @return MakeDate 入机日期
     */
    public Date getMakeDate() {
        return makeDate;
    }

    /**
     * 入机日期
     * @param makeDate 入机日期
     */
    public void setMakeDate(Date makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 入机时间
     * @return MakeTime 入机时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 入机时间
     * @param makeTime 入机时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public Date getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}