package com.sinosoft.eflex.model;

import java.util.Date;

public class FcPerImpartResult {
    /**
     * 
     */
    private String orderItemNo;

    /**
     * 
     */
    private String impartVer;

    /**
     * 
     */
    private String impartCode;

    /**
     * 答题结果
     */
    private String impartResult;

    /**
     * 
     */
    private Date makeDate;

    /**
     * 
     */
    private String makeTime;

    /**
     * 
     */
    private Date modifyDate;

    /**
     * 
     */
    private String modifyTime;

    /**
     * 
     * @return OrderItemNo
     */
    public String getOrderItemNo() {
        return orderItemNo;
    }

    /**
     * 
     * @param orderItemNo
     */
    public void setOrderItemNo(String orderItemNo) {
        this.orderItemNo = orderItemNo;
    }

    /**
     * 
     * @return ImpartVer
     */
    public String getImpartVer() {
        return impartVer;
    }

    /**
     * 
     * @param impartVer
     */
    public void setImpartVer(String impartVer) {
        this.impartVer = impartVer;
    }

    /**
     * 
     * @return ImpartCode
     */
    public String getImpartCode() {
        return impartCode;
    }

    /**
     * 
     * @param impartCode
     */
    public void setImpartCode(String impartCode) {
        this.impartCode = impartCode;
    }

    /**
     * 答题结果 00 否 01 是
     * 
     * @return ImpartResult 答题结果 00 否 01 是
     */
    public String getImpartResult() {
        return impartResult;
    }

    /**
     * 答题结果 00 否 01 是
     * 
     * @param impartResult
     *            答题结果 00 否 01 是
     */
    public void setImpartResult(String impartResult) {
        this.impartResult = impartResult;
    }

    /**
     * 
     * @return MakeDate
     */
    public Date getMakeDate() {
        return makeDate;
    }

    /**
     * 
     * @param makeDate
     */
    public void setMakeDate(Date makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 
     * @return MakeTime
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 
     * @param makeTime
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 
     * @return ModifyDate
     */
    public Date getModifyDate() {
        return modifyDate;
    }

    /**
     * 
     * @param modifyDate
     */
    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 
     * @return ModifyTime
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 
     * @param modifyTime
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}