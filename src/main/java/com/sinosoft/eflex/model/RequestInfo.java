package com.sinosoft.eflex.model;

import com.sinosoft.eflex.model.BatchInsureInterface.Head;
import com.sinosoft.eflex.model.BatchInsureInterface.InsuredInfo;

/**
 * <AUTHOR>
 * @date 2018-09-11 16:59
 */
public class RequestInfo {
    //五要素信息类
    private InsuredInfo insuredInfo;

    private Head head;

    public Head getHead() {
        return head;
    }

    public void setHead(Head head) {
        this.head = head;
    }

    public InsuredInfo getInsuredInfo() {
        return insuredInfo;
    }

    public void setInsuredInfo(InsuredInfo insuredInfo) {
        this.insuredInfo = insuredInfo;
    }
}
