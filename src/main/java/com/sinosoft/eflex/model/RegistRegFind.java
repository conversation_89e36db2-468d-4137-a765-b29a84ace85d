package com.sinosoft.eflex.model;

/**
 * <AUTHOR>
 * @date 2018-08-13 17:36
 */
public class RegistRegFind {
    /*姓名*/
    private String name;
    /*手机号*/
    private String mobilePhone;
    private String nativeplace;
    private String idTypeEndDate;

	/*证件类型*/
    private String IDType;
    /*证件号*/
    private String IDNo;
    /*企业姓名*/
    private String grpName;
    /*审核状态*/
    private  String auditResult;
    /*开始日期*/
    private String startDate;
    /*结束日期*/
    private String endDate;

    /* 管理机构 */
    private String manageCom;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getIDType() {
        return IDType;
    }

    public void setIDType(String IDType) {
        this.IDType = IDType;
    }

    public String getIDNo() {
        return IDNo;
    }

    public void setIDNo(String IDNo) {
        this.IDNo = IDNo;
    }

    public String getGrpName() {
        return grpName;
    }

    public void setGrpName(String grpName) {
        this.grpName = grpName;
    }

    public String getAuditResult() {
        return auditResult;
    }

    public void setAuditResult(String auditResult) {
        this.auditResult = auditResult;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getNativeplace() {
        return nativeplace;
    }

    public void setNativeplace(String nativeplace) {
        this.nativeplace = nativeplace;
    }

    public String getIdTypeEndDate() {
        return idTypeEndDate;
    }

    public void setIdTypeEndDate(String idTypeEndDate) {
        this.idTypeEndDate = idTypeEndDate;
    }

    public String getManageCom() {
        return manageCom;
    }

    public void setManageCom(String manageCom) {
        this.manageCom = manageCom;
    }

    @Override
    public String toString() {
        return "RegistRegFind{" +
                "name='" + name + '\'' +
                ", mobilePhone='" + mobilePhone + '\'' +
                ", IDType='" + IDType + '\'' +
                ", IDNo='" + IDNo + '\'' +
                ", grpName='" + grpName + '\'' +
                ", auditResult='" + auditResult + '\'' +
                ", startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                '}';
    }
}
