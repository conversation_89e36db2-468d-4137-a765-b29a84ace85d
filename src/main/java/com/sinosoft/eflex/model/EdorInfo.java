package com.sinosoft.eflex.model;

/**
 * <AUTHOR>
 * @date 2018-12-28 15:23
 */
public class EdorInfo {

    //保全申请号
    private String  EdorNo;
    //申请项目
    private String  EdorProject;
    //申请日期
    private String ApplyDate;

    private String Applicant;
    //保全生效日
    private String EffectiveDate;
    //应补/退费
    private String PaidFee;
    //实补/退费
    private String PaymentFee;
    //备注
    private String  Remarks;
    //当前保全进度
    private String  Progress;
    //保全类型
    private String edorType;

    public String getApplicant() {
        return Applicant;
    }

    public void setApplicant(String applicant) {
        Applicant = applicant;
    }

    public String getEdorNo() {
        return EdorNo;
    }

    public void setEdorNo(String edorNo) {
        EdorNo = edorNo;
    }

    public String getEdorProject() {
        return EdorProject;
    }

    public void setEdorProject(String edorProject) {
        EdorProject = edorProject;
    }

    public String getApplyDate() {
        return ApplyDate;
    }

    public void setApplyDate(String applyDate) {
        ApplyDate = applyDate;
    }

    public String getEffectiveDate() {
        return EffectiveDate;
    }

    public void setEffectiveDate(String effectiveDate) {
        EffectiveDate = effectiveDate;
    }

    public String getPaidFee() {
        return PaidFee;
    }

    public void setPaidFee(String paidFee) {
        PaidFee = paidFee;
    }

    public String getPaymentFee() {
        return PaymentFee;
    }

    public void setPaymentFee(String paymentFee) {
        PaymentFee = paymentFee;
    }

    public String getRemarks() {
        return Remarks;
    }

    public void setRemarks(String remarks) {
        Remarks = remarks;
    }

    public String getProgress() {
        return Progress;
    }

    public void setProgress(String progress) {
        Progress = progress;
    }

    public String getEdorType() {
        return edorType;
    }

    public void setEdorType(String edorType) {
        this.edorType = edorType;
    }
}
