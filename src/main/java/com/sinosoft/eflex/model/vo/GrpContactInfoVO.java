package com.sinosoft.eflex.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(description = "企业经办人信息")
@Data
public class GrpContactInfoVO implements Serializable {


    @ApiModelProperty("联系人编号")
    private String contactNo;

    @ApiModelProperty("联系人类型")
    private String contactType;

    @ApiModelProperty("企业编号")
    private String grpNo;

    @ApiModelProperty("经办人")
    private String name;

    @ApiModelProperty("性别")
    private String sex;

    @ApiModelProperty("证件信息")
    private String idNo;

    @ApiModelProperty("手机号")
    private String mobilePhone;

    @ApiModelProperty("启用状态")
    private String lockState;
}
