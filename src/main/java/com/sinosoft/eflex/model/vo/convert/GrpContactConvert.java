package com.sinosoft.eflex.model.vo.convert;

import com.sinosoft.eflex.model.FCContactGrpRela;
import com.sinosoft.eflex.model.FcGrpContact;
import com.sinosoft.eflex.model.vo.GrpContactInfoVO;
import com.sinosoft.eflex.util.Base64AndMD5Util;

/**
 * 转换
 *
 * <AUTHOR> * *
 */
public class GrpContactConvert {

    public static GrpContactInfoVO convert(FcGrpContact fcGrpContact, FCContactGrpRela fcContactGrpRela) {
        GrpContactInfoVO grpContactInfoVO = new GrpContactInfoVO();
        grpContactInfoVO.setContactNo(fcContactGrpRela.getContactNo());
        grpContactInfoVO.setContactType(fcContactGrpRela.getContactType());
        grpContactInfoVO.setGrpNo(fcContactGrpRela.getGrpNo());
        grpContactInfoVO.setName(fcGrpContact.getName());
        grpContactInfoVO.setSex(fcGrpContact.getSex());
        grpContactInfoVO.setLockState(fcContactGrpRela.getLockState());
        grpContactInfoVO.setIdNo(Base64AndMD5Util.base64SaltEncode(fcGrpContact.getIdNo(), "130"));
        grpContactInfoVO.setMobilePhone(Base64AndMD5Util.base64SaltEncode(fcGrpContact.getMobilePhone(), "130"));
        return grpContactInfoVO;
    }
}
