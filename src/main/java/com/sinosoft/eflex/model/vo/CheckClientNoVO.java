package com.sinosoft.eflex.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(description = "业务员工号校验返回")
@Data
public class CheckClientNoVO implements Serializable {

    @ApiModelProperty("注册工号")
    private String signClientNo;

    @ApiModelProperty("注册业务员名字")
    private String signName;

    @ApiModelProperty("工号名字")
    private String clientName;


    public CheckClientNoVO(String signClientNo, String signName, String clientName) {
        this.signClientNo = signClientNo;
        this.signName = signName;
        this.clientName = clientName;
    }
}
