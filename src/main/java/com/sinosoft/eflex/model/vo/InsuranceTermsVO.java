package com.sinosoft.eflex.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 保险条款信息*
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "保险条款信息")
public class InsuranceTermsVO implements Serializable {

    @ApiModelProperty("注册工号")
    private String riskCode;

    @ApiModelProperty("注册工号")
    private String riskName;

    @ApiModelProperty("产品说明PDF url")
    private String productDescription;

    @ApiModelProperty("保险条款 url")
    private String insuranceTerms;

    @ApiModelProperty("产品说明PDF url 名称")
    private String productDescriptionName;

    @ApiModelProperty("保险条款 url 名称")
    private String insuranceTermsName;

    @ApiModelProperty("pdf url")
    private String pdfUrl;

    @ApiModelProperty("pdf url")
    private Integer protocolReading;

}
