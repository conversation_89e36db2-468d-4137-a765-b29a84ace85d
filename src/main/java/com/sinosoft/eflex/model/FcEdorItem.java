package com.sinosoft.eflex.model;

/**
 * 保全申请项目表
 */
public class FcEdorItem {
    /**
     * 保全申请批次号
     */
    private String edorBatch;

    /**
     * 团体保单号
     */
    private String grpContNo;

    /**
     * 保全受理号
     */
    private String edorAppNo;

    /**
     * 保全类型 NI 保全增人 ZT 保全减人  NZ 同质风险加减人
     */
    private String edorType;

    /**
     * 批改状态 01 申请中 02 待核保 03 待支付 04 支付失败 05 保全签发 06 核保终止 07 已撤销
     */
    private String edorState;

    /**
     * 保额
     */
    private String amount;

    /**
     * 保费
     */
    private String prem;

    /**
     * 是否是平台的单子 0否 1是
     */
    private String isEflexPolicy;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 生成日期
     */
    private String makeDate;

    /**
     * 生成时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 保全申请批次号
     * @return EdorBatch 保全申请批次号
     */
    public String getEdorBatch() {
        return edorBatch;
    }

    /**
     * 保全申请批次号
     * @param edorBatch 保全申请批次号
     */
    public void setEdorBatch(String edorBatch) {
        this.edorBatch = edorBatch;
    }

    /**
     * 团体保单号
     * @return GrpContNo 团体保单号
     */
    public String getGrpContNo() {
        return grpContNo;
    }

    /**
     * 团体保单号
     * @param grpContNo 团体保单号
     */
    public void setGrpContNo(String grpContNo) {
        this.grpContNo = grpContNo;
    }

    /**
     * 保全受理号
     * @return EdorAppNo 保全受理号
     */
    public String getEdorAppNo() {
        return edorAppNo;
    }

    /**
     * 保全受理号
     * @param edorAppNo 保全受理号
     */
    public void setEdorAppNo(String edorAppNo) {
        this.edorAppNo = edorAppNo;
    }

    /**
     * 保全类型 NI 保全增人 ZT 保全减人  NZ 同质风险加减人
     * @return EdorType 保全类型 NI 保全增人 ZT 保全减人  NZ 同质风险加减人
     */
    public String getEdorType() {
        return edorType;
    }

    /**
     * 保全类型 NI 保全增人 ZT 保全减人  NZ 同质风险加减人
     * @param edorType 保全类型 NI 保全增人 ZT 保全减人  NZ 同质风险加减人
     */
    public void setEdorType(String edorType) {
        this.edorType = edorType;
    }

    /**
     * 批改状态
01 申请中
02 待核保
03 待支付
04 支付失败
05 保全签发
06 核保终止
07 已撤销

     * @return EdorState 批改状态
01 申请中
02 待核保
03 待支付
04 支付失败
05 保全签发
06 核保终止
07 已撤销

     */
    public String getEdorState() {
        return edorState;
    }

    /**
     * 批改状态
01 申请中
02 待核保
03 待支付
04 支付失败
05 保全签发
06 核保终止
07 已撤销

     * @param edorState 批改状态
01 申请中
02 待核保
03 待支付
04 支付失败
05 保全签发
06 核保终止
07 已撤销

     */
    public void setEdorState(String edorState) {
        this.edorState = edorState;
    }

    /**
     * 保额
     * @return Amount 保额
     */
    public String getAmount() {
        return amount;
    }

    /**
     * 保额
     * @param amount 保额
     */
    public void setAmount(String amount) {
        this.amount = amount;
    }

    /**
     * 保费
     * @return Prem 保费
     */
    public String getPrem() {
        return prem;
    }

    /**
     * 保费
     * @param prem 保费
     */
    public void setPrem(String prem) {
        this.prem = prem;
    }

    /**
     * 是否是平台的单子 0否 1是
     * @return IsEflexPolicy 是否是平台的单子 0否 1是
     */
    public String getIsEflexPolicy() {
        return isEflexPolicy;
    }

    /**
     * 是否是平台的单子 0否 1是
     * @param isEflexPolicy 是否是平台的单子 0否 1是
     */
    public void setIsEflexPolicy(String isEflexPolicy) {
        this.isEflexPolicy = isEflexPolicy;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 操作机构
     * @return OperatorCom 操作机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作机构
     * @param operatorCom 操作机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 生成日期
     * @return MakeDate 生成日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 生成日期
     * @param makeDate 生成日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 生成时间
     * @return MakeTime 生成时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 生成时间
     * @param makeTime 生成时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}