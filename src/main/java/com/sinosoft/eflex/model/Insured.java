package com.sinosoft.eflex.model;
/**
 * @DESCRIPTION
 * @create 2018-12-05 15:51
 **/
public class Insured {
	private String OName;
	private String OSex;
	private String OBirthday;
	private String OIDType;
	private String OIDNo;
	private String NewName; 
	private String NewSex; 
	private String NewBirthday;
	private String NewIDType;
	private String NewIDNo;
	private String JobCode;
	private String JobName;
	private String SocialInsuFlag;

    private String MainInsuredName;
    private String MainInsuredNo;
    private String MainRelation;
    private String Name;
    private String Sex;
    private String Birthday;
    private String IDNo;
    private String IDType;
    private String Prem;
    private String PremSource;
    private String AccountType;

    private String NativePlace;

    /**
     * 国籍
     */
    private String NewNativePlace;
	
	
	
	public String getOName() {
		return OName;
	}

	public void setOName(String oName) {
		OName = oName;
	}

	public String getOSex() {
		return OSex;
	}

	public void setOSex(String oSex) {
		OSex = oSex;
	}

	public String getOBirthday() {
		return OBirthday;
	}

	public void setOBirthday(String oBirthday) {
		OBirthday = oBirthday;
	}

	public String getOIDType() {
		return OIDType;
	}

	public void setOIDType(String oIDType) {
		OIDType = oIDType;
	}

	public String getOIDNo() {
		return OIDNo;
	}

	public void setOIDNo(String oIDNo) {
		OIDNo = oIDNo;
	}

	public String getNewName() {
		return NewName;
	}

	public void setNewName(String newName) {
		NewName = newName;
	}

	public String getNewSex() {
		return NewSex;
	}

	public void setNewSex(String newSex) {
		NewSex = newSex;
	}

	public String getNewBirthday() {
		return NewBirthday;
	}

	public void setNewBirthday(String newBirthday) {
		NewBirthday = newBirthday;
	}

	public String getNewIDType() {
		return NewIDType;
	}

	public void setNewIDType(String newIDType) {
		NewIDType = newIDType;
	}

	public String getNewIDNo() {
		return NewIDNo;
	}

	public void setNewIDNo(String newIDNo) {
		NewIDNo = newIDNo;
	}

	public String getJobCode() {
		return JobCode;
	}

	public void setJobCode(String jobCode) {
		JobCode = jobCode;
	}

	public String getSocialInsuFlag() {
		return SocialInsuFlag;
	}

	public void setSocialInsuFlag(String socialInsuFlag) {
		SocialInsuFlag = socialInsuFlag;
	}

	public String getPremSource() {
		return PremSource;
	}

	public void setPremSource(String premSource) {
		PremSource = premSource;
	}

	public String getMainInsuredName() {
		return MainInsuredName;
	}

	public void setMainInsuredName(String mainInsuredName) {
		MainInsuredName = mainInsuredName;
	}

	public String getMainInsuredNo() {
		return MainInsuredNo;
	}

	public void setMainInsuredNo(String mainInsuredNo) {
		MainInsuredNo = mainInsuredNo;
	}

	public String getMainRelation() {
		return MainRelation;
	}

	public void setMainRelation(String mainRelation) {
		MainRelation = mainRelation;
	}

	public String getName() {
		return Name;
	}

	public void setName(String name) {
		Name = name;
	}

	public String getSex() {
		return Sex;
	}

	public void setSex(String sex) {
		Sex = sex;
	}

	public String getBirthday() {
		return Birthday;
	}

	public void setBirthday(String birthday) {
		Birthday = birthday;
	}

	public String getIDNo() {
		return IDNo;
	}

	public void setIDNo(String IDNo) {
		this.IDNo = IDNo;
	}

	public String getIDType() {
		return IDType;
	}

	public void setIDType(String IDType) {
		this.IDType = IDType;
	}

	public String getPrem() {
		return Prem;
	}

	public void setPrem(String prem) {
		Prem = prem;
	}

	public String getJobName() {
		return JobName;
	}

	public void setJobName(String jobName) {
		JobName = jobName;
	}

	public String getAccountType() {
		return AccountType;
	}

	public void setAccountType(String accountType) {
		AccountType = accountType;
	}

    public String getNativePlace() {
        return NativePlace;
    }

    public void setNativePlace(String nativePlace) {
        NativePlace = nativePlace;
    }

    public String getNewNativePlace() {
        return NewNativePlace;
    }

    public void setNewNativePlace(String newNativePlace) {
        NewNativePlace = newNativePlace;
    }
}
