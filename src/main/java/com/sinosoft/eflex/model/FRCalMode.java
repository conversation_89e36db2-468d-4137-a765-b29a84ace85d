package com.sinosoft.eflex.model;

public class FRCalMode {
    /**
     * 算法编码
     */
    private String calCode;

    /**
     * 险种编码
     */
    private String riskCode;

    /**
     * 算法类型，
A--计算责任给付
P--计算缴费
C--计算赔付
G--计算领取
V--校验规则                                                E--保全算法                                                     U--核保算法                                                    O，I，J，K，L，N，O--FX，FB算法
     */
    private String type;

    /**
     * 算法内容
     */
    private String calSQL;

    /**
     * 算法描述
     */
    private String remark;

    /**
     * 算法编码
     * @return CalCode 算法编码
     */
    public String getCalCode() {
        return calCode;
    }

    /**
     * 算法编码
     * @param calCode 算法编码
     */
    public void setCalCode(String calCode) {
        this.calCode = calCode;
    }

    /**
     * 险种编码
     * @return RiskCode 险种编码
     */
    public String getRiskCode() {
        return riskCode;
    }

    /**
     * 险种编码
     * @param riskCode 险种编码
     */
    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    /**
     * 算法类型，
A--计算责任给付
P--计算缴费
C--计算赔付
G--计算领取
V--校验规则                                                E--保全算法                                                     U--核保算法                                                    O，I，J，K，L，N，O--FX，FB算法
     * @return Type 算法类型，
A--计算责任给付
P--计算缴费
C--计算赔付
G--计算领取
V--校验规则                                                E--保全算法                                                     U--核保算法                                                    O，I，J，K，L，N，O--FX，FB算法
     */
    public String getType() {
        return type;
    }

    /**
     * 算法类型，
A--计算责任给付
P--计算缴费
C--计算赔付
G--计算领取
V--校验规则                                                E--保全算法                                                     U--核保算法                                                    O，I，J，K，L，N，O--FX，FB算法
     * @param type 算法类型，
A--计算责任给付
P--计算缴费
C--计算赔付
G--计算领取
V--校验规则                                                E--保全算法                                                     U--核保算法                                                    O，I，J，K，L，N，O--FX，FB算法
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * 算法内容
     * @return CalSQL 算法内容
     */
    public String getCalSQL() {
        return calSQL;
    }

    /**
     * 算法内容
     * @param calSQL 算法内容
     */
    public void setCalSQL(String calSQL) {
        this.calSQL = calSQL;
    }

    /**
     * 算法描述
     * @return Remark 算法描述
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 算法描述
     * @param remark 算法描述
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }
}