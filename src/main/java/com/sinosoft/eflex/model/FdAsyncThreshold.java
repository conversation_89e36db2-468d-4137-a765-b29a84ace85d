package com.sinosoft.eflex.model;

public class FdAsyncThreshold {
    /**
     * 业务代码
     */
    private String businessCode;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 人员阈值
     */
    private Integer peopleLimit;

    /**
     * 单个人处理时长（毫秒）
     */
    private Integer singleDealTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 存当前登录用户ID
     */
    private String operator;

    /**
     * 存当前操作用户的管理机构
     */
    private String operatorCom;

    /**
     * 创建日期
     */
    private String makeDate;

    /**
     * 创建时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 业务代码
     *
     * @return BusinessCode 业务代码
     */
    public String getBusinessCode() {
        return businessCode;
    }

    /**
     * 业务代码
     *
     * @param businessCode 业务代码
     */
    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    /**
     * 业务类型
     *
     * @return BusinessType 业务类型
     */
    public String getBusinessType() {
        return businessType;
    }

    /**
     * 业务类型
     *
     * @param businessType 业务类型
     */
    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    /**
     * 人员阈值
     *
     * @return PeopleLimit 人员阈值
     */
    public Integer getPeopleLimit() {
        return peopleLimit;
    }

    /**
     * 人员阈值
     *
     * @param peopleLimit 人员阈值
     */
    public void setPeopleLimit(Integer peopleLimit) {
        this.peopleLimit = peopleLimit;
    }

    /**
     * 单个人处理时长（毫秒）
     *
     * @return SingleDealTime 单个人处理时长（毫秒）
     */
    public Integer getSingleDealTime() {
        return singleDealTime;
    }

    /**
     * 单个人处理时长（毫秒）
     *
     * @param singleDealTime 单个人处理时长（毫秒）
     */
    public void setSingleDealTime(Integer singleDealTime) {
        this.singleDealTime = singleDealTime;
    }

    /**
     * 备注
     *
     * @return Remark 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 备注
     *
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 存当前登录用户ID
     *
     * @return Operator 存当前登录用户ID
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 存当前登录用户ID
     *
     * @param operator 存当前登录用户ID
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 存当前操作用户的管理机构
     *
     * @return OperatorCom 存当前操作用户的管理机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 存当前操作用户的管理机构
     *
     * @param operatorCom 存当前操作用户的管理机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 创建日期
     *
     * @return MakeDate 创建日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 创建日期
     *
     * @param makeDate 创建日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 创建时间
     *
     * @return MakeTime 创建时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 创建时间
     *
     * @param makeTime 创建时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     *
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     *
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     *
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     *
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}