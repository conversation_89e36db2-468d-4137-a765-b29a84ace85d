package com.sinosoft.eflex.model;

import java.math.BigDecimal;
import java.util.Date;

public class FCEdorAddPlanRiskDutyInfo {
    /**
     * 增人计划流水号
     */
    private String edorAddPlanSN;

    /**
     * 险种编码
     */
    private String riskCode;

    /**
     * 责任编码
     */
    private String dutyCode;

    /**
     * 团体保单号
     */
    private String grpContNo;

    /**
     * 责任名称
     */
    private String dutyName;

    /**
     * 责任保额
     */
    private BigDecimal dutyAmnt;

    /**
     * 责任保费
     */
    private BigDecimal dutyPrem;

    /**
     * 免赔额
     */
    private Double getLimit;

    /**
     * 赔付比例
     */
    private Double getRate;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作员机构
     */
    private String operatorCom;

    /**
     * 生成日期
     */
    private String makeDate;

    /**
     * 生成时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 增人计划流水号
     * @return EdorAddPlanSN 增人计划流水号
     */
    public String getEdorAddPlanSN() {
        return edorAddPlanSN;
    }

    /**
     * 增人计划流水号
     * @param edorAddPlanSN 增人计划流水号
     */
    public void setEdorAddPlanSN(String edorAddPlanSN) {
        this.edorAddPlanSN = edorAddPlanSN;
    }

    /**
     * 险种编码
     * @return RiskCode 险种编码
     */
    public String getRiskCode() {
        return riskCode;
    }

    /**
     * 险种编码
     * @param riskCode 险种编码
     */
    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    /**
     * 责任编码
     * @return DutyCode 责任编码
     */
    public String getDutyCode() {
        return dutyCode;
    }

    /**
     * 责任编码
     * @param dutyCode 责任编码
     */
    public void setDutyCode(String dutyCode) {
        this.dutyCode = dutyCode;
    }

    /**
     * 责任名称
     * @return DutyName 责任名称
     */
    public String getDutyName() {
        return dutyName;
    }

    public String getGrpContNo() {
        return grpContNo;
    }

    public void setGrpContNo(String grpContNo) {
        this.grpContNo = grpContNo;
    }

    /**
     * 责任名称
     * @param dutyName 责任名称
     */
    public void setDutyName(String dutyName) {
        this.dutyName = dutyName;
    }

    /**
     * 责任保额
     * @return DutyAmnt 责任保额
     */
    public BigDecimal getDutyAmnt() {
        return dutyAmnt;
    }

    /**
     * 责任保额
     * @param dutyAmnt 责任保额
     */
    public void setDutyAmnt(BigDecimal dutyAmnt) {
        this.dutyAmnt = dutyAmnt;
    }

    /**
     * 责任保费
     * @return DutyPrem 责任保费
     */
    public BigDecimal getDutyPrem() {
        return dutyPrem;
    }

    /**
     * 责任保费
     * @param dutyPrem 责任保费
     */
    public void setDutyPrem(BigDecimal dutyPrem) {
        this.dutyPrem = dutyPrem;
    }

    /**
     * 免赔额
     * @return GetLimit 免赔额
     */
    public Double getGetLimit() {
        return getLimit;
    }

    /**
     * 免赔额
     * @param getLimit 免赔额
     */
    public void setGetLimit(Double getLimit) {
        this.getLimit = getLimit;
    }

    /**
     * 赔付比例
     * @return GetRate 赔付比例
     */
    public Double getGetRate() {
        return getRate;
    }

    /**
     * 赔付比例
     * @param getRate 赔付比例
     */
    public void setGetRate(Double getRate) {
        this.getRate = getRate;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 操作员机构
     * @return OperatorCom 操作员机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作员机构
     * @param operatorCom 操作员机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 生成日期
     * @return MakeDate 生成日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 生成日期
     * @param makeDate 生成日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 生成时间
     * @return MakeTime 生成时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 生成时间
     * @param makeTime 生成时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}