package com.sinosoft.eflex.model;

import java.util.List;

/**
 * 团体订单表
 */
public class FCGrpOrder {
	/**
	 * 团体订单号
	 */
	private String grpOrderNo;

	/**
	 * 团体订单类型
	 */
	private String grpOrderType;

	/**
	 * 福利编号
	 */
	private String ensureCode;

	/**
	 * 企业客户号
	 */
	private String grpNo;

	/**
	 * 团体投保人编号
	 */
	private String grpAppNo;

	/**
	 * 团体订单状态
	 */
	private String grpOrderStatus;

	/**
	 * 订单提交日期
	 */
	private String commitDate;

	/**
	 * 订单生效日期
	 */
	private String effectDate;

	/**
	 * 投保单号
	 */
	private String prtNo;

	/**
	 * 团体保单号
	 */
	private String grpContNo;

	/**
	 * 消费明细号
	 */
	private String costNo;

	/**
	 * 操作员
	 */
	private String operator;

	/**
	 * 操作机构
	 */
	private String operatorCom;

	/**
	 * 生成日期
	 */
	private String makeDate;

	/**
	 * 生成时间
	 */
	private String makeTime;

	/**
	 * 修改日期
	 */
	private String modifyDate;

	/**
	 * 修改时间
	 */
	private String modifyTime;

	/**
	 * 团体投保人表
	 */
	private FCGrpApplicant fcGrpApplicant;

	/**
	 * 订单表
	 */
	private List<FCOrder> fcOrders;

	/**
	 * 团体订单号
	 * 
	 * @return GrpOrderNo 团体订单号
	 */
	public String getGrpOrderNo() {
		return grpOrderNo;
	}

	/**
	 * 团体订单号
	 * 
	 * @param grpOrderNo
	 *            团体订单号
	 */
	public void setGrpOrderNo(String grpOrderNo) {
		this.grpOrderNo = grpOrderNo;
	}

	/**
	 * 团体订单类型
	 * 
	 * @return GrpOrderType 团体订单类型
	 */
	public String getGrpOrderType() {
		return grpOrderType;
	}

	/**
	 * 团体订单类型
	 * 
	 * @param grpOrderType
	 *            团体订单类型
	 */
	public void setGrpOrderType(String grpOrderType) {
		this.grpOrderType = grpOrderType;
	}

	/**
	 * 福利编号
	 * 
	 * @return EnsureCode 福利编号
	 */
	public String getEnsureCode() {
		return ensureCode;
	}

	/**
	 * 福利编号
	 * 
	 * @param ensureCode
	 *            福利编号
	 */
	public void setEnsureCode(String ensureCode) {
		this.ensureCode = ensureCode;
	}

	/**
	 * 企业客户号
	 * 
	 * @return GrpNo 企业客户号
	 */
	public String getGrpNo() {
		return grpNo;
	}

	/**
	 * 企业客户号
	 * 
	 * @param grpNo
	 *            企业客户号
	 */
	public void setGrpNo(String grpNo) {
		this.grpNo = grpNo;
	}

	/**
	 * 团体投保人编号
	 * 
	 * @return GrpAppNo 团体投保人编号
	 */
	public String getGrpAppNo() {
		return grpAppNo;
	}

	/**
	 * 团体投保人编号
	 * 
	 * @param grpAppNo
	 *            团体投保人编号
	 */
	public void setGrpAppNo(String grpAppNo) {
		this.grpAppNo = grpAppNo;
	}

	/**
	 * 团体订单状态
	 * 
	 * @return GrpOrderStatus 团体订单状态
	 */
	public String getGrpOrderStatus() {
		return grpOrderStatus;
	}

	/**
	 * 团体订单状态
	 * 
	 * @param grpOrderStatus
	 *            团体订单状态
	 */
	public void setGrpOrderStatus(String grpOrderStatus) {
		this.grpOrderStatus = grpOrderStatus;
	}

	/**
	 * 订单提交日期
	 * 
	 * @return CommitDate 订单提交日期
	 */
	public String getCommitDate() {
		return commitDate;
	}

	/**
	 * 订单提交日期
	 * 
	 * @param commitDate
	 *            订单提交日期
	 */
	public void setCommitDate(String commitDate) {
		this.commitDate = commitDate;
	}

	/**
	 * 订单生效日期
	 * 
	 * @return EffectDate 订单生效日期
	 */
	public String getEffectDate() {
		return effectDate;
	}

	/**
	 * 订单生效日期
	 * 
	 * @param effectDate
	 *            订单生效日期
	 */
	public void setEffectDate(String effectDate) {
		this.effectDate = effectDate;
	}

	/**
	 * 投保单号
	 * 
	 * @return PrtNo 投保单号
	 */
	public String getPrtNo() {
		return prtNo;
	}

	/**
	 * 投保单号
	 * 
	 * @param prtNo
	 *            投保单号
	 */
	public void setPrtNo(String prtNo) {
		this.prtNo = prtNo;
	}

	/**
	 * 团体保单号
	 * 
	 * @return grpContNo 团体保单号
	 */
	public String getGrpContNo() {
		return grpContNo;
	}

	/**
	 * 团体保单号
	 * 
	 * @param grpContNo
	 *            团体保单号
	 */
	public void setGrpContNo(String grpContNo) {
		this.grpContNo = grpContNo;
	}

	/**
	 * 消费明细号
	 * 
	 * @return CostNo 消费明细号
	 */
	public String getCostNo() {
		return costNo;
	}

	/**
	 * 消费明细号
	 * 
	 * @param costNo
	 *            消费明细号
	 */
	public void setCostNo(String costNo) {
		this.costNo = costNo;
	}

	/**
	 * 操作员
	 * 
	 * @return Operator 操作员
	 */
	public String getOperator() {
		return operator;
	}

	/**
	 * 操作员
	 * 
	 * @param operator
	 *            操作员
	 */
	public void setOperator(String operator) {
		this.operator = operator;
	}

	/**
	 * 操作机构
	 * 
	 * @return OperatorCom 操作机构
	 */
	public String getOperatorCom() {
		return operatorCom;
	}

	/**
	 * 操作机构
	 * 
	 * @param operatorCom
	 *            操作机构
	 */
	public void setOperatorCom(String operatorCom) {
		this.operatorCom = operatorCom;
	}

	/**
	 * 生成日期
	 * 
	 * @return MakeDate 生成日期
	 */
	public String getMakeDate() {
		return makeDate;
	}

	/**
	 * 生成日期
	 * 
	 * @param makeDate
	 *            生成日期
	 */
	public void setMakeDate(String makeDate) {
		this.makeDate = makeDate;
	}

	/**
	 * 生成时间
	 * 
	 * @return MakeTime 生成时间
	 */
	public String getMakeTime() {
		return makeTime;
	}

	/**
	 * 生成时间
	 * 
	 * @param makeTime
	 *            生成时间
	 */
	public void setMakeTime(String makeTime) {
		this.makeTime = makeTime;
	}

	/**
	 * 修改日期
	 * 
	 * @return ModifyDate 修改日期
	 */
	public String getModifyDate() {
		return modifyDate;
	}

	/**
	 * 修改日期
	 * 
	 * @param modifyDate
	 *            修改日期
	 */
	public void setModifyDate(String modifyDate) {
		this.modifyDate = modifyDate;
	}

	/**
	 * 修改时间
	 * 
	 * @return ModifyTime 修改时间
	 */
	public String getModifyTime() {
		return modifyTime;
	}

	/**
	 * 修改时间
	 * 
	 * @param modifyTime
	 *            修改时间
	 */
	public void setModifyTime(String modifyTime) {
		this.modifyTime = modifyTime;
	}

	/**
	 * @return the fcGrpApplicant
	 */
	public FCGrpApplicant getFcGrpApplicant() {
		return fcGrpApplicant;
	}

	/**
	 * @param fcGrpApplicant
	 *            the fcGrpApplicant to set
	 */
	public void setFcGrpApplicant(FCGrpApplicant fcGrpApplicant) {
		this.fcGrpApplicant = fcGrpApplicant;
	}

	/**
	 * @return the fcOrders
	 */
	public List<FCOrder> getFcOrders() {
		return fcOrders;
	}

	/**
	 * @param fcOrders
	 *            the fcOrders to set
	 */
	public void setFcOrders(List<FCOrder> fcOrders) {
		this.fcOrders = fcOrders;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see java.lang.Object#hashCode()
	 */
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((commitDate == null) ? 0 : commitDate.hashCode());
		result = prime * result + ((costNo == null) ? 0 : costNo.hashCode());
		result = prime * result + ((effectDate == null) ? 0 : effectDate.hashCode());
		result = prime * result + ((ensureCode == null) ? 0 : ensureCode.hashCode());
		result = prime * result + ((fcGrpApplicant == null) ? 0 : fcGrpApplicant.hashCode());
		result = prime * result + ((fcOrders == null) ? 0 : fcOrders.hashCode());
		result = prime * result + ((grpAppNo == null) ? 0 : grpAppNo.hashCode());
		result = prime * result + ((grpContNo == null) ? 0 : grpContNo.hashCode());
		result = prime * result + ((grpNo == null) ? 0 : grpNo.hashCode());
		result = prime * result + ((grpOrderNo == null) ? 0 : grpOrderNo.hashCode());
		result = prime * result + ((grpOrderStatus == null) ? 0 : grpOrderStatus.hashCode());
		result = prime * result + ((grpOrderType == null) ? 0 : grpOrderType.hashCode());
		result = prime * result + ((makeDate == null) ? 0 : makeDate.hashCode());
		result = prime * result + ((makeTime == null) ? 0 : makeTime.hashCode());
		result = prime * result + ((modifyDate == null) ? 0 : modifyDate.hashCode());
		result = prime * result + ((modifyTime == null) ? 0 : modifyTime.hashCode());
		result = prime * result + ((operator == null) ? 0 : operator.hashCode());
		result = prime * result + ((operatorCom == null) ? 0 : operatorCom.hashCode());
		result = prime * result + ((prtNo == null) ? 0 : prtNo.hashCode());
		return result;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see java.lang.Object#equals(java.lang.Object)
	 */
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		FCGrpOrder other = (FCGrpOrder) obj;
		if (commitDate == null) {
			if (other.commitDate != null)
				return false;
		} else if (!commitDate.equals(other.commitDate))
			return false;
		if (costNo == null) {
			if (other.costNo != null)
				return false;
		} else if (!costNo.equals(other.costNo))
			return false;
		if (effectDate == null) {
			if (other.effectDate != null)
				return false;
		} else if (!effectDate.equals(other.effectDate))
			return false;
		if (ensureCode == null) {
			if (other.ensureCode != null)
				return false;
		} else if (!ensureCode.equals(other.ensureCode))
			return false;
		if (fcGrpApplicant == null) {
			if (other.fcGrpApplicant != null)
				return false;
		} else if (!fcGrpApplicant.equals(other.fcGrpApplicant))
			return false;
		if (fcOrders == null) {
			if (other.fcOrders != null)
				return false;
		} else if (!fcOrders.equals(other.fcOrders))
			return false;
		if (grpAppNo == null) {
			if (other.grpAppNo != null)
				return false;
		} else if (!grpAppNo.equals(other.grpAppNo))
			return false;
		if (grpContNo == null) {
			if (other.grpContNo != null)
				return false;
		} else if (!grpContNo.equals(other.grpContNo))
			return false;
		if (grpNo == null) {
			if (other.grpNo != null)
				return false;
		} else if (!grpNo.equals(other.grpNo))
			return false;
		if (grpOrderNo == null) {
			if (other.grpOrderNo != null)
				return false;
		} else if (!grpOrderNo.equals(other.grpOrderNo))
			return false;
		if (grpOrderStatus == null) {
			if (other.grpOrderStatus != null)
				return false;
		} else if (!grpOrderStatus.equals(other.grpOrderStatus))
			return false;
		if (grpOrderType == null) {
			if (other.grpOrderType != null)
				return false;
		} else if (!grpOrderType.equals(other.grpOrderType))
			return false;
		if (makeDate == null) {
			if (other.makeDate != null)
				return false;
		} else if (!makeDate.equals(other.makeDate))
			return false;
		if (makeTime == null) {
			if (other.makeTime != null)
				return false;
		} else if (!makeTime.equals(other.makeTime))
			return false;
		if (modifyDate == null) {
			if (other.modifyDate != null)
				return false;
		} else if (!modifyDate.equals(other.modifyDate))
			return false;
		if (modifyTime == null) {
			if (other.modifyTime != null)
				return false;
		} else if (!modifyTime.equals(other.modifyTime))
			return false;
		if (operator == null) {
			if (other.operator != null)
				return false;
		} else if (!operator.equals(other.operator))
			return false;
		if (operatorCom == null) {
			if (other.operatorCom != null)
				return false;
		} else if (!operatorCom.equals(other.operatorCom))
			return false;
		if (prtNo == null) {
			if (other.prtNo != null)
				return false;
		} else if (!prtNo.equals(other.prtNo))
			return false;
		return true;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return "FCGrpOrder [grpOrderNo=" + grpOrderNo + ", grpOrderType=" + grpOrderType + ", ensureCode=" + ensureCode
				+ ", grpNo=" + grpNo + ", grpAppNo=" + grpAppNo + ", grpOrderStatus=" + grpOrderStatus + ", commitDate="
				+ commitDate + ", effectDate=" + effectDate + ", prtNo=" + prtNo + ", grpContNo=" + grpContNo
				+ ", costNo=" + costNo + ", operator=" + operator + ", operatorCom=" + operatorCom + ", makeDate="
				+ makeDate + ", makeTime=" + makeTime + ", modifyDate=" + modifyDate + ", modifyTime=" + modifyTime
				+ ", fcGrpApplicant=" + fcGrpApplicant + ", fcOrders=" + fcOrders + "]";
	}

}