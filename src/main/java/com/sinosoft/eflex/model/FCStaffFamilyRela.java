package com.sinosoft.eflex.model;

import java.util.List;

public class FCStaffFamilyRela{

    /**
     * 客户编码
     */
    private String perNo;

    /**
     * 个人编码
     */
    private String personID;

    /**
     * 1 本人
            2配偶
            3子女
            4父母
            5其它
     */
    private String relation;

    /**
     * 如果是本人，则此字段存本人的客户号，其他则存对应自己的客户号，将家庭其他成员做成客户，同步程序时需要插入数据
     */
    private String relationProve;

    /**
     * 
     */
    private String operator;

    /**
     * 
     */
    private String operatorCom;

    /**
     * 
     */
    private String makeDate;

    /**
     * 
     */
    private String makeTime;

    /**
     * 
     */
    private String modifyDate;

    /**
     * 
     */
    private String modifyTime;

    private List<FCPerson> fcPersonList;

    private FCOrderInsured fcOrderInsured;

    public FCOrderInsured getFcOrderInsured() {
        return fcOrderInsured;
    }

    public void setFcOrderInsured(FCOrderInsured fcOrderInsured) {
        this.fcOrderInsured = fcOrderInsured;
    }

    public List<FCPerson> getFcPersonList() {
        return fcPersonList;
    }

    public void setFcPersonList(List<FCPerson> fcPersonList) {
        this.fcPersonList = fcPersonList;
    }

    /**
     * 客户编码
     * @return PerNo 客户编码
     */
    public String getPerNo() {
        return perNo;
    }

    /**
     * 客户编码
     * @param perNo 客户编码
     */
    public void setPerNo(String perNo) {
        this.perNo = perNo;
    }

    /**
     * 个人编码
     * @return PersonID 个人编码
     */
    public String getPersonID() {
        return personID;
    }

    /**
     * 个人编码
     * @param personID 个人编码
     */
    public void setPersonID(String personID) {
        this.personID = personID;
    }

    /**
     * 1 本人
            2配偶
            3子女
            4父母
            5其它
     * @return Relation 1 本人
            2配偶
            3子女
            4父母
            5其它
     */
    public String getRelation() {
        return relation;
    }

    /**
     * 1 本人
            2配偶
            3子女
            4父母
            5其它
     * @param relation 1 本人
            2配偶
            3子女
            4父母
            5其它
     */
    public void setRelation(String relation) {
        this.relation = relation;
    }

    /**
     * 如果是本人，则此字段存本人的客户号，其他则存对应自己的客户号，将家庭其他成员做成客户，同步程序时需要插入数据
     * @return RelationProve 如果是本人，则此字段存本人的客户号，其他则存对应自己的客户号，将家庭其他成员做成客户，同步程序时需要插入数据
     */
    public String getRelationProve() {
        return relationProve;
    }

    /**
     * 如果是本人，则此字段存本人的客户号，其他则存对应自己的客户号，将家庭其他成员做成客户，同步程序时需要插入数据
     * @param relationProve 如果是本人，则此字段存本人的客户号，其他则存对应自己的客户号，将家庭其他成员做成客户，同步程序时需要插入数据
     */
    public void setRelationProve(String relationProve) {
        this.relationProve = relationProve;
    }

    /**
     * 
     * @return Operator 
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 
     * @param operator 
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 
     * @return OperatorCom 
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 
     * @param operatorCom 
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 
     * @return MakeDate 
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 
     * @param makeDate 
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 
     * @return MakeTime 
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 
     * @param makeTime 
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 
     * @return ModifyDate 
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 
     * @param modifyDate 
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 
     * @return ModifyTime 
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 
     * @param modifyTime 
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}