package com.sinosoft.eflex.model.OrgAnization;

import com.hqins.common.base.enums.AgentOrgType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrgVO {

    @ApiModelProperty("组织机构类型：PARTNER-合伙人；CHANNEL-渠道商")
    private AgentOrgType orgType;

    @ApiModelProperty("机构代码")
    private String code;

    @ApiModelProperty("机构名称")
    private String name;

    @ApiModelProperty("上级机构代码")
    private String parentCode;
}