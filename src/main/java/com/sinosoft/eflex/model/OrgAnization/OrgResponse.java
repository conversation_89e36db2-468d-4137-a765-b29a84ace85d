package com.sinosoft.eflex.model.OrgAnization;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OrgResponse implements Serializable {
    /**
     * 响应码
     */
    private String code;

    /**
     * data
     */
    private List<OrgVO> data;

    private String message;
    private String timestamp;

}
