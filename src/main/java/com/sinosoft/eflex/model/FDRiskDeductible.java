package com.sinosoft.eflex.model;

public class FDRiskDeductible {

    private String isCheck;

    /**
     * 险种编码
     */
    private String riskCode;

    /**
     * 免赔额
     */
    private Double deductible;

    /**
     * 
     */
    private Double adjustFactor;

    /**
     * 险种编码
     * @return RiskCode 险种编码
     */
    public String getRiskCode() {
        return riskCode;
    }

    /**
     * 险种编码
     * @param riskCode 险种编码
     */
    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    /**
     * 免赔额
     * @return Deductible 免赔额
     */
    public Double getDeductible() {
        return deductible;
    }

    /**
     * 免赔额
     * @param deductible 免赔额
     */
    public void setDeductible(Double deductible) {
        this.deductible = deductible;
    }

    /**
     * 
     * @return adjustFactor 
     */
    public Double getAdjustFactor() {
        return adjustFactor;
    }

    /**
     * 
     * @param adjustFactor 
     */
    public void setAdjustFactor(Double adjustFactor) {
        this.adjustFactor = adjustFactor;
    }

    public String getIsCheck() {
        return isCheck;
    }

    public void setIsCheck(String isCheck) {
        this.isCheck = isCheck;
    }
}