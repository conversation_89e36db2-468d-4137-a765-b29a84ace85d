package com.sinosoft.eflex.model.core;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 核心返回实体*
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CoreResponseDTO {
    /**
     * 返回标志	0-成功，1-失败 *
     */
    private String flag;
    /**
     * 返回信息
     */
    private String desc;
    /**
     * 交易来源	固定值：782092647FAF7E0E9F8F6BC679AE7B09
     */
    private String source;
    /**
     * 交易子来源	固定值：05
     */
    private String subSource;

    /**
     * 交易子来源	固定值：05
     */
    private CoreDailyPlanResDTO body;


}
