package com.sinosoft.eflex.model.core;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 核心返回实体*
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CoreDailyPlanResDTO {
    /**
     * 自核通过标志*
     */
    private String autoUWFlag;
    /**
     * 自核结论描述
     */
    private String remark;
    /**
     * 团体投保单号
     */
    private String grpPrtNo;
    /**
     * 交易子来源	固定值：05
     */
    private String grpContNo;

    /**
     * 交易子来源	固定值：05
     */
    private List autoUWInfoList;

    private String contNo;

    private String prtNo;

    private String polApplyDate;

    private String signDate;

    private String cvaliDate;

    private String contInfo;


}
