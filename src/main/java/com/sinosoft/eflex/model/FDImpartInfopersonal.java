package com.sinosoft.eflex.model;

public class FDImpartInfopersonal {
    /**
     * 告知编码
     */
    private String impartCode;

    /**
     * 告知版别
     */
    private String impartVer;

    /**
     * 告知内容
     */
    private String impartContent;

    /**
     * 详细描述
     */
    private String impartParamModle;

    /**
     * 
     */
    private String disimpartParam;

    /**
     * 告知编码
     * @return ImpartCode 告知编码
     */
    public String getImpartCode() {
        return impartCode;
    }

    /**
     * 告知编码
     * @param impartCode 告知编码
     */
    public void setImpartCode(String impartCode) {
        this.impartCode = impartCode;
    }

    /**
     * 告知版别
     * @return ImpartVer 告知版别
     */
    public String getImpartVer() {
        return impartVer;
    }

    /**
     * 告知版别
     * @param impartVer 告知版别
     */
    public void setImpartVer(String impartVer) {
        this.impartVer = impartVer;
    }

    /**
     * 告知内容
     * @return ImpartContent 告知内容
     */
    public String getImpartContent() {
        return impartContent;
    }

    /**
     * 告知内容
     * @param impartContent 告知内容
     */
    public void setImpartContent(String impartContent) {
        this.impartContent = impartContent;
    }

    /**
     * 详细描述
     * @return ImpartParamModle 详细描述
     */
    public String getImpartParamModle() {
        return impartParamModle;
    }

    /**
     * 详细描述
     * @param impartParamModle 详细描述
     */
    public void setImpartParamModle(String impartParamModle) {
        this.impartParamModle = impartParamModle;
    }

    /**
     * 
     * @return DisimpartParam 
     */
    public String getDisimpartParam() {
        return disimpartParam;
    }

    /**
     * 
     * @param disimpartParam 
     */
    public void setDisimpartParam(String disimpartParam) {
        this.disimpartParam = disimpartParam;
    }
}