package com.sinosoft.eflex.model;

import lombok.Data;

@Data
public class FCOrderBnf {
    /**
     * 受益人编号
     */
    private String bnfNo;

    /**
     * 与被保人关系
     */
    private String relation;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private String sex;

    /**
     * 出生日期
     */
    private String birthday;

    /**
     * 证件类型
     */
    private String IDType;

    /**
     * 证件号
     */
    private String IDNo;

    /**
     * 证件有效期
     */
    private String idTypeEndDate;

    /**
     * 电话
     */
    private String mobilePhone;

    /**
     * 联系地址
     */
    private String address;
    private String province;
    private String city;
    private String county;
    private String detaileAddress;
    private String nativePlace;
    private String zipCode;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 生成日期
     */
    private String makeDate;

    /**
     * 生成时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 受益人编号
     * @return BnfNo 受益人编号
     */
    public String getBnfNo() {
        return bnfNo;
    }

    /**
     * 受益人编号
     * @param bnfNo 受益人编号
     */
    public void setBnfNo(String bnfNo) {
        this.bnfNo = bnfNo;
    }

    /**
     * 与被保人关系
     * @return Relation 与被保人关系
     */
    public String getRelation() {
        return relation;
    }

    /**
     * 与被保人关系
     * @param relation 与被保人关系
     */
    public void setRelation(String relation) {
        this.relation = relation;
    }

    /**
     * 姓名
     * @return Name 姓名
     */
    public String getName() {
        return name;
    }

    /**
     * 姓名
     * @param name 姓名
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 性别
     * @return Sex 性别
     */
    public String getSex() {
        return sex;
    }

    /**
     * 性别
     * @param sex 性别
     */
    public void setSex(String sex) {
        this.sex = sex;
    }

    /**
     * 出生日期
     * @return Birthday 出生日期
     */
    public String getBirthday() {
        return birthday;
    }

    /**
     * 出生日期
     * @param birthday 出生日期
     */
    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    /**
     * 证件类型
     * @return IDType 证件类型
     */
    public String getIDType() {
        return IDType;
    }

    /**
     * 证件类型
     * @param IDType 证件类型
     */
    public void setIDType(String IDType) {
        this.IDType = IDType;
    }

    /**
     * 证件号
     * @return IDNo 证件号
     */
    public String getIDNo() {
        return IDNo;
    }

    /**
     * 证件号
     * @param IDNo 证件号
     */
    public void setIDNo(String IDNo) {
        this.IDNo = IDNo;
    }

    /**
     * 证件有效期
     * @return idTypeEndDate 证件有效期
     */
    public String getIdTypeEndDate() {
        return idTypeEndDate;
    }

    /**
     * 证件有效期
     * @param idTypeEndDate 证件有效期
     */
    public void setIdTypeEndDate(String idTypeEndDate) {
        this.idTypeEndDate = idTypeEndDate;
    }

    /**
     * 电话
     * @return MobilePhone 电话
     */
    public String getMobilePhone() {
        return mobilePhone;
    }

    /**
     * 电话
     * @param mobilePhone 电话
     */
    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    /**
     * 联系地址
     * @return Address 联系地址
     */
    public String getAddress() {
        return address;
    }

    /**
     * 联系地址
     * @param address 联系地址
     */
    public void setAddress(String address) {
        this.address = address;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 操作机构
     * @return OperatorCom 操作机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作机构
     * @param operatorCom 操作机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 生成日期
     * @return MakeDate 生成日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 生成日期
     * @param makeDate 生成日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 生成时间
     * @return MakeTime 生成时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 生成时间
     * @param makeTime 生成时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	public String getDetaileAddress() {
		return detaileAddress;
	}

	public void setDetaileAddress(String detaileAddress) {
		this.detaileAddress = detaileAddress;
	}

    public String getNativePlace() {
        return nativePlace;
    }

    public void setNativePlace(String nativePlace) {
        this.nativePlace = nativePlace;
    }

	public String getZipCode() {
		return zipCode;
	}

	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}
}