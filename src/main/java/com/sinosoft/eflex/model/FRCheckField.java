package com.sinosoft.eflex.model;

public class FRCheckField extends FRCheckFieldKey {
    /**
     * 险种版本
     */
    private String riskVer;

    /**
     * 控制类型，
TBINSERT--承保录入控制  
TBUPDATE--承保修改控制             
CALCLAIM--理赔计算控制 
ZT--保全退保控制
BQ--保全项目公用控制 
     */
    private String fieldName;

    /**
     * 算法
     */
    private String calCode;

    /**
     * 备用
     */
    private String pageLocation;

    /**
     * 备用
     */
    private String location;

    /**
     * 提示信息
     */
    private String msg;

    /**
     * 提示标记，
Y--是,表示需要将提示信息显示出来
N--否,表示不显示提示信息
     */
    private String msgFlag;

    /**
     * 备用，
Y--是,表示需要将屏幕上的相关信息进行修改
N--否
     */
    private String updFlag;

    /**
     * 有效结果标记，
描述哪些结果对于该控制是有效的,如果有多个有效值,则通过 分号;将多个有效值分开
     */
    private String valiFlag;

    /**
     * 返回值有效标记，
Y--是,表示执行成功,并且需要进一步处理
N--否,表示不成功,不进行其他任何处理
     */
    private String returnValiFlag;

    /**
     * 险种版本
     * @return RiskVer 险种版本
     */
    public String getRiskVer() {
        return riskVer;
    }

    /**
     * 险种版本
     * @param riskVer 险种版本
     */
    public void setRiskVer(String riskVer) {
        this.riskVer = riskVer;
    }

    /**
     * 控制类型，
TBINSERT--承保录入控制  
TBUPDATE--承保修改控制             
CALCLAIM--理赔计算控制 
ZT--保全退保控制
BQ--保全项目公用控制 
     * @return FieldName 控制类型，
TBINSERT--承保录入控制  
TBUPDATE--承保修改控制             
CALCLAIM--理赔计算控制 
ZT--保全退保控制
BQ--保全项目公用控制 
     */
    public String getFieldName() {
        return fieldName;
    }

    /**
     * 控制类型，
TBINSERT--承保录入控制  
TBUPDATE--承保修改控制             
CALCLAIM--理赔计算控制 
ZT--保全退保控制
BQ--保全项目公用控制 
     * @param fieldName 控制类型，
TBINSERT--承保录入控制  
TBUPDATE--承保修改控制             
CALCLAIM--理赔计算控制 
ZT--保全退保控制
BQ--保全项目公用控制 
     */
    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    /**
     * 算法
     * @return CalCode 算法
     */
    public String getCalCode() {
        return calCode;
    }

    /**
     * 算法
     * @param calCode 算法
     */
    public void setCalCode(String calCode) {
        this.calCode = calCode;
    }

    /**
     * 备用
     * @return PageLocation 备用
     */
    public String getPageLocation() {
        return pageLocation;
    }

    /**
     * 备用
     * @param pageLocation 备用
     */
    public void setPageLocation(String pageLocation) {
        this.pageLocation = pageLocation;
    }

    /**
     * 备用
     * @return Location 备用
     */
    public String getLocation() {
        return location;
    }

    /**
     * 备用
     * @param location 备用
     */
    public void setLocation(String location) {
        this.location = location;
    }

    /**
     * 提示信息
     * @return Msg 提示信息
     */
    public String getMsg() {
        return msg;
    }

    /**
     * 提示信息
     * @param msg 提示信息
     */
    public void setMsg(String msg) {
        this.msg = msg;
    }

    /**
     * 提示标记，
Y--是,表示需要将提示信息显示出来
N--否,表示不显示提示信息
     * @return MsgFlag 提示标记，
Y--是,表示需要将提示信息显示出来
N--否,表示不显示提示信息
     */
    public String getMsgFlag() {
        return msgFlag;
    }

    /**
     * 提示标记，
Y--是,表示需要将提示信息显示出来
N--否,表示不显示提示信息
     * @param msgFlag 提示标记，
Y--是,表示需要将提示信息显示出来
N--否,表示不显示提示信息
     */
    public void setMsgFlag(String msgFlag) {
        this.msgFlag = msgFlag;
    }

    /**
     * 备用，
Y--是,表示需要将屏幕上的相关信息进行修改
N--否
     * @return UpdFlag 备用，
Y--是,表示需要将屏幕上的相关信息进行修改
N--否
     */
    public String getUpdFlag() {
        return updFlag;
    }

    /**
     * 备用，
Y--是,表示需要将屏幕上的相关信息进行修改
N--否
     * @param updFlag 备用，
Y--是,表示需要将屏幕上的相关信息进行修改
N--否
     */
    public void setUpdFlag(String updFlag) {
        this.updFlag = updFlag;
    }

    /**
     * 有效结果标记，
描述哪些结果对于该控制是有效的,如果有多个有效值,则通过 分号;将多个有效值分开
     * @return ValiFlag 有效结果标记，
描述哪些结果对于该控制是有效的,如果有多个有效值,则通过 分号;将多个有效值分开
     */
    public String getValiFlag() {
        return valiFlag;
    }

    /**
     * 有效结果标记，
描述哪些结果对于该控制是有效的,如果有多个有效值,则通过 分号;将多个有效值分开
     * @param valiFlag 有效结果标记，
描述哪些结果对于该控制是有效的,如果有多个有效值,则通过 分号;将多个有效值分开
     */
    public void setValiFlag(String valiFlag) {
        this.valiFlag = valiFlag;
    }

    /**
     * 返回值有效标记，
Y--是,表示执行成功,并且需要进一步处理
N--否,表示不成功,不进行其他任何处理
     * @return ReturnValiFlag 返回值有效标记，
Y--是,表示执行成功,并且需要进一步处理
N--否,表示不成功,不进行其他任何处理
     */
    public String getReturnValiFlag() {
        return returnValiFlag;
    }

    /**
     * 返回值有效标记，
Y--是,表示执行成功,并且需要进一步处理
N--否,表示不成功,不进行其他任何处理
     * @param returnValiFlag 返回值有效标记，
Y--是,表示执行成功,并且需要进一步处理
N--否,表示不成功,不进行其他任何处理
     */
    public void setReturnValiFlag(String returnValiFlag) {
        this.returnValiFlag = returnValiFlag;
    }
}