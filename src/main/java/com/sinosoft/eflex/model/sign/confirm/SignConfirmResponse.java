package com.sinosoft.eflex.model.sign.confirm;

public class SignConfirmResponse {
	private boolean success;//成功失败标识
	private String code;//状态码
	private String message;//信息描述
	private SignConfirmReturnData data;//批量标志
	
	public boolean isSuccess() {
		return success;
	}
	public void setSuccess(boolean success) {
		this.success = success;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public SignConfirmReturnData getData() {
		return data;
	}
	public void setData(SignConfirmReturnData data) {
		this.data = data;
	}
	
}
