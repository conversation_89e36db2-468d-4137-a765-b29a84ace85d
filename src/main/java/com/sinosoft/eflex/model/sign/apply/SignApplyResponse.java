package com.sinosoft.eflex.model.sign.apply;

public class SignApplyResponse {
	private boolean success;//成功失败标识
	private String code;//状态码
	private String message;//信息描述
	private SignApplyReturnData data;//批量标志
	
	public boolean isSuccess() {
		return success;
	}
	public void setSuccess(boolean success) {
		this.success = success;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public SignApplyReturnData getData() {
		return data;
	}
	public void setData(SignApplyReturnData data) {
		this.data = data;
	}
	
}
