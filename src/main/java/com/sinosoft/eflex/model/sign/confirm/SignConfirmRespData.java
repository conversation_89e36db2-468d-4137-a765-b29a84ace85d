
package com.sinosoft.eflex.model.sign.confirm;

@lombok.Data
@SuppressWarnings("unused")
public class SignConfirmRespData {

    // 业务单号 成功true;失败false，非签约申请结果标识，签约申请结果以signState为准
    private String businessNo;
    // 签约流水号
    private String rdSeq;
    // 签约文字信息
    private String signMsg;
    // 签约协议号 已签约数据返回
    private Object signNo;
    // 签约状态编码 0-签约失败；1-签约成功
    private String signState;
    // 交易流水号
    private String transId;
    // 银行卡签约记录编码
    private String bankAccCode;
    // 银行账号
    private String bankAccNo;
    // 客户姓名
    private String customName;
    // 证件类型
    private String idCardType;
    // 证件号码
    private String idCardNo;
    // 手机
    private String mobile;

}
