package com.sinosoft.eflex.model.sign.apply;

import java.util.List;

/**
 * 签约申请入参实体类
 * 
 * <AUTHOR>
 *
 */
public class SignApply {
	private String source;//系统来源  默认：H08
	private String corpEntity;//企业方机构 默认：440498
	private String batchFlag;//批量标志 默认：1
	private String bankCode;//银行代码
	private String accountNo;//账号
	private String accountName;//账户名
	private String accountType;//账户类型
	private String CVV2;//信用卡CVV2
	private String expiredDate;//信用卡有效期
	private String certType;//证件类型
	private String certNo;//证件号码
	private String cellPhone;//手机号
	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}
	public String getCorpEntity() {
		return corpEntity;
	}
	public void setCorpEntity(String corpEntity) {
		this.corpEntity = corpEntity;
	}
	public String getBatchFlag() {
		return batchFlag;
	}
	public void setBatchFlag(String batchFlag) {
		this.batchFlag = batchFlag;
	}
	public String getBankCode() {
		return bankCode;
	}
	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}
	public String getAccountNo() {
		return accountNo;
	}
	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}
	public String getAccountName() {
		return accountName;
	}
	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}
	public String getAccountType() {
		return accountType;
	}
	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}
	public String getCVV2() {
		return CVV2;
	}
	public void setCVV2(String cVV2) {
		CVV2 = cVV2;
	}
	public String getExpiredDate() {
		return expiredDate;
	}
	public void setExpiredDate(String expiredDate) {
		this.expiredDate = expiredDate;
	}
	public String getCertType() {
		return certType;
	}
	public void setCertType(String certType) {
		this.certType = certType;
	}
	public String getCertNo() {
		return certNo;
	}
	public void setCertNo(String certNo) {
		this.certNo = certNo;
	}
	public String getCellPhone() {
		return cellPhone;
	}
	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
}
