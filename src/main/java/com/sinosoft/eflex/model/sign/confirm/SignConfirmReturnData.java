package com.sinosoft.eflex.model.sign.confirm;

public class SignConfirmReturnData {
	private String signNo;//签约流水号
	private String signState;//签约状态
	private String signMsg;//签约信息
	private String signAgreementNo;//签约协议号
	private String bankCode;//银行代码
	private String accountNo;//账号
	private String accountName;//账户名
	private String accountType;//账户类型
	private String certType;//证件类型
	private String certNo;//证件号码
	private String cellPhone;//手机号

	public String getSignNo() {
		return signNo;
	}

	public void setSignNo(String signNo) {
		this.signNo = signNo;
	}

	public String getSignState() {
		return signState;
	}

	public void setSignState(String signState) {
		this.signState = signState;
	}

	public String getSignMsg() {
		return signMsg;
	}

	public void setSignMsg(String signMsg) {
		this.signMsg = signMsg;
	}

	public String getSignAgreementNo() {
		return signAgreementNo;
	}

	public void setSignAgreementNo(String signAgreementNo) {
		this.signAgreementNo = signAgreementNo;
	}

	public String getBankCode() {
		return bankCode;
	}

	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}

	public String getAccountNo() {
		return accountNo;
	}

	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public String getAccountType() {
		return accountType;
	}

	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}

	public String getCertType() {
		return certType;
	}

	public void setCertType(String certType) {
		this.certType = certType;
	}

	public String getCertNo() {
		return certNo;
	}

	public void setCertNo(String certNo) {
		this.certNo = certNo;
	}

	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
}
