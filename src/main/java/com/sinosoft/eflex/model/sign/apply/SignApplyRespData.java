
package com.sinosoft.eflex.model.sign.apply;

import lombok.Data;

import java.io.Serializable;

/**
 * 签约申请返回信息
 */
@Data
public class SignApplyRespData implements Serializable {

    // 业务单号 对接系统-投保单号，原样返回（如没有投保单号，则为对接系统数据的唯一标识）
    private String businessNo;
    // 签约流水号 N
    private String rdSeq;
    // 签约请求信息
    private String signMsg;
    // 签约协议号 已签约数据则返回该信息 N
    private String signNo;
    // 签约状态编码 0-签约申请失败；1-签约申请成功
    private String signState;
    // 交易流水号 N
    private String transId;
    // 银行卡签约记录编码 N
    private String bankAccCode;
    // 银行账号
    private String bankAccNo;
    // 客户姓名
    private String customName;
    // 手机号
    private String mobile;
    // 证件类型
    private String idCardType;
    // 证件号码
    private String idCardNo;

}
