package com.sinosoft.eflex.model.sign.confirm;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SignConfirmRequest implements Serializable {


    /**
     * 用户标识*
     */
    @NotBlank(message = "用户标识不能为空！")
    private String perNo;

    /**
     * 福利编码*
     */
    @NotBlank(message = "福利编码不能为空！")
    private String ensureCode;

    /**
     * 订单编号*
     */
    @NotBlank(message = "订单编号不能为空！")
    private String orderNo;
    /**
     * 验证码*
     */
    @NotBlank(message = "验证码不能为空！")
    private String verifyCode;

}
