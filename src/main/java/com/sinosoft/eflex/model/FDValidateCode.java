package com.sinosoft.eflex.model;


public class FDValidateCode {
    /**
     * 
     */
    private String validatecodesn;

    /**
     * 
     */
    private String userno;

    /**
     * 
     */
    private String validatecode;

    /**
     * 
     */
    private String codetype;

    /**
     * 
     */
    private String isvalid;

    /**
     * 
     */
    private String makedate;

    /**
     * 
     */
    private String maketime;

    /**
     * 
     */
    private String enddate;

    /**
     * 
     */
    private String endtime;

    /**
     * 
     * @return ValidateCodeSN 
     */
    public String getValidatecodesn() {
        return validatecodesn;
    }

    /**
     * 
     * @param validatecodesn 
     */
    public void setValidatecodesn(String validatecodesn) {
        this.validatecodesn = validatecodesn;
    }

    /**
     * 
     * @return UserNo 
     */
    public String getUserno() {
        return userno;
    }

    /**
     * 
     * @param userno 
     */
    public void setUserno(String userno) {
        this.userno = userno;
    }

    /**
     * 
     * @return ValidateCode 
     */
    public String getValidatecode() {
        return validatecode;
    }

    /**
     * 
     * @param validatecode 
     */
    public void setValidatecode(String validatecode) {
        this.validatecode = validatecode;
    }

    /**
     * 
     * @return CodeType 
     */
    public String getCodetype() {
        return codetype;
    }

    /**
     * 
     * @param codetype 
     */
    public void setCodetype(String codetype) {
        this.codetype = codetype;
    }

    /**
     * 
     * @return IsValid 
     */
    public String getIsvalid() {
        return isvalid;
    }

    /**
     * 
     * @param isvalid 
     */
    public void setIsvalid(String isvalid) {
        this.isvalid = isvalid;
    }

    /**
     * 
     * @return MakeDate 
     */
    public String getMakedate() {
        return makedate;
    }

    /**
     * 
     * @param makedate 
     */
    public void setMakedate(String makedate) {
        this.makedate = makedate;
    }

    /**
     * 
     * @return MakeTime 
     */
    public String getMaketime() {
        return maketime;
    }

    /**
     * 
     * @param maketime 
     */
    public void setMaketime(String maketime) {
        this.maketime = maketime;
    }

    /**
     * 
     * @return EndDate 
     */
    public String getEnddate() {
        return enddate;
    }

    /**
     * 
     * @param enddate 
     */
    public void setEnddate(String enddate) {
        this.enddate = enddate;
    }

    /**
     * 
     * @return EndTime 
     */
    public String getEndtime() {
        return endtime;
    }

    /**
     * 
     * @param endtime 
     */
    public void setEndtime(String endtime) {
        this.endtime = endtime;
    }
}