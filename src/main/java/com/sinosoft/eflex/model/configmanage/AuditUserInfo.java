package com.sinosoft.eflex.model.configmanage;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2021/7/22
 * @desc 审核人员信息
 */
@Data
public class AuditUserInfo {

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 用户账号
     */
    private String loginName;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户角色，如有多个角色：以,隔开
     */
    private String userRole;
    private String userRoleName;

    /**
     * 用户手机号
     */
    private String phone;

    /**
     * 用户状态 0-禁用 1-启用
     */
    private String userState;
    private String userStateName;

    /**
     * 管理机构
     */
    private String manageCom;
    private String manageComName;

}
