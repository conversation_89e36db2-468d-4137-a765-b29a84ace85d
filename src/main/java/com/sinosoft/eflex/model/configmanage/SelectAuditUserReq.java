package com.sinosoft.eflex.model.configmanage;

import lombok.Data;


/**
 * <AUTHOR>
 * @create 2021/7/20
 * @desc 查询审核用户信息条件参数
 */
@Data
public class SelectAuditUserReq {

    /**
     * 管理机构
     */
    private String manageCom;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户账号
     */
    private String loginName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 用户状态 0-禁用 1-启用
     */
    private String userState;

    /**
     * 用户角色
     */
    private String userRole;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 条数
     */
    private Integer pageSize;

}
