package com.sinosoft.eflex.model.PolicyUpdate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/12/5 11:46
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("员福查询团险保全数据-定时查已生效保全项推送mq数据封装类")
public class GrpEdorSendResultPojo extends LPGrpEdorLogPojo  implements Serializable {
    @ApiModelProperty("投保单位信息，AC-投保人资料变更")
    private GrpAppntInfo grpAppntInfo;
    @ApiModelProperty("机构信息（客户身份识别），OI-机构身份识别信息变更")
    private GrpInstitutionalInfo grpInstitutionalInfo;
    @ApiModelProperty("被保人信息列表，BB-被保人基本资料变更  IC-被保人重要资料变更  LC-保险计划变更  NI-新增被保险人 NZ-同质风险加减人 ZT-减少被保险人")
    private List<GrpInsuredInfo> grpInsuredInfoList;
    @ApiModelProperty("保险计划列表，LM-新增保险计划")
    private List<GrpContPlanInfo> grpContPlanInfoList;

    public GrpEdorSendResultPojo(LPGrpEdorLogPojo lpGrpEdorLogPojo) {
        BeanUtils.copyProperties(lpGrpEdorLogPojo, this);
    }
}
