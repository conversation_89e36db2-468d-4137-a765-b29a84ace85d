package com.sinosoft.eflex.model.PolicyUpdate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/12/18 17:03
 **/
@Data
public class GrpContPlanInfo    implements Serializable {
    @ApiModelProperty("保险计划编码")
    private String contPlanCode;
    @ApiModelProperty("保险计划名称")
    private String contPlanName;
    @ApiModelProperty("计划类别")
    private String planType;
    @ApiModelProperty("保险计划详细信息")
    List<GrpContPlanDetailInfo> grpContPlanDetailInfoList;
}
