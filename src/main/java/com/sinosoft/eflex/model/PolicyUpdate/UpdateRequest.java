package com.sinosoft.eflex.model.PolicyUpdate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/12/11 14:29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpdateRequest  implements Serializable {

    private PolicyDataContent dataContent;
    //private String dataHead;

}
