package com.sinosoft.eflex.model.PolicyUpdate;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/12/5 14:21
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GrpInsuredInfo   implements Serializable {
    // LDPerson
    @ApiModelProperty("客户号")
    private String insuredNo;
    @ApiModelProperty("个人保单号")
    private String contNo;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("性别")
    private String sex;
    @ApiModelProperty("出生日期")
    private String birthday;
    @ApiModelProperty("证件类型")
    private String idType;
    @ApiModelProperty("证件号码")
    private String idNo;

    private String oldIdNo;
    @ApiModelProperty("证件有效期起期")
    private String IDExpStartDate;
    @ApiModelProperty("证件有效期止期")
    private String idExpDate;
    @ApiModelProperty("国籍")
    private String nativePlace;
    @ApiModelProperty("职业代码")
    private String occupationCode;
    @ApiModelProperty("职业类别")
    private String occupationType;
    @ApiModelProperty("是否拥有社保")
    private String socialInsuFlag;
    @ApiModelProperty("被保人年龄")
    private String age;
    @ApiModelProperty("健康服务标识")
    private String health;

    // LCAddress
    @ApiModelProperty("移动电话")
    private String mobile;
    @ApiModelProperty("其他联系电话")
    private String bp;
    @ApiModelProperty("电子邮箱")
    private String email;

    // LCInsured
    @ApiModelProperty("开户银行")
    private String bankCode;
    @ApiModelProperty("银行账户")
    private String bankAccNo;
    @ApiModelProperty("账户名")
    private String accName;
    @ApiModelProperty("入司时间")
    private String joinCompanyDate;
    @ApiModelProperty("工资")
    private String salary;
    @ApiModelProperty("工作证号")
    private String workNo;
    @ApiModelProperty("保险计划编码")
    private String contPlanCode;
    @ApiModelProperty("保险计划名称")
    private String contPlanName;
    @ApiModelProperty("子公司代码")
    private String grpNo;
    @ApiModelProperty("处理机构")
    private String executeCom;
    @ApiModelProperty("卡单编码")
    private String certifyCode;
    @ApiModelProperty("卡单起号")
    private String startCode;
    @ApiModelProperty("卡单止号")
    private String endCode;
    @ApiModelProperty("主被保人姓名")
    private String lastNamePy;
    @ApiModelProperty("主被保人客户号")
    private String employeeNo;
    @ApiModelProperty("主被保人证件号")
    private String employeeIdNo;
    @ApiModelProperty("与主被保人关系")
    private String employeeRelation;
    @ApiModelProperty("投保人是被保人的")
    private String relationToAppnt;

    // LCCont
    @ApiModelProperty("保单生效日期")
    private String cvaliDate;
    @ApiModelProperty("个人保单总保费")
    private String prem;
    /**
     * 核心被保人表i对应员福订单表，但是被保人保单终止（减人保全）不会从被保人表删除，员福会从订单表删除，此字段用来判断被保人保单是否有效
     * appFlag = 1 有效保单  appFlag = 4 终止保单
     */
    @ApiModelProperty("保单状态 1-有效保单  4-终止保单")
    private String appFlag;
    /**
     * 增人的时候会有这个字段
     * 1-在职
     * 2-退休
     * 3-离休
     * 4-未就业
     */
    @ApiModelProperty("增人的时候会有这个字段")
    private String state;

    // LCPol-standByFlag1
    @ApiModelProperty("减保生效日期")
    private String ztValiDate;


    private String PerTempNo;
    private String personID;
}
