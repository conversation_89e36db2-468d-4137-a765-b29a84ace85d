package com.sinosoft.eflex.model.PolicyUpdate;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/12/5 14:21
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GrpInstitutionalInfo   implements Serializable {
    @ApiModelProperty("机构名称")
    private String orgName;
    @ApiModelProperty("统一社会信用代码")
    private String socialCreditCode;
    @ApiModelProperty("营业执照有效期")
    private String bak1;
    @ApiModelProperty("其他证件类型")
    private String otherCardType;
    @ApiModelProperty("证件代码")
    private String otherCardNo;
    @ApiModelProperty("证件有效期")
    private String bak2;
    @ApiModelProperty("注册会所")
    private String registrationClub;
    @ApiModelProperty("经营范围")
    private String scopeOfBusiness;
    @ApiModelProperty("银行账户名称")
    private String bankAccount;
    @ApiModelProperty("银行账号")
    private String bankAccountNoNew;
    @ApiModelProperty("开户银行名称-银行")
    private String depositBankName;
    @ApiModelProperty("开户银行名称-分行")
    private String depositBankName1;
    @ApiModelProperty("开户银行名称-支行")
    private String depositBankName2;
    @ApiModelProperty("开户行所在地-省")
    private String depositBankNameProvice;
    @ApiModelProperty("开户行所在地-市(县)")
    private String depositBankNameCity;
    @ApiModelProperty("所属行业")
    private String businessTypeNew;
    @ApiModelProperty("企业类别")
    private String grpNatureNew;
    @ApiModelProperty("法人类别")
    private String corporationType;
    @ApiModelProperty("交易目的")
    private String trading;
    @ApiModelProperty("其他详细信息")
    private String otherDetail;
    @ApiModelProperty("资金来源")
    private String fundsProvided;
    @ApiModelProperty("资金来源是否为信托财产")
    private String isXT;
    @ApiModelProperty("填表人姓名")
    private String pname;
    @ApiModelProperty("职业代码")
    private String occupationCode1;
    @ApiModelProperty("手机")
    private String telPhone;
    @ApiModelProperty("电子邮箱")
    private String email;
    @ApiModelProperty("控股股东/实际控制人信息")
    private List<GrpOrgPersonsInfo> shareholderList;
    @ApiModelProperty("受益所有人信息")
    private List<GrpOrgPersonsInfo> beneficialList;
    @ApiModelProperty("法定代表人信息")
    private List<GrpOrgPersonsInfo> corporationList;
    @ApiModelProperty("授权经办人信息")
    private List<GrpOrgPersonsInfo> linkManList;
    @ApiModelProperty("信托信息")
    private List<GrpOrgXTInfo> xtInfoList;
}
