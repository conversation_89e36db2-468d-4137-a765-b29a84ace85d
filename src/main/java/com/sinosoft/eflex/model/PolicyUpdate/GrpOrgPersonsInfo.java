package com.sinosoft.eflex.model.PolicyUpdate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/12/5 17:34
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("客户身份识别-控股股东/实际控制人、受益所有人、法定代表人、授权经办人实体类")
public class GrpOrgPersonsInfo   implements Serializable {
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("证件类型")
    private String idType;
    @ApiModelProperty("证件号码")
    private String idNo;
    @ApiModelProperty("证件有效期")
    private String idExpDate;
    @ApiModelProperty("地址")
    private String address;
}
