package com.sinosoft.eflex.model.PolicyUpdate;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/12/5 15:08
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GrpLinkManInfo   implements Serializable {
    @ApiModelProperty("姓名")
    private String linkMan1;
    @ApiModelProperty("性别")
    private String gender1;
    @ApiModelProperty("出生日期")
    private String birthday1;
    @ApiModelProperty("证件类型")
    private String insContIDType1;
    @ApiModelProperty("授权经办人原证件号码")
    private String insContOldIDNo1;
    @ApiModelProperty("证件号码")
    private String insContIDNo1;
    @ApiModelProperty("国籍")
    private String nationality1;
    @ApiModelProperty("证件有效期起期")
    private String insContIDStartPeriod1;
    @ApiModelProperty("证件有效期止期")
    private String insContIDPeriodOfValidityType1;
    @ApiModelProperty("联系方式")
    private String preferredPhoneNum1;
    @ApiModelProperty("手机号码")
    private String mobilePhone1;
    @ApiModelProperty("电子邮箱")
    private String email1;
    @ApiModelProperty("固定电话")
    private String phone1;
}
