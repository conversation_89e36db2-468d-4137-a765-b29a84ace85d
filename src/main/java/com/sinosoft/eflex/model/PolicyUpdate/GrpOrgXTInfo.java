package com.sinosoft.eflex.model.PolicyUpdate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/12/5 17:38
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("客户身份识别-信托信息实体类")
public class GrpOrgXTInfo  implements Serializable {
    @ApiModelProperty("信托人姓名")
    private String xtName;
    @ApiModelProperty("信托人-联系方式")
    private String xtTelPhone;
    @ApiModelProperty("信托受益人姓名")
    private String xtBnfName;
    @ApiModelProperty("信托受益人-联系方式")
    private String xtBnfTelPhone;
}
