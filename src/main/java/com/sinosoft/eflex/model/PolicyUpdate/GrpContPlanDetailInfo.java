package com.sinosoft.eflex.model.PolicyUpdate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/12/18 17:04
 **/
@Data
public class GrpContPlanDetailInfo   implements Serializable {
    @ApiModelProperty("险种编码")
    private String riskCode;
    @ApiModelProperty("险种编码")
    private String riskName;
    @ApiModelProperty("责任编码")
    private String dutyCode;
    @ApiModelProperty("责任名称")
    private String dutyName;
    @ApiModelProperty("责任类型 M-必选 B-备用 其他-可选")
    private String choFlag;
    @ApiModelProperty("要素名称")
    private String factorName;
    @ApiModelProperty("要素值")
    private String calFactorValue;
}
