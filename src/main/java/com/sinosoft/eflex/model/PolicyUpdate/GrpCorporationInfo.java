package com.sinosoft.eflex.model.PolicyUpdate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/12/5 15:03
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("客户基本信息公共类，对应AC保全项法定代表人信息")
public class GrpCorporationInfo  implements Serializable {
    @ApiModelProperty("姓名")
    private String corporation;
    @ApiModelProperty("性别")
    private String corporationGender;
    @ApiModelProperty("出生日期")
    private String corporationBirthday;
    @ApiModelProperty("证件类型")
    private String corporationIDType;
    @ApiModelProperty("证件号码")
    private String corporationIDNo;
    @ApiModelProperty("法人原证件号码")
    private String corporationOldIDNo;
    @ApiModelProperty("国籍")
    private String corporationNationality;
    @ApiModelProperty("证件有效期起期")
    private String corporationIDExpStartDate;
    @ApiModelProperty("证件有效期止期")
    private String corporationIDExpDate;
}
