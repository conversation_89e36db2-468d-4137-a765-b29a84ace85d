package com.sinosoft.eflex.model.PolicyUpdate;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/12/5 14:21
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GrpAppntInfo  implements Serializable {
    @ApiModelProperty("单位客户号")
    private String appntNo;
    @ApiModelProperty("投保人名称")
    private String grpName;
    @ApiModelProperty("单位性质 一级")
    private String grpNature1;
    @ApiModelProperty("单位性质 二级")
    private String grpNature;
    @ApiModelProperty("行业类别")
    private String businessType;
    @ApiModelProperty("员工总数")
    private String peoples;
    @ApiModelProperty("证件类型")
    private String comType;
    @ApiModelProperty("统一社会信用代码")
    private String comNo;
    @ApiModelProperty("单位成立日期")
    private String foundDate;
    @ApiModelProperty("付款方式")
    private String getFlag;
    @ApiModelProperty("开户银行")
    private String bankCode;
    @ApiModelProperty("账号")
    private String bankAccNo;
    @ApiModelProperty("证件有效期起期")
    private String busliceStartDate;
    @ApiModelProperty("证件有效期止期")
    private String busliceDate;
    @ApiModelProperty("控股股东或者实际控制人")
    private String contrShar;
    @ApiModelProperty("注册地")
    private String regestedPlace;
    @ApiModelProperty("单位地址编码")
    private String addressNo;
    @ApiModelProperty("单位地址")
    private String grpAddress;
    @ApiModelProperty("同质风险加减人百分比")
    private String nzProportion;
    @ApiModelProperty("法定代表人/负责人信息")
    private GrpCorporationInfo grpCorporationInfo;
    @ApiModelProperty("授权经办人信息")
    private GrpLinkManInfo grpLinkManInfo;


}
