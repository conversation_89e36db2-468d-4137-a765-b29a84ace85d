package com.sinosoft.eflex.model.PolicyUpdate;

import com.sinosoft.eflex.model.AddressEntity.ReturnCode;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/12/7 10:49
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PolicyUpdateRequest  implements Serializable {

    @ApiModelProperty("body")
    private GrpEdorSendResultPojo body;
    @ApiModelProperty("cid")
    private int cid;
    /**
     * code = 0表示数据正常
     */
    @ApiModelProperty("code")
    private int code;
    @ApiModelProperty("elapsed")
    private int elapsed;
    @ApiModelProperty("message")
    private String message;
    @ApiModelProperty("returnCode")
    private ReturnCode returnCode;
    @ApiModelProperty("systime")
    private long systime;
}
