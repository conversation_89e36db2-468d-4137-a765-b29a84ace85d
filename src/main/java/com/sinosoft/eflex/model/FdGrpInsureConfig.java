package com.sinosoft.eflex.model;

import java.util.Date;

public class FdGrpInsureConfig {
    /**
     * 企业编号
     */
    private String grpNo;

    /**
     * 企业名称
     */
    private String grpName;

    /**
     * 是否为场地险
     */
    private String isSiteInsure;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 生成日期
     */
    private Date makeDate;

    /**
     * 生成时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private Date modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 企业编号
     * 
     * @return GrpNo 企业编号
     */
    public String getGrpNo() {
        return grpNo;
    }

    /**
     * 企业编号
     * 
     * @param grpNo
     *            企业编号
     */
    public void setGrpNo(String grpNo) {
        this.grpNo = grpNo;
    }

    /**
     * 企业名称
     * 
     * @return GrpName 企业名称
     */
    public String getGrpName() {
        return grpName;
    }

    /**
     * 企业名称
     * 
     * @param grpName
     *            企业名称
     */
    public void setGrpName(String grpName) {
        this.grpName = grpName;
    }

    /**
     * 是否为场地险
     * 
     * @return IsSiteInsure 是否为场地险
     */
    public String getIsSiteInsure() {
        return isSiteInsure;
    }

    /**
     * 是否为场地险
     * 
     * @param isSiteInsure
     *            是否为场地险
     */
    public void setIsSiteInsure(String isSiteInsure) {
        this.isSiteInsure = isSiteInsure;
    }

    /**
     * 操作员
     * 
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * 
     * @param operator
     *            操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 操作机构
     * 
     * @return OperatorCom 操作机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作机构
     * 
     * @param operatorCom
     *            操作机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 生成日期
     * 
     * @return MakeDate 生成日期
     */
    public Date getMakeDate() {
        return makeDate;
    }

    /**
     * 生成日期
     * 
     * @param makeDate
     *            生成日期
     */
    public void setMakeDate(Date makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 生成时间
     * 
     * @return MakeTime 生成时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 生成时间
     * 
     * @param makeTime
     *            生成时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * 
     * @return ModifyDate 修改日期
     */
    public Date getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * 
     * @param modifyDate
     *            修改日期
     */
    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * 
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * 
     * @param modifyTime
     *            修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}