package com.sinosoft.eflex.model.remove;


import com.sinosoft.eflex.model.policy.DataHead;

/**

/**
 * <AUTHOR> wenying <PERSON>a
 * @date : 2020-07-20 10:59
 **/
public class DataBadyRemove {
    private DataContentRemove dataContent;
    private DataHead dataHead;


    public DataContentRemove getDataContent() {
        return dataContent;
    }

    public void setDataContent(DataContentRemove dataContent) {
        this.dataContent = dataContent;
    }

    public DataHead getDataHead() {
        return dataHead;
    }

    public void setDataHead(DataHead dataHead) {
        this.dataHead = dataHead;
    }

}
