package com.sinosoft.eflex.model.remove;/**

/**
 * <AUTHOR> wenying <PERSON>a
 * @date : 2020-07-20 11:02
 **/
public class DataContentValueRemove {
    /**
     * 代理人工号
     */
    private String uid;
    /**
     * 投保人姓名
     */
    private String applyName;
    /**
     * 投保单号   例：A4419000000001
     */
    private String applyCode;
    /**
     * 撤件日期  例：2020-02-02 00:00:00
     */
    private String cancleTime;
    /**
     * 撤件原因
     */
    private String cancelCause;
    /**
     * 代理人姓名
     */
    private String agentName;
    /**
     * 保单号  例：A4419000000001
     */
    private String contno;
    /**
     * 保费
     */
    private String prem;
    /**
     * 被保险人姓名
     */
    private String insuredName;
    /**
     * 险种名称  例：横琴一路无忧
     */
    private String riskName;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getApplyName() {
        return applyName;
    }

    public void setApplyName(String applyName) {
        this.applyName = applyName;
    }

    public String getApplyCode() {
        return applyCode;
    }

    public void setApplyCode(String applyCode) {
        this.applyCode = applyCode;
    }

    public String getCancleTime() {
        return cancleTime;
    }

    public void setCancleTime(String cancleTime) {
        this.cancleTime = cancleTime;
    }

    public String getCancelCause() {
        return cancelCause;
    }

    public void setCancelCause(String cancelCause) {
        this.cancelCause = cancelCause;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getContno() {
        return contno;
    }

    public void setContno(String contno) {
        this.contno = contno;
    }

    public String getPrem() {
        return prem;
    }

    public void setPrem(String prem) {
        this.prem = prem;
    }

    public String getInsuredName() {
        return insuredName;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }

    public String getRiskName() {
        return riskName;
    }

    public void setRiskName(String riskName) {
        this.riskName = riskName;
    }
}
