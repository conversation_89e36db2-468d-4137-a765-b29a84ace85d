package com.sinosoft.eflex.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FdUser {
    private String edorType;
    private String oldIdNo;
    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 登录名
     */
    private String userName;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 用户密码
     */
    private String passWord;

    /**
     * 用户类型
     */
    private String customType;

    /**
     * 客户号
     */
    private String customNo;

    /**
     * 是否锁定
     */
    private String isLock;

    /**
     * 是否是VIP
     */
    private String isVIP;

    /**
     * 登录失败次数
     */
    private Integer loginFailTimes;

    /**
     * 用户状态
     */
    private String userState;

    /**
     * 用户来源
     */
    private String source;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机
     */
    private String phone;

    /**
     * 证件号码
     */
    private String IDNo;

    /**
     * 移动端请求ID
     */
    private String openID;

    // add by wudezhong 2021.7.21 谢老师生产优化需求，新增管理机构字段
    /**
     * 管理机构
     */
    private String manageCom;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 注册日期
     */
    private String makeDate;

    /**
     * 注册时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 密码修改状态
     */
    private String PWDState;

    /**
     * 锁定日期
     */
    private String lockDate;

    /**
     * 锁定时间
     */
    private String lockTime;

    /**
     * 是否需要验证码
     */
    private String isNeedCaptcha;

    //add by zch 2020/11/12
    /**
     * 密码变更日期
     */
    private String pWDChangeDate;

    /**
     * 用户编号
     * @return UserNo 用户编号
     */
    public String getUserNo() {
        return userNo;
    }

    /**
     * 用户编号
     * @param userNo 用户编号
     */
    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    /**
     * 登录名
     * @return UserName 登录名
     */
    public String getUserName() {
        return userName;
    }

    /**
     * 登录名
     * @param userName 登录名
     */
    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     * 昵称
     * @return NickName 昵称
     */
    public String getNickName() {
        return nickName;
    }

    /**
     * 昵称
     * @param nickName 昵称
     */
    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    /**
     * 用户密码
     * @return PassWord 用户密码
     */
    public String getPassWord() {
        return passWord;
    }

    /**
     * 用户密码
     * @param passWord 用户密码
     */
    public void setPassWord(String passWord) {
        this.passWord = passWord;
    }

    /**
     * 用户类型
     * @return CustomType 用户类型
     */
    public String getCustomType() {
        return customType;
    }

    /**
     * 用户类型
     * @param customType 用户类型
     */
    public void setCustomType(String customType) {
        this.customType = customType;
    }

    /**
     * 客户号
     * @return CustomNo 客户号
     */
    public String getCustomNo() {
        return customNo;
    }

    /**
     * 客户号
     * @param customNo 客户号
     */
    public void setCustomNo(String customNo) {
        this.customNo = customNo;
    }

    /**
     * 是否锁定
     * @return IsLock 是否锁定
     */
    public String getIsLock() {
        return isLock;
    }

    /**
     * 是否锁定
     * @param isLock 是否锁定
     */
    public void setIsLock(String isLock) {
        this.isLock = isLock;
    }

    /**
     * 是否是VIP
     * @return IsVIP 是否是VIP
     */
    public String getIsVIP() {
        return isVIP;
    }

    /**
     * 是否是VIP
     * @param isVIP 是否是VIP
     */
    public void setIsVIP(String isVIP) {
        this.isVIP = isVIP;
    }

    /**
     * 登录失败次数
     * @return LoginFailTimes 登录失败次数
     */
    public Integer getLoginFailTimes() {
        return loginFailTimes;
    }

    /**
     * 登录失败次数
     * @param loginFailTimes 登录失败次数
     */
    public void setLoginFailTimes(Integer loginFailTimes) {
        this.loginFailTimes = loginFailTimes;
    }

    /**
     * 用户状态
     * @return UserState 用户状态
     */
    public String getUserState() {
        return userState;
    }

    /**
     * 用户状态
     * @param userState 用户状态
     */
    public void setUserState(String userState) {
        this.userState = userState;
    }

    /**
     * 用户来源
     * @return Source 用户来源
     */
    public String getSource() {
        return source;
    }

    /**
     * 用户来源
     * @param source 用户来源
     */
    public void setSource(String source) {
        this.source = source;
    }

    /**
     * 邮箱
     * @return Email 邮箱
     */
    public String getEmail() {
        return email;
    }

    /**
     * 邮箱
     * @param email 邮箱
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * 手机
     * @return Phone 手机
     */
    public String getPhone() {
        return phone;
    }

    /**
     * 手机
     * @param phone 手机
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * 证件号码
     * @return IDNo 证件号码
     */
    public String getIDNo() {
        return IDNo;
    }

    /**
     * 证件号码
     * @param IDNo 证件号码
     */
    public void setIDNo(String IDNo) {
        this.IDNo = IDNo;
    }

    /**
     * 移动端请求ID
     * @return OpenID 移动端请求ID
     */
    public String getOpenID() {
        return openID;
    }

    /**
     * 移动端请求ID
     * @param openID 移动端请求ID
     */
    public void setOpenID(String openID) {
        this.openID = openID;
    }

    public String getManageCom() {
        return manageCom;
    }

    public void setManageCom(String manageCom) {
        this.manageCom = manageCom;
    }

    /**
     * 备注
     * @return Remark 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 备注
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 操作机构
     * @return OperatorCom 操作机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作机构
     * @param operatorCom 操作机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 注册日期
     * @return MakeDate 注册日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 注册日期
     * @param makeDate 注册日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 注册时间
     * @return MakeTime 注册时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 注册时间
     * @param makeTime 注册时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    /**
     * 密码修改状态
     * @return PWDState 密码修改状态
     */
    public String getPWDState() {
        return PWDState;
    }

    /**
     * 密码修改状态
     * @param PWDState 密码修改状态
     */
    public void setPWDState(String PWDState) {
        this.PWDState = PWDState;
    }

    /**
     * 锁定日期
     * @return LockDate 锁定日期
     */
    public String getLockDate() {
        return lockDate;
    }

    /**
     * 锁定日期
     * @param lockDate 锁定日期
     */
    public void setLockDate(String lockDate) {
        this.lockDate = lockDate;
    }

    /**
     * 锁定时间
     * @return LockTime 锁定时间
     */
    public String getLockTime() {
        return lockTime;
    }

    /**
     * 锁定时间
     * @param lockTime 锁定时间
     */
    public void setLockTime(String lockTime) {
        this.lockTime = lockTime;
    }

    /**
     * 密码变更日期
     * @return PWDChangeDate 密码变更日期
     */
    public  String  getPWDChangeDate(){
        return pWDChangeDate;
    }

    /**
     * 密码变更日期
     * @param pWDChangeDate 密码变更日期
     */
    public void setPWDChangeDate(String pWDChangeDate){
        this.pWDChangeDate = pWDChangeDate;
    }

    /**
     * 是否需要验证码
     * @return IsNeedCaptcha 是否需要验证码
     */
    public String getIsNeedCaptcha() {
        return isNeedCaptcha;
    }

    /**
     * 是否需要验证码
     * @param isNeedCaptcha 是否需要验证码
     */
    public void setIsNeedCaptcha(String isNeedCaptcha) {
        this.isNeedCaptcha = isNeedCaptcha;
    }
}