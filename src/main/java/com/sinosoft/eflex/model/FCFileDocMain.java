package com.sinosoft.eflex.model;

public class FCFileDocMain {
    /**
     * 
     */
    private String docID;

    /**
     * 
     */
    private String docType;

    /**
     * 
     */
    private String fileName;

    /**
     * 
     */
    private String fileSaveName;

    /**
     * 
     */
    private String fileSuffix;

    /**
     * 
     */
    private String fileURL;

    /**
     * 
     */
    private String filePath;

    /**
     * 
     */
    private String validFlag;

    /**
     * 
     */
    private String remark;

    /**
     * 
     */
    private String operator;

    /**
     * 
     */
    private String operatorCom;

    /**
     * 
     */
    private String makeDate;

    /**
     * 
     */
    private String makeTime;

    /**
     * 
     */
    private String modifyDate;

    /**
     * 
     */
    private String modifyTime;

    /**
     * 
     * @return DocID
     */
    public String getDocID() {
        return docID;
    }

    /**
     * 
     * @param docID
     */
    public void setDocID(String docID) {
        this.docID = docID;
    }

    /**
     * 
     * @return DocType
     */
    public String getDocType() {
        return docType;
    }

    /**
     * 
     * @param docType
     */
    public void setDocType(String docType) {
        this.docType = docType;
    }

    /**
     * 
     * @return FileName
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * 
     * @param fileName
     */
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     * 
     * @return FileSaveName
     */
    public String getFileSaveName() {
        return fileSaveName;
    }

    /**
     * 
     * @param fileSaveName
     */
    public void setFileSaveName(String fileSaveName) {
        this.fileSaveName = fileSaveName;
    }

    /**
     * 
     * @return FileSuffix
     */
    public String getFileSuffix() {
        return fileSuffix;
    }

    /**
     * 
     * @param fileSuffix
     */
    public void setFileSuffix(String fileSuffix) {
        this.fileSuffix = fileSuffix;
    }

    /**
     * 
     * @return FileURL
     */
    public String getFileURL() {
        return fileURL;
    }

    /**
     * 
     * @param fileURL
     */
    public void setFileURL(String fileURL) {
        this.fileURL = fileURL;
    }

    /**
     * 
     * @return FilePath
     */
    public String getFilePath() {
        return filePath;
    }

    /**
     * 
     * @param filePath
     */
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    /**
     * 
     * @return ValidFlag
     */
    public String getValidFlag() {
        return validFlag;
    }

    /**
     * 
     * @param validFlag
     */
    public void setValidFlag(String validFlag) {
        this.validFlag = validFlag;
    }

    /**
     * 
     * @return Remark
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 
     * @param remark
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 
     * @return Operator
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 
     * @param operator
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 
     * @return OperatorCom
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 
     * @param operatorCom
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 
     * @return MakeDate
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 
     * @param makeDate
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 
     * @return MakeTime
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 
     * @param makeTime
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 
     * @return ModifyDate
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 
     * @param modifyDate
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 
     * @return ModifyTime
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 
     * @param modifyTime
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "FCFileDocMain [docID=" + docID + ", docType=" + docType + ", fileName=" + fileName + ", fileSaveName="
                + fileSaveName + ", fileSuffix=" + fileSuffix + ", fileURL=" + fileURL + ", filePath=" + filePath
                + ", validFlag=" + validFlag + ", remark=" + remark + ", operator=" + operator + ", operatorCom="
                + operatorCom + ", makeDate=" + makeDate + ", makeTime=" + makeTime + ", modifyDate=" + modifyDate
                + ", modifyTime=" + modifyTime + "]";
    }

}