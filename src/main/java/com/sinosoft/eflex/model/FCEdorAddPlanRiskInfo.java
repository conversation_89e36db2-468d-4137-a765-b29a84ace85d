package com.sinosoft.eflex.model;

import java.math.BigDecimal;
import java.util.Date;

public class FCEdorAddPlanRiskInfo {
    /**
     * 增人计划流水号
     */
    private String edorAddPlanSN;

    /**
     * 险种编码
     */
    private String riskCode;

    /**
     * 团体保单号
     */
    private  String grpContNo;

    /**
     * 险种名称
     */
    private String riskName;

    /**
     * 险种保额
     */
    private BigDecimal riskAmnt;

    /**
     * 险种保费
     */
    private BigDecimal riskPrem;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作员机构
     */
    private String operatorCom;

    /**
     * 生成日期
     */
    private String makeDate;

    /**
     * 生成时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 增人计划流水号
     * @return EdorAddPlanSN 增人计划流水号
     */
    public String getEdorAddPlanSN() {
        return edorAddPlanSN;
    }

    /**
     * 增人计划流水号
     * @param edorAddPlanSN 增人计划流水号
     */
    public void setEdorAddPlanSN(String edorAddPlanSN) {
        this.edorAddPlanSN = edorAddPlanSN;
    }

    /**
     * 险种编码
     * @return RiskCode 险种编码
     */
    public String getRiskCode() {
        return riskCode;
    }

    /**
     * 险种编码
     * @param riskCode 险种编码
     */
    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    public String getGrpContNo() {
        return grpContNo;
    }

    public void setGrpContNo(String grpContNo) {
        this.grpContNo = grpContNo;
    }

    /**
     * 险种名称
     * @return RiskName 险种名称
     */
    public String getRiskName() {
        return riskName;
    }

    /**
     * 险种名称
     * @param riskName 险种名称
     */
    public void setRiskName(String riskName) {
        this.riskName = riskName;
    }

    /**
     * 险种保额
     * @return RiskAmnt 险种保额
     */
    public BigDecimal getRiskAmnt() {
        return riskAmnt;
    }

    /**
     * 险种保额
     * @param riskAmnt 险种保额
     */
    public void setRiskAmnt(BigDecimal riskAmnt) {
        this.riskAmnt = riskAmnt;
    }

    /**
     * 险种保费
     * @return RiskPrem 险种保费
     */
    public BigDecimal getRiskPrem() {
        return riskPrem;
    }

    /**
     * 险种保费
     * @param riskPrem 险种保费
     */
    public void setRiskPrem(BigDecimal riskPrem) {
        this.riskPrem = riskPrem;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 操作员机构
     * @return OperatorCom 操作员机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作员机构
     * @param operatorCom 操作员机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 生成日期
     * @return MakeDate 生成日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 生成日期
     * @param makeDate 生成日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 生成时间
     * @return MakeTime 生成时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 生成时间
     * @param makeTime 生成时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}