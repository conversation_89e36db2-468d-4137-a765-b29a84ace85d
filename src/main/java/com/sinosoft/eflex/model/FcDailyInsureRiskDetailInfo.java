package com.sinosoft.eflex.model;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> wenying <PERSON>a
 * @date : 2020-04-16 10:56
 *
 *      FcDailyInsureRiskDetailInfo(日常投保险种信息配置详情表)
 **/
@Data
public class FcDailyInsureRiskDetailInfo implements Serializable {

    private String DeployDetalNo;//详情流水号
    private String DeployNo;//配置流水号
    private String EnsureCode;//福利编号
    private String RiskCode;//险种编码
    private String PlanCode;//计划编码
    private String PlanState;//计划状态 0--有效 1--无效
    private String OperatorCom;//操作机构
    private String Operator;//操作员
    private String MakeDate;//入机日期
    private String MakeTime;//入机时间
    private String ModifyDate;//修改日期
    private String ModifyTime;//修改时间
    private String PlanUnderwritingStatus;//计划承保状态
	public String getDeployDetalNo() {
		return DeployDetalNo;
	}
	public void setDeployDetalNo(String deployDetalNo) {
		DeployDetalNo = deployDetalNo;
	}
	public String getDeployNo() {
		return DeployNo;
	}
	public void setDeployNo(String deployNo) {
		DeployNo = deployNo;
	}
	public String getEnsureCode() {
		return EnsureCode;
	}
	public void setEnsureCode(String ensureCode) {
		EnsureCode = ensureCode;
	}
	public String getRiskCode() {
		return RiskCode;
	}
	public void setRiskCode(String riskCode) {
		RiskCode = riskCode;
	}
	public String getPlanCode() {
		return PlanCode;
	}
	public void setPlanCode(String planCode) {
		PlanCode = planCode;
	}
	public String getPlanState() {
		return PlanState;
	}
	public void setPlanState(String planState) {
		PlanState = planState;
	}
	public String getOperatorCom() {
		return OperatorCom;
	}
	public void setOperatorCom(String operatorCom) {
		OperatorCom = operatorCom;
	}
	public String getOperator() {
		return Operator;
	}
	public void setOperator(String operator) {
		Operator = operator;
	}
	public String getMakeDate() {
		return MakeDate;
	}
	public void setMakeDate(String makeDate) {
		MakeDate = makeDate;
	}
	public String getMakeTime() {
		return MakeTime;
	}
	public void setMakeTime(String makeTime) {
		MakeTime = makeTime;
	}
	public String getModifyDate() {
		return ModifyDate;
	}
	public void setModifyDate(String modifyDate) {
		ModifyDate = modifyDate;
	}
	public String getModifyTime() {
		return ModifyTime;
	}
	public void setModifyTime(String modifyTime) {
		ModifyTime = modifyTime;
	}
	public String getPlanUnderwritingStatus() {
		return PlanUnderwritingStatus;
	}
	public void setPlanUnderwritingStatus(String planUnderwritingStatus) {
		PlanUnderwritingStatus = planUnderwritingStatus;
	}
    
}
