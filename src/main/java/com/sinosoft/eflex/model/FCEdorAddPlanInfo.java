package com.sinosoft.eflex.model;

import java.math.BigDecimal;
import java.util.Date;

public class FCEdorAddPlanInfo {
    /**
     * 增人计划流水号
     */
    private String edorAddPlanSN;

    /**
     * 团体保单号
     */
    private String grpContNo;

    /**
     * 批次号
     */
    private String batch;

    /**
     * 计划编码
     */
    private String planCode;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 计划对象
     */
    private String planObject;

    /**
     * 计划总保费
     */
    private BigDecimal totalPrem;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作员机构
     */
    private String operatorCom;

    /**
     * 生成日期
     */
    private String makeDate;

    /**
     * 生成时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 增人计划流水号
     * @return EdorAddPlanSN 增人计划流水号
     */
    public String getEdorAddPlanSN() {
        return edorAddPlanSN;
    }

    /**
     * 增人计划流水号
     * @param edorAddPlanSN 增人计划流水号
     */
    public void setEdorAddPlanSN(String edorAddPlanSN) {
        this.edorAddPlanSN = edorAddPlanSN;
    }

    /**
     * 团体保单号
     * @return GrpContNo 团体保单号
     */
    public String getGrpContNo() {
        return grpContNo;
    }

    /**
     * 团体保单号
     * @param grpContNo 团体保单号
     */
    public void setGrpContNo(String grpContNo) {
        this.grpContNo = grpContNo;
    }

    /**
     * 批次号
     * @return Batch 批次号
     */
    public String getBatch() {
        return batch;
    }

    /**
     * 批次号
     * @param batch 批次号
     */
    public void setBatch(String batch) {
        this.batch = batch;
    }

    /**
     * 计划编码
     * @return PlanCode 计划编码
     */
    public String getPlanCode() {
        return planCode;
    }

    /**
     * 计划编码
     * @param planCode 计划编码
     */
    public void setPlanCode(String planCode) {
        this.planCode = planCode;
    }

    /**
     * 计划名称
     * @return PlanName 计划名称
     */
    public String getPlanName() {
        return planName;
    }

    /**
     * 计划名称
     * @param planName 计划名称
     */
    public void setPlanName(String planName) {
        this.planName = planName;
    }

    /**
     * 计划对象
     * @return PlanObject 计划对象
     */
    public String getPlanObject() {
        return planObject;
    }

    /**
     * 计划对象
     * @param planObject 计划对象
     */
    public void setPlanObject(String planObject) {
        this.planObject = planObject;
    }

    /**
     * 计划总保费
     * @return TotalPrem 计划总保费
     */
    public BigDecimal getTotalPrem() {
        return totalPrem;
    }

    /**
     * 计划总保费
     * @param totalPrem 计划总保费
     */
    public void setTotalPrem(BigDecimal totalPrem) {
        this.totalPrem = totalPrem;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 操作员机构
     * @return OperatorCom 操作员机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作员机构
     * @param operatorCom 操作员机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 生成日期
     * @return MakeDate 生成日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 生成日期
     * @param makeDate 生成日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 生成时间
     * @return MakeTime 生成时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 生成时间
     * @param makeTime 生成时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}