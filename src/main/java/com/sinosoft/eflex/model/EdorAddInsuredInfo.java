package com.sinosoft.eflex.model;

import lombok.Data;

@Data
public class EdorAddInsuredInfo {
    /**
     * 序号
     */
    private String orderNumber;

    /**
     * 保全增人人员编号
     */
    private String plusInsuredSN;

    /**
     * 被保险人姓名
     */
    private String name;

    /**
     * 被保险人国籍
     */
    private String nativeplace;
    private String nativeplaceName;

    /**
     * 证件类型
     */
    private String idType;
    private String idTypeName;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 被保险人证件有效期
     */
    private String idTypeEndDate;

    /**
     * 性别
     */
    private String sex;
    private String sexName;

    /**
     * 出生日期
     */
    private String birthday;

    /**
     * 计划编码
     */
    private String planCode;

    /**
     * 增人生效日期
     */
    private String plusEffectDate;

    /**
     * 职业类别
     */
    private String jobType;
    private String jobTypeName;

    /**
     * 职业代码
     */
    private String jobCode;
    private String jobCodeName;

    /**
     * 是否拥有社保
     */
    private String medicareStatus;
    private String medicareStatusName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 与员工关系
     */
    private String relation;
    private String relationName;

    /**
     * 员工姓名
     */
    private String staffName;

    /**
     * 主被保人证件号
     */
    private String mainIdNo;

    /**
     * 主被保人证件类型
     */
    private String mainIdType;
    private String mainIdTypeName;

    /**
     * 缴费方式
     */
    private String accountType;
    private String accountTypeName;

    /**
     * 结算方式
     */
    private String premSource;
    private String premSourceName;

    /**
     * 试算保费
     */
    private String prem;

    /**
     * 是否有误
     */
    private String isError;

    /**
     * 错误信息
     */
    private String errorDesc;

}