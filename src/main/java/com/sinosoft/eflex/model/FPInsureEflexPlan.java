package com.sinosoft.eflex.model;

import java.util.Date;

public class FPInsureEflexPlan {
    /**
     * 个人投保编号
     */
    private String insureElfexPlanNo;

    /**
     * 福利编号
     */
    private String ensureCode;

    /**
     * 被保人ID
     */
    private String personId;

    /**
     * 主被保人客户号
     */
    private String perNo;

    /**
     * 投保年度
     */
    private String appntYear;

    /**
     * 投保险种
     */
    private String riskCode;
    
    /**
     * 险种类别
     */
    private String riskType;
    

    /**
     * 投保必选责任档次
     */
    private String amountGrageCode;

    /**
     * 免赔额属性
     */
    private String deductibleAttr;

    /**
     * 免赔额
     */
    private Double deductible;

    /**
     * 赔付比例
     */
    private Double compensationRatio;
    
    /**
     * 保费
     */
    private Double prem;

    /**
     * 投单状态  0-未提交订单表 1-已提交订单表 2-核心承保成功
     */
    private String insureState;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 管理机构
     */
    private String operatorCom;

    /**
     * 入机日期
     */
    private String makeDate;

    /**
     * 入机时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 个人投保编号
     * @return InsureElfexPlanNo 个人投保编号
     */
    public String getInsureElfexPlanNo() {
        return insureElfexPlanNo;
    }

    /**
     * 个人投保编号
     * @param insureElfexPlanNo 个人投保编号
     */
    public void setInsureElfexPlanNo(String insureElfexPlanNo) {
        this.insureElfexPlanNo = insureElfexPlanNo;
    }

    /**
     * 福利编号
     * @return EnsureCode 福利编号
     */
    public String getEnsureCode() {
        return ensureCode;
    }

    /**
     * 福利编号
     * @param ensureCode 福利编号
     */
    public void setEnsureCode(String ensureCode) {
        this.ensureCode = ensureCode;
    }

    /**
     * 被保人ID
     * @return PersonId 被保人ID
     */
    public String getPersonId() {
        return personId;
    }

    /**
     * 被保人ID
     * @param personId 被保人ID
     */
    public void setPersonId(String personId) {
        this.personId = personId;
    }

    /**
     * 主被保人客户号
     * @return PerNo 主被保人客户号
     */
    public String getPerNo() {
        return perNo;
    }

    /**
     * 主被保人客户号
     * @param perNo 主被保人客户号
     */
    public void setPerNo(String perNo) {
        this.perNo = perNo;
    }

    /**
     * 投保年度
     * @return AppntYear 投保年度
     */
    public String getAppntYear() {
        return appntYear;
    }

    /**
     * 投保年度
     * @param appntYear 投保年度
     */
    public void setAppntYear(String appntYear) {
        this.appntYear = appntYear;
    }

    /**
     * 投保险种
     * @return RiskCode 投保险种
     */
    public String getRiskCode() {
        return riskCode;
    }

    /**
     * 投保险种
     * @param riskCode 投保险种
     */
    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    /**
     * 投保必选责任档次
     * @return AmountGrageCode 投保必选责任档次
     */
    public String getAmountGrageCode() {
        return amountGrageCode;
    }

    /**
     * 投保必选责任档次
     * @param amountGrageCode 投保必选责任档次
     */
    public void setAmountGrageCode(String amountGrageCode) {
        this.amountGrageCode = amountGrageCode;
    }

    /**
     * 免赔额属性
     * @return DeductibleAttr 免赔额属性
     */
    public String getDeductibleAttr() {
        return deductibleAttr;
    }

    /**
     * 免赔额属性
     * @param deductibleAttr 免赔额属性
     */
    public void setDeductibleAttr(String deductibleAttr) {
        this.deductibleAttr = deductibleAttr;
    }

    /**
     * 免赔额
     * @return Deductible 免赔额
     */
    public Double getDeductible() {
        return deductible;
    }

    /**
     * 免赔额
     * @param deductible 免赔额
     */
    public void setDeductible(Double deductible) {
        this.deductible = deductible;
    }

    /**
     * 赔付比例
     * @return CompensationRatio 赔付比例
     */
    public Double getCompensationRatio() {
        return compensationRatio;
    }

    /**
     * 赔付比例
     * @param compensationRatio 赔付比例
     */
    public void setCompensationRatio(Double compensationRatio) {
        this.compensationRatio = compensationRatio;
    }

    /**
     * 投单状态  0-未提交订单表 1-已提交订单表 2-核心承保成功
     * @return InsureState 投单状态  0-未提交订单表 1-已提交订单表 2-核心承保成功
     */
    public String getInsureState() {
        return insureState;
    }

    /**
     * 投单状态  0-未提交订单表 1-已提交订单表 2-核心承保成功
     * @param insureState 投单状态  0-未提交订单表 1-已提交订单表 2-核心承保成功
     */
    public void setInsureState(String insureState) {
        this.insureState = insureState;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 管理机构
     * @return OperatorCom 管理机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 管理机构
     * @param operatorCom 管理机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 入机日期
     * @return MakeDate 入机日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 入机日期
     * @param makeDate 入机日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 入机时间
     * @return MakeTime 入机时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 入机时间
     * @param makeTime 入机时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

	public String getRiskType() {
		return riskType;
	}

	public void setRiskType(String riskType) {
		this.riskType = riskType;
	}

	public Double getPrem() {
		return prem;
	}

	public void setPrem(Double prem) {
		this.prem = prem;
	}
    
}