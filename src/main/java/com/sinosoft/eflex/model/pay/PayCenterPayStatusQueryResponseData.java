package com.sinosoft.eflex.model.pay;

public class PayCenterPayStatusQueryResponseData{
	private String channelCode;//渠道
	private String businessNo;//业务单号
	private String payTransactionNo;//支付交易号
	private Double payAmount;//支付金额
	private String payStatus;//支付状态
	private String payTime;//支付时间
	private String payResultMsg;//支付结果说明
	private String payType;//支付方式
	private String idCardType;//证件类型
	private String idCardNo;//证件号
	private String bankCode;//银行编码
	private String bankAccType;//银行账户类型
	private String bankAccNo;//银行账号
	private String bankAccName;//银行账户姓名
	private String bankAccPhone;//银行账户预留手机号
	public String getChannelCode() {
		return channelCode;
	}
	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}
	public String getBusinessNo() {
		return businessNo;
	}
	public void setBusinessNo(String businessNo) {
		this.businessNo = businessNo;
	}
	public String getPayTransactionNo() {
		return payTransactionNo;
	}
	public void setPayTransactionNo(String payTransactionNo) {
		this.payTransactionNo = payTransactionNo;
	}
	public Double getPayAmount() {
		return payAmount;
	}
	public void setPayAmount(Double payAmount) {
		this.payAmount = payAmount;
	}
	public String getPayStatus() {
		return payStatus;
	}
	public void setPayStatus(String payStatus) {
		this.payStatus = payStatus;
	}
	public String getPayTime() {
		return payTime;
	}
	public void setPayTime(String payTime) {
		this.payTime = payTime;
	}
	public String getPayResultMsg() {
		return payResultMsg;
	}
	public void setPayResultMsg(String payResultMsg) {
		this.payResultMsg = payResultMsg;
	}
	public String getPayType() {
		return payType;
	}
	public void setPayType(String payType) {
		this.payType = payType;
	}
	public String getIdCardType() {
		return idCardType;
	}
	public void setIdCardType(String idCardType) {
		this.idCardType = idCardType;
	}
	public String getIdCardNo() {
		return idCardNo;
	}
	public void setIdCardNo(String idCardNo) {
		this.idCardNo = idCardNo;
	}
	public String getBankCode() {
		return bankCode;
	}
	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}
	public String getBankAccType() {
		return bankAccType;
	}
	public void setBankAccType(String bankAccType) {
		this.bankAccType = bankAccType;
	}
	public String getBankAccNo() {
		return bankAccNo;
	}
	public void setBankAccNo(String bankAccNo) {
		this.bankAccNo = bankAccNo;
	}
	public String getBankAccName() {
		return bankAccName;
	}
	public void setBankAccName(String bankAccName) {
		this.bankAccName = bankAccName;
	}
	public String getBankAccPhone() {
		return bankAccPhone;
	}
	public void setBankAccPhone(String bankAccPhone) {
		this.bankAccPhone = bankAccPhone;
	}
}