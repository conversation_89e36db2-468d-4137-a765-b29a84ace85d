package com.sinosoft.eflex.model.pay;

public class PayCenterGetPayUrl {
    /**
     * 交易系统来源
     */
	private String transSource;
    /**
     * 交易编码
     */
	private String transCode;
    /**
     * 交易时间
     */
	private String transTime;
    /**
     * 交易流水号
     */
	private String transNo;
    /**
     * 支付接入类型
     */
	private String payKind;
    /**
     * 渠道
     */
	private String channelCode;
    /**
     * 业务单号
     */
	private String businessNo;
    /**
     * 业务描述
     */
	private String businessDesc;
    /**
     * 付款金额
     */
	private Double amount;
    /**
     * 页面回调地址
     */
	private String pageBackUrl;
    /**
     * 支付后台通知地址
     */
	private String dataBackUrl;
    /**
     * 客户姓名
     */
	private String customName;
    /**
     * 客户唯一标识
     */
	private String customId;
    /**
     * 证件类型
     */
	private String idCardType;
    /**
     * 证件号码
     */
	private String idCardNo;
    /**
     * 手机号
     */
	private String mobile;
    /**
     * 是否仅批扣方式标识true/false
     */
	private boolean onlyPkFlag;
    /**
     * 是否批扣方式可选true/false,仅批扣方式为true时，是否批扣方式可选必须为true
     */
	private boolean pkChooseFlag;
    /**
     * 页面主题风格 不传默认为blue
     */
	private String themeColor;
	/**
	 * 产品id
	 */
	private String productId;
	/**
	 * appid
	 */
	private String aid;
	/**
	 *机构id
	 */
	private String oid;
	/**
	 *页面当前流程id
	 */
	private String tid;
	/**
	 *缴费方式
	 */
	private String payFreq;


	public String getProductId() {
		return productId;
	}

	public void setProductId(String productId) {
		this.productId = productId;
	}

	public String getAid() {
		return aid;
	}

	public void setAid(String aid) {
		this.aid = aid;
	}

	public String getOid() {
		return oid;
	}

	public void setOid(String oid) {
		this.oid = oid;
	}

	public String getTid() {
		return tid;
	}

	public void setTid(String tid) {
		this.tid = tid;
	}

	public String getPayFreq() {
		return payFreq;
	}

	public void setPayFreq(String payFreq) {
		this.payFreq = payFreq;
	}

	public String getTransSource() {
		return transSource;
	}
	public void setTransSource(String transSource) {
		this.transSource = transSource;
	}
	public String getTransCode() {
		return transCode;
	}
	public void setTransCode(String transCode) {
		this.transCode = transCode;
	}
	public String getTransTime() {
		return transTime;
	}
	public void setTransTime(String transTime) {
		this.transTime = transTime;
	}
	public String getTransNo() {
		return transNo;
	}
	public void setTransNo(String transNo) {
		this.transNo = transNo;
	}
	public String getPayKind() {
		return payKind;
	}
	public void setPayKind(String payKind) {
		this.payKind = payKind;
	}
	public String getChannelCode() {
		return channelCode;
	}
	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}
	public String getBusinessNo() {
		return businessNo;
	}
	public void setBusinessNo(String businessNo) {
		this.businessNo = businessNo;
	}
	public Double getAmount() {
		return amount;
	}
	public void setAmount(Double amount) {
		this.amount = amount;
	}
	public String getPageBackUrl() {
		return pageBackUrl;
	}
	public void setPageBackUrl(String pageBackUrl) {
		this.pageBackUrl = pageBackUrl;
	}
	public String getDataBackUrl() {
		return dataBackUrl;
	}
	public void setDataBackUrl(String dataBackUrl) {
		this.dataBackUrl = dataBackUrl;
	}
	public String getCustomName() {
		return customName;
	}
	public void setCustomName(String customName) {
		this.customName = customName;
	}
	public String getIdCardType() {
		return idCardType;
	}
	public void setIdCardType(String idCardType) {
		this.idCardType = idCardType;
	}
	public String getIdCardNo() {
		return idCardNo;
	}
	public void setIdCardNo(String idCardNo) {
		this.idCardNo = idCardNo;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getBusinessDesc() {
		return businessDesc;
	}
	public void setBusinessDesc(String businessDesc) {
		this.businessDesc = businessDesc;
	}
	public String getCustomId() {
		return customId;
	}
	public void setCustomId(String customId) {
		this.customId = customId;
	}
	public boolean isOnlyPkFlag() {
		return onlyPkFlag;
	}
	public void setOnlyPkFlag(boolean onlyPkFlag) {
		this.onlyPkFlag = onlyPkFlag;
	}
	public boolean isPkChooseFlag() {
		return pkChooseFlag;
	}
	public void setPkChooseFlag(boolean pkChooseFlag) {
		this.pkChooseFlag = pkChooseFlag;
	}
	public String getThemeColor() {
		return themeColor;
	}
	public void setThemeColor(String themeColor) {
		this.themeColor = themeColor;
	}
}
