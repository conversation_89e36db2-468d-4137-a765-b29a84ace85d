package com.sinosoft.eflex.model.datamanage;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2021/11/9
 * @desc 查询福利信息
 */
@Data
public class SelectEnsureInfoListReq {

    /**
     * 企业名称
     */
    private String grpName;

    /**
     * 企业证件类型
     */
    private String grpIdType;

    /**
     * 企业证件号
     */
    private String grpIdNo;

    /**
     * 福利类型
     */
    private String ensureType;

    /**
     *
     * 福利编码
     */
    private String ensureCode;

    /**
     * 福利名称
     */
    private String ensureName;

    /**
     * 福利状态
     */
    private String ensureState;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 页面条数
     */
    private Integer pageSize;

    /**
     * 管理机构
     */
    private String manageCom;

    /**
     * 计划类型
     */
    private String planType;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

}
