package com.sinosoft.eflex.model.datamanage;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2021/8/4
 * @desc 获取福利投保相关信息请求参数
 */
@Data
public class GetEnsureInsureDataReq {

    /**
     * 企业名称
     */
    private String grpName;

    /**
     * 企业证件类型
     */
    private String grpIdType;

    /**
     * 企业证件号
     */
    private String grpIdNo;

    /**
     * 计划类型
     */
    private String planType;

    /**
     * 福利状态
     */
    private String ensureState;

    /**
     * 福利名称
     */
    private String ensureName;

    /**
     * 福利编码
     */
    private String ensureCode;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String  endTime;

    /**
     * 管理机构
     */
    private String manageCom;

    /**
     * 勾选的福利编码
     */
    private List<String> ensureCodeList;

}
