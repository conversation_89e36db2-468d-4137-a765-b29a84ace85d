package com.sinosoft.eflex.model;

import java.math.BigDecimal;
import java.util.Date;

public class FDAgentInfo {
    /**
     * 流水号码
     */
    private String serialNo;

    /**
     * 代理人编码
     */
    private String agentCode;

    /**
     * 
     */
    private String agentGroup;

    /**
     * 
     */
    private String manageCom;

    /**
     * 
     */
    private String password;

    /**
     * --新版基???法没有??到
     */
    private String entryNo;

    /**
     * 
     */
    private String name;

    /**
     * 
     */
    private String sex;

    /**
     * 
     */
    private Date birthday;

    /**
     * 
     */
    private String nativePlace;

    /**
     * 
     */
    private String nationality;

    /**
     * 
     */
    private String marriage;

    /**
     * 
     */
    private String creditGrade;

    /**
     * 
     */
    private String homeAddressCode;

    /**
     * 
     */
    private String homeAddress;

    /**
     * 
     */
    private String postalAddress;

    /**
     * 
     */
    private String zipCode;

    /**
     * 
     */
    private String phone;

    /**
     * 
     */
    private String BP;

    /**
     * 
     */
    private String mobile;

    /**
     * 
     */
    private String EMail;

    /**
     * 
     */
    private Date marriageDate;

    /**
     * 
     */
    private String IDNo;

    /**
     * 来源地
     */
    private String source;

    /**
     * A/B/AB/O
     */
    private String bloodType;

    /**
     * 
     */
    private String polityVisage;

    /**
     * 
     */
    private String degree;

    /**
     * 
     */
    private String graduateSchool;

    /**
     * 
     */
    private String speciality;

    /**
     * 
     */
    private String postTitle;

    /**
     * 
     */
    private String foreignLevel;

    /**
     * 
     */
    private Short workAge;

    /**
     * 
     */
    private String oldCom;

    /**
     * 
     */
    private String oldOccupation;

    /**
     * 
     */
    private String headShip;

    /**
     * 
     */
    private String recommendAgent;

    /**
     * 
     */
    private String business;

    /**
     * Y/N
     */
    private String saleQuaf;

    /**
     * 
     */
    private String quafNo;

    /**
     * 
     */
    private Date quafStartDate;

    /**
     * 
     */
    private Date quafEndDate;

    /**
     * 
     */
    private String devNo1;

    /**
     * 
     */
    private String devNo2;

    /**
     * 
     */
    private String retainContNo;

    /**
     * 01-银代经理
            02-银代协理
            03-渠道经理
            04-银代客户经理
            05-培训岗
            
            06-企划策划岗
            
            07-销售支援岗
            08-综合岗
            
            
            
            10-普通代理人
            11-组主管
            
            12-部主管
            
            13-督导长
            
            14-区域督导长
            
            
            
            20-法人客户经理
            21-法人组经理
            
            22-法人协理
            23－法人部经理
            24-培训岗
            
            25-企划策划岗
            
            26-销售支援岗
            27-综合岗
            
            
     */
    private String agentKind;

    /**
     * 
     */
    private String devGrade;

    /**
     * 0-内勤
            1-外勤
     */
    private String insideFlag;

    /**
     * 0 --专职
            1 --兼职
            
            
     */
    private String fullTimeFlag;

    /**
     * 0 --没有待业证
            
            1 --有待业证
            
     */
    private String noWorkFlag;

    /**
     * 
     */
    private Date trainDate;

    /**
     * --入司日期
     */
    private Date employDate;

    /**
     * 不可修改，由行政信息提供
            A01->A02
     */
    private Date inDueFormDate;

    /**
     * 不可修改，由行政信息提供
            离职减员或考核清退置CurrentDate
     */
    private Date outWorkDate;

    /**
     * 推荐名编号2
     */
    private String recommendNo;

    /**
     * 
     */
    private String cautionerName;

    /**
     * 
     */
    private String cautionerSex;

    /**
     * 
     */
    private String cautionerID;

    /**
     * 
     */
    private Date cautionerBirthday;

    /**
     * 
     */
    private String approver;

    /**
     * 
     */
    private Date approveDate;

    /**
     * 
     */
    private BigDecimal assuMoney;

    /**
     * 
     */
    private String remark;

    /**
     * 代理人状态(01－增员，02－二次增员，03－离职,04-二次离职,05-清退）
     */
    private String agentState;

    /**
     * 0 or null-档案未调入
            
            1-档案调入。
     */
    private String qualiPassFlag;

    /**
     * 0,null-打折A01
            1-不打折
     */
    private String smokeFlag;

    /**
     * 
     */
    private String rgtAddress;

    /**
     * 
     */
    private String bankCode;

    /**
     * 
     */
    private String bankAccNo;

    /**
     * 
     */
    private String operator;

    /**
     * 
     */
    private Date makeDate;

    /**
     * 
     */
    private String makeTime;

    /**
     * 
     */
    private Date modifyDate;

    /**
     * 
     */
    private String modifyTime;

    /**
     * 展业类型(1-个人营销，2-团险，3－银行保险，4-续收员.5-续收督导,6-联办代理,7-新中介,9-中介续收督导)
     */
    private String branchType;

    /**
     * 
     */
    private String trainPeriods;

    /**
     * ---??险：直辖组编码labranchgroup.agentgroup
     */
    private String branchCode;

    /**
     * 
     */
    private Integer age;

    /**
     * 
     */
    private String channelName;

    /**
     * 
     */
    private String IDType;

    /**
     * 展业类型(1-个人营销，2-团险，3－银行保险，4-续收员.5-续收督导,6-联办代理,7-新中介,9-中介续???督导)
     */
    private String branchType2;

    /**
     * 
     */
    private String receiptNo;

    /**
     * 资格证编码
     */
    private String qualifNo;

    /**
     * 荣誉
     */
    private String honor;

    /**
     * 推送标志 0 - 未推送 1- 推送成功
     */
    private String isFlag;

    /**
     * 
     */
    private String isFlag1;

    /**
     * 
     */
    private String isFlag2;

    /**
     * 流水号码
     * @return SerialNo 流水号码
     */
    public String getSerialNo() {
        return serialNo;
    }

    /**
     * 流水号码
     * @param serialNo 流水号码
     */
    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    /**
     * 代理人编码
     * @return AgentCode 代理人编码
     */
    public String getAgentCode() {
        return agentCode;
    }

    /**
     * 代理人编码
     * @param agentCode 代理人编码
     */
    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    /**
     * 
     * @return AgentGroup 
     */
    public String getAgentGroup() {
        return agentGroup;
    }

    /**
     * 
     * @param agentGroup 
     */
    public void setAgentGroup(String agentGroup) {
        this.agentGroup = agentGroup;
    }

    /**
     * 
     * @return ManageCom 
     */
    public String getManageCom() {
        return manageCom;
    }

    /**
     * 
     * @param manageCom 
     */
    public void setManageCom(String manageCom) {
        this.manageCom = manageCom;
    }

    /**
     * 
     * @return Password 
     */
    public String getPassword() {
        return password;
    }

    /**
     * 
     * @param password 
     */
    public void setPassword(String password) {
        this.password = password;
    }

    /**
     * --新版基???法没有??到
     * @return EntryNo --新版基???法没有??到
     */
    public String getEntryNo() {
        return entryNo;
    }

    /**
     * --新版基???法没有??到
     * @param entryNo --新版基???法没有??到
     */
    public void setEntryNo(String entryNo) {
        this.entryNo = entryNo;
    }

    /**
     * 
     * @return Name 
     */
    public String getName() {
        return name;
    }

    /**
     * 
     * @param name 
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 
     * @return Sex 
     */
    public String getSex() {
        return sex;
    }

    /**
     * 
     * @param sex 
     */
    public void setSex(String sex) {
        this.sex = sex;
    }

    /**
     * 
     * @return Birthday 
     */
    public Date getBirthday() {
        return birthday;
    }

    /**
     * 
     * @param birthday 
     */
    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    /**
     * 
     * @return NativePlace 
     */
    public String getNativePlace() {
        return nativePlace;
    }

    /**
     * 
     * @param nativePlace 
     */
    public void setNativePlace(String nativePlace) {
        this.nativePlace = nativePlace;
    }

    /**
     * 
     * @return Nationality 
     */
    public String getNationality() {
        return nationality;
    }

    /**
     * 
     * @param nationality 
     */
    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    /**
     * 
     * @return Marriage 
     */
    public String getMarriage() {
        return marriage;
    }

    /**
     * 
     * @param marriage 
     */
    public void setMarriage(String marriage) {
        this.marriage = marriage;
    }

    /**
     * 
     * @return CreditGrade 
     */
    public String getCreditGrade() {
        return creditGrade;
    }

    /**
     * 
     * @param creditGrade 
     */
    public void setCreditGrade(String creditGrade) {
        this.creditGrade = creditGrade;
    }

    /**
     * 
     * @return HomeAddressCode 
     */
    public String getHomeAddressCode() {
        return homeAddressCode;
    }

    /**
     * 
     * @param homeAddressCode 
     */
    public void setHomeAddressCode(String homeAddressCode) {
        this.homeAddressCode = homeAddressCode;
    }

    /**
     * 
     * @return HomeAddress 
     */
    public String getHomeAddress() {
        return homeAddress;
    }

    /**
     * 
     * @param homeAddress 
     */
    public void setHomeAddress(String homeAddress) {
        this.homeAddress = homeAddress;
    }

    /**
     * 
     * @return PostalAddress 
     */
    public String getPostalAddress() {
        return postalAddress;
    }

    /**
     * 
     * @param postalAddress 
     */
    public void setPostalAddress(String postalAddress) {
        this.postalAddress = postalAddress;
    }

    /**
     * 
     * @return ZipCode 
     */
    public String getZipCode() {
        return zipCode;
    }

    /**
     * 
     * @param zipCode 
     */
    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    /**
     * 
     * @return Phone 
     */
    public String getPhone() {
        return phone;
    }

    /**
     * 
     * @param phone 
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * 
     * @return BP 
     */
    public String getBP() {
        return BP;
    }

    /**
     * 
     * @param BP 
     */
    public void setBP(String BP) {
        this.BP = BP;
    }

    /**
     * 
     * @return Mobile 
     */
    public String getMobile() {
        return mobile;
    }

    /**
     * 
     * @param mobile 
     */
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    /**
     * 
     * @return EMail 
     */
    public String getEMail() {
        return EMail;
    }

    /**
     * 
     * @param EMail 
     */
    public void setEMail(String EMail) {
        this.EMail = EMail;
    }

    /**
     * 
     * @return MarriageDate 
     */
    public Date getMarriageDate() {
        return marriageDate;
    }

    /**
     * 
     * @param marriageDate 
     */
    public void setMarriageDate(Date marriageDate) {
        this.marriageDate = marriageDate;
    }

    /**
     * 
     * @return IDNo 
     */
    public String getIDNo() {
        return IDNo;
    }

    /**
     * 
     * @param IDNo 
     */
    public void setIDNo(String IDNo) {
        this.IDNo = IDNo;
    }

    /**
     * 来源地
     * @return Source 来源地
     */
    public String getSource() {
        return source;
    }

    /**
     * 来源地
     * @param source 来源地
     */
    public void setSource(String source) {
        this.source = source;
    }

    /**
     * A/B/AB/O
     * @return BloodType A/B/AB/O
     */
    public String getBloodType() {
        return bloodType;
    }

    /**
     * A/B/AB/O
     * @param bloodType A/B/AB/O
     */
    public void setBloodType(String bloodType) {
        this.bloodType = bloodType;
    }

    /**
     * 
     * @return PolityVisage 
     */
    public String getPolityVisage() {
        return polityVisage;
    }

    /**
     * 
     * @param polityVisage 
     */
    public void setPolityVisage(String polityVisage) {
        this.polityVisage = polityVisage;
    }

    /**
     * 
     * @return Degree 
     */
    public String getDegree() {
        return degree;
    }

    /**
     * 
     * @param degree 
     */
    public void setDegree(String degree) {
        this.degree = degree;
    }

    /**
     * 
     * @return GraduateSchool 
     */
    public String getGraduateSchool() {
        return graduateSchool;
    }

    /**
     * 
     * @param graduateSchool 
     */
    public void setGraduateSchool(String graduateSchool) {
        this.graduateSchool = graduateSchool;
    }

    /**
     * 
     * @return Speciality 
     */
    public String getSpeciality() {
        return speciality;
    }

    /**
     * 
     * @param speciality 
     */
    public void setSpeciality(String speciality) {
        this.speciality = speciality;
    }

    /**
     * 
     * @return PostTitle 
     */
    public String getPostTitle() {
        return postTitle;
    }

    /**
     * 
     * @param postTitle 
     */
    public void setPostTitle(String postTitle) {
        this.postTitle = postTitle;
    }

    /**
     * 
     * @return ForeignLevel 
     */
    public String getForeignLevel() {
        return foreignLevel;
    }

    /**
     * 
     * @param foreignLevel 
     */
    public void setForeignLevel(String foreignLevel) {
        this.foreignLevel = foreignLevel;
    }

    /**
     * 
     * @return WorkAge 
     */
    public Short getWorkAge() {
        return workAge;
    }

    /**
     * 
     * @param workAge 
     */
    public void setWorkAge(Short workAge) {
        this.workAge = workAge;
    }

    /**
     * 
     * @return OldCom 
     */
    public String getOldCom() {
        return oldCom;
    }

    /**
     * 
     * @param oldCom 
     */
    public void setOldCom(String oldCom) {
        this.oldCom = oldCom;
    }

    /**
     * 
     * @return OldOccupation 
     */
    public String getOldOccupation() {
        return oldOccupation;
    }

    /**
     * 
     * @param oldOccupation 
     */
    public void setOldOccupation(String oldOccupation) {
        this.oldOccupation = oldOccupation;
    }

    /**
     * 
     * @return HeadShip 
     */
    public String getHeadShip() {
        return headShip;
    }

    /**
     * 
     * @param headShip 
     */
    public void setHeadShip(String headShip) {
        this.headShip = headShip;
    }

    /**
     * 
     * @return RecommendAgent 
     */
    public String getRecommendAgent() {
        return recommendAgent;
    }

    /**
     * 
     * @param recommendAgent 
     */
    public void setRecommendAgent(String recommendAgent) {
        this.recommendAgent = recommendAgent;
    }

    /**
     * 
     * @return Business 
     */
    public String getBusiness() {
        return business;
    }

    /**
     * 
     * @param business 
     */
    public void setBusiness(String business) {
        this.business = business;
    }

    /**
     * Y/N
     * @return SaleQuaf Y/N
     */
    public String getSaleQuaf() {
        return saleQuaf;
    }

    /**
     * Y/N
     * @param saleQuaf Y/N
     */
    public void setSaleQuaf(String saleQuaf) {
        this.saleQuaf = saleQuaf;
    }

    /**
     * 
     * @return QuafNo 
     */
    public String getQuafNo() {
        return quafNo;
    }

    /**
     * 
     * @param quafNo 
     */
    public void setQuafNo(String quafNo) {
        this.quafNo = quafNo;
    }

    /**
     * 
     * @return QuafStartDate 
     */
    public Date getQuafStartDate() {
        return quafStartDate;
    }

    /**
     * 
     * @param quafStartDate 
     */
    public void setQuafStartDate(Date quafStartDate) {
        this.quafStartDate = quafStartDate;
    }

    /**
     * 
     * @return QuafEndDate 
     */
    public Date getQuafEndDate() {
        return quafEndDate;
    }

    /**
     * 
     * @param quafEndDate 
     */
    public void setQuafEndDate(Date quafEndDate) {
        this.quafEndDate = quafEndDate;
    }

    /**
     * 
     * @return DevNo1 
     */
    public String getDevNo1() {
        return devNo1;
    }

    /**
     * 
     * @param devNo1 
     */
    public void setDevNo1(String devNo1) {
        this.devNo1 = devNo1;
    }

    /**
     * 
     * @return DevNo2 
     */
    public String getDevNo2() {
        return devNo2;
    }

    /**
     * 
     * @param devNo2 
     */
    public void setDevNo2(String devNo2) {
        this.devNo2 = devNo2;
    }

    /**
     * 
     * @return RetainContNo 
     */
    public String getRetainContNo() {
        return retainContNo;
    }

    /**
     * 
     * @param retainContNo 
     */
    public void setRetainContNo(String retainContNo) {
        this.retainContNo = retainContNo;
    }

    /**
     * 01-银代经理
            02-银代协理
            03-渠道经理
            04-银代客户经理
            05-培训岗
            
            06-企划策划岗
            
            07-销售支援岗
            08-综合岗
            
            
            
            10-普通代理人
            11-组主管
            
            12-部主管
            
            13-督导长
            
            14-区域督导长
            
            
            
            20-法人客户经理
            21-法人组经理
            
            22-法人协理
            23－法人部经理
            24-培训岗
            
            25-企划策划岗
            
            26-销售支援岗
            27-综合岗
            
            
     * @return AgentKind 01-银代经理
            02-银代协理
            03-渠道经理
            04-银代客户经理
            05-培训岗
            
            06-企划策划岗
            
            07-销售支援岗
            08-综合岗
            
            
            
            10-普通代理人
            11-组主管
            
            12-部主管
            
            13-督导长
            
            14-区域督导长
            
            
            
            20-法人客户经理
            21-法人组经理
            
            22-法人协理
            23－法人部经理
            24-培训岗
            
            25-企划策划岗
            
            26-销售支援岗
            27-综合岗
            
            
     */
    public String getAgentKind() {
        return agentKind;
    }

    /**
     * 01-银代经理
            02-银代协理
            03-渠道经理
            04-银代客户经理
            05-培训岗
            
            06-企划策划岗
            
            07-销售支援岗
            08-综合岗
            
            
            
            10-普通代理人
            11-组主管
            
            12-部主管
            
            13-督导长
            
            14-区域督导长
            
            
            
            20-法人客户经理
            21-法人组经理
            
            22-法人协理
            23－法人部经理
            24-培训岗
            
            25-企划策划岗
            
            26-销售支援岗
            27-综合岗
            
            
     * @param agentKind 01-银代经理
            02-银代协理
            03-渠道经理
            04-银代客户经理
            05-培训岗
            
            06-企划策划岗
            
            07-销售支援岗
            08-综合岗
            
            
            
            10-普通代理人
            11-组主管
            
            12-部主管
            
            13-督导长
            
            14-区域督导长
            
            
            
            20-法人客户经理
            21-法人组经理
            
            22-法人协理
            23－法人部经理
            24-培训岗
            
            25-企划策划岗
            
            26-销售支援岗
            27-综合岗
            
            
     */
    public void setAgentKind(String agentKind) {
        this.agentKind = agentKind;
    }

    /**
     * 
     * @return DevGrade 
     */
    public String getDevGrade() {
        return devGrade;
    }

    /**
     * 
     * @param devGrade 
     */
    public void setDevGrade(String devGrade) {
        this.devGrade = devGrade;
    }

    /**
     * 0-内勤
            1-外勤
     * @return InsideFlag 0-内勤
            1-外勤
     */
    public String getInsideFlag() {
        return insideFlag;
    }

    /**
     * 0-内勤
            1-外勤
     * @param insideFlag 0-内勤
            1-外勤
     */
    public void setInsideFlag(String insideFlag) {
        this.insideFlag = insideFlag;
    }

    /**
     * 0 --专职
            1 --兼职
            
            
     * @return FullTimeFlag 0 --专职
            1 --兼职
            
            
     */
    public String getFullTimeFlag() {
        return fullTimeFlag;
    }

    /**
     * 0 --专职
            1 --兼职
            
            
     * @param fullTimeFlag 0 --专职
            1 --兼职
            
            
     */
    public void setFullTimeFlag(String fullTimeFlag) {
        this.fullTimeFlag = fullTimeFlag;
    }

    /**
     * 0 --没有待业证
            
            1 --有待业证
            
     * @return NoWorkFlag 0 --没有待业证
            
            1 --有待业证
            
     */
    public String getNoWorkFlag() {
        return noWorkFlag;
    }

    /**
     * 0 --没有待业证
            
            1 --有待业证
            
     * @param noWorkFlag 0 --没有待业证
            
            1 --有待业证
            
     */
    public void setNoWorkFlag(String noWorkFlag) {
        this.noWorkFlag = noWorkFlag;
    }

    /**
     * 
     * @return TrainDate 
     */
    public Date getTrainDate() {
        return trainDate;
    }

    /**
     * 
     * @param trainDate 
     */
    public void setTrainDate(Date trainDate) {
        this.trainDate = trainDate;
    }

    /**
     * --入司日期
     * @return EmployDate --入司日期
     */
    public Date getEmployDate() {
        return employDate;
    }

    /**
     * --入司日期
     * @param employDate --入司日期
     */
    public void setEmployDate(Date employDate) {
        this.employDate = employDate;
    }

    /**
     * 不可修改，由行政信息提供
            A01->A02
     * @return InDueFormDate 不可修改，由行政信息提供
            A01->A02
     */
    public Date getInDueFormDate() {
        return inDueFormDate;
    }

    /**
     * 不可修改，由行政信息提供
            A01->A02
     * @param inDueFormDate 不可修改，由行政信息提供
            A01->A02
     */
    public void setInDueFormDate(Date inDueFormDate) {
        this.inDueFormDate = inDueFormDate;
    }

    /**
     * 不可修改，由行政信息提供
            离职减员或考核清退置CurrentDate
     * @return OutWorkDate 不可修改，由行政信息提供
            离职减员或考核清退置CurrentDate
     */
    public Date getOutWorkDate() {
        return outWorkDate;
    }

    /**
     * 不可修改，由行政信息提供
            离职减员或考核清退置CurrentDate
     * @param outWorkDate 不可修改，由行政信息提供
            离职减员或考核清退置CurrentDate
     */
    public void setOutWorkDate(Date outWorkDate) {
        this.outWorkDate = outWorkDate;
    }

    /**
     * 推荐名编号2
     * @return RecommendNo 推荐名编号2
     */
    public String getRecommendNo() {
        return recommendNo;
    }

    /**
     * 推荐名编号2
     * @param recommendNo 推荐名编号2
     */
    public void setRecommendNo(String recommendNo) {
        this.recommendNo = recommendNo;
    }

    /**
     * 
     * @return CautionerName 
     */
    public String getCautionerName() {
        return cautionerName;
    }

    /**
     * 
     * @param cautionerName 
     */
    public void setCautionerName(String cautionerName) {
        this.cautionerName = cautionerName;
    }

    /**
     * 
     * @return CautionerSex 
     */
    public String getCautionerSex() {
        return cautionerSex;
    }

    /**
     * 
     * @param cautionerSex 
     */
    public void setCautionerSex(String cautionerSex) {
        this.cautionerSex = cautionerSex;
    }

    /**
     * 
     * @return CautionerID 
     */
    public String getCautionerID() {
        return cautionerID;
    }

    /**
     * 
     * @param cautionerID 
     */
    public void setCautionerID(String cautionerID) {
        this.cautionerID = cautionerID;
    }

    /**
     * 
     * @return CautionerBirthday 
     */
    public Date getCautionerBirthday() {
        return cautionerBirthday;
    }

    /**
     * 
     * @param cautionerBirthday 
     */
    public void setCautionerBirthday(Date cautionerBirthday) {
        this.cautionerBirthday = cautionerBirthday;
    }

    /**
     * 
     * @return Approver 
     */
    public String getApprover() {
        return approver;
    }

    /**
     * 
     * @param approver 
     */
    public void setApprover(String approver) {
        this.approver = approver;
    }

    /**
     * 
     * @return ApproveDate 
     */
    public Date getApproveDate() {
        return approveDate;
    }

    /**
     * 
     * @param approveDate 
     */
    public void setApproveDate(Date approveDate) {
        this.approveDate = approveDate;
    }

    /**
     * 
     * @return AssuMoney 
     */
    public BigDecimal getAssuMoney() {
        return assuMoney;
    }

    /**
     * 
     * @param assuMoney 
     */
    public void setAssuMoney(BigDecimal assuMoney) {
        this.assuMoney = assuMoney;
    }

    /**
     * 
     * @return Remark 
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 
     * @param remark 
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 代理人状态(01－增员，02－二次增员，03－离职,04-二次离职,05-清退）
     * @return AgentState 代理人状态(01－增员，02－二次增员，03－离职,04-二次离职,05-清退）
     */
    public String getAgentState() {
        return agentState;
    }

    /**
     * 代理人状态(01－增员，02－二次增员，03－离职,04-二次离职,05-清退）
     * @param agentState 代理人状态(01－增员，02－二次增员，03－离职,04-二次离职,05-清退）
     */
    public void setAgentState(String agentState) {
        this.agentState = agentState;
    }

    /**
     * 0 or null-档案未调入
            
            1-档案调入。
     * @return QualiPassFlag 0 or null-档案未调入
            
            1-档案调入。
     */
    public String getQualiPassFlag() {
        return qualiPassFlag;
    }

    /**
     * 0 or null-档案未调入
            
            1-档案调入。
     * @param qualiPassFlag 0 or null-档案未调入
            
            1-档案调入。
     */
    public void setQualiPassFlag(String qualiPassFlag) {
        this.qualiPassFlag = qualiPassFlag;
    }

    /**
     * 0,null-打折A01
            1-不打折
     * @return SmokeFlag 0,null-打折A01
            1-不打折
     */
    public String getSmokeFlag() {
        return smokeFlag;
    }

    /**
     * 0,null-打折A01
            1-不打折
     * @param smokeFlag 0,null-打折A01
            1-不打折
     */
    public void setSmokeFlag(String smokeFlag) {
        this.smokeFlag = smokeFlag;
    }

    /**
     * 
     * @return RgtAddress 
     */
    public String getRgtAddress() {
        return rgtAddress;
    }

    /**
     * 
     * @param rgtAddress 
     */
    public void setRgtAddress(String rgtAddress) {
        this.rgtAddress = rgtAddress;
    }

    /**
     * 
     * @return BankCode 
     */
    public String getBankCode() {
        return bankCode;
    }

    /**
     * 
     * @param bankCode 
     */
    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    /**
     * 
     * @return BankAccNo 
     */
    public String getBankAccNo() {
        return bankAccNo;
    }

    /**
     * 
     * @param bankAccNo 
     */
    public void setBankAccNo(String bankAccNo) {
        this.bankAccNo = bankAccNo;
    }

    /**
     * 
     * @return Operator 
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 
     * @param operator 
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 
     * @return MakeDate 
     */
    public Date getMakeDate() {
        return makeDate;
    }

    /**
     * 
     * @param makeDate 
     */
    public void setMakeDate(Date makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 
     * @return MakeTime 
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 
     * @param makeTime 
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 
     * @return ModifyDate 
     */
    public Date getModifyDate() {
        return modifyDate;
    }

    /**
     * 
     * @param modifyDate 
     */
    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 
     * @return ModifyTime 
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 
     * @param modifyTime 
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    /**
     * 展业类型(1-个人营销，2-团险，3－银行保险，4-续收员.5-续收督导,6-联办代理,7-新中介,9-中介续收督导)
     * @return BranchType 展业类型(1-个人营销，2-团险，3－银行保险，4-续收员.5-续收督导,6-联办代理,7-新中介,9-中介续收督导)
     */
    public String getBranchType() {
        return branchType;
    }

    /**
     * 展业类型(1-个人营销，2-团险，3－银行保险，4-续收员.5-续收督导,6-联办代理,7-新中介,9-中介续收督导)
     * @param branchType 展业类型(1-个人营销，2-团险，3－银行保险，4-续收员.5-续收督导,6-联办代理,7-新中介,9-中介续收督导)
     */
    public void setBranchType(String branchType) {
        this.branchType = branchType;
    }

    /**
     * 
     * @return TrainPeriods 
     */
    public String getTrainPeriods() {
        return trainPeriods;
    }

    /**
     * 
     * @param trainPeriods 
     */
    public void setTrainPeriods(String trainPeriods) {
        this.trainPeriods = trainPeriods;
    }

    /**
     * ---??险：直辖组编码labranchgroup.agentgroup
     * @return BranchCode ---??险：直辖组编码labranchgroup.agentgroup
     */
    public String getBranchCode() {
        return branchCode;
    }

    /**
     * ---??险：直辖组编码labranchgroup.agentgroup
     * @param branchCode ---??险：直辖组编码labranchgroup.agentgroup
     */
    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    /**
     * 
     * @return Age 
     */
    public Integer getAge() {
        return age;
    }

    /**
     * 
     * @param age 
     */
    public void setAge(Integer age) {
        this.age = age;
    }

    /**
     * 
     * @return ChannelName 
     */
    public String getChannelName() {
        return channelName;
    }

    /**
     * 
     * @param channelName 
     */
    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    /**
     * 
     * @return IDType 
     */
    public String getIDType() {
        return IDType;
    }

    /**
     * 
     * @param IDType 
     */
    public void setIDType(String IDType) {
        this.IDType = IDType;
    }

    /**
     * 展业类型(1-个人营销，2-团险，3－银行保险，4-续收员.5-续收督导,6-联办代理,7-新中介,9-中介续???督导)
     * @return BranchType2 展业类型(1-个人营销，2-团险，3－银行保险，4-续收员.5-续收督导,6-联办代理,7-新中介,9-中介续???督导)
     */
    public String getBranchType2() {
        return branchType2;
    }

    /**
     * 展业类型(1-个人营销，2-团险，3－银行保险，4-续收员.5-续收督导,6-联办代理,7-新中介,9-中介续???督导)
     * @param branchType2 展业类型(1-个人营销，2-团险，3－银行保险，4-续收员.5-续收督导,6-联办代理,7-新中介,9-中介续???督导)
     */
    public void setBranchType2(String branchType2) {
        this.branchType2 = branchType2;
    }

    /**
     * 
     * @return ReceiptNo 
     */
    public String getReceiptNo() {
        return receiptNo;
    }

    /**
     * 
     * @param receiptNo 
     */
    public void setReceiptNo(String receiptNo) {
        this.receiptNo = receiptNo;
    }

    /**
     * 资格证编码
     * @return QualifNo 资格证编码
     */
    public String getQualifNo() {
        return qualifNo;
    }

    /**
     * 资格证编码
     * @param qualifNo 资格证编码
     */
    public void setQualifNo(String qualifNo) {
        this.qualifNo = qualifNo;
    }

    /**
     * 荣誉
     * @return Honor 荣誉
     */
    public String getHonor() {
        return honor;
    }

    /**
     * 荣誉
     * @param honor 荣誉
     */
    public void setHonor(String honor) {
        this.honor = honor;
    }

    /**
     * 推送标志 0 - 未推送 1- 推送成功
     * @return IsFlag 推送标志 0 - 未推送 1- 推送成功
     */
    public String getIsFlag() {
        return isFlag;
    }

    /**
     * 推送标志 0 - 未推送 1- 推送成功
     * @param isFlag 推送标志 0 - 未推送 1- 推送成功
     */
    public void setIsFlag(String isFlag) {
        this.isFlag = isFlag;
    }

    /**
     * 
     * @return IsFlag1 
     */
    public String getIsFlag1() {
        return isFlag1;
    }

    /**
     * 
     * @param isFlag1 
     */
    public void setIsFlag1(String isFlag1) {
        this.isFlag1 = isFlag1;
    }

    /**
     * 
     * @return IsFlag2 
     */
    public String getIsFlag2() {
        return isFlag2;
    }

    /**
     * 
     * @param isFlag2 
     */
    public void setIsFlag2(String isFlag2) {
        this.isFlag2 = isFlag2;
    }
}