package com.sinosoft.eflex.model;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 企业弹性福利保障表实体类
 */
@Data
@ToString(callSuper = true)
public class FCEnsure implements Serializable {

    /**
     * 福利编号
     */
    private String ensureCode;

    /**
     * 企业客户号
     */
    private String grpNo;

    /**
     * 福利名称
     */
    private String ensureName;

    /**
     * 福利保障申请时间
     */
    private String appDate;

    /**
     * 业务员工号
     */
    private String clientNo;

    /**
     * 业务员工号
     */
    private String clientName;

    /**
     * 二级渠道
     */
    private String channel;

    /**
     * 中介机构code
     */
    private String intermediaryOrganCode;

    /**
     * 中介机构name
     */
    private String intermediaryOrganName;

    /**
     * 是否绿色保险
     */
    private Boolean greenInsurance;

    /**
     * 被续保保单编号
     */
    private String reNewGrpContNo;

    /**
     * 福利状态 参考ensureStateEnum
     */
    private String ensureState;

    /**
     * 福利状态
     */
    private String codeName;

    /**
     * 投保年度
     */
    private String appntYear;

    /**
     * 保险期间类型 1-极短期，2-一年期
     */
    private String insuredPeriodType;

    /**
     * 保险期间
     */
    private String insuredPeriod;

    /**
     * 付款方式，todo 这个表下面已经有payType了，为什么还需要payMode。
     */
    private String payMode;

    /**
     * 保单生效日
     */
    private String cvaliDate;

    /**
     * 保单截止日
     */
    private String policyEndDate;

    /**
     * 投保人数
     */
    private Integer insuredNumber;

    /**
     * 总保费
     */
    private Double totalPrem;

    /**
     * 投保开放日期
     */
    private String startAppntDate;

    /**
     * 投保结束日期
     */
    private String endAppntDate;

    /**
     * 保单状态 1-投保中 2-待承保 3-已承保 4-承保结束 5-承保失败
     */
    private String policyState;

    /**
     * 保单状态 1-投保中 2-待承保 3-已承保 4-承保结束 5-承保失败
     */
    private String policyStateName;

    /**
     * 保费计算方式
     */
    private String premCalType;

    /**
     * 支付方式
     */
    private String payType;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 生成日期
     */
    private String makeDate;

    /**
     * 生成时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 付款方式 1-代扣代缴 存fcensureconfig表
     */
    private String pyaMode;

    /**
     * 企业名称
     */
    private String grpName;

    /**
     * 统一社会信用代码
     */
    private String unifiedsociCode;

    /**
     * 福利投保类型
     */
    private String ensureType;

    /**
     * 计划类型
     */
    private String planType;


    /**
     * 审核类型 0-新增计划  1-变更计划
     */
    private String AuditType;

    /**
     * 变更计划类型  0-变更计划待审核、1-变更审核中、2-变更已承保、3-变更退回
     * 			4-定制中、5-待审核、6-审核中、7-审核退回 、8-代收费签单 、9-已承保
     *//*
    private String ChangeType;*/

    /**
     * 团险计划书新增订单关联字段
     */
    private String orderNo;

    /**
     * 保障计划集合
     */
    private List<FCEnsurePlan> fcEnsurePlans;

    /**
     * 签单状态
     */
    private String policySignState;

    /**
     * 签单返回信息
     */
    private String policySignErrorMsg;

}