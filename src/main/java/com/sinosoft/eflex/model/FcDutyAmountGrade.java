package com.sinosoft.eflex.model;

import java.math.BigDecimal;
import java.util.List;

public class FcDutyAmountGrade {
    /**
     * 保额档次编码
     */
    private String amountGrageCode;

    /**
     * 福利编号
     */
    private String ensureCode;

    /**
     * 险种编码
     */
    private String riskCode;

    /**
     * 险种名称
     */
    private String riskName;

    /**
     * 险种类别 只针对15070险种（其他险种存01）
     */
    private String riskType;

    /**
     * 必选责任编码
     */
    private String dutyCode;

    /**
     * 责任名称
     */
    private String dutyName;

    /**
     * 责任范围
     */
    private String dutyRange;

    /**
     * 是否必选 0--N  1--Y
     */
    private String dutyType;


    /**
     * 自定义档次名称
     */
    private String amountGrageName;

    /**
     * 保费
     */
    private Double prem;

    /**
     * 保额
     */
    private Double amnt;

    /**
     * 折扣比例
     */
    private Double discountRatio;

    /**
     * 按次/按年免赔
     */
    private String annualTimeDeduction;

    /**
     * 等待期
     */
    private Double waitingPeriod;

    /**
     * 最大赔付天数
     */
    private BigDecimal maxGetDay;

    public BigDecimal getMaxGetDay() {
        return maxGetDay;
    }

    public void setMaxGetDay(BigDecimal maxGetDay) {
        this.maxGetDay = maxGetDay;
    }

    /**
     * 特别约定
     */
    private String specialAgreement;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 管理机构
     */
    private String operatorCom;

    /**
     * 入机日期
     */
    private String makeDate;

    /**
     * 入机时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 默认免赔额
     */
    private String defaultDeductible;

    /**
     * 默认赔付比例
     */
    private String defaultCompensationRatio;

    /**
     * 免赔额
     */
    private List<Integer> fcDutyGroupDeductibleList;

    /**
     * 赔付比例
     */
    private List<Integer> fcDutGradeCompensationRatioList;

    /**
     *isDefaultFlag 是否为默认档次 应前台要求增加该字段 用于配置投保对象是否默认 默认选中否  0--否  1--是
     */
    private String isDefaultFlag;

    /**
     * existAmountGrageCode 根据层级查询所选所有保额档次时 根据该字段判断该每个保额档次是否在该层级内 用于页面保额档次是否勾选  0--不勾选  1 -- 勾选
     */
    private String existAmountGrageCode;

    /**
     * 保额档次编码
     * @return AmountGrageCode 保额档次编码
     */
    public String getAmountGrageCode() {
        return amountGrageCode;
    }

    /**
     * 保额档次编码
     * @param amountGrageCode 保额档次编码
     */
    public void setAmountGrageCode(String amountGrageCode) {
        this.amountGrageCode = amountGrageCode;
    }

    /**
     * 福利编号
     * @return EnsureCode 福利编号
     */
    public String getEnsureCode() {
        return ensureCode;
    }

    /**
     * 福利编号
     * @param ensureCode 福利编号
     */
    public void setEnsureCode(String ensureCode) {
        this.ensureCode = ensureCode;
    }

    /**
     * 险种编码
     * @return RiskCode 险种编码
     */
    public String getRiskCode() {
        return riskCode;
    }

    /**
     * 险种编码
     * @param riskCode 险种编码
     */
    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    /**
     * 险种类别 只针对15070险种（其他险种存01）
     * @return RiskType 险种类别 只针对15070险种（其他险种存01）
     */
    public String getRiskType() {
        return riskType;
    }

    /**
     * 险种类别 只针对15070险种（其他险种存01）
     * @param riskType 险种类别 只针对15070险种（其他险种存01）
     */
    public void setRiskType(String riskType) {
        this.riskType = riskType;
    }

    /**
     * 必选责任编码
     * @return DutyCode 必选责任编码
     */
    public String getDutyCode() {
        return dutyCode;
    }

    /**
     * 必选责任编码
     * @param dutyCode 必选责任编码
     */
    public void setDutyCode(String dutyCode) {
        this.dutyCode = dutyCode;
    }

    public String getRiskName() {
        return riskName;
    }

    public void setRiskName(String riskName) {
        this.riskName = riskName;
    }

    public String getDutyName() {
        return dutyName;
    }

    public void setDutyName(String dutyName) {
        this.dutyName = dutyName;
    }

    /**
     * 自定义档次名称
     * @return AmountGrageName 自定义档次名称
     */
    public String getAmountGrageName() {
        return amountGrageName;
    }

    /**
     * 自定义档次名称
     * @param amountGrageName 自定义档次名称
     */
    public void setAmountGrageName(String amountGrageName) {
        this.amountGrageName = amountGrageName;
    }

    /**
     * 保费
     * @return Prem 保费
     */
    public Double getPrem() {
        return prem;
    }

    /**
     * 保费
     * @param prem 保费
     */
    public void setPrem(Double prem) {
        this.prem = prem;
    }

    /**
     * 保额
     * @return Amnt 保额
     */
    public Double getAmnt() {
        return amnt;
    }

    /**
     * 保额
     * @param amnt 保额
     */
    public void setAmnt(Double amnt) {
        this.amnt = amnt;
    }

    /**
     * 折扣比例
     * @return DiscountRatio 折扣比例
     */
    public Double getDiscountRatio() {
        return discountRatio;
    }

    /**
     * 折扣比例
     * @param discountRatio 折扣比例
     */
    public void setDiscountRatio(Double discountRatio) {
        this.discountRatio = discountRatio;
    }

    /**
     * 按次/按年免赔
     * @return AnnualTimeDeduction 按次/按年免赔
     */
    public String getAnnualTimeDeduction() {
        return annualTimeDeduction;
    }

    /**
     * 按次/按年免赔
     * @param annualTimeDeduction 按次/按年免赔
     */
    public void setAnnualTimeDeduction(String annualTimeDeduction) {
        this.annualTimeDeduction = annualTimeDeduction;
    }

    /**
     * 等待期
     * @return WaitingPeriod 等待期
     */
    public Double getWaitingPeriod() {
        return waitingPeriod;
    }

    /**
     * 等待期
     * @param waitingPeriod 等待期
     */
    public void setWaitingPeriod(Double waitingPeriod) {
        this.waitingPeriod = waitingPeriod;
    }

    /**
     * 特别约定
     * @return SpecialAgreement 特别约定
     */
    public String getSpecialAgreement() {
        return specialAgreement;
    }

    /**
     * 特别约定
     * @param specialAgreement 特别约定
     */
    public void setSpecialAgreement(String specialAgreement) {
        this.specialAgreement = specialAgreement;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 管理机构
     * @return OperatorCom 管理机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 管理机构
     * @param operatorCom 管理机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 入机日期
     * @return MakeDate 入机日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 入机日期
     * @param makeDate 入机日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 入机时间
     * @return MakeTime 入机时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 入机时间
     * @param makeTime 入机时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getDutyRange() {
        return dutyRange;
    }

    public void setDutyRange(String dutyRange) {
        this.dutyRange = dutyRange;
    }

    public String getDutyType() {
        return dutyType;
    }

    public void setDutyType(String dutyType) {
        this.dutyType = dutyType;
    }

    public String getDefaultDeductible() {
        return defaultDeductible;
    }

    public void setDefaultDeductible(String defaultDeductible) {
        this.defaultDeductible = defaultDeductible;
    }

    public String getDefaultCompensationRatio() {
        return defaultCompensationRatio;
    }

    public void setDefaultCompensationRatio(String defaultCompensationRatio) {
        this.defaultCompensationRatio = defaultCompensationRatio;
    }

    public List<Integer> getFcDutyGroupDeductibleList() {
        return fcDutyGroupDeductibleList;
    }

    public void setFcDutyGroupDeductibleList(List<Integer> fcDutyGroupDeductibleList) {
        this.fcDutyGroupDeductibleList = fcDutyGroupDeductibleList;
    }

    public List<Integer> getFcDutGradeCompensationRatioList() {
        return fcDutGradeCompensationRatioList;
    }

    public void setFcDutGradeCompensationRatioList(List<Integer> fcDutGradeCompensationRatioList) {
        this.fcDutGradeCompensationRatioList = fcDutGradeCompensationRatioList;
    }

    public String getIsDefaultFlag() {
        return isDefaultFlag;
    }

    public void setIsDefaultFlag(String isDefaultFlag) {
        this.isDefaultFlag = isDefaultFlag;
    }

    public String getExistAmountGrageCode() {
        return existAmountGrageCode;
    }

    public void setExistAmountGrageCode(String existAmountGrageCode) {
        this.existAmountGrageCode = existAmountGrageCode;
    }

}