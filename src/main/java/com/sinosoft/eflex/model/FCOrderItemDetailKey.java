package com.sinosoft.eflex.model;

public class FCOrderItemDetailKey {
    /**
     * 子订单详情编号
     */
    private String orderItemDetailNo;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品要素编码
001-责任组合编码
002-保费
003-0表示产品 1表示其他福利
004-责任组合名称

--新增的要素（日常）
0010-责任编码
005-保额
006-交费期间 
007-交费年期 
008-交费方式 
009-保费计算方式 
0011-是否是生日单  0-不是生日单  1-是
0012-是否有犹豫期  0-不需犹豫期  1-需
     */
    private String productEleCode;

    /**
     * 子订单详情编号
     * @return OrderItemDetailNo 子订单详情编号
     */
    public String getOrderItemDetailNo() {
        return orderItemDetailNo;
    }

    /**
     * 子订单详情编号
     * @param orderItemDetailNo 子订单详情编号
     */
    public void setOrderItemDetailNo(String orderItemDetailNo) {
        this.orderItemDetailNo = orderItemDetailNo;
    }

    /**
     * 产品编码
     * @return ProductCode 产品编码
     */
    public String getProductCode() {
        return productCode;
    }

    /**
     * 产品编码
     * @param productCode 产品编码
     */
    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    /**
     * 产品要素编码
001-责任组合编码
002-保费
003-0表示产品 1表示其他福利
004-责任组合名称

--新增的要素（日常）
0010-责任编码
005-保额
006-交费期间 
007-交费年期 
008-交费方式 
009-保费计算方式 
0011-是否是生日单  0-不是生日单  1-是
0012-是否有犹豫期  0-不需犹豫期  1-需
     * @return ProductEleCode 产品要素编码
001-责任组合编码
002-保费
003-0表示产品 1表示其他福利
004-责任组合名称

--新增的要素（日常）
0010-责任编码
005-保额
006-交费期间 
007-交费年期 
008-交费方式 
009-保费计算方式 
0011-是否是生日单  0-不是生日单  1-是
0012-是否有犹豫期  0-不需犹豫期  1-需
     */
    public String getProductEleCode() {
        return productEleCode;
    }

    /**
     * 产品要素编码
001-责任组合编码
002-保费
003-0表示产品 1表示其他福利
004-责任组合名称

--新增的要素（日常）
0010-责任编码
005-保额
006-交费期间 
007-交费年期 
008-交费方式 
009-保费计算方式 
0011-是否是生日单  0-不是生日单  1-是
0012-是否有犹豫期  0-不需犹豫期  1-需
     * @param productEleCode 产品要素编码
001-责任组合编码
002-保费
003-0表示产品 1表示其他福利
004-责任组合名称

--新增的要素（日常）
0010-责任编码
005-保额
006-交费期间 
007-交费年期 
008-交费方式 
009-保费计算方式 
0011-是否是生日单  0-不是生日单  1-是
0012-是否有犹豫期  0-不需犹豫期  1-需
     */
    public void setProductEleCode(String productEleCode) {
        this.productEleCode = productEleCode;
    }
}