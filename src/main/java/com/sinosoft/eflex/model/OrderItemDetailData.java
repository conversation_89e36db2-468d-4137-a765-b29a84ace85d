package com.sinosoft.eflex.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/5/27 10:14
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
@Builder
public class OrderItemDetailData implements Serializable {

    private String orderItemNo;

    /**
     * 对应核心个单号
     */
    private String contNo;
    /**
     * 企业缴费
     */
    private Double grpPrem;



    private Double selfPrem;


    /**
     * 计划编码
     */
    private String productCode;
    /**
     * 人员id
     */
    private String personId;
    /**
     * 姓名
     */
    private String name;
    /**
     * 性别
     */
    private String sex;
    /**
     * 出生日期
     */
    private String birthday;
    /**
     * 国籍
     */
    private String nativePlace;
    /**
     *证件号
     */
    private String idType;
    /**
     * 证件类型
     */
    private String idNo;
    /**
     * 与住被保人关系
     */
    private String relation;

}
