
package com.sinosoft.eflex.model.makeProposalForm;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2020/6/10
 */
@Data
public class DATASETS {
    //接口应用场景
    private String Control;
    //影像类型编码集合
    private ImageTypes ImageTypes;
    //所有影像报文集合
    private DATASET DATASET;

    public String getControl() {
        return Control;
    }

    public void setControl(String control) {
        Control = control;
    }

    public com.sinosoft.eflex.model.makeProposalForm.DATASET getDATASET() {
        return DATASET;
    }

    public void setDATASET(com.sinosoft.eflex.model.makeProposalForm.DATASET DATASET) {
        this.DATASET = DATASET;
    }

    public com.sinosoft.eflex.model.makeProposalForm.ImageTypes getImageTypes() {
        return ImageTypes;
    }

    public void setImageTypes(com.sinosoft.eflex.model.makeProposalForm.ImageTypes imageTypes) {
        ImageTypes = imageTypes;
    }
}
