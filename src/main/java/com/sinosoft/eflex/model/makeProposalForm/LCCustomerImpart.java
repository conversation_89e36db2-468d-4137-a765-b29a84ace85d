
package com.sinosoft.eflex.model.makeProposalForm;

import java.util.List;

public class LCCustomerImpart {

    //投保人姓名
    private String AppntName;
    //告知人或告知人监护人姓名（指被保人）
    private String InsuredName;
    //是否为豁免险的智能核保问卷
    private String IsExemptRisk;
    //智能问卷问题节点集合
    private List<LCCustomerImparts> LCCustomerImpartsList;


    public String getAppntName() {
        return AppntName;
    }

    public void setAppntName(String appntName) {
        AppntName = appntName;
    }

    public String getInsuredName() {
        return InsuredName;
    }

    public void setInsuredName(String insuredName) {
        InsuredName = insuredName;
    }

    public String getIsExemptRisk() {
        return IsExemptRisk;
    }

    public void setIsExemptRisk(String isExemptRisk) {
        IsExemptRisk = isExemptRisk;
    }

    public List<LCCustomerImparts> getLCCustomerImpartsList() {
        return LCCustomerImpartsList;
    }

    public void setLCCustomerImpartsList(List<LCCustomerImparts> LCCustomerImpartsList) {
        this.LCCustomerImpartsList = LCCustomerImpartsList;
    }
}
