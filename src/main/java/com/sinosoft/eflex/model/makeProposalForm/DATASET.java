
package com.sinosoft.eflex.model.makeProposalForm;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/6/10
 */
public class DATASET {


    //保单号
    private String ContNo;
    //投保单号
    private String PrtNo;
    //订单号
    private String OrderNo;
    //管理机构代码（营业员所属机构代码）
    private String ManageComCode;
    //管理机构（营业员所属机构名称）
    private String ManageComName;
    //营业员编码
    private String AgentCode;
    //营业员姓名
    private String AgentName;
    //营业员电话
    private String AgentPhone;
    //币种单位
    private String CurrencyType;
    //首期保费合计大写
    private String PayMoneySumWrods;
    //首期保费合计小写
    private String PayMoneySumFigures;
    //首期交费方式
    private String PayMode;
    //续期交费方式
    private String NewPayMode;
    //交费频次（年交/月交/季交/半年交/一次性交清）
    private String PayIntv;
    //一年期产品是否自动续保（是/否）
    private String RNewFlag;
    //是否有社保
    private String SocialInsuFlag;
    //红利领取方式
    private String BonusGetMode;
    //生存/养老年金领取方式
    private String AnnuityGetMode;
    //生存/养老年金领取频次
    private String AnnuityGetIntv;
    //【转账授权】银行
    private String BankName;
    //【转账授权】账号
    private String BankAccNo;
    //【转账授权】账户名称
    private String BankAccName;
    //【转账授权】账户类型
    private String BankAccType;
    //投保书电子签名日期
    private String SignDate;
    //营业员签名日期
    private String AgentSignDate;
    //贷款类型
    private String LoanType;
    //发放贷款机构
    private String LoanOrg;
    //贷款合同号
    private String LoanContNo;
    //贷款金额
    private String LoanMoney;
    //投保人姓名
    private String AppntName;
    //投保人生日
    private String AppntBirthday;
    //投保人性别
    private String AppntSex;
    //投保人国籍
    private String AppntNationality;
    //投保人证件类型
    private String AppntIdTypeName;
    //投保人证件号码
    private String AppntIdNo;
    //投保人证件有效期
    private String AppntIdExpDate;
    //投保人职业类别
    private String AppntOccupationName;
    //投保人职业代码
    private String AppntOccupationCode;
    //投保人工作单位
    private String AppntWorkPlace;
    //投保人近两年年收入（万元）
    private String AppntAnnualIncome;
    //投保人地址
    private String AppntAddress;
    //投保人邮编
    private String AppntZipCode;
    //投保人手机
    private String AppntPhone;
    //投保人固定电话
    private String AppntHomePhone;
    //投保人邮箱
    private String AppntEmail;
    //团险种类
    private String GrpType;
    //电子投保单种类
    private String EPolicyType;

    //被保人列表
    private List<InsuredsItem> Insureds;
    //特别约定
    private String SpecInfoSet;

    //健康、财务及其他告知集合
    private List<CustomerImpart> CustomerImparts;

    //智能问卷内容集合
    private List<LCCustomerImpart> SmartImparts;

    //签名图片列表
    private List<PicFile> PicFiles;

    public String getContNo() {
        return ContNo;
    }

    public void setContNo(String contNo) {
        ContNo = contNo;
    }

    public String getPrtNo() {
        return PrtNo;
    }

    public void setPrtNo(String prtNo) {
        PrtNo = prtNo;
    }

    public String getOrderNo() {
        return OrderNo;
    }

    public void setOrderNo(String orderNo) {
        OrderNo = orderNo;
    }

    public String getManageComCode() {
        return ManageComCode;
    }

    public void setManageComCode(String manageComCode) {
        ManageComCode = manageComCode;
    }

    public String getManageComName() {
        return ManageComName;
    }

    public void setManageComName(String manageComName) {
        ManageComName = manageComName;
    }

    public String getAgentCode() {
        return AgentCode;
    }

    public void setAgentCode(String agentCode) {
        AgentCode = agentCode;
    }

    public String getAgentName() {
        return AgentName;
    }

    public void setAgentName(String agentName) {
        AgentName = agentName;
    }

    public String getAgentPhone() {
        return AgentPhone;
    }

    public void setAgentPhone(String agentPhone) {
        AgentPhone = agentPhone;
    }

    public String getCurrencyType() {
        return CurrencyType;
    }

    public void setCurrencyType(String currencyType) {
        CurrencyType = currencyType;
    }

    public String getPayMoneySumWrods() {
        return PayMoneySumWrods;
    }

    public void setPayMoneySumWrods(String payMoneySumWrods) {
        PayMoneySumWrods = payMoneySumWrods;
    }

    public String getPayMoneySumFigures() {
        return PayMoneySumFigures;
    }

    public void setPayMoneySumFigures(String payMoneySumFigures) {
        PayMoneySumFigures = payMoneySumFigures;
    }

    public String getPayMode() {
        return PayMode;
    }

    public void setPayMode(String payMode) {
        PayMode = payMode;
    }

    public String getNewPayMode() {
        return NewPayMode;
    }

    public void setNewPayMode(String newPayMode) {
        NewPayMode = newPayMode;
    }

    public String getPayIntv() {
        return PayIntv;
    }

    public void setPayIntv(String payIntv) {
        PayIntv = payIntv;
    }

    public String getRNewFlag() {
        return RNewFlag;
    }

    public void setRNewFlag(String RNewFlag) {
        this.RNewFlag = RNewFlag;
    }

    public String getSocialInsuFlag() {
        return SocialInsuFlag;
    }

    public void setSocialInsuFlag(String socialInsuFlag) {
        SocialInsuFlag = socialInsuFlag;
    }

    public String getBonusGetMode() {
        return BonusGetMode;
    }

    public void setBonusGetMode(String bonusGetMode) {
        BonusGetMode = bonusGetMode;
    }

    public String getAnnuityGetMode() {
        return AnnuityGetMode;
    }

    public void setAnnuityGetMode(String annuityGetMode) {
        AnnuityGetMode = annuityGetMode;
    }

    public String getAnnuityGetIntv() {
        return AnnuityGetIntv;
    }

    public void setAnnuityGetIntv(String annuityGetIntv) {
        AnnuityGetIntv = annuityGetIntv;
    }

    public String getBankName() {
        return BankName;
    }

    public void setBankName(String bankName) {
        BankName = bankName;
    }

    public String getBankAccNo() {
        return BankAccNo;
    }

    public void setBankAccNo(String bankAccNo) {
        BankAccNo = bankAccNo;
    }

    public String getBankAccName() {
        return BankAccName;
    }

    public void setBankAccName(String bankAccName) {
        BankAccName = bankAccName;
    }

    public String getBankAccType() {
        return BankAccType;
    }

    public void setBankAccType(String bankAccType) {
        BankAccType = bankAccType;
    }

    public String getSignDate() {
        return SignDate;
    }

    public void setSignDate(String signDate) {
        SignDate = signDate;
    }

    public String getAgentSignDate() {
        return AgentSignDate;
    }

    public void setAgentSignDate(String agentSignDate) {
        AgentSignDate = agentSignDate;
    }

    public String getLoanType() {
        return LoanType;
    }

    public void setLoanType(String loanType) {
        LoanType = loanType;
    }

    public String getLoanOrg() {
        return LoanOrg;
    }

    public void setLoanOrg(String loanOrg) {
        LoanOrg = loanOrg;
    }

    public String getLoanContNo() {
        return LoanContNo;
    }

    public void setLoanContNo(String loanContNo) {
        LoanContNo = loanContNo;
    }

    public String getLoanMoney() {
        return LoanMoney;
    }

    public void setLoanMoney(String loanMoney) {
        LoanMoney = loanMoney;
    }

    public String getAppntName() {
        return AppntName;
    }

    public void setAppntName(String appntName) {
        AppntName = appntName;
    }

    public String getAppntBirthday() {
        return AppntBirthday;
    }

    public void setAppntBirthday(String appntBirthday) {
        AppntBirthday = appntBirthday;
    }

    public String getAppntSex() {
        return AppntSex;
    }

    public void setAppntSex(String appntSex) {
        AppntSex = appntSex;
    }

    public String getAppntNationality() {
        return AppntNationality;
    }

    public void setAppntNationality(String appntNationality) {
        AppntNationality = appntNationality;
    }

    public String getAppntIdTypeName() {
        return AppntIdTypeName;
    }

    public void setAppntIdTypeName(String appntIdTypeName) {
        AppntIdTypeName = appntIdTypeName;
    }

    public String getAppntIdNo() {
        return AppntIdNo;
    }

    public void setAppntIdNo(String appntIdNo) {
        AppntIdNo = appntIdNo;
    }

    public String getAppntIdExpDate() {
        return AppntIdExpDate;
    }

    public void setAppntIdExpDate(String appntIdExpDate) {
        AppntIdExpDate = appntIdExpDate;
    }

    public String getAppntOccupationName() {
        return AppntOccupationName;
    }

    public void setAppntOccupationName(String appntOccupationName) {
        AppntOccupationName = appntOccupationName;
    }

    public String getAppntOccupationCode() {
        return AppntOccupationCode;
    }

    public void setAppntOccupationCode(String appntOccupationCode) {
        AppntOccupationCode = appntOccupationCode;
    }

    public String getAppntWorkPlace() {
        return AppntWorkPlace;
    }

    public void setAppntWorkPlace(String appntWorkPlace) {
        AppntWorkPlace = appntWorkPlace;
    }

    public String getAppntAnnualIncome() {
        return AppntAnnualIncome;
    }

    public void setAppntAnnualIncome(String appntAnnualIncome) {
        AppntAnnualIncome = appntAnnualIncome;
    }

    public String getAppntAddress() {
        return AppntAddress;
    }

    public void setAppntAddress(String appntAddress) {
        AppntAddress = appntAddress;
    }

    public String getAppntZipCode() {
        return AppntZipCode;
    }

    public void setAppntZipCode(String appntZipCode) {
        AppntZipCode = appntZipCode;
    }

    public String getAppntPhone() {
        return AppntPhone;
    }

    public void setAppntPhone(String appntPhone) {
        AppntPhone = appntPhone;
    }

    public String getAppntHomePhone() {
        return AppntHomePhone;
    }

    public void setAppntHomePhone(String appntHomePhone) {
        AppntHomePhone = appntHomePhone;
    }

    public String getAppntEmail() {
        return AppntEmail;
    }

    public void setAppntEmail(String appntEmail) {
        AppntEmail = appntEmail;
    }

    public List<InsuredsItem> getInsureds() {
        return Insureds;
    }

    public void setInsureds(List<InsuredsItem> insureds) {
        Insureds = insureds;
    }

    public List<CustomerImpart> getCustomerImparts() {
        return CustomerImparts;
    }

    public void setCustomerImparts(List<CustomerImpart> customerImparts) {
        CustomerImparts = customerImparts;
    }

    public List<LCCustomerImpart> getSmartImparts() {
        return SmartImparts;
    }

    public void setSmartImparts(List<LCCustomerImpart> smartImparts) {
        SmartImparts = smartImparts;
    }

    public List<PicFile> getPicFiles() {
        return PicFiles;
    }

    public void setPicFiles(List<PicFile> picFiles) {
        PicFiles = picFiles;
    }

	public String getGrpType() {
		return GrpType;
	}

	public void setGrpType(String grpType) {
		GrpType = grpType;
	}

	public String getEPolicyType() {
		return EPolicyType;
	}

	public void setEPolicyType(String ePolicyType) {
		EPolicyType = ePolicyType;
	}

    public String getSpecInfoSet() {
        return SpecInfoSet;
    }

    public void setSpecInfoSet(String specInfoSet) {
        SpecInfoSet = specInfoSet;
    }
}
