package com.sinosoft.eflex.model.makeProposalForm;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/6/10
 */
public class CustomerImpart {

    //保险对象姓名
    private String InsuredObjectName;
    //保险对象类型（第一被保险人、第二被保险人、未成年子女一…、投保人）
    private String InsuredObjectType;

    //告知内容集合
    private List<InsuredImpart> InsuredImpartList;


    public List<InsuredImpart> getInsuredImpartList() {
        return InsuredImpartList;
    }

    public void setInsuredImpartList(List<InsuredImpart> insuredImpartList) {
        InsuredImpartList = insuredImpartList;
    }

    public String getInsuredObjectName() {
        return InsuredObjectName;
    }

    public void setInsuredObjectName(String insuredObjectName) {
        InsuredObjectName = insuredObjectName;
    }

    public String getInsuredObjectType() {
        return InsuredObjectType;
    }

    public void setInsuredObjectType(String insuredObjectType) {
        InsuredObjectType = insuredObjectType;
    }
}
