package com.sinosoft.eflex.model.makeProposalForm;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/6/10
 */
public class RiskInfosItem {

    //险种代码
    private String RiskCode;
    //险种名称
    private String RiskName;
    //主/附险标志（M-主险，S-附加险）
    private String SubRiskFlag;
    //被保险人（保险对象）姓名
    private String InsuredObjectName;
    //保险金额/给付限额（优乐保）
    private String Amnt;
    //保险期间
    private String InsuYear;
    //交费期间
    private String PayEndYear;
    //期交保费
    private String Prem;

    //责任列表
    private List<DutyInfoListItem> DutyInfoList;


    public String getAmnt() {
        return Amnt;
    }

    public void setAmnt(String amnt) {
        Amnt = amnt;
    }

    public List<DutyInfoListItem> getDutyInfoList() {
        return DutyInfoList;
    }

    public void setDutyInfoList(List<DutyInfoListItem> dutyInfoList) {
        DutyInfoList = dutyInfoList;
    }

    public String getInsuYear() {
        return InsuYear;
    }

    public void setInsuYear(String insuYear) {
        InsuYear = insuYear;
    }

    public String getInsuredObjectName() {
        return InsuredObjectName;
    }

    public void setInsuredObjectName(String insuredObjectName) {
        InsuredObjectName = insuredObjectName;
    }

    public String getPayEndYear() {
        return PayEndYear;
    }

    public void setPayEndYear(String payEndYear) {
        PayEndYear = payEndYear;
    }

    public String getPrem() {
        return Prem;
    }

    public void setPrem(String prem) {
        Prem = prem;
    }

    public String getRiskCode() {
        return RiskCode;
    }

    public void setRiskCode(String riskCode) {
        RiskCode = riskCode;
    }

    public String getRiskName() {
        return RiskName;
    }

    public void setRiskName(String riskName) {
        RiskName = riskName;
    }

    public String getSubRiskFlag() {
        return SubRiskFlag;
    }

    public void setSubRiskFlag(String subRiskFlag) {
        SubRiskFlag = subRiskFlag;
    }
}
