package com.sinosoft.eflex.model.makeProposalForm;

/**
 * <AUTHOR>
 * @create 2020/6/10
 */
public class InsuredImpart {

    //告知类型编码
    private String impver;
    //告知编码（序号）
    private String ImpartCode;
    //告知询问事项
    private String ImpartContent;
    //告知情况（多种情况说明用“/”分割）
    private String ImpartReply;
    //告知情况备注
    private String IntroDuctions = "";


    public String getImpartCode() {
        return ImpartCode;
    }

    public void setImpartCode(String impartCode) {
        ImpartCode = impartCode;
    }

    public String getImpartContent() {
        return ImpartContent;
    }

    public void setImpartContent(String impartContent) {
        ImpartContent = impartContent;
    }

    public String getImpartReply() {
        return ImpartReply;
    }

    public void setImpartReply(String impartReply) {
        ImpartReply = impartReply;
    }

    public String getImpver() {
        return impver;
    }

    public void setImpver(String impver) {
        this.impver = impver;
    }

    public String getIntroDuctions() {
        return IntroDuctions;
    }

    public void setIntroDuctions(String introDuctions) {
        IntroDuctions = introDuctions;
    }
}
