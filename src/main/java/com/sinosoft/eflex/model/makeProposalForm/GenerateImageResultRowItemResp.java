package com.sinosoft.eflex.model.makeProposalForm;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2020/6/17
 */
@Data
public class GenerateImageResultRowItemResp {

    //返回代码	String	20	N	详见返回码
    private String rescode;
    //返回信息	String	50	N
    private String resmsg;
    //影像文件类型	String	30	N
    private String imageType;
    //OSS存储路径	String	300	Y	返回码为0000时，不为空
    private String fileId;
    //内网地址	String	300	Y	返回码为0000时，不为空
    private String innerNet;
    //外网地址	String	300	Y	返回码为0000时，不为空
    private String outerNet;
    //返回码为0000时，不为空
    private String bucketName;
    //文件内网地址	String	1000	Y	https://bucketName.innerNet/fileId
    private String innerNetUrl;
    //文件外网地址	String	1000	Y	https://outerNet/fileId
    private String outerNetUrl;
	public String getRescode() {
		return rescode;
	}
	public void setRescode(String rescode) {
		this.rescode = rescode;
	}
	public String getResmsg() {
		return resmsg;
	}
	public void setResmsg(String resmsg) {
		this.resmsg = resmsg;
	}
	public String getImageType() {
		return imageType;
	}
	public void setImageType(String imageType) {
		this.imageType = imageType;
	}
	public String getFileId() {
		return fileId;
	}
	public void setFileId(String fileId) {
		this.fileId = fileId;
	}
	public String getInnerNet() {
		return innerNet;
	}
	public void setInnerNet(String innerNet) {
		this.innerNet = innerNet;
	}
	public String getOuterNet() {
		return outerNet;
	}
	public void setOuterNet(String outerNet) {
		this.outerNet = outerNet;
	}
	public String getBucketName() {
		return bucketName;
	}
	public void setBucketName(String bucketName) {
		this.bucketName = bucketName;
	}
	public String getInnerNetUrl() {
		return innerNetUrl;
	}
	public void setInnerNetUrl(String innerNetUrl) {
		this.innerNetUrl = innerNetUrl;
	}
	public String getOuterNetUrl() {
		return outerNetUrl;
	}
	public void setOuterNetUrl(String outerNetUrl) {
		this.outerNetUrl = outerNetUrl;
	}
    
    
}
