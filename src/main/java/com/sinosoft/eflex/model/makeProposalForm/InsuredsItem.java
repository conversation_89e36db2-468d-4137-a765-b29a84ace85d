package com.sinosoft.eflex.model.makeProposalForm;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/6/10
 * 生成的时候需要重命名下
 */
public class InsuredsItem {

    //被保险人类型（第一被保险人、第二被保险人、未成年子女一...）
    private String InsuredType;
    //被保险人姓名
    private String InsuredName;
    //被保险人生日
    private String InsuredBirthday;
    //被保险人性别
    private String InsuredSex;
    //被保险人国籍
    private String InsuredNationality;
    //被保险人证件类型
    private String InsuredIdTypeName;
    //被保险人证件号码
    private String InsuredIdNo;
    //被保险人证件有效期
    private String InsuredIdExpDate;
    //被保险人职业类别
    private String InsuredOccupationName;
    //被保险人职业代码
    private String InsuredOccupationCode;
    //被保险人工作单位
    private String InsuredWorkPlace;
    //被保险人近两年年收入（万元）
    private String InsuredAnnualIncome;
    //被保险人地址
    private String InsuredAddress;
    //被保险人邮编
    private String InsuredZipCode;
    //被保险人手机
    private String InsuredPhone;
    //被保险人固定电话
    private String InsuredHomePhone;
    //被保险人邮箱
    private String InsuredEmail;
    //被保人是否需要确认
    private String NeedConfirmationFlag;
    //投保人与被保险人关系
    private String AppntRelationToInsured;

    //身故受益人列表
    private List<BnfInfosItem> BnfInfos;

    //险种列表
    private List<RiskInfosItem> RiskInfos;


    public String getAppntRelationToInsured() {
        return AppntRelationToInsured;
    }

    public void setAppntRelationToInsured(String appntRelationToInsured) {
        AppntRelationToInsured = appntRelationToInsured;
    }

    public List<BnfInfosItem> getBnfInfos() {
        return BnfInfos;
    }

    public void setBnfInfos(List<BnfInfosItem> bnfInfos) {
        BnfInfos = bnfInfos;
    }

    public String getInsuredAddress() {
        return InsuredAddress;
    }

    public void setInsuredAddress(String insuredAddress) {
        InsuredAddress = insuredAddress;
    }

    public String getInsuredAnnualIncome() {
        return InsuredAnnualIncome;
    }

    public void setInsuredAnnualIncome(String insuredAnnualIncome) {
        InsuredAnnualIncome = insuredAnnualIncome;
    }

    public String getInsuredBirthday() {
        return InsuredBirthday;
    }

    public void setInsuredBirthday(String insuredBirthday) {
        InsuredBirthday = insuredBirthday;
    }

    public String getInsuredEmail() {
        return InsuredEmail;
    }

    public void setInsuredEmail(String insuredEmail) {
        InsuredEmail = insuredEmail;
    }

    public String getInsuredHomePhone() {
        return InsuredHomePhone;
    }

    public void setInsuredHomePhone(String insuredHomePhone) {
        InsuredHomePhone = insuredHomePhone;
    }

    public String getInsuredIdExpDate() {
        return InsuredIdExpDate;
    }

    public void setInsuredIdExpDate(String insuredIdExpDate) {
        InsuredIdExpDate = insuredIdExpDate;
    }

    public String getInsuredIdNo() {
        return InsuredIdNo;
    }

    public void setInsuredIdNo(String insuredIdNo) {
        InsuredIdNo = insuredIdNo;
    }

    public String getInsuredIdTypeName() {
        return InsuredIdTypeName;
    }

    public void setInsuredIdTypeName(String insuredIdTypeName) {
        InsuredIdTypeName = insuredIdTypeName;
    }

    public String getInsuredName() {
        return InsuredName;
    }

    public void setInsuredName(String insuredName) {
        InsuredName = insuredName;
    }

    public String getInsuredNationality() {
        return InsuredNationality;
    }

    public void setInsuredNationality(String insuredNationality) {
        InsuredNationality = insuredNationality;
    }

    public String getInsuredOccupationCode() {
        return InsuredOccupationCode;
    }

    public void setInsuredOccupationCode(String insuredOccupationCode) {
        InsuredOccupationCode = insuredOccupationCode;
    }

    public String getInsuredOccupationName() {
        return InsuredOccupationName;
    }

    public void setInsuredOccupationName(String insuredOccupationName) {
        InsuredOccupationName = insuredOccupationName;
    }

    public String getInsuredPhone() {
        return InsuredPhone;
    }

    public void setInsuredPhone(String insuredPhone) {
        InsuredPhone = insuredPhone;
    }

    public String getInsuredSex() {
        return InsuredSex;
    }

    public void setInsuredSex(String insuredSex) {
        InsuredSex = insuredSex;
    }

    public String getInsuredType() {
        return InsuredType;
    }

    public void setInsuredType(String insuredType) {
        InsuredType = insuredType;
    }

    public String getInsuredWorkPlace() {
        return InsuredWorkPlace;
    }

    public void setInsuredWorkPlace(String insuredWorkPlace) {
        InsuredWorkPlace = insuredWorkPlace;
    }

    public String getInsuredZipCode() {
        return InsuredZipCode;
    }

    public void setInsuredZipCode(String insuredZipCode) {
        InsuredZipCode = insuredZipCode;
    }

    public String getNeedConfirmationFlag() {
        return NeedConfirmationFlag;
    }

    public void setNeedConfirmationFlag(String needConfirmationFlag) {
        NeedConfirmationFlag = needConfirmationFlag;
    }

    public List<RiskInfosItem> getRiskInfos() {
        return RiskInfos;
    }

    public void setRiskInfos(List<RiskInfosItem> riskInfos) {
        RiskInfos = riskInfos;
    }
}
