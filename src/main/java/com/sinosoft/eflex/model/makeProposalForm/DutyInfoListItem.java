package com.sinosoft.eflex.model.makeProposalForm;

/**
 * <AUTHOR>
 * @create 2020/6/10
 */
public class DutyInfoListItem {

    //责任名称
    private String DutyName;
    //责任编码
    private String DutyCode;
    //责任保险金额/给付限额（优乐保）
    private String Amnt;
    //保险期间
    private String InsuYear;
    //交费期间
    private String PayEndYear;
    //期交保费
    private String Prem;
    //赔付比例
    private String GetRate = "";
    //给付限额
    private String GetLimit = "";
    //免赔额
    private String GetDeductible = "";


    public String getAmnt() {
        return Amnt;
    }

    public void setAmnt(String amnt) {
        Amnt = amnt;
    }

    public String getDutyCode() {
        return DutyCode;
    }

    public void setDutyCode(String dutyCode) {
        DutyCode = dutyCode;
    }

    public String getDutyName() {
        return DutyName;
    }

    public void setDutyName(String dutyName) {
        DutyName = dutyName;
    }

    public String getGetDeductible() {
        return GetDeductible;
    }

    public void setGetDeductible(String getDeductible) {
        GetDeductible = getDeductible;
    }

    public String getGetLimit() {
        return GetLimit;
    }

    public void setGetLimit(String getLimit) {
        GetLimit = getLimit;
    }

    public String getGetRate() {
        return GetRate;
    }

    public void setGetRate(String getRate) {
        GetRate = getRate;
    }

    public String getInsuYear() {
        return InsuYear;
    }

    public void setInsuYear(String insuYear) {
        InsuYear = insuYear;
    }

    public String getPayEndYear() {
        return PayEndYear;
    }

    public void setPayEndYear(String payEndYear) {
        PayEndYear = payEndYear;
    }

    public String getPrem() {
        return Prem;
    }

    public void setPrem(String prem) {
        Prem = prem;
    }
}
