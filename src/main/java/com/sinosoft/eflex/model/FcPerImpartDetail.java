package com.sinosoft.eflex.model;

import java.util.Date;

public class FcPerImpartDetail {
    /**
     * 
     */
    private String orderItemNo;

    /**
     * 
     */
    private String impartVer;

    /**
     * 
     */
    private String impartCode;

    /**
     * 答题结果
     */
    private String impartContent;

    /**
     * 
     */
    private Date makeDate;

    /**
     * 
     */
    private String makeTime;

    /**
     * 
     */
    private Date modifyDate;

    /**
     * 
     */
    private String modifyTime;

    /**
     * 
     * @return OrderItemNo
     */
    public String getOrderItemNo() {
        return orderItemNo;
    }

    /**
     * 
     * @param orderItemNo
     */
    public void setOrderItemNo(String orderItemNo) {
        this.orderItemNo = orderItemNo;
    }

    /**
     * 
     * @return ImpartVer
     */
    public String getImpartVer() {
        return impartVer;
    }

    /**
     * 
     * @param impartVer
     */
    public void setImpartVer(String impartVer) {
        this.impartVer = impartVer;
    }

    /**
     * 
     * @return ImpartCode
     */
    public String getImpartCode() {
        return impartCode;
    }

    /**
     * 
     * @param impartCode
     */
    public void setImpartCode(String impartCode) {
        this.impartCode = impartCode;
    }

    /**
     * 答题结果
     * 
     * @return ImpartContent 答题结果
     */
    public String getImpartContent() {
        return impartContent;
    }

    /**
     * 答题结果
     * 
     * @param impartContent
     *            答题结果
     */
    public void setImpartContent(String impartContent) {
        this.impartContent = impartContent;
    }

    /**
     * 
     * @return MakeDate
     */
    public Date getMakeDate() {
        return makeDate;
    }

    /**
     * 
     * @param makeDate
     */
    public void setMakeDate(Date makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 
     * @return MakeTime
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 
     * @param makeTime
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 
     * @return ModifyDate
     */
    public Date getModifyDate() {
        return modifyDate;
    }

    /**
     * 
     * @param modifyDate
     */
    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 
     * @return ModifyTime
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 
     * @param modifyTime
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}