package com.sinosoft.eflex.model;

import java.util.Date;

public class FDUserRole {
    /**
     * 用户角色流水号
     */
    private String userRoleSN;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 角色类型
     */
    private String roleType;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 注册日期
     */
    private String makeDate;

    /**
     * 注册时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 用户角色流水号
     * @return UserRoleSN 用户角色流水号
     */
    public String getUserRoleSN() {
        return userRoleSN;
    }

    /**
     * 用户角色流水号
     * @param userRoleSN 用户角色流水号
     */
    public void setUserRoleSN(String userRoleSN) {
        this.userRoleSN = userRoleSN;
    }

    /**
     * 用户编号
     * @return UserNo 用户编号
     */
    public String getUserNo() {
        return userNo;
    }

    /**
     * 用户编号
     * @param userNo 用户编号
     */
    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    /**
     * 角色类型
     * @return RoleType 角色类型
     */
    public String getRoleType() {
        return roleType;
    }

    /**
     * 角色类型
     * @param roleType 角色类型
     */
    public void setRoleType(String roleType) {
        this.roleType = roleType;
    }

    /**
     * 操作机构
     * @return OperatorCom 操作机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作机构
     * @param operatorCom 操作机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }




    /**
     * 注册时间
     * @return MakeTime 注册时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 注册时间
     * @param makeTime 注册时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    public String getMakeDate() {
        return makeDate;
    }

    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    public String getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}