package com.sinosoft.eflex.model;

public class Family {

    /**
     * 姓名
     */
    private String Name;

    /**
     * 性别
     */
    private String Sex;

    /**
     * 出生日期
     */
    private String Birthday;

    /**
     * 证件类型
     */
    private String IDType;

    /**
     * 证件号
     */
    private String IDNo;

    /**
     * 移动电话
     */
    private String Mobile;

    /**
     * 固定电话
     */
    private String Phone;

    /**
     * 职业类别
     */
    private String OccupationType;


    /**
     * 职业代码
     */
    private String OccupationCode;
    
    /**
     * 是否参加医保:
            0-否
            1-是
     */
    private String SocialInsuFlag;


    /**
     * 邮箱
     */
    private String Email;

    /**
     * 邮编
     */
    private String ZipCode;

    /**
     * 详细地址
     */
    private String Address;



    /*与员工关系*/
    private String Relation;



	public String getName() {
		return Name;
	}



	public void setName(String name) {
		Name = name;
	}



	public String getSex() {
		return Sex;
	}



	public void setSex(String sex) {
		Sex = sex;
	}



	public String getBirthday() {
		return Birthday;
	}



	public void setBirthday(String birthday) {
		Birthday = birthday;
	}



	public String getIDType() {
		return IDType;
	}



	public void setIDType(String iDType) {
		IDType = iDType;
	}



	public String getIDNo() {
		return IDNo;
	}



	public void setIDNo(String iDNo) {
		IDNo = iDNo;
	}



	public String getMobile() {
		return Mobile;
	}



	public void setMobile(String mobile) {
		Mobile = mobile;
	}



	public String getPhone() {
		return Phone;
	}



	public void setPhone(String phone) {
		Phone = phone;
	}



	public String getOccupationType() {
		return OccupationType;
	}



	public void setOccupationType(String occupationType) {
		OccupationType = occupationType;
	}



	public String getOccupationCode() {
		return OccupationCode;
	}



	public void setOccupationCode(String occupationCode) {
		OccupationCode = occupationCode;
	}



	public String getSocialInsuFlag() {
		return SocialInsuFlag;
	}



	public void setSocialInsuFlag(String socialInsuFlag) {
		SocialInsuFlag = socialInsuFlag;
	}



	public String getEmail() {
		return Email;
	}



	public void setEmail(String email) {
		Email = email;
	}



	public String getZipCode() {
		return ZipCode;
	}



	public void setZipCode(String zipCode) {
		ZipCode = zipCode;
	}



	public String getAddress() {
		return Address;
	}



	public void setAddress(String address) {
		Address = address;
	}



	public String getRelation() {
		return Relation;
	}



	public void setRelation(String relation) {
		Relation = relation;
	}

 
}
