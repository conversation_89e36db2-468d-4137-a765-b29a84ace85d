package com.sinosoft.eflex.model;

import java.util.List;

public class FDmenu {
    /**
     * 菜单节点
     */
    private String nodeCode;

    /**
     * 菜单父节点
     */
    private String parentNodeCode;

    /**
     * 菜单层级
     */
    private Integer nodeLevel;

    /**
     * 菜单名称
     */
    private String nodeName;

    /**
     * 子菜单个数
     */
    private Integer childFlag;

    /**
     * 页面链接地址
     */
    private String runScript;

    /**
     * 菜单图片
     */
    private String nodeImgUrl;

    /**
     * 菜单节点说明
     */
    private String nodeDesc;

    private List<FDmenu>  fDmenuList;

    public List<FDmenu> getfDmenuList() {
        return fDmenuList;
    }

    public void setfDmenuList(List<FDmenu> fDmenuList) {
        this.fDmenuList = fDmenuList;
    }

    /**
     * 菜单顺序
     */
    private Integer nodeOrder;

    /**
     * 菜单节点
     * @return NodeCode 菜单节点
     */
    public String getNodeCode() {
        return nodeCode;
    }

    /**
     * 菜单节点
     * @param nodeCode 菜单节点
     */
    public void setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
    }

    /**
     * 菜单父节点
     * @return ParentNodeCode 菜单父节点
     */
    public String getParentNodeCode() {
        return parentNodeCode;
    }

    /**
     * 菜单父节点
     * @param parentNodeCode 菜单父节点
     */
    public void setParentNodeCode(String parentNodeCode) {
        this.parentNodeCode = parentNodeCode;
    }

    /**
     * 菜单层级
     * @return NodeLevel 菜单层级
     */
    public Integer getNodeLevel() {
        return nodeLevel;
    }

    /**
     * 菜单层级
     * @param nodeLevel 菜单层级
     */
    public void setNodeLevel(Integer nodeLevel) {
        this.nodeLevel = nodeLevel;
    }

    /**
     * 菜单名称
     * @return NodeName 菜单名称
     */
    public String getNodeName() {
        return nodeName;
    }

    /**
     * 菜单名称
     * @param nodeName 菜单名称
     */
    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    /**
     * 子菜单个数
     * @return ChildFlag 子菜单个数
     */
    public Integer getChildFlag() {
        return childFlag;
    }

    /**
     * 子菜单个数
     * @param childFlag 子菜单个数
     */
    public void setChildFlag(Integer childFlag) {
        this.childFlag = childFlag;
    }

    /**
     * 页面链接地址
     * @return RunScript 页面链接地址
     */
    public String getRunScript() {
        return runScript;
    }

    /**
     * 页面链接地址
     * @param runScript 页面链接地址
     */
    public void setRunScript(String runScript) {
        this.runScript = runScript;
    }

    /**
     * 菜单图片
     * @return NodeImgUrl 菜单图片
     */
    public String getNodeImgUrl() {
        return nodeImgUrl;
    }

    /**
     * 菜单图片
     * @param nodeImgUrl 菜单图片
     */
    public void setNodeImgUrl(String nodeImgUrl) {
        this.nodeImgUrl = nodeImgUrl;
    }

    /**
     * 菜单节点说明
     * @return NodeDesc 菜单节点说明
     */
    public String getNodeDesc() {
        return nodeDesc;
    }

    /**
     * 菜单节点说明
     * @param nodeDesc 菜单节点说明
     */
    public void setNodeDesc(String nodeDesc) {
        this.nodeDesc = nodeDesc;
    }

    /**
     * 菜单顺序
     * @return NodeOrder 菜单顺序
     */
    public Integer getNodeOrder() {
        return nodeOrder;
    }

    /**
     * 菜单顺序
     * @param nodeOrder 菜单顺序
     */
    public void setNodeOrder(Integer nodeOrder) {
        this.nodeOrder = nodeOrder;
    }
}