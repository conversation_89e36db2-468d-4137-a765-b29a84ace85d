package com.sinosoft.eflex.model;

import java.io.Serializable;

import lombok.Data;

/**
 * <AUTHOR> wenying Xia
 * @date : 2020-04-16 10:50
 *
 * FcDailyInsureRiskInfo(日常投保险种信息配置表)
 **/
@Data
public class FcDailyInsureRiskInfo implements Serializable {
	//配置流水号
    private String DeployNo;
	//福利编号
    private String EnsureCode;
	//险种编码
    private String RiskCode;
	//手续费比率
	private String FeeRatio;
	//佣金/服务津贴率
	private String CommissionOrAllowanceRatio;
	//操作机构
    private String OperatorCom;
	//操作员
    private String Operator;
	//入机日期
    private String MakeDate;
	//入机时间
    private String MakeTime;
	//修改日期
    private String ModifyDate;
	//修改时间
    private String ModifyTime;
}
