package com.sinosoft.eflex.model;

import lombok.Data;
@Data
public class FCAppntImpartInfoPersonal {
    /**
     * 子订单号
     */
    private String orderItemNo;

    /**
     * 告知编码
     */
    private String impartCode;

    /**
     * 企业客户号
     */
    private String grpNo;

    /**
     * 详细描述
     */
    private String impartParamModle;

 /**
     * 详细描述
     */
    private String dsimpartParam;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 入机日期
     */
    private String makeDate;

    /**
     * 入机时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 子订单号
     * @return OrderItemNo 子订单号
     */
    public String getOrderItemNo() {
        return orderItemNo;
    }

    /**
     * 子订单号
     * @param orderItemNo 子订单号
     */
    public void setOrderItemNo(String orderItemNo) {
        this.orderItemNo = orderItemNo;
    }

    /**
     * 告知编码
     * @return ImpartCode 告知编码
     */
    public String getImpartCode() {
        return impartCode;
    }

    /**
     * 告知编码
     * @param impartCode 告知编码
     */
    public void setImpartCode(String impartCode) {
        this.impartCode = impartCode;
    }

    /**
     * 企业客户号
     * @return GrpNo 企业客户号
     */
    public String getGrpNo() {
        return grpNo;
    }

    /**
     * 企业客户号
     * @param grpNo 企业客户号
     */
    public void setGrpNo(String grpNo) {
        this.grpNo = grpNo;
    }

    /**
     * 详细描述
     * @return ImpartParamModle 详细描述
     */
    public String getImpartParamModle() {
        return impartParamModle;
    }

    /**
     * 详细描述
     * @param impartParamModle 详细描述
     */
    public void setImpartParamModle(String impartParamModle) {
        this.impartParamModle = impartParamModle;
    }

    /**
     * 操作员
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 操作机构
     * @return OperatorCom 操作机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作机构
     * @param operatorCom 操作机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 入机日期
     * @return MakeDate 入机日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 入机日期
     * @param makeDate 入机日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 入机时间
     * @return MakeTime 入机时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 入机时间
     * @param makeTime 入机时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}