package com.sinosoft.eflex.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class FDRiskDutyInfo extends FDRiskDutyInfoKey {
    /**
     * 责任名称
     */
    private String dutyName;

    /**
     * 免赔额
     */
    private BigDecimal getLimit;

    /**
     * 免赔额集合
     */
    private List<String> getLimitList;

    /**
     * 免赔额属性集合 1-按次免赔 2-按年免赔
     */
    private List<String> getLimitTypeList;

    /**
     * 保额集合
     */
    private List<String> amntList;

    /**
     * 给付天数
     */
    private BigDecimal payDay;

    /**
     * 免赔天数
     */
    private BigDecimal noGetDay;

    /**
     * 赔付比例
     */
    private BigDecimal getRate;

    /**
     * 赔付比例集合
     */
    private List<String> getRateList;

    /**
     * 费率/折扣
     */
    private BigDecimal floatRate;

    /**
     * 计算规则
     */
    private String calRule;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 生成日期
     */
    private Date makeDate;

    /**
     * 生成时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private Date modifyDate;

    public List<String> getAmntList() {
        return amntList;
    }

    public void setAmntList(List<String> amntList) {
        this.amntList = amntList;
    }

    public List<String> getGetLimitTypeList() {
        return getLimitTypeList;
    }

    public void setGetLimitTypeList(List<String> getLimitTypeList) {
        this.getLimitTypeList = getLimitTypeList;
    }

    /**
     * 修改时间
     */
    private String modifyTime;

    public String getDutyName() {
        return dutyName;
    }

    public void setDutyName(String dutyName) {
        this.dutyName = dutyName;
    }

    public BigDecimal getGetLimit() {
        return getLimit;
    }

    public void setGetLimit(BigDecimal getLimit) {
        this.getLimit = getLimit;
    }

    public List<String> getGetLimitList() {
        return getLimitList;
    }

    public void setGetLimitList(List<String> getLimitList) {
        this.getLimitList = getLimitList;
    }

    public BigDecimal getPayDay() {
        return payDay;
    }

    public void setPayDay(BigDecimal payDay) {
        this.payDay = payDay;
    }

    public BigDecimal getNoGetDay() {
        return noGetDay;
    }

    public void setNoGetDay(BigDecimal noGetDay) {
        this.noGetDay = noGetDay;
    }

    public BigDecimal getGetRate() {
        return getRate;
    }

    public void setGetRate(BigDecimal getRate) {
        this.getRate = getRate;
    }

    public List<String> getGetRateList() {
        return getRateList;
    }

    public void setGetRateList(List<String> getRateList) {
        this.getRateList = getRateList;
    }

    public BigDecimal getFloatRate() {
        return floatRate;
    }

    public void setFloatRate(BigDecimal floatRate) {
        this.floatRate = floatRate;
    }

    public String getCalRule() {
        return calRule;
    }

    public void setCalRule(String calRule) {
        this.calRule = calRule;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperatorCom() {
        return operatorCom;
    }

    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    public Date getMakeDate() {
        return makeDate;
    }

    public void setMakeDate(Date makeDate) {
        this.makeDate = makeDate;
    }

    public String getMakeTime() {
        return makeTime;
    }

    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
}