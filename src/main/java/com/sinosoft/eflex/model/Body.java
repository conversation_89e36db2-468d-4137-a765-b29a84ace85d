package com.sinosoft.eflex.model;

import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.model.edor.GrpConts;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-12-27 15:42
 */
public class Body {
    private String EdorFlag;
    private String EdorMark;
    private String GrpContNo;
    private String EdorAppNo;
    private String EdorCvaliDate;
    private String Prem;
    private String ContNo;

    private String ProposalPrtNo;

    private List<Insured> InsuredList;

    private List<EdorInfo> EdorList;
    
    private List<Family> FamilyList;
    
	private FpList FpList;

    private List<FpList> InvoiceList;

    private List<Plan> PlanList;

    private String Total;

    public List<FpList> getInvoiceList() {
        return InvoiceList;
    }


    /**
     * 保全挂起查询返回结果处理，核心的返回报文格式存在问题，所以这里垫了一层
     */
    private com.sinosoft.eflex.model.edor.GrpConts GrpConts;



    public void setInvoiceList(List<FpList> InvoiceList) {
        this.InvoiceList = InvoiceList;
    }

    public String getTotal() {
        return Total;
    }

    public void setTotal(String total) {
        Total = total;
    }

    public List<EdorInfo> getEdorList() {
        return EdorList;
    }

    public void setEdorList(List<EdorInfo> edorList) {
        EdorList = edorList;
    }

    public List<Insured> getInsuredList() {
        return InsuredList;
    }

    public void setInsuredList(List<Insured> insuredList) {
        InsuredList = insuredList;
    }

    public String getPrem() {
        return Prem;
    }

    public void setPrem(String prem) {
        Prem = prem;
    }

    public String getContNo() {
        return ContNo;
    }

    public void setContNo(String contNo) {
        ContNo = contNo;
    }

    public String getEdorFlag() {
        return EdorFlag;
    }

    public void setEdorFlag(String edorFlag) {
        EdorFlag = edorFlag;
    }

    public String getEdorMark() {
        return EdorMark;
    }

    public void setEdorMark(String edorMark) {
        EdorMark = edorMark;
    }

    public String getGrpContNo() {
        return GrpContNo;
    }

    public void setGrpContNo(String grpContNo) {
        GrpContNo = grpContNo;
    }

    public String getEdorAppNo() {
        return EdorAppNo;
    }

    public void setEdorAppNo(String edorAppNo) {
        EdorAppNo = edorAppNo;
    }

    public String getEdorCvaliDate() {
        return EdorCvaliDate;
    }

    public void setEdorCvaliDate(String edorCvaliDate) {
        EdorCvaliDate = edorCvaliDate;
    }

	public List<Family> getFamilyList() {
		return FamilyList;
	}

	public void setFamilyList(List<Family> familyList) {
		FamilyList = familyList;
	}

    public FpList getFpList() {
        return FpList;
    }

    public void setFpList(FpList fpList) {
        FpList = fpList;
    }

    public List<Plan> getPlanList() {
        return PlanList;
    }

    public void setPlanList(List<Plan> planList) {
        this.PlanList = planList;
    }

    public String getProposalPrtNo() {
        return ProposalPrtNo;
    }

    public void setProposalPrtNo(String proposalPrtNo) {
        ProposalPrtNo = proposalPrtNo;
    }

    public com.sinosoft.eflex.model.edor.GrpConts getGrpConts() {
        return GrpConts;
    }

    public void setGrpConts(com.sinosoft.eflex.model.edor.GrpConts grpConts) {
        GrpConts = grpConts;
    }
}
