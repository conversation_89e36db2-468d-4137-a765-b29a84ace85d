package com.sinosoft.eflex.model.checkTBRules;

import com.sinosoft.eflex.model.insureEflexPlanPage.AmountGrageInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/6/3
 * @desc
 */
@Data
public class InsuredRiskInfo {

    // 保额档次编码
    private String amountGrageCode;

    // 险种编码
    private String riskCode;

    // 险种名称
    private String riskName;

    // 险种类型
    private String riskType;

    // 责任名称
    private String dutyName;

    // 保额
    private String defaultAmnt;

    // 免赔额
    private String deductible;

    // 赔付比例
    private String compensationRatio;

    // 保费
    private String prem;

    // 出生日期
    private String birthDay;

    // 性别
    private String sex;

    // 有无医保
    private String joinMedProtect;

    // 职业类别
    private String occupationType;

    // 保单生效日期
    private String cvaliDate;

    // 投保人数
    private String insureCount;

    // 展示列表
    private String showList;

    // 保额档次信息
    private List<AmountGrageInfo> amountGradeCodeList;

}
