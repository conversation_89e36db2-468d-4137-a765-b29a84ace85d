package com.sinosoft.eflex.model;

import java.io.Serializable;

/**
 * 返回信息
 * 
 * <AUTHOR>
 *
 */
public class ResponseMsg<T> implements Serializable {

	private static final long serialVersionUID = 7429594305458035238L;

	/**
	 * 描述信息
	 */
	private String message;

	/**
	 * 响应状态
	 */
	private Boolean success;

	/**
	 * 返回数据
	 */
	private T data;

	/**
	 * 响应状态码
	 */
	private String code;

	/**
	 * @return 描述信息
	 */
	public String getMessage() {
		return message;
	}

	/**
	 * @param message
	 *            描述信息
	 */
	public void setMessage(String message) {
		this.message = message;
	}

	/**
	 * 描述信息
	 * 
	 * @param message
	 * @return
	 */
	public ResponseMsg<T> message(String message) {
		this.message = message;
		return this;
	}

	/**
	 * @return 响应状态
	 */
	public Boolean getSuccess() {
		return success;
	}

	/**
	 * 响应状态
	 * 
	 * @param success
	 */
	public void setSuccess(Boolean success) {
		this.success = success;
	}

	/**
	 * @param success
	 *            响应状态
	 */
	public ResponseMsg<T> success(Boolean success) {
		this.success = success;
		return this;
	}

	/**
	 * @return 返回数据
	 */
	public T getData() {
		return data;
	}

	/**
	 * @param object
	 *            返回数据
	 */
	public void setData(T data) {
		this.data = data;
	}

	/**
	 * 返回数据
	 * 
	 * @param data
	 * @return
	 */
	public ResponseMsg<T> data(T data) {
		this.data = data;
		return this;
	}

	/**
	 * @return 响应状态码
	 */
	public String getCode() {
		return code;
	}

	/**
	 * @param code
	 *            响应状态码
	 */
	public void setCode(String code) {
		this.code = code;
	}

	/**
	 * 响应状态码
	 * 
	 * @param code
	 * @return
	 */
	public ResponseMsg<T> code(String code) {
		this.code = code;
		return this;
	}

	/**
	 * 成功状态
	 * 
	 * @return
	 */
	public ResponseMsg<T> okStatus() {
		this.code = "200";
		this.success = true;
		return this;
	}

	/**
	 * 失败状态
	 * 
	 * @return
	 */
	public ResponseMsg<T> errorStatus() {
		this.code = "500";
		this.success = false;
		return this;
	}

	/**
	 * hashCode
	 */
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((code == null) ? 0 : code.hashCode());
		result = prime * result + ((data == null) ? 0 : data.hashCode());
		result = prime * result + ((message == null) ? 0 : message.hashCode());
		result = prime * result + ((success == null) ? 0 : success.hashCode());
		return result;
	}

	/**
	 * equals
	 */
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ResponseMsg other = (ResponseMsg) obj;
		if (code == null) {
			if (other.code != null)
				return false;
		} else if (!code.equals(other.code))
			return false;
		if (data == null) {
			if (other.data != null)
				return false;
		} else if (!data.equals(other.data))
			return false;
		if (message == null) {
			if (other.message != null)
				return false;
		} else if (!message.equals(other.message))
			return false;
		if (success == null) {
			if (other.success != null)
				return false;
		} else if (!success.equals(other.success))
			return false;
		return true;
	}

	/**
	 * toString
	 */
	@Override
	public String toString() {
		return "Response [message=" + message + ", success=" + success + ", data=" + data + ", code=" + code + "]";
	}

}
