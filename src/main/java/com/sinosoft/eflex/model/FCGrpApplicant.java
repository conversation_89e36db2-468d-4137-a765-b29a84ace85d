package com.sinosoft.eflex.model;

public class FCGrpApplicant {
    /**
     * 团体投保人编号
     */
    private String grpAppNo;

    /**
     * 企业客户号
     */
    private String grpNo;

    /**
     * 企业名称
     */
    private String grpName;

    /**
     * 企业地址
     */
    private String grpAddRess;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 统一社会信用代码
     */
    private String unifiedsociCode;

    /**
     * 单位证件类型
     */
    private String grpIdType;

    /**
     * 单位证件号码
     */
    private String grpIdNo;

    /**
     * 企业性质
     */
    private String grpType;

    /**
     * 开户账户名名称
     */
    private String accName;

    /**
     * 开户银行编码
     */
    private String grpBankCode;

    /**
     * 开户银行账号
     */
    private String grpBankAccNo;

    /**
     * 企业总人数
     */
    private Integer peoples;

    /**
     * 企业法人代表
     */
    private String corporationMan;

    /**
     * 企业注册电话
     */
    private String telphone;

    /**
     * 企业注册地址
     */
    private String regaddress;

    /**
     * 企业邮箱
     */
    private String email;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 业务员工号
     */
    private String clientno;

    /**
     * 证件有效起期
     */
    private String grpTypeStartDate;

    /**
     * 证件有效止期
     */
    private String grpTypeEndDate;

    /**
     * 企业成立日期
     */
    private String grpEstablishDate;

    /**
     * 企业规模类型
     */
    private String grpScaleType;

    /**
     * 参加社会统筹标志
     */
    private String sociologyPlanSign;

    /**
     * 注册资本
     */
    private String registeredCapital;

    /**
     * 客户类别
     */
    private String grpCategory;

    /**
     * 所属行业
     */
    private String trade;

    /**
     * 营业期限
     */
    private String businessTerm;

    /**
     * 操作机构
     */
    private String operatorCom;

    /**
     * 入机时间
     */
    private String makeTime;

    /**
     * 入机日期
     */
    private String makeDate;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 企业证件图片1(正面)
     */
    private String grpIDImage1;

    /**
     * 企业证件图片2(反面)
     */
    private String grpIDImage2;

    /**
     * 法人证件图1
     */
    private String legIDImage1;

    /**
     * 法人证件图2
     */
    private String legIDImage2;

    /**
     * 团体投保人编号
     *
     * @return GrpAppNo 团体投保人编号
     */
    public String getGrpAppNo() {
        return grpAppNo;
    }

    /**
     * 团体投保人编号
     *
     * @param grpAppNo 团体投保人编号
     */
    public void setGrpAppNo(String grpAppNo) {
        this.grpAppNo = grpAppNo;
    }

    /**
     * 企业客户号
     *
     * @return GrpNo 企业客户号
     */
    public String getGrpNo() {
        return grpNo;
    }

    /**
     * 企业客户号
     *
     * @param grpNo 企业客户号
     */
    public void setGrpNo(String grpNo) {
        this.grpNo = grpNo;
    }

    /**
     * 企业名称
     *
     * @return GrpName 企业名称
     */
    public String getGrpName() {
        return grpName;
    }

    /**
     * 企业名称
     *
     * @param grpName 企业名称
     */
    public void setGrpName(String grpName) {
        this.grpName = grpName;
    }

    /**
     * 企业地址
     *
     * @return GrpAddRess 企业地址
     */
    public String getGrpAddRess() {
        return grpAddRess;
    }

    /**
     * 企业地址
     *
     * @param grpAddRess 企业地址
     */
    public void setGrpAddRess(String grpAddRess) {
        this.grpAddRess = grpAddRess;
    }

    /**
     * 邮编
     *
     * @return ZipCode 邮编
     */
    public String getZipCode() {
        return zipCode;
    }

    /**
     * 邮编
     *
     * @param zipCode 邮编
     */
    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    /**
     * 统一社会信用代码
     *
     * @return UnifiedsociCode 统一社会信用代码
     */
    public String getUnifiedsociCode() {
        return unifiedsociCode;
    }

    /**
     * 统一社会信用代码
     *
     * @param unifiedsociCode 统一社会信用代码
     */
    public void setUnifiedsociCode(String unifiedsociCode) {
        this.unifiedsociCode = unifiedsociCode;
    }

    /**
     * 单位证件类型
     *
     * @return GrpIdType 单位证件类型
     */
    public String getGrpIdType() {
        return grpIdType;
    }

    /**
     * 单位证件类型
     *
     * @param grpIdType 单位证件类型
     */
    public void setGrpIdType(String grpIdType) {
        this.grpIdType = grpIdType;
    }

    /**
     * 单位证件号码
     *
     * @return GrpIdNo 单位证件号码
     */
    public String getGrpIdNo() {
        return grpIdNo;
    }

    /**
     * 单位证件号码
     *
     * @param grpIdNo 单位证件号码
     */
    public void setGrpIdNo(String grpIdNo) {
        this.grpIdNo = grpIdNo;
    }

    /**
     * 企业性质
     *
     * @return GrpType 企业性质
     */
    public String getGrpType() {
        return grpType;
    }

    /**
     * 企业性质
     *
     * @param grpType 企业性质
     */
    public void setGrpType(String grpType) {
        this.grpType = grpType;
    }

    /**
     * 开户账户名名称
     *
     * @return AccName 开户账户名名称
     */
    public String getAccName() {
        return accName;
    }

    /**
     * 开户账户名名称
     *
     * @param accName 开户账户名名称
     */
    public void setAccName(String accName) {
        this.accName = accName;
    }

    /**
     * 开户银行编码
     *
     * @return grpBankCode 开户银行编码
     */
    public String getGrpBankCode() {
        return grpBankCode;
    }

    /**
     * 开户银行编码
     *
     * @param grpBankCode 开户银行编码
     */
    public void setGrpBankCode(String grpBankCode) {
        this.grpBankCode = grpBankCode;
    }

    /**
     * 开户银行账号
     *
     * @return grpBankAccNo 开户银行账号
     */
    public String getGrpBankAccNo() {
        return grpBankAccNo;
    }

    /**
     * 开户银行账号
     *
     * @param grpBankAccNo 开户银行账号
     */
    public void setGrpBankAccNo(String grpBankAccNo) {
        this.grpBankAccNo = grpBankAccNo;
    }

    /**
     * 企业总人数
     *
     * @return Peoples 企业总人数
     */
    public Integer getPeoples() {
        return peoples;
    }

    /**
     * 企业总人数
     *
     * @param peoples 企业总人数
     */
    public void setPeoples(Integer peoples) {
        this.peoples = peoples;
    }

    /**
     * 企业法人代表
     *
     * @return CorporationMan 企业法人代表
     */
    public String getCorporationMan() {
        return corporationMan;
    }

    /**
     * 企业法人代表
     *
     * @param corporationMan 企业法人代表
     */
    public void setCorporationMan(String corporationMan) {
        this.corporationMan = corporationMan;
    }

    /**
     * 企业注册电话
     *
     * @return telphone 企业注册电话
     */
    public String getTelphone() {
        return telphone;
    }

    /**
     * 企业注册电话
     *
     * @param telphone 企业注册电话
     */
    public void setTelphone(String telphone) {
        this.telphone = telphone;
    }

    /**
     * 企业注册地址
     *
     * @return regaddress 企业注册地址
     */
    public String getRegaddress() {
        return regaddress;
    }

    /**
     * 企业注册地址
     *
     * @param regaddress 企业注册地址
     */
    public void setRegaddress(String regaddress) {
        this.regaddress = regaddress;
    }

    /**
     * 企业邮箱
     *
     * @return Email 企业邮箱
     */
    public String getEmail() {
        return email;
    }

    /**
     * 企业邮箱
     *
     * @param email 企业邮箱
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * 操作员
     *
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     *
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 业务员工号
     *
     * @return Clientno 业务员工号
     */
    public String getClientno() {
        return clientno;
    }

    /**
     * 业务员工号
     *
     * @param clientno 业务员工号
     */
    public void setClientno(String clientno) {
        this.clientno = clientno;
    }

    /**
     * 证件有效起期
     *
     * @return GrpTypeStartDate 证件有效起期
     */
    public String getGrpTypeStartDate() {
        return grpTypeStartDate;
    }

    /**
     * 证件有效起期
     *
     * @param grpTypeStartDate 证件有效起期
     */
    public void setGrpTypeStartDate(String grpTypeStartDate) {
        this.grpTypeStartDate = grpTypeStartDate;
    }

    /**
     * 证件有效止期
     *
     * @return GrpTypeEndDate 证件有效止期
     */
    public String getGrpTypeEndDate() {
        return grpTypeEndDate;
    }

    /**
     * 证件有效止期
     *
     * @param grpTypeEndDate 证件有效止期
     */
    public void setGrpTypeEndDate(String grpTypeEndDate) {
        this.grpTypeEndDate = grpTypeEndDate;
    }

    /**
     * 企业成立日期
     *
     * @return GrpEstablishDate 企业成立日期
     */
    public String getGrpEstablishDate() {
        return grpEstablishDate;
    }

    /**
     * 企业成立日期
     *
     * @param grpEstablishDate 企业成立日期
     */
    public void setGrpEstablishDate(String grpEstablishDate) {
        this.grpEstablishDate = grpEstablishDate;
    }

    /**
     * 企业规模类型
     *
     * @return GrpScaleType 企业规模类型
     */
    public String getGrpScaleType() {
        return grpScaleType;
    }

    /**
     * 企业规模类型
     *
     * @param grpScaleType 企业规模类型
     */
    public void setGrpScaleType(String grpScaleType) {
        this.grpScaleType = grpScaleType;
    }

    /**
     * 参加社会统筹标志
     *
     * @return SociologyPlanSign 参加社会统筹标志
     */
    public String getSociologyPlanSign() {
        return sociologyPlanSign;
    }

    /**
     * 参加社会统筹标志
     *
     * @param sociologyPlanSign 参加社会统筹标志
     */
    public void setSociologyPlanSign(String sociologyPlanSign) {
        this.sociologyPlanSign = sociologyPlanSign;
    }

    /**
     * 注册资本
     *
     * @return RegisteredCapital 注册资本
     */
    public String getRegisteredCapital() {
        return registeredCapital;
    }

    /**
     * 注册资本
     *
     * @param registeredCapital 注册资本
     */
    public void setRegisteredCapital(String registeredCapital) {
        this.registeredCapital = registeredCapital;
    }

    /**
     * 客户类别
     *
     * @return GrpCategory 客户类别
     */
    public String getGrpCategory() {
        return grpCategory;
    }

    /**
     * 客户类别
     *
     * @param grpCategory 客户类别
     */
    public void setGrpCategory(String grpCategory) {
        this.grpCategory = grpCategory;
    }

    /**
     * 所属行业
     *
     * @return Trade 所属行业
     */
    public String getTrade() {
        return trade;
    }

    /**
     * 所属行业
     *
     * @param trade 所属行业
     */
    public void setTrade(String trade) {
        this.trade = trade;
    }

    /**
     * 营业期限
     *
     * @return BusinessTerm 营业期限
     */
    public String getBusinessTerm() {
        return businessTerm;
    }

    /**
     * 营业期限
     *
     * @param businessTerm 营业期限
     */
    public void setBusinessTerm(String businessTerm) {
        this.businessTerm = businessTerm;
    }

    /**
     * 操作机构
     *
     * @return OperatorCom 操作机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作机构
     *
     * @param operatorCom 操作机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 入机时间
     *
     * @return MakeTime 入机时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 入机时间
     *
     * @param makeTime 入机时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 入机日期
     *
     * @return MakeDate 入机日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 入机日期
     *
     * @param makeDate 入机日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 修改日期
     *
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     *
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 修改时间
     *
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     *
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    /**
     * 企业证件图片1(正面)
     *
     * @return grpIDImage1 企业证件图片1(正面)
     */
    public String getGrpIDImage1() {
        return grpIDImage1;
    }

    /**
     * 企业证件图片1(正面)
     *
     * @param grpIDImage1 企业证件图片1(正面)
     */
    public void setGrpIDImage1(String grpIDImage1) {
        this.grpIDImage1 = grpIDImage1;
    }

    /**
     * 企业证件图片2(反面)
     *
     * @return grpIDImage2 企业证件图片2(反面)
     */
    public String getGrpIDImage2() {
        return grpIDImage2;
    }

    /**
     * 企业证件图片2(反面)
     *
     * @param grpIDImage2 企业证件图片2(反面)
     */
    public void setGrpIDImage2(String grpIDImage2) {
        this.grpIDImage2 = grpIDImage2;
    }

    /**
     * 法人证件图1
     *
     * @return LegIDImage1 法人证件图1
     */
    public String getLegIDImage1() {
        return legIDImage1;
    }

    /**
     * 法人证件图1
     *
     * @param legIDImage1 法人证件图1
     */
    public void setLegIDImage1(String legIDImage1) {
        this.legIDImage1 = legIDImage1;
    }

    /**
     * 法人证件图2
     *
     * @return LegIDImage2 法人证件图2
     */
    public String getLegIDImage2() {
        return legIDImage2;
    }

    /**
     * 法人证件图2
     *
     * @param legIDImage2 法人证件图2
     */
    public void setLegIDImage2(String legIDImage2) {
        this.legIDImage2 = legIDImage2;
    }
}