package com.sinosoft.eflex.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FcHrRegistTemp {
    /**
     * 注册编号
     */
    private String registSN;
    /**
     * 主营业务
     */
    private String businesses;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobilePhone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 国籍
     */
    private String nativeplace;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 证件有效起期
     */
    private String idTypeStartDate;

    /**
     * 证件有效期
     */
    private String idTypeEndDate;

    /**
     * 性别
     */
    private String sex;

    /**
     * 出生日期
     */
    private String birthday;

    /**
     * 所属部门
     */
    private String department;

    /**
     * 企业名称
     */
    private String grpName;

    /**
     * 企业证件类型
     */
    private String grpIdType;

    /**
     * 企业证件号
     */
    private String grpIdNo;

    /**
     * 企业邮编
     */
    private String zipCode;

    /**
     * 企业地址
     */
    private String grpAddress;
    /**
     * 企业注册地
     */
    private String grpRegisterAddress;

    /**
     * 企业统一社会信用代码
     */
    private String unifiedsociCode;

    /**
     * 企业性质
     */
    private String grpType;

    /**
     * 企业组织形式
     */
    private String grpNatureType;

    /**
     * 开户账户
     */
    private String accName;

    /**
     * 开户银行
     */
    private String grpBankcode;

    /**
     * 开户账号
     */
    private String grpBankaccno;

    /**
     * 固定电话
     */
    private String telphone;

    /**
     * 注册地址
     */
    private String regAddress;

    /**
     * 业务员编号
     */
    private String clientNo;

    /**
     * 法人名称
     */
    private String corporationMan;

    /**
     * 0-待审核；1-审核通过；2-审核不通过
     */
    private String checkStatus;

    /**
     * 证件有效起期
     */
    private String grpTypeStartDate;

    /**
     * 证件有效止期
     */
    private String grpTypeEndDate;

    /**
     * 企业成立日期
     */
    private String grpEstablishDate;

    /**
     * 企业规模类型
     */
    private String grpScaleType;

    /**
     * 参加社会统筹标志
     */
    private String sociologyPlanSign;

    /**
     * 注册资本
     */
    private String registeredCapital;

    /**
     * 客户类别
     */
    private String grpCategory;

    /**
     * 操作员所属机构
     */
    private String operatorCom;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 创建日期
     */
    private String makeDate;

    /**
     * 创建时间
     */
    private String makeTime;

    /**
     * 修改日期
     */
    private String modifyDate;

    /**
     *
     */
    private String auditOpinion;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 企业客户号
     */
    private String grpNo;

    /**
     * 企业联系人编号
     */
    private String grpContactNo;

    /**
     * 营业期限
     */
    private String businessTerm;

    /**
     * 所属行业
     */
    private String trade;

    /**
     * 人数
     */
    private Long peoples;

    /**
     * 股东业务
     */
    private String shareBusiness;

    /**
     * 股东名称
     */
    private String shareholdersName;

    /**
     * 经办人影像件
     */
    private String idImage1;

    /**
     * 经办人影像件
     */
    private String idImage2;

    /**
     * 企业影像件
     */
    private String grpIDImage1;

    /**
     * 法人影像件1
     */
    private String legIDImage1;

    /**
     * 企业影像件
     */
    private String grpIDImage2;

    /**
     * 法人影像件2
     */
    private String legIDImage2;


    /**
     * 企业证件图片1
     */
    private String grpImageFront;

    /**
     * 企业证件图片2
     */
    private String grpImageBack;

    private String idCardFront;

    /**
     * 法人影像件反面*
     */
    private String legalImgBack;
    /**
     * 法人影像件正面*
     */
    private String legalImgFront;

    /**
     * 证件图片2
     */
    private String idCardBack;

    /**
     * 修改企业注册信息表时需要
     */
    // 旧的经办人证件类型
    private String oldIdType;
    // 旧的经办人证件号
    private String oldIdNo;
    // 旧的企业证件类型
    private String oldGrpIdType;
    // 旧的企业证件号
    private String oldGrpIdNo;


    /**
     * 企业法定代表人证件号
     */
    private String legID;
    /**
     * 企业法定代表人证件类型
     */
    private String legIDType;
    /**
     * 企业法定代表人性别
     */
    private String legSex;
    /**
     * 企业法定代表人证出生日期
     */
    private String legBirthday;
    /**
     * 企业法定代表人国籍
     */
    private String legNationality;
    /**
     * 企业法定代表人证件有效起期
     */
    private String legIDStartDate;
    /**
     * 企业法定代表人证件有效止期
     */
    private String legIDEndDate;

    public String getLegID() {
        return legID;
    }

    public void setLegID(String legID) {
        this.legID = legID;
    }

    public String getLegIDType() {
        return legIDType;
    }

    public void setLegIDType(String legIDType) {
        this.legIDType = legIDType;
    }

    public String getLegSex() {
        return legSex;
    }

    public void setLegSex(String legSex) {
        this.legSex = legSex;
    }

    public String getLegBirthday() {
        return legBirthday;
    }

    public void setLegBirthday(String legBirthday) {
        this.legBirthday = legBirthday;
    }

    public String getLegNationality() {
        return legNationality;
    }

    public void setLegNationality(String legNationality) {
        this.legNationality = legNationality;
    }

    public String getLegIDStartDate() {
        return legIDStartDate;
    }

    public void setLegIDStartDate(String legIDStartDate) {
        this.legIDStartDate = legIDStartDate;
    }

    public String getLegIDEndDate() {
        return legIDEndDate;
    }

    public void setLegIDEndDate(String legIDEndDate) {
        this.legIDEndDate = legIDEndDate;
    }

    public String getOldIdType() {
        return oldIdType;
    }

    public void setOldIdType(String oldIdType) {
        this.oldIdType = oldIdType;
    }

    public String getOldIdNo() {
        return oldIdNo;
    }

    public void setOldIdNo(String oldIdNo) {
        this.oldIdNo = oldIdNo;
    }

    public String getOldGrpIdType() {
        return oldGrpIdType;
    }

    public void setOldGrpIdType(String oldGrpIdType) {
        this.oldGrpIdType = oldGrpIdType;
    }

    public String getOldGrpIdNo() {
        return oldGrpIdNo;
    }

    public void setOldGrpIdNo(String oldGrpIdNo) {
        this.oldGrpIdNo = oldGrpIdNo;
    }

    /**
     * 注册编号
     *
     * @return RegistSN 注册编号
     */
    public String getRegistSN() {
        return registSN;
    }

    /**
     * 注册编号
     *
     * @param registSN 注册编号
     */
    public void setRegistSN(String registSN) {
        this.registSN = registSN;
    }

    /**
     * 姓名
     *
     * @return name 姓名
     */
    public String getName() {
        return name;
    }

    /**
     * 姓名
     *
     * @param name 姓名
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 手机号
     *
     * @return mobilePhone 手机号
     */
    public String getMobilePhone() {
        return mobilePhone;
    }

    /**
     * 手机号
     *
     * @param mobilePhone 手机号
     */
    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    /**
     * 邮箱
     *
     * @return email 邮箱
     */
    public String getEmail() {
        return email;
    }

    /**
     * 邮箱
     *
     * @param email 邮箱
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * 国籍
     *
     * @return nativeplace 国籍
     */
    public String getNativeplace() {
        return nativeplace;
    }

    /**
     * 国籍
     *
     * @param nativeplace 国籍
     */
    public void setNativeplace(String nativeplace) {
        this.nativeplace = nativeplace;
    }

    /**
     * 证件类型
     *
     * @return idType 证件类型
     */
    public String getIdType() {
        return idType;
    }

    /**
     * 证件类型
     *
     * @param idType 证件类型
     */
    public void setIdType(String idType) {
        this.idType = idType;
    }

    /**
     * 证件号
     *
     * @return idNo 证件号
     */
    public String getIdNo() {
        return idNo;
    }

    /**
     * 证件号
     *
     * @param idNo 证件号
     */
    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    /**
     * 证件有效起期
     *
     * @return idTypeStartDate 证件有效起期
     */
    public String getIdTypeStartDate() {
        return idTypeStartDate;
    }

    /**
     * 证件有效起期
     *
     * @param idTypeStartDate 证件有效起期
     */
    public void setIdTypeStartDate(String idTypeStartDate) {
        this.idTypeStartDate = idTypeStartDate;
    }

    /**
     * 证件有效期
     *
     * @return idTypeEndDate 证件有效期
     */
    public String getIdTypeEndDate() {
        return idTypeEndDate;
    }

    /**
     * 证件有效期
     *
     * @param idTypeEndDate 证件有效期
     */
    public void setIdTypeEndDate(String idTypeEndDate) {
        this.idTypeEndDate = idTypeEndDate;
    }

    /**
     * 性别
     *
     * @return sex 性别
     */
    public String getSex() {
        return sex;
    }

    /**
     * 性别
     *
     * @param sex 性别
     */
    public void setSex(String sex) {
        this.sex = sex;
    }

    /**
     * 出生日期
     *
     * @return birthday 出生日期
     */
    public String getBirthday() {
        return birthday;
    }

    /**
     * 出生日期
     *
     * @param birthday 出生日期
     */
    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    /**
     * 所属部门
     *
     * @return department 所属部门
     */
    public String getDepartment() {
        return department;
    }

    /**
     * 所属部门
     *
     * @param department 所属部门
     */
    public void setDepartment(String department) {
        this.department = department;
    }

    /**
     * 企业名称
     *
     * @return grpName 企业名称
     */
    public String getGrpName() {
        return grpName;
    }

    /**
     * 企业名称
     *
     * @param grpName 企业名称
     */
    public void setGrpName(String grpName) {
        this.grpName = grpName;
    }

    /**
     * 企业证件类型
     *
     * @return grpIdType 企业证件类型
     */
    public String getGrpIdType() {
        return grpIdType;
    }

    /**
     * 企业证件类型
     *
     * @param grpIdType 企业证件类型
     */
    public void setGrpIdType(String grpIdType) {
        this.grpIdType = grpIdType;
    }

    /**
     * 企业证件号
     *
     * @return grpIdNo 企业证件号
     */
    public String getGrpIdNo() {
        return grpIdNo;
    }

    /**
     * 企业证件号
     *
     * @param grpIdNo 企业证件号
     */
    public void setGrpIdNo(String grpIdNo) {
        this.grpIdNo = grpIdNo;
    }

    /**
     * 企业邮编
     *
     * @return zipCode 企业邮编
     */
    public String getZipCode() {
        return zipCode;
    }

    /**
     * 企业邮编
     *
     * @param zipCode 企业邮编
     */
    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    /**
     * 企业地址
     *
     * @return grpAddress 企业地址
     */
    public String getGrpAddress() {
        return grpAddress;
    }

    /**
     * 企业地址
     *
     * @param grpAddress 企业地址
     */
    public void setGrpAddress(String grpAddress) {
        this.grpAddress = grpAddress;
    }

    /**
     * 企业统一社会信用代码
     *
     * @return unifiedsociCode 企业统一社会信用代码
     */
    public String getUnifiedsociCode() {
        return unifiedsociCode;
    }

    /**
     * 企业统一社会信用代码
     *
     * @param unifiedsociCode 企业统一社会信用代码
     */
    public void setUnifiedsociCode(String unifiedsociCode) {
        this.unifiedsociCode = unifiedsociCode;
    }

    /**
     * 企业性质
     *
     * @return grpType 企业性质
     */
    public String getGrpType() {
        return grpType;
    }

    /**
     * 企业性质
     *
     * @param grpType 企业性质
     */
    public void setGrpType(String grpType) {
        this.grpType = grpType;
    }

    /**
     * 开户账户
     *
     * @return accName 开户账户
     */
    public String getAccName() {
        return accName;
    }

    /**
     * 开户账户
     *
     * @param accName 开户账户
     */
    public void setAccName(String accName) {
        this.accName = accName;
    }

    /**
     * 开户银行
     *
     * @return grpBankcode 开户银行
     */
    public String getGrpBankcode() {
        return grpBankcode;
    }

    /**
     * 开户银行
     *
     * @param grpBankcode 开户银行
     */
    public void setGrpBankcode(String grpBankcode) {
        this.grpBankcode = grpBankcode;
    }

    /**
     * 开户账号
     *
     * @return grpBankaccno 开户账号
     */
    public String getGrpBankaccno() {
        return grpBankaccno;
    }

    /**
     * 开户账号
     *
     * @param grpBankaccno 开户账号
     */
    public void setGrpBankaccno(String grpBankaccno) {
        this.grpBankaccno = grpBankaccno;
    }

    /**
     * 固定电话
     *
     * @return telphone 固定电话
     */
    public String getTelphone() {
        return telphone;
    }

    /**
     * 固定电话
     *
     * @param telphone 固定电话
     */
    public void setTelphone(String telphone) {
        this.telphone = telphone;
    }

    /**
     * 注册地址
     *
     * @return regAddress 注册地址
     */
    public String getRegAddress() {
        return regAddress;
    }

    /**
     * 注册地址
     *
     * @param regAddress 注册地址
     */
    public void setRegAddress(String regAddress) {
        this.regAddress = regAddress;
    }

    /**
     * 业务员编号
     *
     * @return clientNo 业务员编号
     */
    public String getClientNo() {
        return clientNo;
    }

    /**
     * 业务员编号
     *
     * @param clientNo 业务员编号
     */
    public void setClientNo(String clientNo) {
        this.clientNo = clientNo;
    }

    /**
     * 法人名称
     *
     * @return corporationMan 法人名称
     */
    public String getCorporationMan() {
        return corporationMan;
    }

    /**
     * 法人名称
     *
     * @param corporationMan 法人名称
     */
    public void setCorporationMan(String corporationMan) {
        this.corporationMan = corporationMan;
    }

    /**
     * @return checkStatus
     */
    public String getCheckStatus() {
        return checkStatus;
    }

    /**
     * @param checkStatus
     */
    public void setCheckStatus(String checkStatus) {
        this.checkStatus = checkStatus;
    }

    /**
     * 证件有效起期
     *
     * @return GrpTypeStartDate 证件有效起期
     */
    public String getGrpTypeStartDate() {
        return grpTypeStartDate;
    }

    /**
     * 证件有效起期
     *
     * @param grpTypeStartDate 证件有效起期
     */
    public void setGrpTypeStartDate(String grpTypeStartDate) {
        this.grpTypeStartDate = grpTypeStartDate;
    }

    /**
     * 证件有效止期
     *
     * @return GrpTypeEndDate 证件有效止期
     */
    public String getGrpTypeEndDate() {
        return grpTypeEndDate;
    }

    /**
     * 证件有效止期
     *
     * @param grpTypeEndDate 证件有效止期
     */
    public void setGrpTypeEndDate(String grpTypeEndDate) {
        this.grpTypeEndDate = grpTypeEndDate;
    }

    /**
     * 企业成立日期
     *
     * @return GrpEstablishDate 企业成立日期
     */
    public String getGrpEstablishDate() {
        return grpEstablishDate;
    }

    /**
     * 企业成立日期
     *
     * @param grpEstablishDate 企业成立日期
     */
    public void setGrpEstablishDate(String grpEstablishDate) {
        this.grpEstablishDate = grpEstablishDate;
    }

    /**
     * 企业规模类型
     *
     * @return GrpScaleType 企业规模类型
     */
    public String getGrpScaleType() {
        return grpScaleType;
    }

    /**
     * 企业规模类型
     *
     * @param grpScaleType 企业规模类型
     */
    public void setGrpScaleType(String grpScaleType) {
        this.grpScaleType = grpScaleType;
    }

    /**
     * 参加社会统筹标志
     *
     * @return SociologyPlanSign 参加社会统筹标志
     */
    public String getSociologyPlanSign() {
        return sociologyPlanSign;
    }

    /**
     * 参加社会统筹标志
     *
     * @param sociologyPlanSign 参加社会统筹标志
     */
    public void setSociologyPlanSign(String sociologyPlanSign) {
        this.sociologyPlanSign = sociologyPlanSign;
    }

    /**
     * 注册资本
     *
     * @return RegisteredCapital 注册资本
     */
    public String getRegisteredCapital() {
        return registeredCapital;
    }

    /**
     * 注册资本
     *
     * @param registeredCapital 注册资本
     */
    public void setRegisteredCapital(String registeredCapital) {
        this.registeredCapital = registeredCapital;
    }

    /**
     * 客户类别
     *
     * @return GrpCategory 客户类别
     */
    public String getGrpCategory() {
        return grpCategory;
    }

    /**
     * 客户类别
     *
     * @param grpCategory 客户类别
     */
    public void setGrpCategory(String grpCategory) {
        this.grpCategory = grpCategory;
    }

    /**
     * 操作员所属机构
     *
     * @return OperatorCom 操作员所属机构
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 操作员所属机构
     *
     * @param operatorCom 操作员所属机构
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 操作员
     *
     * @return Operator 操作员
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 操作员
     *
     * @param operator 操作员
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 创建日期
     *
     * @return MakeDate 创建日期
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 创建日期
     *
     * @param makeDate 创建日期
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 创建时间
     *
     * @return MakeTime 创建时间
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 创建时间
     *
     * @param makeTime 创建时间
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 修改日期
     *
     * @return ModifyDate 修改日期
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 修改日期
     *
     * @param modifyDate 修改日期
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * @return auditOpinion
     */
    public String getAuditOpinion() {
        return auditOpinion;
    }

    /**
     * @param auditOpinion
     */
    public void setAuditOpinion(String auditOpinion) {
        this.auditOpinion = auditOpinion;
    }

    /**
     * 修改时间
     *
     * @return ModifyTime 修改时间
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     *
     * @param modifyTime 修改时间
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    /**
     * 企业客户号
     *
     * @return grpNo 企业客户号
     */
    public String getGrpNo() {
        return grpNo;
    }

    /**
     * 企业客户号
     *
     * @param grpNo 企业客户号
     */
    public void setGrpNo(String grpNo) {
        this.grpNo = grpNo;
    }

    /**
     * 企业联系人编号
     *
     * @return grpContactNo 企业联系人编号
     */
    public String getGrpContactNo() {
        return grpContactNo;
    }

    /**
     * 企业联系人编号
     *
     * @param grpContactNo 企业联系人编号
     */
    public void setGrpContactNo(String grpContactNo) {
        this.grpContactNo = grpContactNo;
    }

    /**
     * 营业期限
     *
     * @return BusinessTerm 营业期限
     */
    public String getBusinessTerm() {
        return businessTerm;
    }

    /**
     * 营业期限
     *
     * @param businessTerm 营业期限
     */
    public void setBusinessTerm(String businessTerm) {
        this.businessTerm = businessTerm;
    }

    /**
     * 所属行业
     *
     * @return Trade 所属行业
     */
    public String getTrade() {
        return trade;
    }

    /**
     * 所属行业
     *
     * @param trade 所属行业
     */
    public void setTrade(String trade) {
        this.trade = trade;
    }

    /**
     * 人数
     *
     * @return Peoples 人数
     */
    public Long getPeoples() {
        return peoples;
    }

    /**
     * 人数
     *
     * @param peoples 人数
     */
    public void setPeoples(Long peoples) {
        this.peoples = peoples;
    }

    /**
     * 股东业务
     *
     * @return shareBusiness 股东业务
     */
    public String getShareBusiness() {
        return shareBusiness;
    }

    /**
     * 股东业务
     *
     * @param shareBusiness 股东业务
     */
    public void setShareBusiness(String shareBusiness) {
        this.shareBusiness = shareBusiness;
    }

    /**
     * 股东名称
     *
     * @return shareholdersName 股东名称
     */
    public String getShareholdersName() {
        return shareholdersName;
    }

    /**
     * 股东名称
     *
     * @param shareholdersName 股东名称
     */
    public void setShareholdersName(String shareholdersName) {
        this.shareholdersName = shareholdersName;
    }

    /**
     * 经办人影像件
     *
     * @return idImage1 经办人影像件
     */
    public String getIdImage1() {
        return idImage1;
    }

    /**
     * 经办人影像件
     *
     * @param idImage1 经办人影像件
     */
    public void setIdImage1(String idImage1) {
        this.idImage1 = idImage1;
    }

    /**
     * 经办人影像件
     *
     * @return idImage2 经办人影像件
     */
    public String getIdImage2() {
        return idImage2;
    }

    /**
     * 经办人影像件
     *
     * @param idImage2 经办人影像件
     */
    public void setIdImage2(String idImage2) {
        this.idImage2 = idImage2;
    }

    /**
     * 企业影像件
     *
     * @return grpIDImage1 企业影像件
     */
    public String getGrpIDImage1() {
        return grpIDImage1;
    }

    /**
     * 企业影像件
     *
     * @param grpIDImage1 企业影像件
     */
    public void setGrpIDImage1(String grpIDImage1) {
        this.grpIDImage1 = grpIDImage1;
    }

    /**
     * 法人影像件1
     *
     * @return legIDImage1 法人影像件1
     */
    public String getLegIDImage1() {
        return legIDImage1;
    }

    /**
     * 法人影像件1
     *
     * @param legIDImage1 法人影像件1
     */
    public void setLegIDImage1(String legIDImage1) {
        this.legIDImage1 = legIDImage1;
    }

    /**
     * 企业影像件
     *
     * @return grpIDImage2 企业影像件
     */
    public String getGrpIDImage2() {
        return grpIDImage2;
    }

    /**
     * 企业影像件
     *
     * @param grpIDImage2 企业影像件
     */
    public void setGrpIDImage2(String grpIDImage2) {
        this.grpIDImage2 = grpIDImage2;
    }

    /**
     * 法人影像件2
     *
     * @return legIDImage2 法人影像件2
     */
    public String getLegIDImage2() {
        return legIDImage2;
    }

    /**
     * 法人影像件2
     *
     * @param legIDImage2 法人影像件2
     */
    public void setLegIDImage2(String legIDImage2) {
        this.legIDImage2 = legIDImage2;
    }

    public String getGrpRegisterAddress() {
        return grpRegisterAddress;
    }

    public void setGrpRegisterAddress(String grpRegisterAddress) {
        this.grpRegisterAddress = grpRegisterAddress;
    }
}