package com.sinosoft.eflex.model.sendmessage;

import java.util.Map;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2020/12/21
 * 发送邮件请求对象
 */
@Data
public class SendEmailReq {

    //产品名称
    private String product;
    //应用名称
    private String application;
    //秘钥
    private String appKey;
    //模板号
    private String templateNo;

    //模板参数
    private Map<String,Object> param;

    //手机号信息（请求时封装使用）
    private SendEmailContent content;


}
