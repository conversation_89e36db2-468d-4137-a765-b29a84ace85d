package com.sinosoft.eflex.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FCPerinfoFamilyTemp {
    private String edorType;
    private String oldIdNo;
    /**
     * 家属临时编号
     */
    private String familyTempNo;

    /**
     * 与投保人关系默认其他
     */
    private String relationship;

    /**
     * 员工临时编号
     */
    private String perTempNo;

    /**
     * 员工临时编号  修改之前
     */
    private String oldPerTempNo;

    /**
     * 福利编号
     */
    private String ensureCode;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private String sex;

    /**
     * 证件类型
     */
    private String iDType;

    /**
     * 证件号
     */
    private String iDNo;

    /**
     * 证件有效期
     */
    private String idTypeEndDate;

    /**
     * 出生日期
     */
    private String birthDay;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 职业类型
     */
    private String occupationType;

    /**
     * 职业代码
     */
    private String occupationCode;

    /**
     * 有无医保  有-0  无-1
     */
    private String joinMedProtect;

    /**
     * 国籍
     */
    private String nativeplace;

    private String nativeplaceName;

    /**
     * 服务年限
     */
    private String serviceTerm;



    public String getServiceTerm() {
        return serviceTerm;
    }

	public void setServiceTerm(String serviceTerm) {
		this.serviceTerm = serviceTerm;
	}

	public String getRetirement() {
		return retirement;
	}

	public void setRetirement(String retirement) {
		this.retirement = retirement;
	}

	/**
     * 是否退休
     */
    private String retirement;

    /**
     * 与员工关系
     */
    private String relation;

    /**
     * 员工姓名
     */
    private String perName;

    /**
     * 员工证件类型
     */
    private String perIDType;

    /**
     * 员工证件号
     */
    private String perIDNo;

    /**
     * 提交状态(未提交-01 已提交-02)
     */
    private String subStaus;

    /**
     * 
     */
    private String operatorCom;

    /**
     * 
     */
    private String operator;

    /**
     * 
     */
    private String makeDate;

    /**
     * 
     */
    private String makeTime;

    /**
     * 
     */
    private String modifyDate;

    /**
     * 
     */
    private String modifyTime;

    /**
     * 家属临时编号
     * @return FamilyTempNo 家属临时编号
     */
    public String getFamilyTempNo() {
        return familyTempNo;
    }

    /**
     * 家属临时编号
     * @param familyTempNo 家属临时编号
     */
    public void setFamilyTempNo(String familyTempNo) {
        this.familyTempNo = familyTempNo;
    }

    public String getPerTempNo() {
        return perTempNo;
    }

    public void setPerTempNo(String perTempNo) {
        this.perTempNo = perTempNo;
    }

    public String getOldPerTempNo() {
        return oldPerTempNo;
    }

    public void setOldPerTempNo(String oldPerTempNo) {
        this.oldPerTempNo = oldPerTempNo;
    }

    /**
     * 福利编号
     * @return EnsureCode 福利编号
     */
    public String getEnsureCode() {
        return ensureCode;
    }

    /**
     * 福利编号
     * @param ensureCode 福利编号
     */
    public void setEnsureCode(String ensureCode) {
        this.ensureCode = ensureCode;
    }

    /**
     * 姓名
     * @return Name 姓名
     */
    public String getName() {
        return name;
    }

    /**
     * 姓名
     * @param name 姓名
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 性别
     * @return Sex 性别
     */
    public String getSex() {
        return sex;
    }

    /**
     * 性别
     * @param sex 性别
     */
    public void setSex(String sex) {
        this.sex = sex;
    }

    /**
     * 证件类型
     * @return IDType 证件类型
     */
    public String getIDType() {
        return iDType;
    }

    /**
     * 证件类型
     * @param IDType 证件类型
     */
    public void setIDType(String IDType) {
        this.iDType = IDType;
    }

    /**
     * 证件号
     * @return IDNo 证件号
     */
    public String getIDNo() {
        return iDNo;
    }

    /**
     * 证件号
     * @param IDNo 证件号
     */
    public void setIDNo(String IDNo) {
        this.iDNo = IDNo;
    }

    /**
     * 证件有效期
     * @return
     */
    public String getIdTypeEndDate() {
        return idTypeEndDate;
    }

    /**
     * 证件有效期
     * @param idTypeEndDate
     */
    public void setIdTypeEndDate(String idTypeEndDate) {
        this.idTypeEndDate = idTypeEndDate;
    }

    /**
     * 出生日期
     * @return BirthDay 出生日期
     */
    public String getBirthDay() {
        return birthDay;
    }

    /**
     * 出生日期
     * @param birthDay 出生日期
     */
    public void setBirthDay(String birthDay) {
        this.birthDay = birthDay;
    }

    /**
     * 手机号
     * @return Phone 手机号
     */
    public String getPhone() {
        return phone;
    }

    /**
     * 手机号
     * @param phone 手机号
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * 职业类型
     * @return OccupationType 职业类型
     */
    public String getOccupationType() {
        return occupationType;
    }

    /**
     * 职业类型
     * @param occupationType 职业类型
     */
    public void setOccupationType(String occupationType) {
        this.occupationType = occupationType;
    }

    /**
     * 职业代码
     * @return OccupationCode 职业代码
     */
    public String getOccupationCode() {
        return occupationCode;
    }

    /**
     * 职业代码
     * @param occupationCode 职业代码
     */
    public void setOccupationCode(String occupationCode) {
        this.occupationCode = occupationCode;
    }

    /**
     * 有无医保  有-0  无-1
     * @return JoinMedProtect 有无医保  有-0  无-1
     */
    public String getJoinMedProtect() {
        return joinMedProtect;
    }

    /**
     * 有无医保  有-0  无-1
     * @param joinMedProtect 有无医保  有-0  无-1
     */
    public void setJoinMedProtect(String joinMedProtect) {
        this.joinMedProtect = joinMedProtect;
    }

    public String getNativeplace() {
        return nativeplace;
    }

    public void setNativeplace(String nativeplace) {
        this.nativeplace = nativeplace;
    }

    public String getNativeplaceName() {
        return nativeplaceName;
    }

    public void setNativeplaceName(String nativeplaceName) {
        this.nativeplaceName = nativeplaceName;
    }

    /**
     * 与员工关系
     *
     * @return Relation 与员工关系
     */
    public String getRelation() {
        return relation;
    }

    /**
     * 与员工关系
     * @param relation 与员工关系
     */
    public void setRelation(String relation) {
        this.relation = relation;
    }

    /**
     * 员工姓名
     * @return PerName 员工姓名
     */
    public String getPerName() {
        return perName;
    }

    /**
     * 员工姓名
     * @param perName 员工姓名
     */
    public void setPerName(String perName) {
        this.perName = perName;
    }

    /**
     * 员工证件类型
     * @return PerIDType 员工证件类型
     */
    public String getPerIDType() {
        return perIDType;
    }

    /**
     * 员工证件类型
     * @param perIDType 员工证件类型
     */
    public void setPerIDType(String perIDType) {
        this.perIDType = perIDType;
    }

    /**
     * 员工证件号
     * @return PerIDNo 员工证件号
     */
    public String getPerIDNo() {
        return perIDNo;
    }

    /**
     * 员工证件号
     * @param perIDNo 员工证件号
     */
    public void setPerIDNo(String perIDNo) {
        this.perIDNo = perIDNo;
    }

    /**
     * 提交状态(未提交-01 已提交-02)
     * @return SubStaus 提交状态(未提交-01 已提交-02)
     */
    public String getSubStaus() {
        return subStaus;
    }

    /**
     * 提交状态(未提交-01 已提交-02)
     * @param subStaus 提交状态(未提交-01 已提交-02)
     */
    public void setSubStaus(String subStaus) {
        this.subStaus = subStaus;
    }

    /**
     * 
     * @return OperatorCom 
     */
    public String getOperatorCom() {
        return operatorCom;
    }

    /**
     * 
     * @param operatorCom 
     */
    public void setOperatorCom(String operatorCom) {
        this.operatorCom = operatorCom;
    }

    /**
     * 
     * @return Operator 
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 
     * @param operator 
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 
     * @return MakeDate 
     */
    public String getMakeDate() {
        return makeDate;
    }

    /**
     * 
     * @param makeDate 
     */
    public void setMakeDate(String makeDate) {
        this.makeDate = makeDate;
    }

    /**
     * 
     * @return MakeTime 
     */
    public String getMakeTime() {
        return makeTime;
    }

    /**
     * 
     * @param makeTime 
     */
    public void setMakeTime(String makeTime) {
        this.makeTime = makeTime;
    }

    /**
     * 
     * @return ModifyDate 
     */
    public String getModifyDate() {
        return modifyDate;
    }

    /**
     * 
     * @param modifyDate 
     */
    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    /**
     * 
     * @return ModifyTime 
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * 
     * @param modifyTime 
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public FCPerinfoFamilyTemp(String ensureCode, String phone) {
        this.ensureCode = ensureCode;
        this.phone = phone;
    }
}