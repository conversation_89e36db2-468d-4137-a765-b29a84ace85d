package com.sinosoft.eflex.model;

import java.util.List;

public class PersonTest {

	private Integer pid;

	private String name;

	private String age;

	private String sex;

	private List<PersonTestBean> personTestBeanList;

	public List<PersonTestBean> getPersonTestBeanList() {
		return personTestBeanList;
	}

	public void setPersonTestBeanList(List<PersonTestBean> personTestBeanList) {
		this.personTestBeanList = personTestBeanList;
	}

	public Integer getPid() {
		return pid;
	}

	public void setPid(Integer pid) {
		this.pid = pid;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getAge() {
		return age;
	}

	public void setAge(String age) {
		this.age = age;
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

}