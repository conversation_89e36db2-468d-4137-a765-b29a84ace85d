package com.sinosoft.eflex.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR> 用户登录实体
 */
@ApiModel(value = "User", description = "用户登录对象")
public class User {

	/** 登录用户名 */
	@ApiModelProperty(value = "用户名")
	private String userName;
	/** 登录密码 */
	@ApiModelProperty(value = "密码")
	private String password;
	/** 用户类型 */
	@ApiModelProperty(value = "用户类型")
	private String userType;
	/** 图片验证码 */
	@ApiModelProperty(value = "图片验证码")
	private String vaildCode;
	/** 图片验证码token */
	@ApiModelProperty(value = "图片验证码token")
	private String vToken;
	/** 手机验证码 */
	@ApiModelProperty(value = "手机短信动态验证码")
	private String phoneCode;
	/** 登录IP地址 */
	@ApiModelProperty(value = "登录IP")
	private String loginIP;

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getUserType() {
		return userType;
	}

	public void setUserType(String userType) {
		this.userType = userType;
	}

	public String getVaildCode() {
		return vaildCode;
	}

	public void setVaildCode(String vaildCode) {
		this.vaildCode = vaildCode;
	}

	public String getvToken() {
		return vToken;
	}

	public void setvToken(String vToken) {
		this.vToken = vToken;
	}

	public String getPhoneCode() {
		return phoneCode;
	}

	public void setPhoneCode(String phoneCode) {
		this.phoneCode = phoneCode;
	}

	public String getLoginIP() {
		return loginIP;
	}

	public void setLoginIP(String loginIP) {
		this.loginIP = loginIP;
	}

}
