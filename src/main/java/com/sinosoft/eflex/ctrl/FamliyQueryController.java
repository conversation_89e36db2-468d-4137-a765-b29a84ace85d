package com.sinosoft.eflex.ctrl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.model.FCEmpAndFamilyInfo;
import com.sinosoft.eflex.model.checkPerson.CheckPersonRequest;
import com.sinosoft.eflex.service.FamilyQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-08-20 15:14
 */
@RestController("FamilyQueryController")
@RequestMapping("/family")
@Slf4j
@RequiredArgsConstructor
@Api(value = "FamliyQueryController", description = "家庭账户")
public class FamliyQueryController {
    private final FamilyQueryService familyQueryService;

    @ApiOperation(value = "查询个人信息", notes = "查询个人信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personId", value = "个人客户号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getPersonalInfoById", method = RequestMethod.GET)
    public String getPersonalInfoById(@RequestHeader String Authorization, String personId) {
        String responseInfo = familyQueryService.selectPersonalInfoById(Authorization, personId);
        log.info("getPersonalInfoById response:{}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "查询个人及家属信息", notes = "查询个人及家属信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "pageSource", value = "页面来源", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getFamilyId", method = RequestMethod.GET)
    public String empAndFamilySelect(@RequestHeader String Authorization, String pageSource) {
        // PageSource 页面来源：多个页面调用同一接口 用于区分页面调用 0--福利查询页面 1--家庭信息维护页面 2--福利定制页面
        log.info("查询个人及家属信息---页面来源:" + pageSource);
        String selectFamilyInfo = familyQueryService.selectFamilyInfo(Authorization, pageSource);
        log.info("查询个人及家属信息--返回报文:" + selectFamilyInfo);
        return selectFamilyInfo;
    }

    @ApiOperation(value = "个人信息投保校验", notes = "个人信息投保校验")
    @RequestMapping(value = "/checkFamilyPerson", method = RequestMethod.GET)
    public Map checkFamilyPerson(String personid) {
        log.info("投保个人信息校验personid:" + personid);
        Map map = familyQueryService.checkFamilyPerson(personid);
        log.info("投保个人信息校验返回报文:" + JSON.toJSONString(map));
        return map;
    }

    @ApiOperation(value = "删除个人信息", notes = "删除个人信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personId", value = "个人客户号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/delectFamliyInfo", method = RequestMethod.GET)
    public String delectFamliyInfo(@RequestHeader String Authorization, String personId) {
        String responseInfo = familyQueryService.delectFamliyInfo(Authorization, personId);
        log.info("返回前台报文:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "校验关系", notes = "校验关系")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/checkFamilyInfo", method = RequestMethod.PUT)
    public String checkFamilyInfo(@RequestParam String personId, @RequestBody FCEmpAndFamilyInfo fcEmpAndFamilyInfo, @RequestHeader String Authorization) {
        String responseInfo = familyQueryService.checkFamilyInfo(personId, fcEmpAndFamilyInfo, Authorization);
        log.info("返回前台报文:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "选择订单校验个人信息", notes = "选择订单校验个人信息")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/checkSelection", method = RequestMethod.POST)
    public String checkSelection(@RequestBody List<CheckPersonRequest> checkList, @RequestHeader String Authorization) {
        log.info("选择订单校验个人信息: {}", JSONObject.toJSONString(checkList));
        String responseInfo = familyQueryService.checkSelection(checkList, Authorization);
        log.info("选择订单校验个人信息: {}", responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "修改个人信息", notes = "修改个人信息")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", dataType = "String", paramType = "header")
    @RequestMapping(value = "/getFamilyInfoUpdate", method = RequestMethod.PUT)
    public String empAndFamilyInfoUpdate(@RequestParam String personId, @RequestBody FCEmpAndFamilyInfo fcEmpAndFamilyInfo, @RequestHeader String Authorization) {
        String responseInfo = familyQueryService.updateFamilyInfo(personId, fcEmpAndFamilyInfo, Authorization);
        log.info("返回前台报文:" + responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "添加个人信息", notes = "添加个人信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "Long", value = "长期标识", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getAddFamilyInfo", method = RequestMethod.POST)
    public String empAndFamilyInfoAdd(@RequestBody FCEmpAndFamilyInfo fcEmpAndFamilyInfo, @RequestHeader String Authorization, String Long) {
        log.info("添加个人信息请求报文: {}", JSONObject.toJSONString(fcEmpAndFamilyInfo));
        String responseInfo = familyQueryService.addFamilyInfo(fcEmpAndFamilyInfo, Authorization, Long);
        log.info("添加个人信息返回报文: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "家庭保障查询", notes = "家庭保障查询")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/getFamilyEnsure", method = RequestMethod.GET)
    public String getFamilyEnsure(@RequestHeader String Authorization) {
        String responseInfo = familyQueryService.getFamilyEnsure(Authorization);
        log.info("返回接口参数:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "查询双重身份的家庭成员", notes = "查询双重身份的家庭成员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personId", value = "个人客户号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/selectFamliyIdentity", method = RequestMethod.GET)
    public String selectFamilyIdentity(@RequestHeader String Authorization, String personId) {
        String responseInfo = familyQueryService.selectFamliyIdentity(Authorization, personId);
        log.info("返回前台报文:" + responseInfo);
        return responseInfo;
    }


}
