package com.sinosoft.eflex.ctrl;

import com.hqins.common.base.ApiResult;
import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.model.sign.confirm.SignConfirmRequest;
import com.sinosoft.eflex.service.userClient.BankSignService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import com.sinosoft.eflex.model.sign.apply.SignApplyReq;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @create 2021/3/15 银行签约对接中台相关接口
 */
@RestController
@RequestMapping("BankSign")
@Api(value = "BankSignController", description = "银行签约相关接口")
@Slf4j
@RequiredArgsConstructor
public class BankSignController {
    private final BankSignService bankSignService;

    @ApiOperation(value = "签约申请", httpMethod = "POST", notes = "签约申请同时发送短信验证码")
    @PostMapping(value = "/signApply")
    public ApiResult<String> signApply(@ApiParam(value = "新式令牌", required = true) @RequestHeader(name = "Authorization") String token,
                                       @ApiParam(value = "签约申请信息", required = true) @Valid @RequestBody SignApplyReq signApplyReq) {
        log.info("签约申请请求报文: {}", JsonUtil.toJSON(signApplyReq));
        return ApiResult.ok(bankSignService.signApply(token, signApplyReq));
    }

    @ApiOperation(value = "签约申请确认", httpMethod = "POST", notes = "签约申请确认（测试使用，实则在订单确认层级内部调用）")
    @PostMapping(value = "/signConfirm")
    public ApiResult<Void> signConfirm(@ApiParam(value = "新式令牌", required = true) @RequestHeader(name = "Authorization") String token,
                                       @ApiParam(value = "签约申请信息", required = true) @RequestBody @Valid SignConfirmRequest signConfirmRequest) {
        log.info("签约申请确认请求报文: {}", JsonUtil.toJSON(signConfirmRequest));
        bankSignService.signConfirm(signConfirmRequest);
        return ApiResult.ok();
    }

}
