package com.sinosoft.eflex.ctrl;

import com.alibaba.fastjson.JSON;
import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.model.FCBusPersonType;
import com.sinosoft.eflex.model.FcPlanRiskInfo;
import com.sinosoft.eflex.service.EflexEnsureMakeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/EFlexEnsureMake")
@Api(value = "EFlexEnsureMakeController", description = "弹性福利计划定制")
public class EFlexEnsureMakeController {
    
    private static final Logger Log = LoggerFactory.getLogger(EFlexEnsureMakeController.class);

    @Autowired
    private EflexEnsureMakeService eflexEnsureMakeService;

    /**
     * 新增/修改 弹性计划保障信息
     * @param Authorization
     * @param params
     * @return
     */
    @ApiOperation(value = "新增/修改 弹性计划保障信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization",value = "新式令牌",required = false,dataType = "String",paramType = "header")
    })
    @RequestMapping(value = "/maintainEnsureInfo",method = RequestMethod.POST)
    public String maintainEnsureInfo(@RequestHeader String Authorization,@RequestBody Map<String, String> params){
        Log.info("新增/修改 弹性计划保障信息入参："+ JSON.toJSONString(params));
        String responseInfo = eflexEnsureMakeService.maintainEnsureInfo(Authorization,params);
        Log.info("新增/修改 弹性计划保障信息结果："+responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "获取险种相关配置数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization",value = "新式令牌",required = false,dataType = "String",paramType = "header"),
            @ApiImplicitParam(name = "riskCode",value = "险种编码",required = false,dataType = "String",paramType = "query"),
            @ApiImplicitParam(name = "dutyCode",value = "责任编码",required = false,dataType = "String",paramType = "query"),
            @ApiImplicitParam(name = "amountGrageCode",value = "档次编码",required = false,dataType = "String",paramType = "query")
    })
    @RequestMapping(value = "/getDutyConfig",method = RequestMethod.GET)
    public String getDutyConfig(@RequestHeader String Authorization,String riskCode,String dutyCode,String amountGrageCode){
        Log.info("获取险种相关配置数据  险种编码："+riskCode+"=======责任编码："+dutyCode+"======档次编码："+amountGrageCode);
        String responseInfo = eflexEnsureMakeService.getDutyConfig(riskCode,dutyCode,amountGrageCode);
        Log.info("获取险种相关配置数据："+responseInfo);
        return responseInfo;
    }

    /**
     *没有调用 已合并至getDutyConfig接口
     * @param Authorization
     * @param riskCode
     * @param amountGrageCode
     * @return
     */
    @ApiOperation(value = "获取必选责任档次相关配置信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization",value = "新式令牌",required = false,dataType = "String",paramType = "header"),
            @ApiImplicitParam(name = "riskCode",value = "险种编码",required = false,dataType = "String",paramType = "query"),
            @ApiImplicitParam(name = "amountGrageCode",value = "保额档次编码",required = false,dataType = "String",paramType = "query")
    })
    @RequestMapping(value = "/getGradeConfig",method = RequestMethod.GET)
    public String getGradeConfig(@RequestHeader String Authorization,String riskCode,String amountGrageCode){
        Log.info("获取必选责任档次相关配置信息  险种编码："+riskCode+"=======保额档次编码："+amountGrageCode);
        String responseInfo = eflexEnsureMakeService.getGradeConfig(riskCode,amountGrageCode);
        Log.info("获取必选责任档次相关配置信息："+responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "弹性计划- -新增/修改保额档次")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization",value = "新式令牌",required = false,dataType = "String",paramType = "header"),
            @ApiImplicitParam(name = "operateType", value = "操作标识符", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/addOrUpdateAmntGrade",method = RequestMethod.POST)
    public String addOrUpdateAmntGrade(@RequestHeader String Authorization,String operateType,@RequestBody Map<String,Object> map){
        Log.info("操作标识符:"+operateType); //operateType 区分接口调用源 0--新增  1--修改
        Log.info("新增保额档次入参："+JSON.toJSONString(map));
        String responseInfo = eflexEnsureMakeService.addOrUpdateAmntGrade(Authorization,operateType,map);
        Log.info("新增保额档次结果："+responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "弹性计划- -新增/修改险种")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization",value = "新式令牌",required = false,dataType = "String",paramType = "header"),
            @ApiImplicitParam(name = "fcPlanRiskInfo", value = "弹性计划险种信息", required = true, dataType = "FcPlanRiskInfo", paramType = "body"),
            @ApiImplicitParam(name = "isCheck", value = "标识符", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/addOrUpdateRiskInfo",method = RequestMethod.POST)
    public String addOrUpdateRiskInfo(@RequestHeader String Authorization,@RequestBody FcPlanRiskInfo fcPlanRiskInfo,String isCheck){
        Log.info("弹性计划- -新增险种入参："+JSON.toJSONString(fcPlanRiskInfo));
        Log.info("标识符："+isCheck);
        String responseInfo = eflexEnsureMakeService.addOrUpdateRiskInfo(Authorization,fcPlanRiskInfo,isCheck);
        Log.info("弹性计划- -新增险种结果："+responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "弹性计划定制- -删除险种/删除保额档次")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization",value = "新式令牌",required = true,dataType = "String",paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "riskCode", value = "险种编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "riskType", value = "险种类别", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dutyCode", value = "责任编码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "amountGrageCode", value = "档次编码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "deductible", value = "免赔额", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "operateType", value = "操作标识符", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/delRiskInfo",method = RequestMethod.GET)
    public String delRiskInfo(@RequestHeader String Authorization,String ensureCode,String riskCode,String riskType,String dutyCode,String amountGrageCode,String operateType,String deductible){
        Log.info("弹性计划--删除所选险种入参：福利编号:"+ensureCode+"==险种编号:"+riskCode+"==险种类别:"+riskType+"===责任编码："+dutyCode+"==档次编码"+amountGrageCode+"==操作符"+operateType+"===免赔额"+deductible);
        String responseInfo = eflexEnsureMakeService.delRiskInfo(Authorization,ensureCode,riskCode,riskType,dutyCode,amountGrageCode,operateType,deductible);
        Log.info("弹性计划--删除所选险种结果："+responseInfo);
        return responseInfo;
    }

    //查询职级
    @ApiOperation(value = "查询员工职级")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
        @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/selectFCBusPersonTypeByEnsureCode", method = RequestMethod.GET)
    public String selectFCBusPersonTypeByEnsureCode(@RequestHeader String token,String ensureCode){
        Log.info("查询员工职级接口请求："+ensureCode);
        String responseInfo = eflexEnsureMakeService.selectFCBusPersonTypeByEnsureCode(token,ensureCode);
        Log.info("查询员工职级接口结果："+responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "职级定制顺序校验")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/checkStaffRank", method = RequestMethod.POST)
    public String checkStaffRank(@RequestHeader String Authorization, @RequestBody List<Integer> orderNumList){
        Log.info("职级定制顺序校验入参：职级顺序号："+JSON.toJSONString(orderNumList));
        String responseInfo = eflexEnsureMakeService.checkStaffRank(orderNumList);
        Log.info("职级定制顺序校验结果："+responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "新增员工职级")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/insertStaffRank", method = RequestMethod.POST)
    public String insertStaffRank(@RequestHeader String Authorization, @RequestBody FCBusPersonType fcBusPersonType){
        Log.info("新增员工职级接口入参：职级参数："+JSON.toJSONString(fcBusPersonType));
        String responseInfo = eflexEnsureMakeService.insertStaffRank(Authorization,fcBusPersonType);
        Log.info("新增员工职级返回结果："+responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "删除员工职级")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号",required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "gradeLevelCode", value = "职级编号",required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/deleteStaffRank", method = RequestMethod.GET)
    public String deleteStaffRank(@RequestHeader String Authorization,String ensureCode,String gradeLevelCode ){
        Log.info("员工职级删除入参：福利编号："+ensureCode+"======职级编号："+gradeLevelCode);
        String responseInfo = eflexEnsureMakeService.deleteStaffRank(Authorization,ensureCode,gradeLevelCode);
        Log.info("员工职级删除返回结果："+responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "配置投保对象--新增层级")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "isCheck", value = "操作标识符",required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/insertHierarchy", method = RequestMethod.POST)
    public String insertHierarchy(@RequestHeader String Authorization,@RequestBody Map<String,Object> map,String isCheck){
        Log.info("新增层级接口入参："+JSON.toJSONString(map)+"====操作标识符："+isCheck);
        String responseInfo = eflexEnsureMakeService.insertHierarchy(Authorization,map,isCheck);
        Log.info("新增层级返回结果："+responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "配置投保对象--层级列表查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号",required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isCheck", value = "选择对象",required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/selectHierarchy", method = RequestMethod.GET)
    public String selectHierarchy(@RequestHeader String Authorization,String ensureCode,String isCheck){
        //选择对象：isCheck       01--员工  02家属
        Log.info("查询层级接口入参：福利编号："+ensureCode+"======选择对象："+isCheck);
        String responseInfo = eflexEnsureMakeService.selectHierarchy(Authorization,ensureCode,isCheck);
        Log.info("查询层级返回结果："+responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "配置投保对象--查询必选责任信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "pageNo", value = "当前查询页", required = true, dataType = "int", paramType = "query", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页记录数", required = true, dataType = "int", paramType = "query", defaultValue = "10"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号",required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "serialNo", value = "层级流水号",required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getDutyAmountGradeByEnsureCode", method = RequestMethod.GET)
    public String getDutyAmountGradeByEnsureCode(@RequestHeader String Authorization,int pageNo,int pageSize,String ensureCode,String serialNo){
        Log.info("查询必选责任信息接口入参：福利编号："+ensureCode);
        String responseInfo = eflexEnsureMakeService.getDutyAmountGradeByEnsureCode(Authorization,pageSize,pageNo,ensureCode,serialNo);
        Log.info("查询必选责任信息返回结果："+responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "查询所选层级保额档次(修改调用)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号",required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "serialNo", value = "层级流水号",required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getAllDutyAmountGradeBySerialNo", method = RequestMethod.GET)
    public String getAllDutyAmountGradeBySerialNo(@RequestHeader String Authorization,String ensureCode,String serialNo){
        Log.info("查询所选层级险种信息接口入参(修改)：福利编号："+ensureCode);
        String responseInfo = eflexEnsureMakeService.getAllDutyAmountGradeBySerialNo(Authorization,ensureCode,serialNo);
        Log.info("查询所选层级险种信息返回结果："+responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "查询所选层级险种信息(回显调用)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号",required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "serialNo", value = "层级流水号",required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getDutyAmountGradeBySerialNo", method = RequestMethod.GET)
    public String getDutyAmountGradeBySerialNo(@RequestHeader String Authorization,String ensureCode,String serialNo){
        Log.info("查询所选层级险种信息接口入参(回显)：福利编号："+ensureCode+"=======层级流水号"+serialNo);
        String responseInfo = eflexEnsureMakeService.getDutyAmountGradeBySerialNo(Authorization,ensureCode,serialNo);
        Log.info("查询所选层级险种信息返回结果："+responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "配置投保对象--删除层级列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "serialNo", value = "层级流水号",required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/deleteDutyGrpObjectBySerialNo", method = RequestMethod.GET)
    public String deleteDutyGrpObjectBySerialNo(@RequestHeader String Authorization,String serialNo){
        Log.info("查询必选责任信息接口入参：层级流水号"+serialNo);
        String responseInfo = eflexEnsureMakeService.deleteDutyGrpObjectBySerialNo(Authorization,serialNo);
        Log.info("查询必选责任信息返回结果："+responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "配置投保对象--配置完成")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号",required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/eflexEnsureComplete", method = RequestMethod.GET)
    public String eflexEnsureComplete(@RequestHeader String Authorization,String ensureCode){
        Log.info("弹性计划配置完成--福利编号："+ensureCode);
        String responseInfo = eflexEnsureMakeService.eflexEnsureComplete(Authorization,ensureCode);
        Log.info("弹性计划配置完成结果："+ensureCode);
        return responseInfo;
    }

    @ApiOperation(value = "弹性计划 - - 导出人员清单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号",required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "grpNo", value = "企业客户号",required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "name", value = "员工姓名",required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sex", value = "员工性别",required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "iDType", value = "证件类型",required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "iDNo", value = "证件号码",required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/eflexExportPerInfoExcel", method = RequestMethod.GET)
    public String eflexExportPerInfoExcel(String Authorization,HttpServletResponse response,String ensureCode,String grpNo,String name,String sex,String iDType,String iDNo){
        Log.info("弹性计划-人员清单导出请求报文：福利编号："+ensureCode+"===姓名："+name+"===性别："+sex+"===证件类型："+iDType+"===证件号："+iDNo+"=====GrpNo"+grpNo);
        String responseInfo = eflexEnsureMakeService.eflexExportPerInfoExcel(Authorization,response,ensureCode,grpNo,name,sex,iDType,iDNo);
        Log.info("弹性计划-人员清单导出返回报文："+responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "新增/修改--福利联系人信息")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/maintainEnsureContactInfo", method = RequestMethod.POST)
    public String maintainEnsureContactInfo(@RequestHeader String Authorization, @RequestBody Map<String, String> params){
        Log.info("新增/修改--福利联系人信息请求报文：" + Authorization + "====" + JsonUtil.toJSON(params));
        String responseInfo = eflexEnsureMakeService.maintainEnsureContactInfo(Authorization, params);
        Log.info("新增/修改--福利联系人信息返回报文：" + responseInfo);
        return responseInfo;
    }
}
