package com.sinosoft.eflex.ctrl;

import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URISyntaxException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Formatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@RestController("HfiveShareController")
@RequestMapping("/share")
@Api(value = "HfiveShareController",description = "微信端页面分享")
public class HfiveShareController {
	private static final Logger LOGGER = LoggerFactory.getLogger(HfiveShareController.class);

	@Autowired
    private MyProps myProps;
    @Autowired
    private RedisUtil redisUtil;
    /**
     * @throws URISyntaxException 
     * @Description: h5微信分享数据准备
     * url:前端分享页面服务器路径（一定要前端获取分享页面地址，不要后端写死，写死容易造成签名错误问题出现）
     */
    @ApiOperation(value = "H5页面分享")
    @ApiImplicitParams({
    		@ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getShareData", method = RequestMethod.POST)
    public Map<String, String> getShareData(@RequestHeader String Authorization, @RequestBody Map<String, String> map){
    	Map<String, String> ret = new HashMap<String, String>();
    	try {
    		LOGGER.info("---1---url-----:{}", map.get("url"));
    		String APPID = myProps.getShare_AppId();
    		LOGGER.info("---2---APPID-----:{}", APPID);
            String APPSECRET = myProps.getShare_AppSecret();
            LOGGER.info("---3---APPSECRET-----:{}", APPSECRET);
            String GETACCESSTOKEN_URL = myProps.getGetAccessToken_URL();
            LOGGER.info("---4---GETACCESSTOKEN_URL-----:{}", GETACCESSTOKEN_URL);
            String GETTICKET_URL = myProps.getGetTicket_URL();
            LOGGER.info("---5---GETTICKET_URL-----:{}", GETTICKET_URL);
            String access_token = redisUtil.get("access_token");
            if (StringUtil.isEmpty(access_token)) {
                // 获取“access_token”,建议放于缓存或者数据库中，定时刷新
                String getAccessTokenPath = GETACCESSTOKEN_URL.replace("APPID", APPID).replace("APPSECRET", APPSECRET);
                LOGGER.info("---6---getAccessTokenPath-----:{}", getAccessTokenPath);
                JSONObject jsonObject = VChartUtil.doGetStr(getAccessTokenPath);
                access_token = jsonObject.getString("access_token");
                Integer loginValidity = Integer.parseInt(myProps.getTermValidityLogin());
                redisUtil.put("access_token", access_token, loginValidity);
            }
            LOGGER.info("---HfiveShare---access_token-----:{}", access_token);
            // 根据“access_token”获取“jsapi_ticket”
            String getTicketPath = GETTICKET_URL.replace("ACCESS_TOKEN", access_token);
            JSONObject jsonObject1 = VChartUtil.doGetStr(getTicketPath);
            String ticket = jsonObject1.getString("ticket");
            LOGGER.info("---HfiveShare---ticket-----:{}", ticket);
            String url=new java.net.URI(map.get("url")).getPath();
            url=new java.net.URI(url).getPath();
            ret = sign(ticket, url,APPID);
		} catch (Exception e) {
			e.printStackTrace();
		}
        return ret;
    }

    /**
     * @Description: 获取签名信息
     */
    public static Map<String, String> sign(String jsapi_ticket, String url,String APPID) {
        Map<String, String> ret = new HashMap<String, String>();
        String nonce_str = create_nonce_str();
        String timestamp = create_timestamp();
        String string1;
        String signature = "";

        //注意这里参数名必须全部小写，且必须有序
        string1 = "jsapi_ticket=" + jsapi_ticket +
                "&noncestr=" + nonce_str +
                "&timestamp=" + timestamp +
                "&url=" + url;
        LOGGER.info(string1);

        try {
            MessageDigest crypt = MessageDigest.getInstance("SHA-1");
            crypt.reset();
            crypt.update(string1.getBytes("UTF-8"));
            signature = byteToHex(crypt.digest());
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        ret.put("url", url);
        ret.put("jsapi_ticket", jsapi_ticket);
        ret.put("nonceStr", nonce_str);
        ret.put("timestamp", timestamp);
        ret.put("signature", signature);
        ret.put("appid", APPID);

        return ret;
    }

    private static String byteToHex(final byte[] hash) {
        Formatter formatter = new Formatter();
        for (byte b : hash) {
            formatter.format("%02x", b);
        }
        String result = formatter.toString();
        formatter.close();
        return result;
    }

    private static String create_nonce_str() {
        return UUIDUtil.genUUID();
    }

    private static String create_timestamp() {
        return Long.toString(System.currentTimeMillis() / 1000);
    }
}
