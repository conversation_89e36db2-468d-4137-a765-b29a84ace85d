package com.sinosoft.eflex.ctrl;

import com.sinosoft.eflex.enums.SignInsureEnum;
import com.sinosoft.eflex.service.TaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


/**
 * 定时任务接口
 *
 * @DESCRIPTION
 * @create 2018-08-30 10:09
 **/
@RestController
@RequestMapping("TaskController")
@Api(value = "TaskController")
@Slf4j
@RequiredArgsConstructor
public class TaskController {

    private final TaskService taskService;

    @ApiOperation(value = "默认计划和批量投保整合接口")
    @RequestMapping(value = "insureBySystem", method = RequestMethod.GET)
    public String insureBySystem(@RequestParam String ensureCode) {
        makeDefaultPlan(ensureCode);
        String responseInfo = taskService.dealGrpOrderList("", ensureCode, SignInsureEnum.BATCHINSURE.getCode());
        return responseInfo;
    }

    @ApiOperation(value = "签单接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query")})
    @RequestMapping(value = "/batchInsure", method = RequestMethod.GET)
    public String batchInsure(@RequestHeader String Authorization, @RequestParam String ensureCode) {
        log.info("签单接口请求报文：福利编号-{}", ensureCode);
        String responseInfo = taskService.dealGrpOrderList(Authorization, ensureCode, SignInsureEnum.ARTIFICIALINSURE.getCode());
        log.info("签单接口返回报文: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "员工没投保投默认计划")
    @RequestMapping(value = "/makeDefaultPlan", method = RequestMethod.GET)
    public String makeDefaultPlan(@RequestParam String ensureCode) {
        String responseInfo = taskService.makeDefaultPlan(ensureCode);
        log.info("makeDefaultPlan:{}", responseInfo);
        return "默认计划投保成功";
    }

    @ApiOperation(value = "整单确认 - - 转默认计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/wholeMakeDefaultPlan", method = RequestMethod.GET)
    public String wholeMakeDefaultPlan(String Authorization, String ensureCode) {
        log.info("整单确认--转默认计划请求报文 :福利编号-{}", ensureCode);
        String responseInfo = taskService.makeDefaultPlan(ensureCode);
        log.info("整单确认--转默认计划返回报文: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "承保保结果查询(前台)")
    @RequestMapping(value = "/insureCallbackClient", method = RequestMethod.GET)
    public String insureCallbackClient(@RequestParam String relaSn) {
        log.info("承保保结果查询(前台) 批量投保流水号：" + relaSn);
        String responseInfo = taskService.insureCallbackClient(relaSn);
        log.info("承保保结果查询(前台) 反馈结果：" + responseInfo);
        return responseInfo;
    }

}
