package com.sinosoft.eflex.ctrl;


import java.io.File;
import java.io.IOException;

import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.dao.PersonMapper;
import com.sinosoft.eflex.model.Person;
import com.sinosoft.eflex.service.PersonService;
import com.sinosoft.eflex.util.RedisUtil;
import com.thoughtworks.xstream.core.util.Base64Encoder;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * 示例API
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/Person")
@Api(value = "PersonController", description = "人员API（这是一个 示例）")
// @EnableConfigurationProperties({ ConfigBean.class })
public class PersonController {
    private static Logger Log = (Logger) LoggerFactory.getLogger(PersonController.class);

    @Autowired
    private PersonService personService;
    @Autowired
    private PersonMapper personMapper;

    /**
     * 新增人员
     *
     * @param person
     * @param headers
     * @return
     *//*
    @ApiOperation(value = "新增人员", notes = "新增人员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "person", value = "Person对象", required = true, dataType = "Person", paramType = "body"),
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收字符集", required = false, dataType = "String", paramType = "header", defaultValue = "utf-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8")})
    @RequestMapping(value = "/insertPerson", method = RequestMethod.POST)
    public String insertPerson(@RequestBody Person person, @RequestHeader(required = false) HttpHeaders headers) {

        String responseInfo = personService.addPersonInfo(person);
        Log.info("返回前台报文：" + responseInfo);

        return responseInfo;
    }

    *//**
     * 删除人员
     *
     * @param id
     * @return
     *//*
    @ApiOperation(value = "删除人员", notes = "删除人员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收字符集", required = false, dataType = "String", paramType = "header", defaultValue = "utf-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8")})
    @RequestMapping(value = "/deletePerson/{id}", method = RequestMethod.DELETE)
    public String deletePerson(@PathVariable Integer id) {

        String responseInfo = personService.deletePersonById(id);
        Log.info("返回前台报文：" + responseInfo);

        return responseInfo;
    }

    *//**
     * 更新人员
     *
     * @param person
     * @param headers
     * @return
     *//*
    @ApiOperation(value = "更新人员", notes = "更新人员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "person", value = "人员", required = true, dataType = "Person", paramType = "body"),
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收的字符集", required = false, dataType = "String", paramType = "header", defaultValue = "UTF-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8")})
    @RequestMapping(value = "/updatePerson", method = RequestMethod.PUT)
    public String updatePerson(@RequestBody Person person, @RequestHeader(required = false) HttpHeaders headers) {
        String responseInfo = personService.updatePersonById(person);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    *//**
     * 根据ID查询人员
     *
     * @param id
     * @return
     *//*
    @ApiOperation(value = "根据ID查询人员", notes = "根据ID查询人员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收字符集", required = false, dataType = "String", paramType = "header", defaultValue = "UTF-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8")})
    @RequestMapping(value = "/getPersonById/{id}", method = RequestMethod.GET)
    public String getPersonById(@PathVariable Integer id) {

        String responseInfo = personService.selectPersonById(id);

        Log.info("返回前台数据：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "分页查询人员", notes = "分页查询人员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "当前查询页", required = true, dataType = "int", paramType = "query", defaultValue = "1"),
            @ApiImplicitParam(name = "rows", value = "每页记录数", required = true, dataType = "int", paramType = "query", defaultValue = "10"),
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收字符集", required = false, dataType = "String", paramType = "header", defaultValue = "UTF-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8")})
    @RequestMapping(value = "/getPersonPage", method = RequestMethod.GET)
    public String getPersonPage(int page, int rows) {

        String responseInfo = personService.findByPage(page, rows);
        Log.info("返回前台数据：" + responseInfo);

        return responseInfo;
    }

    // @Autowired
    // ConfigBean configBean;
    //
    // @ApiIgnore // 使用该注解忽略这个API
    // @ApiOperation(value = "测试API", notes = "测试API")
    // @RequestMapping(value = "/liucyTest", method = RequestMethod.GET)
    // public String liucytest() {
    // return "姓名：" + configBean.getName() + " 年龄：" + configBean.getAge();
    // }

    @ApiOperation(value = "测试API", notes = "测试API")
    @RequestMapping(value = "/restTest", method = RequestMethod.GET)
    public String restTest(@RequestParam String spanToken) {
        return personService.restTest(spanToken);
    }

    @ApiOperation(value = "webservice测试API", notes = "webservice测试API")
    @RequestMapping(value = "/webserviceTest", method = RequestMethod.POST)
    public String webservice() {
        return personService.webserviceTest();
    }

    @ApiOperation(value = "SFTP测试API", notes = "SFTP测试API")
    @RequestMapping(value = "/sftpTest", method = RequestMethod.POST)
    public String sftpTest(@RequestParam String serverIP, @RequestParam int port, @RequestParam String userName,
                           @RequestParam String userPassword) {
        return personService.sftpTest(serverIP, port, userName, userPassword);
    }

    // @RequestMapping(value = "/first", method = RequestMethod.GET)
    // public Map<String, Object> firstResp(HttpServletRequest request) {
    // Map<String, Object> map = new HashMap<>();
    // request.getSession().setAttribute("request Url", request.getRequestURL());
    // map.put("request Url", request.getRequestURL());
    // return map;
    // }
    //
    // @RequestMapping(value = "/sessions", method = RequestMethod.GET)
    // public Object sessions(HttpServletRequest request) {
    // Map<String, Object> map = new HashMap<>();
    // map.put("sessionId", request.getSession().getId());
    // map.put("message", request.getSession().getAttribute("request Url"));
    // return map;
    // }

    *//**
     * 实现文件上传
     *//*
    @RequestMapping(value = "/fileUpload", method = RequestMethod.POST)
    @ResponseBody
    public String fileUpload(@RequestParam("fileName") MultipartFile file) {
        if (file.isEmpty()) {
            return "false";
        }
        String fileName = file.getOriginalFilename();

        String path = "D:/filetest/upload";
        File dest = new File(path + "/" + fileName);
        if (!dest.getParentFile().exists()) { // 判断文件父目录是否存在
            dest.getParentFile().mkdir();
        }
        try {
            file.transferTo(dest); // 保存文件
            Log.info("文件：" + path + "/" + fileName + "   上传成功！");
        } catch (IllegalStateException e) {
            Log.info("实现文件上传出现错误:",e);
            return "false";
        } catch (IOException e) {
            Log.info("实现文件上传出现错误:",e);
            return "false";
        }

        return "true";

    }
//	
//	*//**
//	 * 实现多文件上传
//	 *//*
//	@RequestMapping(value="/multifileUpload",method=RequestMethod.POST)
//	public String multifileUpload(@RequestParam("fileName") List<MultipartFile> files) {
//		if(files.isEmpty()){
//            return "false";
//        }
//
//        String path = "D:/filetest/upload" ;
//        
//        for(MultipartFile file:files){
//            String fileName = file.getOriginalFilename();            
//            if(file.isEmpty()){
//                return "false";
//            }else{        
//                File dest = new File(path + "/" + fileName);
//                if(!dest.getParentFile().exists()){ //判断文件父目录是否存在
//                    dest.getParentFile().mkdir();
//                }
//                try {
//                    file.transferTo(dest);
//                    Log.info("文件：" + path + "/" + fileName+"   上传成功！");
//                }catch (Exception e) {
//                    e.printStackTrace();
//                    return "false";
//                } 
//            }
//        }
//        return "true";
//	}
//
//

    *//**
     * 实现文件下载
     *//*
    @RequestMapping(value = "/download", method = RequestMethod.GET)
    public ResponseEntity<InputStreamResource> downloadFile() throws IOException {
        String filePath = "D:/filetest/upload/";
        String filename = "123.xlsx";
        FileSystemResource file = new FileSystemResource(filePath + filename);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
        // headers.add("Content-Disposition", String.format("attachment;
        // filename=\"%s\"", file.getFilename()));
        headers.add("Content-Disposition",
                "attachment; filename=" + new String(file.getFilename().getBytes("UTF-8"), "iso-8859-1"));
        headers.add("Pragma", "no-cache");
        headers.add("Expires", "0");

        return ResponseEntity.ok().headers(headers).contentLength(file.contentLength())
                .contentType(MediaType.parseMediaType("application/octet-stream"))
                .body(new InputStreamResource(file.getInputStream()));
    }

    @ApiOperation(value = "导入Excel测试", notes = "导入Excel测试")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public String addUser(@RequestParam("fileName") MultipartFile file) {
        String fileName = file.getOriginalFilename();
        String responseInfo = "";
        try {
            responseInfo = personService.batchImport(fileName, file);
        } catch (Exception e) {
            Log.info("导入Excel测试出现错误:",e);
        }
        return responseInfo;
    }

    *//**
     * 获取图片实现前端展现
     *
     * @param imgId
     * @return
     *//*
    @ApiOperation(value = "获取图片", notes = "获取图片）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "imgId", value = "图片id", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收字符集", required = false, dataType = "String", paramType = "header", defaultValue = "UTF-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8")})
    @RequestMapping(value = "/getImage", method = RequestMethod.GET)
    public String getImage(int imgId, HttpServletResponse response) {
    	
    	 response.setHeader("Access-Control-Allow-Origin", "*"); //测试用

        *//*FileInputStream is = null;
        try {
            ServletOutputStream out = response.getOutputStream();
            //根据imgId查询出图片路径，这里暂时写死
            is = new FileInputStream("D:\\360Downloads\\123.jpg");
            byte[] bytes = new byte[1024];
            int len = 0;
            // 开始读取图片信息  
            while (-1 != (len = is.read(bytes))) {
                out.write(bytes, 0, len);
            }
            out.flush();
            out.close();
            is.close();
            is = null;
            Log.info("图片输出完成。。。");
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }*//*
    	String responseInfo = JSON.toJSONString(personMapper.selectPerson(imgId));
    	Log.info(responseInfo);
    	return responseInfo;
    	
    }
    
    *//**
     * 保存图片
     *
     * @param imgId
     * @return
     *//*
    @ApiOperation(value = "保存图片", notes = "保存图片）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "imgdata", value = "图片数据", required = true, dataType = "String", paramType = "body"),
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收字符集", required = false, dataType = "String", paramType = "header", defaultValue = "UTF-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8")})
    @RequestMapping(value = "/saveImage", method = RequestMethod.POST)
    public void saveImage(@RequestBody String imgdata, HttpServletResponse response) {
    	
    	Log.info("请求报文："+imgdata);
    	JSONObject jsonObject = JSON.parseObject(imgdata);
    	String imgstr = jsonObject.getString("imgstr");
    	Person person =  new Person();
    	person.setPid(1);
    	person.setImgstr(imgstr);
        personMapper.updateByPrimaryKeySelective(person);
        Log.info("保存图片成功。。。");
    }
    
    *//**
     * 图片上传
     *//*
    @RequestMapping(value = "/imgUpload", method = RequestMethod.POST)
    @ResponseBody
    public String imgUpload(@RequestParam("fileName") MultipartFile file) {
        if (file.isEmpty()) {
            return "false";
        }
        Base64Encoder encoder = new Base64Encoder();
        try {
			String imgData = encoder.encode(file.getBytes());
			Person person = new Person();
			person.setPid(2);
			person.setImgstr(imgData);
			personMapper.updateByPrimaryKeySelective(person);
		} catch (IOException e) {
			Log.info("图片上传:",e);
		}
        return "true";

    }

    @Autowired
    private RedisUtil service;

    //redis测试
    @RequestMapping(value = "/redistestAdd", method = RequestMethod.GET)
    @ResponseBody
    public void test() {
        Log.info("start.....");
        Person person = new Person();
        person.setPid(123456789);
        person.setName("redistest");
        person.setAge("30");
        person.setSex("男");
        person.setMakedate("2018-08-15");
        service.put("person1", JSON.toJSONString(person), -1);

        Log.info("add success end...");
    }

    //根据key查询
    @ApiOperation(value = "redis查询", notes = "redis查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "userInfo", value = "用户信息", required = false, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/redistestGet", method = RequestMethod.GET)
    @ResponseBody
    public Object get(@RequestHeader String Authorization) {
        Log.info("获取token：" + Authorization);
        return service.get(Authorization);
    }

    //更新key过期时间
    @RequestMapping(value = "/redistestUpdate", method = RequestMethod.POST)
    @ResponseBody
    public void update(@RequestHeader String Authorization) {
//        String values = service.get("zhangsanKey01");
//        service.put("zhangsanKey01", values, -1);
    	service.expire(Authorization, -1);
        Log.info("update success end...");
    }*/

}
