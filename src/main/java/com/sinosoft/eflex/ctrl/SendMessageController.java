package com.sinosoft.eflex.ctrl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.model.sendmessage.SendEmailReq;
import com.sinosoft.eflex.model.sendmessage.SendMessageResp;
import com.sinosoft.eflex.model.sendmessage.SendSMSReq;
import com.sinosoft.eflex.service.sms.SendMessageService;
import com.sinosoft.eflex.util.rest.util.ResponseResult;
import com.sinosoft.eflex.util.rest.util.ResponseResultUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * <AUTHOR>
 * @create 2021/3/15 满天星消息相关接口
 */
@RestController
@RequestMapping("SMS")
@Api(value = "SendMessageController", description = " 满天星消息发送相关接口")
public class SendMessageController {

    // 日志
    private static Logger log = LoggerFactory.getLogger(SendMessageController.class);

    // 引入公共资源
    @Autowired
    private SendMessageService sendMessageService;

    @ApiOperation(value = "发送短信", httpMethod = "POST", notes = "{\n" + "    \"param\":{\n"
            + "        \"roleName\":\"初审岗\"\n" + "    },\n" + "    \"phones\":\"16601117074\",\n"
            + "    \"templateNo\":\"STAFF_BENEFITS_014\"\n" + "}")
    @PostMapping(value = "/sendSMS")
    public ResponseResult<SendMessageResp> sendSMS(
            @ApiParam(value = "新式令牌", required = true) @RequestHeader(name = "Authorization") String token,
            @ApiParam(value = "短信信息", required = true) @RequestBody SendSMSReq sendSMSReq) {
        log.info("发送短信请求报文: {}", JSONObject.toJSONString(sendSMSReq));
        SendMessageResp sendMessageResp = sendMessageService.sendSMS(sendSMSReq);
        log.info("发送短信返回报文: {}", sendMessageResp);
        return ResponseResultUtil.success(sendMessageResp);
    }

    @ApiOperation(value = "发送邮件", httpMethod = "POST", notes = "邮件发送")
    @PostMapping(value = "/sendEmail")
    public ResponseResult<SendMessageResp> sendEmail(
            @ApiParam(value = "新式令牌", required = true) @RequestHeader(name = "Authorization") String token,
            @ApiParam(value = "邮件信息", required = true) @RequestBody SendEmailReq sendEmailReq) {
        log.info("发送邮件请求报文: {}", JSONObject.toJSONString(sendEmailReq));
        SendMessageResp sendMessageResp = sendMessageService.sendEmail(sendEmailReq);
        log.info("发送邮件返回报文: {}", sendMessageResp);
        return ResponseResultUtil.success(sendMessageResp);
    }

}
