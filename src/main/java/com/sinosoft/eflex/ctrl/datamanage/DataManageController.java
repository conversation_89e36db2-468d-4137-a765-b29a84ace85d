package com.sinosoft.eflex.ctrl.datamanage;

import javax.servlet.http.HttpServletRequest;

import com.sinosoft.eflex.model.GetEnsureAuditsReq;
import com.sinosoft.eflex.model.datamanage.SelectEnsureInfoListReq;
import com.sinosoft.eflex.service.admin.EnsureAuditService;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.model.datamanage.GetEnsureInsureDataReq;
import com.sinosoft.eflex.service.datamanage.DataManageService;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @create 2021/8/4
 * @desc 数据管理
 */
@RestController
@RequestMapping("dataManage")
@Api(value = "DataManageController", description = "数据管理")
public class DataManageController {

    // 引入公共资源
    @Autowired
    private DataManageService dataManageService;

    // 日志
    private static Logger log = LoggerFactory.getLogger(DataManageController.class);


    @ApiOperation(value = "审核通过的福利信息列表查询")
    @RequestMapping(value = "/selectEnsureInfoList", method = RequestMethod.POST)
    public String getEnsureAudits(
            @ApiParam(value = "新式令牌", required = true) @RequestHeader(name = "Authorization") String token,
            @ApiParam(value = "福利列表查询请求参数", required = true) @RequestBody SelectEnsureInfoListReq selectEnsureInfoListReq) {
        log.info("审核通过的福利信息列表查询请求报文: {}", JSONObject.toJSONString(selectEnsureInfoListReq));
        String responseInfo = dataManageService.selectEnsureInfoList(token, selectEnsureInfoListReq);
        log.info("审核通过的福利信息列表查询成功！");
        return responseInfo;
    }

    @ApiOperation(value = "获取福利投保数据", httpMethod = "POST", notes = "获取福利投保数据")
    @PostMapping(value = "/getEnsureInsureData")
    public ResponseEntity<InputStreamResource> getEnsureInsureData(HttpServletRequest request,
                                                                   @ApiParam(value = "新式令牌", required = true) @RequestHeader(name = "Authorization") String token,
                                                                   @ApiParam(value = "福利编码", required = true) @RequestBody GetEnsureInsureDataReq getEnsureInsureDataReq) {
        log.info("获取福利投保数据请求报文: {}", JSONObject.toJSONString(getEnsureInsureDataReq));
        ResponseEntity<InputStreamResource> ensureInsureData = dataManageService.getEnsureInsureData(request, token,
                getEnsureInsureDataReq);
        return ensureInsureData;
    }

}
