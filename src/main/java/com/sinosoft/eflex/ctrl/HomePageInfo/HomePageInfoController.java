package com.sinosoft.eflex.ctrl.HomePageInfo;

import com.hqins.common.base.ApiResult;
import com.sinosoft.eflex.model.HomePageInfo.FcPerNameInfo;
import com.sinosoft.eflex.service.userClient.HomePageInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 官微员福首页*
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/home-page")
@Api(value = "HomePageInfoController")
@Slf4j
@RequiredArgsConstructor
public class HomePageInfoController {

    private final HomePageInfoService homePageInfoService;

    @RequestMapping(value = "/info", method = RequestMethod.GET)
    @ApiOperation(value = "首页信息查询", httpMethod = "GET")
    @ResponseStatus(HttpStatus.OK)
    @ApiImplicitParams({@ApiImplicitParam(name = "authorization", value = "商品ID", required = true)})
    public ApiResult<List<FcPerNameInfo>> getHomePageInfo(@RequestHeader(name = "Authorization") String authorization) {
        return ApiResult.ok(homePageInfoService.getHomePageInfo(authorization));
    }

}


