package com.sinosoft.eflex.ctrl;

import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.model.FCGrpInfo;
import com.sinosoft.eflex.model.FcGrpContact;
import com.sinosoft.eflex.service.GrpInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @create 2022/1/5
 * @desc 企业信息维护相关接口相关接口
 */
@RestController
@RequestMapping("GrpInfo")
@Api(value = "GrpInfoController", description = "企业信息维护")
public class GrpInfoController {

    // 日志
    private static Logger log = LoggerFactory.getLogger(GrpInfoController.class);

    @Autowired
    private GrpInfoService grpInfoService;


    @ApiOperation(value = "查询企业和经办人相关信息", httpMethod = "POST", notes = "企业相关信息查询（经办人信息 + 企业信息）")
    @PostMapping(value = "/selectGrpInfo")
    public String selectGrpInfo(@ApiParam(value = "新式令牌", required = true) @RequestHeader(name = "Authorization") String token) {
        log.info("查询企业相关信息请求报文: {}", token);
        String result = grpInfoService.selectGrpInfo(token);
        log.info("查询企业相关信息返回报文: {}", result);
        return result;
    }

    @ApiOperation(value = "企业信息变更", httpMethod = "POST", notes = "企业信息变更（企业信息维护页面，企业信息变更）")
    @PostMapping(value = "/saveGrpInfo")
    public String saveGrpInfo(@ApiParam(value = "新式令牌", required = true) @RequestHeader(name = "Authorization") String token, @ApiParam(value = "企业信息", required = true) @RequestBody FCGrpInfo fcGrpInfo) {
        log.info("企业信息变更请求报文: {}", JSONObject.toJSONString(fcGrpInfo));
        String result = grpInfoService.saveGrpInfo(token, fcGrpInfo);
        log.info("企业信息变更返回报文: {}", result);
        return result;
    }

    @ApiOperation(value = "经办人信息变更", httpMethod = "POST", notes = "经办人信息变更（企业信息维护页面，经办人信息变更）")
    @PostMapping(value = "/saveGrpHrInfo")
    public String saveGrpHrInfo(@ApiParam(value = "新式令牌", required = true) @RequestHeader(name = "Authorization") String token, @ApiParam(value = "经办人信息", required = true) @RequestBody FcGrpContact fcGrpContact) {
        log.info("经办人信息变更请求报文: {}", JSONObject.toJSONString(fcGrpContact));
        String result = grpInfoService.saveGrpHrInfo(token, fcGrpContact);
        log.info("经办人信息变更返回报文: {}", result);
        return result;
    }

}
