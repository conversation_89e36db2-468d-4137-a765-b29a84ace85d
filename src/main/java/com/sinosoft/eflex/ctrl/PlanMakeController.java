package com.sinosoft.eflex.ctrl;

import com.alibaba.fastjson.JSON;
import com.hqins.common.base.ApiResult;
import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.model.FCPlanRiskDuty;
import com.sinosoft.eflex.model.FDCode;
import com.sinosoft.eflex.model.request.FcEnsurePlanReq;
import com.sinosoft.eflex.service.PlanMakeService;
import com.sinosoft.eflex.service.admin.MakePlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> wuShi<PERSON>ao
 * @date : 2021-03-11 09:24
 **/

@RestController
@RequestMapping("PlanMake")
@Api(value = "PlanMakeController")
@RequiredArgsConstructor
@Slf4j
public class PlanMakeController {

    private final PlanMakeService planMakeService;
    private final MakePlanService makePlanService;

    @ApiOperation(value = "固定计划-删除福利下计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/deleteEnsurePlan", method = RequestMethod.POST)
    public String deleteEnsurePlan(@RequestHeader String Authorization, @RequestBody FCPlanRiskDuty fcPlanRiskDuty) {
        log.info("固定计划-删除福利下计划--前台传参：Authorization:{},fcPlanRiskDuty:{}", Authorization,
                JSON.toJSONString(fcPlanRiskDuty));
        String responseInfo = planMakeService.deleteEnsurePlan(fcPlanRiskDuty);
        log.info("固定计划-删除福利下计划--后台返回: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "固定计划定制- -删除险种/删除保额档次")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),})
    @RequestMapping(value = "/delPlanRiskInfo", method = RequestMethod.POST)
    public String delRiskInfo(@RequestHeader String Authorization, @RequestBody FCPlanRiskDuty fcPlanRiskDuty) {
        log.info("固定计划--删除所选险种--前台传参：Authorization:{},fcPlanRiskDutyKey:{}", Authorization,
                JSON.toJSONString(fcPlanRiskDuty));
        String responseInfo = planMakeService.delPlanRiskInfo(Authorization, fcPlanRiskDuty);
        log.info("固定计划--删除所选险种--后台返回: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "新增计划——险种责任信息保存")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    @RequestMapping(value = "/insertRiskDuty", method = RequestMethod.POST)
    public ApiResult<Void> insertRiskDuty(@RequestHeader String Authorization, @RequestBody List<FCPlanRiskDuty> fCPlanRiskDutyList) {
        log.info("新增计划——险种责任配置数据入参 Authorization:{}，fCPlanRiskDuty:{}", Authorization, JsonUtil.toJSON(fCPlanRiskDutyList));
        planMakeService.insertRiskDuty(Authorization, fCPlanRiskDutyList);
        return ApiResult.ok();
    }

    @ApiOperation(value = "保障计划配置完成")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/insertEnsurePlan", method = RequestMethod.POST)
    public ApiResult<Void> insertEnsurePlan(@RequestHeader String Authorization, @Valid @RequestBody FcEnsurePlanReq fcEnsurePlan) {
        log.info("新增计划——保障计划配置数据入参：Authorization: {}，fcEnsurePlan: {}", Authorization, fcEnsurePlan);
        makePlanService.insertEnsurePlan(Authorization, fcEnsurePlan);
        return ApiResult.ok();
    }

    @ApiOperation(value = "新增计划——责任信息回显")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    @RequestMapping(value = "/selectRiskDutyAndPlanRisk", method = RequestMethod.POST)
    public String slectRiskDutyAndPlanRisk(@RequestHeader String Authorization,
                                           @RequestBody FCPlanRiskDuty fcPlanRiskDuty) {
        log.info("新增计划——责任信息回显数据入参 Authorization: {}，fcPlanRiskDuty:{}", Authorization,
                JSON.toJSONString(fcPlanRiskDuty));
        String responseInfo = planMakeService.selectRiskDutyAndPlanRisk(fcPlanRiskDuty);
        log.info("新增计划——责任信息回显数据回显: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "新增计划——险种信息回显")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    @RequestMapping(value = "/selectPlanRisk", method = RequestMethod.POST)
    public String slectPlanRisk(@RequestHeader String Authorization, @RequestBody FCPlanRiskDuty fcPlanRiskDuty) {
        log.info("新增计划——险种信息回显数据入参：Authorization: {}，fcPlanRiskDuty:{}", Authorization, JSON.toJSONString(fcPlanRiskDuty));
        String responseInfo = planMakeService.selectPlanRisk(fcPlanRiskDuty);
        log.info("新增计划——险种信息回显数据回显: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "固定计划--计划新增查询码表")
    @RequestMapping(value = "/planFindCode", method = RequestMethod.POST)
    public String planFindCode(@RequestBody FDCode fdCode) {
        log.info("固定计划--计划新增查询码表--前台传参 fdCode: {}", JSON.toJSONString(fdCode));
        String responseInfo = planMakeService.planFindCode(fdCode);
        log.info("固定计划--计划新增查询码表--后台返回: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "固定计划--根据险种编码获取必选责任名称")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    @RequestMapping(value = "/selectDutyNameByRiskCode", method = RequestMethod.POST)
    public String selectDutyNameByRiskCode(@RequestBody FCPlanRiskDuty fcPlanRiskDuty) {
        log.info("固定计划--根据险种编码获取责任信息--前台传参 fcPlanRiskDuty: {}", JSON.toJSONString(fcPlanRiskDuty));
        String responseInfo = planMakeService.selectDutyNameByRiskCode(fcPlanRiskDuty);
        log.info("固定计划--根据险种编码获取责任信息--后台返回: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "固定计划--责任查询返回具体责任信息")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    @RequestMapping(value = "/selectDutyInfo", method = RequestMethod.POST)
    public String selectDutyInfo(@RequestHeader String Authorization, @RequestBody FCPlanRiskDuty fcPlanRiskDuty) {
        log.info("固定计划--责任查询返回具体责任信息--前台传参:{}", JSON.toJSONString(fcPlanRiskDuty));
        String responseInfo = planMakeService.selectDutyInfo(fcPlanRiskDuty);
        log.info("固定计划--责任查询返回具体责任信息--后台返回:{}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "清空福利下定制中的计划信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", dataType = "String", paramType = "query")})
    @RequestMapping(value = "/deleteMakeingPlanRiskInfo", method = RequestMethod.GET)
    public String deleteMakeingPlanRiskInfo(@RequestHeader String Authorization, String ensureCode) {
        log.info("清空福利下定制中的计划信息请求报文：Authorization: {}，ensureCode: {}", Authorization, ensureCode);
        String responseInfo = planMakeService.deleteMakeingPlanRiskInfo(ensureCode);
        log.info("清空福利下定制中的计划信息返回报文: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "新增计划--获取未添加险种列表信息")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    @RequestMapping(value = "/getRiskInfo", method = RequestMethod.POST)
    public String getRiskInfo(@RequestHeader String Authorization, @RequestBody FCPlanRiskDuty fcPlanRiskDuty) {
        log.info("新增计划--获取未添加险种列表信息--前台传参：Authorization: {}，fcPlanRiskDuty: {}", Authorization, JSON.toJSONString(fcPlanRiskDuty));
        String responseInfo = planMakeService.getRiskInfo(fcPlanRiskDuty);
        log.info("新增计划--获取未添加险种列表信息--后台返回：" + responseInfo);
        return responseInfo;
    }

}
