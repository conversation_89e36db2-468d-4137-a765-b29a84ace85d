package com.sinosoft.eflex.ctrl;/**
 * @DESCRIPTION
 * @create 2019-07-18 11:54:46
 **/

import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.framework.annotation.SqlInject;
import com.sinosoft.eflex.model.FCHealthDesignDetail;
import com.sinosoft.eflex.service.HealthDesignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.configurationprocessor.json.JSONObject;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName: HealthDesignController
 * @Date: 2019/7/18 11:54:46
 * @Version: 1.0
 */
@RestController
@RequestMapping("HealthDesign")
@Api(value = "HealthDesignController", description = "健康告知配置")
public class HealthDesignController {

    private static Logger Log = (Logger) LoggerFactory.getLogger(HealthDesignController.class);

    @Autowired
    private HealthDesignService healthDesignService;

    /**
     * 新增方案（返回方案编号）
     *
     * @param Authorization
     * @return
     */
    @ApiOperation(value = "新增方案（返回方案编号）")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/getDesignNo", method = RequestMethod.GET)
    public String getDesignNo(@RequestHeader String Authorization) {
        String responseInfo = healthDesignService.getDesignNo(Authorization);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    /**
     * 新增/修改 方案
     *
     * @param Authorization
     * @param params
     * @return
     */
    @ApiOperation(value = "新增/修改 方案")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/maintainHealthDesignInfo", method = RequestMethod.POST)
    public String maintainHealthDesignInfo(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        Log.info("新增方案入参：" + params);
        String responseInfo = healthDesignService.maintainDesignInfo(Authorization, params);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    /**
     * 查询方案信息
     *
     * @param Authorization
     * @param params
     * @param page
     * @param rows
     * @return
     */
    @ApiOperation(value = "查询方案信息")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "Authorization", value = "新式令牌", dataType = "String", paramType = "header"),
                    @ApiImplicitParam(name = "page", value = "当前查询页", required = true, dataType = "int", paramType = "query", defaultValue = "1"),
                    @ApiImplicitParam(name = "rows", value = "每页记录数", required = true, dataType = "int", paramType = "query", defaultValue = "10"),
                    @ApiImplicitParam(name = "isReal", value = "权限标识", required = true, dataType = "String", paramType = "query")
            }
    )
    @RequestMapping(value = "/getPlanHealthDesignByParams", method = RequestMethod.POST)
    @SqlInject(KeywordPrevent = true)
    public String getPlanHealthDesignByParams(@RequestHeader String Authorization, @RequestBody Map<String, String> params, int page, int rows, String isReal) {
        Log.info("查询方案信息入参:" + params);
        String responseInfo = healthDesignService.getPlanHealthDesignByParams(Authorization, params, page, rows, isReal);
        Log.info("getPlanHealthDesignByParams 返回前台报文:" + responseInfo);
        return responseInfo;
    }

    /**
     * 修改方案状态(退回、提交、复核)
     *
     * @param Authorization
     * @param params
     * @return
     */
    @ApiOperation(value = "退回后台配置-0/提交复核-3/复核通过-4")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/modifyDesignStatus", method = RequestMethod.POST)
    public String modifyDesignStatus(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        Log.info("修改健康告知状态--前台传参：params: {}", JSON.toJSONString(params));
        String responseInfo = healthDesignService.modifyDesignStatus(Authorization, params);
        Log.info("修改健康告知状态--返回前台报文: {}", responseInfo);
        return responseInfo;
    }


    /**
     * 删除方案信息
     *
     * @param Authorization
     * @param designNo
     * @param ensureCode
     * @return
     */
    @ApiOperation(value = "删除方案信息")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
                    @ApiImplicitParam(name = "designNo", value = "方案编号", dataType = "String", paramType = "query"),
                    @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query")
            }
    )
    @RequestMapping(value = "/deletePlanInfoByDesignNo", method = RequestMethod.GET)
    public String deletePlanInfoByDesignNo(@RequestHeader String Authorization, String designNo, String ensureCode) {
        Log.info("删除方案信息入参：" + designNo + "+" + ensureCode);
        String responseInfo = healthDesignService.deletePlanInfoByDesignNo(Authorization, designNo, ensureCode);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    /**
     * 查询健康告知方案详情列表
     *
     * @param Authorization
     * @param params
     * @param page
     * @param rows
     * @return
     */

    @ApiOperation(value = "查询健康告知方案详情列表")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
                    @ApiImplicitParam(name = "page", value = "当前查询页", required = true, dataType = "int", paramType = "query", defaultValue = "1"),
                    @ApiImplicitParam(name = "rows", value = "每页记录数", required = true, dataType = "int", paramType = "query", defaultValue = "10")
            }
    )
    @RequestMapping(value = "/getHealthDesignDetail", method = RequestMethod.POST)
    public String getHealthDesignDetail(@RequestHeader String Authorization, @RequestBody Map<String, String> params, int page, int rows) {
        Log.info("查询健高方案详情信息入参：" + params);
        String responseInfo = healthDesignService.getHealthDesignDetail(Authorization, params, page, rows);
        Log.info("返回前台报文" + responseInfo);
        return responseInfo;
    }


    /**
     * 修改健康告知详情
     *
     * @param Authorization
     * @param fcHealthDesignDetail
     * @return
     */
    @ApiOperation(value = "修改健康告知详情")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
                    @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query")
            }
    )
    @RequestMapping(value = "/updateHealthDesignDetail", method = RequestMethod.POST)
    public String updateHealthDesignDetail(@RequestHeader String Authorization, @RequestBody FCHealthDesignDetail fcHealthDesignDetail, String ensureCode) {
        Log.info("修改健康告知详情：" + fcHealthDesignDetail.getHealthDesignNo() + "+" + ensureCode);
        String responseInfo = healthDesignService.updateHealthDesignDetail(Authorization, fcHealthDesignDetail, ensureCode);
        Log.info("修改健康告知详情：" + responseInfo);
        return responseInfo;
    }

    /**
     * 删除健康告知方案详情
     *
     * @param Authorization
     * @param healthDesignNo
     * @return
     */
    @ApiOperation(value = "删除健康告知方案详情")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
                    @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query"),
                    @ApiImplicitParam(name = "designNo", value = "方案编号", dataType = "String", paramType = "query"),
                    @ApiImplicitParam(name = "healthDesignNo", value = "健告方案编号", dataType = "String", paramType = "query")
            }
    )
    @RequestMapping(value = "/deleteHealthDesignDetail", method = RequestMethod.GET)
    public String deleteHealthDesignDetail(@RequestHeader String Authorization, String ensureCode, String designNo, String healthDesignNo) {
        Log.info("删除健康告知方案详情入参：" + healthDesignNo);
        String responseInfo = healthDesignService.deleteHealthDesignDetail(Authorization, ensureCode, designNo, healthDesignNo);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }


    /**
     * 健康告知方案详情导入
     *
     * @return
     */
    @ApiOperation(value = "健康告知方案详情导入")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "status", value = "状态", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "designNo", value = "方案编号", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/importHealthDesignDetailExcel", method = RequestMethod.POST)
    public String importHealthDesignDetailExcel(@RequestHeader String Authorization, String status, String ensureCode, String designNo, @RequestParam("fileName") MultipartFile file) {        //String filePath = "C:\\Users\\<USER>\\Desktop\\横琴人寿保险计划导入模板V1.1.xlsx";
        Log.info("健康告知方案详情导入 请求报文：ensureCode:{} ,status:{}", ensureCode, status);
        String responseInfo = healthDesignService.importHealthDesignDetailExcel(Authorization, file, status, ensureCode, designNo);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

}
