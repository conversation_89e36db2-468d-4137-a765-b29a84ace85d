package com.sinosoft.eflex.ctrl;

import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.model.configmanage.AuditUserInfo;
import com.sinosoft.eflex.model.configmanage.SelectAuditUserReq;
import com.sinosoft.eflex.service.UserConfigManageService;
import com.sinosoft.eflex.util.rest.util.ResponseResult;
import com.sinosoft.eflex.util.rest.util.ResponseResultUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @create 2021/7/20 用户配置管理相关接口
 */
@RestController
@RequestMapping("/ConfigManage")
@Slf4j
@Api(value = "UserConfigManageController", description = "用户配置管理相关接口")
@RequiredArgsConstructor
public class UserConfigManageController {

    private final UserConfigManageService userConfigManageService;

    @ApiOperation(value = "查询审核用户", httpMethod = "POST", notes = "查询审核用户列表")
    @PostMapping(value = "/selectAuditUser")
    public String selectAuditUser(
            @ApiParam(value = "新式令牌", required = true) @RequestHeader(name = "Authorization") String token,
            @ApiParam(value = "用户信息", required = true) @RequestBody SelectAuditUserReq selectAuditUserReq) {
        log.info("查询审核用户请求报文: {}", JSONObject.toJSONString(selectAuditUserReq));
        String selectAuditUserList = userConfigManageService.selectAuditUser(token, selectAuditUserReq);
        log.info("selectAuditUser response: {}", selectAuditUserList);
        return selectAuditUserList;
    }

    @ApiOperation(value = "查询单个审核用户", httpMethod = "POST", notes = "查询单个审核用户")
    @PostMapping(value = "/selectSingleAuditUser")
    public String selectSingleAuditUser(
            @ApiParam(value = "新式令牌", required = true) @RequestHeader(name = "Authorization") String token,
            @ApiParam(value = "用户信息", required = true) @RequestBody AuditUserInfo auditUserInfo) {
        log.info("查询单个审核用户请求报文: {}", auditUserInfo);
        String selectSingleAuditUser = userConfigManageService.selectSingleAuditUser(token, auditUserInfo);
        log.info("查询单个审核用户返回报文: {}", selectSingleAuditUser);
        return selectSingleAuditUser;
    }

    @ApiOperation(value = "新增、修改审核用户", httpMethod = "POST", notes = "新增、修改审核用户")
    @PostMapping(value = "/{operation}/auditUser")
    public ResponseResult<String> addAuditUser(
            @PathVariable(name = "operation") String operation,
            @ApiParam(value = "新式令牌", required = true) @RequestHeader(name = "Authorization") String token,
            @ApiParam(value = "用户信息", required = true) @RequestBody AuditUserInfo auditUserInfo) {
        log.info("新增、修改审核用户请求报文: {}", JSONObject.toJSONString(auditUserInfo));
        userConfigManageService.dealAuditUser(token, operation, auditUserInfo);
        return ResponseResultUtil.success();
    }

    @ApiOperation(value = "删除审核用户", httpMethod = "POST", notes = "删除审核用户")
    @PostMapping(value = "/deleteSingleAuditUser")
    public ResponseResult<String> deleteAuditUser(
            @ApiParam(value = "新式令牌", required = true) @RequestHeader(name = "Authorization") String token,
            @ApiParam(value = "用户信息", required = true) @RequestBody AuditUserInfo auditUserInfo) {
        log.info("删除审核用户请求报文: {}", JSONObject.toJSONString(auditUserInfo));
        userConfigManageService.deleteSingleAuditUser(token, auditUserInfo);
        return ResponseResultUtil.success();
    }


}
