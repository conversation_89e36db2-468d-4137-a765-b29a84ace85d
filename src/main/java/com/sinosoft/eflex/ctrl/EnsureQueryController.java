package com.sinosoft.eflex.ctrl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.framework.annotation.SqlInject;
import com.sinosoft.eflex.model.FCContract;
import com.sinosoft.eflex.model.FCMailInfo;
import com.sinosoft.eflex.service.EnsureQueryService;
import com.sinosoft.eflex.service.InsureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 保障信息查询
 *
 * <AUTHOR>
 */
@Api(value = "EnsureController", description = "企业保障信息查询")
@RestController
@RequestMapping(value = "/ensure")
@RequiredArgsConstructor
public class EnsureQueryController {

    private final EnsureQueryService enservice;
    private final InsureService insureService;

    private static final Logger log = LoggerFactory.getLogger(EnsureQueryController.class);

    /**
     * 弹性计划查询
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @ApiOperation(httpMethod = "GET", value = "弹性计划查询", notes = "弹性计划查询接口 By:wyj")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appntYear", value = "投保年度", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "policyState", value = "保单状态", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "beginDate", value = "生效日期起始日", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "endDate", value = "生效日期终止日", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "当前页码", required = false, paramType = "query", dataType = "int", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, paramType = "query", dataType = "int", defaultValue = "10"),
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/getEnsureInfo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @SqlInject(KeywordPrevent = true)
    public String queryEnsureInfo(String appntYear, String policyState, String beginDate, String endDate,
                                  Integer pageNum, Integer pageSize, @RequestHeader String Authorization) {
        Map<String, Object> requestMap = new HashMap<String, Object>(7);
        requestMap.put("appntYear", appntYear);
        requestMap.put("policyState", policyState);
        requestMap.put("beginDate", beginDate);
        requestMap.put("endDate", endDate);
        requestMap.put("pageNum", pageNum);
        requestMap.put("pageSize", pageSize);
        requestMap.put("Authorization", Authorization);
        log.info("查询福利信息请求报文: {}", JSONObject.toJSONString(requestMap));
        String responseInfo = enservice.selectEnsure(Authorization, requestMap);
        log.info("查询福利信息返回报文: {}", responseInfo);
        return responseInfo;
    }

    /**
     * 保障信息查询折线图
     *
     * @param Authorization
     * @return
     * <AUTHOR>
     */
    @ApiOperation(httpMethod = "GET", value = "保障信息查询折线图", notes = "By:wyj")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = false, paramType = "query", dataType = "String")
    })
    @RequestMapping(value = "/getEnsureChart", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String getEnsureChart(@RequestHeader String Authorization, String ensureCode) {
        String responseInfo = enservice.getEnsureChart(Authorization, ensureCode);
        log.info("返回前台报文: {}", responseInfo);
        return responseInfo;
    }

    /**
     * 企业计划汇总查询
     *
     * @param ensureCode
     * @return
     * <AUTHOR>
     */
    @ApiOperation(httpMethod = "GET", value = "企业计划汇总--固定计划", notes = "企业计划汇总 By:wyj")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "policyState", value = "保单状态", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getPlanCollection", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @SqlInject(KeywordPrevent = true)
    public String planCollection(@RequestParam(name = "ensureCode", required = true) String ensureCode, @RequestParam(name = "policyState", required = true) String policyState, @RequestHeader String Authorization) {
        log.info("接收参数-->ensureCode:" + ensureCode);
        String responseInfo = enservice.selectPlanCollection(ensureCode, Authorization);
        log.info("planCollection返回前台报文: {}", responseInfo);
        return responseInfo;
    }

    /**
     * 保障计划列表查询
     *
     * @return
     * <AUTHOR>
     */
    @ApiOperation(httpMethod = "GET", value = "保障计划列表查询", notes = "保障计划列表查询 ")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/getPlanList", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String queryPlanList(@RequestHeader String Authorization) {
        String responseInfo = enservice.queryPlanList(Authorization);
        log.info("返回前台报文: {}", responseInfo);
        return responseInfo;
    }

    /**
     * 员工选择计划查询
     *
     * @param employName
     * @param planName
     * @param pageNum
     * @param pageSize
     * @param department
     * @param Authorization
     * @return
     * <AUTHOR>
     */
    @ApiOperation(httpMethod = "GET", value = "员工选择计划查询", notes = "员工选择计划查询 By:wyj")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "employName", value = "员工姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planName", value = "计划名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "department", value = "部门", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "当前页码", required = false, dataType = "int", paramType = "query", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页的数量", required = false, dataType = "int", paramType = "query", defaultValue = "10"),
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/getEmployPlan", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String queryEmployPlan(String employName, String planName, String ensureCode, String department, Integer pageNum,
                                  Integer pageSize, @RequestHeader String Authorization) {
        Map<String, Object> requestMap = new HashMap<String, Object>(5);
        requestMap.put("employName", employName);
        requestMap.put("planName", planName);
        requestMap.put("department", department);
        requestMap.put("ensureCode", ensureCode);
        requestMap.put("pageNum", pageNum);
        requestMap.put("pageSize", pageSize);
        requestMap.put("Authorization", Authorization);
        String responseInfo = enservice.queryEmployPlan(requestMap);
        log.info("返回前台参数:{}", responseInfo);
        return responseInfo;
    }

    /**
     * 家属选择计划查询
     *
     * @param
     * @param
     * @return
     * <AUTHOR>
     */
    @ApiOperation(httpMethod = "GET", value = "家属选择计划查询", notes = "家属选择计划查询  By:wyj")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "employName", value = "员工姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "relationName", value = "家属姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planName", value = "计划名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "department", value = "部门", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "当前页码", required = false, dataType = "int", paramType = "query", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页的数量", required = false, dataType = "int", paramType = "query", defaultValue = "10"),
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/getRelationPlan", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String queryRelationPlan(String employName, String relationName, String planName, String department, String ensureCode,
                                    Integer pageNum, Integer pageSize, @RequestHeader String Authorization) {
        Map<String, Object> requestMap = new HashMap<String, Object>(5);
        requestMap.put("employName", employName);
        requestMap.put("relationName", relationName);
        requestMap.put("planName", planName);
        requestMap.put("department", department);
        requestMap.put("pageNum", pageNum);
        requestMap.put("pageSize", pageSize);
        requestMap.put("ensureCode", ensureCode);
        requestMap.put("Authorization", Authorization);
        String responseInfo = enservice.queryRelationPlan(requestMap);
        log.info("返回前台参数:{}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(httpMethod = "GET", value = "学生选择计划查询", notes = "学生选择计划查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stuName", value = "学生姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "garName", value = "监护人姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planName", value = "计划名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "当前页码", required = false, dataType = "int", paramType = "query", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页的数量", required = false, dataType = "int", paramType = "query", defaultValue = "10"),
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/queryStudentPlan", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String queryStudentPlan(String stuName, String garName, String planName, String ensureCode,
                                   Integer pageNum, Integer pageSize, @RequestHeader String Authorization) {
        Map<String, Object> requestMap = new HashMap<String, Object>(5);
        requestMap.put("stuName", stuName);
        requestMap.put("garName", garName);
        requestMap.put("planName", planName);
        requestMap.put("pageNum", pageNum);
        requestMap.put("pageSize", pageSize);
        requestMap.put("ensureCode", ensureCode);
        requestMap.put("Authorization", Authorization);
        String responseInfo = enservice.queryStudentPlan(requestMap);
        log.info("返回前台参数:{}", responseInfo);
        return responseInfo;
    }

    /**
     * 投保清单查询(企事业单位投保)
     *
     * @param insuredName
     * @param sex
     * @param department
     * @param planObject
     * @param pageNum
     * @param pageSize
     * @param Authorization
     * @param ensureCode
     * @return
     */
    @ApiOperation(httpMethod = "GET", value = "投保清单查询(企事业单位投保)", notes = "投保清单查询 By:wyj")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "insuredName", value = "被保人姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sex", value = "性别", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "department", value = "部门", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planObject", value = "计划对象", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "当前页码", required = false, dataType = "int", paramType = "query", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页的数量", required = false, dataType = "int", paramType = "query", defaultValue = "10"),
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/getInsuredDetail", method = RequestMethod.GET)
    public String queryInsuredDetail(String insuredName, String sex, String department, String planObject,
                                     Integer pageNum, Integer pageSize, @RequestHeader String Authorization, String ensureCode) {
        Map<String, Object> requestMap = new HashMap<String, Object>(5);
        requestMap.put("insuredName", insuredName);
        requestMap.put("sex", sex);
        requestMap.put("department", department);
        requestMap.put("planObject", planObject);
        requestMap.put("pageNum", pageNum);
        requestMap.put("pageSize", pageSize);
        requestMap.put("ensureCode", ensureCode);
        requestMap.put("Authorization", Authorization);
        String responseInfo = enservice.queryInsuredDetail(requestMap);
        log.info("返回前台参数:{}", responseInfo);
        return responseInfo;
    }

    /**
     * 投保清单查询(学生投保)
     *
     * @param insuredDetail
     * @param Authorization
     * @return
     * <AUTHOR>
     */
    @ApiOperation(httpMethod = "GET", value = "投保清单查询(学生投保)", notes = "投保清单查询 By:hl")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "studentName", value = "学生姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "guarderName", value = "监护人姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planName", value = "计划名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "当前页码", required = false, dataType = "int", paramType = "query", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页的数量", required = false, dataType = "int", paramType = "query", defaultValue = "10"),
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/getStuInsuredDetail", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String queryStuInsuredDetail(String studentName, String guarderName, String planName,
                                        Integer pageNum, Integer pageSize, @RequestHeader String Authorization, String ensureCode) {
        Map<String, Object> requestMap = new HashMap<String, Object>(5);
        requestMap.put("studentName", studentName);
        requestMap.put("guarderName", guarderName);
        requestMap.put("planName", planName);
        requestMap.put("pageNum", pageNum);
        requestMap.put("pageSize", pageSize);
        requestMap.put("ensureCode", ensureCode);
        requestMap.put("Authorization", Authorization);
        String responseInfo = enservice.queryStuInsuredDetail(requestMap);
        log.info("返回前台参数:{}", responseInfo);
        return responseInfo;
    }

    /**
     * 计划详情查询
     *
     * @param planCode
     * @param personId
     * @param Authorization
     * @return
     * <AUTHOR>
     */
    @ApiOperation(httpMethod = "GET", value = "计划详情查询", notes = "计划详情查询  By:wyj")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "planCode", dataType = "String", required = true, paramType = "query", value = "计划编号"),
            @ApiImplicitParam(name = "personId", dataType = "String", required = true, paramType = "query", value = "被被人ID"),
            @ApiImplicitParam(name = "ensureCode", dataType = "String", required = true, paramType = "query", value = "福利编号"),
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/getPlanDetail", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String getPlanDetail(String planCode, String personId, @RequestHeader String Authorization, String ensureCode) {
        log.info("接受前台报文:" + ensureCode);
        log.info("接受前台报文:" + personId);
        log.info("接受前台报文:" + planCode);
        String responseInfo = enservice.getPlanDetail(planCode, personId, Authorization, ensureCode);
        log.info("返回前台参数:{}", responseInfo);
        return responseInfo;
    }

    /**
     * 导出员工选择计划EXCEL
     *
     * @param planDetail
     * @param request
     * @return
     * <AUTHOR>
     */
    @ApiOperation(httpMethod = "GET", value = "导出员工选择计划EXCEL", notes = "导出员工选择计划EXCEL By:wyj")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "employName", value = "员工姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planName", value = "计划名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "department", value = "部门", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "query")})
    @RequestMapping(value = "/exportEmployPlan", method = RequestMethod.GET, produces = {
            "application/vnd.ms-excel; charset=UTF-8"})
    public String exportEmployPlan(String employName, String planName, String department, String Authorization,
                                   HttpServletResponse response, String ensureCode) {
        System.out.println("令牌:" + Authorization);
        Map<String, Object> requestMap = new HashMap<String, Object>(5);
        requestMap.put("employName", employName);
        requestMap.put("planName", planName);
        requestMap.put("department", department);
        requestMap.put("ensureCode", ensureCode);
        requestMap.put("Authorization", Authorization);
        String responseInfo = enservice.exportEmployPlan(requestMap, response);
        log.info("返回前台数据:{}", responseInfo);
        return responseInfo;
    }

    /**
     * 导出家属选择计划EXCEL
     *
     * @param planDetail
     * @param request
     * @return
     * <AUTHOR>
     */
    @ApiOperation(httpMethod = "GET", value = "导出家属选择计划EXCEL", notes = "导出家属选择计划EXCEL By:wyj")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "employName", value = "员工姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "relationName", value = "家属姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planName", value = "计划名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "department", value = "部门", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "query")})
    @RequestMapping(value = "/exportRelationPlan", method = RequestMethod.GET, produces = {
            "application/vnd.ms-excel; charset=UTF-8"})
    public String exportRelationPlan(String relationName, String employName, String ensureCode, String planName, String department,
                                     String Authorization, HttpServletResponse response) {
        Map<String, Object> requestMap = new HashMap<String, Object>(5);
        requestMap.put("employName", employName);
        requestMap.put("relationName", relationName);
        requestMap.put("planName", planName);
        requestMap.put("department", department);
        requestMap.put("ensureCode", ensureCode);
        requestMap.put("Authorization", Authorization);
        String responseInfo = enservice.exportRelationPlan(requestMap, response);
        log.info("返回前台数据:{}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(httpMethod = "GET", value = "导出学生选择计划EXCEL", notes = "导出学生选择计划EXCEL")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stuName", value = "学生姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "garName", value = "监护人姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planName", value = "计划名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "String")})
    @RequestMapping(value = "/exportStudentPlan", method = RequestMethod.GET, produces = {"application/vnd.ms-excel; charset=UTF-8"})
    public String exportStudentPlan(String stuName, String garName, String ensureCode, String planName, String Authorization, HttpServletResponse response) {
        Map<String, Object> requestMap = new HashMap<String, Object>(5);
        requestMap.put("stuName", stuName);
        requestMap.put("garName", garName);
        requestMap.put("planName", planName);
        requestMap.put("ensureCode", ensureCode);
        requestMap.put("Authorization", Authorization);
        String responseInfo = enservice.exportStudentPlan(requestMap, response);
        log.info("返回前台数据:{}", responseInfo);
        return responseInfo;
    }


    /**
     * 导出投保清单EXCEL(企事业单位)
     *
     * @param Authorization
     * @param response
     * <AUTHOR>
     */
    @ApiOperation(httpMethod = "GET", value = "导出投保清单EXCEL(企事业单位)", notes = "导出投保清单EXCEL By:wyj")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "garName", value = "员工姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "garSex ", value = "性别", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "department", value = "部门", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "stuName", value = "学生姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planObject ", value = "计划类型", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planName", value = "计划名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/exportInsureList", method = RequestMethod.GET)
    public String exportInsureList(String Authorization, HttpServletResponse response, String garName, String garSex, String department, String stuName, String planObject, String planName, String ensureCode) {
        Map<String, String> map = new HashMap<>();
        map.put("garName", garName);
        map.put("garSex", garSex);
        map.put("department", department);
        map.put("stuName", stuName);
        map.put("planObject", planObject);
        map.put("planName", planName);
        map.put("ensureCode", ensureCode);
        log.info("导出投保清单EXCEL(企事业单位)请求报文:{},token:{}", JSON.toJSONString(map), Authorization);
        String responseInfo = enservice.exportInsureList(Authorization, response, map);
        log.info("导出投保清单EXCEL(企事业单位)返回报文:" + responseInfo);
        return responseInfo;
    }

    /**
     * 导出投保清单EXCEL(学生投保)
     *
     * @param Authorization
     * @param response
     * <AUTHOR>
     */
    @ApiOperation(httpMethod = "GET", value = "导出投保清单EXCEL(学生投保)", notes = "导出投保清单EXCEL By:hl")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "garName", value = "员工姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "garSex ", value = "性别", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "department", value = "部门", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "stuName", value = "学生姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planObject ", value = "计划类型", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planName", value = "计划名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/exportStuInsureList", method = RequestMethod.GET)
    public String exportStuInsureList(String Authorization, HttpServletResponse response, String garName, String garSex, String department, String stuName, String planObject, String planName, String ensureCode) {
        Map<String, String> map = new HashMap<>();
        map.put("garName", garName);
        map.put("garSex", garSex);
        map.put("department", department);
        map.put("stuName", stuName);
        map.put("planObject", planObject);
        map.put("planName", planName);
        map.put("ensureCode", ensureCode);
        log.info("导出投保清单EXCEL(学生投保)请求报文:{}", JSON.toJSONString(map));
        String responseInfo = enservice.exportStuInsureList(Authorization, response, map);
        log.info("导出投保清单EXCEL(学生投保)返回报文:" + responseInfo);
        return responseInfo;
    }

    /**
     * 家庭成员查询
     *
     * @param Authorization
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "家庭成员查询", notes = "家庭成员查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收的字符集", required = false, dataType = "String", paramType = "header", defaultValue = "UTF-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8")})
    @RequestMapping(value = "/getFamilyInfo", method = RequestMethod.GET)
    public String familySelect(@RequestHeader String Authorization) {
        String familyInfo = enservice.familySelect(Authorization);
        return familyInfo;
    }

    /**
     * 家庭成员福利查询
     *
     * @param personId
     * @param Authorization
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "家庭成员福利查询", notes = "家庭成员福利查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收的字符集", required = false, dataType = "String", paramType = "header", defaultValue = "UTF-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8")})
    @RequestMapping(value = "/getEmpAndFamilyWelfare", method = RequestMethod.GET)
    public String empAndFamilyWelfareSelect(@RequestParam String personId, @RequestHeader String Authorization) {
        String empAndFamilyWelfare = enservice.empAndFamilyWelfareSelect(personId, Authorization);
        return empAndFamilyWelfare;
    }

    /**
     * 查询计划编码是否存在
     *
     * @param planCode
     * @param
     * @return
     */
    @ApiOperation(value = "查询计划编码是否存在", notes = "author:wyj")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "planCode", value = "计划编码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/selectdefultPlanCode", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String selectdefultPlanCode(String planCode, String ensureCode) {
        String responseMsg = enservice.selectdefultPlanCode(planCode, ensureCode);
        log.info("查询计划编码返回报文: " + responseMsg);
        return responseMsg;
    }


    @ApiOperation(value = "HR申请发票")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "grpOrderNo", value = "保险合同号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/referHRMailInfo", method = RequestMethod.POST)
    public String referHRMailInfo(@RequestHeader String Authorization, @RequestBody FCMailInfo fcMailInfo, String grpOrderNo) {
        log.info("\n申请发票入参：fcMailInfo:{},grpOrderNo: {}\n", JSON.toJSONString(fcMailInfo), grpOrderNo);
        String responseInfo = insureService.referMailInfo(Authorization, fcMailInfo, grpOrderNo);
        log.info("\n申请发票返回: {}\n", responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "HR申请合同")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "grpContNo", value = "保险合同号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "edorAppNo", value = "保全受理号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/referContract", method = RequestMethod.POST)
    public String referContract(@RequestHeader String Authorization, @RequestBody FCContract fcContract, String grpContNo, String edorAppNo) {
        String responseInfo = enservice.referContract(Authorization, fcContract, grpContNo, edorAppNo);
        log.info("返回结果" + responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "申请记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/applicationRecord", method = RequestMethod.GET)
    public String applicationRecord(@RequestHeader String Authorization, @RequestParam String ensureCode) {
        String responseInfo = enservice.applicationRecord(Authorization, ensureCode);
        return responseInfo;
    }


    @ApiOperation(value = "获取所在地区")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getLocationMessage", method = RequestMethod.GET)
    public String getLocationMessage(@RequestHeader String Authorization, @RequestParam String placeType, @RequestParam String upplaceCode) {
        String responseInfo = enservice.getLocationMessage(placeType, upplaceCode);
        return responseInfo;
    }


    @ApiOperation(value = "团体合同和个人凭证下载或预览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "contno", value = "保单号（团体或个人）", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ruleCode", value = "规则编码（团体：5E4EECBB06FC75，个人：6495E5A5EC2C961B）", required = true, dataType = "String", paramType = "query")})
    @RequestMapping(value = "/downloadContractOrVoucherFile", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void downloadContractOrVoucherFile(HttpServletResponse response, String contno, String ruleCode) {
        log.info("前台传参：保单号:" + contno + "=============规则：" + ruleCode);
        enservice.downloadContractOrVoucherFile(response, contno, ruleCode);
    }

    @ApiOperation(value = "HR补发纸质合同预申请")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "grpContNo", value = "保险合同号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/ReplacementPreApplication", method = RequestMethod.POST)
    public String ReplacementPreApplication(@RequestHeader String Authorization, String grpContNo) {
        log.info("前台传参" + grpContNo);
        String responseInfo = enservice.ReplacementPreApplication(Authorization, grpContNo);
        log.info("返回结果" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "保险合同日期校验")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/checkDate", method = RequestMethod.POST)
    public String checkDate(@RequestHeader String Authorization, String ensureCode) {
        log.info("!!!!!!!!!!!!!!!!!!!!!!! " + ensureCode);
        String responseInfo = enservice.checkDate(ensureCode);
        log.info("返回结果" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "企业方案汇总--弹性计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = true, paramType = "query", dataType = "String")
    })
    @RequestMapping(value = "/getEfleRiskCollection", method = RequestMethod.GET)
    public String getEfleRiskCollection(@RequestHeader String Authorization, String ensureCode, String policyState) {
        log.info("弹性计划企业方案汇总入参：ensureCode:" + ensureCode + "========policyState：" + policyState);
        String responseInfo = enservice.getEfleRiskCollection(Authorization, ensureCode);
        log.info("弹性计划企业方案汇总解结果：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "弹性计划--投保清单查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "department", value = "部门", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "garName", value = "员工姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "garIDNo ", value = "员工证件号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "gradeLevelCode", value = "员工职级", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "famName ", value = "家属姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "famIDNo", value = "家属证件号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isCheck", value = "操作符", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/queryEfleInsuredDetail", method = RequestMethod.GET)
    public String queryEfleInsuredDetail(@RequestHeader String Authorization, String ensureCode, String department, String garName, String garIDNo, String gradeLevelCode, String famName, String famIDNo, String isCheck) {
        log.info("弹性计划--投保清单查询入参：福利编码：" + ensureCode + "====部门：" + department + "====员工姓名：" + garName + "====员工证件号：" + garIDNo + "====员工职级：" + gradeLevelCode);
        log.info("弹性计划--投保清单查询入参：家属姓名：" + famName + "====家属证件号" + famIDNo + "=====操作符：" + isCheck);
        String responseInfo = enservice.queryEfleInsuredDetail(Authorization, ensureCode, department, garName, garIDNo, gradeLevelCode, famName, famIDNo, isCheck);
        log.info("弹性计划--投保清单查询结果：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "弹性计划--投保清单详情查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderItemNo", value = "子订单详情编号", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/queryInsureRiskInfoByOrderItemNo", method = RequestMethod.GET)
    public String queryInsureRiskInfoByOrderItemNo(@RequestHeader String Authorization, String orderItemNo) {
        log.info("投保清单详情查询入参：子订单详情编号：" + orderItemNo);
        String responseInfo = enservice.queryInsureRiskInfoByOrderItemNo(Authorization, orderItemNo);
        log.info("投保清单详情查询结果：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "弹性计划 - - 导出投保清单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "department", value = "部门", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "garName", value = "员工姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "garIDNo ", value = "员工证件号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "gradeLevelCode", value = "员工职级", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "famName ", value = "家属姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "famIDNo", value = "家属证件号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isCheck", value = "操作符", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/eflexExportPerInfoExcel", method = RequestMethod.GET)
    public String exportEfleInsuredInfoExcel(String Authorization, HttpServletResponse response, String ensureCode, String department, String garName, String garIDNo, String gradeLevelCode, String famName, String famIDNo, String isCheck) {
        log.info("弹性计划导出投保清单请求报文：福利编号：" + ensureCode);
        String responseInfo = enservice.exportEfleInsuredInfoExcel(Authorization, response, ensureCode, department, garName, garIDNo, gradeLevelCode, famName, famIDNo, isCheck);
        log.info("弹性计划导出投保清单返回报文：" + responseInfo);
        return responseInfo;
    }


    /**
     * 导出投保清单EXCEL(被保人清单)
     *
     * @param Authorization
     * @param response
     * <AUTHOR>
     */
    @ApiOperation(httpMethod = "GET", value = "导出投保清单EXCEL(被保人清单)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "String"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/personsInsureList", method = RequestMethod.GET, produces = {"application/vnd.ms-excel; charset=UTF-8"})
    public String personsInsureList(String Authorization, HttpServletResponse response, String ensureCode) {

        log.info("导出投保清单EXCEL(被保人清单)请求报文:{},token:{}", JSON.toJSONString(ensureCode), Authorization);
        String responseInfo = enservice.personsInsureList(Authorization, response, ensureCode);
        log.info("导出投保清单EXCEL(被保人清单)返回报文:" + responseInfo);
        return responseInfo;
    }

}
