package com.sinosoft.eflex.ctrl;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.sinosoft.eflex.service.MaxNoService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * 生成最大号API
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/MaxNo")
@Api(value = "MaxNoController", description = "生成最大号API")
public class MaxNoController {
    private static Logger Log = (Logger) LoggerFactory.getLogger(MaxNoController.class);

    @Autowired
    private MaxNoService maxNoService;

    /**
     * 生成最大号
     *
     * @param person
     * @param headers
     * @return
     */
    @ApiOperation(value = "生成最大号", notes = "生成最大号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cNoType", value = "最大号类型", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "cNoLimit", value = "限制条件", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "cNoLength", value = "最大号长度", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收字符集", required = false, dataType = "String", paramType = "header", defaultValue = "utf-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8")})
    @RequestMapping(value = "/createMaxNo", method = RequestMethod.GET)
    public String createMaxNo(String cNoType, String cNoLimit, int cNoLength) {

        String responseInfo = maxNoService.createMaxNo(cNoType, cNoLimit, cNoLength);
        Log.info("返回前台报文：" + responseInfo);

        return responseInfo;
    }


}
