package com.sinosoft.eflex.ctrl.dailyinsured;

import com.sinosoft.eflex.service.dailyinsure.DailyMakeProposalFormService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/6/9 20:26
 */
@Api(value = "DailyMakeProposalFormController", description = "生成电子投保书")
@RestController
@RequestMapping("/DailyMakeProposalFormController")
public class DailyMakeProposalFormController {

    @Autowired
    private DailyMakeProposalFormService dailyMakeProposalFormService;

    @ApiOperation(value = "生成电子投保书")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "perNo", value = "个人客户号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderItemNo", value = "订单编号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/makeProposalForm", method = RequestMethod.GET)
    public String makeProposalForm(@RequestHeader String Authorization, String ensureCode, String perNo, String orderItemNo) {
        dailyMakeProposalFormService.MakeProposalForm(ensureCode, perNo, orderItemNo);
        
        return "成功";
    }


}
