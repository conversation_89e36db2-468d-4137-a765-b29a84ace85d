package com.sinosoft.eflex.ctrl.dailyinsured;

import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.framework.annotation.SqlInject;
import com.sinosoft.eflex.model.FCBusPersonType;
import com.sinosoft.eflex.model.FCPerInfoTemp;
import com.sinosoft.eflex.service.dailyinsure.FcStaffInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/4/14 15:50
 * @desc
 */

@RestController
@RequestMapping("FcStaffInfoController")
@Api(value = "FcStaffInfoController", description = "员工管理")
public class FcStaffInfoController {

    private static Logger Log = (Logger) LoggerFactory.getLogger(FcStaffInfoController.class);

    @Autowired
    private FcStaffInfoService fcStaffInfoService;

    @ApiOperation(value = "查询员工职级")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "grpNo", value = "企业号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "当前查询页", required = false, dataType = "int", paramType = "query", defaultValue = "1"),
            @ApiImplicitParam(name = "rows", value = "每页记录数", required = true, dataType = "int", paramType = "query", defaultValue = "5")
    })
    @RequestMapping(value = "/getStaffRanks", method = RequestMethod.GET)
    public String getStaffRanks(@RequestHeader String Authorization, String grpNo, int page, int rows) {
        Log.info("\n查询员工职级接口请求:grpNo:{}\n", grpNo);
        String responseInfo = fcStaffInfoService.getStaffRanks(Authorization, grpNo, page, rows);
        Log.info("\n查询员工职级接口结果: {}\n", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "配置员工职级")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "operation", value = "操作标识", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "oldGradeLevelCode", value = "原职级编码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "oldOrderNum", value = "原顺序号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "fcBusPersonType", value = "职级对象", required = true, dataType = "FCBusPersonType", paramType = "body")
    })
    @RequestMapping(value = "/dealStaffRank", method = RequestMethod.POST)
    public String insertStaffRank(@RequestHeader String Authorization, @RequestBody FCBusPersonType fcBusPersonType, String operation, String oldGradeLevelCode, String oldOrderNum) {
        Log.info("\n配置员工职级接口入参：职级对象参数: {},原职级编码: {},原职级顺序: {}\n", JSON.toJSONString(fcBusPersonType), oldGradeLevelCode, oldOrderNum);
        String responseInfo = fcStaffInfoService.dealStaffRank(Authorization, fcBusPersonType, operation, oldGradeLevelCode, oldOrderNum);
        Log.info("\n配置员工职级返回结果: {}\n", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "删除员工职级")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "grpNo", value = "企业号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderNum", value = "职级顺序", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planType", value = "计划类型", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/deleteStaffRank", method = RequestMethod.DELETE)
    public String deleteStaffRank(@RequestHeader String Authorization, String grpNo, String ensureCode, String orderNum, String planType) {
        Log.info("\n员工职级删除入参：企业号:{},福利编号: {},职级顺序: {},计划类型: {}\n", grpNo, ensureCode, orderNum, planType);
        String responseInfo = fcStaffInfoService.deleteStaffRank(Authorization, grpNo, ensureCode, orderNum, planType);
        Log.info("\n员工职级删除返回结果: {}\n", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "职级修改标识")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "grpNo", value = "企业号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderNum", value = "职级顺序", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planType", value = "计划类型", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/isCheckStaffRank", method = RequestMethod.GET)
    public String isCheckStaffRank(@RequestHeader String Authorization, String grpNo, String orderNum, String planType) {
        Log.info("\n职级修改标识入参：企业号:{},职级顺序: {},计划类型: {}\n", grpNo, orderNum, planType);
        String responseInfo = fcStaffInfoService.isCheckStaffRank(Authorization, grpNo, orderNum, planType);
        Log.info("\n职级修改标识返回结果: {}\n", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "查询员工信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "name", value = "姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "iDType", value = "证件类型", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "iDNo", value = "证件号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "levelCode", value = "职级", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "retirement", value = "是否退休", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "tableSign", value = "正式表formal/临时表temp", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "当前查询页", required = true, dataType = "int", paramType = "query", defaultValue = "1"),
            @ApiImplicitParam(name = "rows", value = "每页记录数", required = true, dataType = "int", paramType = "query", defaultValue = "10")
    })
    @RequestMapping(value = "/getStaffInfos", method = RequestMethod.GET)
    @SqlInject(KeywordPrevent = true)
    public String getStaffInfos(@RequestHeader String Authorization, String name, String iDType, String iDNo, String levelCode, String retirement, String tableSign, int page, int rows) {
        Map<String, String> params = new HashMap<>();
        params.put("name", name);
        params.put("iDType", iDType);
        params.put("iDNo", iDNo);
        params.put("levelCode", levelCode);
        params.put("retirement", retirement);
        params.put("tableSign", tableSign);
        Log.info("\n查询员工入参信息: {}\n", params);
        String responseInfo = fcStaffInfoService.getStaffInfos(Authorization, params, page, rows);
        Log.info("\n查询员工接口结果: {}\n", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "企业员工导入")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "status", value = "01添加/02覆盖", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/importGrpStaffExcel", method = RequestMethod.POST)
    public String importGrpStaffExcel(@RequestHeader String Authorization, String status, @RequestParam("fileName") MultipartFile file) {
        String responseInfo = fcStaffInfoService.importGrpStaffExcel(Authorization, status, file);
        Log.info("\n企业员工导入返回前台报文: {}\n", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "企业员工信息配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "operation", value = "操作标识", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "tableSign", value = "正式表formal/临时表temp", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "grpNo", value = "企业号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "fcperinfotemp", value = "员工信息", required = true, dataType = "FCPerInfoTemp", paramType = "body")
    })
    @RequestMapping(value = "/dealStaffInfo", method = RequestMethod.POST)
    public String dealStaffInfo(@RequestHeader String Authorization, @RequestBody FCPerInfoTemp fcperinfotemp, String tableSign, String operation, String grpNo) {
        Log.info("\n员工信息配置入参信息: {},{}\n", operation, grpNo);
        String responseInfo = fcStaffInfoService.dealStaffInfo(Authorization, fcperinfotemp, tableSign, operation, grpNo);
        Log.info("\n员工信息配置返回报文: {}\n", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "职级下拉框")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "grpNo", value = "企业号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getAllRanks", method = RequestMethod.GET)
    public String getAllRanks(String grpNo) {
        Log.info("\n职级下拉框入参信息:grpNo:{}\n", grpNo);
        String responseInfo = fcStaffInfoService.getAllRanks(grpNo);
        Log.info("\n职级下拉框返回报文: {}\n", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "员工信息提交")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "grpNo", value = "企业号", required = true, dataType = "String", paramType = "query"),
    })
    @RequestMapping(value = "/submitStaffInfo", method = RequestMethod.GET)
    public String dealStaffInfo(@RequestHeader String token, String grpNo) {
        Log.info("\n员工信息提交入参信息: {}\n", grpNo);
        String responseInfo = fcStaffInfoService.submitStaffInfo(token, grpNo);
        Log.info("\n员工信息提交返回报文: {}\n", responseInfo);
        return responseInfo;
    }
    @ApiOperation(value = "查询是否设置了职级")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "grpNo", value = "企业号", required = true, dataType = "String", paramType = "query"),
    })
    @RequestMapping(value = "/isHavaRanks", method = RequestMethod.GET)
    public String isHavaRanks(@RequestHeader String Authorization, String grpNo) {
        Log.info("\n查询员工职级接口请求:grpNo:{}\n", grpNo);
        String responseInfo = fcStaffInfoService.isHavaRanks(Authorization, grpNo);
        Log.info("\n查询员工职级接口结果: {}\n", responseInfo);
        return responseInfo;
    }
}
