package com.sinosoft.eflex.ctrl.dailyinsured;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hqins.common.base.ApiResult;
import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.framework.annotation.SqlInject;
import com.sinosoft.eflex.model.FCMailInfo;
import com.sinosoft.eflex.model.FDPlace;
import com.sinosoft.eflex.model.dailyplan.DailyInsureInfo;
import com.sinosoft.eflex.service.dailyinsure.DailyPlanInsureService;
import com.sinosoft.eflex.service.dailyinsure.DailyPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> wenying Xia
 * @date : 2020-04-15 10:35
 **/
@RestController
@RequestMapping("/DailyPlanController")
@Api(value = "DailyPlanController", description = "日常计划")
@RequiredArgsConstructor
@Slf4j
public class DailyPlanController {
    private final DailyPlanService dailyPlanService;
    private final DailyPlanInsureService dailyPlanInsureService;

    /**
     * 初审日常计划信息查询
     *
     * @param Authorization
     * @return
     */
    @ApiOperation(value = "日常计划查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getDailyplan", method = RequestMethod.POST)
    @SqlInject(KeywordPrevent = true)
    public String getDailyplan(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        int pageNo = Integer.parseInt(params.get("pageNumber"));
        int pageSize = Integer.parseInt(params.get("pageSize"));
        String isReal = params.get("isReal");
        log.info("日常计划查询--接口入参：Authorization: {}，LogPrintUntil: {}", Authorization, JsonUtil.toJSON(params));
        String responseInfo = dailyPlanService.getDailyplanList(Authorization, params, pageNo, pageSize, isReal);
        log.info("日常计划查询--返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "日常计划-保存计划信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/savePlanInfo", method = RequestMethod.POST)
    public String savePlanInfo(@RequestHeader String Authorization, @RequestBody Map<String, Object> params) {
        log.info("日常计划-保存计划信息--接口入参：params:{}", JSON.toJSONString(params));
        String responseInfo = dailyPlanService.savePlanInfo(Authorization, params);
        log.info("日常计划-保存计划信息--返回前台报文: {}", responseInfo);
        return responseInfo;
    }

    /**
     * 日常计划定制 基本信息页面 回显  ---查看
     *
     * @param ensureCode
     * @return
     */
    @ApiOperation(value = "日常计划基本信息页面初始化")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getEnsureInfoByCodeList", method = RequestMethod.GET)
    public String getEnsureInfoByCodeList(@RequestHeader String Authorization, String ensureCode) {
        log.info("日常计划-基础信息初始化入参：" + ensureCode);
        String responseInfo = dailyPlanService.getBaseInfoByCodeList(Authorization, ensureCode);
        log.info("日常计划-基础信息初始化结果：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "日常计划-日常计划列表删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "PrtNo", value = "投保单号", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/deleteEnsureInfoByCodeList", method = RequestMethod.DELETE)
    public String deleteEnsureInfoByCodeList(@RequestHeader String Authorization, String ensureCode, String PrtNo) {
        log.info("日常计划列表删除入参：ensureCode:" + ensureCode + "sha  PrtNo:" + PrtNo);
        String responseInfo = dailyPlanService.deleteEnsureInfoByCodeList(Authorization, ensureCode, PrtNo);
        log.info("日常计划列表删除结果：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "日常计划-企业信息和联系人信息 更改保存")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/saveGrpInfo", method = RequestMethod.POST)
    public String saveGrpInfo(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        log.info("日常计划企业信息保存入参：" + params);
        String responseInfo = dailyPlanService.saveGrpInfo(Authorization, params);
        log.info("日常计划企业信息保存结果：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "初审-日常计划-被保险人保费实时变化")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/changePrem", method = RequestMethod.POST)
    public String changePrem(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        log.info("被保险人保费实时变化入参params:{}", params);
        String responseInfo = dailyPlanService.changePrem(Authorization, params);
        log.info("被保险人保费实时变化结果：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "计算周岁+实时保费计算")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getAgeAndChangePrem", method = RequestMethod.POST)
    public String getAgeAndChangePrem(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        log.info("计算周岁+实时保费计算入参params:{}", params);
        String responseInfo = dailyPlanService.getAgeAndChangePrem(Authorization, params);
        log.info("计算周岁+实时保费计算变化结果：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "校验业务员工号", notes = "校验业务员工号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "clientNo", value = "clientNo", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "birthDay", value = "出生日期", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "idTypeEndDate", value = "证件有效期", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "计划编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "nativeplace", value = "国籍", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "iDType", value = "身份证类型", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "iDNo", value = "身份证号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sex", value = "性别", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "name", value = "姓名", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getClientoInfoDaily", method = RequestMethod.GET)
    public String getClientoInfo(@RequestParam String clientNo, String birthDay, String idTypeEndDate, String ensureCode, String nativeplace, String iDType, String iDNo, String sex, String name) {
        log.info("校验业务员工号,入参：" + clientNo + "===" + birthDay + "===" + idTypeEndDate + "===" + ensureCode + "===" + nativeplace
                + "===" + iDType + "===" + iDNo + "===" + sex + "===" + name);
        String clientoInfo = dailyPlanService.getClientoInfo(clientNo, birthDay, idTypeEndDate, ensureCode, nativeplace, iDType, iDNo, sex, name);
        log.info("返回前台报文：" + clientoInfo);
        return clientoInfo;
    }

    @ApiOperation(value = "日常计划定制，回显 企业信息和联系人信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/selectthegrpinfo", method = RequestMethod.POST)
    public String selectthegrpinfo(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        String ensureCode = params.get("ensureCode");
        String grpNo = params.get("grpNo");
        String customNo = params.get("contactNo");
        String isOperation = params.get("isOperation");
        //打印入参
        Map<String, String> map = new HashMap<>();
        map.put("ensureCode", ensureCode);
        map.put("grpNo", grpNo);
        map.put("customNo", customNo);
        map.put("isOperation", isOperation);
        log.info(" 回显企业信息和联系人信息接口入参为:{}", JsonUtil.toJSON(map));
        //业务处理
        String responseInfo = dailyPlanService.initEnsureInfoAdmin_Daily(Authorization, ensureCode, grpNo, customNo, isOperation);
        //打印响应报文
        log.info(" 回显企业信息和联系人信息接口返回前台报文::" + responseInfo);
        return responseInfo;
    }

    /**
     * 检查企业信息  是否保存
     * <p>
     * 参数 params  里面包括联系人信息 和 EnsureCode
     *
     * @param Authorization
     * @param params
     * @return
     */
    @RequestMapping(value = "/checkgrpInfoisSave", method = RequestMethod.POST)
    public String checkgrpInfoisSave(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        //打印入参
        log.info("检查企业信息是否保存--前台传参：params: {}", JSON.toJSONString(params));
        //业务处理
        String responseInfo = dailyPlanService.checkgrpInfoisSave(Authorization, params);
        //打印响应报文
        log.info("检查企业信息是否保存--返回结果: {}", responseInfo);
        return responseInfo;
    }

    /**
     * 查询该企业是否有可投保计划（即是否建立过基础单）
     *
     * @param Authorization
     * @param UnifiedsociCode
     * @return
     */
    @RequestMapping(value = "/checkIsHavedDaily", method = RequestMethod.POST)
    public String checkIsHavedDaily(@RequestHeader String Authorization, String UnifiedsociCode) {
        //打印入参
        log.info("该企业是否已经生成过基础单入参::" + UnifiedsociCode);
        //业务处理
        String responseInfo = dailyPlanService.checkIsHavedDaily(Authorization, UnifiedsociCode);
        //打印响应报文
        log.info("检查该企业是否已经生成过基础单返回前台报文：" + responseInfo);
        return responseInfo;
    }


    /**
     * 维护被保险人信息，同时调用基础单预核保接口
     *
     * @param Authorization
     * @param params
     * @return
     */
    @RequestMapping(value = "/saveissuredInfo", method = RequestMethod.POST)
    public String saveissuredInfo(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        log.info("维护被保险人信息--前台传参: {}", JSON.toJSONString(params));
        String responseInfo = dailyPlanService.saveissuredInfo(Authorization, params);
        log.info("维护被保险人信息--返回前台报文：" + responseInfo);
        return responseInfo;
    }


    /**
     * 查看被保险人信息（最少三个人)
     *
     * @param Authorization
     * @param pageNo
     * @param pageSize
     * @param isReal
     * @return
     */
    @ApiOperation(value = "查看被保险人信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "日常福利编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNo", value = "当前查询页", required = true, dataType = "int", paramType = "query", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页记录数", required = true, dataType = "int", paramType = "query", defaultValue = "10"),
            @ApiImplicitParam(name = "isReal", value = "权限标识", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getIsensurePerson", method = RequestMethod.GET)
    public String getIsensurePerson(@RequestHeader String Authorization, String ensureCode, int pageNo, int pageSize, String isReal) {
        log.info("查看被保险人信息-接口入参：" + Authorization + "====和====" + ensureCode);
        String responseInfo = dailyPlanService.getIsensurePerson(Authorization, ensureCode, pageNo, pageSize, isReal);
        log.info("查看被保险人信息-返回前台报文：" + responseInfo);
        return responseInfo;
    }

    /**
     * 省市县
     *
     * @param placeCode 编号
     * @return 地址集合
     */
    @ApiOperation(value = "地址三级联动", notes = "三级联动")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "placeCode", value = "编码", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/threeAddress", method = RequestMethod.POST)
    public List<FDPlace> threeAddress(String placeCode) {
        log.info("三级联动-接收前台报文:" + placeCode);
        List<FDPlace> insertInfo = dailyPlanService.getThreeAddress(placeCode);
        log.info("三级联动-返回前台报文：" + JSON.toJSONString(insertInfo));
        return insertInfo;
    }


    /**
     * 返回上一步修改计划，之后再次校验被保险人列表
     * 校验投保年龄、校验可投保计划是否合格、校验保费是否发生变化。
     *
     * @param Authorization
     * @param ensureCode
     * @return
     */
    @ApiOperation(value = "日常计划-提交审核时-最后校验。")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/checkPeoples", method = RequestMethod.GET)
    public String checkPeoples(@RequestHeader String Authorization, String ensureCode) {
        log.info("基础单初审二次校验-接口入参：" + ensureCode);
        String responseInfo = dailyPlanService.checkPeoples(Authorization, ensureCode);
        log.info("基础单初审二次校验-接口返回报文：" + responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "日常计划-被保险人信息修改 按钮")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
    })
    @RequestMapping(value = "/updateIsensuredPeople", method = RequestMethod.POST)
    public String updateIsensuredPeople(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        log.info("被保险人信息修改入参：" + params);
        String responseInfo = dailyPlanService.updateIsensuredPeople(Authorization, params);
        log.info("被保险人信息修改结果：" + responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "日常计划-被保险人列表删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "IDNo", value = "证件号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "grpNo", value = "企业客户号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "grpOrderNo", value = "团体订单号", required = true, dataType = "String", paramType = "query")

    })
    @RequestMapping(value = "/deleteisEnsuredPeople", method = RequestMethod.DELETE)
    public String deleteisEnsuredPeople(@RequestHeader String Authorization, String ensureCode, String IDNo, String grpNo, String grpOrderNo) {
        log.info("被保险人列表删除-前台传参：ensureCode: {}，证件号为: {}，企业客户号: {}，团体订单号: {}", ensureCode, IDNo, grpNo, grpOrderNo);
        String responseInfo = dailyPlanService.deleteisEnsuredPeople(Authorization, ensureCode, IDNo, grpNo, grpOrderNo);
        log.info("被保险人列表删除-返回结果: {}", JSON.toJSONString(responseInfo));
        return responseInfo;
    }

    @ApiOperation(value = "日常计划-初次 校验被保险人")
    @RequestMapping(value = "/checkIsensureInfofirst", method = RequestMethod.POST)
    public String checkIsensureInfofirst(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        log.info("初次校验被保人信息是否有误--接口入参：params:{}", JSON.toJSONString(params));
        String responseInfo = dailyPlanService.checkIsensureInfofirst(Authorization, params);
        log.info("初次校验被保人信息是否有误--返回结果: {}", responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "日常计划-变更计划添加可投保计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/addDailyPlan", method = RequestMethod.POST)
    public String addDailyPlan(@RequestHeader String Authorization, @RequestBody Map<String, Object> params) {
        log.info("变更计划添加可投保计划接口入参：" + params);
        String responseInfo = dailyPlanService.addDailyPlan(Authorization, params);
        log.info("变更计划添加可投保计划接口结果：" + responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "日常计划-点击变更计划回显数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "出生日期", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/addDailyPlanInfo", method = RequestMethod.GET)
    public String addDailyPlanInfo(@RequestHeader String Authorization, String ensureCode) {
        log.info("变点击变更计划回显数据接口入参：" + ensureCode);
        String responseInfo = dailyPlanService.addDailyPlanInfo(Authorization, ensureCode);
        log.info("点击变更计划回显数据接口结果：" + responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "日常计划定制复核-新增计划的审核 操作 回显数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/selectAddPlanInfo", method = RequestMethod.GET)
    public String selectAddPlanInfo(@RequestHeader String Authorization, String ensureCode) {
        log.info(" 复核新增计划的审核入参为::" + ensureCode + "===" + Authorization);
        String responseInfo = dailyPlanService.initEnsureInfoAdmin(Authorization, ensureCode);//包括企业信息和联系人信息
        log.info(" 复核新增计划的审核返回前台报文::" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "日常计划签单失败 失败原因 回显数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/selectError", method = RequestMethod.GET)
    public String selectError(@RequestHeader String Authorization, String ensureCode) {
        log.info(" 日常计划签单失败 失败原因 回显数据::" + ensureCode + "===" + Authorization);
        String responseInfo = dailyPlanService.selectError(Authorization, ensureCode);
        log.info(" 日常计划签单失败 失败原因 回显数据::" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "日常计划定制复核-变更计划的审核操作 回显数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/selectUpdatePlanInfo", method = RequestMethod.GET)
    public String selectUpdatePlanInfo(@RequestHeader String Authorization, String ensureCode) {
        log.info(" 复核变更计划的审核操作入参为::" + ensureCode + "===" + Authorization);
        String responseInfo = dailyPlanService.selectUpdatePlanInfo(Authorization, ensureCode);//包括企业信息和联系人信息
        log.info(" 复核变更计划的审核操作返回前台报文::" + responseInfo);
        return responseInfo;
    }


    /**
     * 管理员复核 日常计划信息查询
     *
     * @param Authorization
     * @return
     */
    @ApiOperation(value = "管理员复核日常计划查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getDailyplan_check", method = RequestMethod.POST)
    @SqlInject(KeywordPrevent = true)
    public String getDailyplan_check(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        int pageNo = Integer.parseInt(params.get("pageNo"));
        int pageSize = Integer.parseInt(params.get("pageSize"));
        String isReal = params.get("isReal");
        log.info("管理员复核日常计划查询-后台接口入参：Authorization: {},params:{}", Authorization, JsonUtil.toJSON(params));
        String responseInfo = dailyPlanService.getDailyplan_check(Authorization, params, pageNo, pageSize, isReal);
        log.info("管理员复核日常计划查询-返回前台报文: {}", responseInfo);
        return responseInfo;
    }

    /**
     * 管理员复核 日常计划审核通过接口
     *
     * @param Authorization
     * @param ensureCode
     * @return
     */
    @ApiOperation(value = "管理员复核-变更计划审核通过接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "unifiedsociCode", value = "统一社会信用代码", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/Dailyplan_pass", method = RequestMethod.GET)
    public String Dailyplan_pass(@RequestHeader String Authorization, String ensureCode, String unifiedsociCode, Boolean greenInsurance) {
        log.info("日常计划审核通过接口入参：" + Authorization + "====" + ensureCode + "====" + unifiedsociCode);
        String responseInfo = dailyPlanService.Dailyplan_pass(Authorization, ensureCode, unifiedsociCode, greenInsurance);
        log.info("日常计划审核通过接口-返回前台报文：" + responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "日常计划签单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = true, dataType = "String", paramType = "query")})
    @RequestMapping(value = "/dailyIssue", method = RequestMethod.GET)
    public ApiResult<Void> dailyIssue(@RequestHeader String Authorization, String ensureCode) {
        log.info("日常计划-签单操作接口请求报文：ensureCode:{}", ensureCode);
        dailyPlanInsureService.dailyPlanInsure(Authorization, ensureCode);
        return ApiResult.ok();
    }

    @ApiOperation(value = "HR-日常计划列表页面及查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureName", value = "计划名称", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "planType", value = "计划类型", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "policyState", value = "保单状态", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "beginDate", value = "生效日期起始日", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "endDate", value = "生效日期终止日", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "pageNo", value = "当前查询页", required = true, dataType = "int", paramType = "query", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页记录数", required = true, dataType = "int", paramType = "query", defaultValue = "10")
    })
    @RequestMapping(value = "/getDailyPlanList", method = RequestMethod.GET)
    @SqlInject(KeywordPrevent = true)
    public String getDailyPlanList(@RequestHeader String Authorization, String ensureName, String planType, String policyState, String beginDate, String endDate,
                                   int pageNo, int pageSize) {
        Map<String, String> params = new HashMap<String, String>(5);
        params.put("ensureName", ensureName);
        params.put("planType", planType);
        params.put("policyState", policyState);
        params.put("beginDate", beginDate);
        params.put("endDate", endDate);
        log.info("HR-日常计划列表页面及查询接口入参: {}", params);
        String responseInfo = dailyPlanService.getDailyPlanList(Authorization, params, pageNo, pageSize);
        log.info("HR-日常计划列表页面及查询-返回前台报文: {}" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(httpMethod = "GET", value = "日常计划查看保单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = true, paramType = "query", dataType = "String"),
    })
    @RequestMapping(value = "/getDailyPolicy", method = RequestMethod.GET)
    public String getDailyPolicy(@RequestHeader String Authorization, String ensureCode) {
        log.info("日常计划保单查询入参：ensureCode:{}", ensureCode);
        String responseInfo = dailyPlanService.getDailyPolicy(Authorization, ensureCode);
        log.info("日常计划保单查询入参返回前台报文:{}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "日常计划--投保详情查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderItemNo", value = "子订单号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getInsureInfo", method = RequestMethod.GET)
    public String getInsureInfo(@RequestHeader String Authorization, String orderItemNo) {
        log.info("投保清单详情查询入参：被保险人id: {}", orderItemNo);
        String responseInfo = dailyPlanService.getInsureInfo(Authorization, orderItemNo);
        log.info("投保清单详情查询结果: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "日常计划--投保清单查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "garName", value = "员工姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "garIDNo ", value = "员工证件号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "gradeLevelCode", value = "员工职级", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "famName ", value = "家属姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "famIDNo", value = "家属证件号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isCheck", value = "操作符", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getDailyInsuredDetail", method = RequestMethod.GET)
    public String getDailyInsuredDetail(@RequestHeader String Authorization, String ensureCode, String garName, String garIDNo, String gradeLevelCode, String famName, String famIDNo, String isCheck) {
        log.info("弹性计划--投保清单查询入参：福利编码：" + ensureCode + "====员工姓名：" + garName + "====员工证件号：" + garIDNo + "====员工职级：" + gradeLevelCode);
        log.info("弹性计划--投保清单查询入参：家属姓名：" + famName + "====家属证件号" + famIDNo + "=====操作符：" + isCheck);
        String responseInfo = dailyPlanService.getDailyInsuredDetail(Authorization, ensureCode, garName, garIDNo, gradeLevelCode, famName, famIDNo, isCheck);
        log.info("弹性计划--投保清单查询结果：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "查询职业码表-限制在 1-4 类", notes = "查询码表")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/getOccupationCode", method = RequestMethod.GET)
    public String getOccupationCode(@RequestHeader String Authorization, @RequestParam String codeType, @RequestParam String codeDesc) {
        log.info("请求报文：" + codeDesc + "__CodeType:" + codeType);
        String codeInfo = dailyPlanService.getOccupationCode(codeType, codeDesc);
        log.info("返回前台报文：" + codeInfo);
        return codeInfo;
    }

    @ApiOperation(value = "作废计划确定按钮", notes = "查询码表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = true, paramType = "query", dataType = "query")
    })
    @RequestMapping(value = "/voidPlan", method = RequestMethod.GET)
    public String voidPlan(@RequestHeader String Authorization, @RequestParam String ensureCode) {
        log.info("作废计划请求报文：" + Authorization + "__ensureCode:" + ensureCode);
        String codeInfo = dailyPlanService.voidPlan(Authorization, ensureCode);
        log.info("作废计划返回前台报文：" + codeInfo);
        return codeInfo;
    }

    @ApiOperation(value = "HR申请发票")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "grpContNo", value = "团体保单号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/referHRMailInfo", method = RequestMethod.POST)
    public String referHRMailInfo(@RequestHeader String Authorization, @RequestBody FCMailInfo fcMailInfo, String grpContNo) {
        log.info("申请发票入参：fcMailInfo:{},grpContNo: {}", JSON.toJSONString(fcMailInfo), grpContNo);
        String responseInfo = dailyPlanService.referMailInfo(Authorization, fcMailInfo, grpContNo);
        log.info("申请发票返回: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "团体合同和个人凭证下载或预览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "contno", value = "保单号（团体或个人）", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getGuaranteeDetail", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void getGuaranteeDetail(HttpServletResponse response, String contno) {
        dailyPlanService.getGuaranteeDetail(response, contno);
    }


    @ApiOperation(value = "查询职业码表-限制在 1-6类", notes = "查询码表")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/getOccupationCode1", method = RequestMethod.GET)
    public String getOccupationCode1(@RequestHeader String Authorization, @RequestParam String codeType, @RequestParam String codeDesc) {
        log.info("请求报文：" + codeDesc + "__CodeType:" + codeType);
        String codeInfo = dailyPlanService.getOccupationCode1(codeType, codeDesc);
        log.info("返回前台报文：" + codeInfo);
        return codeInfo;
    }


    @ApiOperation(value = "获取省市县三级信息", notes = "查询地区表")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/getAreaInfo", method = RequestMethod.GET)
    public String getAreaInfo(@RequestHeader String Authorization) {
        String codeInfo = dailyPlanService.getAreaInfo();
        return codeInfo;
    }

    @ApiOperation(value = "日常计划-查询险种编码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/queryRiskList", method = RequestMethod.POST)
    public String queryRiskList(@RequestHeader String Authorization, @RequestBody Map<String, String> map) {
        log.info("日常计划-查询险种编码接收前台报文:{},{}", Authorization, map);
        String responseInfo = dailyPlanService.queryRiskList(map);
        log.info("日常计划-查询险种编码返回前台报文: {}", JSON.toJSONString(responseInfo));
        return responseInfo;
    }

    @ApiOperation(value = "日常计划-根据险种编码查询计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "riskCode", value = "险种编码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/queryPlanListByRiskCode", method = RequestMethod.GET)
    public String queryPlanListByRiskCode(@RequestHeader String Authorization, @RequestParam String riskCode, @RequestParam String ensureCode) {
        log.info("日常计划-根据险种编码查询计划接收前台报文:Authorization: {}，riskCode: {},ensureCode: {}", Authorization, riskCode, ensureCode);
        String responseInfo = dailyPlanService.queryPlanListByRiskCode(Authorization, riskCode, ensureCode);
        log.info("日常计划-根据险种编码查询计划返回前台报文:{}", JSON.toJSONString(responseInfo));
        return responseInfo;
    }

    @ApiOperation(value = "日常计划-根据年龄和险种编码展示交费信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/autoGetPlanInfoByAgeAndRiskCode", method = RequestMethod.POST)
    public String autoGetPlanInfoByAgeAndRiskCode(@RequestHeader String Authorization, @RequestBody DailyInsureInfo dailyInsureInfo) {
        log.info("日常计划-根据年龄和险种编码展示交费信息--接口入参：Authorization:{},map:{}", Authorization,
                JSONObject.toJSON(dailyInsureInfo));
        String responseInfo = dailyPlanService.autoGetPlanInfoByAgeAndRiskCode(dailyInsureInfo);
        log.info("日常计划-根据年龄和险种编码展示交费信息--返回报文: {}", responseInfo);
        return responseInfo;
    }

    /**
     * 实现文件上传
     */
    @ResponseBody
    @ApiOperation(value = "日常计划基础单添加人员相关文件直接上传sFtp")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idImage1", value = "证件影像件1", dataType = "file", required = false),
            @ApiImplicitParam(name = "idImage2", value = "证件影像件2", dataType = "file", required = false),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", required = false)
    })
    @RequestMapping(value = "/personnelFileUpload", method = RequestMethod.POST)
    public String personnelFileUpload(MultipartFile idImage1, MultipartFile idImage2, String ensureCode) {
        Map<String, MultipartFile> files = new HashMap<>();
        if (idImage1 != null) {
            files.put("idImage1", idImage1);
        }
        if (idImage2 != null) {
            files.put("idImage2", idImage2);
        }
        log.info("日常计划基础单添加人员相关文件直接上传 size{},ensureCode:{}", files.size(), ensureCode);
        String responseInfo = dailyPlanService.personnelFileUpload(files, ensureCode);
        log.info("日常计划基础单添加人员相关文件直接上传完成：" + responseInfo);
        return responseInfo;

    }

    @ApiOperation(value = "日常计划退回")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "returnNode", value = "退回节点", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "returnReason", value = "退回原因", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/dailyPlanReturn", method = RequestMethod.GET)
    public String efelxEnsureReturn(@RequestHeader String Authorization, String ensureCode, String returnNode, String returnReason) {
        //  returnNode  0--退回HR定制   1--退回初审岗定制  2--hr退回初审岗定制
        log.info("日常计划退回前台传参：福利编号: {},退回节点: {}，退回原因: {}", ensureCode, returnNode, returnReason);
        String responseInfo = dailyPlanService.dailyPlanReturn(Authorization, ensureCode, returnNode, returnReason);
        log.info("日常计划退回返回结果: {}", JSON.toJSONString(responseInfo));
        return responseInfo;
    }
}

