package com.sinosoft.eflex.ctrl.dailyinsured;

import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.model.dailyplan.ConfirmSignReq;
import com.sinosoft.eflex.model.dailyplan.PersonInsuredReq;
import com.sinosoft.eflex.service.dailyinsure.DailyIssueService;
import com.sinosoft.eflex.service.dailyinsure.StaffInsureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/12 15:47
 * @desc
 */

@RestController
@RequestMapping("StaffInsureController")
@Api(value = "StaffInsureController", description = "日常计划员工投保")
public class StaffInsureController {

    private static Logger Log = (Logger) LoggerFactory.getLogger(StaffInsureController.class);

    @Autowired
    private StaffInsureService staffInsureService;
    @Autowired
    private DailyIssueService dailyIssueService;

    @ApiOperation(value = "投保信息查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "perNo", value = "客户号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "personId", value = "被保人id", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderItemNo", value = "子订单号", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getInsuredInfo", method = RequestMethod.GET)
    public String getInsuredInfo(@RequestHeader String Authorization, String ensureCode, String perNo, String personId, String orderItemNo) {
        Log.info("\n投保信息查询入参信息:ensureCode:{},perNo:{},personId:{},orderItemNo:{}\n", ensureCode, perNo, personId, orderItemNo);
        String responseInfo = staffInsureService.getInsuredInfo(Authorization, ensureCode, perNo, personId, orderItemNo);
        Log.info("\n投保信息查询返回报文: {}\n", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "通过关系查询被保人信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "perNo", value = "客户号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "relation", value = "与员工关系", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderItemNo", value = "子订单号", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getInsuredByRelation", method = RequestMethod.GET)
    public String getInsuredByRelation(@RequestHeader String Authorization, String perNo, String relation, String orderItemNo) {
        Log.info("\n查询被保人信息入参信息:perNo:{},relation:{},orderItemNo:{}\n", perNo, relation, orderItemNo);
        String responseInfo = staffInsureService.getInsuredByRelation(Authorization, perNo, relation, orderItemNo);
        Log.info("\n查询被保人信息返回报文: {}\n", responseInfo);
        return responseInfo;
    }
   @ApiOperation(value = "通过personID查询被保人家属信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personId", value = "被保人id", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getInsuredByPersonId", method = RequestMethod.GET)
    public String getInsuredByPersonId(@RequestHeader String Authorization, String personId) {
        Log.info("\n查询被保人家属信息入参信息:perNo:{},relation:{}\n",personId);
        String responseInfo = staffInsureService.getInsuredByPersonId(Authorization,personId);
        Log.info("\n查询被保人家属信息返回报文: {}\n", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "我要投保")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personInsuredReq", value = "投保信息", required = true, dataType = "PersonInsuredReq", paramType = "body"),
    })
    @RequestMapping(value = "/personInsured", method = RequestMethod.POST)
    public String getInsuredByRelation(@RequestHeader String Authorization, @RequestBody PersonInsuredReq personInsuredReq) {
        Log.info("\n我要投保入参信息:perNo:{}\n", personInsuredReq.getPerNo());
        String responseInfo = staffInsureService.personInsured(Authorization, personInsuredReq);
        Log.info("\n我要投保返回报文: {}\n", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "订单信息查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "perNo", value = "个人客户号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "personId", value = "被保人id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderItemNo", value = "订单编号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getOrderInfo", method = RequestMethod.GET)
    public String getOrderInfo(@RequestHeader String Authorization, String ensureCode, String perNo, String personId, String orderItemNo) {
        Log.info("\n订单信息查询入参信息：ensureCode: {}，perNo:{},personId:{},orderItemNo:{}\n", ensureCode, perNo, personId, orderItemNo);
        String responseInfo = staffInsureService.getOrderInfo(Authorization, ensureCode, perNo, personId, orderItemNo);
        Log.info("\n订单查询返回信息: {}\n", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "查询签名信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "perNo", value = "个人客户号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "personId", value = "被保人id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderItemNo", value = "订单编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "shareSign", value = "分享标识符", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getConfirmInfo", method = RequestMethod.GET)
    public String getConfirmInfo(@RequestHeader String Authorization, String ensureCode, String perNo, String personId, String orderItemNo, String shareSign) {
        Log.info("\n确认签名查询入参信息：ensureCode: {}，perNo:{},personId:{},orderItemNo:{},shareSign:{}\n", ensureCode, perNo, personId, orderItemNo, shareSign);
        String responseInfo = staffInsureService.getConfirmInfo(Authorization, ensureCode, perNo, personId, orderItemNo, shareSign);
        Log.info("\n确认签名查询返回信息: {}\n", responseInfo);
        return responseInfo;
    }
    @ApiOperation(value = "查询分享签名信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderItemNo", value = "订单编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "shareSign", value = "分享标识符", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getConfirmInfoShare", method = RequestMethod.GET)
    public String getConfirmInfoShare(@RequestHeader String Authorization,String orderItemNo, String shareSign) {
        Log.info("\n确认签名查询入参信息：Authorization:{} ,orderItemNo:{},shareSign:{}\n", Authorization, orderItemNo, shareSign);
        String responseInfo = staffInsureService.getConfirmInfoShare(Authorization,orderItemNo, shareSign);
        Log.info("\n确认签名查询返回信息: {}\n", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "确认签名")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "confirmSignReq", value = "投保信息", required = true, dataType = "ConfirmSignReq", paramType = "body")
    })
    @RequestMapping(value = "/confirmSignInfo", method = RequestMethod.POST)
    public String confirmSignInfo(@RequestHeader String Authorization, @RequestBody ConfirmSignReq confirmSignReq) {
        Log.info("\n确认签名入参信息:perNo:{}\n", confirmSignReq.getPerNo());
        String responseInfo = staffInsureService.confirmSignInfo(Authorization, confirmSignReq);
        Log.info("\n确认签名查询返回信息: {}\n", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "删除受益人")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderItemNo", value = "子订单号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "bnfNo", value = "受益人编号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/deleteBnf", method = RequestMethod.GET)
    public String deleteBnf(@RequestHeader String Authorization, String orderItemNo, String bnfNo) {
        Log.info("\n删除受益人入参信息:orderItemNo:{},bnfNo:{}\n", orderItemNo, bnfNo);
        String responseInfo = staffInsureService.deleteBnf(Authorization, orderItemNo, bnfNo);
        Log.info("\n删除受益人返回信息: {}\n", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "删除影像")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "imageNo", value = "", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/deletePersonImage", method = RequestMethod.GET)
    public String deletePersonImage(@RequestHeader String Authorization, String imageNo) {
        Log.info("\n删除影像入参信息:imageNo:{}\n", imageNo);
        String responseInfo = staffInsureService.deletePersonImage(Authorization, imageNo);
        Log.info("\n删除影像返回信息: {}\n", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "自然人签单测试")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "funcFlag", value = "操作标志", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "perNo", value = "个人客户号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderItemNo", value = "子订单号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/dailyIssue", method = RequestMethod.GET)
    public String dailyIssue(@RequestHeader String Authorization, String ensureCode, String funcFlag, String perNo, String orderItemNo) {
        Log.info("\n自然人签单接口入参：ensureCode:{}\n", ensureCode);
        Map<String, Object> paramsMap = dailyIssueService.dailyNaturalSignBill(ensureCode, funcFlag, perNo, orderItemNo);
        Log.info("\n自然人签单返回前台报文: {}\n", JSON.toJSONString(paramsMap));
        return JSON.toJSONString(paramsMap);
    }


}
