package com.sinosoft.eflex.ctrl.dailyinsured;


import com.sinosoft.eflex.service.dailyinsure.DailyPlanPhoneService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


/**
 * <AUTHOR> wenying Xia
 * @date : 2020-05-12 11:25
 **/
@RestController
@RequestMapping("/DailyPlanPhoneController")
@Api(value = "DailyPlanPhoneController", description = "日常计划移动端")
public class DailyPlanPhoneController {
    private static Logger Log = (Logger) LoggerFactory.getLogger(DailyPlanPhoneController.class);
    @Autowired
    private DailyPlanPhoneService dailyPlanPhoneService;


    @ApiOperation(value = "查询企业")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getGrpForTheperson", method = RequestMethod.GET)
    public String getGrpForTheperson(@RequestHeader String Authorization) {
        Log.info("查询企业接口入参：" + Authorization);
        String responseInfo = dailyPlanPhoneService.getGrpForTheperson(Authorization);
        Log.info("查询企业返回前台报文：" + responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "查看企业福利 ")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "grpNo", value = "企业客户号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/showEnsureForPeople", method = RequestMethod.GET)
    public String showEnsureForPeople(@RequestHeader String Authorization, String grpNo) {
        Log.info("查看企业福利接口入参：" + Authorization + "====GrpNo: " + grpNo);
        String responseInfo = dailyPlanPhoneService.showEnsureForPeople(Authorization, grpNo);
        Log.info("查看企业福利返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "回显产品详情页面")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "计划编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "grpNo", value = "企业客户号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/showProductDetails", method = RequestMethod.GET)
    public String showProductDetails(@RequestHeader String Authorization, String ensureCode, String grpNo) {
        Log.info("回显产品详情页面-接口入参： EnsureCode:{}  ", ensureCode);
        String responseInfo = dailyPlanPhoneService.showProductDetails(Authorization, ensureCode, grpNo);
        Log.info("回显产品详情页面-返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "回显快速报价中的可投保计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planCode", value = "计划编码", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/showQuickQuote", method = RequestMethod.GET)
    public String showQuickQuote(@RequestHeader String Authorization, String ensureCode, String planCode) {
        Log.info("回显快速报价接口入参：  PlaneCode:{}  ensureCode:{}", planCode, ensureCode);
        String responseInfo = dailyPlanPhoneService.showQuickQuote(Authorization, planCode, ensureCode);
        Log.info("回显快速报价返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "快速报价-选择生日带出信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "birthDay", value = "出生日期", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/autoGetPlanInfoByOne", method = RequestMethod.GET)
    public String autoGetPlanInfoByOne(@RequestHeader String Authorization, String birthDay) {
        Log.info("快速报价-选择生日带出信息--接口入参：birthDay:{}  ", birthDay);
        String responseInfo = dailyPlanPhoneService.autoGetPlanInfoByOne(Authorization, birthDay);
        Log.info("快速报价-选择生日带出信息--返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "快速报价-选择交费频次带出信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "birthDay", value = "出生日期", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "payFrequency", value = "交费频次", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/autoGetPlanInfoByTwo", method = RequestMethod.GET)
    public String autoGetPlanInfoByTwo(@RequestHeader String Authorization, String birthDay, String payFrequency) {
        Log.info("快速报价-选择交费频次带出信息--接口入参： birthDay:{} payFrequency:{} ", birthDay, payFrequency);
        String responseInfo = dailyPlanPhoneService.autoGetPlanInfoByTwo(Authorization, birthDay, payFrequency);
        Log.info("快速报价-选择交费频次带出信息--返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "员工投保-快速报价 点击【我要投保】")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/insured", method = RequestMethod.POST)
    public String insured(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        Log.info("点击【我要投保】入参params:{}", params);
        String responseInfo = dailyPlanPhoneService.insured(Authorization, params);
        Log.info("点击【我要投保】结果：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "获取支付中心支付页面接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderItemNo", value = "子订单号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "支付类型，01-实时+批扣（支付方式为个人支付且预核保通过），02-批扣（支付方式为个人支付且预核保不通过）", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/goToPayment", method = RequestMethod.POST)
    public String goToPayment(@RequestHeader String Authorization, String orderItemNo, String type) {
        Log.info("获取支付中心支付页面接口入参params:orderItemNo={},type={}", orderItemNo, type);
        String responseInfo = dailyPlanPhoneService.goToPayment(Authorization, orderItemNo, type);
        Log.info("获取支付中心支付页面接口结果：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "支付状态查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderItemNo", value = "子订单号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/payStatusQuery", method = RequestMethod.POST)
    public String payStatusQuery(@RequestHeader String Authorization, String orderItemNo) {
        Log.info("支付状态查询接口入参params:{}", orderItemNo);
        String responseInfo = dailyPlanPhoneService.payStatusQuery(Authorization, orderItemNo);
        Log.info("支付状态查询接口结果：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "获取人脸识别页面接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personId", value = "投/被保人personId", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderItemNo", value = "子订单号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "faceFlag", value = "区别是员工界面还是分享界面标识", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getFaceUrl", method = RequestMethod.POST)
    public String getFaceUrl(@RequestHeader String Authorization, String orderItemNo, String personId, String faceFlag) {
        Log.info("获取人脸识别页面接口入参params:{}<>{}", orderItemNo, personId, faceFlag);
        String responseInfo = dailyPlanPhoneService.getFaceUrl(Authorization, orderItemNo, personId, faceFlag);
        Log.info("获取人脸识别页面接口结果：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "人脸识别结果查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personId", value = "投/被保人personId", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderItemNo", value = "子订单号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/faceStatusQuery", method = RequestMethod.POST)
    public String faceStatusQuery(@RequestHeader String Authorization, String orderItemNo, String personId) {
        Log.info("人脸识别结果查询接口入参params:{}<>{}", orderItemNo, personId);
        String responseInfo = dailyPlanPhoneService.faceStatusQuery(Authorization, orderItemNo, personId);
        Log.info("人脸识别结果查询接口结果：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "个人端健康告知-保存接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "appntImpartInfoPersonal", method = RequestMethod.POST)
    public String appntImpartInfoPersonal(@RequestHeader String Authorization, @RequestParam String orderItemNo, @RequestParam String yearSalary, @RequestParam String relation, @RequestBody Map<String, Map<String, String>> map) {
        Log.info("个人端健康告知保存接口,入参：" + Authorization + "=orderItemNo:" + orderItemNo + "=map:" + map+ "=yearSalary:" + yearSalary + "=relation:" + relation);
        String responseInfo = dailyPlanPhoneService.appntImpartInfoPersonal(Authorization, orderItemNo, map, yearSalary, relation);
        Log.info("个人端健康告知保存接口,返回报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "我的查询--订单查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "commitDate1", value = "订单提交日期1", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "commitDate2", value = "订单提交日期2", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/perOrderQuery", method = RequestMethod.GET)
    public String perOrderQuery(@RequestHeader String Authorization, String commitDate1, String commitDate2) {
        Log.info("我的查询--订单查询请求报文：token: {}，订单开始日期: {}-订单结束日期: {}" ,Authorization,commitDate1,commitDate2);
        String responseInfo = dailyPlanPhoneService.queryOrderByTime(Authorization, commitDate1, commitDate2);
        Log.info("我的查询--订单查询返回报文：" + responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "订单查询-修改信息回显")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderNo", value = "订单号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderItemNo", value = "子订单号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "grpNo", value = "企业客户号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sumPrem", value = "总保费", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "grpName", value = "企业名称", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/updateInfo", method = RequestMethod.GET)
    public String updateInfo(@RequestHeader String Authorization, String orderNo, String orderItemNo, String ensureCode, String grpNo, String sumPrem, String grpName) {
        Log.info("修改信息入参：orderNo:" + orderNo + "--orderitemNo：" + orderItemNo + "--ensureCode:" + ensureCode+ "--grpNo:" + grpNo+ "--sumPrem:" + sumPrem+ "--grpName:" + grpName);
        String responseInfo = dailyPlanPhoneService.updateInfo(Authorization, orderNo, orderItemNo, ensureCode, grpNo, sumPrem, grpName);
        Log.info("修改信息返回前台报文：" + responseInfo);
        return responseInfo;
    }
@ApiOperation(value = "订单查询-修改信息保存")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderItemNo", value = "子订单号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "payPersonId", value = "付款人personid", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/saveInfo", method = RequestMethod.GET)
    public String saveInfo(@RequestHeader String Authorization, String orderItemNo, String payPersonId, String ensureCode) {
        Log.info("保存订单信息入参：orderItemNo:" + orderItemNo + "--payPersonId:" + payPersonId+ "--ensureCode:" + ensureCode);
        String responseInfo = dailyPlanPhoneService.saveInfo(Authorization, orderItemNo, payPersonId, ensureCode);
        Log.info("保存订单信息返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "订单查询-删除订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderNo", value = "订单号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderitemNo", value = "子订单号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/deleteorder", method = RequestMethod.GET)
    public String deleteorder(@RequestHeader String Authorization, String orderNo, String orderitemNo) {
        Log.info("删除订单接口入参：orderNo:" + orderNo + "--orderitemNo：" + orderitemNo );
        String responseInfo = dailyPlanPhoneService.deleteorder(Authorization, orderNo, orderitemNo);
        Log.info("删除订单接口返回前台报文：" + responseInfo);
        return responseInfo;
    }

   @ApiOperation(value = "订单查询-去投保状态校验")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderNo", value = "订单号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/checkInsured", method = RequestMethod.GET)
    public String checkInsured(@RequestHeader String Authorization, String orderNo, String ensureCode) {
       Log.info("去投保状态校验校验入参：orderNo: {} , ensureCode:{}", orderNo, ensureCode);
       String responseInfo = dailyPlanPhoneService.checkInsured(Authorization, orderNo, ensureCode);
        Log.info("去投保状态校验：返回前台报文：" + responseInfo);
        return responseInfo;
    }
   @ApiOperation(value = "订单查询-申请发票")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderNo", value = "订单号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderitemNo", value = "子订单号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/RequestInvoice", method = RequestMethod.GET)
    public String RequestInvoice(@RequestHeader String Authorization, String orderNo, String orderitemNo) {
        Log.info("申请发票入参：orderNo:" + orderNo + "--orderitemNo："+ orderitemNo );
       String responseInfo = dailyPlanPhoneService.RequestInvoice(Authorization, orderNo, orderitemNo);
        Log.info("申请发票：返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "个人端投保人告知查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderItemNo", value = "子订单号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/queryAppntImpartInfophone", method = RequestMethod.GET)
    public String queryAppntImpartInfophone(@RequestHeader String Authorization, String orderItemNo){
        Log.info("投保人告知查询接口，入参:orderItemNo = "+orderItemNo );
        String responseInfo = dailyPlanPhoneService.queryAppntImpartInfophone(Authorization,orderItemNo);
        Log.info("投保人告知查询接口,返回报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "个人端核保结论查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderItemNo", value = "子订单号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getReview", method = RequestMethod.GET)
    public String getReview(@RequestHeader String Authorization, String orderItemNo){
        Log.info("核保结论查询接口，入参:orderItemNo = "+orderItemNo );
        String responseInfo = dailyPlanPhoneService.getReview(Authorization,orderItemNo);
        Log.info("核保结论查询接口,返回报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "确认签名报错后保存保费")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderItemNo", value = "子订单号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "prem", value = "保费", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/savePrem", method = RequestMethod.GET)
    public String savePrem(@RequestHeader String Authorization, String orderItemNo, String prem, String ensureCode ){
        Log.info("确认签名报错后保存保费，入参:orderItemNo:{} ,prem:{} ,ensureCode:{} ", orderItemNo, prem, ensureCode);
        String responseInfo = dailyPlanPhoneService.savePrem(Authorization, orderItemNo, prem, ensureCode);
        Log.info("确认签名报错后保存保费,返回报文：" + responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "去支付改状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderItemNo", value = "子订单号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/changePrem", method = RequestMethod.GET)
    public String changePrem(@RequestHeader String Authorization, String orderItemNo, String ensureCode ){
        Log.info("去支付改状态，入参:orderItemNo:{} ,prem:{} ,ensureCode:{} ", orderItemNo, ensureCode);
        String responseInfo = dailyPlanPhoneService.changePrem(Authorization, orderItemNo, ensureCode);
        Log.info("去支付改状态,返回报文：" + responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "分享 校验")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderItemNo", value = "子订单号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "perNo", value = "员工号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "prem", value = "保费", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/checkShare", method = RequestMethod.GET)
    public String checkShare(@RequestHeader String Authorization, String orderItemNo, String perNo, String prem ){
        Log.info("分享校验，入参:orderItemNo:{} ,prem:{} ,perNo:{} ", orderItemNo, prem, perNo);
        String responseInfo = dailyPlanPhoneService.checkShare( Authorization,  orderItemNo,  perNo,  prem);
        Log.info("分享校验,返回报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "解锁按钮")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderItemNo", value = "子订单号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "shareDetalNo", value = "分享流水号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/unlock", method = RequestMethod.GET)
    public String unlock(@RequestHeader String Authorization, String orderItemNo, String shareDetalNo ){
        Log.info("解锁按钮，入参:orderItemNo:{} ,shareDetalNo:{} ", orderItemNo, shareDetalNo);
        String responseInfo = dailyPlanPhoneService.unlock( Authorization,  orderItemNo,  shareDetalNo);
        Log.info("解锁按钮,返回报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "锁定订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderItemNo", value = "子订单号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/tolock", method = RequestMethod.GET)
    public String tolock(@RequestHeader String Authorization, String orderItemNo ){
        Log.info("锁定订单，入参:orderItemNo:{}  ", orderItemNo);
        String responseInfo = dailyPlanPhoneService.tolock( Authorization,  orderItemNo);
        Log.info("锁定订单,返回报文：" + responseInfo);
        return responseInfo;
    }


  @ApiOperation(value = "分享提交按钮")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderItemNo", value = "子订单号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "perNo", value = "员工号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "shareDetalNo", value = "分享流水号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/submit", method = RequestMethod.GET)
    public String submit(@RequestHeader String Authorization, String orderItemNo , String perNo , String shareDetalNo ){
      Log.info("分享提交，入参:orderItemNo:{}  ，perNo:{}  ,shareDetalNo:{} ", orderItemNo, perNo, shareDetalNo);
      String responseInfo = dailyPlanPhoneService.submit(Authorization, orderItemNo, perNo, shareDetalNo);
        Log.info("分享提交,返回报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "分享锁定查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderItemNo", value = "子订单号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "shareDetalNo", value = "分享流水号", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/checkLock", method = RequestMethod.GET)
    public String checkLock(@RequestHeader String Authorization, String orderItemNo, String shareDetalNo) {
        Log.info("分享锁定查询，入参:orderItemNo:{} ,shareDetalNo:{}   ", orderItemNo, shareDetalNo);
        String responseInfo = dailyPlanPhoneService.checkLock(Authorization, orderItemNo, shareDetalNo);
        Log.info("分享锁定查询,返回报文：" + responseInfo);
        return responseInfo;
    }


  @ApiOperation(value = "根据token 重新生成新的token")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "UserNo", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "grpOrderNo", value = "团体订单号", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/makeToken", method = RequestMethod.GET)
    public String makeToken( String Authorization, String grpOrderNo){
      Log.info("重新生成新的token入参:Authorization:{}  ,grpOrderNo:{}  ", Authorization, grpOrderNo);
      String responseInfo = dailyPlanPhoneService.makeToken(Authorization, grpOrderNo);
        Log.info("重新生成新的token,返回报文：" + responseInfo);
        return responseInfo;
    }



}

