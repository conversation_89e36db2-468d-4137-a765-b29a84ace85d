package com.sinosoft.eflex.ctrl;

import com.sinosoft.eflex.model.FcGrpContact;
import com.sinosoft.eflex.service.ContactService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * @DESCRIPTION
 * @create 2018-08-10 10:35
 **/
@RestController
@RequestMapping("Contact")
@Api(value = "ContactController", description = "第二联系人维护")
public class ContactController {

    private static Logger Log = (Logger) LoggerFactory.getLogger(EnsureMakeController.class);

    @Autowired
    private ContactService contactService;


    /**
     * 新增联系人
     *
     * @param fcGrpContact
     * @return
     */
    @ApiOperation(value = "新增/修改 企业第二联系人联系人")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "isChangeState", value = "联系人冲突操作标识", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "fcGrpContact", value = "FcGrpContact", required = true, dataType = "FcGrpContact", paramType = "body")
    })
    @RequestMapping(value = "/maintainContact", method = RequestMethod.PUT)
    public String maintainContact(@RequestHeader String Authorization, @RequestBody FcGrpContact fcGrpContact,String isChangeState) {
        String responseInfo = contactService.maintainContact(Authorization, fcGrpContact,isChangeState);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }


}
