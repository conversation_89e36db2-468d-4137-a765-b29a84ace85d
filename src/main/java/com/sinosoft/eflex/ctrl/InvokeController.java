package com.sinosoft.eflex.ctrl;

import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.model.OrgAnization.OrgResponse;
import com.sinosoft.eflex.service.InvokeService;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2023/3/28 11:31
 */
@RestController
@RequestMapping("/invoke")
@Api(value = "InvokeController")
public class InvokeController {
    private static Logger Log = (Logger) LoggerFactory.getLogger(InvokeController.class);
    @Autowired
    private InvokeService invokeService;

    @ApiOperation(value = "获取org中介机构")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header") })
    @GetMapping("/orgCode")
    public OrgResponse queryOrgCode(@RequestHeader String Authorization,@ApiParam("channel渠道类型") @RequestParam(value = "channel", required = false) String channel) {
        OrgResponse orgResponse = invokeService.queryOrgCode(channel);
        Log.info("返回前台报文: {}" , JSON.toJSONString(orgResponse));
        return orgResponse;
    }
}
