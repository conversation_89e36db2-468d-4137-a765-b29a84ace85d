package com.sinosoft.eflex.ctrl.claim;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.model.FdUser;
import com.sinosoft.eflex.service.claim.TestService;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * @DESCRIPTION
 * @create 2018-11-05 17:51
 **/
@RestController("TestController")
@RequestMapping("claim/TestController")
public class TestController {

    @Autowired
    private TestService testService;

    @ApiOperation(value = "查询密码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "userName", value = "用户名", required = true, dataType = "String", paramType = "query") })
    @RequestMapping(value = "/selectPassWord", method = RequestMethod.GET)
    public String makeProposalForm(@RequestHeader String Authorization, String userName) {
        List<FdUser> fdUserList = testService.selectPassWord(Authorization, userName);
        return JSONObject.toJSONString(fdUserList);
    }

}
