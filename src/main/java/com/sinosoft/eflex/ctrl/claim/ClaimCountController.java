package com.sinosoft.eflex.ctrl.claim;

import com.sinosoft.eflex.model.ClaimInfo;
import com.sinosoft.eflex.model.GrpClaimCount;
import com.sinosoft.eflex.model.GrpContInfo;
import com.sinosoft.eflex.service.claim.ClaimCountService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2018-11-06 15:49
 */
@RestController
@RequestMapping("ClaimCount")
@Api(value = "ClaimCountController", description = "第三方核心对接接口")
public class ClaimCountController {

    private static Logger Log = (Logger) LoggerFactory.getLogger(ClaimCountController.class);
    @Autowired
    private ClaimCountService claimCountService;

    @ApiOperation(value = "企业整体理赔统计接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "grpContNo", value = "保单号", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/getGrpClaimCount", method = RequestMethod.GET)
    public String getGrpClaimCount(@RequestHeader String Authorization, @RequestParam String grpContNo){
        Log.info("接收请求参数："+grpContNo);
        String responseInfo=claimCountService.getGrpClaimCount(grpContNo);
        Log.info("返回参数："+responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "个人理赔统计接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getClaimCount", method = RequestMethod.POST)
    public String getClaimCount(@RequestHeader String Authorization,@RequestBody Map<String,String> paramMap){
        String responseInfo=claimCountService.getClaimCount(paramMap);
        Log.info("返回参数："+responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "个人理赔记录清单导出")
    @RequestMapping(value = "/exportClaimCount", method = RequestMethod.GET)
    public String exportClaimCount(@RequestParam  String grpContNo,@RequestParam String insuredName,@RequestParam String certiType,@RequestParam String certiCode, HttpServletResponse response){
        Map<String,Object> map=new HashMap<>();
        map.put("grpContNo",grpContNo);
        map.put("insuredName",insuredName);
        map.put("certiType",certiType);
        map.put("certiCode",certiCode);
        Log.info("前台传参："+grpContNo+"========"+insuredName+"======="+certiType+"======="+certiCode);
        String responseInfo=claimCountService.exportClaimCount(map,response);
    	Log.info("返回参数："+responseInfo);
    	return responseInfo;
    }



    @ApiOperation(value = "企业理赔记录清单导出")
    @RequestMapping(value = "/exportGrpClaimCount", method = RequestMethod.GET)
    public String exportGrpClaimCount(@RequestParam  String grpContNo,@RequestParam String sex,@RequestParam String ageStart,@RequestParam String ageEnd,
            @RequestParam String insuredType,@RequestParam  String claimDateS,@RequestParam String claimDateE,@RequestParam String riskCode,
            HttpServletResponse response){
        Map<String,Object> map=new HashMap<>();
        map.put("grpContNo",grpContNo);
        map.put("sex",sex);
        map.put("ageStart",ageStart);
        map.put("ageEnd",ageEnd);
        map.put("insuredType",insuredType);
        map.put("claimDateS",claimDateS);
        map.put("claimDateE",claimDateE);
        map.put("riskCode",riskCode);
        String responseInfo=claimCountService.exportGrpClaimCount(map,response);
        Log.info("返回参数："+responseInfo);
        return responseInfo;
    }
}
