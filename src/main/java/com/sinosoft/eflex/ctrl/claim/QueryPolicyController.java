package com.sinosoft.eflex.ctrl.claim;

import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.framework.annotation.SqlInject;
import com.sinosoft.eflex.service.claim.QueryPolicyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * @DESCRIPTION
 * @create 2018-11-06 10:55
 **/
@RestController
@RequestMapping("QueryPolicy")
@Api(value = "QueryPolicyController", description = "保单查询")
public class QueryPolicyController {

    private static Logger Log = LoggerFactory.getLogger(QueryPolicyController.class);

    @Autowired
    private QueryPolicyService queryPolicyService;


    @ApiOperation(value = "查询企业下所有保单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收的字符集", required = false, dataType = "String", paramType = "header", defaultValue = "UTF-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8"),
            @ApiImplicitParam(name = "pageNum", value = "当前页码", required = false, paramType = "query", dataType = "int", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, paramType = "query", dataType = "int", defaultValue = "10"),
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/queryPolicyListByGrpIdNo", method = RequestMethod.POST)
    @SqlInject(KeywordPrevent = true)
    public String queryPolicyListByGrpIdNo(@RequestHeader String Authorization, @RequestBody Map<String, String> params, Integer pageNum, Integer pageSize) {
        String responseInfo = queryPolicyService.queryPolicyList(Authorization, params, pageNum, pageSize);
        return responseInfo;
    }

    @ApiOperation(value = "查询保单的理赔记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收的字符集", required = false, dataType = "String", paramType = "header", defaultValue = "UTF-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8"),
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "params", value = "数据值", required = false, dataType = "String", paramType = "body")})
    @RequestMapping(value = "/queryPolicyClaimList", method = RequestMethod.POST)
    @SqlInject(KeywordPrevent = true)
    public String queryPolicyClaimList(@RequestHeader String Authorization, @RequestBody HashMap<String, Object> params) {
        Log.info("查询保单的理赔记录前台传参:" + JSON.toJSONString(params));
        String responseInfo = queryPolicyService.queryPolicyClaimList(params);
        Log.info("返回报文:" + responseInfo);
        return responseInfo;
    }
}
