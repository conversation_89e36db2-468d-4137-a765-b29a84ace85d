package com.sinosoft.eflex.ctrl.insurePlan;

import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.framework.annotation.SqlInject;
import com.sinosoft.eflex.model.insurePlan.InsurePlanOrderPersonrel;
import com.sinosoft.eflex.model.insurePlan.InsurePlanVo;
import com.sinosoft.eflex.service.InsurePlan.GroupInsurePlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("InsurePlan")
@Api(value = "InsurePlanController", description = "团险赠险计划书")
public class InsurePlanController {

    private static Logger Log = LoggerFactory.getLogger(InsurePlanController.class);

    @Autowired
    private GroupInsurePlanService groupInsurePlanService;

    @ApiOperation(value = "新增计划——团险赠险计划书保存")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    @RequestMapping(value = "/insertInsurePlan", method = RequestMethod.POST)
    public String insertInsurePlan(@RequestHeader String Authorization,
                                   @RequestBody InsurePlanVo insurePlanVo) {
        Log.info("团险赠险计划书保存：Authorization:{}，InsurePlanVo:{}", Authorization, JSON.toJSONString(insurePlanVo));

        String responseInfo = groupInsurePlanService.saveInsurePlan(Authorization, insurePlanVo);

        Log.info("新增团险计划书保存返回参数:{}", responseInfo);
        return responseInfo;
    }


    /**
     * 计划书列查询
     *
     * @param Authorization
     * @return
     */
    @ApiOperation(value = "计划书列表查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getInsurePlanListPage", method = RequestMethod.POST)
    @SqlInject(KeywordPrevent = true)
    public String getInsurePlanListPage(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        int page = Integer.parseInt(params.get("page"));
        int rows = Integer.parseInt(params.get("rows"));
        Log.info("计划书列表查询入参 分页参数:{},{}", page, rows);
        //查询计划书列表
        String responseInfo = groupInsurePlanService.selectInsurePlanByPage(Authorization, params, page, rows);
        //打印响应报文
        Log.info("计划书列表返回前台报文:" + responseInfo);
        return responseInfo;
    }


    /**
     * 计划书信息详情
     *
     * @param Authorization
     * @return
     */
    @ApiOperation(value = "计划书信息详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getInsurePlanInfo", method = RequestMethod.POST)
    public String getInsurePlanInfo(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        Long Id = Long.parseLong(params.get("Id"));
        Log.info("计划书信息详情接口入参：" + Authorization + "====" + Id);
        String responseInfo = groupInsurePlanService.getInsurePlanInfo(Authorization, Id);
        Log.info("计划书信息详情返回报文：" + responseInfo);
        return responseInfo;
    }


    /**
     * 计划书修改接口
     *
     * @param Authorization
     * @param insurePlanVo
     * @return
     */
    @ApiOperation(value = "修改计划——团险赠险计划书修改")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    @RequestMapping(value = "/updateInsurePlan", method = RequestMethod.POST)
    public String updateInsurePlan(@RequestHeader String Authorization,
                                   @RequestBody InsurePlanVo insurePlanVo) {
        Log.info("团险赠险计划书修改：Authorization:{}，InsurePlanVo:{}", Authorization, JSON.toJSONString(insurePlanVo));

        String responseInfo = groupInsurePlanService.updateInsurePlan(Authorization, insurePlanVo);

        Log.info("新增团险计划书修改返回参数:{}", responseInfo);
        return responseInfo;
    }


    /**
     * 删除计划书信息及险种配置信息
     *
     * @param Authorization
     * @param Id
     * @return
     */
    @ApiOperation(value = "删除计划书信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "Id", value = "计划书基础信息主键", dataType = "Long", paramType = "query")
    })
    @RequestMapping(value = "/deleteInsurePlan", method = RequestMethod.DELETE)
    public String deleteInsurePlan(@RequestHeader String Authorization, Long Id) {
        Log.info("删除计划书信息接口入参：" + Authorization + "====" + Id);
        String responseInfo = groupInsurePlanService.deleteInsurePlanInfo(Authorization, Id);
        Log.info("删除计划书信息返回报文：" + responseInfo);
        return responseInfo;
    }


    /**
     * 计划书上下架接口
     *
     * @param Authorization
     * @return
     */
    @ApiOperation(value = "上下架计划书")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/upInsurePlan", method = RequestMethod.POST)
    public String upInsurePlan(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        Long Id = Long.parseLong(params.get("Id"));
        int status = Integer.parseInt(params.get("status"));
        Log.info("上下架计划书信息接口入参：" + Authorization + "====" + Id);
        String responseInfo = groupInsurePlanService.upInsurePlanInfo(Authorization, Id, status);
        Log.info("上下架计划书信息返回报文：" + responseInfo);
        return responseInfo;
    }

    /**
     * 员工个人信息保存接口
     */
    @ApiOperation(value = "员工个人信息保存接口")
    @RequestMapping(value = "/saveStaffInfo", method = RequestMethod.POST)
    public String saveStaffInfo(@RequestBody InsurePlanOrderPersonrel insurePlanOrderPersonrel) {
        Log.info("个人信息保存:InsurePlanOrderPersonrel:{}", JSON.toJSONString(insurePlanOrderPersonrel));
        String responseInfo = groupInsurePlanService.savePersonrelInfo(insurePlanOrderPersonrel);
        Log.info("个人信息保存:{}", responseInfo);
        return responseInfo;
    }


    /**
     * 员工个人信息查询接口
     */
    @ApiOperation(value = "员工个人信息查询接口")
    @RequestMapping(value = "/getStaffInfo", method = RequestMethod.POST)
    public String getStaffInfo(@RequestBody InsurePlanOrderPersonrel insurePlanOrderPersonrel) {
        Log.info("个人信息，InsurePlanOrderPersonrel:{}", JSON.toJSONString(insurePlanOrderPersonrel));
        String responseInfo = groupInsurePlanService.getStaffInfo(insurePlanOrderPersonrel);
        Log.info("个人信息:{}", responseInfo);
        return responseInfo;
    }


    /**
     * 获取险种列表
     */
    @ApiOperation(value = "获取险种列表接口")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    @RequestMapping(value = "/getRiskInfo", method = RequestMethod.POST)
    public String getRiskInfo(@RequestHeader String Authorization) {
        Log.info("获取险种列表接口：Authorization:{}", Authorization);
        String responseInfo = groupInsurePlanService.getRiskInfo(Authorization);
        Log.info("获取险种列表接口返回:{}", responseInfo);
        return responseInfo;
    }

    /**
     * @param Authorization
     * @param params
     * @return
     */
    @ApiOperation(value = "同步订单人员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/synchronizeOrderStaff", method = RequestMethod.POST)
    public String synchronizeOrderStaff(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        Log.info("订单号查询计划书信息详情接口入参：" + Authorization + "====" + JSON.toJSONString(params));
        String responseInfo = groupInsurePlanService.synchronizeOrderStaff(Authorization, params);
        Log.info("订单号查询计划书信息详情返回报文：" + responseInfo);
        return responseInfo;
    }


    /**
     * 计划书信息详情根据订单编号查询
     *
     * @param Authorization
     * @return
     */
    @ApiOperation(value = "计划书信息详情--订单号查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/getInsurePlanInfoByOrderNo", method = RequestMethod.POST)
    public String getInsurePlanInfoByOrderNo(@RequestHeader String Authorization, @RequestBody Map<String, String> params) {
        String orderNo = params.get("orderNo");
        Log.info("订单号查询计划书信息详情接口入参：" + Authorization + "====" + orderNo);
        String responseInfo = groupInsurePlanService.getInsurePlanInfoByOrderNo(Authorization, orderNo);
        Log.info("订单号查询计划书信息详情返回报文：" + responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "福利计划 - - 导出人员清单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "grpNo", value = "企业客户号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "name", value = "员工姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sex", value = "员工性别", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "iDType", value = "证件类型", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "nativeplace", value = "国籍", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "defaultPlan", value = "证件类型", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "iDNo", value = "证件号码", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/staffExportInfoExcel", method = RequestMethod.GET)
    public String staffExportInfoExcel(String Authorization, HttpServletResponse response, String ensureCode, String grpNo, String name, String sex, String iDType, String iDNo, String nativeplace, String defaultPlan) {
        Log.info("福利计划-人员清单导出请求报文：福利编号：" + ensureCode + "===姓名：" + name + "===性别：" + sex + "===证件类型：" + iDType + "===证件号：" + iDNo + "=====GrpNo" + grpNo);
        String responseInfo = groupInsurePlanService.staffExportInfoExcel(Authorization, response, ensureCode, grpNo, name, sex, iDType, iDNo, nativeplace, defaultPlan);
        Log.info("福利计划-人员清单导出返回报文：" + responseInfo);
        return responseInfo;
    }


    /**
     * 计划书信息详情 -- 提供ihomePro
     *
     * @return
     */
    @ApiOperation(value = "计划书信息详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Id", value = "计划书基础信息主键", dataType = "Integer", paramType = "query")
    })
    @RequestMapping(value = "/getInsurePlan", method = RequestMethod.POST)
    public String getInsurePlan(@RequestBody Map<String, String> params) {
        Long id = Long.parseLong(params.get("id"));
        Log.info("计划书信息详情接口入参：" + "====" + id);
        String responseInfo = groupInsurePlanService.getInsurePlanInfo(null, id);
        Log.info("计划书信息详情返回报文：" + responseInfo);
        return responseInfo;
    }

    /**
     * 计划书信息列表 --提供ihomePro
     *
     * @return
     */
    @ApiOperation(value = "计划书列表查询")
    @RequestMapping(value = "/getInsurePlanList", method = RequestMethod.POST)
    public String getInsurePlanList(@RequestBody Map<String, Object> params) {
        List<String> planIds = (List<String>) params.get("planIds");
        //查询计划书列表
        String responseInfo = groupInsurePlanService.selectInsurePlanList(planIds);
        //打印响应报文
        Log.info("计划书列表返回前台报文：" + responseInfo);
        return responseInfo;
    }


    /**
     * 创建计划书订单信息 --提供ihomePro
     *
     * @return
     */
    @ApiOperation(value = "创建计划书订单信息")
    @RequestMapping(value = "/saveInsurePlanOrder", method = RequestMethod.POST)
    public String saveInsurePlanOrder(@RequestBody Map<String, String> params) {
        Log.info("创建计划书订单信息入参:{}", JSON.toJSONString(params));
        //查询计划书列表
        String responseInfo = groupInsurePlanService.saveInsurePlanOrder(params);
        //打印响应报文
        Log.info("创建计划书订单信息返回前台报文：" + responseInfo);
        return responseInfo;
    }

}
