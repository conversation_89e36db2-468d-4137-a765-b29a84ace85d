/*
package com.sinosoft.eflex.ctrl;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

import com.sinosoft.eflex.service.ExternalService;
import com.sinosoft.eflex.service.TaskService;
import com.sinosoft.eflex.util.CheckUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.sinosoft.eflex.service.TestService;
import com.sinosoft.eflex.util.LogPrintUntil;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

*/
/**
 * @DESCRIPTION
 * @create 2018-10-14 11:48
 **//*

@RestController
@RequestMapping("TestController")
public class TestController {

    private static final Logger log = LoggerFactory.getLogger(TestController.class);

    @Autowired
    private TestService testService;
    @Autowired
    private ExternalService externalService;
    @Autowired
    private TaskService taskService;
    Map<String, String> params = new HashMap<String, String>();

    @ApiOperation(value = "谁都测试......")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "personId", value = "id", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planCode", value = "id", required = true, dataType = "String", paramType = "query"),
    })
    @RequestMapping(value = "testEreryOne", method = RequestMethod.GET)
    public String testEreryOne(@RequestHeader String Authorization , String personId, String planCode) {

        //taskService.test2();
        // 根据客户号删除用户
        //registService.deleteResiter("00000000000000000013");

        //String responseInfo = calTool.calBL("123","123");

        params.put("personId", personId);
        params.put("planCode", planCode);
        LogPrintUntil.linkStrings(params);
        //String responseInfo = testService.testRule(personId,planCode);
        String responseInfo = testService.testRockBack(personId,planCode);
        log.info("************** " + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "谁都测试2222......")
    @RequestMapping(value = "test22", method = RequestMethod.GET)
    public void test22() {

//         taskService.insureOnePolicy("00000000000000000308");
        */
/*log.info("************** " + responseInfo);
        return responseInfo;*//*

    }

    @ApiOperation(value = "企业员工账号加密解密......")
    @ApiImplicitParam(name = "grpNo", value = "id", required = true, dataType = "String", paramType = "query")
    @RequestMapping(value = "encryption", method = RequestMethod.GET)
    public String jiami(String grpNo) {
        //String responseInfo = testService.testRule(personId,planCode);
        String responseInfo = testService.encryption(grpNo);
        log.info("************** " + responseInfo);
        return responseInfo;
    }


    */
/**
     * 实现文件下载
     * @throws InvalidFormatException
     *//*

    @ApiOperation(value = "文件下载")
    @RequestMapping(value = "/downloadFile", method = RequestMethod.GET)
    public void downloadFile(HttpServletResponse response)  {
        System.out.println("=============");
        externalService.down(response);

    }

    @ApiOperation(value = "文件下载222")
    @RequestMapping(value = "/downloadFile222", method = RequestMethod.GET)
    public ResponseEntity<InputStreamResource> downloadFile222()  {
        System.out.println("=============");
        //ResponseEntity<InputStreamResource> r = externalService.downloadFile22();
        return null;
    }

    @ApiOperation(value = "调用核心接口")
    @RequestMapping(value = "/calInterface", method = RequestMethod.GET)
    public String calInterface()  {
        System.out.println("=============");
        String responseStr = testService.callInterFace();
        return responseStr;
    }

    @ApiOperation(value = "调用核心接口222")
    //@ApiImplicitParam(name = "请求报文", value = "reqXml", required = true, dataType = "String", paramType = "query")
    @RequestMapping(value = "/testCalInterface", method = RequestMethod.POST)
    public String testCalInterface(@RequestBody String reqXml)  {
        String responseStr = testService.testCallInterFace(reqXml);
        return responseStr;
    }

    @ApiOperation(value = "调用打印接口!!!!")
    //@ApiImplicitParam(name = "请求报文", value = "reqXml", required = true, dataType = "String", paramType = "query")
    @GetMapping(value = "/testCalInterface")
    public String testInterface()  {
        String responseStr = testService.testInterFace();
        return responseStr;
    }

    @ApiOperation(value = "wenjian!!!!")
    //@ApiImplicitParam(name = "请求报文", value = "reqXml", required = true, dataType = "String", paramType = "query")
    @GetMapping(value = "/testCalInt")
    public MultipartFile testInt()  {
        MultipartFile responseStr = testService.testIn();
        return responseStr;
    }
    @ApiOperation(value = "wenjian!!!!")
    //@ApiImplicitParam(name = "请求报文", value = "reqXml", required = true, dataType = "String", paramType = "query")
    @GetMapping(value = "/test111")
    public String testInt1111()  {
        String responseStr = testService.test();
        return responseStr;
    }

    @ApiOperation(value = "testqianmingtupian")
    //@ApiImplicitParam(name = "请求报文", value = "reqXml", required = true, dataType = "String", paramType = "query")
    @GetMapping(value = "/testqianmingtupian")
    public String testqianmingtupian()  {
        testService.asd();
        return "11";
    }

}
*/
