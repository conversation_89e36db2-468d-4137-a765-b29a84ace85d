package com.sinosoft.eflex.ctrl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hqins.common.base.ApiResult;
import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.framework.annotation.SqlInject;
import com.sinosoft.eflex.model.FCEnsure;
import com.sinosoft.eflex.model.FCPerInfoTemp;
import com.sinosoft.eflex.model.FCPerinfoFamilyTemp;
import com.sinosoft.eflex.model.MaintainEnsureInfoReq;
import com.sinosoft.eflex.model.vo.InsuranceTermsVO;
import com.sinosoft.eflex.service.EnsureMakeService;
import com.sinosoft.eflex.service.admin.EnsureCustomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 保障计划定制*
 *
 * @DESCRIPTION
 * @create 2018-08-03 9:30
 **/
@RestController
@RequestMapping("EnsureMake")
@Api(value = "EnsureMakeController")
@RequiredArgsConstructor
@Slf4j
public class EnsureMakeController {

    private final EnsureMakeService ensureMakeService;
    private final EnsureCustomService ensureCustomService;

    /**
     * Eflex001
     * 企业保障列表查询
     *
     * @param ensureName  保障名称
     * @param ensureCode  保障编号
     * @param ensureState 保障状态
     * @return
     */
    @ApiOperation(value = "企业保障列表查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureName", value = "保障名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "保障编码", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureState", value = "保障状态", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "当前查询页", required = true, dataType = "int", paramType = "query", defaultValue = "1"),
            @ApiImplicitParam(name = "rows", value = "每页记录数", required = true, dataType = "int", paramType = "query", defaultValue = "10")
    })
    @RequestMapping(value = "/getGrpEnsureListPage", method = RequestMethod.GET)
    @SqlInject(KeywordPrevent = true)
    public String getGrpEnsureListPage(@RequestHeader String Authorization, String ensureName, String ensureCode, String ensureState, int page, int rows) {
        //打印入参
        Map<String, String> map = new HashMap<String, String>();
        map.put("ensureName", ensureName);
        map.put("ensureCode", ensureCode);
        map.put("ensureState", ensureState);
        map.put("page", page + "");
        map.put("rows", rows + "");
        log.info(JsonUtil.toJSON(map));
        //业务处理
        String responseInfo = ensureMakeService.selectEnsureListByPage(Authorization, ensureName, ensureCode, ensureState, page, rows);
        //打印响应报文
        log.info("getGrpEnsureListPage返回前台报文：" + responseInfo);
        return responseInfo;
    }


    /**
     * Eflex002
     * 企业保障信息页面初始化
     *
     * @param ensureCode
     * @return
     */
    @ApiOperation(value = "企业保障信息页面初始化")
    @ApiImplicitParams({@ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"), @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query")})
    @RequestMapping(value = "/initEnsureInfo", method = RequestMethod.GET)
    public String initEnsureInfo(@RequestHeader String Authorization, String ensureCode) {
        log.info("企业保障信息页面初始化请求报文：token-{},ensureCode-{}", Authorization, ensureCode);
        String responseInfo = ensureMakeService.initEnsureInfo(Authorization, ensureCode);
        log.info("企业保障信息页面初始化返回报文: {}", responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "新增/修改 福利配置信息")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/maintainEnsureInfo", method = RequestMethod.POST)
    public String maintainEnsureInfo(@RequestHeader String Authorization, @RequestBody MaintainEnsureInfoReq maintainEnsureInfoReq) {
        log.info("新增/修改 福利配置信息接口请求报文: {}", JSONObject.toJSONString(maintainEnsureInfoReq));
        String responseInfo = ensureMakeService.maintainEnsureInfo1(Authorization, maintainEnsureInfoReq);
        log.info("新增/修改 福利配置信息接口返回报文: {}", responseInfo);
        return responseInfo;
    }

    /**
     * @param Authorization
     * @param ensureCode
     * @return
     */
    @ApiOperation(value = "删除福利保障信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/deleteEnsureInfo", method = RequestMethod.DELETE)
    public String deleteEnsureInfo(@RequestHeader String Authorization, String ensureCode) {
        log.info("删除福利保障信息接口入参：" + Authorization + "====" + ensureCode);
        String responseInfo = ensureMakeService.deleteEnsureInfo(Authorization, ensureCode);
        log.info("删除福利保障信息返回报文：" + responseInfo);
        return responseInfo;
    }

    /**
     * Eflex
     * 获取第二联系人
     *
     * @return
     */
    @ApiOperation(value = "获取第二联系人")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/getSecondContactPerson", method = RequestMethod.GET)
    public String getSecondContactPerson(@RequestHeader String Authorization) {
        log.info("获取第二联系人接口，返入参：" + Authorization);
        String responseInfo = ensureMakeService.getSecondContactPerson(Authorization);
        return responseInfo;
    }


    /**
     * 保障计划导入
     *
     * @return
     */
    @ApiOperation(value = "保障计划导入")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "importPlanExcel", method = RequestMethod.POST)
    public String importPlanExcel(@RequestHeader String Authorization, String ensureCode, @RequestParam("fileName") MultipartFile file) {        //String filePath = "C:\\Users\\<USER>\\Desktop\\横琴人寿保险计划导入模板V1.1.xlsx";
        log.info("保障计划导入请求报文: {}", ensureCode);
        String responseInfo = ensureMakeService.readPlanExcel(Authorization, file, ensureCode);
        log.info("保障计划导入返回报文: {}", responseInfo);
        return responseInfo;
    }

    /**
     * 导入成功计划列表查询
     *
     * @param Authorization
     * @return
     */
    @ApiOperation(value = "导入计划列表查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "page", value = "当前查询页", required = true, dataType = "int", paramType = "query", defaultValue = "1"),
            @ApiImplicitParam(name = "rows", value = "每页记录数", required = true, dataType = "int", paramType = "query", defaultValue = "10"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收字符集", required = false, dataType = "String", paramType = "header", defaultValue = "UTF-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8")
    })
    @RequestMapping(value = "queryPlan", method = RequestMethod.GET)
    public String queryPlan(@RequestHeader String Authorization, int page, int rows, String ensureCode) {
        log.info("导入计划列表查询接口入参：" + Authorization + "-------------" + ensureCode);
        return ensureMakeService.queryPlan(Authorization, page, rows, ensureCode);
    }


    /**
     * 险种条款阅读列表
     */
    @ApiOperation(value = "险种条款阅读列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query"),
    })
    @GetMapping(value = "insurance-terms")
    public ApiResult<List<InsuranceTermsVO>> insuranceTerms(@RequestParam(name = "ensureCode") String ensureCode) {
        return ApiResult.ok(ensureCustomService.insuranceTermsList(ensureCode));
    }


    /**
     * 险种条款阅读列表
     */
    @ApiOperation(value = "险种条款阅读列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "riskCode", value = "险种编码", dataType = "String", paramType = "query"),
    })
    @PutMapping(value = "/protocol-reading")
    public ApiResult<Boolean> protocolReading(@RequestParam(name = "ensureCode") String ensureCode,
                                              @RequestParam(name = "riskCode") String riskCode) {
        return ApiResult.ok(ensureCustomService.protocolReading(ensureCode, riskCode));
    }


    /**
     * 人员清单导入
     *
     * @return
     */
    @ApiOperation(value = "人员清单导入")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureState", value = "复核完成后添加标记", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "status", value = "状态", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "importStaffExcel", method = RequestMethod.POST)
    public String importStaffExcel(@RequestHeader String Authorization, String status, String ensureCode, String ensureState, @RequestParam("fileName") MultipartFile file) {
        String responseInfo = ensureMakeService.readStaffExcel(Authorization, status, file, ensureCode, ensureState);
        log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    /**
     * 员工清单查询
     *
     * @return
     */
    @ApiOperation(value = "员工清单查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "defaultPlan", value = "默认计划编码", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "name", value = "姓名", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sex", value = "性别", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "iDType", value = "证件类型", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "iDNo", value = "证件号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "nativePlace", value = "国籍", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "当前查询页", required = true, dataType = "int", paramType = "query", defaultValue = "1"),
            @ApiImplicitParam(name = "rows", value = "每页记录数", required = true, dataType = "int", paramType = "query", defaultValue = "10"),
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收字符集", required = false, dataType = "String", paramType = "header", defaultValue = "UTF-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8")
    })
    @RequestMapping(value = "queryStaff", method = RequestMethod.GET)
    @SqlInject(KeywordPrevent = true)
    public String queryStaff(@RequestHeader String Authorization, String defaultPlan, String name, String sex, String iDType, String iDNo, String ensureCode, String levelCode, String nativePlace, int page, int rows) {
        log.info("员工清单查询,入参:{}", Authorization);
        String responseInfo = ensureMakeService.queryStaff(Authorization, defaultPlan, name, sex, iDType, iDNo, ensureCode, levelCode, nativePlace, page, rows);
        log.info("员工清单查询,返回报文:{}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "员工删除接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "perTempNo", value = "员工编号", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "deleteStaff", method = RequestMethod.GET)
    public String deleteStaff(@RequestHeader String Authorization, String perTempNo, String ensureCode) {
        log.info("员工删除接口,入参ensureCode：" + ensureCode + "===perNo:" + perTempNo);
        String responseInfo = ensureMakeService.deleteStaff(Authorization, perTempNo, ensureCode);
        log.info("员工删除接口,返回报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "员工编辑接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "perInfo", value = "人员", required = true, dataType = "FCPerInfo", paramType = "body")
    })
    @RequestMapping(value = "editStaff", method = RequestMethod.PUT)
    public String editStaff(@RequestHeader String Authorization, @RequestBody FCPerInfoTemp perInfo, String ensureCode) {
        log.info("员工编辑接口,入参：" + Authorization + "===" + JSON.toJSONString(perInfo));
        String responseInfo = ensureMakeService.editStaff(Authorization, perInfo, ensureCode);
        log.info("员工编辑接口,返回报文：" + responseInfo);
        return responseInfo;
    }

    /**
     * 复合完成后的员工修改
     *
     * @return
     */
    @ApiOperation(value = "复合完成后的员工修改编辑接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "perInfo", value = "人员", required = true, dataType = "FCPerInfo", paramType = "body")
    })
    @RequestMapping(value = "updateUser", method = RequestMethod.POST)
    public String updateUser(@RequestBody FCPerInfoTemp perInfo) {
        log.info("复合完成后的员工修改编辑接口,入参:{}" + JsonUtil.toJSON(perInfo));
        String responseInfo = ensureMakeService.updateUser(perInfo);
        log.info("复合完成后的员工修改编辑接口,返回报文:{}", responseInfo);
        return responseInfo;
    }

    /**
     * 复合完成后的员工删除接口
     *
     * @return
     */
    @ApiOperation(value = "复合完成后的员工删除接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "request", value = "请求体", required = true, dataType = "Map", paramType = "body")
    })
    @RequestMapping(value = "deleteUser", method = RequestMethod.POST)
    public String deleteUser(@RequestBody Map<String, String> request) {
        log.info("复合完成后的员工删除接口,入参：===" + JSON.toJSONString(request));
        String responseInfo = ensureMakeService.deleteUser(request);
        log.info("复合完成后的员工删除接口,返回报文：" + responseInfo);
        return responseInfo;
    }

    /**
     * 复合完成后的员工修改提示校验接口
     *
     * @return
     */
    @ApiOperation(value = "复合完成后的员工修改提示校验接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "request", value = "请求体", required = true, dataType = "Map", paramType = "body")
    })
    @RequestMapping(value = "checkUpdate", method = RequestMethod.POST)
    public String checkUpdate(@RequestBody Map<String, String> request) {
        log.info("复合完成后的员工修改提示校验接口,入参：===" + JSON.toJSONString(request));
        String responseInfo = ensureMakeService.checkUpdate(request);
        log.info("复合完成后的员工修改提示校验接口,返回报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "员工新增接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "FCPerInfoTemp", value = "人员", required = true, dataType = "FCPerInfoTemp", paramType = "body")
    })
    @RequestMapping(value = "insertStaff", method = RequestMethod.PUT)
    public String insertStaff(@RequestHeader String Authorization, @RequestBody FCPerInfoTemp perInfo, String ensureCode) {
        log.info("员工新增接口,入参: {}", JSON.toJSONString(perInfo));
        String responseInfo = ensureMakeService.insertStaff(Authorization, perInfo, ensureCode);
        log.info("员工新增接口,返回报文: {}", responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "计划导入校验")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/importCheck", method = RequestMethod.GET)
    public ApiResult<Boolean> importCheck(String ensureCode) {
        return ApiResult.ok(ensureCustomService.importCheck(ensureCode));
    }

    @ApiOperation(value = "福利定制提交")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/ensureMakeConfirm", method = RequestMethod.GET)
    public String ensureMakeConfirm(@RequestHeader String Authorization, String ensureCode) {
        log.info("福利定制提交请求报文：ensurecode={}", ensureCode);
        String responseInfo = ensureMakeService.ensureMakeConfirm(Authorization, ensureCode);
        log.info("福利定制提交返回报文: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "福利计划详情查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ensureCode", value = "福利编码", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "planCode", value = "计划编码", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/queryPlanDetail", method = RequestMethod.GET)
    public String queryPlanDetail(@RequestHeader String Authorization, String ensureCode, String planCode) {
        log.info("福利计划详情查询,入参:{},{}", Authorization, planCode);
        String responseInfo = ensureMakeService.queryPlanDetail(planCode, ensureCode);
        log.info("福利计划详情查询,返回报文：" + responseInfo);
        return responseInfo;

    }


    @ApiOperation(value = "获取计划名称列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "planobject", value = "计划对象", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "getPlanNameList", method = RequestMethod.GET)
    public String getPlanNameList(@RequestHeader String Authorization, String ensureCode, String planobject) {
        log.info("获取计划名称列表: " + Authorization + "===" + ensureCode + "===" + planobject + "===");
        String responseInfo = ensureMakeService.getPlanNameList(Authorization, ensureCode, planobject);
        log.info("获取计划名称列表返回报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "投保人告知接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
    })
    @RequestMapping(value = "appntImpartInfo", method = RequestMethod.POST)
    public String appntImpartInfo(@RequestHeader String Authorization, @RequestParam String ensureCode, @RequestBody Map<String, String> map) {
        log.info("投保人告知接口--前台传参：Authorization: {},ensureCode:{},map:{}", Authorization, ensureCode,
                JSON.toJSONString(map));
        String responseInfo = ensureMakeService.appntImpartInfo(Authorization, ensureCode, map);
        log.info("投保人告知接口--返回报文: {}", responseInfo);
        return responseInfo;

    }


    @ApiOperation(value = "投保人告知查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "queryAppntImpartInfo", method = RequestMethod.GET)
    public String queryAppntImpartInfo(@RequestHeader String Authorization, String ensureCode) {
        log.info("投保人告知查询接口");
        String responseInfo = ensureMakeService.queryAppntImpartInfo(Authorization, ensureCode);
        log.info("投保人告知查询接口,返回报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "服务人员信息查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "querySalesmanInfo", method = RequestMethod.GET)
    public String querySalesmanInfo(@RequestHeader String Authorization, String ensureCode) {
        log.info("查询服务人员信息接口入参：" + ensureCode);
        String responseInfo = ensureMakeService.querySalesmanInfo(Authorization, ensureCode);
        log.info("查询服务人员信息接口返回报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "企业信息查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "queryGrplist", method = RequestMethod.GET)
    public String queryGrplist(@RequestHeader String Authorization) {
        log.info("查询企业列表接口入参：" + Authorization);
        String responseInfo = ensureMakeService.gertGrplist(Authorization);
        log.info("查询企业列表接口返回报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "选中企业信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "grpNo", value = "企业号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "计划号", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "selectionGrp", method = RequestMethod.GET)
    public String selectionGrp(@RequestHeader String Authorization, @RequestParam String grpNo) {
        log.info("查询企业列表接口入参：" + grpNo);
        String responseInfo = ensureMakeService.selectionGrp(Authorization, grpNo);
        log.info("查询服务人员信息接口返回报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "员工家属信息修改接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "iDType", value = "证件类型", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "iDNo", value = "证件号", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "updateByFamilyTempNo", method = RequestMethod.POST)
    public String updateByFamilyTempNo(@RequestHeader String Authorization, @RequestBody FCPerinfoFamilyTemp fcPerinfoFamilyTemp, String iDType, String iDNo) {
        log.info("修改家属信息入参" + JSON.toJSONString(fcPerinfoFamilyTemp) + "====证件类型：" + iDType + "=====证件号：" + iDNo);
        String responseInfo = ensureMakeService.updateByFamilyTempNo(Authorization, fcPerinfoFamilyTemp, iDType, iDNo);
        log.info("修改家属信息返回结果:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "员工家属删除接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "familyTempNo", value = "家属临时编号", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "deleteByFamilyTempNo", method = RequestMethod.POST)
    public String deleteByFamilyTempNo(@RequestHeader String Authorization, String familyTempNo) {
        log.info("删除家属信息入参------家属临时编号：" + familyTempNo);
        String responseInfo = ensureMakeService.deleteByFamilyTempNo(Authorization, familyTempNo);
        log.info("删除家属信息结果-----------------：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "学生清单查询接口")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/queryStudent", method = RequestMethod.POST)
    public String queryStudent(@RequestHeader String Authorization, @RequestBody Map<String, String> map) {
        log.info("学生清单查询入参：" + JSON.toJSONString(map));
        String responseInfo = ensureMakeService.queryStudent(Authorization, map);
        log.info("学生清单返回报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "新增学生接口")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/insertStudent", method = RequestMethod.POST)
    public String insertStudent(@RequestHeader String Authorization, @RequestBody Map<String, String> studentMap) {
        log.info("学生信息：" + JSON.toJSONString(studentMap));
        String responseInfo = ensureMakeService.insertStudent(Authorization, studentMap);
        log.info("新增学生返回结果：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "修改学生接口")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/updateStudent", method = RequestMethod.POST)
    public String updateStudent(@RequestHeader String Authorization, @RequestBody Map<String, String> studentMap) {
        log.info("学生及监护人修改信息：" + JSON.toJSONString(studentMap));
        String responseInfo = ensureMakeService.updateStudent(Authorization, studentMap);
        log.info("修改学生信息返回结果：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "删除学生接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "familyTempNo", value = "学生临时编号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "perTempNo", value = "监护人临时编号", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/deleteStudent", method = RequestMethod.GET)
    public String deleteStudent(String Authorization, String familyTempNo, String perTempNo) {
        log.info("学生删除请求参数：" + familyTempNo + "=============" + perTempNo);
        String responseInfo = ensureMakeService.deleteStudent(Authorization, familyTempNo, perTempNo);
        log.info("修改学生信息返回结果：" + responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "弹性计划--Hr退回后台定制")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/returnEnsure", method = RequestMethod.POST)
    public String returnEnsure(@RequestHeader String Authorization, @RequestBody Map<String, String> map) {
        log.info("退回请求参数：" + JSON.toJSONString(map));
        String responseInfo = ensureMakeService.returnEnsure(Authorization, map);
        log.info("退回返回结果：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "退回原因查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/selectEnsureReturnReason", method = RequestMethod.GET)
    public String selectEnsureReturnReason(@RequestHeader String Authorization, String ensureCode) {
        log.info("退回原因查询接口请求：" + ensureCode);
        String responseInfo = ensureMakeService.selectEnsureReturnReason(Authorization, ensureCode);
        log.info("退回原因查询接口返回结果：" + responseInfo);
        return responseInfo;
    }


    /**
     * 订单号关联接口
     */
    @ApiOperation(value = "订单号关联校验接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderNo", value = "订单号", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/relationOrderNoCheck", method = RequestMethod.POST)
    public String relationOrderNoCheck(@RequestHeader String Authorization, @RequestBody Map<String, String> map) {
        String orderNo = map.get("orderNo");
        log.info("订单号关联校验接口请求：" + orderNo);
        String responseInfo = ensureMakeService.relationOrderNoCheck(Authorization, orderNo);
        log.info("订单号关联校验接口结果：" + responseInfo);
        return responseInfo;
    }

    /**
     * 关联订单
     *
     * @param Authorization
     * @param map
     * @return
     */
    @ApiOperation(value = "订单号关联接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "orderNo", value = "订单号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureName", value = "福利名称", required = true, dataType = "String", paramType = "query"),
    })
    @RequestMapping(value = "/relationOrderNo", method = RequestMethod.POST)
    public String relationOrderNo(@RequestHeader String Authorization, @RequestBody Map<String, String> map) {
        String orderNo = map.get("orderNo");
        FCEnsure fcEnsure = new FCEnsure();
        fcEnsure.setEnsureName(map.get("ensureName"));
        log.info("订单号关联接口请求：" + orderNo);
        String responseInfo = ensureMakeService.relationOrderNo(Authorization, orderNo, fcEnsure);
        log.info("订单号关联接口结果：" + responseInfo);
        return responseInfo;
    }


    /**
     * 企业名称校验
     *
     * @param Authorization
     * @param map
     * @return
     */
    @ApiOperation(value = "解除订单号接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "ensureCode", value = "福利名称", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/relieveOrderNo", method = RequestMethod.POST)
    public String relieveOrderNo(@RequestHeader String Authorization, @RequestBody Map<String, String> map) {
        String ensureCode = map.get("ensureCode");
        log.info("订单号解除关联接口请求：" + ensureCode);
        String responseInfo = ensureMakeService.relieveOrderNo(Authorization, ensureCode);
        log.info("订单号解除关联接口结果：" + responseInfo);
        return responseInfo;
    }


}

