package com.sinosoft.eflex.ctrl;

import com.sinosoft.eflex.model.RegInfo;
import com.sinosoft.eflex.model.RegistRegFind;
import com.sinosoft.eflex.service.RegistRegService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/sys")
@Api(value = "RegistRegController", description = "注册审核")
public class RegistRegController {
    //日志
    private static Logger Log = (Logger) LoggerFactory.getLogger(RegistRegController.class);
    @Autowired
    private RegistRegService registRegService;


    @ApiOperation(value = "注册审核列表查询", notes = "注册审核列表查询")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/getRegistPage", method = RequestMethod.POST)
    public String getPersonPage(@RequestBody RegistRegFind registRegFind,
                                @RequestParam("page") Integer page,
                                @RequestParam("rows") Integer rows,
                                @RequestHeader String Authorization) {
        String responsePage = registRegService.findByPage(Authorization, registRegFind, page, rows);
        Log.info("返回前台报文:" + responsePage);
        return responsePage;
    }

    @ApiOperation(value = "姓名，企业姓名查询", notes = "姓名，企业姓名查询")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/getNameList", method = RequestMethod.POST)
    public String getPersonPage(@RequestHeader String Authorization) {
        String responseAllName = registRegService.findAllName();
        Log.info("返回前台报文:" + responseAllName);
        return responseAllName;
    }

    @ApiOperation(value = "查询注册后的的信息", notes = "查询注册后的信息")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    @RequestMapping(value = "/getPersonInfo", method = RequestMethod.GET)
    public String getPersonInfo(@RequestParam String registSN, @RequestHeader String Authorization) {
        Log.info("查询注册后的的信息请求参数:Authorization-{}，registSN-{}", Authorization, registSN);
        String regPersonInfo = registRegService.getPersonInfo(registSN);
        Log.info("查询注册后的的信息返回参数: {}", regPersonInfo);
        return regPersonInfo;
    }

    @ApiOperation(value = "查询注册后的保障配置信息", notes = "查询注册后的保障配置信息")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/getEnsureConfigInfo", method = RequestMethod.GET)
    public String getEnsureConfigInfo(@RequestParam String grpNo, @RequestHeader String Authorization) {
        String regEnsureConfigInfo = registRegService.getEnsureConfigInfo(grpNo);
        Log.info("返回前台报文:" + regEnsureConfigInfo);
        return regEnsureConfigInfo;
    }

    @ApiOperation(value = "提交审核后的信息", notes = "提交审核后的信息")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/insertRegInfo", method = RequestMethod.POST)
    public String insertRegInfo(@RequestBody RegInfo regInfo, @RequestParam String registSN, @RequestHeader String Authorization) {
        String regInserInfo = registRegService.insertRegInfo(regInfo, registSN, Authorization);
        Log.info("返回前台报文:" + regInserInfo);
        return regInserInfo;
    }
}
