package com.sinosoft.eflex.ctrl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.model.HrRegist;
import com.sinosoft.eflex.model.PolicyAnalysisCapitalField;
import com.sinosoft.eflex.model.User;
import com.sinosoft.eflex.model.configmanage.AuditUserBackPasswordReq;
import com.sinosoft.eflex.model.configmanage.FindMenuInfoReq;
import com.sinosoft.eflex.model.configmanage.SendAuditUserMessageCodeReq;
import com.sinosoft.eflex.service.UserService;
import com.sinosoft.eflex.util.rest.util.ResponseResult;
import com.sinosoft.eflex.util.rest.util.ResponseResultUtil;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;


/**
 * 用户API
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/User")
@Api(value = "UserController", description = "用户API")
public class UserController {
    private static Logger Log = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;

    /**
     * 获取图片验证码
     *
     * @param request
     * @param response
     * @return
     */
    @ApiOperation(value = "获取图片验证码", notes = "获取图片验证码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收字符集", required = false, dataType = "String", paramType = "header", defaultValue = "utf-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8")})
    @RequestMapping(value = "/getVaildCode", method = RequestMethod.GET)
    public String getVaildCode(HttpServletRequest request, HttpServletResponse response) {

        // response.setHeader("Access-Control-Allow-Origin", "*"); //测试用
        return userService.createVaildCode();
    }

    @ApiOperation(value = "用户登录", notes = "用户登录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收的字符集", required = false, dataType = "String", paramType = "header", defaultValue = "UTF-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8"),
            @ApiImplicitParam(name = "user", value = "用户信息", required = true, dataType = "User", paramType = "body")})
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public String login(@RequestBody User user, HttpServletRequest request, HttpServletResponse response) {
        Log.info("用户登录请求报文: {}", JSON.toJSONString(user));
        String result = userService.login(user, request);
        Log.info("用户登录返回报文：" + result);
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                cookie.setSecure(true);
                response.addCookie(cookie);
            }
            Cookie cookie = cookies[0];
            if (cookie != null) {
                String value = cookie.getValue();
                StringBuilder builder = new StringBuilder();
                builder.append("JSESSIONID=" + value + "; ");
                builder.append("Secure=true; ");
                builder.append("HttpOnly; ");
                response.setHeader("Set-Cookie", builder.toString());
            }
        }
        response.setHeader("Content-Security-Policy", "default-src 'self' assets.giocdn.com xflow.zhongan.io wxapi.growingio.com api-xflow.zhongan.io 'unsafe-inline' 'unsafe-eval';font-src *;img-src * data:");
        return result;
    }

    @ApiOperation(value = "用户首次登录，发送完短信验证码调用", notes = "用户首次登录，发送完短信验证码调用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收的字符集", required = false, dataType = "String", paramType = "header", defaultValue = "UTF-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8"),
            @ApiImplicitParam(name = "user", value = "用户信息", required = true, dataType = "String", paramType = "body")})
    @RequestMapping(value = "/firstLogin", method = RequestMethod.POST)
    public String firstLogin(@RequestBody User user, HttpServletRequest request) {
        Log.info(">>>>>>>>>获取登录报文：" + JSON.toJSONString(user));
        String result = userService.firstLogin(user, request);
        Log.info("返回前台报文：" + result);
        return result;
    }

    @ApiOperation(value = "因互联网安全需求发送短信验证码", notes = "因互联网安全需求发送短信验证码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收的字符集", required = false, dataType = "String", paramType = "header", defaultValue = "UTF-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8"),
            @ApiImplicitParam(name = "user", value = "用户信息", required = true, dataType = "String", paramType = "body")})
    @RequestMapping(value = "/secondLogin", method = RequestMethod.POST)
    public String secondLogin(@RequestBody User user, HttpServletRequest request) {
        Log.info(">>>>>>>>>获取登录报文：" + JSON.toJSONString(user));
        String result = userService.secondLogin(user, request);
        Log.info("返回前台报文：" + result);
        return result;
    }

    @ApiOperation(value = "单点登录-获取token信息", notes = "单点登录-获取token信息（官微单点登录使用）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收的字符集", required = false, dataType = "String", paramType = "header", defaultValue = "UTF-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8"),
            @ApiImplicitParam(name = "spanToken", value = "第三方token", required = true, dataType = "String", paramType = "query")})
    @RequestMapping(value = "/spanLogin", method = RequestMethod.POST)
    public String spanLogin(String spanToken, HttpServletRequest request) {
        Log.info("单点登录-获取token信息请求报文: {}", spanToken);
        String result = userService.spanLogin(spanToken, request);
        Log.info("单点登录-获取token信息返回报文: {}", result);
        return result;
    }

    @ApiOperation(value = "单点登录-根据五要素查询系统用户信息", notes = "单点登录-根据五要素查询系统用户信息（前置机调用）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收的字符集", required = false, dataType = "String", paramType = "header", defaultValue = "UTF-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8"),
            @ApiImplicitParam(name = "userInfo", value = "用户五要素", required = true, dataType = "String", paramType = "body")})
    @RequestMapping(value = "/spanLoginApp", method = RequestMethod.POST)
    public String spanLoginApp(@RequestBody String userInfo, HttpServletRequest request) {
        Log.info("单点登录-根据五要素查询系统用户信息请求报文: {}", userInfo);
        String result = userService.spanLoginApp(userInfo, request);
        Log.info("单点登录-根据五要素查询系统用户信息返回报文: {}", result);
        return result;
    }

    @ApiOperation(value = "用户退出/注销", notes = "用户退出/注销")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "Accept", value = "接收属性", required = true, dataType = "String", paramType = "header", defaultValue = "application/json"),
            @ApiImplicitParam(name = "Accept-Charset", value = "接收的字符集", required = false, dataType = "String", paramType = "header", defaultValue = "UTF-8"),
            @ApiImplicitParam(name = "Content-Type", value = "内容类型", required = false, dataType = "String", paramType = "header", defaultValue = "application/json; charset=UTF-8")})
    @RequestMapping(value = "/logout", method = RequestMethod.POST)
    public String logout(@RequestHeader String Authorization) {
        String result = userService.logout(Authorization);
        Log.info("返回前台报文：" + result);
        return result;
    }

    /**
     * <AUTHOR>
     * @description用户的注册
     * @date 19:20 19:20
     * @modified
     */
    @ApiOperation(value = "注册校验", notes = "注册")
    @RequestMapping(value = "/registCheck", method = RequestMethod.POST)
    public String registCheck(@RequestBody HrRegist hrRegist) {
        Log.info("注册校验接口请求报文:" + JSON.toJSONString(hrRegist));
        Log.info("变更前企业信息：" + hrRegist.getOldGrpName() + "=====" + hrRegist.getOldCorporationMan() + "====="
                + hrRegist.getOldGrpIdType() + "=====" + hrRegist.getGrpIdNo());
        String insertInfo = userService.checkRegistInfo(hrRegist);
        Log.info("注册校验接口返回报文：" + insertInfo);
        return insertInfo;
    }

    @ApiOperation(value = "注册", notes = "注册")
    @RequestMapping(value = "/regist", method = RequestMethod.POST)
    public String insertRegist(@RequestBody HrRegist hrRegist) {
        Log.info("注册请求报文: {}", JSONObject.toJSONString(hrRegist));
        String insertInfo = userService.addRegistInfo(hrRegist);
        Log.info("注册返回报文: {}", insertInfo);
        return insertInfo;
    }

    /**
     * <AUTHOR>
     * @description 用户修改密码
     * @date 19:21 19:21
     * @modified
     */
    @ApiOperation(value = "修改密码", notes = "修改密码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "password", value = "原密码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "newpassword", value = "新密码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "conPassword", value = "确认密码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
    })
    @RequestMapping(value = "/modifyPassword", method = RequestMethod.PUT)
    public String modifyPasswordByUsername(@RequestHeader String Authorization, String password, String newpassword,
                                           String conPassword) {
        Log.info("用户修改密码请求报文: {},{}", newpassword, conPassword);
        String modiyPassword = userService.modifyPasswordByUsername(password, newpassword, Authorization);
        Log.info("用户修改密码返回报文：" + modiyPassword);
        return modiyPassword;
    }

    @ApiOperation(value = "创建token", notes = "创建token")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/makeToken", method = RequestMethod.GET)
    public String makeUserToken(@RequestHeader String Authorization, @RequestParam String urlType) {
        String makeToken = userService.makeToken(Authorization, urlType);
        //Log.info("返回前台报文token123=====：" + makeToken);
        return makeToken;
    }

    @ApiOperation(value = "发送找回密码激活码", notes = "customType:1 个人,2 企业HR,3 管理员")
    @RequestMapping(value = "/sendMsg", method = RequestMethod.GET)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idNo", value = "证件号码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "phone", value = "手机号码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "customType", value = "用户类型", required = true, dataType = "String", paramType = "query")})
    public String findUserInfo(String idNo, String phone, String customType) {
        String findUserInfo = userService.findUserInfo(idNo, phone, customType);
        Log.info("返回前台报文：" + findUserInfo);
        return findUserInfo;
    }

    @ApiOperation(value = "找回密码", notes = "customType:1 个人,2 企业HR,3 管理员")
    @RequestMapping(value = "/backPassword", method = RequestMethod.GET)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idNo", value = "证件号码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "phone", value = "手机号码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "customType", value = "用户类型", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "phoneCode", value = "动态验证码", required = true, dataType = "String", paramType = "query"),
    })
    public String backPassword(String idNo, String phone, String customType, String phoneCode) {
        String backPasswordInfo = userService.backPasswordInfo(idNo, phone, customType, phoneCode);
        return backPasswordInfo;
    }

    @ApiOperation(value = "查询菜单", notes = "查询菜单")
    @RequestMapping(value = "/findMenu", method = RequestMethod.POST)
    public String findMenu(
            @ApiParam(value = "新式令牌", required = true) @RequestHeader(name = "Authorization") String token,
            @ApiParam(value = "查询条件", required = true) @RequestBody FindMenuInfoReq findMenuInfoReq) {
        String findMenuInfo = userService.FindMenuInfo(token, findMenuInfoReq);
        return findMenuInfo;
    }

    @ApiOperation(value = "查询码表", notes = "查询码表")
    @RequestMapping(value = "/findCode", method = RequestMethod.GET)
    public String findCode(@RequestParam String CodeType) {
        String codeInfo = userService.findCodeInfo(CodeType);
        // Log.info("返回前台报文：" + codeInfo);
        return codeInfo;
    }

    @ApiOperation(value = "校验业务员工号", notes = "校验业务员工号")
    @RequestMapping(value = "/getClientoInfo", method = RequestMethod.GET)
    public String getClientoInfo(@RequestParam String clientNo) {
        Log.info("校验业务员工号,入参：" + clientNo);
        String clientoInfo = userService.getClientoInfo(clientNo);
        Log.info("返回前台报文：" + clientoInfo);
        return clientoInfo;
    }

    @ApiOperation(value = "企业验真", notes = "企业验真（企业名称、统一社会信用代码、法人）")
    @RequestMapping(value = "/GrpOCRInfo", method = RequestMethod.POST)
    public String grpOCRInfo(@RequestBody HrRegist hrRegist) {
        Log.info("接收前台参数：" + hrRegist);
        String responseInfo = userService.grpOCRInfo(hrRegist);
        Log.info("返回前台报文:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "查询机构", notes = "机构码表")
    @RequestMapping(value = "/findComCode", method = RequestMethod.GET)
    public String findComCode() {
        String codeInfo = userService.findComCode();
        return codeInfo;
    }

    /**
     * 短信验证码
     *
     * @param userNo
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "短信验证码", notes = "by:wyj")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userNo", value = "用户编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")})
    @RequestMapping(value = "/msgVerificationCode", method = RequestMethod.GET)
    public String msgVerificationCode(String userNo) {
        String responseInfo = userService.msgVerificationCode(userNo);
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }

    /**
     * <AUTHOR>
     * @note图片
     */
    @ApiOperation(value = "图片", notes = "图片")
    @RequestMapping(value = "/getImageInfo", method = RequestMethod.POST)
    public String getImageInfo(@RequestParam String imageInfo) {
        Log.info("前台请求参数:" + imageInfo);
        String responseInfo = userService.getImageInfo(imageInfo);
        Log.info("返回前台报文:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "查询职业码表", notes = "查询码表")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/getOccupationCode", method = RequestMethod.GET)
    public String getOccupationCode(@RequestHeader String Authorization, @RequestParam String codeType, @RequestParam String codeDesc) {
        Log.info("请求报文：" + codeDesc);
        Log.info("!!!!!!!" + codeType);
        String codeInfo = userService.getOccupationCode(codeType, codeDesc);
        Log.info("返回前台报文：" + codeInfo);
        return codeInfo;
    }

    /**
     * 通用的规则校验
     */
    @ApiOperation(value = "公共的人员校验")
    @RequestMapping(value = "/checkSinglePeople", method = RequestMethod.POST)
    public String checkSinglePeople(@RequestBody Map<String, String> map) {
        Log.info("接收请求参数:" + map);
        String resposeInfo = userService.checkPeople(map);
        Log.info("返回参数:" + resposeInfo);
        return resposeInfo;
    }


    @ApiOperation(value = "手机号登录，发送短信验证码", notes = "customType:1 个人,2 企业HR")
    @RequestMapping(value = "/sendMessageCode", method = RequestMethod.GET)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机号码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "customType", value = "用户类型", required = true, dataType = "String", paramType = "query")})
    public Map sendMessageCode(String phone, String customType) {
        Log.info("短信验证码登录前台传参：" + phone + "-" + customType);
        Map map = userService.getPhoneLoginCode(phone, customType);
        Log.info("返回前台报文：" + JSON.toJSONString(map));
        return map;
    }

    @ApiOperation(value = "录入验证码，手机号登录", notes = "customType:1 个人,2 企业HR")
    @RequestMapping(value = "/loginByPhoneCode", method = RequestMethod.GET)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机号码", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "customType", value = "用户类型", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "phoneCode", value = "动态验证码", required = true, dataType = "String", paramType = "query"),
    })
    public Map loginByPhoneCode(String phone, String customType, String phoneCode, HttpServletRequest request) {
        Log.info("短信验证码登录前台传参：phone：" + phone + " code:" + phoneCode + " type：" + customType);
        Map map = userService.phoneLogin(phone, phoneCode, customType, request);
        Log.info("返回前台报文：" + JSON.toJSONString(map));
        return map;
    }

    @ApiOperation(value = "同意协议", notes = "同意协议")
    @RequestMapping(value = "/agreement", method = RequestMethod.GET)
    public Map agreement(@RequestHeader String Authorization) {
        Log.info("短信验证码登录前台传参：" + Authorization);
        Map map1 = userService.agreement(Authorization);
        Log.info("短信验证码登录返回前台：" + JSON.toJSONString(map1));
        return map1;
    }

    @ApiOperation(value = "第三方回调平台重新创建token", notes = "第三方回调平台重新创建token")
    @RequestMapping(value = "/reciveCreateToken", method = RequestMethod.POST)
    public String reciveCreateToken(@RequestBody Map<String, String> map) {
        String resposeInfo = userService.reciveCreateToken(map);
        return resposeInfo;
    }

    /**
     * 审核岗-发送验证码
     */
    @ApiOperation(value = "审核岗忘记密码-发送手机验证码", notes = "忘记密码-手机号验证（审核岗）")
    @RequestMapping(value = "/sendAuditUserMessageCode", method = RequestMethod.POST)
    public ResponseResult<String> sendAuditUserMessageCode(
            @ApiParam(value = "用户信息", required = true) @RequestBody SendAuditUserMessageCodeReq sendAuditUserMessageCodeReq) {

        Log.info("审核岗忘记密码-发送手机验证码前台请求报文: {}", JSONObject.toJSONString(sendAuditUserMessageCodeReq));
        userService.sendAuditUserMessageCode(sendAuditUserMessageCodeReq);
        Log.info("审核岗忘记密码-发送手机验证码前台返回报文: {}：");
        return ResponseResultUtil.success();
    }

    /**
     * 审核岗-找回密码
     */
    @ApiOperation(value = "审核岗找回密码", notes = "发送密码至手机号")
    @RequestMapping(value = "/auditUserBackPassword", method = RequestMethod.POST)
    public ResponseResult<String> auditUserBackPassword(
            @ApiParam(value = "用户信息", required = true) @RequestBody AuditUserBackPasswordReq auditUserBackPasswordReq) {
        Log.info("审核岗找回密码请求报文: {}", JSONObject.toJSONString(auditUserBackPasswordReq));
        userService.auditUserBackPassword(auditUserBackPasswordReq);
        return ResponseResultUtil.success();
    }


    /**
     * 审核岗-找回密码
     */
    @ApiOperation(value = "官微隐式登录")
    @RequestMapping(value = "/implicitlyLogin", method = RequestMethod.GET)
    public Map implicitlyLogin(@ApiParam("手机号") @RequestParam(value = "phone") String phone, HttpServletRequest request) {
        Log.info("官微隐式登录: {}", phone);
        Map map = userService.implicitlyLogin(phone, request);
        Log.info("官微隐式登录: {}", JSONObject.toJSONString(map));
        return map;
    }

    /**
     * 审核岗-找回密码
     */
    @ApiOperation(value = "官微留资")
    @RequestMapping(value = "/saveData", method = RequestMethod.POST)
    public ResponseResult<String> saveData(@ApiParam("手机号") @RequestBody PolicyAnalysisCapitalField request) {
        Log.info("官微留资: {}", JSONObject.toJSONString(request));

        return userService.saveData(request);
    }


}
