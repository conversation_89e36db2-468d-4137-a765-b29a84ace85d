package com.sinosoft.eflex.ctrl;

import com.sinosoft.eflex.service.QuestionAndAnswerService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 
 * @description
 *
 * <AUTHOR>
 *
 * @date 2018年10月17日
 */
@RestController
@RequestMapping("Question")
@Api(value = "QuestionAndAnswerController", description = "团险客户常见问题")
public class QuestionAndAnswerController {

    private static Logger Log = (Logger) LoggerFactory.getLogger(QuestionAndAnswerController.class);

    @Autowired
    private QuestionAndAnswerService questionAndAnswerService;


    //获取团险客户常见问题及答案
    @ApiOperation(value = "获取团险客户常见问题及答案")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/getAnswer", method = RequestMethod.POST)
    public String getQuestionInfo(@RequestHeader String Authorization) {
        String responseInfo = questionAndAnswerService.getQuestionInfo();
        Log.info("返回前台报文：" + responseInfo);
        return responseInfo;
    }
}
