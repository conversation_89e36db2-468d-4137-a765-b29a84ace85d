package com.sinosoft.eflex.ctrl;

import java.util.List;
import java.util.Map;

import com.sinosoft.eflex.model.DailyAmountTrail;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.model.DailyPremTrail;
import com.sinosoft.eflex.service.PremTrailService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
/**
 * 保费测算API
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/PremTrailController")
@Api(value = "PremTrailController", description = "保费测算API")
public class PremTrailController {
    private static Logger Log = (Logger) LoggerFactory.getLogger(PremTrailController.class);
    @Autowired
    private PremTrailService premTrailService;
    
    @ApiOperation(value = "弹性计划保费试算接口（暂时不用）")
    @ApiImplicitParams({
	        @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
	})
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/premTrail" , method = RequestMethod.POST)
    public String premTrail(@RequestHeader String Authorization,@RequestBody Map<String,Object> map){
        Log.info("弹性计划保费试算接口请求报文："+JSON.toJSONString(map));
        Map<String,Object> resultMap = premTrailService.premTrail(map);
        Log.info("弹性计划保费试算接口返回报文："+JSON.toJSONString(resultMap));
        return JSON.toJSONString(resultMap);
    }

    @ApiOperation(value = "日常计划保费试算接口")
    @ApiImplicitParams({
    	@ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
        @ApiImplicitParam(name = "DailyPremTrail", value = "保费测算入参对象", required = true, dataType = "DailyPremTrail", paramType = "body")
    })
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/dailyPremTrail" , method = RequestMethod.POST)
    public String dailyPremTrail(@RequestHeader String Authorization,@RequestBody DailyPremTrail dailyPremTrail){
    	Log.info("日常计划保费试算接口入参："+JSON.toJSONString(dailyPremTrail));
    	Map<String,Object> resultMap = premTrailService.dailyPremTrail(dailyPremTrail);
    	Log.info("日常计划保费试算接口返回报文："+JSON.toJSONString(resultMap));
    	return JSON.toJSONString(resultMap);
    }

}
