package com.sinosoft.eflex.ctrl.Hr;

import com.hqins.common.base.ApiResult;
import com.hqins.common.utils.JsonUtil;
import com.sinosoft.eflex.model.EnsureMake.EnsureRequest;
import com.sinosoft.eflex.model.EnsureMake.GrpEnsureResponse;
import com.sinosoft.eflex.service.userClient.HrFcEnsureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


/**
 * Hr 手机端 *
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/hr-ensure")
@Api(value = "HrFcEnsureController")
@Slf4j
@RequiredArgsConstructor
public class HrFcEnsureController {


    private final HrFcEnsureService hrFcEnsureService;

    /**
     * 手机端 企业保障列表查询
     *
     * @return
     */
    @ApiOperation(value = "手机端 企业保障列表查询")
    @RequestMapping(value = "/grp-ensure", method = RequestMethod.POST)
    public ApiResult<GrpEnsureResponse> grpEnsureList(@RequestHeader String Authorization, @RequestBody EnsureRequest ensureRequest) {
        log.info("EnsureMakeController.getGrpEnsureListPagePhone,request:{}", JsonUtil.toJSON(ensureRequest));
        return ApiResult.ok(hrFcEnsureService.grpEnsureList(Authorization, ensureRequest));
    }
}
