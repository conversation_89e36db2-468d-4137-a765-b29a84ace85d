package com.sinosoft.eflex.ctrl;

import java.util.Map;
import com.sinosoft.eflex.service.AmountTrailService;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.alibaba.fastjson.JSON;
import com.sinosoft.eflex.model.DailyAmountTrail;

/**
 * 保额测算API
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/AmountTrailController")
@Api(value = "AmountTrailController", description = "保额测算API")
public class AmountTrailController {

    private static Logger Log = LoggerFactory.getLogger(AmountTrailController.class);
    @Autowired
    private AmountTrailService amountTrailService;


    @ApiOperation(value = "日常计划保额试算接口")
    @RequestMapping(value = "/dailyAmountTrail" , method = RequestMethod.POST)
    public String dailyAmountTrail(
            @ApiParam(value = "token", required = true) @RequestHeader(name = "Authorization", required = true) String token,
            @ApiParam(value = "保额试算请求对象", required = true) @RequestBody(required = true) DailyAmountTrail dailyAmountTrail){
        Log.info("日常计划保额试算接口请求报文: {}",JSON.toJSONString(dailyAmountTrail));
        Map<String,Object> resultMap = amountTrailService.dailyAmountTrail(dailyAmountTrail);
        Log.info("日常计划保额试算接口返回报文: {}",JSON.toJSONString(resultMap));
        return JSON.toJSONString(resultMap);
    }




}
