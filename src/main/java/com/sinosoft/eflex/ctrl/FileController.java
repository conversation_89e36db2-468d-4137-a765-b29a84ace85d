package com.sinosoft.eflex.ctrl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hqins.common.base.ApiResult;
import com.sinosoft.eflex.config.MyProps;
import com.sinosoft.eflex.model.OssEntity.OssEntity;
import com.sinosoft.eflex.service.FileService;
import com.sinosoft.eflex.service.OssService;
import com.sinosoft.eflex.util.FileUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件API
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/File")
@Api(value = "FileController")
@Slf4j
@RequiredArgsConstructor
public class FileController {

    private final FileService fileService;
    private final MyProps myProps;
    private final OssService ossService;

    /**
     * 实现文件上传
     */
    @ApiOperation(value = "文件上传")
    @RequestMapping(value = "/file-upload", method = RequestMethod.POST)
    @ResponseBody
    public ApiResult<String> fileUpload(@RequestParam("fileName") MultipartFile file) {
        OssEntity sign = ossService.uploadFile(file, "sign");
        return ApiResult.ok(sign.getOutUrl());

    }

    /**
     * 实现ocr文件解析
     */
    @ApiOperation(value = "ocr文件解析")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "status", value = "标志符", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "fileType", value = "文件类别", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/ocrFileUpload", method = RequestMethod.POST)
    @ResponseBody
    public String ocrFileUpload(@RequestParam("upfile") MultipartFile file, String status, String fileType) {
        log.info("前台传参：Status:" + status + "========FileType:" + fileType);
        if (status.equals("01")) {
            String ocrInfo = "";
            String fileName = file.getOriginalFilename();
            if ("0101".equals(fileType)) {
                String localFilePath = FileUtil.getLocalPath("0101");
                String localFilePathAndName = localFilePath + fileName;
                // 上传到本地
                fileService.fileUpload(file, localFilePathAndName, "0101", "");
                ocrInfo = getOcrInfo(localFilePathAndName);
            } else if ("0105".equals(fileType)) {
                String localFilePath = FileUtil.getLocalPath("0105");
                String localFilePathAndName = localFilePath + fileName;
                // 上传到本地
                fileService.fileUpload(file, localFilePathAndName, "0105", "");

                ocrInfo = getOcrInfo(localFilePathAndName);
            }
            log.info("返回报文：" + ocrInfo);
            return ocrInfo;
        } else {
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "请求成功");
            return JSON.toJSONString(resultMap);
        }

    }

    private String getOcrInfo(String localFilePathAndName) {
        String ocrInfo = "";
        if ("open".equals(myProps.getOcrOff())) {
            Map<String, Object> resultMap = new HashMap<String, Object>();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("type", "港澳台");
            jsonObject.put("rsp_code", "0000");
            resultMap.put("data", jsonObject.toJSONString());
            resultMap.put("success", true);
            resultMap.put("code", "200");
            resultMap.put("message", "ocr第三方接口调用成功");
            ocrInfo = JSON.toJSONString(resultMap);
        } else {
            ocrInfo = fileService.getFileOrcInfo(localFilePathAndName);
        }
        return ocrInfo;
    }

    /**
     * 实现文件下载
     *
     * @throws InvalidFormatException
     */
    @ApiOperation(value = "文件下载")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "docId", value = "文件编号", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "ensureCode", value = "福利编号", required = false, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/download", method = RequestMethod.GET)
    public ResponseEntity<InputStreamResource> downloadFile(String docId, String ensureCode) throws IOException, InvalidFormatException {
        log.info("文件下载：" + docId);
        return fileService.downloadFile(docId, ensureCode);
    }


    /**
     * 实现文件上传
     */
    @ApiOperation(value = "企业注册文件直接上传sFtp")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idImage1", value = "Hr影像件1", dataType = "file", required = false),
            @ApiImplicitParam(name = "idImage2", value = "Hr影像件2", dataType = "file", required = false),
            @ApiImplicitParam(name = "legIDImage1", value = "法人影像件1", dataType = "file", required = false),
            @ApiImplicitParam(name = "legIDImage2", value = "法人影像件2", dataType = "file", required = false),
            @ApiImplicitParam(name = "grpIDImage1", value = "企业影像件1", dataType = "file", required = false),
            @ApiImplicitParam(name = "grpIDImage2", value = "企业影像件2", dataType = "file", required = false),
            @ApiImplicitParam(name = "relationProve", value = "关系证明", dataType = "file", required = false)
    })
    @RequestMapping(value = "/multifileUpload", method = RequestMethod.POST)
    public String multifileUpload(MultipartFile idImage1, MultipartFile idImage2, MultipartFile legIDImage1, MultipartFile legIDImage2, MultipartFile grpIDImage1, MultipartFile grpIDImage2, MultipartFile relationProve) {
        Map<String, MultipartFile> files = new HashMap<>();
        if (idImage1 != null) {
            files.put("idImage1", idImage1);
        }
        if (idImage2 != null) {
            files.put("idImage2", idImage2);
        }
        if (legIDImage1 != null) {
            files.put("legIDImage1", legIDImage1);
        }
        if (legIDImage2 != null) {
            files.put("legIDImage2", legIDImage2);
        }
        if (grpIDImage1 != null) {
            files.put("grpIDImage1", grpIDImage1);
        }
        if (grpIDImage2 != null) {
            files.put("grpIDImage2", grpIDImage2);
        }
        if (relationProve != null) {
            files.put("relationProve", relationProve);
        }
        log.info("企业注册文件上传 size():" + files.size());
        String responseInfo = fileService.multifileUpload(files);
        log.info("企业注册文件上传完成：" + responseInfo);
        return responseInfo;
    }


    @ApiOperation(value = "注册文件回显")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sftpPath", value = "文件路径", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/fileDisplay", method = RequestMethod.GET)
    public void fileDisplay(HttpServletResponse response, String sftpPath) {
        log.info("注册文件回显--前台传参：response: {}，sftpPath: {}", response, sftpPath);
        boolean bool = fileService.fileDisplay(response, sftpPath);
        log.info("文件：" + sftpPath + ",回显:" + bool);
    }

    @ApiOperation(value = "日常计划影像件回显")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "filePath", value = "文件路径", required = true, dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/dailyFileDisplay", method = RequestMethod.GET)
    public void dailyFileDisplay(HttpServletResponse response, String filePath) {
        boolean bool = fileService.fileDisplay(response, filePath);
        log.info("文件：" + filePath + ",回显:" + bool);
    }

    /**
     * 日常计划影像件上传接口
     */
    @ApiOperation(value = "日常计划影像件上传接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/dailyFileUpload", method = RequestMethod.POST)
    public String dailyFileUpload(@RequestHeader String Authorization, @RequestBody Map<String, Object> map) {
        // 上传到ftp
        String responseInfo = fileService.dailyFileUpload(Authorization, map);
        return responseInfo;
    }

    /**
     * 日常计划影像件删除接口
     */
    @ApiOperation(value = "日常计划影像件删除接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "imageNo", value = "影像流水号", dataType = "String", paramType = "query")
    })
    @RequestMapping(value = "/dailyFileRemove", method = RequestMethod.POST)
    public String dailyFileRemove(@RequestHeader String Authorization, String imageNo) {
        String responseInfoString = fileService.dailyFileRemove(imageNo);
        return responseInfoString;
    }
}
