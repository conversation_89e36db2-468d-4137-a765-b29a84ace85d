package com.sinosoft.eflex.ctrl.edor;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sinosoft.eflex.model.FCEdorAddInsured;
import com.sinosoft.eflex.model.edor.*;
import com.sinosoft.eflex.service.edor.EdorService;
import com.sinosoft.eflex.util.rest.util.ResponseResult;
import com.sinosoft.eflex.util.rest.util.ResponseResultUtil;

import io.swagger.annotations.*;

/**
 * <AUTHOR>
 * @date 2018-09-05 17:04
 */

@RestController
@RequestMapping("edor")
@Api(value = "EdorController", description = "保全相关接口")
public class EdorController {
    private static Logger Log = LoggerFactory.getLogger(EdorController.class);
    @Autowired
    private EdorService edorService;

    @ApiOperation(value = "保全申请记录查询接口", notes = "保全查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header") })
    @RequestMapping(value = "/getEdorInfo", method = RequestMethod.POST)
    public String getEdorInfo(@RequestHeader String Authorization, @RequestBody Map<String, String> paramMap) {
        Log.info("接受请求参数:" + JSON.toJSONString(paramMap));
        String responseInfo = edorService.getEdorInfo(paramMap);
        Log.info("返回报文:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "保全项目列表查询", notes = "保全项目列表查询")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/findEdorInfo", method = RequestMethod.GET)
    public String findEdorInfo() {
        String responseInfo = edorService.findEdorInfo();
        Log.info("保全项目列表查询-返回前台报文：" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "企业保全项目查询", notes = "企业保全项目查询")
    @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    @RequestMapping(value = "/findGrpEdorInfo", method = RequestMethod.GET)
    public String findGrpEdorInfo(@RequestHeader String Authorization) {
        String GrpEdorinfo = edorService.findGrpEdorInfo(Authorization);
        Log.info("企业保全项目查询-返回前台报文：" + GrpEdorinfo);
        return GrpEdorinfo;
    }

    @ApiOperation(value = "导入增加被保险人清单（险种）", notes = "导入增加被保险人清单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header") })
    @RequestMapping(value = "/importPlusInsuredExcel", method = RequestMethod.POST)
    public String importPlusInsuredExcel(@RequestHeader String Authorization,
            @RequestParam("fileName") MultipartFile file, @RequestParam("batch") String batch,
            @RequestParam("grpContNo") String grpContNo, @RequestParam("policyeffectDate") String policyeffectDate,
            @RequestParam("policyendDate") String policyendDate) {
        Log.info("保全增人导入：batch==" + batch + "，grpContNo==" + grpContNo + "，policyeffectDate==" + policyeffectDate
                + "，policyendDate==" + policyendDate);
        String responseInfo = edorService.importPlusInsuredExcel(Authorization, file, batch, grpContNo,
                policyeffectDate, policyendDate);
        Log.info("返回报文:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "导入增加被保险人清单（计划）", notes = "导入增加被保险人清单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header") })
    @RequestMapping(value = "/importPlusInsuredExcelByPlan", method = RequestMethod.POST)
    public String importPlusInsuredExcelByPlan(@RequestHeader String Authorization,
            @RequestParam("fileName") MultipartFile file, @RequestParam("batch") String batch,
            @RequestParam("grpContNo") String grpContNo, @RequestParam("policyeffectDate") String policyeffectDate,
            @RequestParam("policyendDate") String policyendDate, @RequestParam List<String> planInfolist) {
        Log.info("保全增人导入：batch==" + batch + "，grpContNo==" + grpContNo + "，policyeffectDate==" + policyeffectDate
                + "，policyendDate==" + policyendDate + "，planlist==" + planInfolist.toString());
        String responseInfo = edorService.importPlusInsuredExcelByPlan(Authorization, file, batch, grpContNo,
                policyeffectDate, policyendDate, planInfolist);
        Log.info("返回报文:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "导入减少被保险人清单", notes = "导入减少被保险人清单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header") })
    @RequestMapping(value = "/importReduInsuredExcel", method = RequestMethod.POST)
    public String importReduInsuredExcel(@RequestHeader String Authorization,
            @RequestParam("fileName") MultipartFile file, @RequestParam("batch") String batch,
            @RequestParam("grpContNo") String grpContNo) {
        String responseInfo = edorService.importReduInsuredExcel(Authorization, file, batch, grpContNo);
        Log.info("返回报文:" + responseInfo);
        return responseInfo;
    }

    /**
     * 同质风险加减人模板解析HomogeneousRiskAddOrReducePeople
     */
    @ApiOperation(value = "导入同质风险加减人清单", notes = "导入同质风险加减人清单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header") })
    @RequestMapping(value = "/importHomogeneousRiskAddOrReducePeople", method = RequestMethod.POST)
    public String importHomogeneousRiskAddOrReducePeople(@RequestHeader String Authorization,
            @RequestParam("fileName") MultipartFile file, @RequestParam("batch") String batch,
            @RequestParam("grpContNo") String grpContNo) {
        Log.info("同质风险加减人导入前台入参：batch==" + batch + "，grpContNo==" + grpContNo);
        String responseInfo = edorService.importHomogeneousRiskAddOrReducePeople(Authorization, file, batch, grpContNo);
        Log.info("同质风险加减人导入返回报文:" + responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "查询最大批次号", notes = "查询最大批次号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header") })
    @RequestMapping(value = "/getInsuredBatch", method = RequestMethod.POST)
    public String getInsuredBatch(@RequestBody GetInsuredBatchReq getInsuredBatchReq) {
        Log.info("查询最大批次号请求报文: {}", JSONObject.toJSONString(getInsuredBatchReq));
        String responInfo = edorService.findInsuredBatch(getInsuredBatchReq);
        Log.info("查询最大批次号返回报文: {}", responInfo);
        return responInfo;
    }

    @ApiOperation(value = "保全试算接口", notes = "保全试算接口")
    @ApiImplicitParams({
	        @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
	})
    @RequestMapping(value = "/edortrialIO", method = RequestMethod.POST)
    public String edortrialIO(@RequestHeader String Authorization,@RequestBody String edoraTrialInfo) {
        Log.info("保全试算接口请求报文：" + edoraTrialInfo);
        EdoraTrialInfo edoraTrial= JSON.parseObject(edoraTrialInfo,EdoraTrialInfo.class);
        String responseInfo = edorService.trialIO(edoraTrial,Authorization);
    	Log.info("保全试算返回前台报文：" + responseInfo);
    	return responseInfo;
    }

    @ApiOperation(value = "保全申请确认接口", notes = "保全申请确认接口")
    @ApiImplicitParams({
    	@ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
    	@ApiImplicitParam(name = "EdoraApplyConfirmInfo", value = "保全申请确认", required = true, dataType = "EdoraApplyConfirmInfo", paramType = "body")
    })
    @RequestMapping(value = "/edorApplyConfirmIO", method = RequestMethod.POST)
    public String edorApplyConfirmIO(@RequestHeader String Authorization,@RequestBody String edoraApplyConfirmInfo) {
        Log.info("保全申请确认接口入参：" + edoraApplyConfirmInfo);
        EdoraApplyConfirmInfo edoraApplyConfirm= JSON.parseObject(edoraApplyConfirmInfo,EdoraApplyConfirmInfo.class);
        String responseInfo = edorService.applyConfirmIO(edoraApplyConfirm, Authorization);
    	Log.info("返回前台报文：" + responseInfo);
    	return responseInfo;
    }


    @ApiOperation(value = "保全邮编校验接口",notes = "保全邮编校验接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "grpZipCode",value = "邮政编码",required = false,dataType = "String",paramType = "query")
    })
    @RequestMapping(value = "ZipCodeCheck",method = RequestMethod.GET)
    public String ZipCodeCheck(@RequestHeader String Authorization,String grpZipCode){
        Log.info("投保企业邮政编码："+grpZipCode);
        String responseInfo = JSON.toJSONString(edorService.ZipCodeCheck(grpZipCode));
        Log.info("邮政编码校验结果："+responseInfo);
        return responseInfo;
    }
    
    @ApiOperation(value = "查询保单计划列表接口", notes = "查询核心保单对应的计划列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/selectPolicyPlan", method = RequestMethod.POST)
    public String selectPolicyPlan(@RequestHeader String Authorization,
            @RequestBody SelectPolicyPlanReq selectPolicyPlanReq) {
        Log.info("查询保单计划列表请求报文: {}", JSONObject.toJSONString(selectPolicyPlanReq));
        String responseInfo = edorService.selectPolicyPlan(Authorization, selectPolicyPlanReq);
        Log.info("查询保单计划列表返回报文: {}", responseInfo);
        return responseInfo;
    }
    
    /**
     * 保全申请证明材料上传处理：解压->校验->存本地->记录地址
     */
    @ApiOperation(value="保全申请证明文件上传")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")    })
    @RequestMapping(value = "/uploadZipOrRARFile", method = RequestMethod.POST)
    @ResponseBody
    public String uploadZipOrRARFile(@RequestHeader String Authorization,
    		@RequestParam("file") MultipartFile file,
    		@RequestParam("grpContNo") String grpContNo,
    		@RequestParam("batch") String batch,
    		@RequestParam("docType") String docType) {
    	Log.info("被保人告知书上传接口Start"+new Date());
        String responseInfo=edorService.uploadZipOrRARFile(Authorization,file,grpContNo,batch,docType);
        Log.info("返回报文:"+responseInfo);
        return responseInfo;
    }

    /**
     * 保全证明文件删除
     */
    @ApiOperation(value="保全证明文件删除")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")    })
    @RequestMapping(value = "/deleteUploadZipOrRARFile", method = RequestMethod.POST)
    public String deleteUploadZipOrRARFile(@RequestHeader String Authorization,@RequestBody Map<String,String> map) {
    	Log.info("被保人告知书上传接口Start"+new Date());
        String responseInfo=edorService.deleteUploadZipOrRARFile(Authorization,map);
        Log.info("返回报文:"+responseInfo);
        return responseInfo;
    }

    /**
     * 被保人告知书上传到FTP
     */
    @ApiOperation(value="被保人告知书上传（未使用，仅作为参考）")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")    })
    @RequestMapping(value = "/InsuredZipFileUpload", method = RequestMethod.POST)
    @ResponseBody
    public String InsuredZipFileUpload(@RequestHeader String Authorization,
    		@RequestParam("fileName") MultipartFile file,
    		@RequestParam("grpContNo") String grpContNo) {
    	Log.info("被保人告知书上传接口Start"+new Date());
        String responseInfo=edorService.InsuredZipFileUpload(Authorization,file,grpContNo);
        Log.info("返回报文:"+responseInfo);
        return responseInfo;
    }



    @ApiOperation(value = "查询保全增加被保险人信息", notes = "查询保全增加被保险人信息（导入和试算页面）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/selectEdorAddInsured", method = RequestMethod.POST)
    public String selectfcedoraddinsured(@RequestHeader String Authorization, @RequestBody SelectfcedoraddinsuredReq selectfcedoraddinsuredReq){
        Log.info("查询保全增加被保险人信息列表请求报文: {}", JSONObject.toJSONString(selectfcedoraddinsuredReq));
        String responseInfo = edorService.selectEdorAddInsured(Authorization, selectfcedoraddinsuredReq);
        Log.info("查询保全增加被保险人信息列表返回结果: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "新增、修改被保险人接口", httpMethod = "POST", notes = "新增、修改被保险人接口")
    @PostMapping(value = "/{operation}/EdorAddInsured")
    public String addAuditUser(@PathVariable(name = "operation") String operation,
            @ApiParam(value = "新式令牌", required = true) @RequestHeader(name = "Authorization") String token,
            @ApiParam(value = "用户信息", required = true) @RequestBody FCEdorAddInsured edorAddInsured) {
        Log.info("保全增加被保险人接口入参报文-{}: {}", operation, JSON.toJSONString(edorAddInsured));
        String responseInfo = edorService.dealEdorAddInsured(token, operation, edorAddInsured);
        Log.info("保全增加被保险人接口返回报文-{}: {}", operation, responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "保全增人人员删除接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header") })
    @RequestMapping(value = "/deleteEdorAddInsured", method = RequestMethod.POST)
    public ResponseResult<String> deleteEdorAddInsured(@RequestHeader String Authorization,
            @RequestBody DeleteEdorAddInsuredReq deleteEdorAddInsuredReq) {
        Log.info("保全增人人员删除请求报文: {}", JSONObject.toJSONString(deleteEdorAddInsuredReq));
        edorService.deleteEdorAddInsured(Authorization, deleteEdorAddInsuredReq);
        return ResponseResultUtil.success("保全增人人员删除成功！");
    }

    @ApiOperation(value = "查询保全减人人员信息列表", notes = "查询保全减人人员信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/selectEdorReduInsured", method = RequestMethod.POST)
    public String selectEdorReduInsured(@RequestHeader String Authorization,
            @RequestBody SelectEdorReduInsuredReq selectEdorReduInsuredReq) {
        Log.info("查询保全减人被保险人信息列表请求报文: {}", JSONObject.toJSONString(selectEdorReduInsuredReq));
        String responseInfo = edorService.selectEdorReduInsured(Authorization, selectEdorReduInsuredReq);
        Log.info("查询保全减人被保险人信息列表返回报文: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "查询同质风险加减人列表", notes = "查询同质风险加减人列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header")
    })
    @RequestMapping(value = "/selectEdorHomogeneousRiskInsured", method = RequestMethod.POST)
    public String selectEdorHomogeneousRiskInsured(@RequestHeader String Authorization,
            @RequestBody SelectEdorHomogeneousRiskInsuredReq selectEdorHomogeneousRiskInsuredReq) {
        //Log.info("获取同质风险加减人导入请求报文: {}", JSONObject.toJSONString(selectEdorHomogeneousRiskInsuredReq));
        String responseInfo = edorService.selectEdorHomogeneousRiskInsured(Authorization,
                selectEdorHomogeneousRiskInsuredReq);
        Log.info("获取同质风险加减人导入返回报文:{}",responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "保全增人试算接口", notes = "保全增人试算接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header") })
    @RequestMapping(value = "/edorAddInsuredTrialIO", method = RequestMethod.POST)
    public String edorNITrialIO(@RequestHeader String Authorization, @RequestBody EdoraNITrialInfo edoraNITrialInfo) {
        Log.info("保全增人试算接口请求报文: {}", JSONObject.toJSONString(edoraNITrialInfo));
        String responseInfo = edorService.edorNITrialIO(Authorization, edoraNITrialInfo);
        Log.info("保全增人试算接口返回报文: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "保全申请接口", notes = "选择保全项，点击保全申请")
    @RequestMapping(value = "/edorApply", method = RequestMethod.POST)
    public String edorApply(
            @ApiParam(value = "新式令牌", required = true) @RequestHeader(name = "Authorization") String token,
            @ApiParam(value = "保全申请信息", required = true) @RequestBody EdorApplyReq edorApplyReq) {
        Log.info("保全申请请求报文: {}", JSONObject.toJSONString(edorApplyReq));
        String responInfo = edorService.edorApply(edorApplyReq);
        Log.info("保全申请返回报文: {}", responInfo);
        return responInfo;
    }

    @ApiOperation(value = "保全申请引导台", notes = "查询保全申请状态及异步处理信息（点击保全申请接口后调用）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = true, dataType = "String", paramType = "header") })
    @RequestMapping(value = "/selectEdorApplyInfo", method = RequestMethod.POST)
    public String selectEdorApplyInfo(@RequestBody SelectEdorApplyInfoReq selectEdorApplyInfoReq) {
        Log.info("查询保全申请状态及异步处理信息请求报文: {}", JSONObject.toJSONString(selectEdorApplyInfoReq));
        String responseInfo = edorService.selectEdorApplyInfo(selectEdorApplyInfoReq);
        Log.info("查询保全申请状态及异步处理信息返回报文: {}", responseInfo);
        return responseInfo;
    }

    @ApiOperation(value = "保全增人申请确认接口", notes = "保全增人申请确认接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "新式令牌", required = false, dataType = "String", paramType = "header"),
            @ApiImplicitParam(name = "EdoraApplyConfirmInfo", value = "保全申请确认", required = true, dataType = "EdoraApplyConfirmInfo", paramType = "body") })
    @RequestMapping(value = "/edorAddInsuredConfirmIO", method = RequestMethod.POST)
    public String edorAddInsuredConfirmIO(@RequestHeader String Authorization,
            @RequestBody EdorAddInsuredConfirmReq edorAddInsuredConfirmReq) {
        Log.info("保全申请确认接口请求报文: {}", JSONObject.toJSONString(edorAddInsuredConfirmReq));
        String responseInfo = edorService.edorAddInsuredConfirmIO(edorAddInsuredConfirmReq, Authorization);
        Log.info("保全申请确认接口返回报文: {}", responseInfo);
        return responseInfo;
    }

}
