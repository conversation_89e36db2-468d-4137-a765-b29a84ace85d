package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCEdorReduInsured;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FCEdorReduInsured;
import com.sinosoft.eflex.model.edor.SelectEdorReduInsuredReq;

@Repository
public interface FCEdorReduInsuredMapper {

    int deleteByPrimaryKey(String decInsuredSn);

    int insert(FCEdorReduInsured record);

    int insertSelective(FCEdorReduInsured record);

    FCEdorReduInsured selectByPrimaryKey(String decInsuredSn);

    //通过减人批次号来查询
    List<FCEdorReduInsured> selectByReduInsuredBatch(String reduInsuredBatch);

    /**
     * 查询保全减人信息
     *
     * @param selectEdorReduInsuredReq
     * @return
     */
    List<FCEdorReduInsured> selectEdorReduInsured(SelectEdorReduInsuredReq selectEdorReduInsuredReq);

    int updateByPrimaryKeySelective(FCEdorReduInsured record);

    int updateByPrimaryKey(FCEdorReduInsured record);

    List<FCEdorReduInsured> getInsuredExist(Long batch);

    int insertNext(List<FCEdorReduInsured> insuredList);

    List<FCEdorReduInsured> getDecInsuredInfo(Map<String,Object> insuredMap);

    int deleteByBacth(Map<String,Object> map);
}