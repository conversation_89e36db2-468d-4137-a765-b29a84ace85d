package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCOrderBnfRela;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FCOrderBnfRelaMapper {
    int deleteByPrimaryKey(String orderItemNo);

    int insert(FCOrderBnfRela record);

    int insertSelective(FCOrderBnfRela record);

    FCOrderBnfRela selectByPrimaryKey(String orderItemNo);

    int updateByPrimaryKeySelective(FCOrderBnfRela record);

    int updateByPrimaryKey(FCOrderBnfRela record);

    /**
     * 删除单个受益人
     *
     * @param orderItemNo
     * @param bnfNo
     * @return
     */
    int deleteByOrderItemNoAndBnfNo(@Param("orderItemNo") String orderItemNo, @Param("bnfNo") String bnfNo);

    List<FCOrderBnfRela> selectlistByOrderItemNo(@Param("orderItemNo") String orderItemNo);
}