package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCMailInfo;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FCMailInfoMapper {
    int deleteByPrimaryKey(String invoiceInfoSN);

    int insert(FCMailInfo record);

    int insertSelective(FCMailInfo record);

    FCMailInfo selectByPrimaryKey(String invoiceInfoSN);

    int updateByPrimaryKeySelective(FCMailInfo record);

    int updateByPrimaryKey(FCMailInfo record);

    List<FCMailInfo> getMailInfoByPersonId(Map<String,Object> map);

    /**
     * 根据福利查询发票信息
     * @param ensurecode
     * @return
     */
    int selectByEnsureCode(String ensurecode);
}