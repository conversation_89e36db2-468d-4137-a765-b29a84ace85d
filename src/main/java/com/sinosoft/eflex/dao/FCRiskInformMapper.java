package com.sinosoft.eflex.dao;

import java.util.List;
import java.util.Map;

import com.sinosoft.eflex.model.FCRiskInform;
import com.sinosoft.eflex.model.FCRiskInformKey;

/**
 * 险种与健康告知关联表
 */
public interface FCRiskInformMapper {
    int deleteByPrimaryKey(FCRiskInformKey key);

    int insert(FCRiskInform record);

    int insertSelective(FCRiskInform record);

    FCRiskInform selectByPrimaryKey(FCRiskInformKey key);

    int updateByPrimaryKeySelective(FCRiskInform record);

    int updateByPrimaryKey(FCRiskInform record);

    /**
     * 健康告知信息查询
     * 
     * @param riskCode
     * @return
     */
    List<Map<String, Object>> selectInform(String riskCode);

}