package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.*;
import com.sinosoft.eflex.model.EnsureMake.EnsureMake;
import com.sinosoft.eflex.model.datamanage.EnsureInsureInfo;
import com.sinosoft.eflex.model.datamanage.GetEnsureInsureDataReq;
import com.sinosoft.eflex.model.insureEflexPlanPage.EflexEmployInfo;
import com.sinosoft.eflex.model.insureEflexPlanPage.InsureRiskInfo;
import com.sinosoft.eflex.model.insureEflexPlanPage.OptDutyCodeInfo;
import com.sinosoft.eflex.model.insureEflexPlanPage.SelectMinAmntDefaultReq;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface FCEnsureMapper {

    int deleteByPrimaryKey(String ensureCode);

    int checkEnsureNameIsExists(Map<String, String> params);

    int insert(FCEnsure record);

    int saveFcBatchPayBankInfo(FcBatchPayBankInfo fcBatchPayBankInfo);

    int insertSelective(FCEnsure record);

    FCEnsure selectByPrimaryKey(String ensureCode);

    FCEnsure selectEnsureByManageCom(SelectEnsureByManageComReq selectEnsureByManageComReq);

    int updateByPrimaryKeySelective(FCEnsure record);

    int updateByPrimaryKey(FCEnsure record);

    List<FCEnsure> findAll();

    List<HashMap<String, Object>> findByPage();

    /**
     * 企业保障信息查询
     *
     * @param requestMap
     * @return
     */
    List<FCEnsure> selectCompanyEnsureInfo(Map<String, Object> requestMap);

    List<FcBatchPayBankInfo> selectFcBatchPayBankInfo(Map<String, Object> requestMap);

    /**
     * 保障信息查询折线图
     *
     * @param grpNo
     * @return
     */
    List<Map<String, Object>> getEnsureChart(@Param("grpNo") String grpNo, @Param("ensureCode") String ensureCode);

    List<HashMap<String, Object>> findEnsureListByPage(Map<String, Object> map);


    /**
     * 查询福利信息*
     *
     * @param ensureMake
     * @return
     */
    List<FCEnsure> findEnsureListByPagePhone(EnsureMake ensureMake);

    List<FCEnsure> findEnsureList(Map<String, Object> map);

    /**
     * 投保须知
     *
     * @param map
     * @return
     */
    List<Map<String, Object>> selectInsureNotes(Map<String, Object> map);

    List<Map<String, String>> getPersonIdAndPlanCode(@Param("ensureCode") String ensureCode,
                                                     @Param("perNo") String perNo);

    String findNewensureCode(Map<String, Object> map);

    List<FCEnsure> selectEnsureList(Map<String, Object> map);

    String getEffEctiveEnsure(@Param("ensureCode") String ensureCode);

    FCEnsure getFCEnsureByPlanCode(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode);

    FCEnsure getFcensureByGrpContNo(@Param("grpContNo") String grpContNo);

    // 查询临时表中的家属身份是否存在学生
    Integer selectFamTempStudentCount(String personId);

    // 根据PlanCode查询所属企业号
    String selectGrpNoByPLan(String plancode);

    // 查询所有的福利信息
    List<FCEnsureGrpInfo> selectAllEnsure(String perNo);

    List<FCEnsureGrpInfo> selectAllEnsureandGrpNo(Map<String, Object> map);

    List<Map<String, String>> selectCheckDeductibleList(@Param("amountGrageCode") String amountGrageCode,
                                                        @Param("deductible") String deductible);

    List<Map<String, String>> selectCheckCompensationRatioList(@Param("amountGrageCode") String amountGrageCode,
                                                               @Param("compensationRatio") String compensationRatio);

    List<String> selectDeductibleList(String amountGrageCode);

    List<String> selectCompensationRatioList(String amountGrageCode);

    List<Map<String, Object>> selectOptDutyCodeList(@Param("amountGrageCode") String amountGrageCode,
                                                    @Param("riskCode") String riskCode);

    List<OptDutyCodeInfo> selectOptDutyCodeInfoList(@Param("amountGrageCode") String amountGrageCode,
                                                    @Param("riskCode") String riskCode);

    List<Map<String, Object>> selectMinAmntDefault(Map<String, Object> map);

    List<EflexEmployInfo> selectMinAmntDefault1(SelectMinAmntDefaultReq selectMinAmntDefaultReq);

    List<Map<String, Object>> selectEflexEmployList(Map<String, Object> map);

    /**
     * 查询符合员工的保额档次
     *
     * @param selectMinAmntDefaultReq
     * @return
     */
    List<EflexEmployInfo> selectEflexEmployList1(SelectMinAmntDefaultReq selectMinAmntDefaultReq);

    /**
     * 查询符合员工的投保险种信息，即投保专区的最外层结构
     *
     * @param selectMinAmntDefaultReq
     * @return
     */
    List<InsureRiskInfo> selectInsureRiskInfoList(SelectMinAmntDefaultReq selectMinAmntDefaultReq);

    List<Map<String, Object>> selectFamMinAmntDefault(Map<String, Object> map);

    List<Map<String, Object>> selectEflexEmployFamilyList(Map<String, Object> map);

    /**
     * 得到所有注册通过的企业
     *
     * @return
     */
    List<FCGrpInfo> selectGrpInfo();

    FCEnsure getEnsureByGrpNo(@Param("grpNo") String grpNo, @Param("IDNo") String IDNo);

    FCEnsure selectByEnsureName(@Param("EnsureName") String EnsureName);

    int checkEnsureNameIsExists_Daily(Map<String, String> params);

    List<FCEnsure> selectEnsureList_Daily(Map<String, Object> map);

    FCEnsure selectByPrimaryKey_Daily(String ensureCode);

    int selectishasDaily(@Param("GrpOrderNo") String GrpOrderNo, @Param("IDNo") String IDNo);

    int selectishasonDaily(@Param("IDNo") String IDNo);

    /**
     * 日常计划列表查询
     *
     * @param params
     * @return
     */
    List<Map<String, String>> selectDailyPlan(Map<String, String> params);

    /**
     * 日常产品信息查询
     *
     * @param ensureCode
     * @return
     */
    List<Map<String, String>> selectDaliyPlanInfo(String ensureCode);

    /**
     * 安颐无忧年金产品信息查询
     *
     * @param ensureCode
     * @return
     */
    List<Map<String, String>> selectDaliyPlanInfoNew(String ensureCode);

    List<Map<String, String>> selectDaliyPlanInfoForPrem(String ensureCode);

    /**
     * 日常计划 -初审-查询 所有日常计划 列表
     *
     * @param params
     * @return
     */
    List<HashMap<String, Object>> selectAllDailyPlan(Map<String, String> params);

    /**
     * 日常计划 -初审-查询所有新增计划 待审核状态的计划。
     *
     * @param params
     * @return
     */
    List<HashMap<String, Object>> selectByEnsureState013(Map<String, String> params);

    List<HashMap<String, Object>> selectByEnsureState013_check(Map<String, String> params);

    /**
     * 日常计划 -初审-查询所有新增计划 审核退回状态的计划。
     *
     * @param params
     * @return
     */
    List<HashMap<String, Object>> selectByEnsureState017(Map<String, String> params);

    /**
     * 日常计划 -初审-查询所有新增计划 已承保 状态的计划。
     *
     * @param params
     * @return
     */
    List<HashMap<String, Object>> selectByEnsureState015(Map<String, String> params);

    // 查询 日常计划定制 -复核 -日常计划列表 -计划状态已承保-审核状态为新增
    List<HashMap<String, Object>> selectByEnsure0(Map<String, String> params);

    List<HashMap<String, Object>> selectByEnsure1(Map<String, String> params);

    /**
     * 日常计划定制移动端 根据grpNo查询所有的福利信息(包括年度计划和 日常计划）
     *
     * @param map
     * @return
     */
    List<FCEnsureGrpInfo> selectEnsureList_DailyPhone(Map<String, Object> map);

    List<FCEnsureGrpInfo> selectEnsureList_DailyChange(Map<String, Object> map);

    List<FCEnsureGrpInfo> selectEnsureList_Dailys(Map<String, Object> map);

    List<FCEnsureGrpInfo> selectEnsureList_Years(Map<String, Object> map);

    /**
     * 根据订单号查询福利信息
     *
     * @param orderNo
     * @return
     */
    FCEnsure selectEnsureByOrderNo(String orderNo);

    /**
     * 查询是否有福利审核的绑定关系
     */
    int selectByOperator(String userNo);

    /**
     * 查询福利相关投保信息
     *
     * @param ensureCodeList
     * @return
     */
    List<EnsureInsureInfo> selectEnsureInsureInfo(List<String> ensureCodeList);

    /**
     * 根据企业和经办人查询福利信息
     */
    List<FCEnsure> selectEnsureByGrpNo(@Param("grpNo") String grpNo, @Param("idNo") String idNo);


    /**
     * 导出福利相关投保信息
     *
     * @param getEnsureInsureDataReq
     * @return
     */
    List<EnsureInsureInfo> selectExportEnsureInsureInfo(GetEnsureInsureDataReq getEnsureInsureDataReq);

    void updateOrderNoNull(FCEnsure fcEnsure);

    /**
     * 根据计划书绑定的订单号查询福利
     *
     * @param orderNo
     * @return
     */
    FCEnsure selectByOrderNo(String orderNo);

    void updateByEnsureCode(@Param("ensureCode") String ensureCode, @Param("clientNo") String clientNo);

    void updateEnsureState(@Param("ensureCode") String ensureCode);

}