package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FcBusinessProDutyGrpObject;
import com.sinosoft.eflex.model.FcDefaultRiskGrade;

import java.util.List;
import java.util.Map;

import com.sinosoft.eflex.model.FcDutyAmountGrade;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface FcBusinessProDutyGrpObjectMapper {
    int deleteByPrimaryKey(Map<String,String> map);

    int deleteByEnsureCode(@Param("ensureCode") String ensureCode,@Param("insuredType") String insuredType);

    int insertList(List<FcBusinessProDutyGrpObject> list);

    int insertSelective(FcBusinessProDutyGrpObject record);

    FcBusinessProDutyGrpObject selectByPrimaryKey(@Param("serialNo") String serialNo, @Param("ensureCode") String ensureCode, @Param("amountGrageCode") String amountGrageCode);

    int updateByPrimaryKeySelective(FcBusinessProDutyGrpObject record);

    int updateByPrimaryKey(FcBusinessProDutyGrpObject record);

    List<FcBusinessProDutyGrpObject> selectByEnsureCode(@Param("ensureCode")String ensureCode,@Param("isCheck")String isCheck);

    List<FcBusinessProDutyGrpObject> selectByGrpNo(@Param("grpNo") String grpNo, @Param("isCheck") String isCheck, @Param("ensureCode") String ensureCode);

    //查询单个人符合的所有的险种默认档次。
    List<FcDefaultRiskGrade> selectDefaultRiskGrade(Map<String,Object> map);

    List<FcDutyAmountGrade> getFcDutyAmountGradeListByEnsureCode(Map<String,String> map);

    List<FcBusinessProDutyGrpObject> getDutyAmountGradeBySerialNo(Map<String,String> map);

    List<FcDutyAmountGrade> getAllDutyAmountGradeBySerialNo(Map<String,String> map);

    /**
     * 查询用户投保类型
     * @param ensureCode
     * @return
     */
    List<String> selectInsuredType(String ensureCode);

}