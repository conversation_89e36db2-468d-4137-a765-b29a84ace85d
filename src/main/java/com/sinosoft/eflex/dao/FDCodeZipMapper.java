package com.sinosoft.eflex.dao;

import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FDCodeZip;

@Repository
public interface FDCodeZipMapper {
    int deleteByPrimaryKey(String zipCodeKey);

    int insert(FDCodeZip record);

    int insertSelective(FDCode<PERSON>ip record);

    FDCodeZip selectByPrimaryKey(String zipCodeKey);

    int updateByPrimaryKeySelective(FDCodeZip record);

    int updateByPrimaryKey(FDCodeZip record);
}