package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCOrderItemDetail;
import com.sinosoft.eflex.model.FCOrderItemDetailKey;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FCOrderItemDetailMapper {
	int deleteByPrimaryKey(FCOrderItemDetailKey key);

	int insert(FCOrderItemDetail record);

	int insertSelective(FCOrderItemDetail record);

	FCOrderItemDetail selectByPrimaryKey(FCOrderItemDetailKey key);

	int updateByPrimaryKeySelective(FCOrderItemDetail record);
	int updateByPrimaryKeySelect(FCOrderItemDetail record);

	int updateByPrimaryKey(FCOrderItemDetail record);

	/**
	 * 投保清单导出excel
	 * 
	 * @param orderItemDetailNo
	 * @return
	 */
	FCOrderItemDetail selectInsuredDetail(@Param("ensureCode") String ensureCode,@Param("orderItemDetailNo") String orderItemDetailNo,@Param("planName") String planName);

    List<FCOrderItemDetail> selectList(Map<String, Object> params);

    int deleteByKey(String orderItemDetailNo);
    
    int deleteByPersonId(@Param("orderNo") String orderNo,@Param("personId") String personId);
    
    int deleteByOrderNo(String orderNo);

    int insertAll(FCOrderItemDetail fcOrderItemDetail);

    /**
     *
     * @param orderItemNo
     * @return
     */
    List<Map<String, String>> selectDailyInsuredInfo(String orderItemNo);

    /**
     * 查询日常计划 个人保费汇总信息
     * 
     * @param orderItemNo
     * @return
     */
	Map<String, String> selectTotalPremInfo(String orderItemNo);

	/**
	 * 根据 子订单详情编号 在表fcorderitemdetail 查到对象
	 * @param orderItemDetailNo
	 * @return
	 */
	FCOrderItemDetail selectByPrimaryKey_No(@Param("orderItemDetailNo")String orderItemDetailNo);

	/**
	 * 根据订单号查询日常计划订单信息
	 * @param orderItemNo
	 * @return
	 */
	Map<String, String> selectOrderItemInfo(String orderItemNo);
	
	int updateInsureAmountContNo(Map<String, Object> requestMap);

    // 批量执行更新语句
    int updateInsureAmountBeDailySign(List<FCOrderItemDetail> list);

	FCOrderItemDetail selectDetailByOrderItemDetailNo(String orderItemDetailNo);

	void updateByOrderItemDetailNo(@Param("contPlanCode")String contPlanCode,@Param("orderItemDetailNo") String orderItemDetailNo);
}