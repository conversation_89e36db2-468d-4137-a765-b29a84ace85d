package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCPlanRiskDuty;
import com.sinosoft.eflex.model.FCPlanRiskDutyKey;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FCPlanRiskDutyMapper {
    int deleteByPrimaryKey(FCPlanRiskDutyKey key);

    int insert(FCPlanRiskDuty record);

    int insertSelective(FCPlanRiskDuty record);

    FCPlanRiskDuty selectByPrimaryKey(FCPlanRiskDutyKey key);

    int updateByPrimaryKeySelective(FCPlanRiskDuty record);

    int updateByPrimaryKey(FCPlanRiskDuty record);


    /**
     * 计划详情查询
     *
     * @param planCode
     * @param riskCode
     * @return
     */
    List<FCPlanRiskDuty> selectPlanDetailList(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode, @Param("riskCode") String riskCode);

    /**
     * 企业计划汇总
     *
     * @param ensureCode
     * @param planCode
     * @param riskCode
     * @return
     */
    List<FCPlanRiskDuty> selectPlanCollection(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode, @Param("riskCode") String riskCode);


    List<Map<String, Object>> selectRiskPremMap(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode);

    List<FCPlanRiskDuty> selectDutyList(Map<String, Object> params);

    List<FCPlanRiskDuty> selectDutyListOrderByDuty(Map<String, Object> params);

    double selectTotalPremPlan(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode);

    int deleteByEnsureCode(String ensureCode);

    List<FCPlanRiskDuty> selectDutyInfo(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode, @Param("riskCode") String riskCode);

    void insertList(List<FCPlanRiskDuty> fcPlanRiskDutyList);

    List<Map<String, String>> selectDutyListByPlanCode(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode);

    int selectCount(@Param("ensureCode") String ensureCode, @Param("riskCode") String riskCode);

    /**
     * 固定计划-删除福利下某计划
     *
     * @param ensureCode
     * @param planCode
     * @return
     */
    int deleteByEnsureCodeAndPlanCode(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode);

    /**
     * 固定计划-删除福利计划下险种信息
     *
     * @param fcPlanRiskDutyKey
     * @return
     */
    int deleteRiskInfo(FCPlanRiskDutyKey fcPlanRiskDutyKey);

    /**
     * 固定计划-删除福利计划下险种责任信息
     *
     * @param fcPlanRiskDutyKey
     * @return
     */
    int deleteRiskDutyInfo(FCPlanRiskDutyKey fcPlanRiskDutyKey);

    /**
     * 固定计划——责任信息回显，排除GD0071责任信息，17050险种回显需要合并
     *
     * @param fcPlanRiskDuty
     * @return
     */
    List<FCPlanRiskDuty> slectRiskDutyAndPlanRisk(FCPlanRiskDuty fcPlanRiskDuty);

    /**
     * 固定计划——险种信息回显，排除GD0071责任信息，17050险种回显需要合并
     *
     * @param fcPlanRiskDuty
     * @return
     */
    List<FCPlanRiskDuty> slectRiskDutyAndEnsurePlan(FCPlanRiskDuty fcPlanRiskDuty);

    /**
     * 删除定制中的无效福利计划
     *
     * @param ensureCode
     * @return
     */
    int deleteMakeingRiskDutyInfoByEnsureCode(String ensureCode);

    /**
     * 查询险种责任信息
     *
     * @param ensureCode
     * @param planCode
     * @return
     */
    List<FCPlanRiskDuty> selectRiskDutyListByPlanCode(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode);

    void updateByEnsureCode(@Param("jEnsureCode") String jEnsureCode, @Param("ensureCode") String ensureCode);

    FCPlanRiskDuty selectSumPrem(Map<String, Object> params);

}