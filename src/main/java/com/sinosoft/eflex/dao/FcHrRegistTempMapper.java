package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FcHrRegistTemp;
import com.sinosoft.eflex.model.HrRegist;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Param;

@Repository
public interface FcHrRegistTempMapper {

    int deleteByPrimaryKey(String registSN);

    int insert(FcHrRegistTemp record);

    int insertSelective(FcHrRegistTemp record);

    FcHrRegistTemp selectByPrimaryKey(String registSN);

    int updateByPrimaryKeySelective(FcHrRegistTemp record);

    int updateByPrimaryKeyWithBLOBs(FcHrRegistTemp record);

    int updateByPrimaryKey(FcHrRegistTemp record);

    /**
     * 查询企业HR注册信息
     *
     * @param unifiedsociCode
     * @param idNo
     * @return
     */
    FcHrRegistTemp selectHrRegistTemp(@Param("unifiedsociCode") String unifiedsociCode, @Param("idNo") String idNo);

    /**
     * @param record
     * @return
     */
    int insertSelectiveByHrRegist(HrRegist record);

    /**
     * 更新企业HR注册信息表的信息
     *
     * @param fcHrRegistTemp
     * @return
     */
    int updateHrRegistTemp(FcHrRegistTemp fcHrRegistTemp);

    /**
     * 更新企业注册信息表的信息
     *
     * @return
     */
    int updateFcHrRegistTempInfo(FcHrRegistTemp fcHrRegistTemp);

}