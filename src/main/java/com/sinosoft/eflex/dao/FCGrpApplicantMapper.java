package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCGrpApplicant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 团体投保人表
 */

@Repository
public interface FCGrpApplicantMapper {
    int deleteByPrimaryKey(String grpAppNo);

    int insert(FCGrpApplicant record);

    int insertSelective(FCGrpApplicant record);

    FCGrpApplicant selectByPrimaryKey(String grpAppNo);

    int updateByPrimaryKeySelective(FCGrpApplicant record);

    int updateByPrimaryKeyWithBLOBs(FCGrpApplicant record);

    int updateByPrimaryKey(FCGrpApplicant record);


    /**
     * 导出计划清单excel
     *
     * @return
     */
    FCGrpApplicant selectInsuredDetail(@Param("grpAppNo") String grpAppNo);

    List<FCGrpApplicant> selectByGrpNo(@Param("grpNo")String grpNo);
}