package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCHealthDesignDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FCHealthDesignDetailMapper {
    int deleteByPrimaryKey(String healthDesignNo);

    int insert(FCHealthDesignDetail record);

    int insertSelective(FCHealthDesignDetail record);

    FCHealthDesignDetail selectByPrimaryKey(String healthDesignNo);

    int updateByPrimaryKeySelective(FCHealthDesignDetail record);

    int updateByPrimaryKey(FCHealthDesignDetail record);

    void insertList(List<FCHealthDesignDetail> fcHealthDesignDetailList);

    FCHealthDesignDetail selectByParams(@Param("healthDesignNo") String healthDesignNo,@Param("ensureCode") String ensureCode);

}