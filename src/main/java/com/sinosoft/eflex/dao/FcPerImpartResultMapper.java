package com.sinosoft.eflex.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FcPerImpartResult;
import com.sinosoft.eflex.model.makeProposalForm.InsuredImpart;

@Repository
public interface FcPerImpartResultMapper {
    int deleteByPrimaryKey(@Param("orderItemNo") String orderItemNo, @Param("impartVer") String impartVer,
            @Param("impartCode") String impartCode);

    int insert(FcPerImpartResult record);

    int insertSelective(FcPerImpartResult record);

    FcPerImpartResult selectByPrimaryKey(@Param("orderItemNo") String orderItemNo, @Param("impartVer") String impartVer,
            @Param("impartCode") String impartCode);

    int updateByPrimaryKeySelective(FcPerImpartResult record);

    int updateByPrimaryKey(FcPerImpartResult record);

    /**
     * 插入以上皆否的个人健康告知信息
     */
    int insertSameResultImpartList(FcPerImpartResult fcPerImpartResult);

    /**
     * 根据子订单号进行删除个人健康告知信息
     * 
     * @param orderItemNo
     * @return
     */
    int deleteByOrderItemNo(String orderItemNo);

    /**
     * 根据订单号进行删除个人健康告知信息
     * 
     * @param orderNo
     * @return
     */
    int deleteByOrderNo(String orderNo);

    /**
     * 根据子订单号查询个人健康告知信息
     */
    List<InsuredImpart> selectByOrderItemNo(String orderItemNo);

}