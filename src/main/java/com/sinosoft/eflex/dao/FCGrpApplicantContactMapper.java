package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCGrpApplicantContact;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface FCGrpApplicantContactMapper {
    int deleteByPrimaryKey(String serialNo);

    int insert(FCGrpApplicantContact record);

    int insertSelective(FCGrpApplicantContact record);

    FCGrpApplicantContact selectByPrimaryKey(String serialNo);

    int updateByPrimaryKeySelective(FCGrpApplicantContact record);

    int updateByPrimaryKey(FCGrpApplicantContact record);


    FCGrpApplicantContact selectByGrpNoandGrpAppNo(@Param("grpNo") String grpNo, @Param("grpAppNo") String grpAppNo);

    int deleteByGrpAppNo(String grpAppNo);

}