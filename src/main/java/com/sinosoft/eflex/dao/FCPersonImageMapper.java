package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCPersonImage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FCPersonImageMapper {
    int deleteByPrimaryKey(String imageNo);

    int insert(FCPersonImage record);

    int insertSelective(FCPersonImage record);
    
    Integer getImageOrderInfo(@Param("orderItemNo")String orderItemNo,@Param("imageType") String imageType);

    FCPersonImage selectByPrimaryKey(String imageNo);
    
    List<FCPersonImage> selectByOrderItemNo(String orderItemNo);

    int updateByPrimaryKeySelective(FCPersonImage record);

    int updateByPrimaryKey(FCPersonImage record);
    
    int updateImageFtpUrlByImageNo(List<FCPersonImage> list);

    /**
     * 根据 子订单号和PersonID 查询 该用户是否上传身份影像
     * @param orderItemNo
     * @param personID
     */
    int selectCountByOrderItemNo(@Param("orderItemNo")String orderItemNo,@Param("personID") String personID);

    /**
     * 查询影像信息
     * @param params
     * @return
     */
    List<FCPersonImage> selectImages(Map<String, String> params);
    
    List<FCPersonImage> selectImagesSign(Map<String, String> params);

    
    List<FCPersonImage> selectImagesInfo(Map<String, Object> params);

    /**
     * 查询人脸识别信息
     * @param orderItemNo
     * @param personId
     * @return
     */
    String selectOrderFace(@Param("orderItemNo") String orderItemNo, @Param("personId") String personId);

    /**
     * 查询该人 的人脸识别结果个数，不论通过或者不通过
     * @param orderItemNo
     * @param personId
     * @return
     */
    int selectOrderFaceishad(@Param("orderItemNo") String orderItemNo, @Param("personId") String personId);

    List<FCPersonImage> querySignImage(String date);

    FCPersonImage selectByOrderItemNoAndImageType(@Param("orderItemNo")String orderItemNo ,@Param("imageType")String imageType);

    void updateByImageNo(FCPersonImage image);
}