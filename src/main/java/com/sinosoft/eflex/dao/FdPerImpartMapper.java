package com.sinosoft.eflex.dao;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FdPerImpart;

@Repository
public interface FdPerImpartMapper {
    int deleteByPrimaryKey(@Param("impartVer") String impartVer, @Param("impartCode") String impartCode);

    int insert(FdPerImpart record);

    int insertSelective(FdPerImpart record);

    FdPerImpart selectByPrimaryKey(@Param("impartVer") String impartVer, @Param("impartCode") String impartCode);

    int updateByPrimaryKeySelective(FdPerImpart record);

    int updateByPrimaryKey(FdPerImpart record);
}