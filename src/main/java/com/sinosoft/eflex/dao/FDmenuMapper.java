package com.sinosoft.eflex.dao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FDmenu;
import com.sinosoft.eflex.model.configmanage.FindMenuInfoReq;

@Repository
public interface FDmenuMapper {
    int deleteByPrimaryKey(String nodeCode);

    int insert(FDmenu record);

    int insertSelective(FDmenu record);

    FDmenu selectByPrimaryKey(String nodeCode);

    int updateByPrimaryKeySelective(FDmenu record);

    int updateByPrimaryKey(FDmenu record);

    List<HashMap<String, Object>> findMenuInfo(String userNo);

    List<HashMap<String, Object>> findMenuInfo1(FindMenuInfoReq findMenuInfoReq);

}