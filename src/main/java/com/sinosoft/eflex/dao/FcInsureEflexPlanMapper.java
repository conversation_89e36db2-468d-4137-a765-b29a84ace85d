package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FcInsureEflexPlan;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

import java.util.List;

@Repository
public interface FcInsureEflexPlanMapper {
    int deleteByPrimaryKey(@Param("orderItemDetailNo") String orderItemDetailNo, @Param("amountGrageCode") String amountGrageCode);

    int insert(FcInsureEflexPlan record);

    int insertSelective(FcInsureEflexPlan record);

    FcInsureEflexPlan selectByPrimaryKey(@Param("orderItemDetailNo") String orderItemDetailNo, @Param("amountGrageCode") String amountGrageCode);

    int updateByPrimaryKeySelective(FcInsureEflexPlan record);

    int updateByPrimaryKey(FcInsureEflexPlan record);

    int insertList(List<FcInsureEflexPlan> list);

    //根据personId得到福利编号、子订单详情编号
    List<Map<String,String>> selectByPersonId(String personID);
    
    //根据personId得到福利编号、子订单详情编号
    List<Map<String,String>> getByPersonId(String personID);

    //根据子订单详情编号查询信息
    List<Map<String, String>> selectByorderItemDetailNo(String orderItemDetailNo);

    //查询保险凭证
    List<Map<String, String>> selectDetailByPersonId(String personID);

    Map<String,String> getRiskPremByOrderItemDetailNo(Map<String,String> map);

    int deleteByOrderNo(String orderNo);

    List<Map<String,String>> getInsureRiskDutyListByOrderItemDetailNo(String orderItemDetailNo);

    List<Map<String,String>> getDutyAmntByRiskCode(Map<String,Object> map);

    List<String> getRiskCodeByOrderItemDetailNo(String orderItemDetailNo);
}