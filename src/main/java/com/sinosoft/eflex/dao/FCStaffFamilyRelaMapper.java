package com.sinosoft.eflex.dao;

import java.util.List;
import java.util.Map;

import com.sinosoft.eflex.model.FCPerInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FCStaffFamilyRela;

@Repository
public interface FCStaffFamilyRelaMapper {
	int deleteByPrimaryKey(Map<String, String> map);

	int insert(FCStaffFamilyRela record);

	int insertSelective(FCStaffFamilyRela record);

	FCStaffFamilyRela selectByPrimaryKey(Map<String, String> map);
	
	FCStaffFamilyRela selectRelaSameStaff(Map<String, String> map);

	FCStaffFamilyRela selectRelaSameStaffOne(Map<String, String> map);

	int updateByPrimaryKeySelective(FCStaffFamilyRela record);

	int updateByPrimaryKey(FCStaffFamilyRela record);

	int deletePersonId(String personId);

	FCStaffFamilyRela selectPersonIdInfo(String personId);
	
	List<FCStaffFamilyRela> selectPersonIdInfos(String personId);

	// 查询员工本人唯一数据
	FCStaffFamilyRela selectByPerNo(String perNo);

	int updateStaffFamilyRela(FCStaffFamilyRela fcStaffFamilyRela);
	
	//查询该家属是否为员工
    int selectStaffIdentityCount(FCStaffFamilyRela fcStaffFamilyRela);

	/**
	 * 导出计划清单excel : 根据个人编码查询与员工关系
	 * 
	 * @param personID
	 * @return
	 */
	FCStaffFamilyRela selectInsuredDetail(@Param("personID") String personID);

	List<FCStaffFamilyRela> getPersonByInfo(String perNo);

	List<FCStaffFamilyRela> getPersonByInsurePlan(Map<String,String> map);

	int updateFamilyRelaByIDNo(@Param(value = "list")  List<Map<String,Object>> list);
	
	List<String> selectStaffCount(Map<String,String> map);
	
	Integer selectCountPerson(String ensureCode);
	
	String selectStaffPersonid(String perNo);

    /**
     * 查询Base64编码的关系证明信息
     * @return
     */
	List<FCStaffFamilyRela> selectBase64RelationProve();

    /**
     * 批量修改base64的影像件，转到Ftp
     * @param list
     * @return
     */
    Integer updateBase64RelationProvePath(List<FCStaffFamilyRela> list);

}