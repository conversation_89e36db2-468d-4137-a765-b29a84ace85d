package com.sinosoft.eflex.dao;

import org.springframework.stereotype.Repository;
import com.sinosoft.eflex.model.FdGrpInsureConfig;

/**
 * 企业投保配置表，标识是否为场地险投保
 */
@Repository
public interface FdGrpInsureConfigMapper {
    int deleteByPrimaryKey(String grpNo);

    int insert(FdGrpInsureConfig record);

    int insertSelective(FdGrpInsureConfig record);

    FdGrpInsureConfig selectByPrimaryKey(String grpNo);

    int updateByPrimaryKeySelective(FdGrpInsureConfig record);

    int updateByPrimaryKey(FdGrpInsureConfig record);
}