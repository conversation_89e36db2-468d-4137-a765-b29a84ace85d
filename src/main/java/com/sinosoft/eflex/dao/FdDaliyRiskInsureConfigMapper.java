package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.dailyplan.DailyInsureInfo;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> wuShiHao
 * @date : 2021-02-05 12:22
 **/
@Repository
public interface FdDaliyRiskInsureConfigMapper {

    List<Map<String, String>> selectDailyInsurePeriodInfo(DailyInsureInfo dailyInsureInfo);

    List<Map<String, String>> selectPaymentFrequencyInfo(DailyInsureInfo dailyInsureInfo);

    List<Map<String, String>> selectPaymentPeriodInfo(DailyInsureInfo dailyInsureInfo);

}
