package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FcDailyInsureRiskDetailInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> wenying Xia
 * @date : 2020-04-16 10:54
 **/
@Repository
public interface FcDailyInsureRiskDetailInfoMapper {
    int insert(FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo);

    int insertSelective(FcDailyInsureRiskDetailInfo fcDailyInsureRiskDetailInfo);

    List<FcDailyInsureRiskDetailInfo> selectListBydailyPlanCode(@Param("EnsureCode") String EnsureCode);


    List<FcDailyInsureRiskDetailInfo> selectByCodeAndstate(@Param("EnsureCode") String EnsureCode);

    List<FcDailyInsureRiskDetailInfo> selectByCodeAndstateAll(@Param("EnsureCode") String EnsureCode);

    List<FcDailyInsureRiskDetailInfo> selectByCodeAndstate013(@Param("EnsureCode") String EnsureCode);

    List<FcDailyInsureRiskDetailInfo> selectByCodeAndstate_success(@Param("EnsureCode") String EnsureCode);

    List<FcDailyInsureRiskDetailInfo> selectByCodeAndstate_Update(@Param("EnsureCode") String EnsureCode);

    FcDailyInsureRiskDetailInfo selectByCodeAndEnsureCode(@Param("PlanCode") String PlanCode, @Param("EnsureCode") String EnsureCode);


    int deleteByEnsureCode(@Param("EnsureCode") String EnsureCode);

    int updatePlanCodeTobad(@Param("EnsureCode") String ensureCode);

    FcDailyInsureRiskDetailInfo selectByPlanCodeAndEnsureCode(@Param("EnsureCode") String ensureCode, @Param("PlanCode") String PlanCode, @Param("RiskCode") String RiskCode);

    int updateByPrimaryKeySelective(FcDailyInsureRiskDetailInfo record);


    /**
     * 查询基础单配置的计划信息
     *
     * @param ensureCode
     * @return
     */
    List<Map<String, String>> selectDailyPlans(String ensureCode);

    /**
     * 根据 福利编号和计划编号，查询出一条语句。如果查出来多条说明数据有问题
     *
     * @param EnsureCode
     * @param PlanCode
     * @return
     */
    FcDailyInsureRiskDetailInfo selectBycodeandPlanCode(@Param("EnsureCode") String EnsureCode, @Param("PlanCode") String PlanCode);
}
