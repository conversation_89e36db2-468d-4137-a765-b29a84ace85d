package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FPInsurePlan;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FPInsurePlanMapper {
    int deleteByPrimaryKey(String insurePlanNo);

    int insert(FPInsurePlan record);

    int insertSelective(FPInsurePlan record);

    FPInsurePlan selectByPrimaryKey(String insurePlanNo);

    int updateByPrimaryKeySelective(FPInsurePlan record);

    List<FPInsurePlan> selectFPInsurePlanPerNo(Map<String, String> map);

    List<String> selectPlanList(Map<String, Object> params);

    List<Map<String, String>> selectPlanCodeByPersonId(Map<String, Object> params);

    List<Map<String, String>> getPlanCodeByPersonId(Map<String, Object> params);

    /**
     * 判断员工是否已经投保
     * 
     * @param perNo
     * @return
     */
    Integer selectStaffInsured(String perNo);

    /**
     * 判断家属是否已经投保
     * 
     * @param perNo
     * @param personId
     * @return
     */
    Integer selectStaffFamilyInsured(@Param("perNo") String perNo, @Param("personId") String personId);

    /**
     * 保存个人计划表
     * @param insurePlan
     */
    void insertSubtables(FPInsurePlan insurePlan);

    List<FPInsurePlan> selectFPInsurePlans(Map<String, Object> params);

    List<FPInsurePlan> selectFlansInfo(Map<String, Object> map);

    int deleteInsurePlan(FPInsurePlan insurePlan);

    List<FPInsurePlan> selectEnsureCodeByPersonId(Map<String, Object> map);

    int updateByPrimaryKey(FPInsurePlan record);

    List<FPInsurePlan> selectFPInsurePlansByState(Map<String, Object> params);

    FPInsurePlan selectByEnsureCodeAndPersonId(@Param("personID") String personID, @Param("ensureCode")String ensureCode);
}