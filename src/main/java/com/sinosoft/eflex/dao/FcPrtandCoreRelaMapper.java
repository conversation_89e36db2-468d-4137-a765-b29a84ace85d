package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FcPrtandCoreRela;
import org.springframework.stereotype.Repository;

@Repository
public interface FcPrtandCoreRelaMapper {
    int deleteByPrimaryKey(String relaSn);

    int insert(FcPrtandCoreRela record);

    int insertSelective(FcPrtandCoreRela record);

    FcPrtandCoreRela selectByPrimaryKey(String relaSn);

    int updateByPrimaryKeySelective(FcPrtandCoreRela record);

    int updateByPrimaryKey(FcPrtandCoreRela record);

    FcPrtandCoreRela selectCoreReturnMsg(String ensureCode);

    FcPrtandCoreRela selectByTPrtNo(String tPrtNo);

    FcPrtandCoreRela selectByPrtNo(String PrtNo);
}