package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FcDutyGradeOptionalAmountInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FcDutyGradeOptionalAmountInfoMapper {
    int deleteByPrimaryKey(@Param("amountGrageCode") String amountGrageCode, @Param("optDutyCode") String optDutyCode);

    int insert(FcDutyGradeOptionalAmountInfo record);

    int insertSelective(FcDutyGradeOptionalAmountInfo record);

    List<FcDutyGradeOptionalAmountInfo> selectByPrimaryKey(Map<String,String> map);
    
    List<String> selectOptDutyCodeList(String AmountGrageCode); 

    int updateByPrimaryKeySelective(FcDutyGradeOptionalAmountInfo record);

    int updateByPrimaryKey(FcDutyGradeOptionalAmountInfo record);

    int deleteByEnsureCode(Map<String,String> map);

    int insertOptDutyList(@Param("list") List<FcDutyGradeOptionalAmountInfo> list);

    int updateOptDutyList(@Param("list") List<FcDutyGradeOptionalAmountInfo> list);
}