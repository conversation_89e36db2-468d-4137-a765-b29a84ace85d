package com.sinosoft.eflex.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FCEnsurePlan;

@Repository
public interface FCEnsurePlanMapper {
    int deleteByPrimaryKey(String planCode);

    int insert(FCEnsurePlan record);

    int insertSelective(FCEnsurePlan record);

    FCEnsurePlan selectByPrimaryKey(Map<String, Object> paramOne);

    /**
     * 根据福利编号查询
     *
     * @param ensureCode
     * @return
     */
    ArrayList<FCEnsurePlan> selectByEnsureCode(@Param("ensureCode") String ensureCode);

    int updateByPrimaryKeySelective(FCEnsurePlan record);

    int updateByPrimaryKey(FCEnsurePlan record);


    List<FCEnsurePlan> selectFCEnsurePlans(Map<String, Object> params);

    List<FCEnsurePlan> selectAllFCEnsurePlans(Map<String, Object> params);

    /**
     * 投保清单导出excel
     *
     * @param planCode
     * @return
     */
    FCEnsurePlan selectInsuredDetail(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode,
                                     @Param("planName") String planName);

    /**
     * 根据计划编号查询计划详情
     *
     * @param planCode
     * @return
     */
    FCEnsurePlan selectPlanDetail(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode);

    /**
     * 查询方案的保费
     *
     * @param ensureCode
     * @param planCode
     * @return
     */
    double selectPlanPrem(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode);

    int deleteByEnsureCode(String ensureCode);

    List<FCEnsurePlan> selectEnsureCodeByplanObject(@Param("ensureCode") String ensureCode,
                                                    @Param("planObject") String planObject);

    /**
     * 查询转码后的计划列表
     *
     * @param params
     * @return
     */
    List<FCEnsurePlan> selectTranscodingFCEnsurePlans(Map<String, Object> params);

    /**
     * 查询计划编码是否存在
     *
     * @param params
     * @return
     */
    int existdefultPlanCode(Map<String, Object> params);

    List<String> existListdefultPlanCode(Map<String, String> map);

    List<FCEnsurePlan> selectPlanInfo(@Param("ensureCode") String ensureCode, @Param("planObject") String planObject);

    FCEnsurePlan selectByKey(Map<String, String> param);

    void insertList(List<FCEnsurePlan> fcEnsurePlanList);

    /**
     * 查询是否存在学生投保的计划
     *
     * @param list
     * @return
     */
    Integer selectCountStudentEnsure(List<String> list);

    String getSumTotalPrem(@Param("perNo") String perNo, @Param("ensureCode") String ensureCode);

    /**
     * 根据ensureCode和planCode删除福利下的某条计划
     *
     * @param ensureCode
     * @param planCode
     * @return
     */
    int deleteByEnsureCodeAndPlanCode(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode);

    /**
     * 删除定制中的无效福利计划
     *
     * @param ensureCode
     * @return
     */
    int deleteMakeingPlanByEnsureCode(String ensureCode);

    /**
     * 查询福利下投保类型
     */
    List<String> selectPlanObject(String ensureCode);

    /**
     * 计划书ID替换福利编号
     *
     * @param jEnsureCode
     * @param ensureCode
     */
    void updateByEnsureCode(@Param("jEnsureCode") String jEnsureCode, @Param("ensureCode") String ensureCode);
}