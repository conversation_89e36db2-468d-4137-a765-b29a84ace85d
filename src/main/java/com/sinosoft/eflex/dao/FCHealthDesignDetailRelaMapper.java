package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCHealthDesignDetailRela;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FCHealthDesignDetailRelaMapper {
    int deleteByPrimaryKey(@Param("designNo") String designNo, @Param("healthDesignNo") String healthDesignNo);

    int insert(FCHealthDesignDetailRela record);

    int insertSelective(FCHealthDesignDetailRela record);

    FCHealthDesignDetailRela selectByPrimaryKey(@Param("designNo") String designNo, @Param("healthDesignNo") String healthDesignNo);

    int updateByPrimaryKeySelective(FCHealthDesignDetailRela record);

    int updateByPrimaryKey(FCHealthDesignDetailRela record);

    List<FCHealthDesignDetailRela> selectByDesignNo(String designNo);

    void insertList(List<FCHealthDesignDetailRela> fcHealthDesignDetailRelaList);

    int deleteAllBydesign(String designNo);
}