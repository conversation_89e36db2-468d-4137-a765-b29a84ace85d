package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FDRiskDeductible;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FDRiskDeductibleMapper {
    int deleteByPrimaryKey(@Param("riskCode") String riskCode, @Param("deductible") Double deductible);

    int insert(FDRiskDeductible record);

    int insertSelective(FDRiskDeductible record);

    List<FDRiskDeductible> selectByPrimaryKey(Map<String,String> map);

    int updateByPrimaryKeySelective(FDRiskDeductible record);

    int updateByPrimaryKey(FDRiskDeductible record);
}