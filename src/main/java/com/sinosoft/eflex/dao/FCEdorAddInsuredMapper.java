package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.EdorAddInsuredInfo;
import com.sinosoft.eflex.model.FCEdorAddInsured;
import com.sinosoft.eflex.model.edor.SelectfcedoraddinsuredReq;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface FCEdorAddInsuredMapper {
    int deleteByPrimaryKey(String plusInsuredSN);

    int insert(FCEdorAddInsured record);

    int insertSelective(FCEdorAddInsured record);

    FCEdorAddInsured selectByPrimaryKey(String plusInsuredSN);

    int updateByPrimaryKeySelective(FCEdorAddInsured record);

    int updateByPrimaryKey(FCEdorAddInsured record);


    int insertNext(List<FCEdorAddInsured> list);

    int selectInsuredCount(Map<String, Object> insuredNumMap);

    List<EdorAddInsuredInfo> getInsuredExist(String batch);

    /**
     * 查询增人信息列表
     *
     * @param selectfcedoraddinsuredReq
     * @return
     */
    List<EdorAddInsuredInfo> getInsuredExist1(SelectfcedoraddinsuredReq selectfcedoraddinsuredReq);

    List<FCEdorAddInsured> getAddInsuredInfo(Map<String, Object> insuredMap);

    int deleteAddinsured(String batch);

    int checkIdNoIsExists(@Param("params") Map<String, String> map);

    int checkIdNoIsExists2(@Param("batch") String batch, @Param("list") List<HashMap<String, String>> listEdorAddInsured);

    List<FCEdorAddInsured> checkOtherIsEsists(@Param("batch") String batch, @Param("list") List<HashMap<String, String>> listPerIfo);

    int insertfcEdorAddInsured(List<FCEdorAddInsured> edorAddInsuredlist);

    int updatefcEdorAddInsured(FCEdorAddInsured fcEdorAddInsured);

    int deleteEdorAddInsured(String plusInsuredSN);

    int deleteEdorAddInsuredList(List<String> plusInsuredSNList);

    int checkOneIdNoIsExists(FCEdorAddInsured fcEdorAddInsured);

    int checkOneIdNoIsExistsUpdate(FCEdorAddInsured fcEdorAddInsured);

    int checkOneOtherIsEsists(@Param("params") Map<String, String> map);

    int selectByPhone(FCEdorAddInsured fcEdorAddInsured);

    int selectByPhoneUpdate(FCEdorAddInsured fcEdorAddInsured);

    /**
     * 查询同批次的主被保险人的信息
     *
     * @param mainEdorAddInsured
     * @return
     */
    FCEdorAddInsured selectMainEdorInsured(FCEdorAddInsured mainEdorAddInsured);

    /**
     * 查询同批次所属主被保险人的家属信息
     *
     * @param mainEdorAddInsured
     * @return
     */
    List<FCEdorAddInsured> selectSubEdorInsured(FCEdorAddInsured mainEdorAddInsured);

    /**
     * 获取同批次保全增人信息
     *
     * @param edorAddInsured
     * @return
     */
    List<FCEdorAddInsured> selectEdorAddInsuredLst(FCEdorAddInsured edorAddInsured);

    /**
     * 查询保全增人信息
     *
     * @param edorAddInsured
     * @return
     */
    List<FCEdorAddInsured> selectEdorAddInsured(FCEdorAddInsured edorAddInsured);

    /**
     * 查询保全增人信息
     *
     * @param edorAddInsured
     * @return
     */
    List<FCEdorAddInsured> selectEdorAddInsuredForUpdate(FCEdorAddInsured edorAddInsured);

    /**
     * 保费试算完成后的数据更新
     */
    int updateEdorAddInsured(FCEdorAddInsured record);

    /**
     * 查询保全批次中错误信息的人数
     */
    int selectErrorEdorAddInsured(@Param("grpContNo") String grpContNo, @Param("batch") String batch);

}