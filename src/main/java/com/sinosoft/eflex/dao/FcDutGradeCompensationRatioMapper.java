package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FcDutGradeCompensationRatio;
import com.sinosoft.eflex.model.FcDutyGroupDeductible;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FcDutGradeCompensationRatioMapper {
    int deleteByPrimaryKey(@Param("amountGrageCode") String amountGrageCode, @Param("compensationRatio") Double compensationRatio);

    int insert(FcDutGradeCompensationRatio record);

    int insertSelective(FcDutGradeCompensationRatio record);

    List<FcDutGradeCompensationRatio> selectByPrimaryKey(Map<String,String> map);

    int updateByPrimaryKeySelective(FcDutGradeCompensationRatio record);

    int updateByPrimaryKey(FcDutGradeCompensationRatio record);

    int deleteByEnsureCode(Map<String,String> map);

    int insertDutyRatioList(@Param("list")List<FcDutGradeCompensationRatio> list);
}