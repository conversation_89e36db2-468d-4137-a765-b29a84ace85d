package com.sinosoft.eflex.dao;

import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FCUserToken;
import org.springframework.stereotype.Repository;

@Repository
public interface FCUserTokenMapper {
    int deleteByPrimaryKey(String token);

    int insert(FCUserToken record);

    int insertSelective(FCUserToken record);

    FCUserToken selectByPrimaryKey(String token);

    int updateByPrimaryKeySelective(FCUserToken record);

    int updateByPrimaryKey(FCUserToken record);
}