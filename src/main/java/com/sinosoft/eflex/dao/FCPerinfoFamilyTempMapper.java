package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCPerinfoFamilyTemp;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface FCPerinfoFamilyTempMapper {
    int deleteByPrimaryKey(HashMap<String, String> map);

    int insert(FCPerinfoFamilyTemp record);

    int insertSelective(FCPerinfoFamilyTemp record);

    FCPerinfoFamilyTemp selectByPrimaryKey(String familyTempNo);

    int updateByPrimaryKeySelective(FCPerinfoFamilyTemp record);

    int updateByPrimaryKey(FCPerinfoFamilyTemp record);

    int insertList(List<FCPerinfoFamilyTemp> list);

    List<FCPerinfoFamilyTemp> getFamilyByEnsureCode(Map<String, Object> map);

    Integer getFamilyCountByEnsureCode(FCPerinfoFamilyTemp fcPerinfoFamilyTemp);

    int updateSubStaus(@Param("params") Map<String, String> map);

    List<Map<String, String>> queryStudent(Map<String, String> map);

    List<Map<String, String>> getPlanByEnsureCode(String ensureCode);

    List<FCPerinfoFamilyTemp> selectByFamilyTemp(FCPerinfoFamilyTemp fcPerinfoFamilyTemp);

    List<Map<String, String>> getPerInfoByEnsureCode(Map<String, String> map);

    /**
     * 根据员工客户号删除家属
     *
     * @param perTempNo
     */
    void deleteByPerTempNo(String perTempNo);

    List<FCPerinfoFamilyTemp> selectByIdNo(@Param("idNo") String idNo);

    void updateByIdNo(Map<String, Object> params);

    void updateBatchById(@Param("list") List<FCPerinfoFamilyTemp> fcPerinfoFamilyTempList);

    FCPerinfoFamilyTemp selectByIdNoAndEnsureCode(@Param("ensureCode") String ensureCode, @Param("idNo") String idNo);

    List<FCPerinfoFamilyTemp> selectByEnsureCode(String ensureCode);
}