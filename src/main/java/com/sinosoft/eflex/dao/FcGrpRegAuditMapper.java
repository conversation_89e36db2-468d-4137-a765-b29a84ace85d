package com.sinosoft.eflex.dao;

import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.sinosoft.eflex.model.FcGrpRegAudit;
import com.sinosoft.eflex.model.HrRegist;
import org.springframework.stereotype.Repository;

@Repository
public interface FcGrpRegAuditMapper {
    int deleteByPrimaryKey(String grpRegNo);

    int insert(FcGrpRegAudit record);

    int insertSelective(FcGrpRegAudit record);

    FcGrpRegAudit selectByPrimaryKey(String grpRegNo);

    int updateByPrimaryKeySelective(FcGrpRegAudit record);

    int updateByPrimaryKey(FcGrpRegAudit record);

    FcGrpRegAudit selectAuditResult(String grpNo);

    int  updateAuditResult(FcGrpRegAudit regAudit);

    void  updateFCHrRegistTemp(@Param("params")Map<String,String> map);

    HrRegist selectTranscodingAuditResult(String registSN);


}