package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCOrderBnf;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FCOrderBnfMapper {
    int deleteByPrimaryKey(String bnfNo);

    int insert(FCOrderBnf record);

    int insertSelective(FCOrderBnf record);

    FCOrderBnf selectByPrimaryKey(String bnfNo);

    int updateByPrimaryKeySelective(FCOrderBnf record);

    int updateByPrimaryKey(FCOrderBnf record);

    /**
     * 查询受益人信息
     * @param orderItemNo
     * @return
     */
    List<Map<String, String>> selectBnfInfos(String orderItemNo);
    List<Map<String, String>> selectBnfInfos2(String orderItemNo);//就是把受益人得信息得 100%  改了

    /**
     * 新增 受益人信息，包括字段 zipCode
     * @param record
     * @return
     */
    int insertSelectiveAll(FCOrderBnf record);

    /**
     * 修改 受益人信息（包括 字段zipCode）
     * @param record
     * @return
     */
    int updateByPrimaryKeySelectiveAll(FCOrderBnf record);

}