package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FDFTPInfo;
import org.springframework.stereotype.Repository;

@Repository
public interface FDFTPInfoMapper {
    int deleteByPrimaryKey(String ftpinfosn);

    int insert(FDFTPInfo record);

    int insertSelective(FDFTPInfo record);

    FDFTPInfo selectByPrimaryKey(String ftpinfosn);

    int updateByPrimaryKeySelective(FDFTPInfo record);

    int updateByPrimaryKey(FDFTPInfo record);
}