package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FPInsureEflexPlanOptional;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface FPInsureEflexPlanOptionalMapper {
    int deleteByPrimaryKey(@Param("insureElfexPlanNo") String insureElfexPlanNo, @Param("amountGrageCode") String amountGrageCode, @Param("optDutyCode") String optDutyCode);
    
    int deletefPInsureEflexPlanOptional(@Param("personId") String personId,@Param("perNo") String perNo,@Param("ensureCode") String ensureCode);
    
    int deletefPfPEflexCheckRuleEflexPlanOptional(@Param("personId") String personId,@Param("perNo") String perNo,@Param("ensureCode") String ensureCode);

    int insert(FPInsureEflexPlanOptional record);
    
    int updateInsureState(Map<String, Object> params);

    int insertSelective(FPInsureEflexPlanOptional record);
    
    int insertfPInsureEflexPlanOptional(List<FPInsureEflexPlanOptional> fPInsureEflexPlanOptionalList);
    
    int insertfPfPEflexCheckRuleEflexPlanOptional(List<FPInsureEflexPlanOptional> fPInsureEflexPlanOptionalList);

    FPInsureEflexPlanOptional selectByPrimaryKey(@Param("insureElfexPlanNo") String insureElfexPlanNo, @Param("amountGrageCode") String amountGrageCode, @Param("optDutyCode") String optDutyCode);

    int updateByPrimaryKeySelective(FPInsureEflexPlanOptional record);

    int updateByPrimaryKey(FPInsureEflexPlanOptional record);

}