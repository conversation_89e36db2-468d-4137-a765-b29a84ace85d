package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FDImpartInfopersonal;

public interface FDImpartInfopersonalMapper {
    int deleteByPrimaryKey(String impartCode);

    int insert(FDImpartInfopersonal record);

    int insertSelective(FDImpartInfopersonal record);

    FDImpartInfopersonal selectByPrimaryKey(String impartCode);

    int updateByPrimaryKeySelective(FDImpartInfopersonal record);

    int updateByPrimaryKey(FDImpartInfopersonal record);
}