package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCGrpOrder;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface FCGrpOrderMapper {

    int deleteByPrimaryKey(String grpOrderNo);

	int insert(FCGrpOrder record);
	
	int insertFcPrtAndCoreRela(Map<String, String> map);

	int updateFcPrtAndCoreRela(@Param("params")Map<String, String> map);

	int insertSelective(FCGrpOrder record);

	FCGrpOrder selectByPrimaryKey(String grpOrderNo);

	int updateByPrimaryKeySelective(FCGrpOrder record);

	int updateByPrimaryKey(FCGrpOrder record);

	FCGrpOrder selectPrtNo(String prtNo);
	FCGrpOrder selectPrtNo_Daily(String prtNo);

	/**
	 * 投保清单导出excel
	 */
	FCGrpOrder selectInsuredDetail(Map<String,String> resultMap);
	
	List<Map<String,String>> getPayType(@Param("grpNo") String grpNo, @Param("ensureCode") String ensureCode);

	List<HashMap<String,Object>> selectGrpOrder(String sysDate);

    FCGrpOrder selectGrpOrderByEnsureCode(String ensureCode);

    FCGrpOrder selectGrpOrderByGrpContNo(@Param("grpContNo") String grpContNo);

    FCGrpOrder selectGrpOrderByEnsureCodeAndManageCom(@Param("ensureCode") String ensureCode,
            @Param("manageCom") String manageCom);

	Map<String,Object> selectGrpOrderNo(Map<String,Object> map);

	List<Map<String,Object>> selectGrpOrderNoList(Map<String,Object> map);

	List<Map<String,Object>> selectGrpPrem(@Param("ensureCode") String ensureCode,@Param("grpNo") String grpNo);

	Map<String,Object> selectGrpOrderInfo(Map<String,Object> map);

	int deleteByEnsureCode(String ensureCode);

	FCGrpOrder selectByGrpContNo(Map<String,Object> map);

	Map<String,String> getFcPrtAndCoreRelaByRelaSn(String relaSn);

	/**
	 * 根据平台投保单号查询核心投保单号  状态为 承保成功
	 * @param prtNo
	 * @return
     */
    String selectbyPrtNo(String prtNo);

    /**
     * 根据平台投保单号查询核心投保单号  状态为 待发送
     *
     * @param prtNo
     * @return
     */
    String selectbyPrtNo06(String prtNo);

    int insertFcPrtAndCoreRela06(Map<String, String> map);

    List<FCGrpOrder> selectGrpOrderByGrpNo(@Param("grpNo") String grpNo);

    FCGrpOrder queryGrpByGrpOrderNo(String grpOrderNo);

	FCGrpOrder selectByEnsureCodeKey(String ensureCode);

	List<String> selectGrpContNo();

}