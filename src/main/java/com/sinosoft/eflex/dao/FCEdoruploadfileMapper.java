package com.sinosoft.eflex.dao;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FCEdoruploadfile;

@Repository
public interface FCEdoruploadfileMapper {
    int insert(FCEdoruploadfile record);

    int insertSelective(FCEdoruploadfile record);
    
    int deleteUploadfile(Map<String,Object> map);
    
    List<FCEdoruploadfile> selectUploadfileByGrpContNo(Map<String,Object> map);
}