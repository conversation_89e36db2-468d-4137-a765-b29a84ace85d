package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCGrpInfo;
import com.sinosoft.eflex.model.FcGrpContact;
import com.sinosoft.eflex.model.FcGrpRegAudit;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ContactAndInfoMapper {
     List<FcGrpContact> findContactInfo(@Param("grpNo") String grpNo);
     List<FCGrpInfo> findGrpInfo(@Param("grpNo") String grpNo);
}
