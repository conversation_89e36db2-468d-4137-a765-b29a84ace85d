package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCGrpInfo;
import com.sinosoft.eflex.model.FCGrpInfoExample;
import com.sinosoft.eflex.model.FCgrplistInfo;
import com.sinosoft.eflex.model.HrRegist;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FCGrpInfoMapper {
    int deleteByPrimaryKey(String grpNo);

    int insert(FCGrpInfo record);

    int insertSelective(FCGrpInfo record);

    FCGrpInfo selectByPrimaryKey(String grpNo);

    int updateByExampleSelective(@Param("record") FCGrpInfo record, @Param("example") FCGrpInfoExample example);

    int updateByExample(@Param("record") FCGrpInfo record, @Param("example") FCGrpInfoExample example);

    int updateByPrimaryKeySelective(FCGrpInfo record);

    int updateByPrimaryKey(FCGrpInfo record);

    FCGrpInfo selectGrpInfo(String unifiedsociCode);

    //查询转码后的数据
    FCGrpInfo selectTranscodingGrpInfo(String grpNo);

    //查询企业信息
    List<FCGrpInfo> selectGrpInfoList(Map<String, Object> params);

    List<HrRegist> selectGrpInfoListTemp(Map<String, Object> params);

    //根据HR信息查询企业信息列表
    List<FCgrplistInfo> getGrpInfoByContactNo(String contactNo);

    List<FCGrpInfo> getGrpListByNameOrCode(Map<String, String> map);

    List<Map<String, String>> getImageInfo(String grpNo);

    String selectGrpNoByNameAndCode(@Param("GrpName") String GrpName, @Param("UnifiedsociCode") String UnifiedsociCode);

    int updateclientNoBygrpNo(Map<String, String> map);

    int updateImagePath(@Param(value = "list") List<FCGrpInfo> list);

    int updateHrImagePath(@Param(value = "list") List<HrRegist> list);

    List<String> selectPlanByUnifiedsociCode(@Param("UnifiedsociCode") String unifiedsociCode);

    FCGrpInfo selectByEnsureCode(String ensureCode);

    List<HrRegist> getAllGrpHrInfo(@Param("unifiedsociCode") String unifiedsociCode, @Param("grpIdNo") String grpIdNo);

    List<FCGrpInfo> getAllGrpInfo(@Param("grpNo") String grpNo);

    List<Map<String, String>> getGrpListBygrpName(@Param("grpName") String grpName);

    /**
     * 查询企业的全部信息
     */
    FCGrpInfo selectGrpInfo1(String grpNo);

    FCGrpInfo selectGrpInfo2(String grpNo);

    List<FCGrpInfo> selectGrpInfoImageNull();
}