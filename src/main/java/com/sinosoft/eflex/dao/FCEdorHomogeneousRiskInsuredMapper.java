package com.sinosoft.eflex.dao;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FCEdorHomogeneousRiskInsured;
import com.sinosoft.eflex.model.FCEdorHomogeneousRiskInsuredInfo;
import com.sinosoft.eflex.model.edor.SelectEdorHomogeneousRiskInsuredReq;

@Repository
public interface FCEdorHomogeneousRiskInsuredMapper {
    int deleteByPrimaryKey(String homogeneousRiskInsuredSN);

    int insert(FCEdorHomogeneousRiskInsured record);

    int insertSelective(FCEdorHomogeneousRiskInsured record);

    FCEdorHomogeneousRiskInsured selectByPrimaryKey(String homogeneousRiskInsuredSN);

    int updateByPrimaryKeySelective(FCEdorHomogeneousRiskInsured record);

    int updateByPrimaryKey(FCEdorHomogeneousRiskInsured record);

    int selectInsuredCount(Map<String,Object> insuredNumMap);
    
    int deleteHomogeneousRiskInsured(String batch);
    
    int insertNext(List<FCEdorHomogeneousRiskInsured> list);
    
    List<FCEdorHomogeneousRiskInsured> getHomogeneousRiskInsuredInfo(FCEdorHomogeneousRiskInsured fcEdorHomogeneousRiskInsured);

    /**
     * 增加码值转换的操作
     *
     * @param selectEdorHomogeneousRiskInsuredReq
     * @return
     */
    List<FCEdorHomogeneousRiskInsuredInfo> selectEdorHomogeneousRiskInsured(
            SelectEdorHomogeneousRiskInsuredReq selectEdorHomogeneousRiskInsuredReq);

}