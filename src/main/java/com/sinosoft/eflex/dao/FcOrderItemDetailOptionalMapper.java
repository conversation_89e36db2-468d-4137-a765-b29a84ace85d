package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FcOrderItemDetailOptional;
import org.apache.ibatis.annotations.Param;

public interface FcOrderItemDetailOptionalMapper {
    int deleteByPrimaryKey(@Param("orderItemDetailNo") String orderItemDetailNo, @Param("amountGrageCode") String amountGrageCode, @Param("optDutyCode") String optDutyCode);

    int insert(FcOrderItemDetailOptional record);

    int insertSelective(FcOrderItemDetailOptional record);

    FcOrderItemDetailOptional selectByPrimaryKey(@Param("orderItemDetailNo") String orderItemDetailNo, @Param("amountGrageCode") String amountGrageCode, @Param("optDutyCode") String optDutyCode);

    int updateByPrimaryKeySelective(FcOrderItemDetailOptional record);

    int updateByPrimaryKey(FcOrderItemDetailOptional record);
}