package com.sinosoft.eflex.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FDRiskInfo;
import com.sinosoft.eflex.model.FdRIskPlanInfo;

@Repository
public interface FDRiskInfoMapper {
    int deleteByPrimaryKey(String riskCode);

    int insert(FDRiskInfo record);

    int insertSelective(FDRiskInfo record);

    FDRiskInfo selectByPrimaryKey(String riskCode);

    int updateByPrimaryKeySelective(FDRiskInfo record);

    int updateByPrimaryKey(FDRiskInfo record);
    
    String selectRiskNameByRiskCode(String riskCode);

    List<FDRiskInfo> selectByRiskType(String riskType);

    List<FDRiskInfo>  selectRiskInfo();

    List<FDRiskInfo> selectByEnsureCode(Map<String,String> map);

    List<FDRiskInfo> selectByEnsureCodeAndPlanCode(@Param("ensureCode") String ensureCode,
            @Param("planCode") String planCode, @Param("list") List<String> noShortRiskCodeList);

    //得到10个险种编码
    List<FDRiskInfo> selectRiskCode();

    List<Map<String, String>> selectRiskCodeByPlanType(String planType);

    List<FdRIskPlanInfo> selectByRiskCode(String riskCode);

    List<Map<String, String>> selectRiskNameAndRiskCodeByEnsureCode(String ensureCode);

    /**
     * 查询停售的险种信息
     * @return
     */
    List<String> selectStopSaleRiskCode();

}