package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FcInsureEflexPlanOptional;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface FcInsureEflexPlanOptionalMapper {
    int deleteByPrimaryKey(@Param("orderItemDetailNo") String orderItemDetailNo, @Param("amountGrageCode") String amountGrageCode, @Param("optDutyCode") String optDutyCode);

    int insert(FcInsureEflexPlanOptional record);

    int insertSelective(FcInsureEflexPlanOptional record);

    FcInsureEflexPlanOptional selectByPrimaryKey(@Param("orderItemDetailNo") String orderItemDetailNo, @Param("amountGrageCode") String amountGrageCode, @Param("optDutyCode") String optDutyCode);

    int updateByPrimaryKeySelective(FcInsureEflexPlanOptional record);

    int updateByPrimaryKey(FcInsureEflexPlanOptional record);

    int deleteByOrderNo(String orderNo);
}