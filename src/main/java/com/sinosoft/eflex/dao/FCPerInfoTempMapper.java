package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCPerInfoTemp;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface FCPerInfoTempMapper {

    int checkIdNoIsExists(@Param("list") List<HashMap<String, String>> listPerIfo);

    int checkIdNoIsExistsTemp(@Param("grpNo") String grpNo, @Param("list") List<HashMap<String, String>> listPerIfo);

    int updateCheckIdNoIsExistsTemp(Map<String, String> map);

    List<FCPerInfoTemp> updateCheckOtherIsEsistsTemp(Map<String, String> map);

    List<FCPerInfoTemp> checkOtherIsEsists(@Param("list") List<HashMap<String, String>> listPerIfo);

    List<FCPerInfoTemp> checkOtherIsEsistsTemp(@Param("grpNo") String grpNo, @Param("list") List<HashMap<String, String>> listPerIfo);

    List<FCPerInfoTemp> checkTempIdNoIsExists(@Param("grpNo") String grpNo, @Param("list") List<HashMap<String, String>> listPerIfo);

    int insertFcPerInfo(List<HashMap<String, String>> listPerIfo);

    int insertFcPerSon(List<HashMap<String, String>> listPerIfo);

    int updateFcPerInfo(@Param("params") Map<String, String> map);

    int updateFcPerSon(@Param("params") Map<String, String> map);

    int updateFCPerRegistDay(@Param("params") Map<String, String> map);

    int updatetFcensureconfig(@Param("params") Map<String, String> map);

    int checkOneIdNoIsExists(@Param("idNo") String idNo);

    void deleteFcPerInfoTemp(String perNo);

    void deleteAllFcPerInfoTemp(String ensureCode);

    void insertFcplaninform(@Param("params") Map<String, String> map);

    int checkTempOneIdNoIsExists(@Param("params") Map<String, String> map);

    int checkTempOneNIdNoIsExists(@Param("params") Map<String, String> map);

    int checkOneOtherIsEsists(@Param("params") Map<String, String> map);

    int updateFdUser(@Param("params") Map<String, String> map);

    int updateOneFcPerInfoTemp(FCPerInfoTemp fcPerInfoTemp);

    int updateFCDefaultPlan(@Param("params") Map<String, String> map);

    int insertFCStaffFamilyRela(List<HashMap<String, String>> listPerIfo);

    int insertFCDefaultPlan(List<HashMap<String, String>> listPerIfo);

    int insertFdUser(List<HashMap<String, String>> listPerIfo);

    int insertFdUserRole(List<HashMap<String, String>> listPerIfo);

    int insertFdPwdHist(List<HashMap<String, String>> listPerIfo);

    int insertFDusertomenugrp(List<HashMap<String, String>> listPerIfo);

    int insertFCPerRegistDay(List<HashMap<String, String>> listPerIfo);

    int insert(List<FCPerInfoTemp> perInfoTemplist);

    List<FCPerInfoTemp> getNeedSyncNum(String ensureCode);

    List<Map<String, String>> getNeedSyncGarNum(String ensureCode);

    List<Map<String, String>> selectOccupationList(String grpNo);

    List<Map<String, String>> selectOccupationCode(String codeKey);

    int updateFCPerinfoTemp(@Param("params") Map<String, String> map);

    List<String> getIdTypeList(String str);

    List<String> getIdTypeCodeList(String str);

    List<String> getOccupationTypeList(String str);

    List<String> getOccupationTypeList();

    List<String> getOccupationCodeList(String str);

    List<String> getOccupationCodeList();

    List<String> getOpenBankList(String str);

    int selectHas(String ensureCode);

    FCPerInfoTemp selectFcPerInfoTemp(String perNo);

    Integer checkOneOtherIsEsistsTemp(Map<String, String> map);

    List<Map<String, String>> getPlanByEnsureCode(String ensureCode);

    List<FCPerInfoTemp> getPerInfoByEnsureCode(Map<String, String> map);

    @Select("select count(1) from FCPerInfoTemp where mobilephone = #{phone} and perTempNo != #{perTempNo} and idno != #{idno} and MakeDate >= CURDATE() - INTERVAL 3 YEAR")
    int selectByPhone(@Param("phone") String phone, @Param("perTempNo") String perTempNo,
                      @Param("idno") String idno, @Param("idtype") String idtype);

    /**
     * 查询企业下所有的员工职级
     *
     * @param grpNo
     * @return
     */
    List<String> selectLevelCodeByGrpNo(String grpNo);

    /**
     * 查询某个固定职级
     *
     * @param ensureCode,idno
     * @return
     */
    String selectLevelCodeByEnsureCodeAndIdNo(@Param("ensureCode") String ensureCode, @Param("idNo") String idNo);

    /**
     * 根据企业号和其他参数查询员工信息
     *
     * @param params
     * @return
     */
    List<Map<String, String>> selectByGrpNoAndParams(Map<String, String> params);

    /**
     * 根据企业号和其他参数查询员工（临时表）信息
     *
     * @param params
     * @return
     */
    List<Map<String, String>> selectTempByGrpNo(Map<String, String> params);

    /**
     * 根据企业号删除员工信息（ensureCode为空的）
     *
     * @param grpNo
     */
    void deleteByGrpNoAndEnsureCode(String grpNo);

    /**
     * 根据企业号查询员工
     *
     * @param grpNo
     * @return
     */
    List<FCPerInfoTemp> selectByGrpNo(String grpNo);

    List<FCPerInfoTemp> selectByMobilePhone(String mobilPhone);

    int updateByPrimaryKeySelective(FCPerInfoTemp record);

    int insertSelective(FCPerInfoTemp record);

    /**
     * 根据证件号查询当前企业 临时表中所有的员工信息
     *
     * @param grpNo
     * @param idNo
     * @return
     */
    List<FCPerInfoTemp> selectByGrpNoIdNo(@Param("grpNo") String grpNo, @Param("idNo") String idNo, @Param("perTempNo") String perTempNo);

    /**
     * 查询证件号相同的所有员工
     *
     * @param idNo
     * @return
     */
    List<FCPerInfoTemp> selectByIdNo(String idNo);

    /**
     * 校验手机号重复
     *
     * @param mobilePhone
     * @param perTempNo
     * @param iDNo
     * @return
     */
    @Select("select count(1) from FCPerInfoTemp where mobilephone = #{mobilePhone} and perTempNo != #{perTempNo} and idno != #{idno} and MakeDate >= CURDATE() - INTERVAL 3 YEAR")
    int selectRepeatPhone(@Param("mobilePhone") String mobilePhone, @Param("perTempNo") String perTempNo, @Param("idno") String iDNo);

    List<FCPerInfoTemp> selectByEnsureCode(String ensureCode);

    void updateByIdNo(Map<String, Object> params);

    void updateByensureCode(Map<String, Object> paraMap);

    void updateBatchById(@Param("list") List<FCPerInfoTemp> fcPerInfoTempList);

    FCPerInfoTemp selectLevelCodeByEnsureCodeAndIdNo2(@Param("ensureCode") String ensureCode, @Param("idNo") String idNo);

    FCPerInfoTemp selectByEnsureCodeAndIdNo(@Param("ensureCode") String ensureCode, @Param("idNo") String idNo);
}