package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCPerAppnt;
import org.springframework.stereotype.Repository;

@Repository
public interface FCPerAppntMapper {
    int deleteByPrimaryKey(String perAppNo);

    int insert(FCPerAppnt record);

    int insertSelective(FCPerAppnt record);

    FCPerAppnt selectByPrimaryKey(String perAppNo);

    int updateByPrimaryKeySelective(FCPerAppnt record);

    int updateByPrimaryKey(FCPerAppnt record);

    int deleteByKey(String perNo);
}