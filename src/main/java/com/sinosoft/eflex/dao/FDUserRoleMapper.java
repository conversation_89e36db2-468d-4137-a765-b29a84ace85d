package com.sinosoft.eflex.dao;

import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FDUserRole;

@Repository
public interface FDUserRoleMapper {
    int deleteByPrimaryKey(String userRoleSN);

    int insert(FDUserRole record);

    int insertSelective(FDUserRole record);

    FDUserRole selectByPrimaryKey(String userRoleSN);

    int updateByPrimaryKeySelective(FDUserRole record);

    int updateByPrimaryKey(FDUserRole record);

    int deleteByUserNo(String userNo);

}