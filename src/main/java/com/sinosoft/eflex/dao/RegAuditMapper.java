package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.RegistRegFind;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

@Repository
public interface RegAuditMapper {
    /**
     * <AUTHOR>
     * @description 注册信息以列表形式显示
     * @date 18:00 18:00
     * @modified
     */
    List<HashMap<String,Object>> findPageInfo(RegistRegFind registRegFind);

    List<String> selectAllhrName();

    List<String> selectAllgrpName();
}
