package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCPerInfo;
import com.sinosoft.eflex.model.FCPerInfoTemp;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FCPerInfoMapper {

    List<Map<String,String>> queryUnInsuredList(Map<String,String> map);

    int deleteByPrimaryKey(String perNo);

    int insert(FCPerInfo record);

    int insertSelective(FCPerInfo record);

    FCPerInfo selectByPrimaryKey(String perNo);

    int updateByPrimaryKeySelective(FCPerInfo record);

    int updateByPerNoOrIdNoSelective(FCPerInfo record);

    int updateByPrimaryKey(FCPerInfo record);

    List<FCPerInfo> selectByExample(Map<String, Object> map);

    int updatePerNoPrimary(FCPerInfo fcPerInfo);

    FCPerInfo findUserInfo(String userNo);

    /**
     * 投保清单导出excel
     *
     * @param perNo
     * @param grpNo
     * @return
     */
    FCPerInfo selectInsuredDetail(@Param("perNo") String perNo, @Param("grpNo") String grpNo);

    Map<String, String> getSignBankInfo(@Param("perNo") String perNo, @Param("ensureCode") String ensureCode);
    
    Map<String, String> getPayBankInfo(String orderItemNo);

    int countPeoplesBygrpNo(String grpNo);

    List<FCPerInfoTemp> selectByParam(Map<String, Object> map);

    // 判断是否存在该员工
    List<FCPerInfo> isExitPerInfo(Map<String, Object> map);

    FCPerInfo selectByUserNo(Map<String, String> map);

    List<Map<String, String>> selectByParamList(Map<String, Object> map);


    String selectSinglePerNo(Map<String, String> map);

    List<String> selectPerNo(String perno);

    FCPerInfo selectNewFcperinfo(String perNo);

    List<Map<String, String>> getPerInfoByEnsureCode(Map<String, String> map);

    List<String> getPerNoByFamIdNoList(@Param("perIdNo") String perIdNo, @Param("famIdNo") String famIdNo);

    List<FCPerInfo> getPerNoByFamIdNoList2(@Param("perIdNo") String perIdNo, @Param("famIdNo") String famIdNo);

    List<Map<String, String>> selectInsuredPerson(@Param("EnsureCode") String EnsureCode);

    List<Map<String, String>> selectInsuredPerson_addcheckstatus(@Param("EnsureCode") String EnsureCode);

    /**
     * 日常计划 初审 查询被保险人列表信息
     * @param EnsureCode
     * @return
     */
    List<Map<String, String>> selectInsuredPerson_All(@Param("EnsureCode") String EnsureCode);
    List<Map<String, String>> selectInsuredPerson_allInfo(@Param("EnsureCode") String EnsureCode);

    /**
     * 查询证件号相同的所有员工
     *
     * @param idNo
     * @return
     */
    List<FCPerInfo> selectByIdNo(String idNo);

    String selectInsuredPersonByIDNoandEnCode(@Param("IDNo")String IDNo, @Param("EnsureCode")String EnsureCode);

    FCPerInfo selectbyIdNoandGrpNo(@Param("IDNo")String IDNo, @Param("GrpNo")String GrpNo);

    /**
     *
     * @param grpNo
     * @param idNo
     * @param perNo
     * @return
     */
    List<FCPerInfo> selectByGrpNoIdNoAndPerNo(@Param("grpNo") String grpNo, @Param("idNo") String idNo,@Param("perNo") String perNo);

    /**
     * 查询员工信息 用于生成登录用户
     * @param ensureCode
     * @return
     */
    List<FCPerInfo> selectFromFCPerRegistDayByEnsureCode(String ensureCode);

    /**
     * 根据  ensureCode,  grpNo,  idNo  判断 日常计划定制中 在一个福利下面，一个企业 选择的这个员工  是否已经存在，返回的是这个员工的个数。
     * @param ensureCode
     * @param grpNo
     * @param idNo
     * @return
     */
    int selectThePeopleNum( @Param("EnsureCode")String ensureCode, @Param("grpNo")String grpNo, @Param("idNo")String idNo);

    /**
     * 根据 token 的perNo  查询该员工对应的企业集合
     * @param perNo
     * @return
     */
    List<Map<String, String>> selectgrpListByPerNo(@Param("perNo")String perNo);
    List<Map<String, String>> selectgrpListByPerNo2(@Param("perNo")String perNo);

    /**
     * 根据员工其中一个perNo  查找到对应其他企业得perNo
     * @param perNo
     * @param grpNo
     * @return
     */
    String selectperNobyPerNo(@Param("perNo")String perNo, @Param("grpNo")String grpNo);

    /**
     * 查询正式表的手机号是否已经存在
     * @param mobilePhone
     * @param perNo
     * @param iDNo
     * @return
     */
    int selectRepeatPhone(@Param("mobilePhone") String mobilePhone, @Param("perNo") String perNo, @Param("idno") String iDNo);
    int updateByPrimaryKeySelective1(FCPerInfo record);

    void updateByIdNo(Map<String, Object> params);

    List<Map<String, String>> selectInsuredPerson_Alls(@Param("EnsureCode") String EnsureCode);

    void updateBatchById(@Param("list")List<FCPerInfo> fcPerInfoList);

    FCPerInfo selectByIdNoAndGrpNo(@Param("idNo")String idNo, @Param("grpNo")String grpNo);
}