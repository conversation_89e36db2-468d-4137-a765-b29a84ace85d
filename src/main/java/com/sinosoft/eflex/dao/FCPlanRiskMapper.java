package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCPlanRisk;
import com.sinosoft.eflex.model.FCPlanRiskKey;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 计划险种表
 */
@Repository
public interface FCPlanRiskMapper {
    int deleteByPrimaryKey(FCPlanRiskKey key);

    int insert(FCPlanRisk record);

    int insertSelective(FCPlanRisk record);

    FCPlanRisk selectByPrimaryKey(FCPlanRiskKey key);

    int updateByPrimaryKeySelective(FCPlanRisk record);

    int updateByPrimaryKey(FCPlanRisk record);

    /**
     * 企业计划汇总
     *
     * @param ensureCode
     * @param planCode
     * @return
     */
    List<FCPlanRisk> selectPlanCollection(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode);

    /**
     * 计划详情查询
     *
     * @param planCode 计划编码
     * @return
     */
    List<FCPlanRisk> selectPlanDetailList(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode);

    List<String> selectRiskCodeList(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode);

    List<FCPlanRisk> selectRiskList(Map<String, Object> params);

    List<FCPlanRisk> selectRiskInfoList(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode);

    /**
     * 投保清单导出excel
     *
     * @param planCode 计划编码
     * @return
     */
    List<FCPlanRisk> selectInsuredDetail(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode);

    int deleteByEnsureCode(String ensureCode);


    List<FCPlanRisk> selectRiskInfo(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode);

    void insertList(List<FCPlanRisk> fcPlanRiskList);

    void insertList2(List<FCPlanRisk> fcPlanRiskList);

    //获取险种配置信息
    List<FCPlanRisk> getensureRiskInfo(String ensureCode);

    /**
     * 修改险种配置信息*
     */
    int updateEnsureRiskInfo(FCPlanRisk fcPlanRisk);

    /**
     * 查询停售的计划险种信息
     *
     * @param ensureCode
     */
    List<FCPlanRisk> selectStopSaleRiskCodeInfoByEnsureCode(String ensureCode);

    /**
     * 查询停售的计划险种信息
     *
     * @param ensureCode
     */
    List<String> selectStopSaleRiskCodeByEnsureCode(String ensureCode);

    /**
     * 固定计划-删除福利下某计划
     *
     * @param ensureCode
     */
    int deleteByEnsureCodeAndPlanCode(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode);

    /**
     * 删除定制中的无效福利计划险种关联信息
     *
     * @param ensureCode
     * @return
     */
    int deleteMakeingPlanRiskByEnsureCode(String ensureCode);

    void updateByEnsureCode(@Param("jEnsureCode") String jEnsureCode, @Param("ensureCode") String ensureCode);

    List<FCPlanRisk> selectPlanList(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode);

    List<FCPlanRisk> selectFcRiskList(FCPlanRisk fcPlanRisk);
}