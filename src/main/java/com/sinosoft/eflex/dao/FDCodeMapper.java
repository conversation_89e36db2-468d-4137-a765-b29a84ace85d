package com.sinosoft.eflex.dao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FDCode;
import com.sinosoft.eflex.model.FDCodeKey;

@Repository
public interface FDCodeMapper {
    int deleteByPrimaryKey(FDCodeKey key);

    int insert(FDCode record);

    int insertSelective(FDCode record);

    FDCode selectByPrimaryKey(FDCodeKey key);

    int updateByPrimaryKeySelective(FDCode record);

    int updateByPrimaryKey(FDCode record);

    List<HashMap<String,Object>> findCodeInfo(@Param("codeType") String codeType);

    List<HashMap<String,Object>> CodeInfo(@Param("codeType") String codeType);

    List<HashMap<String,Object>> getprovince_list(String provinceList);
    
    List<HashMap<String,Object>> getcity_list(String cityList);
    
    List<HashMap<String,Object>> getcounty_list(String countyList);

    String selectNameByCode(@Param("codeType") String codeType, @Param("codeKey") String codeKey);
    
    String selectKeyByCodeName(@Param("codeType") String codeType, @Param("codeName") String codeName);

    List<HashMap<String,Object>>  getOccupationCode(@Param("codeType") String codeType,@Param("codeDesc") String codeDesc);
    List<HashMap<String,Object>>  getOccupationCodeOneToFour(@Param("codeType") String codeType,@Param("codeDesc") String codeDesc);
    List<HashMap<String,Object>>  getOccupationCodeOneTosix(@Param("codeType") String codeType,@Param("codeDesc") String codeDesc);

    FDCode selectByCodeKey(@Param("codeKey")String codeKey);

    String selectSOccupationCodeOtherSign(@Param("codeKey")String codeKey);

    FDCode selectByCodeName(Map<String,String> map);

    /**
     *查询类型所对应的码值描述
     * @param codeType
     * @return
     */
    List<String> selectCodeNameByCodeType(String codeType);

    /**
     * 查询类型所对应的码值
     * @param codeType
     * @return
     */
    List<String> selectCodeKeyByCodeType(String codeType);

    /**
     * 查询指定的码或码值
     * @param fdCode
     * @return
     */
    String selectCodeKeyOrCodeName(FDCode fdCode);

    /**
     * 查询职级类别
     * @param occupationCode
     * @return
     */
    String selectOccupationType(String occupationCode);


    List<HashMap<String, Object>> findCodeInfoNew(@Param("codeType") String codeType,
            @Param("otherSign") String codeDesc);

    /**
     * 查询行政区划代码，统一社会信用代码校验使用
     * 
     * @param countyCode
     * @return
     */
    int selectSingleInfoByCountyCode(String countyCode);

    /**
     * 查询核心对应的码值
     *
     * @param fdCode
     */
    String selectCoreCode(FDCode fdCode);

    /**
     * 查询员福与核心的码值对应关系
     *
     * @param codeType
     * @return
     */
    List<FDCode> selectCodeAndCoreCode(String codeType);

    /**
     * 查询其他标志
     */
    String selectOtherSign(@Param("codeType") String codeType, @Param("codeKey") String codeKey);

    /**
     * 查询核心编码
     */
    String selectCoreCodeKey(@Param("codeType") String codeType, @Param("codeKey") String codeKey);

    String queryNameByCode(String openBank);
}