package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FcEnsureContact;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface FcEnsureContactMapper {
    int deleteByPrimaryKey(String ensureCode);

    int insert(FcEnsureContact record);

    int insertSelective(FcEnsureContact record);

    FcEnsureContact selectByPrimaryKey(String ensureCode);

    int updateByPrimaryKeySelective(FcEnsureContact record);

    int updateByPrimaryKey(FcEnsureContact record);

    FcEnsureContact selectByEnsureCode(@Param("ensureCode") String ensureCode);
}