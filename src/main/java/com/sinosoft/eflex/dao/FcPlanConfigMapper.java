package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FcPlanConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FcPlanConfigMapper {
    int delete(@Param("planCode") String planCode ,@Param("ensureCode") String ensureCode);

    int insertSelective(FcPlanConfig record);

    FcPlanConfig selectByPrimaryKey(String serialNo);

    int updateByPrimaryKeySelective(FcPlanConfig record);

    int updateByPrimaryKey(FcPlanConfig record);

    int insert(@Param("planConfig")List<FcPlanConfig> planConfig);

    List<FcPlanConfig> getFCPlanConfigList(Map<String, String> params);

    List<FcPlanConfig> getConfigExplain(Map<String,Object> map);

    List<FcPlanConfig> getFCPlanConfigByPlanCode(@Param("planCode") String planCode, @Param("ensureCode") String ensureCode);

    List<FcPlanConfig> getFCPlanConfigMaxMinAge(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode);

    List<FcPlanConfig> selectFCPlanConfigByEnsureCode(@Param("ensureCode") String ensureCode);
}