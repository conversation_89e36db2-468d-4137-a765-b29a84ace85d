package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCBackMoneyDeal;
import org.apache.ibatis.annotations.Param;

public interface FCBackMoneyDealMapper {
    int deleteByPrimaryKey(@Param("orderNo") String orderNo, @Param("payFlowNo") String payFlowNo);

    int insert(FCBackMoneyDeal record);

    int insertSelective(FCBackMoneyDeal record);

    FCBackMoneyDeal selectByPrimaryKey(@Param("orderNo") String orderNo, @Param("payFlowNo") String payFlowNo);

    int updateByPrimaryKeySelective(FCBackMoneyDeal record);

    int updateByPrimaryKey(FCBackMoneyDeal record);
}