package com.sinosoft.eflex.dao;

import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FCEdorACInfo;

@Repository
public interface FCEdorACInfoMapper {
    int deleteByPrimaryKey(String edorAppNo);

    int insert(FCEdorACInfo record);

    int insertSelective(FCEdorACInfo record);

    FCEdorACInfo selectByPrimaryKey(String edorAppNo);

    int updateByPrimaryKeySelective(FCEdorACInfo record);

    int updateByPrimaryKey(FCEdorACInfo record);
}