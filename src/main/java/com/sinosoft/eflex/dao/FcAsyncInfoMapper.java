package com.sinosoft.eflex.dao;

import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FcAsyncInfo;

@Repository
public interface FcAsyncInfoMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(FcAsyncInfo record);

    int insertSelective(FcAsyncInfo record);

    FcAsyncInfo selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(FcAsyncInfo record);

    int updateByPrimaryKey(FcAsyncInfo record);

    /**
     * 查询当前保单下异步记录信息
     */
    FcAsyncInfo selectAsyncInfo(FcAsyncInfo fcAsyncInfo);

}