package com.sinosoft.eflex.dao;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FdCom;
import com.sinosoft.eflex.model.SelectAllManageComResp;

@Repository
public interface FdComMapper {
    int deleteByPrimaryKey(String manageCom);

    int insert(FdCom record);

    int insertSelective(FdCom record);

    FdCom selectByPrimaryKey(String manageCom);

    int updateByPrimaryKeySelective(FdCom record);

    int updateByPrimaryKey(FdCom record);

    /**
     * 查询机构信息
     * 
     * @param fdCom
     * @return
     */
    List<SelectAllManageComResp> selectAllManageCom(FdCom fdCom);

}