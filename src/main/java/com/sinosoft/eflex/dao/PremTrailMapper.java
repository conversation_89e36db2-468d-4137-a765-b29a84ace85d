package com.sinosoft.eflex.dao;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.DailyPremTrail;

@Repository
public interface PremTrailMapper {
    List<String> getPrem_12020(Map<String, Object> map);

    List<String> getPrem_15030(Map<String, Object> map);

    List<String> getPrem_15060(Map<String, Object> map);

    List<String> getPrem_16040(Map<String, Object> map);

    List<Map<String, String>> getPrem_15060_2(Map<String, Object> map);

    List<String> getPrem_16490(Map<String, Object> map);

    List<String> getPrem_17020(Map<String, Object> map);

    List<Map<String, String>> getPrem_17020_2(Map<String, Object> map);

    List<String> getPrem_17030(Map<String, Object> map);

    List<String> getPrem_15040(Map<String, Object> map);

    List<String> getPrem_17010(Map<String, Object> map);

    List<String> getPrem_17050(Map<String, Object> map);

    List<String> getPrem_15070(Map<String, Object> map);

    List<String> premTrial_311012(DailyPremTrail dailyPremTrail);

    List<Map<String, String>> getPrem_15070_2(Map<String, Object> map);
}
