package com.sinosoft.eflex.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FCDefaultPlan;

@Repository
public interface FCDefaultPlanMapper {
    int deleteByPrimaryKey(String serialNo);

    int insert(FCDefaultPlan record);

    int insertSelective(FCDefaultPlan record);

    FCDefaultPlan selectByPrimaryKey(String serialNo);

    int updateByPrimaryKeySelective(FCDefaultPlan record);

    int updateByPrimaryKey(FCDefaultPlan record);

    List<FCDefaultPlan> selectDefaultPlans(Map<String,Object> params);

    @Select("<script>select * from fcdefaultplan where EnsureCode = #{ensureCode,jdbcType=VARCHAR} and personId in (" +
            "<trim>" +
            "<foreach collection='list' item='item' separator=','> " +
            "#{item}" +
            "</foreach>)" +
            "</trim></script>")
    List<FCDefaultPlan> getAllByList(@Param("ensureCode") String ensureCode,@Param("list") List list);

    @Select("select * from fcperson where IDNo = (select IDNo from fcperson where PersonID = #{id})")
    List<String> getAllPersonId(String id);
    /**
     * 计划详情查询>个人默认计划总保费
     * @param personId 被保人ID
     * @param ensureCode 福利编号
     * @return 个人默认计划总保费
     */
    Map<String, Double> selectPayment(@Param("personId") String personId,@Param("ensureCode") String ensureCode);

    /* 根据员工表客户号查询默认计划 */
    FCDefaultPlan selectByPerNo(String perNo);

    int deleteByParams(Map<String, String> param);
    
    String getEmployPersonid(String perNo);

    FCDefaultPlan selectDoublePlan(Map<String,Object> map);

    FCDefaultPlan selectDoublePlanByPerNo(Map<String,Object> map);

    void deleteByPerId(Map<String, String> request);
}