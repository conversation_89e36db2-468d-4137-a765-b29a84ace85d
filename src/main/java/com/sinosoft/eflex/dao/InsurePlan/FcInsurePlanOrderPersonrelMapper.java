package com.sinosoft.eflex.dao.InsurePlan;



import com.sinosoft.eflex.model.insurePlan.InsurePlanOrderPersonrel;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface FcInsurePlanOrderPersonrelMapper {


    InsurePlanOrderPersonrel  selectByPrimaryKey(String id);

    void deleteByPrimaryKey(String id);

    void insert(InsurePlanOrderPersonrel insurePlanorderPersonrel);

    void insertSelective(InsurePlanOrderPersonrel insurePlanorderPersonrel);


    void updateByPrimaryKeySelective(InsurePlanOrderPersonrel insurePlanorderPersonrel);

    List<InsurePlanOrderPersonrel> selectByOrderNo(String orderNo);

    int selectCountPersonrel(String orderNo);

    InsurePlanOrderPersonrel selectByPersonrelInfo(InsurePlanOrderPersonrel insurePlanOrderPersonrel);

    void updateByOrderNO(InsurePlanOrderPersonrel insurePlanOrderPersonrel);
}
