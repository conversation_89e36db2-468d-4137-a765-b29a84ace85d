package com.sinosoft.eflex.dao.InsurePlan;


import com.sinosoft.eflex.model.insurePlan.InsurePlan;
import com.sinosoft.eflex.model.insurePlan.InsurePlanVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public interface FcInsurePlanMapper {

    void  insert(InsurePlan insurePlan);

    Long insertSelective(InsurePlan insurePlan);

    InsurePlan selectByPrimaryKey(Long id);

    void updateByPrimaryKeySelective(InsurePlan insurePlan);

    void delByPrimaryKey(Long id);

    List selectInsurePlanByPage(Map map);

    List selectInsurePlanList(@Param("planIds") List<String> planIds);

    InsurePlanVo selectByInsurePlanCode(String insurePlanCode);

    InsurePlan selectByInsureplanName(@Param("insureplanName") String insureplanName);
}
