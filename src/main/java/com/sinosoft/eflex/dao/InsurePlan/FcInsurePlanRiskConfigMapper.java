package com.sinosoft.eflex.dao.InsurePlan;

import com.sinosoft.eflex.model.insurePlan.InsurePlan;
import com.sinosoft.eflex.model.insurePlan.InsurePlanRiskConfig;
import org.springframework.stereotype.Repository;
import org.yaml.snakeyaml.events.Event;

import java.util.List;

@Repository
public interface FcInsurePlanRiskConfigMapper {


    void delByInsureplanCode(String insureplanCode);

    InsurePlanRiskConfig selectByPrimaryKey(String id);

    void insert(InsurePlanRiskConfig riskConfig);

    void insertSelective(InsurePlanRiskConfig riskConfig);

    void updateByPrimaryKeySelective(InsurePlanRiskConfig riskConfig);

    void deleteByPrimaryKey(String id);

    List<InsurePlanRiskConfig> selectRiskConfigListByCode(String insureplanCode);


    void updateByInsurPlanCode(InsurePlanRiskConfig riskConfig);

}
