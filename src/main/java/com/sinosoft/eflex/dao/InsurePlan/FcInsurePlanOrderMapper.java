package com.sinosoft.eflex.dao.InsurePlan;



import com.sinosoft.eflex.model.insurePlan.InsurePlanOrder;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface FcInsurePlanOrderMapper {

    InsurePlanOrder selectByPrimaryKey(String id);

    void deleteByPrimaryKey(String id);

    void insert(InsurePlanOrder insurePlanorderPersonrel);

    void insertSelective(InsurePlanOrder insurePlanorderPersonrel);


    void updateByPrimaryKeySelective(InsurePlanOrder insurePlanorderPersonrel);

    InsurePlanOrder selectByOrderNo(String orderNo);

    List<InsurePlanOrder> selectByInsurePlanCode(String orderNo);

}
