package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCFileDocMain;
import org.springframework.stereotype.Repository;

@Repository
public interface FCFileDocMainMapper {
    int deleteByPrimaryKey(String docID);

    int insert(FCFileDocMain record);

    int insertSelective(FCFileDocMain record);

    FCFileDocMain selectByPrimaryKey(String docID);

    int updateByPrimaryKeySelective(FCFileDocMain record);

    int updateByPrimaryKey(FCFileDocMain record);
}