package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCPlanHealthDesignRela;

import java.util.List;
import java.util.Map;

import com.sinosoft.eflex.model.confirmInsureEflex.CheckIsNeedInformReq;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface FCPlanHealthDesignRelaMapper {
    int deleteByPrimaryKey(@Param("ensureCode") String ensureCode, @Param("designNo") String designNo);

    int insert(FCPlanHealthDesignRela record);

    int insertSelective(FCPlanHealthDesignRela record);

    FCPlanHealthDesignRela selectByPrimaryKey(@Param("ensureCode") String ensureCode, @Param("designNo") String designNo);

    int updateByPrimaryKeySelective(FCPlanHealthDesignRela record);

    int updateByPrimaryKey(FCPlanHealthDesignRela record);

    int updateByEnsureCode(FCPlanHealthDesignRela record);

    int checkIsNeedInform(Map<String, Object> amountGrageCodeMap);

    /**
     * 判断是否需要健康告知
     * 
     * @param checkIsNeedInformReq
     * @return
     */
    int checkIsNeedHealthNotice(CheckIsNeedInformReq checkIsNeedInformReq);

    //根据方案编号修改方案
    int updateByDesignNo(FCPlanHealthDesignRela record);

    List<FCPlanHealthDesignRela> selectByEnsureCode(String ensureCode);
}