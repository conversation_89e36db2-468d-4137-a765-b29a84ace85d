package com.sinosoft.eflex.dao;

import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FDusertomenugrpKey;

@Repository
public interface FDusertomenugrpMapper {
    int deleteByPrimaryKey(FDusertomenugrpKey key);

    int insert(FDusertomenugrpKey record);

    int insertSelective(FDusertomenugrpKey record);

    FDusertomenugrpKey findMenuInfo(String userNo);

    int deleteByUserNo(String userNo);

}