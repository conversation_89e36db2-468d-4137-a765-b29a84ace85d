package com.sinosoft.eflex.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FCEdorAddPlanRiskInfo;

@Repository
public interface FCEdorAddPlanRiskInfoMapper {
    int deleteByPrimaryKey(@Param("edorAddPlanSN") String edorAddPlanSN, @Param("riskCode") String riskCode);

    int insert(FCEdorAddPlanRiskInfo record);

    int insertSelective(FCEdorAddPlanRiskInfo record);

    FCEdorAddPlanRiskInfo selectByPrimaryKey(@Param("edorAddPlanSN") String edorAddPlanSN, @Param("riskCode") String riskCode);

    int updateByPrimaryKeySelective(FCEdorAddPlanRiskInfo record);

    int updateByPrimaryKey(FCEdorAddPlanRiskInfo record);

    int insertEdorAddPlanRiskList(List<FCEdorAddPlanRiskInfo> fcEdorAddPlanRiskInfoList);

    List<FCEdorAddPlanRiskInfo> getEdorPlanRiskListBygrpContNo(String grpContNo);

    List<FCEdorAddPlanRiskInfo> getEdorPlanRiskListByedorAddPlanSN(String edorAddPlanSN);

    /**
     * 删除保单下的险种信息
     * 
     * @param grpContNo
     * @return
     */
    int deleteByGrpContNo(@Param("grpContNo") String grpContNo);

}