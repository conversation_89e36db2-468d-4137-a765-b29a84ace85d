package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCBackBankInfo;
import org.apache.ibatis.annotations.Param;

public interface FCBackBankInfoMapper {
    int deleteByPrimaryKey(@Param("orderNo") String orderNo, @Param("payFlowNo") String payFlowNo);

    int insert(FCBackBankInfo record);

    int insertSelective(FCBackBankInfo record);

    FCBackBankInfo selectByPrimaryKey(@Param("orderNo") String orderNo, @Param("payFlowNo") String payFlowNo);

    int updateByPrimaryKeySelective(FCBackBankInfo record);

    int updateByPrimaryKey(FCBackBankInfo record);
}