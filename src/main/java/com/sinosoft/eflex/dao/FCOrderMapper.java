package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCOrder;
import com.sinosoft.eflex.model.FCOrderPay;
import com.sinosoft.eflex.model.confirmInsure.GrpOrderInfo;
import com.sinosoft.eflex.model.confirmInsureEflex.InsureEflexOrderDetailInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository
public interface FCOrderMapper {

    int deleteByPrimaryKey(String orderNo);

    String getInsuranceSignatureByOrderNo(String orderNo);

    int insert(FCOrder record);

    int insertSelective(FCOrder record);

    int updateFcorderpay(Map<String, Object> params);

    int updateFcorderByConNo(Map<String, Object> params);

    int updateFCOrderReview(Map<String, Object> params);

    int insertFCOrderReview(Map<String, Object> params);

    int updateFcorderFace(Map<String, Object> params);

    int updateFcorderFaceByQuery(Map<String, Object> params);

    int updateFcorderpayIsValid(Map<String, Object> params);

    int insertFcorderpay(Map<String, Object> params);

    int insertFcorderFace(Map<String, Object> params);

    FCOrder selectByPrimaryKey(String orderNo);

    FCOrder selectOrderByOrderItemNo(String orderItemNo);

    int updateByPrimaryKeySelective(FCOrder record);

    int updateByPrimaryKey(FCOrder record);

    /**
     * 投保清单查询
     *
     * @param requestMap
     * @return
     */
    ArrayList<Map<String, String>> listInsureDetail(Map<String, Object> requestMap);

    ArrayList<Map<String, String>> listStuInsureDetail(Map<String, Object> requestMap);

    ArrayList<Map<String, String>> getDeleteOrderInfo(String orderNo);

    List<FCOrder> selectList(Map<String, Object> params);

    /**
     * 团体订单号 查询*
     *
     * @param grpOrderNo 团体订单号
     * @return 订单信息
     */
    List<FCOrder> selectOrderListByGrpOrderNo(@Param("grpOrderNo") String grpOrderNo);

    /**
     * 投保清单导出excel
     *
     * @param grpOrderNo
     * @param grpNo
     * @return
     */
    List<FCOrder> selectInsuredDetail(@Param("ensureCode") String ensureCode, @Param("grpOrderNo") String grpOrderNo, @Param("grpNo") String grpNo, @Param("planName") String planName);

    List<FCOrder> selectPerNo(@Param("perNo") String perNo, @Param("orderStatus") String orderStatus, @Param("ensureCode") String ensureCode);

    List<String> getBizTokenByPerInfo(@Param("orderNo") String orderNo, @Param("personId") String personId);

    List<Map<String, Object>> getFaceInfoByPerInfo(@Param("orderNo") String orderNo, @Param("personId") String personId);

    List<FCOrder> selectOrder(@Param("perNo") String perNo, @Param("ensureCode") String ensureCode);

    FCOrder selectOrderByperNo(Map<String, Object> map);

    String selectOrderList(@Param("perNo") String perNo, @Param("ensureCode") String ensureCode);

    List<String> selectByEnsureCode(@Param("ensureCode") String ensureCode);

    List<Map<String, Object>> getSelfOrderInfo(@Param("perNo") String perNo, @Param("ensureCode") String ensureCode);

    List<Map<String, Object>> selfEmpOrderInfo(@Param("perNo") String perNo, @Param("ensureCode") String ensureCode);

    List<Map<String, Object>> selfFamilyOrderInfo(@Param("perNo") String perNo, @Param("ensureCode") String ensureCode);

    List<Map<String, Object>> selfOrderDetailDesInfo(@Param("perNo") String perNo, @Param("ensureCode") String ensureCode, @Param("personId") String personId);

    List<InsureEflexOrderDetailInfo> selfOrderDetailDesInfo1(@Param("ensureCode") String ensureCode,
                                                             @Param("perNo") String perNo, @Param("personId") String personId);

    void updateFcorder(@Param("orderStatus") String orderStatus, @Param("orderNo") String orderNo, @Param("modifyDate") String modifyDate, @Param("modifyTime") String modifyTime);

    List<FCOrder> getOrderListByPerNo(Map<String, String> map);

    List<Map<String, Object>> getOrderListByPerNoStaff(Map<String, String> map);

    Map<String, String> getOrderInfoByOrderNo(@Param("orderNo") String orderNo);

    Map<String, String> getInsureInfo(@Param("orderNo") String orderNo);

    Map<String, String> getOrderInfoByOrderNoDaily(@Param("orderNo") String orderNo);

    List<Map<String, String>> getOrderListByEnsureCode(Map<String, String> map);

    List<Map<String, String>> getOrderStatusByEnsureCode(Map<String, String> map);

    int updateCloseDayByEnsureCode(@Param("ensureCode") String ensureCode, @Param("closeDay") String closeDay);

    /**
     * 根据企业号和客人客户号查询订单信息
     *
     * @param grpNo
     * @param perNo
     * @return
     */
    List<String> selectByGrpNoAndPerNo(@Param("grpNo") String grpNo, @Param("perNo") String perNo);

    //根据企业号和客人客户号查询订单信息
    List<Map<String, String>> selectAllByGrpNoAndPerNoandCode(@Param("grpNo") String grpNo, @Param("perNo") String perNo, @Param("ensureCode") String ensureCode);

    /**
     * 查询其他企业的订单信息
     *
     * @param idNo
     * @param grpNo
     * @return
     */
    List<String> selectOtherGrpNoOrderNosByIdNo(@Param("idNo") String idNo, @Param("grpNo") String grpNo);

    /**
     * 查询福利下的订单信息
     *
     * @param ensureCode
     * @return
     */
    List<FCOrder> selectOrderInfoByEnsureCode(String ensureCode);

    /**
     * 查询核保结论
     *
     * @param orderitemNo
     * @return
     */
    Map<String, String> getReview(@Param("orderItemNo") String orderitemNo);

    /**
     * 根据 子订单号查询  支付方式。
     *
     * @param orderItemNo
     * @return
     */
    FCOrderPay selectorderPay(@Param("orderItemNo") String orderItemNo);

    /**
     * 查询登录系统的员工投保总数
     *
     * @param ensureCode
     * @return
     */
    int selectLoginOrderCountByEnsureCode(String ensureCode);

    /**
     * 查询员工投保总数
     *
     * @param ensureCode
     * @return
     */
    int selectOrderCountByEnsureCode(String ensureCode);

    /**
     * 获取福利编码
     */
    GrpOrderInfo selectGrpOrderInfo(@Param("orderNo") String orderNo);

    List<FCOrder> queryGrpOrder(String orderItemNo);

    FCOrder selectOrderByPerNoAndEnsureCode(@Param("perNo") String perNo, @Param("ensureCode") String ensureCode);

    void deleteByGrpOrderAndPerNo(@Param("grpNo") String grpNo, @Param("perNo") String perNo);

    FCOrder selectGrpOrderAndPerNo(@Param("grpOrderNo") String grpOrderNo, @Param("perNo") String perNo);

    List<FCOrder> selectByOrder(FCOrder fcOrder);

    void updateByGrpOrderNo(@Param("grpOrderNo") String grpOrderNo);
}