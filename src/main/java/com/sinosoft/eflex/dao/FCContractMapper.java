package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCContract;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FCContractMapper {
    int deleteByPrimaryKey(String contractSN);

    int insert(FCContract record);

    int insertSelective(FCContract record);

    FCContract selectByPrimaryKey(String contractSN);

    int updateByPrimaryKeySelective(FCContract record);

    int updateByPrimaryKey(FCContract record);

    List<FCContract> selectContract(Map<String,Object> map);
}