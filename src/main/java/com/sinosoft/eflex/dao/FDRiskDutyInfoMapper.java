package com.sinosoft.eflex.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FDRiskDutyInfo;
import com.sinosoft.eflex.model.FDRiskDutyInfoKey;

@Repository
public interface FDRiskDutyInfoMapper {
    int deleteByPrimaryKey(FDRiskDutyInfoKey key);

    int insert(FDRiskDutyInfo record);

    int insertSelective(FDRiskDutyInfo record);

    FDRiskDutyInfo selectByPrimaryKey(FDRiskDutyInfoKey key);

    int updateByPrimaryKeySelective(FDRiskDutyInfo record);

    int updateByPrimaryKey(FDRiskDutyInfo record);

    Map<String, Object> selectByDutyCode(@Param("params")Map<String, String> map);

    Map<String,Object> selectDutyName(Map<String,String> map);

    List<Map<String,String>> getOptDutyByRiskCode(@Param("riskCode") String riskCode,@Param("dutyCode") String dutyCode);

    List<FDRiskDutyInfo> getRisk_15070ByEnsureCode(@Param("ensureCode")String ensureCode);

    List<FDRiskDutyInfo> getRisk_15070ByEnsureCodeAndPlanCode(@Param("ensureCode") String ensureCode,
            @Param("planCode") String planCode);

    List<Map<String,String>> getRiskAndDutyInfo(Map<String,String> map);

    List<Map<String,String>> getRiskAndDutyInfoByDutyCode(Map<String,String> map);

    String selectByRiskCodeAndDutyCode(@Param("riskCode")String riskCode,@Param("dutyCode")String dutyCode);

    /* 根据险种编码获取责任信息 */
    List<FDRiskDutyInfo> selectByRiskCode(@Param("riskCode") String riskCode);

    /* 根据险种编码集合获取责任信息 */
    List<FDRiskDutyInfo> selectByRiskCodes(@Param("riskCodes") List<String> riskCodes);


    List<FDRiskDutyInfo> getRisk_15070ByDutyCode(@Param("dutyCode") String dutyCode);
}