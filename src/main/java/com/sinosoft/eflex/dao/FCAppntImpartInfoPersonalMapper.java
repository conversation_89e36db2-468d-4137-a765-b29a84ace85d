package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCAppntImpartInfoPersonal;
import com.sinosoft.eflex.model.dailyplan.AppntImpart;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FCAppntImpartInfoPersonalMapper {
    int deleteByPrimaryKey(@Param("orderItemNo") String orderItemNo, @Param("impartCode") String impartCode);

    int insert(FCAppntImpartInfoPersonal record);

    int insertSelective(FCAppntImpartInfoPersonal record);

    FCAppntImpartInfoPersonal selectByPrimaryKey(@Param("orderItemNo") String orderItemNo, @Param("impartCode") String impartCode);

    int updateByPrimaryKeySelective(FCAppntImpartInfoPersonal record);

    /**
     * 据告知编码把 告知详情存库
     *
     * @param impartCode
     * @param dsimpartParam
     * @return
     */
    int updateByModle(@Param("impartCode") String impartCode, @Param("dsimpartParam") String dsimpartParam, @Param("orderItemNo") String orderItemNo);

    int updateByPrimaryKey(FCAppntImpartInfoPersonal record);

    int deleteImpartInfo(@Param("orderItemNo") String orderItemNo);

    /**
     * 根据子订单号查询个人健告
     *
     * @param orderItemNo
     * @return
     */
    List<AppntImpart> selectByOrderItemNo(String orderItemNo);
    List<AppntImpart> selectByOrderItemNos(String orderItemNo);//不包括 身高体重 年收入  和 少儿体重
    List<AppntImpart> selectByOrderItemNoAndCode(String orderItemNo);//只包括 少儿体重

    /**
     * 根据 子订单号 查询 个人端健康告知
     *
     * @param map
     * @return
     */
    List<FCAppntImpartInfoPersonal> selectList(Map<String, String> map);

    /**
     * 根据子订单号查询个人健告
     * @param orderItemNo
     * @return
     */
    List<FCAppntImpartInfoPersonal> selectAllByOrderItemNo(String orderItemNo);
}