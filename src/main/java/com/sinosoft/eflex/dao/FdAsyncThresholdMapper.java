package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FdAsyncThreshold;
import org.springframework.stereotype.Repository;

@Repository
public interface FdAsyncThresholdMapper {
    int deleteByPrimaryKey(String businessCode);

    int insert(FdAsyncThreshold record);

    int insertSelective(FdAsyncThreshold record);

    FdAsyncThreshold selectByPrimaryKey(String businessCode);

    int updateByPrimaryKeySelective(FdAsyncThreshold record);

    int updateByPrimaryKey(FdAsyncThreshold record);

    /**
     * 查询人员限制阈值
     *
     * @param businessCode
     */
    Integer selectPeopleLimit(String businessCode);

}