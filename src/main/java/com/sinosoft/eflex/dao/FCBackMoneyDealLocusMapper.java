package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCBackMoneyDealLocus;

public interface FCBackMoneyDealLocusMapper {
    int deleteByPrimaryKey(String locusNo);

    int insert(FCBackMoneyDealLocus record);

    int insertSelective(FCBackMoneyDealLocus record);

    FCBackMoneyDealLocus selectByPrimaryKey(String locusNo);

    int updateByPrimaryKeySelective(FCBackMoneyDealLocus record);

    int updateByPrimaryKey(FCBackMoneyDealLocus record);
}