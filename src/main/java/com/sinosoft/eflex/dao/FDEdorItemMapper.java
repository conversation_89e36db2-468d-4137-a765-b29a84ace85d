package com.sinosoft.eflex.dao;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FDEdorItem;

@Repository
public interface FDEdorItemMapper {

    int deleteByPrimaryKey(String edorcode);

    int insert(FDEdorItem record);

    int insertSelective(FDEdorItem record);

    FDEdorItem selectByPrimaryKey(String edorcode);

    int updateByPrimaryKeySelective(FDEdorItem record);

    int updateByPrimaryKey(FDEdorItem record);

    List<FDEdorItem> findEdorInfo();
}