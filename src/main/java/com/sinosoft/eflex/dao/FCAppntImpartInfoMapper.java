package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.BatchInsureInterface.AppntImpartInfo;
import com.sinosoft.eflex.model.FCAppntImpartInfo;
import com.sinosoft.eflex.model.FCAppntImpartInfoKey;
import com.sinosoft.eflex.model.dailyplan.AppntImpart;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FCAppntImpartInfoMapper {
    int deleteByPrimaryKey(FCAppntImpartInfoKey key);

    int insert(FCAppntImpartInfo record);

    int insertSelective(FCAppntImpartInfo record);

    FCAppntImpartInfo selectByPrimaryKey(FCAppntImpartInfoKey key);

    int updateByPrimaryKeySelective(FCAppntImpartInfo record);

    int updateByPrimaryKey(FCAppntImpartInfo record);

    int deleteImpartInfo(Map<String,String> map);

    List<FCAppntImpartInfo> selectList(Map<String,String> map);

    List<AppntImpartInfo> selectAppntImpartList(Map<String,String> map);

    /**
     * 查询健康告知信息
     * @return
     */
    List<AppntImpart> selectImpartInfo(@Param("ensureCode") String ensureCode);

    /**
     * 查询全部健康告知 for 员工
     * @return
     */
    List<AppntImpart> selectAllImpartInfo();
}