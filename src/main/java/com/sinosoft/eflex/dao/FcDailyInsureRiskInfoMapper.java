package com.sinosoft.eflex.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FcDailyInsureRiskInfo;

/**
 * <AUTHOR> wenying <PERSON>a
 * @date : 2020-04-16 10:54
 **/
@Repository
public interface FcDailyInsureRiskInfoMapper {
    int insert(FcDailyInsureRiskInfo fcDailyInsureRiskInfo);

    int deleteByEnsureCode(String ensureCode);

    /**
     * 查询停售的计划险种信息
     * 
     * @param ensureCode
     */
    List<String> selectStopSaleRiskCodeByEnsureCode(String ensureCode);

    /**
     * 查询新增字段手续费比率和佣金/服务津贴率
     */
    FcDailyInsureRiskInfo selectByEnsureCodeAndRiskCode(@Param("ensureCode") String ensureCode,
            @Param("riskCode") String riskCode);

    /**
     * 根据福利编码查询日常计划的险种信息
     * 
     * @param ensureCode
     */
    String selectByEnsureCode(String ensureCode);

    /**
     * 根据福利编码更改日常投保险种信息配置中的一些字段（主要是新增字段佣金/服务津贴率）
     */
    int updateRatioByEnsureCode(FcDailyInsureRiskInfo fcDailyInsureRiskInfo);

    /**
     * 查询日常计划定制的险种
     */
    List<String> selectDailyRiskCodeByEnsureCode(String ensureCode);

}
