package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FDRiskCompensationRatio;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FDRiskCompensationRatioMapper {
    int deleteByPrimaryKey(@Param("riskCode") String riskCode, @Param("compensationRatio") Double compensationRatio);

    int insert(FDRiskCompensationRatio record);

    int insertSelective(FDRiskCompensationRatio record);

    List<FDRiskCompensationRatio> selectByPrimaryKey(Map<String,String> map);

    int updateByPrimaryKeySelective(FDRiskCompensationRatio record);

    int updateByPrimaryKey(FDRiskCompensationRatio record);
}