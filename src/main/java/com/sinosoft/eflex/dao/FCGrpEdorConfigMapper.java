package com.sinosoft.eflex.dao;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FCGrpEdorConfig;
import com.sinosoft.eflex.model.FCGrpEdorConfigKey;

@Repository
public interface FCGrpEdorConfigMapper {
    int deleteByPrimaryKey(FCGrpEdorConfigKey key);

    int insert(FCGrpEdorConfig record);

    int insertSelective(FCGrpEdorConfig record);

    FCGrpEdorConfig selectByPrimaryKey(FCGrpEdorConfigKey key);

    int updateByPrimaryKeySelective(FCGrpEdorConfig record);

    int updateByPrimaryKey(FCGrpEdorConfig record);

    List<FCGrpEdorConfig> findGrpEdorInfo(String grpNo);
}