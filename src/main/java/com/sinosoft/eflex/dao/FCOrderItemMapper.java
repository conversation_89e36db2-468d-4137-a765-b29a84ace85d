package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCOrderItem;
import com.sinosoft.eflex.model.FCOrderItemDetail;
import com.sinosoft.eflex.model.FcOrderItemShareRela;
import com.sinosoft.eflex.model.OrderItemDetailData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 子订单表
 *
 * <AUTHOR>
 */
@Repository
public interface FCOrderItemMapper {
    int deleteByPrimaryKey(String orderItemNo);

    int insert(FCOrderItem record);

    int insertSelective(FCOrderItem record);

    FCOrderItem selectByPrimaryKey(String orderItemNo);

    int updateByPrimaryKeySelective(FCOrderItem record);

    int updateByPrimaryKey(FCOrderItem record);

    /**
     * 员工选择计划
     *
     * @param requestMap
     * @return
     */
    List<Map<String, Object>> selectEmployPlanList(Map<String, Object> requestMap);

    /**
     * 家属选择计划
     *
     * @param requestMap
     * @return
     */
    List<Map<String, String>> selectRelationPlanList(Map<String, Object> requestMap);

    List<Map<String, String>> queryStudentPlanList(Map<String, Object> requestMap);

    int updateFcorderItemByContNo(Map<String, Object> requestMap);

    /**
     * 投保清单导出excel
     *
     * @param orderNo
     * @return
     */
    List<FCOrderItem> selectInsuredDetails(@Param("ensureCode") String ensureCode, @Param("orderNo") String orderNo, @Param("planName") String planName);

    List<FCOrderItem> selectList(Map<String, Object> params);

    List<FCOrderItem> selectOrderNo(@Param("orderNo") String orderNo);

    int getEnsureNum(String ensureCode);

    FCOrderItem getByPersonId(@Param("ensureCode") String ensureCode, @Param("planCode") String planCode, @Param("personId") String personId);

    int deleteByOrderNo(String orderNo);

    int deleteByPersonId(@Param("orderNo") String orderNo, @Param("personId") String personId);

    String getSumTotalPrem(@Param("perNo") String perNo, @Param("grpOrderNo") String ensureCode);

    Map<String, String> selectPremByEnsureCode(@Param("ensureCode") String ensureCode);

    List<String> getPremByEnsureCode(@Param("ensureCode") String ensureCode);

    List<String> selectPerOrderItemByEnsureCode(@Param("ensureCode") String ensureCode);

    Map<String, Object> selectOrderDutyInfoByRelation(Map<String, String> map);

    Map<String, Object> selectOrderOptDutyInfoByRelation(Map<String, String> map);

    FCOrderItem selectBygrpContNoAndOrderNo(Map<String, String> map);


    int insertShareRelaSelective(FcOrderItemShareRela record);

    /**
     * 查找有效的 且未锁定的 分享链接。设置为无效的。
     *
     * @param orderItemNo
     * @return
     */
    int updateShareIsLock(@Param("orderItemNo") String orderItemNo);

    /**
     * 查找有效的 且未锁定的 分享链接。设置为已锁定的。
     *
     * @param orderItemNo
     * @return
     */
    int updateShareToLock(@Param("orderItemNo") String orderItemNo);

    /**
     * 根据分享流水号查找分享信息
     *
     * @param shareDetalNo
     * @return
     */
    FcOrderItemShareRela selectShareRela(@Param("shareDetalNo") String shareDetalNo);

    FcOrderItemShareRela selectShareRelaByorderiterNo(@Param("orderItemNo") String orderItemNo);

    /**
     * 根据分享流水号更新分享链接为无效的
     *
     * @param shareDetalNo
     * @return
     */
    int updateShareStatus(@Param("shareDetalNo") String shareDetalNo);

    /**
     * 查询福利下子订单数
     *
     * @param ensureCode
     */
    int selectOrderItemCountByEnsureCode(String ensureCode);

    /**
     * 查询福利下子订单数
     *
     * @param ensureCode
     */
    int selectFamilyCountByEnsureCode(String ensureCode);

    void updateByOrderNo(FCOrderItem fcOrderItem);

    /**
     * 根据订单号更新团单号*
     *
     * @param grpContNo   团体单号
     * @param orderNoList 订单集合
     */
    void updateByGrpContNo(@Param("grpContNo") String grpContNo, List<String> orderNoList);

    @Select("select  * from  fcorderitem where  OrderNo = #{orderNo} order by OrderItemNo asc limit 1")
    FCOrderItem selectImagesByorderNo(@Param("orderNo") String orderNo);

    List<FCOrderItem> queryOrderitem(String orderItemNo);

    List<OrderItemDetailData> selectByOrderNoAndPerNo(Map<String, Object> params);


    FCOrderItemDetail selectPlanCode(@Param("orderItemNo") String orderItemNo);

    List<FCOrderItem> selectByOrderNoList(List<String> orderNoList);
}