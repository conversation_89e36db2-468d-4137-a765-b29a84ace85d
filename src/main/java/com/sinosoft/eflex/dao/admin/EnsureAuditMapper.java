package com.sinosoft.eflex.dao.admin;

import com.sinosoft.eflex.model.FCEnsure;
import com.sinosoft.eflex.model.FCEnsureConfig;
import com.sinosoft.eflex.model.GetEnsureAuditsReq;
import com.sinosoft.eflex.model.datamanage.EnsureInfo;
import com.sinosoft.eflex.model.datamanage.SelectEnsureInfoListReq;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface EnsureAuditMapper {

    List<HashMap<String, Object>> getEnsureAuditList(Map<String, String> map);

    List<HashMap<String, Object>> getEnsureAuditList1(GetEnsureAuditsReq getEnsureAuditsReq);

    /**
     * 数据管理查询审核通过的福利列表
     * @param selectEnsureInfoListReq
     * @return
     */
    List<EnsureInfo> selectEnsureInfoList(SelectEnsureInfoListReq selectEnsureInfoListReq);

    List<HashMap<String,Object>> getFCEnsureConfigList(Map<String, String> params);

    FCEnsureConfig getFCEnsureConfigInfo(FCEnsureConfig fcEnsureConfig);

    int updateEnsureConfig(FCEnsureConfig fcEnsureConfig);

    int updateEnsureStatus(FCEnsure fcEnsure);

    int delete(String ensureCode);

    List<HashMap<String, Object>> getToReviewEnsure(Map<String, String> map);
    List<HashMap<String, Object>> getDailyplanList(Map<String, String> params);
    List<HashMap<String, Object>> getDailyplanList_orderBytime(Map<String, String> params);
    List<HashMap<String, Object>> getDailyplanList_addPrtNo(Map<String, String> params);
    List<HashMap<String, Object>> getDailyplanList_check(Map<String, String> params);

    /**
     * 查出日常计划 状态为待审核的计划  审核状态为新增计划
     * @param params
     * @return
     */
    List<HashMap<String, Object>> getDailyPlan013(Map<String, String> params);
}