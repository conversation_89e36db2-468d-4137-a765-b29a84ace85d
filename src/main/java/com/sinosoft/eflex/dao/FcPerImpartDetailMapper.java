package com.sinosoft.eflex.dao;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FcPerImpartDetail;

@Repository
public interface FcPerImpartDetailMapper {
    int deleteByPrimaryKey(@Param("orderItemNo") String orderItemNo, @Param("impartVer") String impartVer,
            @Param("impartCode") String impartCode);

    int insert(FcPerImpartDetail record);

    int insertSelective(FcPerImpartDetail record);

    FcPerImpartDetail selectByPrimaryKey(@Param("orderItemNo") String orderItemNo, @Param("impartVer") String impartVer,
            @Param("impartCode") String impartCode);

    int updateByPrimaryKeySelective(FcPerImpartDetail record);

    int updateByPrimaryKey(FcPerImpartDetail record);
}