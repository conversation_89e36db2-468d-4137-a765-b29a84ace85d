package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCPerson;
import com.sinosoft.eflex.model.FCStaffFamilyRela;
import com.sinosoft.eflex.model.Family;
import com.sinosoft.eflex.model.confirmInsure.PeopleInsureInfo;
import com.sinosoft.eflex.model.confirmInsure.SelectFcPersonInfoByPerNoAndPersonIdReq;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface FCPersonMapper {
    int deleteByPrimaryKey(String personID);

    int insert(FCPerson record);

    int insertSelective(FCPerson record);

    FCPerson selectByPrimaryKey(String personID);

    FCPerson selectPersonInfoByOrderitemNo(String orderItemNo);
    
    FCPerson selectByPrimaryKey1(String personID);
    
    List<FCPerson> selectInsuredPerson(Map<String, Object> map);
    
    List<FCPerson> selectPersonByPerno(String perno);
    
    List<FCPerson> selectPersonByPerno1(String perno);

    List<Map<String,String>> selectPersonByPernoNotPerNo(String perno);
    
    int updateByPrimaryKeySelective(FCPerson record);

    int updateByPrimaryKey(FCPerson record);

    List<FCPerson> selectByExample(Map<String, Object> map);

    int updateByPersonId(FCPerson fcPerson);

    List<FCPerson> selectIDNo(String IDNo);
    FCPerson selectPersonByensure(@Param("EnsureCode")String EnsureCode);

    int updatePersonList(@Param(value = "list")  List<Family> list);

    List<FCPerson> selectByIDNo(Family family);

    int updateFcPerSon(@Param("params")Map<String,String> map);

    int insertMap(HashMap<String,String> record);

    Integer checkStuIdNoIsExists(Map<String,String> map);

    Integer checkStuIdNoIsExistsTwo(Map<String,String> map);
    
    List<String> checkNewPersonIsExists(Map<String,String> map);
    
    Integer checkPerIdNoIsExists(Map<String,String> map);

    FCPerson getGrpPersonID(Map<String,String> map);

    FCPerson getPerPersonID(Map<String,String> map);

    FCPerson getFamPersonID(Map<String,String> map);

    int updateFcPerSonStudent(@Param("params")Map<String,String> map);

    int insertFCDefaultPlanStudent(List<HashMap<String,String>> list);

    List<String> getAllPerson(String personID);
    
    List<String> getEmployPersonId(String perNo);

    FCPerson getFcPersonInfoByPerNo(@Param("perNo")String perNo,@Param("personID")String personID);

    @Select("select count(*) from fcperson per,fcstafffamilyrela sta " +
            "where per.personid = sta.personid and per.idno = #{idno} and perno = #{perid}")
    int selectByIdNo(@Param("idno") String idno,@Param("perid") String perid);

    @Select("select  * from fcstafffamilyrela where PerNo=  #{perid} and  PersonID!=#{personId}")
    List<FCStaffFamilyRela> selectByIdNo2(@Param("perid") String perid,@Param("personId") String personId);

    @Select("select  count(*) from fcperson where IDNo= #{idno} and PersonID=#{personId}")
    int selectByIdNo3(@Param("personId") String personId,@Param("idno") String idno);


    /**
     * 根据正式表客户号查询个人客户表信息
     * @param perNo
     * @return
     */
    FCPerson selectFcpersonByPerNo(String perNo);

    /**
     * 根据证件号查询所有客户
     * @param idNo
     * @return
     */
    List<FCPerson> selectFcPersonByIdNo(String idNo);

    /**
     * 查询员工信息 通过 perNo personId relation
     * @param map
     * @return
     */
    List<Map<String, String>> selectFcPersonInfoByParams(Map<String,String> map);
    List<Map<String, String>> selectFcPersonInfoByParam(Map<String,String> map);
    List<Map<String, String>> selectInfoFcPersonInfoByParams(Map<String,String> map);
    List<Map<String, String>> selectMainFcPersonInfoByParams(Map<String,String> map);
    List<Map<String, String>> selectFcPersonAndSalary(Map<String,String> map);
    List<Map<String, String>> selectFcPersonAndSalaryItemNo(Map<String,String> map);
    /**
     * 查询 家属信息，根据 perno  relation  。不分企业。
     * @param map
     * @return
     */
    List<Map<String, String>> selectFcPersonInfosByParams(Map<String,String> map);
 /**
     * 查询员工姓名信息 通过 perNo personId relation
     * @param map
     * @return
     */
    List<Map<String, String>> selectfcPersoninfo(Map<String,String> map);
    List<Map<String, String>> selectfcPersoninfos(Map<String,String> map);
    int updateByPrimaryKeySelectiveNoidno(FCPerson record);

    /**
     * 查询人员信息
     * 
     * @param selectFcPersonInfoByPerNoAndPersonIdReq
     * @return
     */
    PeopleInsureInfo selectFcPersonInfoByPernoAndPersonId(
            SelectFcPersonInfoByPerNoAndPersonIdReq selectFcPersonInfoByPerNoAndPersonIdReq);

    List<FCPerson> selectByPersonIds(List<String> personIds);

    void updateByIdNo(Map<String, Object> params);

    FCPerson selectFcPersonByPersonId(@Param("personID") String personID);

    void updateBatchById(@Param("list") List<FCPerson> fcPersonList);

    FCPerson selectFcPersonByIdNoAndPerNo(@Param("idNo")String idNo, @Param("grpNo")String grpNo);

    FCPerson checkPhoneFcPerson(@Param("idNoList")List<String> idNoList, @Param("mobilePhone")String mobilePhone);

    void updateJuvenilePhone(@Param("idNo")String idNo);
}