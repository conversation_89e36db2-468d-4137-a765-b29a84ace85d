package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCBusPersonType;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FCBusPersonTypeMapper {
    int deleteByPrimaryKey(@Param("ensureCode") String ensureCode, @Param("gradeLevelCode") String gradeLevelCode);

    int deleteByEnsureCode(@Param("ensureCode") String ensureCode);

    int insert(FCBusPersonType record);

    int insertSelective(FCBusPersonType record);

    FCBusPersonType selectByPrimaryKey(Map<String, String> map);

    List<FCBusPersonType> selectByEnsurecode(String ensureCode);

    int updateByPrimaryKeySelective(FCBusPersonType record);

    int updateByPrimaryKey(FCBusPersonType record);

    int insertFCBusPersonTypeList(List<FCBusPersonType> fcBusPersonTypeList);

    /**
     * 根据企业号查询企业的员工职级
     *
     * @param grpNo
     * @return
     */
    List<FCBusPersonType> selectByGrpNo(String grpNo);

    /**
     * 根据企业客户号和职级编码删除
     *
     * @param grpNo
     * @param orderNum
     * @return
     */
    int deleteByGrpNoAndOrderNum(@Param("grpNo") String grpNo, @Param("ensureCode") String ensureCode, @Param("orderNum") String orderNum);

    /**
     * 修改职级 根据原职级
     *
     * @param params
     * @return
     */
    int updateByParams(Map<String, String> params);

    /**
     * 查询所有职级
     * @param grpNo
     * @return
     */
    List<String> selectAllLevelCodeByGrpNo(String grpNo);

    /**
     * 根据职级编码查询职级顺序
     * @param grpNo
     * @param levelCode
     * @return
     */
    List<String> selectOrderNum(@Param("grpNo") String grpNo, @Param("levelCode") String levelCode);

    /**
     * 职级下拉框
     * @param grpNo
     * @return
     */
    List<Map<String, String>> selectAllRanks(String grpNo);

    /**
     * 根据 职级顺序号查找职级编码
     * @param levelCode
     * @param grpNo
     * @return
     */
    List<String> selectLevelNameByGrpNo(@Param("levelCode") String levelCode,@Param("grpNo")String grpNo);

}