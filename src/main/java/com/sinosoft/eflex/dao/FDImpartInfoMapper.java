package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FDImpartInfo;

public interface FDImpartInfoMapper {
    int deleteByPrimaryKey(String impartCode);

    int insert(FDImpartInfo record);

    int insertSelective(FDImpartInfo record);

    FDImpartInfo selectByPrimaryKey(String impartCode);

    int updateByPrimaryKeySelective(FDImpartInfo record);

    int updateByPrimaryKey(FDImpartInfo record);

}