package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCPlanRiskDuty;
import com.sinosoft.eflex.model.FPInsureEflexPlan;
import com.sinosoft.eflex.model.FcInsureEflexPlan;
import com.sinosoft.eflex.model.insureEflexPlanPage.GetInsureRiskInfoReq;
import com.sinosoft.eflex.model.insureEflexPlanPage.GetInsureRiskInfoResp;
import org.apache.ibatis.annotations.Flush;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FPInsureEflexPlanMapper {
    int deleteByPrimaryKey(String insureElfexPlanNo);

    int deletefPInsureEflexPlan(@Param("personId") String personId,@Param("perNo") String perNo,@Param("ensureCode") String ensureCode);
    
    int deletefPEflexCheckRuleEflexPlan(@Param("personId") String personId,@Param("perNo") String perNo,@Param("ensureCode") String ensureCode);

    int insert(FPInsureEflexPlan record);

    int updateInsureState(Map<String, Object> params);

    List<Map<String, Object>> getTotlePrem(Map<String, Object> params);

    List<FCPlanRiskDuty> selectDutyListBY_15070(Map<String, Object> params);
    
    List<FCPlanRiskDuty> selectCheckRuleDutyListBY_15070(Map<String, Object> params);

    List<FCPlanRiskDuty> selectDutyListBY_17050(Map<String, Object> params);
    
    List<FCPlanRiskDuty> selectCheckRuleDutyListBY_17050(Map<String, Object> params);

    List<FCPlanRiskDuty> selectDutyListBY_other(Map<String, Object> params);
    
    List<FCPlanRiskDuty> selectCheckRuleDutyListBY_other(Map<String, Object> params);

    List<String> selectRiskCodeList(@Param("ensureCode") String ensureCode,@Param("perNo") String perNo,@Param("personId") String personId);
    
    List<String> selectCheckRuleRiskCodeList(@Param("ensureCode") String ensureCode,@Param("perNo") String perNo,@Param("personId") String personId);
    
    List<Map<String, Object>> getAllSaveFamily(@Param("ensureCode") String ensureCode,@Param("perNo") String perNo);

    List<String> getPersonAmountGrageCode(Map<String, Object> params);

    List<Map<String, Object>> getNoInsuredFamilyInfo(@Param("ensureCode") String ensureCode,@Param("perNo") String perNo);

    List<Map<String, String>> checkInfoIsExist(Map<String, Object> params);

    /**
     * 获取已经保存的投保险种信息
     * 
     * @param getInsureRiskInfoReq
     * @return
     */
    List<GetInsureRiskInfoResp> getInsureRiskInfo(GetInsureRiskInfoReq getInsureRiskInfoReq);

    List<FcInsureEflexPlan> getFcInsureEflexPlanList(Map<String, Object> params);

    int saveFcInsureEflexPlanList(List<FcInsureEflexPlan> fcInsureEflexPlans);

    int saveFcInsureEflexPlanOptional(Map<String, Object> params);

    int saveFcInsureEflexPlan(Map<String, Object> params);

    List<Map<String, String>> getPersonIdList(@Param("ensureCode") String ensureCode,@Param("perNo") String perNo);

    int insertSelective(FPInsureEflexPlan record);

    int insertfPInsureEflexPlan(List<FPInsureEflexPlan> fPInsureEflexPlanList);
    
    int insertfPEflexCheckRuleEflexPlan(List<FPInsureEflexPlan> fPInsureEflexPlanList);

    FPInsureEflexPlan selectByPrimaryKey(String insureElfexPlanNo);

    int updateByPrimaryKeySelective(FPInsureEflexPlan record);

    int deleteAllFcInsureEflexPlan(String orderNo);
    
    int deleteAllFcInsureEflexPlanOptional(String orderNo);
    
    int deleteFcInsureEflexPlan(@Param("orderNo") String orderNo,@Param("personId") String personId);
    
    int deleteFcInsureEflexPlanOptional(@Param("orderNo") String orderNo,@Param("personId") String personId);
    
    int updateByPrimaryKey(FPInsureEflexPlan record);

    List<Map<String,String>> selectDutyListByPersonID(@Param("orderItemNo") String orderItemNo);

    int deleteByOrderNo(String orderNo);

    //判断家属是否已经投保
    Integer selectStaffFamilyInsured(@Param("perNo")String perNo,@Param("personId") String personId);
}