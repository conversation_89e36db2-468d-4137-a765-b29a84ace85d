package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCOrderPay;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Repository
public interface FCOrderPayMapper {
    int deleteByPrimaryKey(String payNo);

    int insert(FCOrderPay record);

    int insertSelective(FCOrderPay record);

    FCOrderPay selectByPrimaryKey(String payNo);

    int updateByPrimaryKeySelective(FCOrderPay record);

    int updateByPrimaryKey(FCOrderPay record);

    /**
     * 查询子订单的支付信息
     * @param orderNo
     * @return
     */
    Map<String, String> selectByOrderNo(String orderNo);
}