package com.sinosoft.eflex.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FCEdorAddPlanInfo;

@Repository
public interface FCEdorAddPlanInfoMapper {
    int deleteByPrimaryKey(String edorAddPlanSN);

    int insert(FCEdorAddPlanInfo record);

    int insertSelective(FCEdorAddPlanInfo record);

    FCEdorAddPlanInfo selectByPrimaryKey(String edorAddPlanSN);

    int updateByPrimaryKeySelective(FCEdorAddPlanInfo record);

    int updateByPrimaryKey(FCEdorAddPlanInfo record);

    int insertEdorAddPlanList(List<FCEdorAddPlanInfo> fcEdorAddPlanInfoList);

    FCEdorAddPlanInfo getPlanInfoByPlanCode(@Param("batch") String batch,@Param("planCode") String planCode);

    /**
     * 查询保单下计划信息
     * 
     * @param grpContNo
     * @return
     */
    List<FCEdorAddPlanInfo> getEdorPlanListByGrpContNo(@Param("grpContNo") String grpContNo);

    /**
     * 删除保单下的计划信息
     * 
     * @param grpContNo
     * @return
     */
    int deleteByGrpContNo(@Param("grpContNo") String grpContNo);

    /**
     * 获取保单下的计划信息
     * 
     * @param grpContNo
     * @param planCode
     * @return
     */
    FCEdorAddPlanInfo selectEdorPlanInfo(@Param("grpContNo") String grpContNo, @Param("planCode") String planCode);

}