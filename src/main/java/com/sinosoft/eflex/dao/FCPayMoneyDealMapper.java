package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCPayMoneyDeal;
import org.apache.ibatis.annotations.Param;

public interface FCPayMoneyDealMapper {
    int deleteByPrimaryKey(@Param("orderNo") String orderNo, @Param("payFlowNo") String payFlowNo);

    int insert(FCPayMoneyDeal record);

    int insertSelective(FCPayMoneyDeal record);

    FCPayMoneyDeal selectByPrimaryKey(@Param("orderNo") String orderNo, @Param("payFlowNo") String payFlowNo);

    int updateByPrimaryKeySelective(FCPayMoneyDeal record);

    int updateByPrimaryKey(FCPayMoneyDeal record);
}