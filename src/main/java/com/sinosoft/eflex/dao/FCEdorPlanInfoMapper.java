package com.sinosoft.eflex.dao;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FCEdorPlanInfo;

@Repository
public interface FCEdorPlanInfoMapper {
    int deleteByPrimaryKey(String edorPlanSN);

    int insert(FCEdorPlanInfo record);

    int insertSelective(FCEdorPlanInfo record);

    FCEdorPlanInfo selectByPrimaryKey(String edorPlanSN);

    int updateByPrimaryKeySelective(FCEdorPlanInfo record);

    int updateByPrimaryKey(FCEdorPlanInfo record);

    int exitPlanCodeNum(Map<String,Object> planMap);

    int deleteBatch(String batch);

    int insertPlan(List<FCEdorPlanInfo> list);

    List<FCEdorPlanInfo> selectRiskListByPlan(Map<String,Object> planMap);

    List<FCEdorPlanInfo> selectRiskDutyListByPlanRisk(Map<String,Object> planMap);
    
    Double selectPermByPlanCode(Map<String,String> map);
}