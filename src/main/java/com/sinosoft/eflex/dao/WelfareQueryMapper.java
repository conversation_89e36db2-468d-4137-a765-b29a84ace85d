package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCStaffFamilyRela;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface WelfareQueryMapper {

    HashMap<String,Object> selectfamilyInfo(String perNo);

    List<Map<String,String>> findFamilyName(@Param("personId") String personId);
}
