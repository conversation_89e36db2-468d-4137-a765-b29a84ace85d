package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCInform;
import com.sinosoft.eflex.model.FCInformWithBLOBs;

/**
 * 健康告知表
 * <AUTHOR>
 *
 */
public interface FCInformMapper {
    int deleteByPrimaryKey(String informNumber);

    int insert(FCInformWithBLOBs record);

    int insertSelective(FCInformWithBLOBs record);

    FCInformWithBLOBs selectByPrimaryKey(String informNumber);

    int updateByPrimaryKeySelective(FCInformWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(FCInformWithBLOBs record);

    int updateByPrimaryKey(FCInform record);
}