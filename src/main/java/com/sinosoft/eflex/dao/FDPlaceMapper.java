package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FDPlace;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FDPlaceMapper {
    int deleteByPrimaryKey(String placeCode);

    int insert(FDPlace record);

    int insertSelective(FDPlace record);

    FDPlace selectByPrimaryKey(String placeCode);

    int updateByPrimaryKeySelective(FDPlace record);

    int updateByPrimaryKey(FDPlace record);

    List<Map<String,Object>> selectCodeType(Map<String,Object> map);

    @Select("select placeType,placeCode,placeName,upplaceCode from FDPlace where upplaceCode = #{placeCode}")
    List<FDPlace> getThr(@Param("placeCode") String placeCode);

    @Select("select placeType,placeCode,placeName,upplaceCode from FDPlace where upplaceCode is null")
    List<FDPlace> getParent();

    /**
     * 根据编码查地区名称
     * @param placeCode
     * @return
     */
    String selectPlaceNameByPlaceCode(String placeCode);

    String selectLike(@Param("substring") String substring);
}