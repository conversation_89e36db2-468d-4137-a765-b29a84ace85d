package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCHealthDesign;
import com.sinosoft.eflex.model.FCHealthDesignDetail;
import com.sinosoft.eflex.model.FCPlanHealthDesignRela;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface FCHealthDesignMapper {
    int deleteByPrimaryKey(String designNo);

    int insert(FCHealthDesign record);

    int insertSelective(FCHealthDesign record);

    FCHealthDesign selectByPrimaryKey(String designNo);

    int updateByPrimaryKeySelective(FCHealthDesign record);

    int updateByPrimaryKey(FCHealthDesign record);

    //查询方案信息
    List<Map<String,Object>> selectPlanHealthInfoByParams(Map<String, String> params);

    //查询健康告知方案详情
    List<FCHealthDesignDetail> selectHealthDesignDetail(Map<String,String> params);
    List<FCHealthDesignDetail> selectHealthDesignDetailByGrpNo(Map<String,String> params);

    //查询福利编号是否绑定方案
    FCPlanHealthDesignRela selectPlanByEnsureCode(String ensureCode);

    //根据designNo查询福利方案
    Map<String,String> selectPlanByDesignNo(String designNo);

    //查询方案是否绑定方案详情
    int selectDetailByDesignNo(String designNo);
}