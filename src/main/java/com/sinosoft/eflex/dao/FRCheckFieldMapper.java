package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FRCheckField;
import com.sinosoft.eflex.model.FRCheckFieldKey;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FRCheckFieldMapper {
    int deleteByPrimaryKey(FRCheckField<PERSON>ey key);

    int insert(FRCheck<PERSON>ield record);

    int insertSelective(FRCheckField record);

    FRCheckField selectByPrimaryKey(FRCheckField<PERSON>ey key);

    int updateByPrimaryKeySelective(FRCheckField record);

    int updateByPrimaryKey(FRCheckField record);

    //根据计划编码planCode查询投保规则
    //List<FRCheckField> selectListByPlanCode();
    //根据参数查询计算规则(固定计划)
    List<FRCheckField> selectListByParams(Map<String, Object> params);
    //根据参数查询计算规则(弹性计划)
    List<FRCheckField> selectEflexListByParams(Map<String, Object> params);
}