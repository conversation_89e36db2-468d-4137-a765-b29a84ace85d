package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FcDutyGradeOptionalAmountInfo;
import com.sinosoft.eflex.model.FcDutyGroupDeductible;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FcDutyGroupDeductibleMapper {
    int deleteByPrimaryKey(@Param("amountGrageCode") String amountGrageCode, @Param("deductible") Double deductible);

    int insert(FcDutyGroupDeductible record);

    int insertSelective(FcDutyGroupDeductible record);

    List<FcDutyGroupDeductible> selectByPrimaryKey(Map<String,String> map);

    int updateByPrimaryKeySelective(FcDutyGroupDeductible record);

    int updateByPrimaryKey(FcDutyGroupDeductible record);

    int deleteByEnsureCode(Map<String,String> map);

    int insertDutyDeductibleList(@Param("list")List<FcDutyGroupDeductible> list);
}