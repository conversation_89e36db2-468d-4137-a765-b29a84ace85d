package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FDPwdHist;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FDPwdHistMapper {
    int deleteByPrimaryKey(String passWordSN);

    int insert(FDPwdHist record);

    int insertSelective(FDPwdHist record);

    FDPwdHist selectByPrimaryKey(String passWordSN);

    int updateByPrimaryKeySelective(FDPwdHist record);

    int updateByPrimaryKey(FDPwdHist record);

    List<FDPwdHist> selectPwdByUserno(String userNo);

    /**
     * 查询最新的一条密码修改的记录
     */
    FDPwdHist selectNewPwdHist(String userNo);

}