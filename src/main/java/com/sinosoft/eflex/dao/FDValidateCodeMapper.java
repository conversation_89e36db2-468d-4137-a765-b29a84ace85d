package com.sinosoft.eflex.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.sinosoft.eflex.model.FDValidateCode;

@Repository
public interface FDValidateCodeMapper {
    int deleteByPrimaryKey(String validatecodesn);

    int insert(FDValidateCode record);

    int insertSelective(FDValidateCode record);

    FDValidateCode selectByPrimaryKey(String validatecodesn);

    int updateByPrimaryKeySelective(FDValidateCode record);

    int updateByPrimaryKey(FDValidateCode record);
    
    FDValidateCode selectLastCodeByUserNo(@Param("userNo") String userNo, @Param("codeType") String codeType);

    List<FDValidateCode> selectByUserNo(String userNo);
}