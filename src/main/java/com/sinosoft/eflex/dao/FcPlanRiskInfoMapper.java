package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FcPlanRiskInfo;
import com.sinosoft.eflex.model.insureEflexPlanPage.InsureRiskInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FcPlanRiskInfoMapper {
    int deleteByPrimaryKey(@Param("ensureCode") String ensureCode, @Param("riskCode") String riskCode, @Param("riskType") String riskType);

    int insert(FcPlanRiskInfo record);

    int insertSelective(FcPlanRiskInfo record);

    FcPlanRiskInfo selectByPrimaryKey(@Param("ensureCode") String ensureCode, @Param("riskCode") String riskCode, @Param("riskType") String riskType);

    int updateByPrimaryKeySelective(FcPlanRiskInfo record);

    int updateByPrimaryKey(FcPlanRiskInfo record);

    int updateByRiskCode(FcPlanRiskInfo record);

    List<FcPlanRiskInfo> getAddedRiskInfo(Map<String,String> map);

    List<FcPlanRiskInfo> getAddedRiskInfoBy15070(String ensureCode);

    int deleteByEnsureCodeRiskCode(Map<String,String> map);

    /**
     * 弹性计划查询使用
     * @param ensureCode
     * @param riskCode
     * @return
     */
    FcPlanRiskInfo getFeeRatioByEnsureCodeAndRiskCode(@Param("ensureCode") String ensureCode, @Param("riskCode") String riskCode);

    /**
     * 签单逻辑封装数据调用
     * @param ensureCode
     * @param riskCode
     * @return
     */
    FcPlanRiskInfo getFeeRatioByEnsureCodeAndRiskCodeByInsureSign(@Param("ensureCode") String ensureCode, @Param("riskCode") String riskCode);

    /**
     * 查询停售的计划险种信息
     * @param ensureCode
     */
    List<String> selectStopSaleRiskCodeByEnsureCode(String ensureCode);

    /**
     * 根据福利编码，查询弹性计划险种信息
     * 
     * @param ensureCode
     * @return
     */
    List<InsureRiskInfo> selectRiskInfoByEnsureCode(String ensureCode);

}