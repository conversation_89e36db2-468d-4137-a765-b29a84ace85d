package com.sinosoft.eflex.dao;

import com.sinosoft.eflex.model.FCEnsure;
import com.sinosoft.eflex.model.FCOrderInsured;
import com.sinosoft.eflex.model.confirmInsure.PeopleInsureInfo;
import com.sinosoft.eflex.model.dailyplan.SelectBaseInsuredPeopleReq;
import com.sinosoft.eflex.model.dailyplan.SelectBaseInsuredPeopleResp;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 被保人表
 */
@Repository
public interface FCOrderInsuredMapper {
    int deleteByPrimaryKey(String orderItemNo);

    int insert(FCOrderInsured record);

    int insertSelective(FCOrderInsured record);

    FCOrderInsured selectByPrimaryKey(String orderItemNo);

    int updateByPrimaryKeySelective(FCOrderInsured record);

    int updateByPrimaryKey(FCOrderInsured record);


    List<FCOrderInsured> selectList(Map<String, Object> params);

    /**
     * 投保清单导出excel
     *
     * @param orderNo
     * @param orderItemNo
     * @return
     */
    FCOrderInsured selectInsuredDetail(@Param("orderNo") String orderNo, @Param("orderItemNo") String orderItemNo);

    List<FCOrderInsured> selectOrderNo(String orderNo);

    List<FCOrderInsured> selectListPlan(Map<String, Object> map);

    int deleteByOrderNo(String orderNo);

    FCEnsure getFcensureByPersonID(Map<String, Object> map);

    FCOrderInsured selectOrderItemNo(@Param("grpOrderNo") String grpOrderNo, @Param("orderNo") String orderNo, @Param("personID") String personID);

    int updateByPersonID(FCOrderInsured fcOrderInsured);

    int deleteByPersonId(@Param("orderNo") String orderNo, @Param("personId") String personId);

    List<Map<String, Object>> getOrderItemInfoList(@Param("orderNo") String orderNo, @Param("perNo") String perNo);

    FCOrderInsured selectInsuredByPersonID(Map<String, String> map);

    FCEnsure getFcensureByParams(Map<String, Object> map);

    List<Map<String, String>> queryEfleInsuredDetail(Map<String, String> map);

    List<Map<String, String>> getInsuredOrderInfo(@Param("perNo") String perNo, @Param("personID") String orderpersonIDNo);

    int updateInPhoneByNoCommitCore(FCOrderInsured fcOrderInsured);

    /**
     * 日常计划--投保清单查询（一个人可能有多个orderItemNo）
     *
     * @param params
     * @return
     */
    List<Map<String, String>> selectDailyInsuredDetail(Map<String, String> params);

    /**
     * 判断家属是否已经投保
     *
     * @param personID
     * @return
     */
    Integer selectStaffFamilyInsured(String personID);

    Integer selectStaffFamilyInsuredAndOrder(String personID);

    Map<String, String> selectStaffFamilyInsuredOrderInfo(String personID);

    /**
     * 查询该员工是否存在基础单
     *
     * @param iDNo
     * @param grpOrderNo
     * @return
     */
    Integer selectPeopleNum(@Param("iDNo") String iDNo, @Param("grpOrderNo") String grpOrderNo);

    /**
     * 查询被保人信息
     *
     * @param orderItemNo
     * @return
     */
    Map<String, String> selectByOrderItemNo(String orderItemNo);

    Map<String, String> selectPersonByOrderItemNo(String personID);

    Integer selectByPersonID(@Param("personID") String personID);

    /**
     * 根据 福利编号 查询签单的基础单的所有被保人信息。（用于签单封装数据的时候）
     *
     * @param ensureCode
     * @return
     */
    List<FCOrderInsured> selectBasePerple(@Param("ensureCode") String ensureCode);

    /**
     * 获取重新试算的人员信息
     *
     * @param selectBaseInsuredPeopleReq
     * @return
     */
    List<SelectBaseInsuredPeopleResp> selectBaseInsuredPeople(SelectBaseInsuredPeopleReq selectBaseInsuredPeopleReq);

    /**
     * 投保确认信息加载
     *
     * @param orderNo
     * @return
     */
    List<PeopleInsureInfo> selectPeopleInsureInfo(String orderNo);

    /**
     * 查询保单下人员是否已经存在
     */
    int selectOrderInsured(FCOrderInsured fcOrderInsured);

    /**
     * 查询主被保险人的信息
     */
    int selectMianOrderInsured(FCOrderInsured fcOrderInsured);

    List<FCOrderInsured> selectByIdNo(@Param("idNo") String idNo);

    void updateByIdNo(Map<String, Object> params);

    List<Map<String, Object>> getOrderItemCustomerInfoList(@Param("ensureCode") String ensureCode);

    void updateBatchById(@Param("list") List<FCOrderInsured> fcOrderInsuredList);

    FCOrderInsured selectByGrpOrderNoAndIdNo(@Param("grpOrderNo") String grpOrderNo, @Param("idNo") String idNo);

    FCOrderInsured selectByPersonIDAndIdNo(@Param("personID") String personID, @Param("idNo") String idNo);

    FCOrderInsured selectByIdNoAndGrpOrderNo(@Param("idNo") String idNo, @Param("grpOrderNo") String grpOrderNo);
    /**
     * 增加成年人校验
     */
    List<FCOrderInsured> selectByOrderItemNoList(List<String> orderItemNo);
}