<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FdComMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FdCom">
        <id column="manageCom" jdbcType="VARCHAR" property="manageCom"/>
        <result column="outComCode" jdbcType="VARCHAR" property="outComCode"/>
        <result column="manageComName" jdbcType="VARCHAR" property="manageComName"/>
        <result column="shortName" jdbcType="VARCHAR" property="shortName"/>
        <result column="englishName" jdbcType="VARCHAR" property="englishName"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="zipcode" jdbcType="VARCHAR" property="zipcode"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="fax" jdbcType="VARCHAR" property="fax"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="webAddress" jdbcType="VARCHAR" property="webAddress"/>
        <result column="satrapName" jdbcType="VARCHAR" property="satrapName"/>
        <result column="comCodeisc" jdbcType="VARCHAR" property="comCodeisc"/>
        <result column="otherComcode" jdbcType="VARCHAR" property="otherComcode"/>
        <result column="comNature" jdbcType="VARCHAR" property="comNature"/>
        <result column="comGrade" jdbcType="VARCHAR" property="comGrade"/>
        <result column="comAreaType" jdbcType="VARCHAR" property="comAreaType"/>
        <result column="upComCode" jdbcType="VARCHAR" property="upComCode"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="county" jdbcType="VARCHAR" property="county"/>
        <result column="findb" jdbcType="VARCHAR" property="findb"/>
        <result column="comCode" jdbcType="VARCHAR" property="comCode"/>
        <result column="serviceName" jdbcType="VARCHAR" property="serviceName"/>
        <result column="servicePhone" jdbcType="VARCHAR" property="servicePhone"/>
        <result column="servicePostAddress" jdbcType="VARCHAR" property="servicePostAddress"/>
        <result column="prefectUralLevelcity" jdbcType="VARCHAR" property="prefectUralLevelcity"/>
        <result column="segment1" jdbcType="VARCHAR" property="segment1"/>
        <result column="segment2" jdbcType="VARCHAR" property="segment2"/>
        <result column="segment3" jdbcType="VARCHAR" property="segment3"/>
        <result column="segment4" jdbcType="VARCHAR" property="segment4"/>
        <result column="segment5" jdbcType="VARCHAR" property="segment5"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    manageCom, outComCode, manageComName, shortName, englishName, address, zipcode, phone,
    fax, email, webAddress, satrapName, comCodeisc, otherComcode, comNature, comGrade, 
    comAreaType, upComCode, province, city, county, findb, comCode, serviceName, servicePhone, 
    servicePostAddress, prefectUralLevelcity, segment1, segment2, segment3, segment4, 
    segment5, MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdcom
        where manageCom = #{manageCom,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fdcom
    where manageCom = #{manageCom,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FdCom">
    insert into fdcom (manageCom, outComCode, manageComName,
      shortName, englishName, address, 
      zipcode, phone, fax, 
      email, webAddress, satrapName, 
      comCodeisc, otherComcode, comNature, 
      comGrade, comAreaType, upComCode, 
      province, city, county, 
      findb, comCode, serviceName, 
      servicePhone, servicePostAddress, prefectUralLevelcity, 
      segment1, segment2, segment3, 
      segment4, segment5, MakeDate, 
      MakeTime, ModifyDate, ModifyTime
      )
    values (#{manageCom,jdbcType=VARCHAR}, #{outComCode,jdbcType=VARCHAR}, #{manageComName,jdbcType=VARCHAR},
      #{shortName,jdbcType=VARCHAR}, #{englishName,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, 
      #{zipcode,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, #{fax,jdbcType=VARCHAR}, 
      #{email,jdbcType=VARCHAR}, #{webAddress,jdbcType=VARCHAR}, #{satrapName,jdbcType=VARCHAR}, 
      #{comCodeisc,jdbcType=VARCHAR}, #{otherComcode,jdbcType=VARCHAR}, #{comNature,jdbcType=VARCHAR}, 
      #{comGrade,jdbcType=VARCHAR}, #{comAreaType,jdbcType=VARCHAR}, #{upComCode,jdbcType=VARCHAR}, 
      #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, #{county,jdbcType=VARCHAR}, 
      #{findb,jdbcType=VARCHAR}, #{comCode,jdbcType=VARCHAR}, #{serviceName,jdbcType=VARCHAR}, 
      #{servicePhone,jdbcType=VARCHAR}, #{servicePostAddress,jdbcType=VARCHAR}, #{prefectUralLevelcity,jdbcType=VARCHAR}, 
      #{segment1,jdbcType=VARCHAR}, #{segment2,jdbcType=VARCHAR}, #{segment3,jdbcType=VARCHAR}, 
      #{segment4,jdbcType=VARCHAR}, #{segment5,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, 
      #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR}
      )
  </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FdCom">
        insert into fdcom
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="manageCom != null">
                manageCom,
            </if>
            <if test="outComCode != null">
                outComCode,
            </if>
            <if test="manageComName != null">
                manageComName,
            </if>
            <if test="shortName != null">
                shortName,
            </if>
            <if test="englishName != null">
                englishName,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="zipcode != null">
                zipcode,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="fax != null">
                fax,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="webAddress != null">
                webAddress,
            </if>
            <if test="satrapName != null">
                satrapName,
            </if>
            <if test="comCodeisc != null">
                comCodeisc,
            </if>
            <if test="otherComcode != null">
                otherComcode,
            </if>
            <if test="comNature != null">
                comNature,
            </if>
            <if test="comGrade != null">
                comGrade,
            </if>
            <if test="comAreaType != null">
                comAreaType,
            </if>
            <if test="upComCode != null">
                upComCode,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="county != null">
                county,
            </if>
            <if test="findb != null">
                findb,
            </if>
            <if test="comCode != null">
                comCode,
            </if>
            <if test="serviceName != null">
                serviceName,
            </if>
            <if test="servicePhone != null">
                servicePhone,
            </if>
            <if test="servicePostAddress != null">
                servicePostAddress,
            </if>
            <if test="prefectUralLevelcity != null">
                prefectUralLevelcity,
            </if>
            <if test="segment1 != null">
                segment1,
            </if>
            <if test="segment2 != null">
                segment2,
            </if>
            <if test="segment3 != null">
                segment3,
            </if>
            <if test="segment4 != null">
                segment4,
            </if>
            <if test="segment5 != null">
                segment5,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="manageCom != null">
                #{manageCom,jdbcType=VARCHAR},
            </if>
            <if test="outComCode != null">
                #{outComCode,jdbcType=VARCHAR},
            </if>
            <if test="manageComName != null">
                #{manageComName,jdbcType=VARCHAR},
            </if>
            <if test="shortName != null">
                #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="englishName != null">
                #{englishName,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="zipcode != null">
                #{zipcode,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="fax != null">
                #{fax,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="webAddress != null">
                #{webAddress,jdbcType=VARCHAR},
            </if>
            <if test="satrapName != null">
                #{satrapName,jdbcType=VARCHAR},
            </if>
            <if test="comCodeisc != null">
                #{comCodeisc,jdbcType=VARCHAR},
            </if>
            <if test="otherComcode != null">
                #{otherComcode,jdbcType=VARCHAR},
            </if>
            <if test="comNature != null">
                #{comNature,jdbcType=VARCHAR},
            </if>
            <if test="comGrade != null">
                #{comGrade,jdbcType=VARCHAR},
            </if>
            <if test="comAreaType != null">
                #{comAreaType,jdbcType=VARCHAR},
            </if>
            <if test="upComCode != null">
                #{upComCode,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                #{county,jdbcType=VARCHAR},
            </if>
            <if test="findb != null">
                #{findb,jdbcType=VARCHAR},
            </if>
            <if test="comCode != null">
                #{comCode,jdbcType=VARCHAR},
            </if>
            <if test="serviceName != null">
                #{serviceName,jdbcType=VARCHAR},
            </if>
            <if test="servicePhone != null">
                #{servicePhone,jdbcType=VARCHAR},
            </if>
            <if test="servicePostAddress != null">
                #{servicePostAddress,jdbcType=VARCHAR},
            </if>
            <if test="prefectUralLevelcity != null">
                #{prefectUralLevelcity,jdbcType=VARCHAR},
            </if>
            <if test="segment1 != null">
                #{segment1,jdbcType=VARCHAR},
            </if>
            <if test="segment2 != null">
                #{segment2,jdbcType=VARCHAR},
            </if>
            <if test="segment3 != null">
                #{segment3,jdbcType=VARCHAR},
            </if>
            <if test="segment4 != null">
                #{segment4,jdbcType=VARCHAR},
            </if>
            <if test="segment5 != null">
                #{segment5,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FdCom">
        update fdcom
        <set>
            <if test="outComCode != null">
                outComCode = #{outComCode,jdbcType=VARCHAR},
            </if>
            <if test="manageComName != null">
                manageComName = #{manageComName,jdbcType=VARCHAR},
            </if>
            <if test="shortName != null">
                shortName = #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="englishName != null">
                englishName = #{englishName,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="zipcode != null">
                zipcode = #{zipcode,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="fax != null">
                fax = #{fax,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="webAddress != null">
                webAddress = #{webAddress,jdbcType=VARCHAR},
            </if>
            <if test="satrapName != null">
                satrapName = #{satrapName,jdbcType=VARCHAR},
            </if>
            <if test="comCodeisc != null">
                comCodeisc = #{comCodeisc,jdbcType=VARCHAR},
            </if>
            <if test="otherComcode != null">
                otherComcode = #{otherComcode,jdbcType=VARCHAR},
            </if>
            <if test="comNature != null">
                comNature = #{comNature,jdbcType=VARCHAR},
            </if>
            <if test="comGrade != null">
                comGrade = #{comGrade,jdbcType=VARCHAR},
            </if>
            <if test="comAreaType != null">
                comAreaType = #{comAreaType,jdbcType=VARCHAR},
            </if>
            <if test="upComCode != null">
                upComCode = #{upComCode,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                city = #{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                county = #{county,jdbcType=VARCHAR},
            </if>
            <if test="findb != null">
                findb = #{findb,jdbcType=VARCHAR},
            </if>
            <if test="comCode != null">
                comCode = #{comCode,jdbcType=VARCHAR},
            </if>
            <if test="serviceName != null">
                serviceName = #{serviceName,jdbcType=VARCHAR},
            </if>
            <if test="servicePhone != null">
                servicePhone = #{servicePhone,jdbcType=VARCHAR},
            </if>
            <if test="servicePostAddress != null">
                servicePostAddress = #{servicePostAddress,jdbcType=VARCHAR},
            </if>
            <if test="prefectUralLevelcity != null">
                prefectUralLevelcity = #{prefectUralLevelcity,jdbcType=VARCHAR},
            </if>
            <if test="segment1 != null">
                segment1 = #{segment1,jdbcType=VARCHAR},
            </if>
            <if test="segment2 != null">
                segment2 = #{segment2,jdbcType=VARCHAR},
            </if>
            <if test="segment3 != null">
                segment3 = #{segment3,jdbcType=VARCHAR},
            </if>
            <if test="segment4 != null">
                segment4 = #{segment4,jdbcType=VARCHAR},
            </if>
            <if test="segment5 != null">
                segment5 = #{segment5,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where manageCom = #{manageCom,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FdCom">
    update fdcom
    set outComCode = #{outComCode,jdbcType=VARCHAR},
      manageComName = #{manageComName,jdbcType=VARCHAR},
      shortName = #{shortName,jdbcType=VARCHAR},
      englishName = #{englishName,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      zipcode = #{zipcode,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      fax = #{fax,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      webAddress = #{webAddress,jdbcType=VARCHAR},
      satrapName = #{satrapName,jdbcType=VARCHAR},
      comCodeisc = #{comCodeisc,jdbcType=VARCHAR},
      otherComcode = #{otherComcode,jdbcType=VARCHAR},
      comNature = #{comNature,jdbcType=VARCHAR},
      comGrade = #{comGrade,jdbcType=VARCHAR},
      comAreaType = #{comAreaType,jdbcType=VARCHAR},
      upComCode = #{upComCode,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      county = #{county,jdbcType=VARCHAR},
      findb = #{findb,jdbcType=VARCHAR},
      comCode = #{comCode,jdbcType=VARCHAR},
      serviceName = #{serviceName,jdbcType=VARCHAR},
      servicePhone = #{servicePhone,jdbcType=VARCHAR},
      servicePostAddress = #{servicePostAddress,jdbcType=VARCHAR},
      prefectUralLevelcity = #{prefectUralLevelcity,jdbcType=VARCHAR},
      segment1 = #{segment1,jdbcType=VARCHAR},
      segment2 = #{segment2,jdbcType=VARCHAR},
      segment3 = #{segment3,jdbcType=VARCHAR},
      segment4 = #{segment4,jdbcType=VARCHAR},
      segment5 = #{segment5,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where manageCom = #{manageCom,jdbcType=VARCHAR}
  </update>
    <select id="selectAllManageCom" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.SelectAllManageComResp">
    SELECT
      ManageCom manageCom,
      shortName manageComName
    FROM FDCOM
  </select>
</mapper>