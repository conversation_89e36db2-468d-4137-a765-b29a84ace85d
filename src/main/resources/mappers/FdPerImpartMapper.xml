<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FdPerImpartMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FdPerImpart">
        <id column="ImpartVer" jdbcType="VARCHAR" property="impartVer"/>
        <id column="ImpartCode" jdbcType="VARCHAR" property="impartCode"/>
        <result column="ImpartContent" jdbcType="VARCHAR" property="impartContent"/>
        <result column="State" jdbcType="VARCHAR" property="state"/>
        <result column="ImpartParamModle" jdbcType="VARCHAR" property="impartParamModle"/>
        <result column="UwClaimFlag" jdbcType="VARCHAR" property="uwClaimFlag"/>
        <result column="HaveParamFlag" jdbcType="VARCHAR" property="haveParamFlag"/>
        <result column="PrtFlag" jdbcType="VARCHAR" property="prtFlag"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="ImpartType" jdbcType="VARCHAR" property="impartType"/>
        <result column="UpLevel" jdbcType="VARCHAR" property="upLevel"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    ImpartVer, ImpartCode, ImpartContent, State, ImpartParamModle, UwClaimFlag, HaveParamFlag, 
    PrtFlag, Remark, ImpartType, UpLevel, MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
    <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdperimpart
        where ImpartVer = #{impartVer,jdbcType=VARCHAR}
        and ImpartCode = #{impartCode,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="map">
    delete from fdperimpart
    where ImpartVer = #{impartVer,jdbcType=VARCHAR}
      and ImpartCode = #{impartCode,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FdPerImpart">
    insert into fdperimpart (ImpartVer, ImpartCode, ImpartContent, 
      State, ImpartParamModle, UwClaimFlag, 
      HaveParamFlag, PrtFlag, Remark, 
      ImpartType, UpLevel, MakeDate, 
      MakeTime, ModifyDate, ModifyTime
      )
    values (#{impartVer,jdbcType=VARCHAR}, #{impartCode,jdbcType=VARCHAR}, #{impartContent,jdbcType=VARCHAR}, 
      #{state,jdbcType=VARCHAR}, #{impartParamModle,jdbcType=VARCHAR}, #{uwClaimFlag,jdbcType=VARCHAR}, 
      #{haveParamFlag,jdbcType=VARCHAR}, #{prtFlag,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{impartType,jdbcType=VARCHAR}, #{upLevel,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, 
      #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR}
      )
  </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FdPerImpart">
        insert into fdperimpart
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="impartVer != null">
                ImpartVer,
            </if>
            <if test="impartCode != null">
                ImpartCode,
            </if>
            <if test="impartContent != null">
                ImpartContent,
            </if>
            <if test="state != null">
                State,
            </if>
            <if test="impartParamModle != null">
                ImpartParamModle,
            </if>
            <if test="uwClaimFlag != null">
                UwClaimFlag,
            </if>
            <if test="haveParamFlag != null">
                HaveParamFlag,
            </if>
            <if test="prtFlag != null">
                PrtFlag,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="impartType != null">
                ImpartType,
            </if>
            <if test="upLevel != null">
                UpLevel,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="impartVer != null">
                #{impartVer,jdbcType=VARCHAR},
            </if>
            <if test="impartCode != null">
                #{impartCode,jdbcType=VARCHAR},
            </if>
            <if test="impartContent != null">
                #{impartContent,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=VARCHAR},
            </if>
            <if test="impartParamModle != null">
                #{impartParamModle,jdbcType=VARCHAR},
            </if>
            <if test="uwClaimFlag != null">
                #{uwClaimFlag,jdbcType=VARCHAR},
            </if>
            <if test="haveParamFlag != null">
                #{haveParamFlag,jdbcType=VARCHAR},
            </if>
            <if test="prtFlag != null">
                #{prtFlag,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="impartType != null">
                #{impartType,jdbcType=VARCHAR},
            </if>
            <if test="upLevel != null">
                #{upLevel,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FdPerImpart">
        update fdperimpart
        <set>
            <if test="impartContent != null">
                ImpartContent = #{impartContent,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                State = #{state,jdbcType=VARCHAR},
            </if>
            <if test="impartParamModle != null">
                ImpartParamModle = #{impartParamModle,jdbcType=VARCHAR},
            </if>
            <if test="uwClaimFlag != null">
                UwClaimFlag = #{uwClaimFlag,jdbcType=VARCHAR},
            </if>
            <if test="haveParamFlag != null">
                HaveParamFlag = #{haveParamFlag,jdbcType=VARCHAR},
            </if>
            <if test="prtFlag != null">
                PrtFlag = #{prtFlag,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="impartType != null">
                ImpartType = #{impartType,jdbcType=VARCHAR},
            </if>
            <if test="upLevel != null">
                UpLevel = #{upLevel,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where ImpartVer = #{impartVer,jdbcType=VARCHAR}
        and ImpartCode = #{impartCode,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FdPerImpart">
    update fdperimpart
    set ImpartContent = #{impartContent,jdbcType=VARCHAR},
      State = #{state,jdbcType=VARCHAR},
      ImpartParamModle = #{impartParamModle,jdbcType=VARCHAR},
      UwClaimFlag = #{uwClaimFlag,jdbcType=VARCHAR},
      HaveParamFlag = #{haveParamFlag,jdbcType=VARCHAR},
      PrtFlag = #{prtFlag,jdbcType=VARCHAR},
      Remark = #{remark,jdbcType=VARCHAR},
      ImpartType = #{impartType,jdbcType=VARCHAR},
      UpLevel = #{upLevel,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where ImpartVer = #{impartVer,jdbcType=VARCHAR}
      and ImpartCode = #{impartCode,jdbcType=VARCHAR}
  </update>
</mapper>