<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FcEdorItemMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FcEdorItem">
    <id column="EdorBatch" jdbcType="VARCHAR" property="edorBatch" />
    <result column="GrpContNo" jdbcType="VARCHAR" property="grpContNo" />
    <result column="EdorAppNo" jdbcType="VARCHAR" property="edorAppNo" />
    <result column="EdorType" jdbcType="VARCHAR" property="edorType" />
    <result column="EdorState" jdbcType="VARCHAR" property="edorState" />
    <result column="Amount" jdbcType="VARCHAR" property="amount" />
    <result column="Prem" jdbcType="VARCHAR" property="prem" />
    <result column="IsEflexPolicy" jdbcType="VARCHAR" property="isEflexPolicy" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    EdorBatch, GrpContNo, EdorAppNo, EdorType, EdorState, Amount, Prem, IsEflexPolicy, 
    Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcedoritem
    where EdorBatch = #{edorBatch,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcedoritem
    where EdorBatch = #{edorBatch,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FcEdorItem">
    insert into fcedoritem (EdorBatch, GrpContNo, EdorAppNo, 
      EdorType, EdorState, Amount, 
      Prem, IsEflexPolicy, Operator, 
      OperatorCom, MakeDate, MakeTime, 
      ModifyDate, ModifyTime)
    values (#{edorBatch,jdbcType=VARCHAR}, #{grpContNo,jdbcType=VARCHAR}, #{edorAppNo,jdbcType=VARCHAR}, 
      #{edorType,jdbcType=VARCHAR}, #{edorState,jdbcType=VARCHAR}, #{amount,jdbcType=VARCHAR}, 
      #{prem,jdbcType=VARCHAR}, #{isEflexPolicy,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, 
      #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, 
      #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FcEdorItem">
    insert into fcedoritem
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="edorBatch != null">
        EdorBatch,
      </if>
      <if test="grpContNo != null">
        GrpContNo,
      </if>
      <if test="edorAppNo != null">
        EdorAppNo,
      </if>
      <if test="edorType != null">
        EdorType,
      </if>
      <if test="edorState != null">
        EdorState,
      </if>
      <if test="amount != null">
        Amount,
      </if>
      <if test="prem != null">
        Prem,
      </if>
      <if test="isEflexPolicy != null">
        IsEflexPolicy,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="edorBatch != null">
        #{edorBatch,jdbcType=VARCHAR},
      </if>
      <if test="grpContNo != null">
        #{grpContNo,jdbcType=VARCHAR},
      </if>
      <if test="edorAppNo != null">
        #{edorAppNo,jdbcType=VARCHAR},
      </if>
      <if test="edorType != null">
        #{edorType,jdbcType=VARCHAR},
      </if>
      <if test="edorState != null">
        #{edorState,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=VARCHAR},
      </if>
      <if test="prem != null">
        #{prem,jdbcType=VARCHAR},
      </if>
      <if test="isEflexPolicy != null">
        #{isEflexPolicy,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FcEdorItem">
    update fcedoritem
    <set>
      <if test="grpContNo != null">
        GrpContNo = #{grpContNo,jdbcType=VARCHAR},
      </if>
      <if test="edorAppNo != null">
        EdorAppNo = #{edorAppNo,jdbcType=VARCHAR},
      </if>
      <if test="edorType != null">
        EdorType = #{edorType,jdbcType=VARCHAR},
      </if>
      <if test="edorState != null">
        EdorState = #{edorState,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        Amount = #{amount,jdbcType=VARCHAR},
      </if>
      <if test="prem != null">
        Prem = #{prem,jdbcType=VARCHAR},
      </if>
      <if test="isEflexPolicy != null">
        IsEflexPolicy = #{isEflexPolicy,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where EdorBatch = #{edorBatch,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FcEdorItem">
    update fcedoritem
    set GrpContNo = #{grpContNo,jdbcType=VARCHAR},
      EdorAppNo = #{edorAppNo,jdbcType=VARCHAR},
      EdorType = #{edorType,jdbcType=VARCHAR},
      EdorState = #{edorState,jdbcType=VARCHAR},
      Amount = #{amount,jdbcType=VARCHAR},
      Prem = #{prem,jdbcType=VARCHAR},
      IsEflexPolicy = #{isEflexPolicy,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where EdorBatch = #{edorBatch,jdbcType=VARCHAR}
  </update>
  <select id="selectEdorItemInfo" parameterType="com.sinosoft.eflex.model.FcEdorItem" resultType="com.sinosoft.eflex.model.FcEdorItem">
      select
      <include refid="Base_Column_List" />
      from fcedoritem
      where 1=1
      <if test="grpContNo != null">
          and GrpContNo = #{grpContNo,jdbcType=VARCHAR}
      </if>
      <if test="edorAppNo != null">
          and EdorAppNo = #{edorAppNo,jdbcType=VARCHAR}
      </if>
      <if test="edorType != null">
          and EdorType = #{edorType,jdbcType=VARCHAR}
      </if>
      <if test="edorState != null">
          and EdorState = #{edorState,jdbcType=VARCHAR}
      </if>
      <if test="isEflexPolicy != null">
          and IsEflexPolicy = #{isEflexPolicy,jdbcType=VARCHAR}
      </if>
  </select>
</mapper>