<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCPersonMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCPerson">
        <id column="PersonID" jdbcType="VARCHAR" property="personID"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Sex" jdbcType="VARCHAR" property="sex"/>
        <result column="Nativeplace" jdbcType="VARCHAR" property="nativeplace"/>
        <result column="BirthDate" jdbcType="DATE" property="birthDate"/>
        <result column="IDType" jdbcType="VARCHAR" property="IDType"/>
        <result column="IDNo" jdbcType="VARCHAR" property="IDNo"/>
        <result column="idTypeEndDate" jdbcType="DATE" property="idTypeEndDate"/>
        <result column="LevelCode" jdbcType="VARCHAR" property="levelCode"/>
        <result column="Retirement" jdbcType="VARCHAR" property="retirement"/>
        <result column="MobilePhone" jdbcType="VARCHAR" property="mobilePhone"/>
        <result column="Phone" jdbcType="VARCHAR" property="phone"/>
        <result column="OccupationType" jdbcType="VARCHAR" property="occupationType"/>
        <result column="OccupationCode" jdbcType="VARCHAR" property="occupationCode"/>
        <result column="JoinMedProtect" jdbcType="VARCHAR" property="joinMedProtect"/>
        <result column="MedProtectType" jdbcType="VARCHAR" property="medProtectType"/>
        <result column="EMail" jdbcType="VARCHAR" property="EMail"/>
        <result column="ZipCode" jdbcType="VARCHAR" property="zipCode"/>
        <result column="Address" jdbcType="VARCHAR" property="address"/>
        <result column="OpenBank" jdbcType="VARCHAR" property="openBank"/>
        <result column="OpenAccount" jdbcType="VARCHAR" property="openAccount"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="Province" jdbcType="VARCHAR" property="province"/>
        <result column="City" jdbcType="VARCHAR" property="city"/>
        <result column="County" jdbcType="VARCHAR" property="county"/>
        <result column="DetaileAddress" jdbcType="VARCHAR" property="detaileAddress"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        PersonID
        , Name, Sex, Nativeplace,BirthDate, IDType, IDNo,idTypeEndDate, LevelCode,Retirement,MobilePhone, Phone, OccupationType,
    OccupationCode, JoinMedProtect, MedProtectType, EMail, ZipCode, Address, OpenBank, 
    OpenAccount, Operator, OperatorCom,Province,City,County,DetaileAddress, MakeDate, MakeTime, ModifyDate, ModifyTime
    </sql>
    <sql id="Base_Column_List1">
        fc
        .
        PersonID
        , fc.Name, fc.Sex, fc.Nativeplace,fc.BirthDate, fc.IDType, fc.IDNo,fc.idTypeEndDate,fc.MobilePhone, fc.Phone, fc.OccupationType,
    fc.OccupationCode, fc.JoinMedProtect, fc.MedProtectType, fc.EMail, fc.ZipCode, fc.Address, fc.OpenBank, 
    fc.OpenAccount, fc.Operator, fc.OperatorCom,fc.Province,fc.City,fc.County,fc.DetaileAddress, fc.MakeDate, fc.MakeTime, fc.ModifyDate, fc.ModifyTime,fr.relation
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcperson
        where PersonID = #{personID,jdbcType=VARCHAR}
    </select>
    <select id="selectPersonInfoByOrderitemNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcperson
        where PersonID in (select PersonID from fcorderinsured where orderitemno=#{orderItemNo,jdbcType=VARCHAR})
    </select>
    <select id="selectByPrimaryKey1" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCPerson">
        select
        <include refid="Base_Column_List1"/>
        from fcperson fc LEFT JOIN fcstafffamilyrela fr ON fc.personid=fr.personid
        where fc.PersonID = #{personID,jdbcType=VARCHAR} limit 1
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fcperson
        where PersonID = #{personID,jdbcType=VARCHAR}
    </delete>

    <update id="updateFcPerSon" parameterType="java.util.Map">
        update fcperson a,fcperinfofamilytemp b
            INNER JOIN fcstafffamilyrela d
        ON d.personID = a.personID
            INNER JOIN fcperinfo p ON p.perno = d.perno
            set a.name=b.name,
                a.sex=b.sex,
                a.BirthDate=b.BirthDay,
                a.IDType=b.IDType,
                a.IDNo=b.IDNo,
                a.idTypeEndDate=b.idTypeEndDate,
                a.Nativeplace=b.Nativeplace,
                a.MobilePhone=b.Phone,
                a.OccupationType=b.OccupationType,
                a.OccupationCode=b.OccupationCode,
                a.JoinMedProtect=b.JoinMedProtect,
                a.ModifyDate=str_to_date(#{params.currentDate,jdbcType=VARCHAR}, '%Y-%m-%d'),
                a.ModifyTime=#{params.currentTime,jdbcType=VARCHAR}
        where a.idno = b.idno
          and b.SubStaus = '01'
          and b.EnsureCode = #{params.ensureCode,jdbcType=VARCHAR}
          and p.idno = b.idno
          and p.grpNo = #{params.grpNo,jdbcType=VARCHAR}
    </update>

    <insert id="insert" parameterType="com.sinosoft.eflex.model.FCPerson">
        insert into fcperson (PersonID, Name, Sex, Nativeplace,
                              BirthDate, IDType, IDNo, idTypeEndDate, relationship,
                              MobilePhone, Phone, OccupationType,
                              OccupationCode, JoinMedProtect, MedProtectType,
                              EMail, ZipCode, Address,
                              OpenBank, OpenAccount, Operator,
                              OperatorCom, MakeDate, MakeTime,
                              ModifyDate, ModifyTime)
        values (#{personID,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{sex,jdbcType=VARCHAR},
                #{nativeplace,jdbcType=VARCHAR},
                #{birthDate,jdbcType=DATE}, #{IDType,jdbcType=VARCHAR}, #{IDNo,jdbcType=VARCHAR},
                #{idTypeEndDate,jdbcType=VARCHAR}, #{relationship,jdbcType=VARCHAR},
                #{mobilePhone,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, #{occupationType,jdbcType=VARCHAR},
                #{occupationCode,jdbcType=VARCHAR}, #{joinMedProtect,jdbcType=VARCHAR},
                #{medProtectType,jdbcType=VARCHAR},
                #{EMail,jdbcType=VARCHAR}, #{zipCode,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR},
                #{openBank,jdbcType=VARCHAR}, #{openAccount,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
                #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR},
                #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCPerson">
        insert into fcperson
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="personID != null">
                PersonID,
            </if>
            <if test="name != null">
                Name,
            </if>
            <if test="sex != null">
                Sex,
            </if>
            <if test="nativeplace != null">
                Nativeplace,
            </if>
            <if test="birthDate != null">
                BirthDate,
            </if>
            <if test="IDType != null">
                IDType,
            </if>
            <if test="IDNo != null">
                IDNo,
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate,
            </if>
            <if test="levelCode != null">
                LevelCode,
            </if>
            <if test="retirement != null">
                Retirement,
            </if>
            <if test="mobilePhone != null">
                MobilePhone,
            </if>
            <if test="phone != null">
                Phone,
            </if>
            <if test="occupationType != null">
                OccupationType,
            </if>
            <if test="occupationCode != null">
                OccupationCode,
            </if>
            <if test="joinMedProtect != null">
                JoinMedProtect,
            </if>
            <if test="medProtectType != null">
                MedProtectType,
            </if>
            <if test="EMail != null">
                EMail,
            </if>
            <if test="zipCode != null">
                ZipCode,
            </if>
            <if test="address != null">
                Address,
            </if>
            <if test="openBank != null">
                OpenBank,
            </if>
            <if test="openAccount != null">
                OpenAccount,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="county != null">
                county,
            </if>
            <if test="detaileAddress != null">
                detaileAddress,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
            <if test="relationship != null">
                relationship,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="personID != null">
                #{personID,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="birthDate != null">
                #{birthDate,jdbcType=DATE},
            </if>
            <if test="IDType != null">
                #{IDType,jdbcType=VARCHAR},
            </if>
            <if test="IDNo != null">
                #{IDNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeEndDate != null">
                #{idTypeEndDate,jdbcType=DATE},
            </if>
            <if test="levelCode != null">
                #{levelCode,jdbcType=VARCHAR},
            </if>
            <if test="retirement != null">
                #{retirement,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="occupationType != null">
                #{occupationType,jdbcType=VARCHAR},
            </if>
            <if test="occupationCode != null">
                #{occupationCode,jdbcType=VARCHAR},
            </if>
            <if test="joinMedProtect != null">
                #{joinMedProtect,jdbcType=VARCHAR},
            </if>
            <if test="medProtectType != null">
                #{medProtectType,jdbcType=VARCHAR},
            </if>
            <if test="EMail != null">
                #{EMail,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="openBank != null">
                #{openBank,jdbcType=VARCHAR},
            </if>
            <if test="openAccount != null">
                #{openAccount,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                #{county,jdbcType=VARCHAR},
            </if>
            <if test="detaileAddress != null">
                #{detaileAddress,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test="relationship != null">
                #{relationship,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCPerson">
        update fcperson
        <set>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                Sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                Nativeplace = #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="birthDate != null">
                BirthDate = #{birthDate,jdbcType=DATE},
            </if>
            <if test="IDType != null">
                IDType = #{IDType,jdbcType=VARCHAR},
            </if>
            <if test="IDNo != null">
                IDNo = #{IDNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate = #{idTypeEndDate,jdbcType=DATE},
            </if>
            <if test="mobilePhone != null">
                MobilePhone = #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                Phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="occupationType != null">
                OccupationType = #{occupationType,jdbcType=VARCHAR},
            </if>
            <if test="occupationCode != null">
                OccupationCode = #{occupationCode,jdbcType=VARCHAR},
            </if>
            <if test="joinMedProtect != null">
                JoinMedProtect = #{joinMedProtect,jdbcType=VARCHAR},
            </if>
            <if test="medProtectType != null">
                MedProtectType = #{medProtectType,jdbcType=VARCHAR},
            </if>
            <if test="EMail != null">
                EMail = #{EMail,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                ZipCode = #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                Address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="openBank != null">
                OpenBank = #{openBank,jdbcType=VARCHAR},
            </if>
            <if test="openAccount != null">
                OpenAccount = #{openAccount,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                Province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                City = #{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                County = #{county,jdbcType=VARCHAR},
            </if>
            <if test="detaileAddress != null">
                DetaileAddress = #{detaileAddress,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where 1=1
        <if test="personID != null">
            AND PersonID = #{personID,jdbcType=VARCHAR}
        </if>
        <if test="IDNo != null and IDNo != ''">
            AND IDNo = #{IDNo}
        </if>
    </update>
    <update id="updateByPrimaryKeySelectiveNoidno" parameterType="com.sinosoft.eflex.model.FCPerson">
        update fcperson
        <set>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                Sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                Nativeplace = #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="birthDate != null">
                BirthDate = #{birthDate,jdbcType=DATE},
            </if>
            <if test="IDType != null">
                IDType = #{IDType,jdbcType=VARCHAR},
            </if>
            <if test="IDNo != null">
                IDNo = #{IDNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate = #{idTypeEndDate,jdbcType=DATE},
            </if>
            <if test="mobilePhone != null">
                MobilePhone = #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                Phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="occupationType != null">
                OccupationType = #{occupationType,jdbcType=VARCHAR},
            </if>
            <if test="occupationCode != null">
                OccupationCode = #{occupationCode,jdbcType=VARCHAR},
            </if>
            <if test="joinMedProtect != null">
                JoinMedProtect = #{joinMedProtect,jdbcType=VARCHAR},
            </if>
            <if test="medProtectType != null">
                MedProtectType = #{medProtectType,jdbcType=VARCHAR},
            </if>
            <if test="EMail != null">
                EMail = #{EMail,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                ZipCode = #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                Address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="openBank != null">
                OpenBank = #{openBank,jdbcType=VARCHAR},
            </if>
            <if test="openAccount != null">
                OpenAccount = #{openAccount,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                Province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                City = #{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                County = #{county,jdbcType=VARCHAR},
            </if>
            <if test="detaileAddress != null">
                DetaileAddress = #{detaileAddress,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where 1=1
        <if test="personID != null">
            AND PersonID = #{personID,jdbcType=VARCHAR}
        </if>
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCPerson">
        update fcperson
        set Name           = #{name,jdbcType=VARCHAR},
            Sex            = #{sex,jdbcType=VARCHAR},
            Nativeplace    = #{nativeplace,jdbcType=VARCHAR},
            BirthDate      = #{birthDate,jdbcType=DATE},
            IDType         = #{IDType,jdbcType=VARCHAR},
            IDNo           = #{IDNo,jdbcType=VARCHAR},
            MobilePhone    = #{mobilePhone,jdbcType=VARCHAR},
            Phone          = #{phone,jdbcType=VARCHAR},
            OccupationType = #{occupationType,jdbcType=VARCHAR},
            OccupationCode = #{occupationCode,jdbcType=VARCHAR},
            JoinMedProtect = #{joinMedProtect,jdbcType=VARCHAR},
            MedProtectType = #{medProtectType,jdbcType=VARCHAR},
            EMail          = #{EMail,jdbcType=VARCHAR},
            ZipCode        = #{zipCode,jdbcType=VARCHAR},
            Address        = #{address,jdbcType=VARCHAR},
            OpenBank       = #{openBank,jdbcType=VARCHAR},
            OpenAccount    = #{openAccount,jdbcType=VARCHAR},
            Operator       = #{operator,jdbcType=VARCHAR},
            OperatorCom    = #{operatorCom,jdbcType=VARCHAR},
            MakeDate       = #{makeDate,jdbcType=DATE},
            MakeTime       = #{makeTime,jdbcType=VARCHAR},
            ModifyDate     = #{modifyDate,jdbcType=DATE},
            ModifyTime     = #{modifyTime,jdbcType=VARCHAR}
        where PersonID = #{personID,jdbcType=VARCHAR}
    </update>
    <select id="selectByExample" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcperson
        where 1=1
        <if test="IDNo != null">
            and IDNo = #{IDNo}
        </if>
    </select>
    <select id="selectIDNo" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCPerson">
        select
        <include refid="Base_Column_List"/>
        from fcperson
        where IDNo = #{IDNo,jdbcType=VARCHAR}
    </select>
    <select id="selectPersonByensure" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCPerson">
        select c.*
        from fcperregistday a
                 inner join fcstafffamilyrela b on a.PerNo = b.PerNo
                 inner join fcperson c on b.PersonID = c.PersonID
        where a.EnsureCode = #{EnsureCode}
    </select>
    <update id="updateByPersonId" parameterType="com.sinosoft.eflex.model.FCPerson">
        update fcperson
        <set>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                Sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                Nativeplace = #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="birthDate != null">
                BirthDate = #{birthDate,jdbcType=DATE},
            </if>
            <if test="IDType != null">
                IDType = #{IDType,jdbcType=VARCHAR},
            </if>
            <if test="IDNo != null">
                IDNo = #{IDNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeEndDate != ''">
                idTypeEndDate = #{idTypeEndDate,jdbcType=DATE},
            </if>
            <if test="mobilePhone != null">
                MobilePhone = #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="occupationType != null">
                OccupationType = #{occupationType,jdbcType=VARCHAR},
            </if>
            <if test="occupationCode != null">
                OccupationCode = #{occupationCode,jdbcType=VARCHAR},
            </if>
            <if test="EMail != null">
                EMail = #{EMail,jdbcType=VARCHAR},
            </if>
            <if test="openBank != null">
                OpenBank = #{openBank,jdbcType=VARCHAR},
            </if>
            <if test="openAccount != null">
                OpenAccount = #{openAccount,jdbcType=VARCHAR},
            </if>
            <if test="joinMedProtect != null">
                JoinMedProtect = #{joinMedProtect,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                Province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                City =#{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                County = #{county,jdbcType=VARCHAR},
            </if>
            <if test="detaileAddress != null">
                DetaileAddress = #{detaileAddress,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test="relationship != null">
                relationship = #{relationship,jdbcType=VARCHAR},
            </if>
        </set>
        where PersonID = #{personID,jdbcType=VARCHAR}
    </update>

    <update id="updatePersonList" parameterType="com.sinosoft.eflex.model.Family">
        <if test="list!=null">
            <foreach collection="list" item="Family" index="index" open="" close="" separator=";">
                update fcperson fc
                set fc.`OccupationType` = if(fc.OccupationType is null OR fc.OccupationType =
                '',#{Family.OccupationType},fc.OccupationType),
                fc.`OccupationCode` = if(fc.OccupationCode is null OR fc.OccupationCode =
                '',#{Family.OccupationCode},fc.OccupationCode),
                fc.`MobilePhone` = if(fc.MobilePhone is null OR fc.MobilePhone = '',#{Family.Mobile},fc.MobilePhone),
                fc.`Phone` = if(fc.Phone is null OR fc.Phone = '',#{Family.Phone},fc.Phone),
                fc.`EMail` = if(fc.EMail is null OR fc.EMail = '',#{Family.Email},fc.EMail),
                fc.`JoinMedProtect` = if(fc.JoinMedProtect is null OR fc.JoinMedProtect =
                '',#{Family.SocialInsuFlag},fc.JoinMedProtect),
                fc.`ZipCode` = if(fc.ZipCode is null OR fc.ZipCode = '',#{Family.ZipCode},fc.ZipCode),
                fc.`Address` = if(fc.Address is null OR fc.Address = '',#{Family.Address},fc.Address),
                fc.`ModifyDate` = (select D FROM (SELECT CURDATE() as D FROM fcperson LIMIT 1) person),
                fc.`ModifyTime` = (select T FROM (SELECT CURTIME() as T FROM fcperson LIMIT 1) person)
                WHERE fc.IDNo = #{Family.IDNo} and fc.IDType = #{Family.IDType} and fc.Name = #{Family.Name} and fc.Sex
                = #{Family.Sex} AND fc.BirthDate = #{Family.Birthday}
            </foreach>
        </if>
    </update>

    <select id="selectByIDNo" parameterType="com.sinosoft.eflex.model.Family"
            resultType="com.sinosoft.eflex.model.FCPerson">
        select *
        from fcperson
        where IDNo = #{IDNo,jdbcType=VARCHAR}
    </select>

    <select id="selectPersonByPerno" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM fcperson fc
        LEFT JOIN fcstafffamilyrela fr ON fc.personid=fr.personid
        WHERE fr.perno=#{perno,jdbcType=VARCHAR}
    </select>
    <select id="selectPersonByPerno1" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCPerson">
        SELECT
        <include refid="Base_Column_List1"/>
        FROM fcperson fc
        LEFT JOIN fcstafffamilyrela fr ON fc.personid=fr.personid
        WHERE fr.perno=#{perno,jdbcType=VARCHAR}
    </select>
    <select id="selectInsuredPerson" parameterType="java.util.HashMap" resultType="com.sinosoft.eflex.model.FCPerson">
        SELECT
        <include refid="Base_Column_List1"/>
        FROM fcperson fc
        LEFT JOIN fcstafffamilyrela fr ON fc.personid=fr.personid
        LEFT JOIN fpinsureplan fp on fp.perno=fr.perno
        WHERE fc.personid=fp.personid and fr.perno=#{perNo,jdbcType=VARCHAR}
        and ensurecode=#{ensureCode,jdbcType=VARCHAR}
    </select>

    <insert id="insertMap" parameterType="java.util.HashMap">
        insert into fcperson (PersonID, relationship, Name, Sex,
                              BirthDate, nativeplace, IDType, IDNo, idTypeEndDate,
                              MobilePhone, OccupationType,
                              OccupationCode, JoinMedProtect, OpenBank, OpenAccount, Operator,
                              OperatorCom, MakeDate, MakeTime,
                              ModifyDate, ModifyTime)
        SELECT #{personID,jdbcType=VARCHAR},
               #{relationship,jdbcType=VARCHAR},
               f.`NAME`,
               f.sex,
               f.BirthDay,
               f.nativeplace,
               f.IDType,
               f.IDNo,
               f.idTypeEndDate,
               f.Phone,
               f.OccupationType,
               f.OccupationCode,
               f.JoinMedProtect,
               i.openbank,
               i.OpenAccount,
               #{operator,jdbcType=VARCHAR},
               #{operatorCom,jdbcType=VARCHAR},
               #{makeDate,jdbcType=DATE},
               #{makeTime,jdbcType=VARCHAR},
               #{modifyDate,jdbcType=DATE},
               #{modifyTime,jdbcType=VARCHAR}
        FROM fcperinfofamilytemp f,
             fcperinfotemp i
        WHERE f.FamilyTempNo = #{familyTempNo,jdbcType=VARCHAR}
          AND f.PerTempNo = i.PerTempNo
          AND f.EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          AND f.idno = #{IDNo,jdbcType=VARCHAR}
          AND f.SubStaus = '01'
          AND f.IDNo not in (select idno
                             from fcperson
                             where personID IN (SELECT s.PersonID
                                                FROM fcperson s
                                                         INNER JOIN fcstafffamilyrela d ON d.`PersonID` = s.`PersonID`
                                                         INNER JOIN fcperinfo p ON p.`PerNo` = d.`PerNo`
                                                WHERE p.`GrpNo` = #{grpNo,jdbcType=VARCHAR}
                                                  AND s.`IDNo` = #{IDNo,jdbcType=VARCHAR}))
    </insert>


    <select id="checkStuIdNoIsExists" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM fcperson
        WHERE IDNo = #{idNo,jdbcType=VARCHAR}
    </select>

    <select id="checkStuIdNoIsExistsTwo" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(1)
        from fcperson
        where idno = #{idNo,jdbcType=VARCHAR}
          and (name &lt;&gt; #{name,jdbcType=VARCHAR} or
               sex &lt;&gt; #{sex,jdbcType=VARCHAR} or
               date_format(BirthDate, '%Y-%m-%d') &lt;&gt; #{birthday,jdbcType=VARCHAR} or
               idtype &lt;&gt; #{idType,jdbcType=VARCHAR})
    </select>
    <select id="checkNewPersonIsExists" parameterType="java.util.Map" resultType="java.lang.String">
        select f1.personId
        from fcperson f1
                 inner join fcstafffamilyrela f2 on f1.PersonID = f2.PersonID
        where f2.PerNo IN (SELECT c.perno
                           FROM fcperinfo c
                           WHERE c.idno = (SELECT d.idno FROM fcperinfo d WHERE d.perno = #{perNO,jdbcType=VARCHAR}))
          and f1.name = #{name,jdbcType=VARCHAR}
          and f1.Sex = #{sex,jdbcType=VARCHAR}
          and f1.BirthDate = STR_TO_DATE(#{birthday,jdbcType=VARCHAR}, '%Y-%m-%d')
          and f1.IDNo = #{idNo,jdbcType=VARCHAR}
          and f1.IDType = #{idType,jdbcType=VARCHAR}
    </select>

    <select id="checkPerIdNoIsExists" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(1)
        from fcperson
        where personId = #{personId,jdbcType=VARCHAR}
          and (idno &lt;&gt; #{idNo,jdbcType=VARCHAR} or
               name &lt;&gt; #{name,jdbcType=VARCHAR} or
               sex &lt;&gt; #{sex,jdbcType=VARCHAR} or
               date_format(BirthDate, '%Y-%m-%d') &lt;&gt; #{birthday,jdbcType=VARCHAR} or
               idtype &lt;&gt; #{idType,jdbcType=VARCHAR})
    </select>

    <select id="getGrpPersonID" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FCPerson">
        SELECT s.PersonID
        FROM fcperson s
                 INNER JOIN fcstafffamilyrela d ON d.`PersonID` = s.`PersonID`
                 INNER JOIN fcperinfo p ON p.`PerNo` = d.`PerNo`
        WHERE p.`GrpNo` = #{grpNo,jdbcType=VARCHAR}
          AND p.`IDNo` = #{perIDNo,jdbcType=VARCHAR}
          AND s.`IDNo` = #{idNo,jdbcType=VARCHAR}
    </select>

    <select id="getPerPersonID" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FCPerson">
        SELECT s.PersonID
        FROM fcperson s
                 INNER JOIN fcstafffamilyrela d ON d.`PersonID` = s.`PersonID`
                 INNER JOIN fcperinfo p ON p.`PerNo` = d.`PerNo`
        WHERE p.`GrpNo` = #{grpNo,jdbcType=VARCHAR}
          AND s.`IDNo` = #{idNo,jdbcType=VARCHAR}
          and d.Relation = '0' LIMIT 1
    </select>

    <select id="getFamPersonID" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FCPerson">
        SELECT s.PersonID
        FROM fcperson s
                 INNER JOIN fcstafffamilyrela d ON d.`PersonID` = s.`PersonID`
                 INNER JOIN fcperinfo p ON p.`PerNo` = d.`PerNo`
        WHERE p.`GrpNo` = #{grpNo,jdbcType=VARCHAR}
          AND p.IDNo = #{perIDNo,jdbcType=VARCHAR}
          AND s.`IDNo` = #{idNo,jdbcType=VARCHAR}
          and d.Relation != '0'
        LIMIT 1
    </select>

    <update id="updateFcPerSonStudent" parameterType="java.util.Map">
        update fcperson a,fcperinfofamilytemp b,fcperinfotemp i
        set a.name=b.name,
            a.sex=b.sex,
            a.BirthDate=b.BirthDay,
            a.IDType=b.IDType,
            a.IDNo=b.IDNo,
            a.idTypeEndDate=b.idTypeEndDate,
            a.Nativeplace=b.Nativeplace,
            a.MobilePhone=b.Phone,
            a.OccupationType=b.OccupationType,
            a.OccupationCode=b.OccupationCode,
            a.JoinMedProtect=b.JoinMedProtect,
            a.OpenBank = i.openbank,
            a.OpenAccount = i.OpenAccount,
            a.ModifyDate=str_to_date(#{params.currentDate,jdbcType=VARCHAR}, '%Y-%m-%d'),
            a.ModifyTime=#{params.currentTime,jdbcType=VARCHAR}
        where a.idno = b.idno
          and b.PerTempNo = i.PerTempNo
          and b.SubStaus = '01'
          and b.EnsureCode = #{params.ensureCode,jdbcType=VARCHAR}
          AND a.personID IN (select person.psonID
            FROM (select q.personID as psonID
            from fcperson q
            INNER JOIN fcstafffamilyrela w ON w.personid = q.personid
            INNER JOIN fcperinfo e ON e.perno = w.perno
            WHERE e.grpno = #{params.grpNo,jdbcType=VARCHAR}
          AND w.Relation != '0'
          AND e.idno IN (select PerIDNo
            from fcperinfofamilytemp
            where SubStaus = '01'
          and EnsureCode = #{params.ensureCode,jdbcType=VARCHAR})) person)
    </update>
    <update id="updateByIdNo" parameterType="java.util.Map">
        update fcperson
        set MobilePhone=#{mobilePhone}
        where IDNo = #{idNo};
    </update>

    <insert id="insertFCDefaultPlanStudent" parameterType="java.util.List">
        insert into fcdefaultplan
        (SerialNo, personId, EnsureCode,
        AppntYear, PlanCode, Operator,
        OperatorCom, MakeDate, MakeTime,
        ModifyDate, ModifyTime)
        <foreach collection="list" item="item" index="index" separator="union all">
            (select #{list[${index}].seriaNo,jdbcType=VARCHAR},
            #{list[${index}].personId,jdbcType=VARCHAR},
            #{list[${index}].ensureCode,jdbcType=VARCHAR},
            #{list[${index}].year,jdbcType=VARCHAR},
            p.DefaultPlan,
            #{list[${index}].Operator,jdbcType=VARCHAR},
            a.OperatorCom,
            str_to_date(#{list[${index}].currentDate,jdbcType=VARCHAR},'%Y-%m-%d'),
            #{list[${index}].currentTime,jdbcType=VARCHAR},
            str_to_date(#{list[${index}].currentDate,jdbcType=VARCHAR},'%Y-%m-%d'),
            #{list[${index}].currentTime,jdbcType=VARCHAR}
            from fcperinfofamilytemp a
            inner join fcperinfotemp p on p.PerTempNo = a.PerTempNo
            where a.EnsureCode=#{list[${index}].ensureCode,jdbcType=VARCHAR} and a.SubStaus='01' and
            a.idno=#{list[${index}].idNo,jdbcType=VARCHAR} AND p.DefaultPlan is not null AND p.DefaultPlan != '')
        </foreach>
    </insert>

    <select id="getAllPerson" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT PersonID
        FROM fcperson
        WHERE IDNo = (SELECT IDNo FROM fcperson WHERE PersonID = #{personID,jdbcType=VARCHAR})
    </select>

    <select id="getEmployPersonId" parameterType="java.lang.String" resultType="java.lang.String">
        select a.personId
        from fcstafffamilyrela a,
             fcperinfo b
        where a.perno = b.perno
          and b.perno = #{perNo,jdbcType=VARCHAR}
          and a.relation = '0'
    </select>

    <select id="selectPersonByPernoNotPerNo" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT fc.PersonID       AS personID,
               fc.`IDNo`,
               fc.Name           AS `name`,
               fc.BirthDate      AS birthDay,
               fc.sex            AS sex,
               fc.joinMedProtect AS joinMedProtect,
               fc.occupationType AS occupationType,
               fr.Relation       AS realtion
        FROM fcperson fc
                 LEFT JOIN fcstafffamilyrela fr ON fc.personid = fr.personid
        WHERE fr.perno IN (SELECT PerNo
                           FROM fcperinfo
                           WHERE IDNo = (SELECT IDNo FROM fcperinfo WHERE PerNo = #{perno,jdbcType=VARCHAR}))
          AND fr.Relation != '0'
        GROUP BY fc.`IDNo`
    </select>

    <select id="getFcPersonInfoByPerNo" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCPerson">
        SELECT *
        FROM fcperson a
                 INNER JOIN fcstafffamilyrela b ON a.`PersonID` = b.`PersonID`
                 INNER JOIN fcperinfo c ON b.`PerNo` = c.`PerNo`
        WHERE c.`PerNo` = #{perNo}
          AND b.`PersonID` IN
              (SELECT PersonID FROM fcperson WHERE IDNo = (SELECT IDNo FROM fcperson WHERE PersonID = #{personID}))
    </select>


    <select id="selectFcpersonByPerNo" resultMap="BaseResultMap">
        select a.*
        from fcperson a
                 inner join fcstafffamilyrela b on a.PersonID = b.PersonID
        where b.PerNo = #{perNo}
          and b.Relation = '0'
    </select>
    <select id="selectFcPersonByIdNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcperson
        where IDNo = #{idNo}
    </select>
    <select id="selectFcPersonInfoByParams" resultType="java.util.Map">
        select
        f1.PersonID personId,
        f1.Name name,
        f1.IDType idType,
        f3.CodeName idTypeName,
        f1.IDNo idNo,
        date_format(f1.BirthDate,'%Y-%m-%d') birthDate,
        CAST(TIMESTAMPDIFF(YEAR, f1.BirthDate, CURDATE()) as char) age,
        CAST(TIMESTAMPDIFF(DAY, f1.BirthDate, CURDATE()) as char ) ageDay,
        f1.Sex sex,
        f4.CodeName sexName,
        date_format(f1.idTypeEndDate,'%Y-%m-%d') idTypeEndDate,
        f1.nativeplace,
        f9.CodeName nativeplaceName,
        f1.OccupationCode occupationCode,
        (select codename from fdcode where CodeType = 'OccupationDetail' and CodeKey = f1.OccupationCode) as
        occupationCodeName,
        f1.OccupationType occupationType,
        f1.Province province,
        f1.City city,
        f1.County county,
        f6.PlaceName provinceName,
        f7.PlaceName cityName,
        f8.PlaceName countyName,
        f1.DetaileAddress detaileAddress,
        f1.ZipCode zipCode,
        f1.MobilePhone mobilePhone,
        f1.EMail eMail,
        f2.Relation relation,
        f5.CodeName relationName
        from fcperson f1
        inner join fcstafffamilyrela f2 on f1.PersonID = f2.PersonID
        left join fdcode f3 on f3.CodeType = 'IDType' and f3.CodeKey = f1.IDType
        left join fdcode f4 on f4.CodeType = 'sex' and f4.CodeKey = f1.Sex
        left join fdcode f5 on f5.CodeType = 'relation' and f5.CodeKey = f2.Relation
        left join fdcode f9 on f9.CodeType = 'Nativeplace' and f9.CodeKey = f1.nativeplace
        left join fdplace f6 on f6.PlaceCode = f1.Province
        left join fdplace f7 on f7.PlaceCode = f1.City
        left join fdplace f8 on f8.PlaceCode = f1.County
        where f2.PerNo = #{perNo}
        <if test="personId != null and personId !=''">
            and f1.PersonID = #{personId}
        </if>
        <if test="relation != null and relation !=''">
            and f2.Relation = #{relation}
        </if>
    </select>
    <select id="selectFcPersonInfoByParam" resultType="java.util.Map">
        select
        fc.PersonID personId,
        fc.Name name,
        fc.IDType idType,
        f3.CodeName idTypeName,
        fc.IDNo idNo,
        date_format(f1.BirthDate,'%Y-%m-%d') birthDate,
        CAST(TIMESTAMPDIFF(YEAR, fc.Birthday, CURDATE()) as char) age,
        CAST(TIMESTAMPDIFF(DAY, fc.Birthday, CURDATE()) as char ) ageDay,
        fc.Sex sex,
        f4.CodeName sexName,
        date_format(f1.idTypeEndDate,'%Y-%m-%d') idTypeEndDate,
        f1.nativeplace,
        f9.CodeName nativeplaceName,
        fc.OccupationCode occupationCode,
        (select codename from fdcode where CodeType = 'OccupationDetail' and CodeKey = fc.OccupationCode) as
        occupationCodeName,
        fc.OccupationType occupationType,
        fc.Province province,
        fc.City city,
        fc.County county,
        f6.PlaceName provinceName,
        f7.PlaceName cityName,
        f8.PlaceName countyName,
        fc.DetaileAddress detaileAddress,
        fc.ZipCode zipCode,
        fc.MobilePhone mobilePhone,
        fc.EMail eMail,
        f2.Relation relation,
        f5.CodeName relationName
        from fcperson f1
        inner join fcstafffamilyrela f2 on f1.PersonID = f2.PersonID
        LEFT JOIN fcorderinsured fc ON fc.`OrderItemNo`= #{orderItemNo}
        left join fdcode f3 on f3.CodeType = 'IDType' and f3.CodeKey = f1.IDType
        left join fdcode f4 on f4.CodeType = 'sex' and f4.CodeKey = f1.Sex
        left join fdcode f5 on f5.CodeType = 'relation' and f5.CodeKey = f2.Relation
        left join fdcode f9 on f9.CodeType = 'Nativeplace' and f9.CodeKey = f1.nativeplace
        left join fdplace f6 on f6.PlaceCode = fc.Province
        left join fdplace f7 on f7.PlaceCode = fc.City
        left join fdplace f8 on f8.PlaceCode = fc.County
        where f2.PerNo = #{perNo}
        <if test="personId != null and personId !=''">
            and f1.PersonID = #{personId}
        </if>
        <if test="relation != null and relation !=''">
            and f2.Relation = #{relation}
        </if>
    </select>
    <select id="selectFcPersonInfoByPernoAndPersonId"
            parameterType="com.sinosoft.eflex.model.confirmInsure.SelectFcPersonInfoByPerNoAndPersonIdReq"
            resultType="com.sinosoft.eflex.model.confirmInsure.PeopleInsureInfo">
        select
        f1.PersonID personId,
        f1.Name name,
        date_format(f1.BirthDate,'%Y-%m-%d') birthDate,
        f1.IDType idType,
        f2.Relation relation,
        f3.CodeName relationName
        from fcperson f1
        inner join fcstafffamilyrela f2 on f1.PersonID = f2.PersonID
        left join fdcode f3 on f3.CodeType = 'relation' and f3.CodeKey = f2.Relation
        where f2.PerNo = #{perNo}
        <if test="personId != null and personId !=''">
            and f1.PersonID = #{personId}
        </if>
    </select>

    <select id="selectByPersonIds" resultMap="BaseResultMap" parameterType="java.util.List">
        select
        <include refid="Base_Column_List"/>
        from fcperson
        where personId in
        <foreach item="item" index="index" collection="list" open="(" separator=", " close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectInfoFcPersonInfoByParams" resultType="java.util.Map">
        select
        fc.PersonID personId,
        fc.Name name,
        fc.IDType idType,
        f3.CodeName idTypeName,
        fc.IDNo idNo,
        date_format(fc.Birthday,'%Y-%m-%d') birthDate,
        CAST(TIMESTAMPDIFF(YEAR, fc.Birthday, CURDATE()) as char) age,
        CAST(TIMESTAMPDIFF(DAY, fc.Birthday, CURDATE()) as char ) ageDay,
        fc.Sex sex,
        f4.CodeName sexName,
        date_format(f1.idTypeEndDate,'%Y-%m-%d') idTypeEndDate,
        f1.nativeplace,
        f9.CodeName nativeplaceName,
        f1.OccupationCode occupationCode,
        (select codename from fdcode where CodeType = 'OccupationDetail' and CodeKey = f1.OccupationCode) as
        occupationCodeName,
        f1.OccupationType occupationType,
        fc.Province province,
        fc.City city,
        fc.County county,
        f6.PlaceName provinceName,
        f7.PlaceName cityName,
        f8.PlaceName countyName,
        fc.DetaileAddress detaileAddress,
        fc.ZipCode zipCode,
        fc.MobilePhone mobilePhone,
        fc.EMail eMail,
        f2.Relation relation,
        f5.CodeName relationName
        from fcperson f1
        inner join fcstafffamilyrela f2 on f1.PersonID = f2.PersonID
        LEFT JOIN fcorderinsured fc ON fc.`OrderItemNo`= #{orderItemNo}
        left join fdcode f3 on f3.CodeType = 'IDType' and f3.CodeKey = f1.IDType
        left join fdcode f4 on f4.CodeType = 'sex' and f4.CodeKey = f1.Sex
        left join fdcode f5 on f5.CodeType = 'relation' and f5.CodeKey = f2.Relation
        left join fdcode f9 on f9.CodeType = 'Nativeplace' and f9.CodeKey = f1.nativeplace
        left join fdplace f6 on f6.PlaceCode = fc.Province
        left join fdplace f7 on f7.PlaceCode = fc.City
        left join fdplace f8 on f8.PlaceCode = fc.County
        where f2.PerNo = #{perNo}
        <if test="personId != null and personId !=''">
            and f1.PersonID = #{personId}
        </if>
        <if test="relation != null and relation !=''">
            and f2.Relation = #{relation}
        </if>
    </select>
    <select id="selectMainFcPersonInfoByParams" resultType="java.util.Map">
        select
        f1.PersonID personId,
        f1.Name name,
        f1.IDType idType,
        f3.CodeName idTypeName,
        f1.IDNo idNo,
        date_format(f1.BirthDate,'%Y-%m-%d') birthDate,
        CAST(TIMESTAMPDIFF(YEAR, f1.BirthDate, CURDATE()) as char) age,
        CAST(TIMESTAMPDIFF(DAY, f1.BirthDate, CURDATE()) as char ) ageDay,
        f1.Sex sex,
        f4.CodeName sexName,
        date_format(f1.idTypeEndDate,'%Y-%m-%d') idTypeEndDate,
        f1.nativeplace,
        f9.CodeName nativeplaceName,
        f1.OccupationCode occupationCode,
        (select codename from fdcode where CodeType = 'OccupationDetail' and CodeKey = f1.OccupationCode) as
        occupationCodeName,
        f1.OccupationType occupationType,
        fc.MainProvince province,
        fc.MainCity city,
        fc.MainCounty county,
        f6.PlaceName provinceName,
        f7.PlaceName cityName,
        f8.PlaceName countyName,
        fc.MainDetaileAddress detaileAddress,
        fc.MainZipCode zipCode,
        fc.MainMobilePhone mobilePhone,
        fc.MainEMail eMail,
        f2.Relation relation,
        f5.CodeName relationName
        from fcperson f1
        inner join fcstafffamilyrela f2 on f1.PersonID = f2.PersonID
        LEFT JOIN fcorderinsured fc ON fc.`OrderItemNo`= #{orderItemNo}
        left join fdcode f3 on f3.CodeType = 'IDType' and f3.CodeKey = f1.IDType
        left join fdcode f4 on f4.CodeType = 'sex' and f4.CodeKey = f1.Sex
        left join fdcode f5 on f5.CodeType = 'relation' and f5.CodeKey = f2.Relation
        left join fdcode f9 on f9.CodeType = 'Nativeplace' and f9.CodeKey = f1.nativeplace
        left join fdplace f6 on f6.PlaceCode = f1.Province
        left join fdplace f7 on f7.PlaceCode = f1.City
        left join fdplace f8 on f8.PlaceCode = f1.County
        where f2.PerNo = #{perNo}
        <if test="personId != null and personId !=''">
            and f1.PersonID = #{personId}
        </if>
        <if test="relation != null and relation !=''">
            and f2.Relation = #{relation}
        </if>
    </select>
    <select id="selectFcPersonAndSalary" resultType="java.util.Map">
        select
        f1.PersonID personId,
        f1.Name name,
        f1.IDType idType,
        f3.CodeName idTypeName,
        f1.IDNo idNo,
        date_format(f1.BirthDate,'%Y-%m-%d') birthDate,
        CAST(TIMESTAMPDIFF(YEAR, f1.BirthDate, CURDATE()) as char) age,
        CAST(TIMESTAMPDIFF(DAY, f1.BirthDate, CURDATE()) as char ) ageDay,
        f1.Sex sex,
        f4.CodeName sexName,
        date_format(f1.idTypeEndDate,'%Y-%m-%d') idTypeEndDate,
        f1.nativeplace,
        f9.CodeName nativeplaceName,
        f1.OccupationCode occupationCode,
        (select codename from fdcode where CodeType = 'OccupationDetail' and CodeKey = f1.OccupationCode) as
        occupationCodeName,
        f1.OccupationType occupationType,
        f1.Province province,
        f1.City city,
        f1.County county,
        f6.PlaceName provinceName,
        f7.PlaceName cityName,
        f8.PlaceName countyName,
        f1.DetaileAddress detaileAddress,
        f1.ZipCode zipCode,
        f1.MobilePhone mobilePhone,
        f1.EMail eMail,
        f2.Relation relation,
        f5.CodeName relationName
        from fcperson f1
        inner join fcstafffamilyrela f2 on f1.PersonID = f2.PersonID
        inner join fcperinfo fc on f2.PerNo = fc.PerNo
        left join fdcode f3 on f3.CodeType = 'IDType' and f3.CodeKey = f1.IDType
        left join fdcode f4 on f4.CodeType = 'sex' and f4.CodeKey = f1.Sex
        left join fdcode f5 on f5.CodeType = 'relation' and f5.CodeKey = f2.Relation
        left join fdcode f9 on f9.CodeType = 'Nativeplace' and f9.CodeKey = f1.nativeplace
        left join fdplace f6 on f6.PlaceCode = f1.Province
        left join fdplace f7 on f7.PlaceCode = f1.City
        left join fdplace f8 on f8.PlaceCode = f1.County
        where f2.PerNo = #{perNo}
        <if test="personId != null and personId !=''">
            and f1.PersonID = #{personId}
        </if>
        <if test="relation != null and relation !=''">
            and f2.Relation = #{relation}
        </if>
    </select>
    <select id="selectFcPersonAndSalaryItemNo" resultType="java.util.Map">
        select
        f1.PersonID personId,
        f1.Name name,
        f1.IDType idType,
        f3.CodeName idTypeName,
        f1.IDNo idNo,
        date_format(f1.BirthDate,'%Y-%m-%d') birthDate,
        date_format(fd.Birthday,'%Y-%m-%d') birthday,
        CAST(TIMESTAMPDIFF(YEAR, f1.BirthDate, CURDATE()) as char) age,
        CAST(TIMESTAMPDIFF(DAY, f1.BirthDate, CURDATE()) as char ) ageDay,
        f1.Sex sex,
        f4.CodeName sexName,
        date_format(f1.idTypeEndDate,'%Y-%m-%d') idTypeEndDate,
        f1.nativeplace,
        f9.CodeName nativeplaceName,
        f1.OccupationCode occupationCode,
        (select codename from fdcode where CodeType = 'OccupationDetail' and CodeKey = f1.OccupationCode) as
        occupationCodeName,
        f1.OccupationType occupationType,
        fd.MainProvince province,
        fd.MainCity city,
        fd.MainCounty county,
        f6.PlaceName provinceName,
        f7.PlaceName cityName,
        f8.PlaceName countyName,
        fd.MainDetaileAddress detaileAddress,
        fd.MainZipCode zipCode,
        fd.MainMobilePhone mobilePhone,
        fd.MainEMail eMail,
        f2.Relation relation,
        f5.CodeName relationName,
        fd.MainYearSalary yearSalary
        from fcperson f1
        inner join fcstafffamilyrela f2 on f1.PersonID = f2.PersonID
        left join fcorderinsured fd on fd.OrderItemNo=#{orderItemNo}
        left join fdcode f3 on f3.CodeType = 'IDType' and f3.CodeKey = f1.IDType
        left join fdcode f4 on f4.CodeType = 'sex' and f4.CodeKey = f1.Sex
        left join fdcode f5 on f5.CodeType = 'relation' and f5.CodeKey = f2.Relation
        left join fdcode f9 on f9.CodeType = 'Nativeplace' and f9.CodeKey = f1.nativeplace
        left join fdplace f6 on f6.PlaceCode = fd.MainProvince
        left join fdplace f7 on f7.PlaceCode = fd.MainCity
        left join fdplace f8 on f8.PlaceCode = fd.MainCounty
        where f2.PerNo = #{perNo}
        <if test="personId != null and personId !=''">
            and f1.PersonID = #{personId}
        </if>
        <if test="relation != null and relation !=''">
            and f2.Relation = #{relation}
        </if>
    </select>
    <select id="selectFcPersonInfosByParams" resultType="java.util.Map">
        select
        f1.PersonID personId,
        f1.Name name,
        f1.IDType idType,
        f3.CodeName idTypeName,
        f1.IDNo idNo,
        date_format(f1.BirthDate,'%Y-%m-%d') birthDate,
        CAST(TIMESTAMPDIFF(YEAR, f1.BirthDate, CURDATE()) as char) age,
        CAST(TIMESTAMPDIFF(DAY, f1.BirthDate, CURDATE()) as char ) ageDay,
        f1.Sex sex,
        f4.CodeName sexName,
        date_format(f1.idTypeEndDate,'%Y-%m-%d') idTypeEndDate,
        f1.nativeplace,
        f9.CodeName nativeplaceName,
        f1.OccupationCode occupationCode,
        (select codename from fdcode where CodeType = 'OccupationDetail' and CodeKey = f1.OccupationCode) as
        occupationCodeName,
        f1.OccupationType occupationType,
        f1.Province province,
        f1.City city,
        f1.County county,
        f6.PlaceName provinceName,
        f7.PlaceName cityName,
        f8.PlaceName countyName,
        f1.DetaileAddress detaileAddress,
        f1.ZipCode zipCode,
        f1.MobilePhone mobilePhone,
        f1.EMail eMail,
        f2.Relation relation,
        f5.CodeName relationName
        from fcperson f1
        inner join fcstafffamilyrela f2 on f1.PersonID = f2.PersonID
        left join fdcode f3 on f3.CodeType = 'IDType' and f3.CodeKey = f1.IDType
        left join fdcode f4 on f4.CodeType = 'sex' and f4.CodeKey = f1.Sex
        left join fdcode f5 on f5.CodeType = 'relation' and f5.CodeKey = f2.Relation
        left join fdcode f9 on f9.CodeType = 'Nativeplace' and f9.CodeKey = f1.nativeplace
        left join fdplace f6 on f6.PlaceCode = f1.Province
        left join fdplace f7 on f7.PlaceCode = f1.City
        left join fdplace f8 on f8.PlaceCode = f1.County
        where f2.PerNo IN( SELECT c.perno FROM fcperinfo c WHERE c.idno = (SELECT d.idno FROM fcperinfo d WHERE d.perno=
        #{perNo}))
        <if test="personId != null and personId !=''">
            and f1.PersonID = #{personId}
        </if>
        <if test="relation != null and relation !=''">
            and f2.Relation = #{relation}
        </if>
    </select>

    <select id="selectfcPersoninfo" resultType="java.util.Map">
        select
        f1.PersonID personId,
        f1.Name name
        from fcperson f1
        inner join fcstafffamilyrela f2 on f1.PersonID = f2.PersonID
        where f2.PerNo = #{perNo}
        <if test="personId != null and personId !=''">
            and f1.PersonID = #{personId}
        </if>
        <if test="relation != null and relation !=''">
            and f2.Relation = #{relation}
        </if>
    </select>
    <select id="selectfcPersoninfos" resultType="java.util.Map">
        select
        f1.PersonID personId,
        f1.Name name
        from fcperson f1
        inner join fcstafffamilyrela f2 on f1.PersonID = f2.PersonID
        where f2.PerNo IN( SELECT c.perno FROM fcperinfo c WHERE c.idno = (SELECT d.idno FROM fcperinfo d WHERE d.perno=
        #{perNo}))
        <if test="personId != null and personId !=''">
            and f1.PersonID = #{personId}
        </if>
        <if test="relation != null and relation !=''">
            and f2.Relation = #{relation}
        </if>
    </select>
    <select id="selectFcPersonByPersonId" resultType="com.sinosoft.eflex.model.FCPerson">
        select *
        from fcperson
        where PersonID = #{personID}
    </select>
    <select id="selectFcPersonByIdNoAndPerNo" resultType="com.sinosoft.eflex.model.FCPerson">
        select *
        from fcperson
        where IDNo = #{idNo}
          and GrpNo = #{grpNo}
    </select>
    <select id="checkPhoneFcPerson" resultType="com.sinosoft.eflex.model.FCPerson">
        SELECT * FROM fcperson WHERE BirthDate <![CDATA[ <= ]]> DATE_SUB(CURDATE(), INTERVAL 18 YEAR)
        and IDNo  NOT IN
            <foreach item="item" index="index" collection="idNoList" open="(" separator=", " close=")">
             #{item}
            </foreach>
        AND MobilePhone = #{mobilePhone}   ORDER BY MakeDate DESC LIMIT 1

    </select>

    <update id="updateBatchById">
        <foreach collection="list" item="item" separator=";">
            update fcperson
            <set>
                <if test="item.name != null">
                    Name = #{item.name,jdbcType=VARCHAR},
                </if>
                <if test="item.sex != null">
                    Sex = #{item.sex,jdbcType=VARCHAR},
                </if>
                <if test="item.nativeplace != null">
                    Nativeplace = #{item.nativeplace,jdbcType=VARCHAR},
                </if>
                <if test="item.birthDate != null">
                    BirthDate = #{item.birthDate,jdbcType=DATE},
                </if>
                <if test="item.IDType != null">
                    IDType = #{item.IDType,jdbcType=VARCHAR},
                </if>
                <if test="item.IDNo != null">
                    IDNo = #{item.IDNo,jdbcType=VARCHAR},
                </if>
                <if test="item.idTypeEndDate != null">
                    idTypeEndDate = #{item.idTypeEndDate,jdbcType=DATE},
                </if>
                <if test="item.mobilePhone != null">
                    MobilePhone = #{item.mobilePhone,jdbcType=VARCHAR},
                </if>
                <if test="item.phone != null">
                    Phone = #{item.phone,jdbcType=VARCHAR},
                </if>
                <if test="item.occupationType != null">
                    OccupationType = #{item.occupationType,jdbcType=VARCHAR},
                </if>
                <if test="item.occupationCode != null">
                    OccupationCode = #{item.occupationCode,jdbcType=VARCHAR},
                </if>
                <if test="item.joinMedProtect != null">
                    JoinMedProtect = #{item.joinMedProtect,jdbcType=VARCHAR},
                </if>
                <if test="item.medProtectType != null">
                    MedProtectType = #{item.medProtectType,jdbcType=VARCHAR},
                </if>
                <if test="item.EMail != null">
                    EMail = #{item.EMail,jdbcType=VARCHAR},
                </if>
                <if test="item.zipCode != null">
                    ZipCode = #{item.zipCode,jdbcType=VARCHAR},
                </if>
                <if test="item.address != null">
                    Address = #{item.address,jdbcType=VARCHAR},
                </if>
                <if test="item.openBank != null">
                    OpenBank = #{item.openBank,jdbcType=VARCHAR},
                </if>
                <if test="item.openAccount != null">
                    OpenAccount = #{item.openAccount,jdbcType=VARCHAR},
                </if>
                <if test="item.operator != null">
                    Operator = #{item.operator,jdbcType=VARCHAR},
                </if>
                <if test="item.operatorCom != null">
                    OperatorCom = #{item.operatorCom,jdbcType=VARCHAR},
                </if>
                <if test="item.province != null">
                    Province = #{item.province,jdbcType=VARCHAR},
                </if>
                <if test="item.city != null">
                    City = #{item.city,jdbcType=VARCHAR},
                </if>
                <if test="item.county != null">
                    County = #{item.county,jdbcType=VARCHAR},
                </if>
                <if test="item.detaileAddress != null">
                    DetaileAddress = #{item.detaileAddress,jdbcType=VARCHAR},
                </if>
                <if test="item.makeDate != null">
                    MakeDate = #{item.makeDate,jdbcType=DATE},
                </if>
                <if test="item.makeTime != null">
                    MakeTime = #{item.makeTime,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyDate != null">
                    ModifyDate = #{item.modifyDate,jdbcType=DATE},
                </if>
                <if test="item.modifyTime != null">
                    ModifyTime = #{item.modifyTime,jdbcType=VARCHAR},
                </if>
            </set>
            where
            <choose>
                <when test="item.edorType == 'IC' and item.edorType != null and item.edorType != ''">
                    IDNo = #{item.oldIdNo}
                </when>
                <otherwise>
                    IDNo = #{item.IDNo}
                </otherwise>
            </choose>

        </foreach>
    </update>
    <update id="updateJuvenilePhone">
        update fcperson set MobilePhone=''   where  IDNo=#{idNo}
    </update>
</mapper>
