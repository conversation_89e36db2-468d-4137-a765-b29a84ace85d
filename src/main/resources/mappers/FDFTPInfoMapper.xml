<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FDFTPInfoMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FDFTPInfo">
    <id column="FTPInfoSN" jdbcType="VARCHAR" property="ftpinfosn" />
    <result column="ModuleType" jdbcType="VARCHAR" property="moduletype" />
    <result column="OperateType" jdbcType="VARCHAR" property="operatetype" />
    <result column="FTPSiteType" jdbcType="VARCHAR" property="ftpsitetype" />
    <result column="FileType" jdbcType="VARCHAR" property="filetype" />
    <result column="FTPRootPath" jdbcType="VARCHAR" property="ftprootpath" />
    <result column="IPAddress" jdbcType="VARCHAR" property="ipaddress" />
    <result column="Port" jdbcType="INTEGER" property="port" />
    <result column="UserName" jdbcType="VARCHAR" property="username" />
    <result column="PassWord" jdbcType="VARCHAR" property="password" />
  </resultMap>
  <sql id="Base_Column_List">
    FTPInfoSN, ModuleType, OperateType, FTPSiteType, FileType, FTPRootPath, IPAddress, 
    Port, UserName, PassWord
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from FDFTPInfo
    where FTPInfoSN = #{ftpinfosn,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from FDFTPInfo
    where FTPInfoSN = #{ftpinfosn,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FDFTPInfo">
    insert into FDFTPInfo (FTPInfoSN, ModuleType, OperateType, 
      FTPSiteType, FileType, FTPRootPath, 
      IPAddress, Port, UserName, 
      PassWord)
    values (#{ftpinfosn,jdbcType=VARCHAR}, #{moduletype,jdbcType=VARCHAR}, #{operatetype,jdbcType=VARCHAR}, 
      #{ftpsitetype,jdbcType=VARCHAR}, #{filetype,jdbcType=VARCHAR}, #{ftprootpath,jdbcType=VARCHAR}, 
      #{ipaddress,jdbcType=VARCHAR}, #{port,jdbcType=INTEGER}, #{username,jdbcType=VARCHAR}, 
      #{password,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FDFTPInfo">
    insert into FDFTPInfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ftpinfosn != null">
        FTPInfoSN,
      </if>
      <if test="moduletype != null">
        ModuleType,
      </if>
      <if test="operatetype != null">
        OperateType,
      </if>
      <if test="ftpsitetype != null">
        FTPSiteType,
      </if>
      <if test="filetype != null">
        FileType,
      </if>
      <if test="ftprootpath != null">
        FTPRootPath,
      </if>
      <if test="ipaddress != null">
        IPAddress,
      </if>
      <if test="port != null">
        Port,
      </if>
      <if test="username != null">
        UserName,
      </if>
      <if test="password != null">
        PassWord,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ftpinfosn != null">
        #{ftpinfosn,jdbcType=VARCHAR},
      </if>
      <if test="moduletype != null">
        #{moduletype,jdbcType=VARCHAR},
      </if>
      <if test="operatetype != null">
        #{operatetype,jdbcType=VARCHAR},
      </if>
      <if test="ftpsitetype != null">
        #{ftpsitetype,jdbcType=VARCHAR},
      </if>
      <if test="filetype != null">
        #{filetype,jdbcType=VARCHAR},
      </if>
      <if test="ftprootpath != null">
        #{ftprootpath,jdbcType=VARCHAR},
      </if>
      <if test="ipaddress != null">
        #{ipaddress,jdbcType=VARCHAR},
      </if>
      <if test="port != null">
        #{port,jdbcType=INTEGER},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FDFTPInfo">
    update FDFTPInfo
    <set>
      <if test="moduletype != null">
        ModuleType = #{moduletype,jdbcType=VARCHAR},
      </if>
      <if test="operatetype != null">
        OperateType = #{operatetype,jdbcType=VARCHAR},
      </if>
      <if test="ftpsitetype != null">
        FTPSiteType = #{ftpsitetype,jdbcType=VARCHAR},
      </if>
      <if test="filetype != null">
        FileType = #{filetype,jdbcType=VARCHAR},
      </if>
      <if test="ftprootpath != null">
        FTPRootPath = #{ftprootpath,jdbcType=VARCHAR},
      </if>
      <if test="ipaddress != null">
        IPAddress = #{ipaddress,jdbcType=VARCHAR},
      </if>
      <if test="port != null">
        Port = #{port,jdbcType=INTEGER},
      </if>
      <if test="username != null">
        UserName = #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        PassWord = #{password,jdbcType=VARCHAR},
      </if>
    </set>
    where FTPInfoSN = #{ftpinfosn,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FDFTPInfo">
    update FDFTPInfo
    set ModuleType = #{moduletype,jdbcType=VARCHAR},
      OperateType = #{operatetype,jdbcType=VARCHAR},
      FTPSiteType = #{ftpsitetype,jdbcType=VARCHAR},
      FileType = #{filetype,jdbcType=VARCHAR},
      FTPRootPath = #{ftprootpath,jdbcType=VARCHAR},
      IPAddress = #{ipaddress,jdbcType=VARCHAR},
      Port = #{port,jdbcType=INTEGER},
      UserName = #{username,jdbcType=VARCHAR},
      PassWord = #{password,jdbcType=VARCHAR}
    where FTPInfoSN = #{ftpinfosn,jdbcType=VARCHAR}
  </update>
</mapper>