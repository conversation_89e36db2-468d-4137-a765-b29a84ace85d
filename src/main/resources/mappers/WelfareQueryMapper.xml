<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.WelfareQueryMapper">
    <resultMap id="fcStaffFamilyRelaInfo" type="com.sinosoft.eflex.model.FCStaffFamilyRela">
        <id column="PerNo" jdbcType="VARCHAR" property="perNo" />
        <result column="Relation" jdbcType="VARCHAR" property="relation" />
        <collection property="fcPersonList" ofType="com.sinosoft.eflex.model.FCPerson" resultMap="fcPerson"/>
    </resultMap>
    <resultMap id="fcPerson" type="com.sinosoft.eflex.model.FCPerson">
        <id column="PersonID" jdbcType="VARCHAR" property="personID" />
        <result column="Name" jdbcType="VARCHAR" property="name" />
        <association property="fcOrderInsured" javaType="com.sinosoft.eflex.model.FCOrderInsured" resultMap="fcOrderInsured"/>
    </resultMap>
    <resultMap id="fcOrderInsured" type="com.sinosoft.eflex.model.FCOrderInsured">
        <id column="OrderItemNo" jdbcType="VARCHAR" property="orderItemNo" />
        <result column="PersonID" jdbcType="VARCHAR" property="personID" />
        <collection property="fcOrderItemList" ofType="com.sinosoft.eflex.model.FCOrderItem" resultMap="fcOrderItem"/>
    </resultMap>
    <resultMap id="fcOrderItem" type="com.sinosoft.eflex.model.FCOrderItem">
        <id column="OrderItemNo" jdbcType="VARCHAR" property="orderItemNo" />
        <result column="OrderItemDetailNo" jdbcType="VARCHAR" property="orderItemDetailNo" />
        <association property="fcOrderItemDetail" javaType="com.sinosoft.eflex.model.FCOrderItemDetail" resultMap="fcOrderItemDetail"/>
    </resultMap>
    <resultMap id="fcOrderItemDetail" type="com.sinosoft.eflex.model.FCOrderItemDetail">
        <id column="OrderItemDetailNo" jdbcType="VARCHAR" property="orderItemDetailNo" />
        <result column="ProductCode" jdbcType="VARCHAR" property="productCode" />
    </resultMap>

    <select id="selectfamilyInfo" resultType="java.util.HashMap" parameterType="java.lang.String">
    select r.Relation,p.Name,r.PersonID
    from  fcstafffamilyrela r inner join fcperson p on r.PersonID=p.PersonID where r.PerNo=#{perNo}
    </select>

    <select id="findFamilyName" resultType="map" parameterType="java.lang.String">
        select r.Relation,p.Name,tail.ProductCode
        from  fcstafffamilyrela r left join fcperson p on r.PersonID=p.PersonID
        right join  fcorderinsured i  on r.PersonID=i.PersonID
        right join (fcorderitem item) on i.OrderItemNo=item.OrderItemNo
        left  join (fcorderitemdetail tail) on item.OrderItemDetailNo=tail.OrderItemDetailNo
        <where>
            <if test="personId !=null and personId !=''">
                r.PersonID=#{personId}
            </if>
        </where>
    </select>
</mapper>