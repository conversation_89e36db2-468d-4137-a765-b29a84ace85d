<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FDUserRoleMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FDUserRole">
    <id column="UserRoleSN" jdbcType="VARCHAR" property="userRoleSN" />
    <result column="UserNo" jdbcType="VARCHAR" property="userNo" />
    <result column="RoleType" jdbcType="VARCHAR" property="roleType" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    UserRoleSN, UserNo, RoleType, OperatorCom, Operator, MakeDate, MakeTime, ModifyDate, 
    ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fduserrole
    where UserRoleSN = #{userRoleSN,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fduserrole
    where UserRoleSN = #{userRoleSN,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FDUserRole">
    insert into fduserrole (UserRoleSN, UserNo, RoleType, 
      OperatorCom, Operator, MakeDate, 
      MakeTime, ModifyDate, ModifyTime
      )
    values (#{userRoleSN,jdbcType=VARCHAR}, #{userNo,jdbcType=VARCHAR}, #{roleType,jdbcType=VARCHAR}, 
      #{operatorCom,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, 
      #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FDUserRole">
    insert into fduserrole
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userRoleSN != null">
        UserRoleSN,
      </if>
      <if test="userNo != null">
        UserNo,
      </if>
      <if test="roleType != null">
        RoleType,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userRoleSN != null">
        #{userRoleSN,jdbcType=VARCHAR},
      </if>
      <if test="userNo != null">
        #{userNo,jdbcType=VARCHAR},
      </if>
      <if test="roleType != null">
        #{roleType,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FDUserRole">
    update fduserrole
    <set>
      <if test="userNo != null">
        UserNo = #{userNo,jdbcType=VARCHAR},
      </if>
      <if test="roleType != null">
        RoleType = #{roleType,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where UserRoleSN = #{userRoleSN,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FDUserRole">
    update fduserrole
    set UserNo = #{userNo,jdbcType=VARCHAR},
      RoleType = #{roleType,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where UserRoleSN = #{userRoleSN,jdbcType=VARCHAR}
  </update>

  <delete id="deleteByUserNo" parameterType="java.lang.String">
    delete from fduserrole
    where UserNo = #{userNo}
  </delete>
</mapper>