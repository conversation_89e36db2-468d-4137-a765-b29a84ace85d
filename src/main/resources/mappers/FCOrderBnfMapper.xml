<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCOrderBnfMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCOrderBnf">
        <id column="BnfNo" jdbcType="VARCHAR" property="bnfNo"/>
        <result column="Relation" jdbcType="VARCHAR" property="relation"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Sex" jdbcType="VARCHAR" property="sex"/>
        <result column="Birthday" jdbcType="DATE" property="birthday"/>
        <result column="IDType" jdbcType="VARCHAR" property="IDType"/>
        <result column="IDNo" jdbcType="VARCHAR" property="IDNo"/>
        <result column="idTypeEndDate" jdbcType="VARCHAR" property="idTypeEndDate"/>
        <result column="MobilePhone" jdbcType="VARCHAR" property="mobilePhone"/>
        <result column="Address" jdbcType="VARCHAR" property="address"/>
        <result column="ZipCode" jdbcType="VARCHAR" property="zipCode"/>
        <result column="NativePlace" jdbcType="VARCHAR" property="nativePlace"/>
        <result column="Province" jdbcType="VARCHAR" property="province"/>
        <result column="City" jdbcType="VARCHAR" property="city"/>
        <result column="County" jdbcType="VARCHAR" property="county"/>
        <result column="DetaileAddress" jdbcType="VARCHAR" property="detaileAddress"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        BnfNo, Relation, Name, Sex, Birthday, IDType, IDNo, idTypeEndDate, MobilePhone, Address, ZipCode, NativePlace, Province,City,County,DetaileAddress,
    Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcorderbnf
        where BnfNo = #{bnfNo,jdbcType=VARCHAR}
    </select>
    <select id="selectBnfInfos" resultType="java.util.Map">
        select f1.BnfNo                                  bnfNo,
               f1.Relation                               relation,
               f4.CodeName                               relationName,
               f1.Name                                   name,
               f1.IDType                                 idType,
               f3.CodeName                               idTypeName,
               f1.IDNo                                   idNo,
               date_format(f1.Birthday, '%Y-%m-%d')      birthday,
               f1.Sex                                    sex,
               f5.CodeName                               sexName,
               date_format(f1.idTypeEndDate, '%Y-%m-%d') idTypeEndDate,
               f1.MobilePhone                            mobilePhone,
               f1.Province                               province,
               f1.City                                   city,
               f1.County                                 county,
               f1.NativePlace                            nativePlace,
               f10.CodeName                              nativeplaceName,
               f6.PlaceName                              provinceName,
               f7.PlaceName                              cityName,
               f8.PlaceName                              countyName,
               f1.DetaileAddress                         detaileAddress,
               CAST(f2.BnfOrder as char)                 bnfOrder,
               CONCAT(f2.BnfRatio * 100, '(%)')          bnfRatio,
               CAST(f2.BnfRatio as char)                 coreBnfRatio,
               f2.BnfType                                bnfType,
               f2.BnfKind                                bnfKind,
               f9.CodeName                               bnfKindName
        from fcorderbnf f1
                 inner join fcorderbnfrela f2 on f1.BnfNo = f2.BnfNo
                 left join fdcode f3 on f3.CodeType = 'IDType' and f3.CodeKey = f1.IDType
                 left join fdcode f4 on f4.CodeType = 'Relation' and f4.CodeKey = f1.Relation
                 left join fdcode f5 on f5.CodeType = 'Sex' and f5.CodeKey = f1.Sex
                 left join fdplace f6 on f6.PlaceCode = f1.Province
                 left join fdplace f7 on f7.PlaceCode = f1.City
                 left join fdplace f8 on f8.PlaceCode = f1.County
                 left join fdcode f9 on f9.CodeType = 'BnfKind' and f9.CodeKey = f2.BnfKind
                 left join fdcode f10 on f10.CodeType = 'Nativeplace' and f10.CodeKey = f1.nativeplace
        where f2.OrderItemNo = #{orderItemNo}
    </select>
    <select id="selectBnfInfos2" resultType="java.util.Map">
        select distinct
        f1.BnfNo                                  bnfNo,
               f1.Relation                               relation,
               f4.CodeName                               relationName,
               f1.Name                                   name,
               f1.IDType                                 idType,
               f3.CodeName                               idTypeName,
               f1.IDNo                                   idNo,
               date_format(f1.Birthday, '%Y-%m-%d')      birthday,
               f1.Sex                                    sex,
               f5.CodeName                               sexName,
               date_format(f1.idTypeEndDate, '%Y-%m-%d') idTypeEndDate,
               f1.MobilePhone                            mobilePhone,
               f1.Province                               province,
               f1.City                                   city,
               f1.County                                 county,
               f1.NativePlace                            nativePlace,
               f10.CodeName                              nativeplaceName,
               f6.PlaceName                              provinceName,
               f7.PlaceName                              cityName,
               f8.PlaceName                              countyName,
               f1.DetaileAddress                         detaileAddress,
               CAST(f2.BnfOrder as char)                 bnfOrder,
               (f2.BnfRatio * 100)         bnfRatio,
               CAST(f2.BnfRatio as char)                 coreBnfRatio,
               f2.BnfType                                bnfType,
               f2.BnfKind                                bnfKind,
               f9.CodeName                               bnfKindName,
               f1.ZipCode                                 zipCode
        from fcorderbnf f1
                 inner join fcorderbnfrela f2 on f1.BnfNo = f2.BnfNo
                 left join fdcode f3 on f3.CodeType = 'IDType' and f3.CodeKey = f1.IDType
                 left join fdcode f4 on f4.CodeType = 'Relation' and f4.CodeKey = f1.Relation
                 left join fdcode f5 on f5.CodeType = 'Sex' and f5.CodeKey = f1.Sex
                 left join fdplace f6 on f6.PlaceCode = f1.Province
                 left join fdplace f7 on f7.PlaceCode = f1.City
                 left join fdplace f8 on f8.PlaceCode = f1.County
                 left join fdcode f9 on f9.CodeType = 'BnfKind' and f9.CodeKey = f2.BnfKind
                 left join fdcode f10 on f10.CodeType = 'Nativeplace' and f10.CodeKey = f1.nativeplace
        where f2.OrderItemNo = #{orderItemNo}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fcorderbnf
        where BnfNo = #{bnfNo,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FCOrderBnf">
        insert into fcorderbnf (BnfNo, Relation, Name,
                                Sex, Birthday, IDType,
                                IDNo, idTypeEndDate, MobilePhone,
                                Address, Operator, OperatorCom,
                                MakeDate, MakeTime, ModifyDate,
                                ModifyTime)
        values (#{bnfNo,jdbcType=VARCHAR}, #{relation,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
                #{sex,jdbcType=VARCHAR}, #{birthday,jdbcType=DATE}, #{IDType,jdbcType=VARCHAR},
                #{IDNo,jdbcType=VARCHAR}, #{idTypeEndDate,jdbcType=VARCHAR}, #{mobilePhone,jdbcType=VARCHAR},
                #{address,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR},
                #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE},
                #{modifyTime,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCOrderBnf">
        insert into fcorderbnf
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bnfNo != null">
                BnfNo,
            </if>
            <if test="relation != null">
                Relation,
            </if>
            <if test="name != null">
                Name,
            </if>
            <if test="sex != null">
                Sex,
            </if>
            <if test="birthday != null">
                Birthday,
            </if>
            <if test="IDType != null">
                IDType,
            </if>
            <if test="IDNo != null">
                IDNo,
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate,
            </if>
            <if test="mobilePhone != null">
                MobilePhone,
            </if>
            <if test="address != null">
                Address,
            </if>
            <if test="nativePlace != null">
                Nativeplace,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="county != null">
                county,
            </if>
            <if test="detaileAddress != null">
                detaileAddress,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bnfNo != null">
                #{bnfNo,jdbcType=VARCHAR},
            </if>
            <if test="relation != null">
                #{relation,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                #{birthday,jdbcType=DATE},
            </if>
            <if test="IDType != null">
                #{IDType,jdbcType=VARCHAR},
            </if>
            <if test="IDNo != null">
                #{IDNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeEndDate != null">
                #{idTypeEndDate,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="nativePlace != null">
                #{nativePlace,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                #{county,jdbcType=VARCHAR},
            </if>
            <if test="detaileAddress != null">
                #{detaileAddress,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
   <insert id="insertSelectiveAll" parameterType="com.sinosoft.eflex.model.FCOrderBnf">
        insert into fcorderbnf
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bnfNo != null">
                BnfNo,
            </if>
            <if test="relation != null">
                Relation,
            </if>
            <if test="name != null">
                Name,
            </if>
            <if test="sex != null">
                Sex,
            </if>
            <if test="birthday != null">
                Birthday,
            </if>
            <if test="IDType != null">
                IDType,
            </if>
            <if test="IDNo != null">
                IDNo,
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate,
            </if>
            <if test="mobilePhone != null">
                MobilePhone,
            </if>
            <if test="address != null">
                Address,
            </if>
           <if test="address != null">
                Address,
            </if>
           <if test="zipCode != null">
               ZipCode,
            </if>
            <if test="nativePlace != null">
                Nativeplace,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="county != null">
                county,
            </if>
            <if test="detaileAddress != null">
                detaileAddress,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bnfNo != null">
                #{bnfNo,jdbcType=VARCHAR},
            </if>
            <if test="relation != null">
                #{relation,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                #{birthday,jdbcType=DATE},
            </if>
            <if test="IDType != null">
                #{IDType,jdbcType=VARCHAR},
            </if>
            <if test="IDNo != null">
                #{IDNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeEndDate != null">
                #{idTypeEndDate,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="nativePlace != null">
                #{nativePlace,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                #{county,jdbcType=VARCHAR},
            </if>
            <if test="detaileAddress != null">
                #{detaileAddress,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCOrderBnf">
        update fcorderbnf
        <set>
            <if test="relation != null">
                Relation = #{relation,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                Sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                Birthday = #{birthday,jdbcType=DATE},
            </if>
            <if test="IDType != null">
                IDType = #{IDType,jdbcType=VARCHAR},
            </if>
            <if test="IDNo != null">
                IDNo = #{IDNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate = #{idTypeEndDate,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                MobilePhone = #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                Address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="nativePlace != null">
                NativePlace = #{nativePlace,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where BnfNo = #{bnfNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKeySelectiveAll" parameterType="com.sinosoft.eflex.model.FCOrderBnf">
        update fcorderbnf
        <set>
            <if test="relation != null">
                Relation = #{relation,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                Sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                Birthday = #{birthday,jdbcType=DATE},
            </if>
            <if test="IDType != null">
                IDType = #{IDType,jdbcType=VARCHAR},
            </if>
            <if test="IDNo != null">
                IDNo = #{IDNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate = #{idTypeEndDate,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                MobilePhone = #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                Address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                ZipCode = #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="nativePlace != null">
                NativePlace = #{nativePlace,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where BnfNo = #{bnfNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCOrderBnf">
        update fcorderbnf
        set Relation      = #{relation,jdbcType=VARCHAR},
            Name          = #{name,jdbcType=VARCHAR},
            Sex           = #{sex,jdbcType=VARCHAR},
            Birthday      = #{birthday,jdbcType=DATE},
            IDType        = #{IDType,jdbcType=VARCHAR},
            IDNo          = #{IDNo,jdbcType=VARCHAR},
            idTypeEndDate = #{idTypeEndDate,jdbcType=VARCHAR},
            MobilePhone   = #{mobilePhone,jdbcType=VARCHAR},
            Address       = #{address,jdbcType=VARCHAR},
            Operator      = #{operator,jdbcType=VARCHAR},
            OperatorCom   = #{operatorCom,jdbcType=VARCHAR},
            MakeDate      = #{makeDate,jdbcType=DATE},
            MakeTime      = #{makeTime,jdbcType=VARCHAR},
            ModifyDate    = #{modifyDate,jdbcType=DATE},
            ModifyTime    = #{modifyTime,jdbcType=VARCHAR}
        where BnfNo = #{bnfNo,jdbcType=VARCHAR}
    </update>
</mapper>