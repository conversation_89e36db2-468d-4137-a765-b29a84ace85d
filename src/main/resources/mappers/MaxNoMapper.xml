<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.MaxNoMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.MaxNo">
    <id column="NoType" jdbcType="VARCHAR" property="notype" />
    <id column="NoLimit" jdbcType="VARCHAR" property="nolimit" />
    <result column="MaxNo" jdbcType="INTEGER" property="maxno" />
  </resultMap>
  <sql id="Base_Column_List">
    NoType, NoLimit, MaxNo
  </sql>
  <select id="selectByPrimaryKey" parameterType="com.sinosoft.eflex.model.MaxNo" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fdmaxno
    where NoType = #{notype,jdbcType=VARCHAR}
      and NoLimit = #{nolimit,jdbcType=VARCHAR}
      for update
      
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.sinosoft.eflex.model.MaxNo">
    delete from fdmaxno
    where NoType = #{notype,jdbcType=VARCHAR}
      and NoLimit = #{nolimit,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.MaxNo">
    insert into fdmaxno (NoType, NoLimit, MaxNo
      )
    values (#{notype,jdbcType=VARCHAR}, #{nolimit,jdbcType=VARCHAR}, #{maxno,jdbcType=INTEGER}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.MaxNo">
    update fdmaxno
    set MaxNo = #{maxno,jdbcType=INTEGER}
    where NoType = #{notype,jdbcType=VARCHAR}
      and NoLimit = #{nolimit,jdbcType=VARCHAR}
  </update>
</mapper>