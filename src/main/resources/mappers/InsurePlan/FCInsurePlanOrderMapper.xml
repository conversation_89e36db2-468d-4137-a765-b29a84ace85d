<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.InsurePlan.FcInsurePlanOrderMapper">


    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.insurePlan.InsurePlanOrder">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="insureplan_code" jdbcType="VARCHAR" property="insureplanCode" />
        <result column="company_name" jdbcType="VARCHAR" property="companyName" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
        <result column="modifier" jdbcType="VARCHAR" property="modifier" />
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    </resultMap>
    <sql id="Base_Column_List">
        id, order_no, insureplan_code, company_name, status, create_time, update_time, creator_id, 
    creator, modifier_id, modifier, is_deleted
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fcinsureplanorder
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByOrderNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fcinsureplanorder
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </select>

    <select id="selectByInsurePlanCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fcinsureplanorder
        where insureplan_code = #{insureplanCode,jdbcType=VARCHAR}
    </select>




    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from fcinsureplanorder
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.insurePlan.InsurePlanOrder">
        insert into fcinsureplanorder (id, order_no, insureplan_code,
                                       company_name, status, create_time,
                                       update_time, creator_id, creator,
                                       modifier_id, modifier, is_deleted
        )
        values (#{id,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{insureplanCode,jdbcType=VARCHAR},
                #{companyName,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP},
                #{updateTime,jdbcType=TIMESTAMP}, #{creatorId,jdbcType=BIGINT}, #{creator,jdbcType=VARCHAR},
                #{modifierId,jdbcType=BIGINT}, #{modifier,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}
               )
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.insurePlan.InsurePlanOrder">
        insert into fcinsureplanorder
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="insureplanCode != null">
                insureplan_code,
            </if>
            <if test="companyName != null">
                company_name,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="creatorId != null">
                creator_id,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifierId != null">
                modifier_id,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="insureplanCode != null">
                #{insureplanCode,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creatorId != null">
                #{creatorId,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="modifierId != null">
                #{modifierId,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.insurePlan.InsurePlanOrder">
        update fcinsureplanorder
        <set>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="insureplanCode != null">
                insureplan_code = #{insureplanCode,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creatorId != null">
                creator_id = #{creatorId,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="modifierId != null">
                modifier_id = #{modifierId,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>