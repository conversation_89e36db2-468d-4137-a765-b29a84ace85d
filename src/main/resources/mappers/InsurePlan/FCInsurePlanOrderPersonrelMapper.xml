<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.InsurePlan.FcInsurePlanOrderPersonrelMapper">

    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.insurePlan.InsurePlanOrderPersonrel">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="insureplan_code" jdbcType="VARCHAR" property="insureplanCode" />
        <result column="openId" jdbcType="VARCHAR" property="openId" />
        <result column="unionId" jdbcType="VARCHAR" property="unionId" />
        <result column="company_name" jdbcType="VARCHAR" property="companyName" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="phone" jdbcType="VARCHAR" property="phone" />
        <result column="credential_type" jdbcType="VARCHAR" property="credentialType" />
        <result column="credential_no" jdbcType="VARCHAR" property="credentialNo" />
        <result column="credential_period" jdbcType="VARCHAR" property="credentialPeriod" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
        <result column="modifier" jdbcType="VARCHAR" property="modifier" />
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    </resultMap>
    <sql id="Base_Column_List">
        id, order_no, openId, unionId,insureplan_code, company_name, name, phone, credential_type, credential_no,
    credential_period, create_time, update_time, creator_id, creator, modifier_id, modifier,
    is_deleted
    </sql>
    
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fcInsurePlanOrderPersonrel
        where id = #{id,jdbcType=BIGINT}
    </select>


    <select id="selectByOrderNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fcInsurePlanOrderPersonrel
        where is_deleted=0
        AND  order_no = #{orderNo,jdbcType=VARCHAR}
    </select>


    <select id="selectCountPersonrel"  resultType="int">
        select COUNT(*) from fcInsurePlanOrderPersonrel
        where  order_no = #{orderNo,jdbcType=VARCHAR}
    </select>



    <select id="selectByPersonrelInfo" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fcInsurePlanOrderPersonrel
        where is_deleted=0
        <if test="orderNo != null">
           AND order_no = #{orderNo,jdbcType=VARCHAR}
        </if>
        <if test="openId != null">
            AND openId = #{openId,jdbcType=VARCHAR}
        </if>
        <if test="unionId != null">
            AND unionId = #{unionId,jdbcType=VARCHAR}
        </if>
        <if test="insureplanCode != null">
            AND insureplan_code = #{insureplanCode,jdbcType=VARCHAR}
        </if>
        <if test="companyName != null">
            AND  company_name = #{companyName,jdbcType=VARCHAR}
        </if>
        <if test="name != null">
            AND name = #{name,jdbcType=VARCHAR}
        </if>
        <if test="phone != null">
            AND  phone = #{phone,jdbcType=VARCHAR}
        </if>
        <if test="credentialType != null">
            AND  credential_type = #{credentialType,jdbcType=VARCHAR}
        </if>
        <if test="credentialNo != null">
            AND  credential_no = #{credentialNo,jdbcType=VARCHAR}
        </if>
        limit 1
    </select>


    
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from fcInsurePlanOrderPersonrel
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.sinosoft.eflex.model.insurePlan.InsurePlanOrderPersonrel">
        insert into fcinsureplanorderpersonrel (id, order_no, openId,
                                                unionId, insureplan_code, company_name,
                                                name, phone, credential_type,
                                                credential_no, credential_period, create_time,
                                                update_time, creator_id, creator,
                                                modifier_id, modifier, is_deleted
        )
        values (#{id,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{openId,jdbcType=VARCHAR},
                #{unionId,jdbcType=VARCHAR}, #{insureplanCode,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR},
                #{name,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, #{credentialType,jdbcType=VARCHAR},
                #{credentialNo,jdbcType=VARCHAR}, #{credentialPeriod,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
                #{updateTime,jdbcType=TIMESTAMP}, #{creatorId,jdbcType=BIGINT}, #{creator,jdbcType=VARCHAR},
                #{modifierId,jdbcType=BIGINT}, #{modifier,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}
               )
    </insert>

    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.insurePlan.InsurePlanOrderPersonrel">
        insert into fcinsureplanorderpersonrel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="openId != null">
                openId,
            </if>
            <if test="unionId != null">
                unionId,
            </if>
            <if test="insureplanCode != null">
                insureplan_code,
            </if>
            <if test="companyName != null">
                company_name,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="credentialType != null">
                credential_type,
            </if>
            <if test="credentialNo != null">
                credential_no,
            </if>
            <if test="credentialPeriod != null">
                credential_period,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="creatorId != null">
                creator_id,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifierId != null">
                modifier_id,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="openId != null">
                #{openId,jdbcType=VARCHAR},
            </if>
            <if test="unionId != null">
                #{unionId,jdbcType=VARCHAR},
            </if>
            <if test="insureplanCode != null">
                #{insureplanCode,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="credentialType != null">
                #{credentialType,jdbcType=VARCHAR},
            </if>
            <if test="credentialNo != null">
                #{credentialNo,jdbcType=VARCHAR},
            </if>
            <if test="credentialPeriod != null">
                #{credentialPeriod,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creatorId != null">
                #{creatorId,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="modifierId != null">
                #{modifierId,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.insurePlan.InsurePlanOrderPersonrel">
        update fcinsureplanorderpersonrel
        <set>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="openId != null">
                openId = #{openId,jdbcType=VARCHAR},
            </if>
            <if test="unionId != null">
                unionId = #{unionId,jdbcType=VARCHAR},
            </if>
            <if test="insureplanCode != null">
                insureplan_code = #{insureplanCode,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="credentialType != null">
                credential_type = #{credentialType,jdbcType=VARCHAR},
            </if>
            <if test="credentialNo != null">
                credential_no = #{credentialNo,jdbcType=VARCHAR},
            </if>
            <if test="credentialPeriod != null">
                credential_period = #{credentialPeriod,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creatorId != null">
                creator_id = #{creatorId,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="modifierId != null">
                modifier_id = #{modifierId,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <update id="updateByOrderNO" parameterType="com.sinosoft.eflex.model.insurePlan.InsurePlanOrderPersonrel">
        update fcinsureplanorderpersonrel
        <set>
            <if test="openId != null">
                openId = #{openId,jdbcType=VARCHAR},
            </if>
            <if test="unionId != null">
                unionId = #{unionId,jdbcType=VARCHAR},
            </if>
            <if test="insureplanCode != null">
                insureplan_code = #{insureplanCode,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="credentialType != null">
                credential_type = #{credentialType,jdbcType=VARCHAR},
            </if>
            <if test="credentialNo != null">
                credential_no = #{credentialNo,jdbcType=VARCHAR},
            </if>
            <if test="credentialPeriod != null">
                credential_period = #{credentialPeriod,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creatorId != null">
                creator_id = #{creatorId,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="modifierId != null">
                modifier_id = #{modifierId,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=TINYINT},
            </if>
        </set>
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </update>



</mapper>