<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.InsurePlan.FcInsurePlanMapper">

	<resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.insurePlan.InsurePlanVo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="grp_no" jdbcType="VARCHAR" property="grpNo" />
		<result column="insureplan_code" jdbcType="VARCHAR" property="insureplanCode" />
		<result column="insureplan_name" jdbcType="VARCHAR" property="insureplanName" />
		<result column="insured_age_start" jdbcType="VARCHAR" property="insuredAgeStart" />
		<result column="insured_age_end" jdbcType="VARCHAR" property="insuredAgeEnd" />
		<result column="occupation_type" jdbcType="VARCHAR" property="occupationType" />
		<result column="clause" jdbcType="VARCHAR" property="clause" />
		<result column="status" jdbcType="TINYINT" property="status" />
		<result column="create_time" jdbcType="TIMESTAMP"   property="createTime" />
		<result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
		<result column="creator_id" jdbcType="BIGINT" property="creatorId" />
		<result column="creator" jdbcType="VARCHAR" property="creator" />
		<result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
		<result column="modifier" jdbcType="VARCHAR" property="modifier" />
		<result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
		<result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
		<result column="riskNames" jdbcType="TINYINT" property="riskNames" />
		<result column="amountCount" jdbcType="TINYINT" property="amountCount" />
		<result column="insured_period_type" jdbcType="VARCHAR" property="insuredPeriodType" />
		<result column="insured_period" jdbcType="VARCHAR" property="insuredPeriod" />
		<result column="prem_count" jdbcType="DECIMAL" property="premCount" />
		<result column="del" jdbcType="VARCHAR" property="del" />
		<result column="orderNo" jdbcType="VARCHAR" property="orderNo" />
		<collection property="insurePlanRiskConfigList" ofType="com.sinosoft.eflex.model.insurePlan.InsurePlanRiskConfig" javaType="java.util.List">
			<result column="insureplan_code" jdbcType="VARCHAR" property="insureplanCode" />
			<result column="risk_code" jdbcType="VARCHAR" property="riskCode" />
			<result column="RiskName" jdbcType="VARCHAR" property="riskName" />
			<result column="amount" jdbcType="DECIMAL" property="amount" />
		</collection>
	</resultMap>





	<sql id="Base_Column_List">
		id, grp_no, insureplan_code, insureplan_name, insured_age_start, insured_age_end,insured_period_type,insured_period,
    occupation_type, clause, status, create_time, update_time, creator_id, creator, modifier_id,prem_count,
    modifier, is_deleted
	</sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from fcinsureplan
		where id = #{id,jdbcType=BIGINT}
	</select>

	<delete id="delByPrimaryKey" parameterType="java.lang.Long">
		delete from fcinsureplan
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<insert id="insert" parameterType="com.sinosoft.eflex.model.insurePlan.InsurePlan">
		insert into fcinsureplan (id, grp_no, insureplan_code,
								  insureplan_name, insured_age_start, insured_age_end,
								  occupation_type, clause, status,
								  create_time, update_time, creator_id,
								  creator, modifier_id, modifier,
								  is_deleted)
		values (#{id,jdbcType=BIGINT}, #{grpNo,jdbcType=VARCHAR}, #{insureplanCode,jdbcType=VARCHAR},
				#{insureplanName,jdbcType=VARCHAR}, #{insuredAgeStart,jdbcType=VARCHAR}, #{insuredAgeEnd,jdbcType=VARCHAR},
				#{occupationType,jdbcType=VARCHAR}, #{clause,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT},
				#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{creatorId,jdbcType=BIGINT},
				#{creator,jdbcType=VARCHAR}, #{modifierId,jdbcType=BIGINT}, #{modifier,jdbcType=VARCHAR},
				#{isDeleted,jdbcType=TINYINT})
	</insert>

	<select id="selectInsurePlanByPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select fip.id,
		       fip.insureplan_name,
			   fip.creator,
			   fip.create_time,
			   GROUP_CONCAT( fdr.RiskName SEPARATOR ',' ) as riskNames,
		       sum(fpr.amount) as amountCount,
		       fip.status
		from  fcinsureplan fip
		    inner join fcinsureplanriskconfig fpr on fip.insureplan_code = fpr.insureplan_code
		    left join fdriskinfo fdr on fpr.risk_code=fdr.RiskCode
		where fip.is_deleted=0
		and fpr.is_deleted=0
		<if test="insurePlanName != null and insurePlanName != '' ">
			and fip.insureplan_name LIKE concat('%',#{insurePlanName},'%')
		</if>
		group by fip.insureplan_code
		<if test="riskName != null and riskName != '' ">
			having riskNames LIKE concat('%',#{riskName},'%')
		</if>
        order by fip.create_time desc
	</select>

	<select id="selectByInsureplanName" parameterType="java.lang.String" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from fcinsureplan
		where 1=1
		and is_deleted=0
		<if test="insureplanName != null and insureplanName != '' ">
			and insureplan_name = #{insureplanName,jdbcType=VARCHAR}
		</if>
	</select>


	<select id="selectByInsurePlanCode"  resultMap="BaseResultMap">
		 select
	         fp.id, fp.grp_no, fp.insureplan_code, fp.insureplan_name, fp.insured_age_start, fp.insured_age_end,
			 fp.occupation_type, fp.clause, fp.status,
			 fp.insured_period_type,fp.insured_period,
		     rc.insureplan_code,
		     rc.amount,
		     rc.risk_code,
		     fdr.RiskName
		 from  fcinsureplan fp
		 inner join  fcinsureplanriskconfig rc on fp.insureplan_code=rc.insureplan_code
		 left join fdriskinfo fdr  on rc.risk_code=fdr.RiskCode
		 where fp.is_deleted=0
	     and fp.insureplan_code = #{insurePlanCode,jdbcType=VARCHAR}

	</select>



	<select id="selectInsurePlanList"  resultMap="BaseResultMap">
		select
		fp.id, fp.grp_no, fp.insureplan_code, fp.insureplan_name, fp.insured_age_start, fp.insured_age_end,fp.prem_count,
		fp.occupation_type, fp.clause, fp.status,
		rc.insureplan_code,
		rc.amount,
		rc.risk_code,
		fdr.RiskName,
		case when o.insureplan_code is NULL then 1 ELSE 0 END as del,
		GROUP_CONCAT(DISTINCT o.order_no) as orderNo
		from  fcinsureplan fp
		inner join  fcinsureplanriskconfig rc on fp.insureplan_code=rc.insureplan_code
		left join fdriskinfo fdr  on rc.risk_code=fdr.RiskCode
		LEFT JOIN fcinsureplanorderpersonrel o on fp.insureplan_code=o.insureplan_code
		where fp.is_deleted=0
		and fp.status=0
		<if test="planIds != null">
			AND
			<foreach collection="planIds" item="item" index="index" separator="or" open="(" close=")">
				fp.id = #{item,jdbcType=VARCHAR}
			</foreach>
		</if>
		GROUP BY fp.insureplan_code
        order by fp.create_time desc
	</select>



	<insert id="insertSelective" parameterType="com.sinosoft.eflex.model.insurePlan.InsurePlan"
		useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		insert into fcinsureplan
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">
				id,
			</if>
			<if test="grpNo != null">
				grp_no,
			</if>
			<if test="insureplanCode != null">
				insureplan_code,
			</if>
			<if test="insureplanName != null">
				insureplan_name,
			</if>
			<if test="insuredAgeStart != null">
				insured_age_start,
			</if>
			<if test="insuredAgeEnd != null">
				insured_age_end,
			</if>
			<if test="occupationType != null">
				occupation_type,
			</if>
			<if test="clause != null">
				clause,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
			<if test="creatorId != null">
				creator_id,
			</if>
			<if test="creator != null">
				creator,
			</if>
			<if test="modifierId != null">
				modifier_id,
			</if>
			<if test="modifier != null">
				modifier,
			</if>
			<if test="isDeleted != null">
				is_deleted,
			</if>
			<if test="insuredPeriodType != null">
				insured_period_type,
			</if>
			<if test="insuredPeriod != null">
				insured_period,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id,jdbcType=BIGINT},
			</if>
			<if test="grpNo != null">
				#{grpNo,jdbcType=VARCHAR},
			</if>
			<if test="insureplanCode != null">
				#{insureplanCode,jdbcType=VARCHAR},
			</if>
			<if test="insureplanName != null">
				#{insureplanName,jdbcType=VARCHAR},
			</if>
			<if test="insuredAgeStart != null">
				#{insuredAgeStart,jdbcType=VARCHAR},
			</if>
			<if test="insuredAgeEnd != null">
				#{insuredAgeEnd,jdbcType=VARCHAR},
			</if>
			<if test="occupationType != null">
				#{occupationType,jdbcType=VARCHAR},
			</if>
			<if test="clause != null">
				#{clause,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				#{status,jdbcType=TINYINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="creatorId != null">
				#{creatorId,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				#{creator,jdbcType=VARCHAR},
			</if>
			<if test="modifierId != null">
				#{modifierId,jdbcType=BIGINT},
			</if>
			<if test="modifier != null">
				#{modifier,jdbcType=VARCHAR},
			</if>
			<if test="isDeleted != null">
				#{isDeleted,jdbcType=TINYINT},
			</if>
			<if test="insuredPeriodType != null">
				#{insuredPeriodType,jdbcType=VARCHAR},
			</if>
			<if test="insuredPeriod != null">
				#{insuredPeriod,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>
	<update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.insurePlan.InsurePlan">
		update fcinsureplan
		<set>
			<if test="grpNo != null">
				grp_no = #{grpNo,jdbcType=VARCHAR},
			</if>
			<if test="insureplanCode != null">
				insureplan_code = #{insureplanCode,jdbcType=VARCHAR},
			</if>
			<if test="insureplanName != null">
				insureplan_name = #{insureplanName,jdbcType=VARCHAR},
			</if>
			<if test="insuredAgeStart != null">
				insured_age_start = #{insuredAgeStart,jdbcType=VARCHAR},
			</if>
			<if test="insuredAgeEnd != null">
				insured_age_end = #{insuredAgeEnd,jdbcType=VARCHAR},
			</if>
			<if test="occupationType != null">
				occupation_type = #{occupationType,jdbcType=VARCHAR},
			</if>
			<if test="clause != null">
				clause = #{clause,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				status = #{status,jdbcType=TINYINT},
			</if>
			<if test="createTime != null">
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateTime != null">
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="creatorId != null">
				creator_id = #{creatorId,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				creator = #{creator,jdbcType=VARCHAR},
			</if>
			<if test="modifierId != null">
				modifier_id = #{modifierId,jdbcType=BIGINT},
			</if>
			<if test="modifier != null">
				modifier = #{modifier,jdbcType=VARCHAR},
			</if>
			<if test="isDeleted != null">
				is_deleted = #{isDeleted,jdbcType=TINYINT},
			</if>
			<if test="insuredPeriodType != null">
				insured_period_type = #{insuredPeriodType,jdbcType=VARCHAR},
			</if>
			<if test="insuredPeriod != null">
				insured_period = #{insuredPeriod,jdbcType=VARCHAR},
			</if>
			<if test="premCount != null">
				prem_count = #{premCount,jdbcType=DECIMAL},
			</if>
		</set>
		where id = #{id,jdbcType=BIGINT}
	</update>


</mapper>