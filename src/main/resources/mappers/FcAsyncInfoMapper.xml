<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FcAsyncInfoMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FcAsyncInfo">
        <id column="Id" jdbcType="INTEGER" property="id"/>
        <result column="BusinessType" jdbcType="VARCHAR" property="businessType"/>
        <result column="BusinessId" jdbcType="VARCHAR" property="businessId"/>
        <result column="DealState" jdbcType="CHAR" property="dealState"/>
        <result column="ResultCode" jdbcType="CHAR" property="resultCode"/>
        <result column="ResultMessage" jdbcType="VARCHAR" property="resultMessage"/>
        <result column="DealTime" jdbcType="VARCHAR" property="dealTime"/>
        <result column="EstimateDealTime" jdbcType="VARCHAR" property="estimateDealTime"/>
        <result column="EstimateCompleteTime" jdbcType="VARCHAR" property="estimateCompleteTime"/>
        <result column="Remark" jdbcType="VARCHAR" property="remark"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    Id, BusinessType, BusinessId, DealState, ResultCode, ResultMessage, DealTime, EstimateDealTime, 
    EstimateCompleteTime, Remark, Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, 
    ModifyTime
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcasyncinfo
        where Id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fcasyncinfo
    where Id = #{id,jdbcType=INTEGER}
  </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FcAsyncInfo">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into fcasyncinfo (BusinessType, BusinessId, DealState,
        ResultCode, ResultMessage, DealTime,
        EstimateDealTime, EstimateCompleteTime,
        Remark, Operator, OperatorCom,
        MakeDate, MakeTime, ModifyDate,
        ModifyTime)
        values (#{businessType,jdbcType=VARCHAR}, #{businessId,jdbcType=VARCHAR}, #{dealState,jdbcType=CHAR},
        #{resultCode,jdbcType=CHAR}, #{resultMessage,jdbcType=VARCHAR}, #{dealTime,jdbcType=VARCHAR},
        #{estimateDealTime,jdbcType=VARCHAR}, #{estimateCompleteTime,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR},
        #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE},
        #{modifyTime,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FcAsyncInfo">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into fcasyncinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="businessType != null">
                BusinessType,
            </if>
            <if test="businessId != null">
                BusinessId,
            </if>
            <if test="dealState != null">
                DealState,
            </if>
            <if test="resultCode != null">
                ResultCode,
            </if>
            <if test="resultMessage != null">
                ResultMessage,
            </if>
            <if test="dealTime != null">
                DealTime,
            </if>
            <if test="estimateDealTime != null">
                EstimateDealTime,
            </if>
            <if test="estimateCompleteTime != null">
                EstimateCompleteTime,
            </if>
            <if test="remark != null">
                Remark,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessType != null">
                #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="businessId != null">
                #{businessId,jdbcType=VARCHAR},
            </if>
            <if test="dealState != null">
                #{dealState,jdbcType=CHAR},
            </if>
            <if test="resultCode != null">
                #{resultCode,jdbcType=CHAR},
            </if>
            <if test="resultMessage != null">
                #{resultMessage,jdbcType=VARCHAR},
            </if>
            <if test="dealTime != null">
                #{dealTime,jdbcType=VARCHAR},
            </if>
            <if test="estimateDealTime != null">
                #{estimateDealTime,jdbcType=VARCHAR},
            </if>
            <if test="estimateCompleteTime != null">
                #{estimateCompleteTime,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FcAsyncInfo">
        update fcasyncinfo
        <set>
            <if test="businessType != null">
                BusinessType = #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="businessId != null">
                BusinessId = #{businessId,jdbcType=VARCHAR},
            </if>
            <if test="dealState != null">
                DealState = #{dealState,jdbcType=CHAR},
            </if>
            <if test="resultCode != null">
                ResultCode = #{resultCode,jdbcType=CHAR},
            </if>
            <if test="resultMessage != null">
                ResultMessage = #{resultMessage,jdbcType=VARCHAR},
            </if>
            <if test="dealTime != null">
                DealTime = #{dealTime,jdbcType=VARCHAR},
            </if>
            <if test="estimateDealTime != null">
                EstimateDealTime = #{estimateDealTime,jdbcType=VARCHAR},
            </if>
            <if test="estimateCompleteTime != null">
                EstimateCompleteTime = #{estimateCompleteTime,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                Remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where Id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FcAsyncInfo">
    update fcasyncinfo
    set BusinessType = #{businessType,jdbcType=VARCHAR},
      BusinessId = #{businessId,jdbcType=VARCHAR},
      DealState = #{dealState,jdbcType=CHAR},
      ResultCode = #{resultCode,jdbcType=CHAR},
      ResultMessage = #{resultMessage,jdbcType=VARCHAR},
      DealTime = #{dealTime,jdbcType=VARCHAR},
      EstimateDealTime = #{estimateDealTime,jdbcType=VARCHAR},
      EstimateCompleteTime = #{estimateCompleteTime,jdbcType=VARCHAR},
      Remark = #{remark,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where Id = #{id,jdbcType=INTEGER}
  </update>
    <select id="selectAsyncInfo" parameterType="com.sinosoft.eflex.model.FcAsyncInfo"
            resultType="com.sinosoft.eflex.model.FcAsyncInfo">
        select
        <include refid="Base_Column_List"/>
        from fcasyncinfo
        where 1=1
        <if test="businessType != null">
            and businessType = #{businessType}
        </if>
        <if test="businessId != null">
            and businessId = #{businessId}
        </if>
        <if test="dealState != null">
            and dealState = #{dealState}
        </if>
        <if test="resultCode != null">
            and resultCode = #{resultCode}
        </if>
        order by id desc limit 1
    </select>
</mapper>