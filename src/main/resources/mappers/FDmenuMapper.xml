<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FDmenuMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FDmenu">
    <id column="NodeCode" jdbcType="VARCHAR" property="nodeCode" />
    <result column="ParentNodeCode" jdbcType="VARCHAR" property="parentNodeCode" />
    <result column="NodeLevel" jdbcType="INTEGER" property="nodeLevel" />
    <result column="NodeName" jdbcType="VARCHAR" property="nodeName" />
    <result column="ChildFlag" jdbcType="INTEGER" property="childFlag" />
    <result column="RunScript" jdbcType="VARCHAR" property="runScript" />
    <result column="NodeImgUrl" jdbcType="VARCHAR" property="nodeImgUrl" />
    <result column="NodeDesc" jdbcType="VARCHAR" property="nodeDesc" />
    <result column="NodeOrder" jdbcType="INTEGER" property="nodeOrder" />
  </resultMap>
  <sql id="Base_Column_List">
    NodeCode, ParentNodeCode, NodeLevel, NodeName, ChildFlag, RunScript, NodeImgUrl, 
    NodeDesc, NodeOrder
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fdmenu
    where NodeCode = #{nodeCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fdmenu
    where NodeCode = #{nodeCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FDmenu">
    insert into fdmenu (NodeCode, ParentNodeCode, NodeLevel, 
      NodeName, ChildFlag, RunScript, 
      NodeImgUrl, NodeDesc, NodeOrder
      )
    values (#{nodeCode,jdbcType=VARCHAR}, #{parentNodeCode,jdbcType=VARCHAR}, #{nodeLevel,jdbcType=INTEGER}, 
      #{nodeName,jdbcType=VARCHAR}, #{childFlag,jdbcType=INTEGER}, #{runScript,jdbcType=VARCHAR}, 
      #{nodeImgUrl,jdbcType=VARCHAR}, #{nodeDesc,jdbcType=VARCHAR}, #{nodeOrder,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FDmenu">
    insert into fdmenu
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="nodeCode != null">
        NodeCode,
      </if>
      <if test="parentNodeCode != null">
        ParentNodeCode,
      </if>
      <if test="nodeLevel != null">
        NodeLevel,
      </if>
      <if test="nodeName != null">
        NodeName,
      </if>
      <if test="childFlag != null">
        ChildFlag,
      </if>
      <if test="runScript != null">
        RunScript,
      </if>
      <if test="nodeImgUrl != null">
        NodeImgUrl,
      </if>
      <if test="nodeDesc != null">
        NodeDesc,
      </if>
      <if test="nodeOrder != null">
        NodeOrder,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="nodeCode != null">
        #{nodeCode,jdbcType=VARCHAR},
      </if>
      <if test="parentNodeCode != null">
        #{parentNodeCode,jdbcType=VARCHAR},
      </if>
      <if test="nodeLevel != null">
        #{nodeLevel,jdbcType=INTEGER},
      </if>
      <if test="nodeName != null">
        #{nodeName,jdbcType=VARCHAR},
      </if>
      <if test="childFlag != null">
        #{childFlag,jdbcType=INTEGER},
      </if>
      <if test="runScript != null">
        #{runScript,jdbcType=VARCHAR},
      </if>
      <if test="nodeImgUrl != null">
        #{nodeImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="nodeDesc != null">
        #{nodeDesc,jdbcType=VARCHAR},
      </if>
      <if test="nodeOrder != null">
        #{nodeOrder,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FDmenu">
    update fdmenu
    <set>
      <if test="parentNodeCode != null">
        ParentNodeCode = #{parentNodeCode,jdbcType=VARCHAR},
      </if>
      <if test="nodeLevel != null">
        NodeLevel = #{nodeLevel,jdbcType=INTEGER},
      </if>
      <if test="nodeName != null">
        NodeName = #{nodeName,jdbcType=VARCHAR},
      </if>
      <if test="childFlag != null">
        ChildFlag = #{childFlag,jdbcType=INTEGER},
      </if>
      <if test="runScript != null">
        RunScript = #{runScript,jdbcType=VARCHAR},
      </if>
      <if test="nodeImgUrl != null">
        NodeImgUrl = #{nodeImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="nodeDesc != null">
        NodeDesc = #{nodeDesc,jdbcType=VARCHAR},
      </if>
      <if test="nodeOrder != null">
        NodeOrder = #{nodeOrder,jdbcType=INTEGER},
      </if>
    </set>
    where NodeCode = #{nodeCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FDmenu">
    update fdmenu
    set ParentNodeCode = #{parentNodeCode,jdbcType=VARCHAR},
      NodeLevel = #{nodeLevel,jdbcType=INTEGER},
      NodeName = #{nodeName,jdbcType=VARCHAR},
      ChildFlag = #{childFlag,jdbcType=INTEGER},
      RunScript = #{runScript,jdbcType=VARCHAR},
      NodeImgUrl = #{nodeImgUrl,jdbcType=VARCHAR},
      NodeDesc = #{nodeDesc,jdbcType=VARCHAR},
      NodeOrder = #{nodeOrder,jdbcType=INTEGER}
    where NodeCode = #{nodeCode,jdbcType=VARCHAR}
  </update>
    <select id="findMenuInfo" parameterType="java.lang.String" resultType="java.util.HashMap">
	select m.NodeCode,
	       m.ParentNodeCode,
	       m.NodeLevel,
	       m.NodeName,
	       m.ChildFlag,
	       m.RunScript,
	       m.NodeImgUrl,
	       m.NodeOrder,
	       gm.menugrpcode as menugrpcode
	  from fdusertomenugrp ug, fdmenugrptomenu gm, fdmenu m,fduserrole f1,fdmenugrp f2
	 where  gm.nodecode = m.nodecode
	 and ug.userno = #{userNo,jdbcType=VARCHAR}
	 and gm.menugrpcode = ug.menugrpcode
	 and f1.userNo = ug.userNo
	 and f1.roleType = f2.roleType
	 and f2.menuGrpCode = ug.menugrpcode
	 order by m.nodelevel, m.nodeorder
  </select>
    <select id="findMenuInfo1" parameterType="com.sinosoft.eflex.model.configmanage.FindMenuInfoReq"
            resultType="java.util.HashMap">
        select m.NodeCode,
        m.ParentNodeCode,
        m.NodeLevel,
        m.NodeName,
        m.ChildFlag,
        m.RunScript,
        m.NodeImgUrl,
        m.NodeOrder,
        gm.menugrpcode as menugrpcode
        from fdusertomenugrp ug, fdmenugrptomenu gm, fdmenu m,fduserrole f1,fdmenugrp f2
        where gm.nodecode = m.nodecode
        and ug.userno = #{userNo,jdbcType=VARCHAR}
        and gm.menugrpcode = ug.menugrpcode
        and f1.userNo = ug.userNo
        and f1.roleType = f2.roleType
        and f2.menuGrpCode = ug.menugrpcode
        <if test="userRole != null">
            and find_in_set(f1.roleType,#{userRole})
        </if>
        order by m.nodelevel, m.nodeorder
    </select>
</mapper>