<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FDImpartInfoMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FDImpartInfo">
    <id column="ImpartCode" jdbcType="VARCHAR" property="impartCode" />
    <result column="ImpartVer" jdbcType="VARCHAR" property="impartVer" />
    <result column="ImpartContent" jdbcType="VARCHAR" property="impartContent" />
    <result column="ImpartParamModle" jdbcType="VARCHAR" property="impartParamModle" />
  </resultMap>
  <sql id="Base_Column_List">
    ImpartCode, ImpartVer, ImpartContent, ImpartParamModle
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fdimpartinfo
    where ImpartCode = #{impartCode,jdbcType=VARCHAR}
  </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fdimpartinfo
    where ImpartCode = #{impartCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FDImpartInfo">
    insert into fdimpartinfo (ImpartCode, ImpartVer, ImpartContent, 
      ImpartParamModle)
    values (#{impartCode,jdbcType=VARCHAR}, #{impartVer,jdbcType=VARCHAR}, #{impartContent,jdbcType=VARCHAR}, 
      #{impartParamModle,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FDImpartInfo">
    insert into fdimpartinfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="impartCode != null">
        ImpartCode,
      </if>
      <if test="impartVer != null">
        ImpartVer,
      </if>
      <if test="impartContent != null">
        ImpartContent,
      </if>
      <if test="impartParamModle != null">
        ImpartParamModle,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="impartCode != null">
        #{impartCode,jdbcType=VARCHAR},
      </if>
      <if test="impartVer != null">
        #{impartVer,jdbcType=VARCHAR},
      </if>
      <if test="impartContent != null">
        #{impartContent,jdbcType=VARCHAR},
      </if>
      <if test="impartParamModle != null">
        #{impartParamModle,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FDImpartInfo">
    update fdimpartinfo
    <set>
      <if test="impartVer != null">
        ImpartVer = #{impartVer,jdbcType=VARCHAR},
      </if>
      <if test="impartContent != null">
        ImpartContent = #{impartContent,jdbcType=VARCHAR},
      </if>
      <if test="impartParamModle != null">
        ImpartParamModle = #{impartParamModle,jdbcType=VARCHAR},
      </if>
    </set>
    where ImpartCode = #{impartCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FDImpartInfo">
    update fdimpartinfo
    set ImpartVer = #{impartVer,jdbcType=VARCHAR},
      ImpartContent = #{impartContent,jdbcType=VARCHAR},
      ImpartParamModle = #{impartParamModle,jdbcType=VARCHAR}
    where ImpartCode = #{impartCode,jdbcType=VARCHAR}
  </update>
</mapper>