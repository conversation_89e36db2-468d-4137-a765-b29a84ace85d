<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCOrderItemMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCOrderItem">
        <id column="OrderItemNo" jdbcType="VARCHAR" property="orderItemNo"/>
        <result column="OrderNo" jdbcType="VARCHAR" property="orderNo"/>
        <result column="CostNo" jdbcType="VARCHAR" property="costNo"/>
        <result column="OrderItemDetailNo" jdbcType="VARCHAR" property="orderItemDetailNo"/>
        <result column="GrpContNo" jdbcType="VARCHAR" property="grpContNo"/>
        <result column="ContNo" jdbcType="VARCHAR" property="contNo"/>
        <result column="PayPersonId" jdbcType="VARCHAR" property="payPersonId"/>
        <result column="SelfPrem" jdbcType="DOUBLE" property="selfPrem"/>
        <result column="GrpPrem" jdbcType="DOUBLE" property="grpPrem"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>

    <!-- 投保清单导出excel -->
    <resultMap type="com.sinosoft.eflex.model.FCOrderItem" id="InsuredDetails"
               extends="BaseResultMap">
        <association property="fcOrderItemDetail"
                     column="{ensureCode=ensureCode,ensureType=ensureType,OrderItemNo=OrderItemNo,orderItemDetailNo=OrderItemDetailNo,garName=garName,garSex=garSex,stuName=stuName,planName=planName,planObject=planObject,department=department}"
                     javaType="com.sinosoft.eflex.model.FCOrderItemDetail"
                     select="com.sinosoft.eflex.dao.FCOrderItemDetailMapper.selectInsuredDetail"
                     fetchType="lazy"/>
        <association property="fcOrderInsured"
                     javaType="com.sinosoft.eflex.model.FCOrderInsured"
                     column="{orderNo=OrderNo,orderItemNo=OrderItemNo,ensureType=ensureType,stuName=stuName,garName=garName,garSex=garSex,planName=planName,planObject=planObject,department=department}"
                     select="com.sinosoft.eflex.dao.FCOrderInsuredMapper.selectInsuredDetail"
                     fetchType="lazy"/>
    </resultMap>
    <!-- 投保清单导出excel -->
    <select id="selectInsuredDetails" parameterType="java.util.Map"
            resultMap="InsuredDetails">
        select case when ('${ensureType}' != '') then '${ensureType}' else '' end as ensureType,
               case when ('${garName}' != '') then '${garName}' else '' end       as garName,
               case when ('${garSex}' != '') then '${garSex}' else '' end         as garSex,
               case when ('${department}' != '') then '${department}' else '' end as department,
               case when ('${stuName}' != '') then '${stuName}' else '' end       as stuName,
               case when ('${planName}' != '') then '${planName}' else '' end     as planName,
               case WHEN ('${planObject}' != '') then '${planObject}' else '' end as planObject,
               case WHEN ('${ensureCode}' != '') then '${ensureCode}' else '' end as ensureCode,
               OrderItemNo,
               OrderNo,
               OrderItemDetailNo,
               SelfPrem,
               GrpPrem
        from fcorderitem
        where OrderNo = #{orderNo,jdbcType=VARCHAR}
    </select>
    <sql id="Base_Column_List">
        OrderItemNo
        , OrderNo, CostNo, OrderItemDetailNo, GrpContNo, ContNo, PayPersonId, IFNULL(CAST((SelfPrem) as decimal(20,2)),0.00) as SelfPrem,IFNULL(CAST(GrpPrem as DECIMAL(20,2)),0.00) as GrpPrem,
    Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
    </sql>
    <sql id="ShareRela_Column_List">
        ShareDetalNo
        , OrderItemNo, ShareState, IsLock,
    Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcorderitem
        where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fcorderitem
        where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FCOrderItem">
        insert into fcorderitem (OrderItemNo, OrderNo, CostNo,
                                 OrderItemDetailNo, GrpContNo, ContNo,
                                 SelfPrem, GrpPrem, Operator,
                                 OperatorCom, MakeDate, MakeTime,
                                 ModifyDate, ModifyTime)
        values (#{orderItemNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{costNo,jdbcType=VARCHAR},
                #{orderItemDetailNo,jdbcType=VARCHAR}, #{grpContNo,jdbcType=VARCHAR}, #{contNo,jdbcType=VARCHAR},
                #{selfPrem,jdbcType=DOUBLE}, #{grpPrem,jdbcType=DOUBLE}, #{operator,jdbcType=VARCHAR},
                #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR},
                #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCOrderItem">
        insert into fcorderitem
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderItemNo != null">
                OrderItemNo,
            </if>
            <if test="orderNo != null">
                OrderNo,
            </if>
            <if test="costNo != null">
                CostNo,
            </if>
            <if test="orderItemDetailNo != null">
                OrderItemDetailNo,
            </if>
            <if test="grpContNo != null">
                GrpContNo,
            </if>
            <if test="contNo != null">
                ContNo,
            </if>
            <if test="selfPrem != null">
                SelfPrem,
            </if>
            <if test="grpPrem != null">
                GrpPrem,
            </if>
            <if test="payPersonId != null">
                PayPersonId,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderItemNo != null">
                #{orderItemNo,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="costNo != null">
                #{costNo,jdbcType=VARCHAR},
            </if>
            <if test="orderItemDetailNo != null">
                #{orderItemDetailNo,jdbcType=VARCHAR},
            </if>
            <if test="grpContNo != null">
                #{grpContNo,jdbcType=VARCHAR},
            </if>
            <if test="contNo != null">
                #{contNo,jdbcType=VARCHAR},
            </if>
            <if test="selfPrem != null">
                #{selfPrem,jdbcType=DOUBLE},
            </if>
            <if test="grpPrem != null">
                #{grpPrem,jdbcType=DOUBLE},
            </if>
            <if test="payPersonId != null">
                #{payPersonId,jdbcType=DOUBLE},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCOrderItem">
        update fcorderitem
        <set>
            <if test="orderNo != null">
                OrderNo = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="costNo != null">
                CostNo = #{costNo,jdbcType=VARCHAR},
            </if>
            <if test="orderItemDetailNo != null">
                OrderItemDetailNo =
                #{orderItemDetailNo,jdbcType=VARCHAR},
            </if>
            <if test="grpContNo != null">
                GrpContNo = #{grpContNo,jdbcType=VARCHAR},
            </if>
            <if test="contNo != null">
                ContNo = #{contNo,jdbcType=VARCHAR},
            </if>
            <if test="payPersonId != null">
                PayPersonId = #{payPersonId,jdbcType=VARCHAR},
            </if>
            <if test="selfPrem != null">
                SelfPrem = #{selfPrem,jdbcType=DOUBLE},
            </if>
            <if test="grpPrem != null">
                GrpPrem = #{grpPrem,jdbcType=DOUBLE},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCOrderItem">
        update fcorderitem
        set OrderNo           = #{orderNo,jdbcType=VARCHAR},
            CostNo            =
                #{costNo,jdbcType=VARCHAR},
            OrderItemDetailNo =
                #{orderItemDetailNo,jdbcType=VARCHAR},
            GrpContNo         =
                #{grpContNo,jdbcType=VARCHAR},
            ContNo            = #{contNo,jdbcType=VARCHAR},
            SelfPrem          = #{selfPrem,jdbcType=DOUBLE},
            GrpPrem           = #{grpPrem,jdbcType=DOUBLE},
            Operator          = #{operator,jdbcType=VARCHAR},
            OperatorCom       =
                #{operatorCom,jdbcType=VARCHAR},
            MakeDate          = #{makeDate,jdbcType=DATE},
            MakeTime          = #{makeTime,jdbcType=VARCHAR},
            ModifyDate        =
                #{modifyDate,jdbcType=DATE},
            ModifyTime        =
                #{modifyTime,jdbcType=VARCHAR}
        where OrderItemNo =
              #{orderItemNo,jdbcType=VARCHAR}
    </update>

    <!-- 选择计划详情:员工选择计划 -->
    <select id="selectEmployPlanList" parameterType="java.util.Map"
            resultType="java.util.Map">
        select insured.Name as name,insured.Department as
        department,plan.PlanName as planName
        from
        (select OrderNo,perno,grpOrderNo from fcorder
        <where>
            <if test="grpNo != null and grpNo != ''">
                GrpNo=#{grpNo,jdbcType=VARCHAR}
            </if>
        </where>
        ) fcorder
        inner join fcperregistday fp on fcorder.perno = fp.perno and fp.ensureCode=#{ensureCode,jdbcType=VARCHAR}
        inner join
        fcgrporder grp
        on
        grp.grpOrderNo = fcorder.grpOrderNo and grp.ensureCode=#{ensureCode,jdbcType=VARCHAR}
        join
        fcorderitem item
        on
        fcorder.OrderNo = item.OrderNo
        join
        fcorderinsured insured
        on
        insured.OrderNo = item.OrderNo and
        insured.OrderItemNo = item.OrderItemNo
        join
        (select
        OrderItemDetailNo
        ,ProductCode
        from
        fcorderitemdetail
        where
        ProductEleCode="001" and
        Value="1") detail
        on
        item.OrderItemDetailNo = detail.OrderItemDetailNo
        join
        fcensureplan plan
        on
        detail.ProductCode = plan.PlanCode and plan.ensureCode=#{ensureCode,jdbcType=VARCHAR}
        <where>
            fp.LockState = '0'
            <if test="employName != null and employName !=''">
                <!-- 根据员工名称模糊查询 -->
                <bind name="employNameLike" value=" '%'+employName+ '%'"/>
                and insured.Name like #{employNameLike,jdbcType=VARCHAR}
            </if>
            <if test="planName != null and planName !=''">
                and plan.PlanName = #{planName,jdbcType=VARCHAR}
            </if>
            <if test="department != null and department !=''">
                and insured.Department = #{department,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!-- 选择计划详情:家属选择计划 -->
    <select id="selectRelationPlanList" parameterType="java.util.Map"
            resultType="java.util.Map">
        select insured.Department as
        department,plan.PlanName as planName, info.Name as employeeName,insured.name faName
        from
        (select OrderNo,PerNo,GrpNo,grpOrderNo from fcorder
        <where>
            <if test="grpNo != null and grpNo != ''">
                GrpNo=#{grpNo,jdbcType=VARCHAR}
            </if>
        </where>
        ) fcorder
        inner join fcperregistday fp on fcorder.perno = fp.perno and fp.ensureCode=#{ensureCode,jdbcType=VARCHAR} and
        fp.LockState = '0'
        inner join
        fcgrporder grp
        on grp.grpOrderNo = fcorder.grpOrderNo and grp.ensureCode=#{ensureCode,jdbcType=VARCHAR}
        join
        fcperinfo info
        on
        fcorder.GrpNo = info.GrpNo and
        fcorder.PerNo = info.PerNo
        join
        fcorderitem item
        on
        fcorder.OrderNo =
        item.OrderNo
        join
        fcorderinsured insured
        on
        insured.OrderNo = item.OrderNo
        and insured.OrderItemNo = item.OrderItemNo
        join
        (select
        OrderItemDetailNo ,ProductCode
        from
        fcorderitemdetail
        where
        ProductEleCode="001" and Value="2") detail
        on
        item.OrderItemDetailNo =
        detail.OrderItemDetailNo
        join
        fcensureplan plan
        on
        detail.ProductCode =
        plan.PlanCode and plan.ensureCode=#{ensureCode,jdbcType=VARCHAR}
        <where>
            <if test="relationName != null and relationName !=''">
                <!-- 根据家属姓名模糊查询 -->
                <bind name="relationNameLike" value=" '%' + relationName + '%' "/>
                insured.Name like #{relationNameLike,jdbcType=VARCHAR}
            </if>
            <if test="planName != null and planName !=''">
                and plan.PlanName = #{planName,jdbcType=VARCHAR}
            </if>
            <if test="department != null and department !=''">
                and insured.Department = #{department,jdbcType=VARCHAR}
            </if>
            <if test="employName != null and employName !=''">
                <!-- 根据员工姓名模糊查询 -->
                <bind name="employNameLike" value=" '%' + employName + '%' "/>
                and info.Name like #{employNameLike,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="queryStudentPlanList" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT insured.name AS stuName,info.name AS garName,plan.planName AS planName FROM fcperinfo info
        INNER JOIN fcorder orde ON orde.GrpNo = info.GrpNo AND orde.perNo = info.perNo
        INNER JOIN fcorderitem item ON item.OrderNo = orde.OrderNo
        INNER JOIN fcorderinsured insured ON insured.OrderItemNo = item.OrderItemNo
        INNER JOIN fcorderitemdetail detail ON detail.OrderItemDetailNo = item.OrderItemDetailNo
        INNER JOIN fcgrporder fg ON orde.grporderno = fg.grporderno
        INNER JOIN fcensureplan plan ON plan.PlanCode = detail.ProductCode and plan.ensureCode=fg.EnsureCode
        INNER JOIN fcensure ensure ON ensure.ensureCode = plan.ensureCode
        WHERE ensure.ensureCode = #{ensureCode,jdbcType=VARCHAR}
        <if test=" planName != null and planName != ''">
            <bind name="planNameLike" value=" '%' + planName + '%' "/>
            AND plan.planName LIKE #{planNameLike,jdbcType=VARCHAR}
        </if>
        <if test=" stuName != null and stuName != ''">
            <bind name="stuNameLike" value=" '%' + stuName + '%' "/>
            AND insured.name LIKE #{stuNameLike,jdbcType=VARCHAR}
        </if>
        <if test=" garName != null and garName != ''">
            <bind name="garNameLike" value=" '%' + garName + '%' "/>
            AND info.name LIKE #{garNameLike,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectList" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcorderitem
        where 1=1
        <if test="orderNo != null">
            and OrderNo = #{orderNo,jdbcType=VARCHAR}
        </if>
        <if test="orderItemNo != null">
            and OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
        </if>
        order by OrderItemNo
    </select>
    <select id="selectOrderNo" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCOrderItem">
        select
        <include refid="Base_Column_List"/>
        from fcorderitem
        where
        1=1
        <if test="orderNo != null">
            and OrderNo = #{orderNo,jdbcType=VARCHAR}
        </if>
    </select>
    <!-- 查询投保福利总人数 -->
    <select id="getEnsureNum" parameterType="java.lang.String" resultType="int">
        select count(*)
        from fcorderitem fi
        where fi.OrderNo in (
            select fd.orderno
            from fcorder fd
            where fd.GrpOrderNo = (
                select grporderno from fcgrporder fg where EnsureCode = #{ensureCode,jdbcType=VARCHAR})
        )
    </select>
    <delete id="deleteByOrderNo" parameterType="java.lang.String">
        delete
        from fcorderitem
        where orderno = #{orderNo,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByPersonId" parameterType="java.lang.String">
        delete
        from fcorderitem
        where orderno = #{orderNo,jdbcType=VARCHAR}
          and orderitemno in (
            select orderitemno
            from fcorderinsured
            where orderno = #{orderNo,jdbcType=VARCHAR}
              and personid = #{personId,jdbcType=VARCHAR}
        )
    </delete>

    <select id="getByPersonId" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCOrderItem">
        SELECT *
        FROM fcorderitem
        WHERE OrderItemNo = (SELECT OrderItemNo
                             FROM fcorderinsured
                             WHERE grporderNo IN (SELECT GrpOrderNo
                                                  FROM fcgrporder
                                                  WHERE EnsureCode = #{ensureCode,jdbcType=VARCHAR})
                               AND personID = #{personId} order by OrderItemNo desc limit 1)
    </select>

    <select id="getSumTotalPrem" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT SUM(GrpPrem)
        FROM fcorderitem
        WHERE OrderNo IN (SELECT OrderNo
                          FROM fcorder
                          WHERE GrpOrderNo = #{grpOrderNo,jdbcType=VARCHAR}
                            AND PerNo = #{perNo,jdbcType=VARCHAR})
    </select>

    <select id="selectPremByEnsureCode" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT SUM(a.`GrpPrem`) AS grpPrem, SUM(a.`SelfPrem`) AS selfPrem
        FROM fcorderitem a
                 RIGHT JOIN fcorderinsured c ON a.`OrderItemNo` = c.`OrderItemNo`
                 INNER JOIN fcorder b ON a.`OrderNo` = b.`OrderNo`
                 INNER JOIN fcgrporder d ON b.`GrpOrderNo` = d.`GrpOrderNo`
        WHERE d.EnsureCode = #{ensureCode}
    </select>

    <select id="selectPerOrderItemByEnsureCode" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT a.`OrderItemDetailNo`
        FROM fcorderitem a
                 INNER JOIN fcorder b ON a.`OrderNo` = b.`OrderNo`
                 INNER JOIN fcgrporder d ON b.`GrpOrderNo` = d.`GrpOrderNo`
                 INNER JOIN fcorderinsured e ON a.`OrderItemNo` = e.`OrderItemNo`
                 INNER JOIN fcstafffamilyrela f ON f.`PersonID` = e.`PersonID`
        WHERE d.EnsureCode = #{ensureCode}
          AND f.`Relation` = '0'
    </select>

    <select id="getPremByEnsureCode" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT FORMAT(SUM(a.`prem`), 2) AS prem
        FROM FcInsureEflexPlan a
                 INNER JOIN fcorderitem c ON a.OrderItemDetailNo = c.`OrderItemDetailNo`
                 INNER JOIN fcorder d ON d.`OrderNo` = c.`OrderNo`
                 INNER JOIN fcgrporder e ON e.`GrpOrderNo` = d.`GrpOrderNo`
        WHERE e.`EnsureCode` = #{ensureCode}
        UNION
        SELECT FORMAT(SUM(b.Prem), 2) AS prem
        FROM FcInsureEflexPlanOptional b
                 INNER JOIN fcorderitem c ON b.OrderItemDetailNo = c.`OrderItemDetailNo`
                 INNER JOIN fcorder d ON d.`OrderNo` = c.`OrderNo`
                 INNER JOIN fcgrporder e ON e.`GrpOrderNo` = d.`GrpOrderNo`
        WHERE e.`EnsureCode` = #{ensureCode}
    </select>

    <select id="selectOrderDutyInfoByRelation" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT CONVERT(SUM(a.`prem`),DECIMAL(20,2)) AS prem,COUNT(d.`OrderItemNo`) AS peoples FROM fcinsureeflexplan a
        INNER JOIN fcorderitem b ON a.`OrderItemDetailNo` = b.`OrderItemDetailNo`
        INNER JOIN fcorderinsured d ON b.`OrderItemNo` = d.`OrderItemNo`
        INNER JOIN fcorder e ON d.`OrderNo` = e.`OrderNo`
        INNER JOIN fcgrporder f ON e.`GrpOrderNo` = f.`GrpOrderNo`
        INNER JOIN FcDutyAmountGrade c ON a.`AmountGrageCode` = c.`AmountGrageCode` AND c.EnsureCode = #{ensureCode} AND
        c.DutyCode = #{dutyCode}
        WHERE f.`EnsureCode` = #{ensureCode}
        <if test=' check == 0  '>
            AND (SELECT Relation FROM fcstafffamilyrela WHERE PerNo = e.`PerNo` AND PersonID = d.`PersonID`) = '0'
        </if>
        <if test=' check != 0 '>
            AND (SELECT Relation FROM fcstafffamilyrela WHERE PerNo = e.`PerNo` AND PersonID = d.`PersonID`) in (1,2,3)
        </if>
    </select>

    <select id="selectOrderOptDutyInfoByRelation" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT SUM(a.`prem`) AS prem,COUNT(d.`OrderItemNo`) AS peoples FROM fcinsureeflexplanoptional a
        INNER JOIN fcorderitem b ON a.`OrderItemDetailNo` = b.`OrderItemDetailNo`
        INNER JOIN fcorderinsured d ON b.`OrderItemNo` = d.`OrderItemNo`
        INNER JOIN fcorder e ON d.`OrderNo` = e.`OrderNo`
        INNER JOIN fcgrporder f ON e.`GrpOrderNo` = f.`GrpOrderNo`
        INNER JOIN fcdutygradeoptionalamountinfo c ON a.`AmountGrageCode` = c.`AmountGrageCode` AND c.`OptDutyCode` =
        #{dutyCode}
        WHERE c.`AmountGrageCode` IN (SELECT AmountGrageCode FROM FcDutyAmountGrade WHERE EnsureCode = #{ensureCode} AND
        RiskCode = #{riskCode}) AND a.`OptDutyCode` = #{dutyCode}
        AND f.`EnsureCode` = #{ensureCode}
        <if test=' check == 0  '>
            AND (SELECT Relation FROM fcstafffamilyrela WHERE PerNo = e.`PerNo` AND PersonID = d.`PersonID`) = '0'
        </if>
        <if test=' check != 0 '>
            AND (SELECT Relation FROM fcstafffamilyrela WHERE PerNo = e.`PerNo` AND PersonID = d.`PersonID`) in (1,2,3)
        </if>
    </select>
    <select id="selectBygrpContNoAndOrderNo" parameterType="java.util.Map"
            resultType="com.sinosoft.eflex.model.FCOrderItem">
        select
        <include refid="Base_Column_List"/>
        from fcorderitem
        where
        1=1
        <if test="grpContNo != null and grpContNo != ''">
            and grpContNo = #{grpContNo,jdbcType=VARCHAR}
        </if>
        <if test="orderNo != null and orderNo != ''">
            and OrderNo = #{orderNo,jdbcType=VARCHAR}
        </if>
    </select>
    <update id="updateFcorderItemByContNo" parameterType="java.util.Map">
        update FcorderItem
        <set>
            <if test="selfPrem != null">
                SelfPrem = #{selfPrem,jdbcType=DOUBLE},
            </if>
            <if test="grpPrem != null">
                GrpPrem = #{grpPrem,jdbcType=DOUBLE},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where ContNo = #{contNo,jdbcType=VARCHAR}
    </update>
    <insert id="insertShareRelaSelective" parameterType="com.sinosoft.eflex.model.FcOrderItemShareRela">
        insert into fcorderitemsharerela
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shareDetalNo != null">
                ShareDetalNo,
            </if>
            <if test="orderItemNo != null">
                OrderItemNo,
            </if>
            <if test="shareState != null">
                ShareState,
            </if>
            <if test="isLock != null">
                IsLock,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shareDetalNo != null">
                #{shareDetalNo,jdbcType=VARCHAR},
            </if>
            <if test="orderItemNo != null">
                #{orderItemNo,jdbcType=VARCHAR},
            </if>
            <if test="shareState != null">
                #{shareState,jdbcType=VARCHAR},
            </if>
            <if test="isLock != null">
                #{isLock,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateShareIsLock" parameterType="java.lang.String">
        update fcorderitemsharerela
        set ShareState = '1'
        where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
          and IsLock = '0'
          and ShareState = '0'
    </update>
    <update id="updateShareToLock" parameterType="java.lang.String">
        update fcorderitemsharerela
        set IsLock = '1'
        where OrderItemNo = #{orderItemNo,jdbcType=VARCHAR}
          and IsLock = '0'
          and ShareState = '0'
    </update>

    <select id="selectShareRela" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.FcOrderItemShareRela">
        select
        <include refid="ShareRela_Column_List"/>
        from fcorderitemsharerela
        where
        ShareDetalNo = #{shareDetalNo,jdbcType=VARCHAR}
    </select>
    <select id="selectShareRelaByorderiterNo" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.FcOrderItemShareRela">
        select
        <include refid="ShareRela_Column_List"/>
        from fcorderitemsharerela
        where
        OrderItemNo = #{orderItemNo,jdbcType=VARCHAR} and ShareState = '0' and IsLock = '1'
    </select>
    <update id="updateShareStatus" parameterType="java.lang.String">
        update fcorderitemsharerela
        set ShareState = '1'
        where ShareDetalNo = #{shareDetalNo,jdbcType=VARCHAR}
    </update>

    <select id="selectOrderItemCountByEnsureCode" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(*)
        from fcorderitem
        where orderNo in (select OrderNo
                          from fcorder
                          where GrpOrderNo in (select grpOrderNo from fcgrporder where ensureCode = #{ensureCode}))
    </select>

    <select id="selectFamilyCountByEnsureCode" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(*)
        from fcorderitem m
                 join fcorder r on m.OrderNo = r.OrderNo
                 join fcgrporder o on o.GrpOrderNo = r.GrpOrderNo
        where o.ensureCode = #{ensureCode}
    </select>


    <select id="queryOrderitem" resultType="com.sinosoft.eflex.model.FCOrderItem">
        select *
        from fcorderitem
        where OrderNo = (
            select fcorderitem.OrderNo from fcorderitem where OrderItemNo = #{orderItemNo}
        )
    </select>
    <select id="selectByOrderNoAndPerNo" resultType="com.sinosoft.eflex.model.OrderItemDetailData">
        select f.OrderItemNo as OrderItemNo,
               contNo,
               grpPrem,
               selfPrem,
               ProductCode,
               f2.personid   as personid,
               name,
               sex,
               birthday,
               nativeplace,
               idtype,
               idno,
               f3.Relation   as Relation
        from fcorderitem f
                 left join fcorderitemdetail f1 on f.OrderItemDetailNo = f1.OrderItemDetailNo
                 left join fcorderinsured f2 on f.OrderItemNo = f2.OrderItemNo
                 left join fcstafffamilyrela f3 on f2.PersonID = f3.PersonID
        where 1 = 1
          and f.OrderNo = #{orderNo,jdbcType=VARCHAR}
          and f3.PerNo = #{perNo,jdbcType=VARCHAR}
        order by f.OrderItemNo;
    </select>
    <select id="selectPlanCode" resultType="com.sinosoft.eflex.model.FCOrderItemDetail">
        select fl.*
        from fcorderitem fm
                 left join fcorderitemdetail fl on fm.OrderItemDetailNo = fl.OrderItemDetailNo
        where OrderItemNo = #{orderItemNo}

    </select>

    <select id="selectByOrderNoList" resultType="com.sinosoft.eflex.model.FCOrderItem">
        select * from fcorderitem where
        orderNo IN
        <foreach item="item" index="index" collection="list" open="(" separator=", " close=")">
            #{item}
        </foreach>
    </select>


    <update id="updateByOrderNo" parameterType="com.sinosoft.eflex.model.FCOrderItem">
        update fcorderitem
        <set>
            <if test="costNo != null">
                CostNo = #{costNo,jdbcType=VARCHAR},
            </if>
            <if test="orderItemDetailNo != null">
                OrderItemDetailNo =
                #{orderItemDetailNo,jdbcType=VARCHAR},
            </if>
            <if test="grpContNo != null">
                GrpContNo = #{grpContNo,jdbcType=VARCHAR},
            </if>
            <if test="contNo != null">
                ContNo = #{contNo,jdbcType=VARCHAR},
            </if>
            <if test="payPersonId != null">
                PayPersonId = #{payPersonId,jdbcType=VARCHAR},
            </if>
            <if test="selfPrem != null">
                SelfPrem = #{selfPrem,jdbcType=DOUBLE},
            </if>
            <if test="grpPrem != null">
                GrpPrem = #{grpPrem,jdbcType=DOUBLE},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where OrderNo = #{orderNo,jdbcType=VARCHAR}
    </update>

    <update id="updateByGrpContNo">
        update fcorderitem set GrpContNo = #{grpContNo,jdbcType=VARCHAR} where
        orderNo IN
        <foreach item="item" index="index" collection="list" open="(" separator=", " close=")">
            #{item}
        </foreach>
    </update>
</mapper>