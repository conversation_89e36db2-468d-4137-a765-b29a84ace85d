<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCUserLoginMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCUserLogin">
    <id column="LoginSerialNo" jdbcType="VARCHAR" property="loginserialno" />
    <result column="UserNo" jdbcType="VARCHAR" property="userno" />
    <result column="LoginDate" jdbcType="DATE" property="logindate" />
    <result column="LoginTime" jdbcType="VARCHAR" property="logintime" />
    <result column="LoginIP" jdbcType="VARCHAR" property="loginip" />
    <result column="LoginType" jdbcType="VARCHAR" property="logintype" />
    <result column="LoginSource" jdbcType="VARCHAR" property="loginsource" />
    <result column="LogoutDate" jdbcType="DATE" property="logoutdate" />
    <result column="LogoutTime" jdbcType="VARCHAR" property="logouttime" />
  </resultMap>
  <sql id="Base_Column_List">
    LoginSerialNo, UserNo, LoginDate, LoginTime, LoginIP, LoginType, LoginSource, LogoutDate, 
    LogoutTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcuserlogin
    where LoginSerialNo = #{loginserialno,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcuserlogin
    where LoginSerialNo = #{loginserialno,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCUserLogin">
    insert into fcuserlogin (LoginSerialNo, UserNo, LoginDate, 
      LoginTime, LoginIP, LoginType, 
      LoginSource, LogoutDate, LogoutTime
      )
    values (#{loginserialno,jdbcType=VARCHAR}, #{userno,jdbcType=VARCHAR}, #{logindate,jdbcType=DATE}, 
      #{logintime,jdbcType=VARCHAR}, #{loginip,jdbcType=VARCHAR}, #{logintype,jdbcType=VARCHAR}, 
      #{loginsource,jdbcType=VARCHAR}, #{logoutdate,jdbcType=DATE}, #{logouttime,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCUserLogin">
    insert into fcuserlogin
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="loginserialno != null">
        LoginSerialNo,
      </if>
      <if test="userno != null">
        UserNo,
      </if>
      <if test="logindate != null">
        LoginDate,
      </if>
      <if test="logintime != null">
        LoginTime,
      </if>
      <if test="loginip != null">
        LoginIP,
      </if>
      <if test="logintype != null">
        LoginType,
      </if>
      <if test="loginsource != null">
        LoginSource,
      </if>
      <if test="logoutdate != null">
        LogoutDate,
      </if>
      <if test="logouttime != null">
        LogoutTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="loginserialno != null">
        #{loginserialno,jdbcType=VARCHAR},
      </if>
      <if test="userno != null">
        #{userno,jdbcType=VARCHAR},
      </if>
      <if test="logindate != null">
        #{logindate,jdbcType=DATE},
      </if>
      <if test="logintime != null">
        #{logintime,jdbcType=VARCHAR},
      </if>
      <if test="loginip != null">
        #{loginip,jdbcType=VARCHAR},
      </if>
      <if test="logintype != null">
        #{logintype,jdbcType=VARCHAR},
      </if>
      <if test="loginsource != null">
        #{loginsource,jdbcType=VARCHAR},
      </if>
      <if test="logoutdate != null">
        #{logoutdate,jdbcType=DATE},
      </if>
      <if test="logouttime != null">
        #{logouttime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCUserLogin">
    update fcuserlogin
    <set>
      <if test="userno != null">
        UserNo = #{userno,jdbcType=VARCHAR},
      </if>
      <if test="logindate != null">
        LoginDate = #{logindate,jdbcType=DATE},
      </if>
      <if test="logintime != null">
        LoginTime = #{logintime,jdbcType=VARCHAR},
      </if>
      <if test="loginip != null">
        LoginIP = #{loginip,jdbcType=VARCHAR},
      </if>
      <if test="logintype != null">
        LoginType = #{logintype,jdbcType=VARCHAR},
      </if>
      <if test="loginsource != null">
        LoginSource = #{loginsource,jdbcType=VARCHAR},
      </if>
      <if test="logoutdate != null">
        LogoutDate = #{logoutdate,jdbcType=DATE},
      </if>
      <if test="logouttime != null">
        LogoutTime = #{logouttime,jdbcType=VARCHAR},
      </if>
    </set>
    where LoginSerialNo = #{loginserialno,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCUserLogin">
    update fcuserlogin
    set UserNo = #{userno,jdbcType=VARCHAR},
      LoginDate = #{logindate,jdbcType=DATE},
      LoginTime = #{logintime,jdbcType=VARCHAR},
      LoginIP = #{loginip,jdbcType=VARCHAR},
      LoginType = #{logintype,jdbcType=VARCHAR},
      LoginSource = #{loginsource,jdbcType=VARCHAR},
      LogoutDate = #{logoutdate,jdbcType=DATE},
      LogoutTime = #{logouttime,jdbcType=VARCHAR}
    where LoginSerialNo = #{loginserialno,jdbcType=VARCHAR}
  </update>
  <select id="selectLastLoginByUserNo" parameterType="java.lang.String" resultMap="BaseResultMap">
	    select fl.LoginSerialNo,
	       fl.UserNo,
	       fl.LoginDate,
	       fl.LoginTime,
	       fl.LoginIP,
	       fl.LoginType,
	       fl.LoginSource,
	       fl.LogoutDate,
	       fl.LogoutTime
	  from fcuserlogin fl
	 where fl.loginserialno =
	       (select max(f.loginserialno) from fcuserlogin f where f.userno = #{userNo,jdbcType=VARCHAR})
  </select>
    <select id="selectUserLoginTimesByUserNo" parameterType="java.lang.String" resultType="java.lang.Integer">
	    select
	      count(*)
	    from fcuserlogin f
	    where f.userno = #{userNo,jdbcType=VARCHAR}
  </select>
</mapper>