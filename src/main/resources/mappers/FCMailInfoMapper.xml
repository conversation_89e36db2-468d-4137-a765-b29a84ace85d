<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCMailInfoMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCMailInfo">
    <id column="InvoiceInfoSN" jdbcType="VARCHAR" property="invoiceInfoSN" />
    <result column="GrpNo" jdbcType="VARCHAR" property="grpNo" />
    <result column="PersonId" jdbcType="VARCHAR" property="personId" />
    <result column="CustomType" jdbcType="VARCHAR" property="customType" />
    <result column="InvoiceType" jdbcType="VARCHAR" property="invoiceType" />
    <result column="InvoiceAmount" jdbcType="DOUBLE" property="invoiceAmount" />
    <result column="Receiver" jdbcType="VARCHAR" property="receiver" />
    <result column="TelPhone" jdbcType="VARCHAR" property="telPhone" />
    <result column="Zipcode" jdbcType="VARCHAR" property="zipcode" />
    <result column="PayName" jdbcType="VARCHAR" property="payName" />
    <result column="Email" jdbcType="VARCHAR" property="email" />
    <result column="Location" jdbcType="VARCHAR" property="location" />
    <result column="Address" jdbcType="VARCHAR" property="address" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
    <result column="EnsureCode" jdbcType="VARCHAR" property="ensureCode" />
    <result column="ApplicantName" jdbcType="VARCHAR" property="applicantName" />
  </resultMap>
  <sql id="Base_Column_List">
    InvoiceInfoSN, GrpNo, PersonId, CustomType, InvoiceType, InvoiceAmount, Receiver, 
    TelPhone, Zipcode, PayName, Email, Location, Address, Operator, OperatorCom, MakeDate, 
    MakeTime, ModifyDate, ModifyTime,EnsureCode,applicantName
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcmailinfo
    where InvoiceInfoSN = #{invoiceInfoSN,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcmailinfo
    where InvoiceInfoSN = #{invoiceInfoSN,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCMailInfo">
    insert into fcmailinfo (InvoiceInfoSN, GrpNo, PersonId, 
      CustomType, InvoiceType, InvoiceAmount, 
      Receiver, TelPhone, Zipcode, 
      PayName, Email, Location, 
      Address, Operator, OperatorCom, 
      MakeDate, MakeTime, ModifyDate, 
      ModifyTime,EnsureCode,ApplicantName)
    values (#{invoiceInfoSN,jdbcType=VARCHAR}, #{grpNo,jdbcType=VARCHAR}, #{personId,jdbcType=VARCHAR}, 
      #{customType,jdbcType=VARCHAR}, #{invoiceType,jdbcType=VARCHAR}, #{invoiceAmount,jdbcType=DOUBLE}, 
      #{receiver,jdbcType=VARCHAR}, #{telPhone,jdbcType=VARCHAR}, #{zipcode,jdbcType=VARCHAR}, 
      #{payName,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{location,jdbcType=VARCHAR}, 
      #{address,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, 
      #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, 
      #{modifyTime,jdbcType=VARCHAR},#{ensureCode,jdbcType=VARCHAR},#{applicantName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCMailInfo">
    insert into fcmailinfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceInfoSN != null">
        InvoiceInfoSN,
      </if>
      <if test="grpNo != null">
        GrpNo,
      </if>
      <if test="personId != null">
        PersonId,
      </if>
      <if test="customType != null">
        CustomType,
      </if>
      <if test="invoiceType != null">
        InvoiceType,
      </if>
      <if test="invoiceAmount != null">
        InvoiceAmount,
      </if>
      <if test="receiver != null">
        Receiver,
      </if>
      <if test="telPhone != null">
        TelPhone,
      </if>
      <if test="zipcode != null">
        Zipcode,
      </if>
      <if test="payName != null">
        PayName,
      </if>
      <if test="email != null">
        Email,
      </if>
      <if test="location != null">
        Location,
      </if>
      <if test="address != null">
        Address,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
      <if test="ensureCode != null">
        EnsureCode
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="invoiceInfoSN != null">
        #{invoiceInfoSN,jdbcType=VARCHAR},
      </if>
      <if test="grpNo != null">
        #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="personId != null">
        #{personId,jdbcType=VARCHAR},
      </if>
      <if test="customType != null">
        #{customType,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=VARCHAR},
      </if>
      <if test="invoiceAmount != null">
        #{invoiceAmount,jdbcType=DOUBLE},
      </if>
      <if test="receiver != null">
        #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="telPhone != null">
        #{telPhone,jdbcType=VARCHAR},
      </if>
      <if test="zipcode != null">
        #{zipcode,jdbcType=VARCHAR},
      </if>
      <if test="payName != null">
        #{payName,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
      <if test="ensureCode != null">
        #{ensureCode,jdbcType=VARCHAR}
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCMailInfo">
    update fcmailinfo
    <set>
      <if test="grpNo != null">
        GrpNo = #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="personId != null">
        PersonId = #{personId,jdbcType=VARCHAR},
      </if>
      <if test="customType != null">
        CustomType = #{customType,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        InvoiceType = #{invoiceType,jdbcType=VARCHAR},
      </if>
      <if test="invoiceAmount != null">
        InvoiceAmount = #{invoiceAmount,jdbcType=DOUBLE},
      </if>
      <if test="receiver != null">
        Receiver = #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="telPhone != null">
        TelPhone = #{telPhone,jdbcType=VARCHAR},
      </if>
      <if test="zipcode != null">
        Zipcode = #{zipcode,jdbcType=VARCHAR},
      </if>
      <if test="payName != null">
        PayName = #{payName,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        Email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        Location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        Address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
      ModifyTime = #{modifyTime,jdbcType=VARCHAR},
    </if>
      <if test="ensureCode != null">
        EnsureCode=#{ensureCode,jdbcType=VARCHAR}
      </if>

    </set>
    where InvoiceInfoSN = #{invoiceInfoSN,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCMailInfo">
    update fcmailinfo
    set GrpNo = #{grpNo,jdbcType=VARCHAR},
      PersonId = #{personId,jdbcType=VARCHAR},
      CustomType = #{customType,jdbcType=VARCHAR},
      InvoiceType = #{invoiceType,jdbcType=VARCHAR},
      InvoiceAmount = #{invoiceAmount,jdbcType=DOUBLE},
      Receiver = #{receiver,jdbcType=VARCHAR},
      TelPhone = #{telPhone,jdbcType=VARCHAR},
      Zipcode = #{zipcode,jdbcType=VARCHAR},
      PayName = #{payName,jdbcType=VARCHAR},
      Email = #{email,jdbcType=VARCHAR},
      Location = #{location,jdbcType=VARCHAR},
      Address = #{address,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      EnsureCode=#{ensureCode,jdbcType=VARCHAR}
    where InvoiceInfoSN = #{invoiceInfoSN,jdbcType=VARCHAR}
  </update>

  <select id="getMailInfoByPersonId" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FCMailInfo">
    select
    <include refid="Base_Column_List" />
    from fcmailinfo
    <where>
      1=1
      <if test="ensureCode != null">
       and EnsureCode=#{ensureCode,jdbcType=VARCHAR}
      </if>
      <if test="personId != null">
       and PersonId = #{personId,jdbcType=VARCHAR}
      </if>
      <if test="customType != null">
       and CustomType = #{customType,jdbcType=VARCHAR}
      </if>
      <if test="grpNo != null">
        and GrpNo = #{grpNo,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
    <select id="selectByEnsureCode" resultType="java.lang.Integer">
      select count(1) from fcmailinfo where EnsureCode = #{ensurecode}
    </select>
</mapper>