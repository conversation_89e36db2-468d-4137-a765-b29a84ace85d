<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FRCalModeMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FRCalMode">
    <id column="CalCode" jdbcType="VARCHAR" property="calCode" />
    <result column="RiskCode" jdbcType="VARCHAR" property="riskCode" />
    <result column="Type" jdbcType="VARCHAR" property="type" />
    <result column="CalSQL" jdbcType="VARCHAR" property="calSQL" />
    <result column="Remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    CalCode, RiskCode, Type, CalSQL, Remark
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap" flushCache="true"
            useCache="false">
    select 
    <include refid="Base_Column_List" />
    from frcalmode
    where CalCode = #{calCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from frcalmode
    where CalCode = #{calCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FRCalMode">
    insert into frcalmode (CalCode, RiskCode, Type, 
      CalSQL, Remark)
    values (#{calCode,jdbcType=VARCHAR}, #{riskCode,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, 
      #{calSQL,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FRCalMode">
    insert into frcalmode
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="calCode != null">
        CalCode,
      </if>
      <if test="riskCode != null">
        RiskCode,
      </if>
      <if test="type != null">
        Type,
      </if>
      <if test="calSQL != null">
        CalSQL,
      </if>
      <if test="remark != null">
        Remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="calCode != null">
        #{calCode,jdbcType=VARCHAR},
      </if>
      <if test="riskCode != null">
        #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="calSQL != null">
        #{calSQL,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FRCalMode">
    update frcalmode
    <set>
      <if test="riskCode != null">
        RiskCode = #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        Type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="calSQL != null">
        CalSQL = #{calSQL,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        Remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where CalCode = #{calCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FRCalMode">
    update frcalmode
    set RiskCode = #{riskCode,jdbcType=VARCHAR},
      Type = #{type,jdbcType=VARCHAR},
      CalSQL = #{calSQL,jdbcType=VARCHAR},
      Remark = #{remark,jdbcType=VARCHAR}
    where CalCode = #{calCode,jdbcType=VARCHAR}
  </update>
</mapper>