<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.AmountTrailMapper">
    <select id="amountTrial_14110" parameterType="com.sinosoft.eflex.model.DailyAmountTrail" resultType="java.lang.String">
   		 SELECT IFNULL(CAST(FORMAT(((#{prem}+0)/1000*Amount),2) AS Char),'') from fcdailyrate_14110
	   		 WHERE RiskCode=#{riskCode}
             <if test="planCode != null and planCode !='' ">
	   		      and PlanCode=#{planCode}
	   		 </if>
	   		 and InsurePeriod=#{insurePeriod}
	   		 and InsureAge=TIMESTAMPDIFF(YEAR, #{birthDay}, #{insureDate})
	   		 and Gender=#{gender}
	   		 and PayPeriod=#{payPeriod}
    </select>
</mapper>