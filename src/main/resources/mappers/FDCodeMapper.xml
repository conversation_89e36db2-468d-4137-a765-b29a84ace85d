<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FDCodeMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FDCode">
    <id column="CodeType" jdbcType="VARCHAR" property="codeType" />
    <id column="CodeKey" jdbcType="VARCHAR" property="codeKey" />
    <result column="CoreName" jdbcType="VARCHAR" property="coreName" />
    <result column="CoreCode" jdbcType="VARCHAR" property="coreCode" />
    <result column="CodeName" jdbcType="VARCHAR" property="codeName" />
    <result column="CodeDesc" jdbcType="VARCHAR" property="codeDesc" />
    <result column="OtherSign" jdbcType="VARCHAR" property="otherSign" />
    <result column="CodeOrder" jdbcType="INTEGER" property="codeOrder" />
  </resultMap>
  <sql id="Base_Column_List">
    CodeType, CodeKey, CoreName, CoreCode, CodeName, CodeDesc, OtherSign, CodeOrder
  </sql>
  <select id="selectByPrimaryKey" parameterType="com.sinosoft.eflex.model.FDCodeKey" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fdcode
    where CodeType = #{codeType,jdbcType=VARCHAR}
      and CodeKey = #{codeKey,jdbcType=VARCHAR}
  </select>
  <select id="selectByCodeName" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FDCode">
    select
    <include refid="Base_Column_List" />
    from fdcode
    where CodeType = #{codeType,jdbcType=VARCHAR}
    and CodeName = #{codeName,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.sinosoft.eflex.model.FDCodeKey">
    delete from fdcode
    where CodeType = #{codeType,jdbcType=VARCHAR}
      and CodeKey = #{codeKey,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FDCode">
    insert into fdcode (CodeType, CodeKey, CoreName, 
      CoreCode, CodeName, CodeDesc, 
      OtherSign, CodeOrder)
    values (#{codeType,jdbcType=VARCHAR}, #{codeKey,jdbcType=VARCHAR}, #{coreName,jdbcType=VARCHAR}, 
      #{coreCode,jdbcType=VARCHAR}, #{codeName,jdbcType=VARCHAR}, #{codeDesc,jdbcType=VARCHAR}, 
      #{otherSign,jdbcType=VARCHAR}, #{codeOrder,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FDCode">
    insert into fdcode
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="codeType != null">
        CodeType,
      </if>
      <if test="codeKey != null">
        CodeKey,
      </if>
      <if test="coreName != null">
        CoreName,
      </if>
      <if test="coreCode != null">
        CoreCode,
      </if>
      <if test="codeName != null">
        CodeName,
      </if>
      <if test="codeDesc != null">
        CodeDesc,
      </if>
      <if test="otherSign != null">
        OtherSign,
      </if>
      <if test="codeOrder != null">
        CodeOrder,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="codeType != null">
        #{codeType,jdbcType=VARCHAR},
      </if>
      <if test="codeKey != null">
        #{codeKey,jdbcType=VARCHAR},
      </if>
      <if test="coreName != null">
        #{coreName,jdbcType=VARCHAR},
      </if>
      <if test="coreCode != null">
        #{coreCode,jdbcType=VARCHAR},
      </if>
      <if test="codeName != null">
        #{codeName,jdbcType=VARCHAR},
      </if>
      <if test="codeDesc != null">
        #{codeDesc,jdbcType=VARCHAR},
      </if>
      <if test="otherSign != null">
        #{otherSign,jdbcType=VARCHAR},
      </if>
      <if test="codeOrder != null">
        #{codeOrder,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FDCode">
    update fdcode
    <set>
      <if test="coreName != null">
        CoreName = #{coreName,jdbcType=VARCHAR},
      </if>
      <if test="coreCode != null">
        CoreCode = #{coreCode,jdbcType=VARCHAR},
      </if>
      <if test="codeName != null">
        CodeName = #{codeName,jdbcType=VARCHAR},
      </if>
      <if test="codeDesc != null">
        CodeDesc = #{codeDesc,jdbcType=VARCHAR},
      </if>
      <if test="otherSign != null">
        OtherSign = #{otherSign,jdbcType=VARCHAR},
      </if>
      <if test="codeOrder != null">
        CodeOrder = #{codeOrder,jdbcType=INTEGER},
      </if>
    </set>
    where CodeType = #{codeType,jdbcType=VARCHAR}
      and CodeKey = #{codeKey,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FDCode">
    update fdcode
    set CoreName = #{coreName,jdbcType=VARCHAR},
      CoreCode = #{coreCode,jdbcType=VARCHAR},
      CodeName = #{codeName,jdbcType=VARCHAR},
      CodeDesc = #{codeDesc,jdbcType=VARCHAR},
      OtherSign = #{otherSign,jdbcType=VARCHAR},
      CodeOrder = #{codeOrder,jdbcType=INTEGER}
    where CodeType = #{codeType,jdbcType=VARCHAR}
      and CodeKey = #{codeKey,jdbcType=VARCHAR}
  </update>


  <select id="findCodeInfo" resultType="java.util.Map" parameterType="java.lang.String">
    select
   CodeKey,CodeName,OtherSign
    from  fdcode where CodeType=#{codeType,jdbcType=VARCHAR} order by CodeOrder
  </select>
  
  <!-- 根据CodeKey和CodeType取CodeName -->
    <select id="CodeInfo" resultType="java.util.HashMap" parameterType="java.lang.String">
    select
   		CodeName,CodeKey
    from  fdcode where CodeType=#{codeType,jdbcType=VARCHAR}
  </select>

  <select id="selectNameByCode" resultType="java.lang.String">
    select
      codeName
    from fdcode
    where CodeType = #{codeType,jdbcType=VARCHAR}
    and CodeKey = #{codeKey,jdbcType=VARCHAR}
  </select>

  <select id="selectKeyByCodeName" resultType="java.lang.String">
    select
      codeKey
    from fdcode
    where CodeType = #{codeType,jdbcType=VARCHAR}
    and CodeName = #{codeName,jdbcType=VARCHAR}
  </select>

  <select id="getOccupationCode" resultType="java.util.HashMap">
    select
    CodeName,CodeKey,OtherSign
    from fdcode
    <where>
      1=1
      <if test="codeType != null and codeType!=''">
        and CodeType = #{codeType,jdbcType=VARCHAR}
      </if>
      <if test="codeDesc != null and codeDesc!=''">
        and CodeDesc = #{codeDesc,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
 <select id="getOccupationCodeOneToFour" resultType="java.util.HashMap">
    select
    CodeName,CodeKey,OtherSign
    from fdcode
    <where>
      1=1
      <if test="codeType != null and codeType!=''">
        and CodeType = #{codeType,jdbcType=VARCHAR}
      </if>
      <if test="codeDesc != null and codeDesc!=''">
        and CodeDesc = #{codeDesc,jdbcType=VARCHAR}
      </if>
    </where>
    AND ((othersign IN('1','2','3','4')) OR (othersign IS NULL))
  </select>
<select id="getOccupationCodeOneTosix" resultType="java.util.HashMap">
    select
    CodeName,CodeKey,OtherSign
    from fdcode
    <where>
      1=1
      <if test="codeType != null and codeType!=''">
        and CodeType = #{codeType,jdbcType=VARCHAR}
      </if>
      <if test="codeDesc != null and codeDesc!=''">
        and CodeDesc = #{codeDesc,jdbcType=VARCHAR}
      </if>
    </where>
    AND ((othersign IN('1','2','3','4','5','6')) OR (othersign IS NULL))
  </select>

  <select id="selectByCodeKey" resultType="com.sinosoft.eflex.model.FDCode" parameterType="java.lang.String">
    select * FROM fdcode where codekey = #{codeKey} AND codetype = 'SOccupationCode'
  </select>
  <select id="selectSOccupationCodeOtherSign" parameterType="java.lang.String" resultType="java.lang.String">
      select OtherSign from fdcode where CodeType = 'SOccupationCode' and CodeKey = #{codeKey}
  </select>
    <select id="selectCodeNameByCodeType" resultType="java.lang.String">
      select CodeName from fdcode where CodeType = #{codeType}
    </select>
  <select id="selectCodeKeyOrCodeName" resultType="java.lang.String">
    select
    <if test="codeName != null">
      CodeKey
    </if>
    <if test="codeKey != null">
      CodeName
    </if>
    from fdcode
    where CodeType = #{codeType,jdbcType=VARCHAR}
    <if test="codeKey != null">
      and CodeType = #{codeKey,jdbcType=VARCHAR}
    </if>
    <if test="codeName != null">
      and CodeName = #{codeName,jdbcType=VARCHAR}
    </if>
  </select>
  <select id="selectOccupationType" resultType="java.lang.String">
    select OtherSign from fdcode where CodeType = 'OccupationDetail' and CodeKey = #{occupationCode}
  </select>
  <select id="selectCodeKeyByCodeType" resultType="java.lang.String">
     select CodeKey from fdcode where CodeType = #{codeType}
  </select>
  <select id="getprovince_list" resultType="java.util.Map" parameterType="java.lang.String">
     select placeCode,PlaceName from fdplace where UpplaceCode is null
  </select>
  <select id="getcity_list" resultType="java.util.Map" parameterType="java.lang.String">
     select placeCode,PlaceName from fdplace where UpplaceCode is not null and UpplaceCode in (select placeCode from fdplace where UpplaceCode is null)
  </select>

    <select id="getcounty_list" resultType="java.util.Map" parameterType="java.lang.String">
     select placeCode,PlaceName from fdplace where UpplaceCode is not null and UpplaceCode in (select placeCode from fdplace where UpplaceCode is not null)
  </select>
    <select id="findCodeInfoNew" parameterType="java.lang.String" resultType="java.util.HashMap">
    select
   CodeKey,CodeName
    from  fdcode where CodeType=#{codeType,jdbcType=VARCHAR} and OtherSign=#{otherSign,jdbcType=VARCHAR} order by CodeOrder
  </select>
    <select id="selectSingleInfoByCountyCode" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(*)
        from fdadministrative
        where countyCode = #{countyCode}
    </select>
    <select id="selectCoreCode" parameterType="com.sinosoft.eflex.model.FDCode" resultType="java.lang.String">
        select coreCode
        from fdcode
        where codeType = #{codeType}
          and codeKey = #{codeKey}
    </select>
    <select id="selectCodeAndCoreCode" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FDCode">
        select codeKey, coreCode
        from fdcode
        where codeType = #{codeType}
    </select>
    <select id="selectOtherSign" resultType="java.lang.String">
        select OtherSign
        from fdcode
        where CodeType = #{codeType,jdbcType=VARCHAR}
          and CodeKey = #{codeKey,jdbcType=VARCHAR}
    </select>
    <select id="selectCoreCodeKey" resultType="java.lang.String">
        select coreCode
        from fdcode
        where codeType = #{codeType}
          and codeKey = #{codeKey}
    </select>
  <select id="queryNameByCode" resultType="java.lang.String">
    select
      CodeName
    from  fdcode where CodeKey=#{openBank} and CodeType ='BankSign' order by CodeOrder
  </select>

</mapper>