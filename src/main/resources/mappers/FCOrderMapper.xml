<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCOrderMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCOrder">
        <id column="OrderNo" jdbcType="VARCHAR" property="orderNo"/>
        <result column="OrderStatus" jdbcType="VARCHAR" property="orderStatus"/>
        <result column="OrderType" jdbcType="VARCHAR" property="orderType"/>
        <result column="OpenDay" jdbcType="DATE" property="openDay"/>
        <result column="CloseDay" jdbcType="DATE" property="closeDay"/>
        <result column="GrpOrderNo" jdbcType="VARCHAR" property="grpOrderNo"/>
        <result column="GrpNo" jdbcType="VARCHAR" property="grpNo"/>
        <result column="PerNo" jdbcType="VARCHAR" property="perNo"/>
        <result column="PerAppNo" jdbcType="VARCHAR" property="perAppNo"/>
        <result column="CostNo" jdbcType="VARCHAR" property="costNo"/>
        <result column="PubEleNo" jdbcType="VARCHAR" property="pubEleNo"/>
        <result column="CommitDate" jdbcType="DATE" property="commitDate"/>
        <result column="EffectDate" jdbcType="DATE" property="effectDate"/>
        <result column="IsInfo" jdbcType="VARCHAR" property="isInfo"/>
        <result column="OrderSource" jdbcType="VARCHAR" property="orderSource"/>
        <result column="ClientNo" jdbcType="VARCHAR" property="clientNo"/>
        <result column="IsLock" jdbcType="VARCHAR" property="isLock"/>
        <result column="OrderSign" jdbcType="VARCHAR" property="orderSign"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>

    <!-- 导出投保清单excel -->
    <resultMap type="com.sinosoft.eflex.model.FCOrder" id="InsuredDetails"
               extends="BaseResultMap">
        <association property="fcPerInfo"
                     column="{perNo=PerNo,grpNo=GrpNo,ensureType=ensureType,garName=garName,garSex=garSex,stuName=stuName,planName=planName,planObject=planObject,department=department}"
                     select="com.sinosoft.eflex.dao.FCPerInfoMapper.selectInsuredDetail"
                     fetchType="lazy"/>
        <collection property="fcOrderItems"
                    column="{ensureCode=ensureCode,orderNo=OrderNo,ensureType=ensureType,garName=garName,garSex=garSex,stuName=stuName,planName=planName,planObject=planObject,department=department}"
                    javaType="java.util.ArrayList" ofType="com.sinosoft.eflex.model.FCOrderItem"
                    select="com.sinosoft.eflex.dao.FCOrderItemMapper.selectInsuredDetails"
                    fetchType="lazy"/>
    </resultMap>

    <sql id="Base_Column_List">
        OrderNo
        , OrderStatus, OrderType, OpenDay, CloseDay,
		GrpOrderNo, GrpNo, PerNo,
		PerAppNo,
		CostNo, PubEleNo, CommitDate,
		EffectDate, IsInfo, OrderSource, ClientNo,IsLock,OrderSign,
		Operator,
		OperatorCom,
		MakeDate, MakeTime, ModifyDate, ModifyTime
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcorder
        where OrderNo = #{orderNo,jdbcType=VARCHAR}
    </select>
    <select id="selectOrderByOrderItemNo" parameterType="java.lang.String"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcorder
        where OrderNo = (select orderno from fcorderitem where orderitemno=#{orderItemNo,jdbcType=VARCHAR})
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fcorder
        where OrderNo = #{orderNo,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FCOrder">
        insert into fcorder (OrderNo,
                             OrderStatus, OrderType,
                             OpenDay, CloseDay, GrpOrderNo,
                             GrpNo, PerNo,
                             PerAppNo,
                             CostNo, PubEleNo, CommitDate,
                             EffectDate, IsInfo, OrderSource,
                             ClientNo, Operator, OperatorCom,
                             MakeDate, MakeTime, ModifyDate,
                             ModifyTime)
        values (#{orderNo,jdbcType=VARCHAR},
                #{orderStatus,jdbcType=VARCHAR},
                #{orderType,jdbcType=VARCHAR},
                #{openDay,jdbcType=DATE}, #{closeDay,jdbcType=DATE},
                #{grpOrderNo,jdbcType=VARCHAR},
                #{grpNo,jdbcType=VARCHAR},
                #{perNo,jdbcType=VARCHAR},
                #{perAppNo,jdbcType=VARCHAR},
                #{costNo,jdbcType=VARCHAR}, #{pubEleNo,jdbcType=VARCHAR},
                #{commitDate,jdbcType=DATE},
                #{effectDate,jdbcType=DATE},
                #{isInfo,jdbcType=VARCHAR},
                #{orderSource,jdbcType=VARCHAR},
                #{clientNo,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
                #{operatorCom,jdbcType=VARCHAR},
                #{makeDate,jdbcType=DATE},
                #{makeTime,jdbcType=VARCHAR},
                #{modifyDate,jdbcType=DATE},
                #{modifyTime,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCOrder">
        insert into fcorder
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">
                OrderNo,
            </if>
            <if test="orderStatus != null">
                OrderStatus,
            </if>
            <if test="orderType != null">
                OrderType,
            </if>
            <if test="openDay != null">
                OpenDay,
            </if>
            <if test="closeDay != null">
                CloseDay,
            </if>
            <if test="grpOrderNo != null">
                GrpOrderNo,
            </if>
            <if test="grpNo != null">
                GrpNo,
            </if>
            <if test="perNo != null">
                PerNo,
            </if>
            <if test="perAppNo != null">
                PerAppNo,
            </if>
            <if test="costNo != null">
                CostNo,
            </if>
            <if test="pubEleNo != null">
                PubEleNo,
            </if>
            <if test="commitDate != null">
                CommitDate,
            </if>
            <if test="effectDate != null">
                EffectDate,
            </if>
            <if test="isInfo != null">
                IsInfo,
            </if>
            <if test="orderSource != null">
                OrderSource,
            </if>
            <if test="clientNo != null">
                ClientNo,
            </if>
            <if test="isLock != null">
                IsLock,
            </if>
            <if test="orderSign != null">
                OrderSign,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderStatus != null">
                #{orderStatus,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null">
                #{orderType,jdbcType=VARCHAR},
            </if>
            <if test="openDay != null">
                #{openDay,jdbcType=DATE},
            </if>
            <if test="closeDay != null">
                #{closeDay,jdbcType=DATE},
            </if>
            <if test="grpOrderNo != null">
                #{grpOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="grpNo != null">
                #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="perNo != null">
                #{perNo,jdbcType=VARCHAR},
            </if>
            <if test="perAppNo != null">
                #{perAppNo,jdbcType=VARCHAR},
            </if>
            <if test="costNo != null">
                #{costNo,jdbcType=VARCHAR},
            </if>
            <if test="pubEleNo != null">
                #{pubEleNo,jdbcType=VARCHAR},
            </if>
            <if test="commitDate != null">
                #{commitDate,jdbcType=DATE},
            </if>
            <if test="effectDate != null">
                #{effectDate,jdbcType=DATE},
            </if>
            <if test="isInfo != null">
                #{isInfo,jdbcType=VARCHAR},
            </if>
            <if test="orderSource != null">
                #{orderSource,jdbcType=VARCHAR},
            </if>
            <if test="clientNo != null">
                #{clientNo,jdbcType=VARCHAR},
            </if>
            <if test="isLock != null">
                #{isLock,jdbcType=VARCHAR},
            </if>
            <if test="orderSign != null">
                #{orderSign,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="insertFCOrderReview" parameterType="java.util.Map">
        insert into FCOrderReview
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="OrderReviewNo != null">
                OrderReviewNo,
            </if>
            OrderItemNo,
            PrePrem,
            PreAmnt,
            Amnt,
            <if test="Prem != null">
                Prem,
            </if>
            <if test="ReviewState != null">
                ReviewState,
            </if>
            <if test="Approved != null">
                Approved,
            </if>
            <if test="FailedCause != null">
                FailedCause,
            </if>
            <if test="Operator != null">
                Operator,
            </if>
            <if test="OperatorCom != null">
                OperatorCom,
            </if>
            <if test="MakeDate != null">
                MakeDate,
            </if>
            <if test="MakeTime != null">
                MakeTime,
            </if>
            <if test="ModifyDate != null">
                ModifyDate,
            </if>
            <if test="ModifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="OrderReviewNo != null">
                #{OrderReviewNo,jdbcType=VARCHAR},
            </if>
            (select orderitemno from fcorderitem where contno=#{contNo,jdbcType=VARCHAR}),
            (select IFNULL(selfprem,0) + IFNULL(grpprem,0) from fcorderitem where contno=#{contNo,jdbcType=VARCHAR}),
            (select IFNULL(InsuredAmount,0) from fcorderitemdetail where OrderItemDetailNo in (select OrderItemDetailNo
            from fcorderitem where contno=#{contNo,jdbcType=VARCHAR})),
            (select IFNULL(InsuredAmount,0) from fcorderitemdetail where OrderItemDetailNo in (select OrderItemDetailNo
            from fcorderitem where contno=#{contNo,jdbcType=VARCHAR})),
            <if test="Prem != null">
                #{Prem,jdbcType=DOUBLE},
            </if>
            <if test="ReviewState != null">
                #{ReviewState,jdbcType=VARCHAR},
            </if>
            <if test="Approved != null">
                #{Approved,jdbcType=VARCHAR},
            </if>
            <if test="FailedCause != null">
                #{FailedCause,jdbcType=VARCHAR},
            </if>
            <if test="Operator != null">
                #{Operator,jdbcType=VARCHAR},
            </if>
            <if test="OperatorCom != null">
                #{OperatorCom,jdbcType=VARCHAR},
            </if>
            <if test="MakeDate != null">
                #{MakeDate,jdbcType=DATE},
            </if>
            <if test="MakeTime != null">
                #{MakeTime,jdbcType=VARCHAR},
            </if>
            <if test="ModifyDate != null">
                #{ModifyDate,jdbcType=DATE},
            </if>
            <if test="ModifyTime != null">
                #{ModifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="insertFcorderpay" parameterType="java.util.Map">
        insert into fcorderpay
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="PayNo != null">
                PayNo,
            </if>
            <if test="orderNo != null">
                OrderNo,
            </if>
            <if test="PayType != null">
                PayType,
            </if>
            <if test="TotalPrem != null">
                TotalPrem,
            </if>
            <if test="GrpPrem != null">
                GrpPrem,
            </if>
            <if test="PersonPrem != null">
                PersonPrem,
            </if>
            <if test="IsValid != null">
                IsValid,
            </if>
            <if test="BankCode != null">
                BankCode,
            </if>
            <if test="BankAccType != null">
                BankAccType,
            </if>
            <if test="BankAccNo != null">
                BankAccNo,
            </if>
            <if test="BankAccName != null">
                BankAccName,
            </if>
            <if test="bankAccPhone != null">
                bankAccPhone,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="PayNo != null">
                #{PayNo,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="PayType != null">
                #{PayType,jdbcType=VARCHAR},
            </if>
            <if test="TotalPrem != null">
                #{TotalPrem,jdbcType=DOUBLE},
            </if>
            <if test="GrpPrem != null">
                #{GrpPrem,jdbcType=DOUBLE},
            </if>
            <if test="PersonPrem != null">
                #{PersonPrem,jdbcType=DOUBLE},
            </if>
            <if test="IsValid != null">
                #{IsValid,jdbcType=VARCHAR},
            </if>
            <if test="BankCode != null">
                #{BankCode,jdbcType=VARCHAR},
            </if>
            <if test="BankAccType != null">
                #{BankAccType,jdbcType=VARCHAR},
            </if>
            <if test="BankAccNo != null">
                #{BankAccNo,jdbcType=VARCHAR},
            </if>
            <if test="BankAccName != null">
                #{BankAccName,jdbcType=VARCHAR},
            </if>
            <if test="bankAccPhone != null">
                #{bankAccPhone,jdbcType=DATE},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="insertFcorderFace" parameterType="java.util.Map">
        insert into FcorderFace
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="FaceNo != null">
                FaceNo,
            </if>
            <if test="orderNo != null">
                OrderNo,
            </if>
            <if test="personId != null">
                personId,
            </if>
            <if test="bizToken != null">
                bizToken,
            </if>
            <if test="requestId != null">
                requestId,
            </if>
            <if test="faceUrl != null">
                faceUrl,
            </if>
            <if test="IsValid != null">
                IsValid,
            </if>
            <if test="IsPass != null">
                IsPass,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="FaceNo != null">
                #{FaceNo,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="personId != null">
                #{personId,jdbcType=VARCHAR},
            </if>
            <if test="bizToken != null">
                #{bizToken,jdbcType=VARCHAR},
            </if>
            <if test="requestId != null">
                #{requestId,jdbcType=VARCHAR},
            </if>
            <if test="faceUrl != null">
                #{faceUrl,jdbcType=VARCHAR},
            </if>
            <if test="IsValid != null">
                #{IsValid,jdbcType=VARCHAR},
            </if>
            <if test="IsPass != null">
                #{IsPass,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateFcorderByConNo" parameterType="java.util.Map">
        update fcorder
        set orderStatus=#{orderStatus,jdbcType=VARCHAR},
            ModifyDate = #{modifyDate,jdbcType=DATE},
            ModifyTime = #{modifyTime,jdbcType=VARCHAR}
        where orderNo in (select orderNo from fcorderitem where contNo = #{contNo,jdbcType=VARCHAR})
    </update>
    <update id="updateFCOrderReview" parameterType="java.util.Map">
        update FCOrderReview
        set ReviewState=#{ReviewState,jdbcType=VARCHAR},
            ModifyDate = #{modifyDate,jdbcType=DATE},
            ModifyTime = #{modifyTime,jdbcType=VARCHAR}
        where OrderItemNo in (select OrderItemNo from fcorderitem where contNo = #{contNo,jdbcType=VARCHAR})
    </update>
    <update id="updateFcorderpay" parameterType="java.util.Map">
        update fcorderpay
        set IsValid='1',
            operator=#{operator,jdbcType=VARCHAR},
            ModifyDate = #{modifyDate,jdbcType=DATE},
            ModifyTime = #{modifyTime,jdbcType=VARCHAR}
        where orderNo = #{orderNo,jdbcType=VARCHAR}
    </update>
    <update id="updateFcorderpayIsValid" parameterType="java.util.Map">
        update fcorderpay
        <set>
            <if test="payType != null">
                PayType = #{payType,jdbcType=VARCHAR},
            </if>
            <if test="TotalPrem != null">
                TotalPrem = #{TotalPrem,jdbcType=VARCHAR},
            </if>
            <if test="GrpPrem != null">
                GrpPrem = #{GrpPrem,jdbcType=DATE},
            </if>
            <if test="PersonPrem != null">
                PersonPrem = #{PersonPrem,jdbcType=DATE},
            </if>
            <if test="IsValid != null">
                IsValid = #{IsValid,jdbcType=VARCHAR},
            </if>
            <if test="BankCode != null">
                BankCode = #{BankCode,jdbcType=VARCHAR},
            </if>
            <if test="BankAccType != null">
                BankAccType = #{BankAccType,jdbcType=VARCHAR},
            </if>
            <if test="BankAccNo != null">
                BankAccNo = #{BankAccNo,jdbcType=VARCHAR},
            </if>
            <if test="BankAccName != null">
                BankAccName = #{BankAccName,jdbcType=VARCHAR},
            </if>
            <if test="bankAccPhone != null">
                bankAccPhone = #{bankAccPhone,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where orderNo=#{orderNo,jdbcType=VARCHAR} and IsValid='0'
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCOrder">
        update fcorder
        <set>
            <if test="orderStatus != null">
                OrderStatus = #{orderStatus,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null">
                OrderType = #{orderType,jdbcType=VARCHAR},
            </if>
            <if test="openDay != null">
                OpenDay = #{openDay,jdbcType=DATE},
            </if>
            <if test="closeDay != null">
                CloseDay = #{closeDay,jdbcType=DATE},
            </if>
            <if test="grpOrderNo != null">
                GrpOrderNo = #{grpOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="grpNo != null">
                GrpNo = #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="perNo != null">
                PerNo = #{perNo,jdbcType=VARCHAR},
            </if>
            <if test="perAppNo != null">
                PerAppNo = #{perAppNo,jdbcType=VARCHAR},
            </if>
            <if test="costNo != null">
                CostNo = #{costNo,jdbcType=VARCHAR},
            </if>
            <if test="pubEleNo != null">
                PubEleNo = #{pubEleNo,jdbcType=VARCHAR},
            </if>
            <if test="commitDate != null">
                CommitDate = #{commitDate,jdbcType=DATE},
            </if>
            <if test="effectDate != null">
                EffectDate = #{effectDate,jdbcType=DATE},
            </if>
            <if test="isInfo != null">
                IsInfo = #{isInfo,jdbcType=VARCHAR},
            </if>
            <if test="isLock != null">
                IsLock = #{isLock,jdbcType=VARCHAR},
            </if>
            <if test="orderSource != null">
                OrderSource = #{orderSource,jdbcType=VARCHAR},
            </if>
            <if test="clientNo != null">
                ClientNo = #{clientNo,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where OrderNo = #{orderNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCOrder">
        update fcorder
        set OrderStatus = #{orderStatus,jdbcType=VARCHAR},
            OrderType   =
                #{orderType,jdbcType=VARCHAR},
            OpenDay     = #{openDay,jdbcType=DATE},
            CloseDay    = #{closeDay,jdbcType=DATE},
            GrpOrderNo  =
                #{grpOrderNo,jdbcType=VARCHAR},
            GrpNo       = #{grpNo,jdbcType=VARCHAR},
            PerNo       = #{perNo,jdbcType=VARCHAR},
            PerAppNo    =
                #{perAppNo,jdbcType=VARCHAR},
            CostNo      = #{costNo,jdbcType=VARCHAR},
            PubEleNo    = #{pubEleNo,jdbcType=VARCHAR},
            CommitDate  =
                #{commitDate,jdbcType=DATE},
            EffectDate  = #{effectDate,jdbcType=DATE},
            IsInfo      = #{isInfo,jdbcType=VARCHAR},
            OrderSource =
                #{orderSource,jdbcType=VARCHAR},
            ClientNo    =
                #{clientNo,jdbcType=VARCHAR},
            Operator    = #{operator,jdbcType=VARCHAR},
            OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            MakeDate    =
                #{makeDate,jdbcType=DATE},
            MakeTime    = #{makeTime,jdbcType=VARCHAR},
            ModifyDate  = #{modifyDate,jdbcType=DATE},
            ModifyTime  =
                #{modifyTime,jdbcType=VARCHAR}
        where OrderNo =
              #{orderNo,jdbcType=VARCHAR}
    </update>

    <!-- 投保清单查询 (企事业单位投保)-->
    <select id="listInsureDetail" parameterType="java.util.Map"
            resultType="java.util.Map">
        select a.name iName,a.planCode iPlanCode,a.planName iPlanName,a.totalPrem iTotal,a.planObject iPlanObject,
        a.orderNo,a.personID iPersonId,a.ensureCode,
        b.name sName,b.planCode sPlanCode,b.planName sPlanName,b.totalPrem sTotal,b.planObject sPlanObject,
        b.personID sPersonId
        from (select insured.Name as name, plan.PlanCode as planCode, plan.PlanName
        as planName,
        plan.TotalPrem as totalPrem, plan.PlanObject as
        planObject, fcorder.OrderNo as
        orderNo,insured.PersonID as
        personID,plan.EnsureCode as ensureCode
        from
        (select OrderNo,PerNo,GrpNo,GrpOrderNo
        from fcorder
        <where>
            <if test="grpNo != null and grpNo != ''">
                GrpNo=#{grpNo,jdbcType=VARCHAR}
            </if>
        </where>
        ) fcorder inner join fcgrporder grp on fcorder.GrpOrderNo=grp.GrpOrderNo and
        grp.ensureCode=#{ensureCode,jdbcType=VARCHAR}
        inner join fcperregistday perr on perr.perno = fcorder.perno and perr.ensureCode=grp.ensureCode and
        perr.LockState = '0'
        join
        (select PerNo,GrpNo from fcperinfo
        <where>
            <if test="department != null and department != ''">
                department = #{department,jdbcType=VARCHAR}
            </if>
            <if test="sex != null and sex !=''">
                and Sex = #{sex,jdbcType=VARCHAR}
            </if>
            <if test="insuredName != null and insuredName != ''">
                <!-- 根据被保人姓名模糊查询 -->
                <bind name="insuredNameLike" value=" '%' + insuredName + '%' "/>
                and Name like #{insuredNameLike,jdbcType=VARCHAR}
            </if>
        </where>
        ) info
        on
        fcorder.GrpNo = info.GrpNo and fcorder.PerNo = info.PerNo
        join
        fcorderitem item
        on
        fcorder.OrderNo = item.OrderNo
        join
        fcorderinsured
        insured
        on
        item.OrderNo = insured.OrderNo and insured.OrderItemNo =
        item.OrderItemNo
        join
        fcorderitemdetail detail
        on
        detail.OrderItemDetailNo = item.OrderItemDetailNo
        join
        fcensureplan plan
        on
        plan.PlanCode = detail.ProductCode and plan.ensureCode=#{ensureCode,jdbcType=VARCHAR}
        <where>
            detail.ProductEleCode = '001' and detail.Value =
            plan.PlanObject and plan.PlanObject='1'
            <if test="planName != null and planName != ''">
                <!-- 根据计划名称模糊查询 -->
                <bind name="planNameLike" value=" '%' + planName + '%' "/>
                and plan.PlanName like #{planNameLike,jdbcType=VARCHAR}
            </if>
        </where>
        ) a left join (select insured.Name as name, plan.PlanCode as planCode, plan.PlanName
        as planName,
        plan.TotalPrem as totalPrem, plan.PlanObject as
        planObject, fcorder.OrderNo as
        orderNo,insured.PersonID as
        personID,plan.EnsureCode as ensureCode
        from
        (select OrderNo,PerNo,GrpNo,GrpOrderNo
        from fcorder
        <where>
            <if test="grpNo != null and grpNo != ''">
                GrpNo=#{grpNo,jdbcType=VARCHAR}
            </if>
        </where>
        ) fcorder inner join fcgrporder grp on fcorder.GrpOrderNo=grp.GrpOrderNo and
        grp.ensureCode=#{ensureCode,jdbcType=VARCHAR}
        inner join fcperregistday perr on perr.perno = fcorder.perno and perr.ensureCode=grp.ensureCode and
        perr.LockState = '0'
        join
        (select PerNo,GrpNo from fcperinfo
        <where>
            <if test="department != null and department != ''">
                department = #{department,jdbcType=VARCHAR}
            </if>
            <if test="sex != null and sex !=''">
                and Sex = #{sex,jdbcType=VARCHAR}
            </if>
            <if test="insuredName != null and insuredName != ''">
                <!-- 根据被保人姓名模糊查询 -->
                <bind name="insuredNameLike" value=" '%' + insuredName + '%' "/>
                and Name like #{insuredNameLike,jdbcType=VARCHAR}
            </if>
        </where>
        ) info
        on
        fcorder.GrpNo = info.GrpNo and fcorder.PerNo = info.PerNo
        join
        fcorderitem item
        on
        fcorder.OrderNo = item.OrderNo
        join
        fcorderinsured
        insured
        on
        item.OrderNo = insured.OrderNo and insured.OrderItemNo =
        item.OrderItemNo
        join
        fcorderitemdetail detail
        on
        detail.OrderItemDetailNo = item.OrderItemDetailNo
        join
        fcensureplan plan
        on
        plan.PlanCode = detail.ProductCode and plan.ensureCode=#{ensureCode,jdbcType=VARCHAR}
        <where>
            detail.ProductEleCode = '001'
            and detail.Value=plan.PlanObject
            and plan.PlanObject='2'
            <if test="planName != null and planName != ''">
                <!-- 根据计划名称模糊查询 -->
                <bind name="planNameLike" value=" '%' + planName + '%' "/>
                and plan.PlanName like #{planNameLike,jdbcType=VARCHAR}
            </if>
        </where>
        ) b on a.orderNo=b.orderNo and a.ensureCode=b.ensureCode
        where 1=1
        <if test=' planObject == "1" '>
            group by a.name
        </if>
        <if test=' planObject == "2" '>
            group by b.name
        </if>
        order by a.orderNo
    </select>
    <!-- 投保清单查询(学生投保)-->
    <select id="listStuInsureDetail" parameterType="java.util.Map"
            resultType="java.util.Map">
        select c.name as studentName,g.name as guarderName,a.planName,a.totalPrem,c.personId,a.planCode
        from fcensureplan a,
        fcorderitemdetail b,
        fcorderinsured c,
        fcorder d,
        fcorderitem e,
        fcgrporder f,
        fcperinfo g
        where f.grporderno=d.grporderno
        and d.orderno=e.orderno
        and c.orderno=d.orderno
        and c.orderitemno=e.orderitemno
        and e.OrderItemDetailNo=b.OrderItemDetailNo
        and b.value=a.PlanObject
        and a.PlanCode = b.ProductCode
        and a.ensurecode=f.ensurecode
        and d.perno=g.perno
        <if test="grpNo != null and grpNo != ''">
            and f.grpno=#{grpNo,jdbcType=VARCHAR}
        </if>
        and a.ensurecode=#{ensureCode,jdbcType=VARCHAR}
        and b.ProductEleCode = '001'
        and a.PlanObject='3'
        <if test="studentName != null and studentName != ''">
            <!-- 根据学生姓名模糊查询 -->
            <bind name="studentNameLike" value=" '%' + studentName + '%' "/>
            and c.Name like #{studentNameLike,jdbcType=VARCHAR}
        </if>
        <if test="guarderName != null and guarderName != ''">
            <!-- 根据监护人姓名模糊查询 -->
            <bind name="guarderNameLike" value=" '%' + guarderName + '%' "/>
            and g.name like #{guarderNameLike,jdbcType=VARCHAR}
        </if>
        <if test="planName != null and planName != ''">
            <!-- 根据计划名称模糊查询 -->
            <bind name="planNameLike" value=" '%' + planName + '%' "/>
            and a.planName like #{planNameLike,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectList" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcorder
        where 1=1
        <if test="grpOrderNo != null">
            and GrpOrderNo = #{grpOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="perNo != null">
            and PerNo = #{perNo}
        </if>
    </select>

    <select id="selectOrderListByGrpOrderNo" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcorder
        where GrpOrderNo = #{grpOrderNo,jdbcType=VARCHAR}
    </select>


    <!-- 导出投保清单excel -->
    <select id="selectInsuredDetail" parameterType="java.util.Map"
            resultMap="InsuredDetails">
        select case when ('${ensureType}' != '') then '${ensureType}' else '' end as ensureType,
               case when ('${garName}' != '') then '${garName}' else '' end       as garName,
               case when ('${garSex}' != '') then '${garSex}' else '' end         as garSex,
               case when ('${department}' != '') then '${department}' else '' end as department,
               case when ('${stuName}' != '') then '${stuName}' else '' end       as stuName,
               case when ('${planName}' != '') then '${planName}' else '' end     as planName,
               case WHEN ('${planObject}' != '') then '${planObject}' else '' end as planObject,
               case WHEN ('${ensureCode}' != '') then '${ensureCode}' else '' end as ensureCode,
               OrderNo,
               a.GrpOrderNo,
               a.GrpNo,
               a.PerNo,
               PerAppNo
        from fcorder a
                 inner join fcGrpOrder c on c.GrpOrderNo = #{grpOrderNo,jdbcType=VARCHAR}
                 inner join fcperregistday d on a.perno = d.perno and d.ensureCode = c.ensureCode
        where a.GrpOrderNo = #{grpOrderNo,jdbcType=VARCHAR}
          and a.GrpNo = #{grpNo,jdbcType=VARCHAR}
          and d.LockState = '0'
    </select>

    <select id="selectPerNo" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCOrder">
        select
        <include refid="Base_Column_List"/>
        from fcorder
        where PerNo = #{perNo,jdbcType=VARCHAR} and OrderStatus = #{orderStatus,jdbcType=VARCHAR}
        and GrpOrderNo=(select fg.GrpOrderNo from fcgrporder fg where fg.EnsureCode = #{ensureCode,jdbcType=VARCHAR})
    </select>

    <select id="selectOrder" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FCOrder">
        select fc.OrderNo,
               fc.OrderStatus
        from fcorder fc
        where fc.PerNo = #{perNo,jdbcType=VARCHAR}
          and fc.GrpOrderNo = (
            select fg.GrpOrderNo
            from fcgrporder fg
            where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        )
    </select>

    <select id="selectOrderList" parameterType="java.util.Map" resultType="java.lang.String">
        select fc.OrderNo
        from fcorder fc
        where fc.PerNo = #{perNo,jdbcType=VARCHAR}
          and fc.GrpOrderNo = (
            select fg.GrpOrderNo
            from fcgrporder fg
            where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        )
    </select>


    <select id="selectOrderByperNo" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FCOrder">
        select
        <include refid="Base_Column_List"/>
        from fcorder
        where
        GrpOrderNo = #{grpOrderNo,jdbcType=VARCHAR}
        and PerNo = #{perNo,jdbcType=VARCHAR}
    </select>

    <select id="selectByEnsureCode" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT OrderStatus
        FROM fcorder
        WHERE GrpOrderNo IN (SELECT GrpOrderNo FROM fcgrporder WHERE ensureCode = #{ensureCode,jdbcType=VARCHAR})
    </select>
    <select id="getSelfOrderInfo" parameterType="java.lang.String" resultType="java.util.Map">
        select '横琴人寿保险有限公司'                                BusinessName,
               '年度弹性福利产品'                                  ProductName,
               CONCAT(a.makedate, CONCAT(' ', a.maketime)) CommitDate,
               a.OrderNo,
               FORMAT(sum(b.SelfPrem), 2)                  SelfPrem,
               '人民币'                                       CurrencyType
        from fcorder a,
             fcorderitem b
        where a.orderno = b.orderno
          and a.perno = #{perNo,jdbcType=VARCHAR}
          and a.grporderno in (select grporderno from fcgrporder where ensurecode = #{ensureCode,jdbcType=VARCHAR})
    </select>
    <select id="selfEmpOrderInfo" parameterType="java.lang.String" resultType="java.util.Map">
        select c.Name,
               (b.SelfPrem + b.GrpPrem) TotlePrem,
               b.GrpPrem,
               b.SelfPrem,
               c.PersonId
        from fcorder a,
             fcorderitem b,
             fcorderinsured c
        where a.orderno = b.orderno
          and b.orderno = c.orderno
          and b.orderitemno = c.orderitemno
          and c.personid in
              (select personid from fcstafffamilyrela where relation = '0' and perno = #{perNo,jdbcType=VARCHAR})
          and a.perno = #{perNo,jdbcType=VARCHAR}
          and a.grporderno in (select grporderno from fcgrporder where ensurecode = #{ensureCode,jdbcType=VARCHAR})
    </select>
    <select id="selfFamilyOrderInfo" parameterType="java.lang.String" resultType="java.util.Map">
        select c.Name,
               (b.SelfPrem + b.GrpPrem) TotlePrem,
               b.GrpPrem,
               b.SelfPrem,
               c.PersonId
        from fcorder a,
             fcorderitem b,
             fcorderinsured c
        where a.orderno = b.orderno
          and b.orderno = c.orderno
          and b.orderitemno = c.orderitemno
          and c.personid in (select personid from fcstafffamilyrela where relation!='0'
          and perno=#{perNo,jdbcType=VARCHAR})
          and a.perno=#{perNo,jdbcType=VARCHAR}
          and a.grporderno in (
        select grporderno
        from fcgrporder
        where ensurecode=#{ensureCode,jdbcType=VARCHAR})
    </select>
    <select id="selfOrderDetailDesInfo" parameterType="java.lang.String" resultType="java.util.Map">
        select t.RiskName,
               t.DutyName,
               t.Amnt,
               t.prem
        from (select CONCAT(CASE
                                WHEN b.RiskCode = '15070' then
                                    CASE
                                        WHEN b.RiskType = '01' then CONCAT(c.RiskName, '(民航班机)')
                                        WHEN b.RiskType = '02' then CONCAT(c.RiskName, '(轨道交通工具)')
                                        WHEN b.RiskType = '03' then CONCAT(c.RiskName, '(水运公共交通工具)')
                                        WHEN b.RiskType = '04' then CONCAT(c.RiskName, '(公路公共交通工具)')
                                        else CONCAT(c.RiskName, '(私家车)') end
                                else c.RiskName end,
                            CASE
                                WHEN (a.Deductible is not null and a.CompensationRatio is not null) then CONCAT('(',
                                                                                                                '免赔额',
                                                                                                                ROUND(a.Deductible, 2),
                                                                                                                ',赔付比例',
                                                                                                                ROUND(a.CompensationRatio, 2),
                                                                                                                ')')
                                WHEN (a.Deductible is null and a.CompensationRatio is not null)
                                    then CONCAT('(', '赔付比例', ROUND(a.CompensationRatio, 2), ')')
                                WHEN (a.Deductible is not null and a.CompensationRatio is null)
                                    then CONCAT('(', '免赔额', ROUND(a.Deductible, 2), ')')
                                else '' end) RiskName,
                     d.DutyRange             DutyName,
                     b.Amnt,
                     a.prem
              from FPInsureEflexPlan a,
                   FcDutyAmountGrade b,
                   fdriskinfo c,
                   fdriskdutyinfo d
              where a.AmountGrageCode = b.AmountGrageCode
                and b.RiskCode = c.RiskCode
                and c.RiskCode = d.RiskCode
                and b.DutyCode = d.DutyCode
                and a.EnsureCode = #{ensureCode,jdbcType=VARCHAR}
                and a.PerNo = #{perNo,jdbcType=VARCHAR}
                and a.PersonId = #{personId,jdbcType=VARCHAR}

              union all

              select CONCAT(CASE
                                WHEN b.RiskCode = '15070' then
                                    CASE
                                        WHEN b.RiskType = '01' then CONCAT(c.RiskName, '(民航班机)')
                                        WHEN b.RiskType = '02' then CONCAT(c.RiskName, '(轨道交通工具)')
                                        WHEN b.RiskType = '03' then CONCAT(c.RiskName, '(水运公共交通工具)')
                                        WHEN b.RiskType = '04' then CONCAT(c.RiskName, '(公路公共交通工具)')
                                        else CONCAT(c.RiskName, '(私家车)') end
                                else c.RiskName end,
                            CASE
                                WHEN (a.Deductible is not null and a.CompensationRatio is not null)
                                    then CONCAT('(', '免赔额', a.Deductible, ',赔付比例', a.CompensationRatio, ')')
                                WHEN (a.Deductible is null and a.CompensationRatio is not null)
                                    then CONCAT('(', '赔付比例', a.CompensationRatio, ')')
                                WHEN (a.Deductible is not null and a.CompensationRatio is null)
                                    then CONCAT('(', '免赔额', a.Deductible, ')')
                                else '' end) RiskName,
                     d.DutyRange             DutyName,
                     f.Amnt,
                     e.prem
              from FPInsureEflexPlan a,
                   FPInsureEflexPlanOptional e,
                   FcDutyAmountGrade b,
                   FcDutyGradeOptionalAmountInfo f,
                   fdriskinfo c,
                   fdriskdutyinfo d
              where a.InsureElfexPlanNo = e.InsureElfexPlanNo
                and e.AmountGrageCode = b.AmountGrageCode
                and b.AmountGrageCode = f.AmountGrageCode
                and b.RiskCode = c.RiskCode
                and c.RiskCode = d.RiskCode
                and f.OptDutyCode = d.DutyCode
                and e.OptDutyCode = d.DutyCode
                and a.EnsureCode = #{ensureCode,jdbcType=VARCHAR}
                and a.PerNo = #{perNo,jdbcType=VARCHAR}
                and a.PersonId = #{personId,jdbcType=VARCHAR}) t
        order by t.RiskName
    </select>
    <select id="selfOrderDetailDesInfo1" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.confirmInsureEflex.InsureEflexOrderDetailInfo">
        select t.amountGrageCode,
               t.RiskCode,
               t.riskType,
               t.RiskName,
               t.dutyCode,
               t.DutyName,
               t.Amnt amount,
               t.prem
        from (select c.riskCode,
                     c.riskType,
                     CONCAT(CASE
                                WHEN b.RiskCode = '15070' then
                                    CASE
                                        WHEN b.RiskType = '01' then CONCAT(c.RiskName, '(民航班机)')
                                        WHEN b.RiskType = '02' then CONCAT(c.RiskName, '(轨道交通工具)')
                                        WHEN b.RiskType = '03' then CONCAT(c.RiskName, '(水运公共交通工具)')
                                        WHEN b.RiskType = '04' then CONCAT(c.RiskName, '(公路公共交通工具)')
                                        else CONCAT(c.RiskName, '(私家车)') end
                                else c.RiskName end,
                            CASE
                                WHEN (a.Deductible is not null and a.CompensationRatio is not null) then CONCAT('(',
                                                                                                                '免赔额',
                                                                                                                ROUND(a.Deductible, 2),
                                                                                                                ',赔付比例',
                                                                                                                ROUND(a.CompensationRatio, 2),
                                                                                                                ')')
                                WHEN (a.Deductible is null and a.CompensationRatio is not null)
                                    then CONCAT('(', '赔付比例', ROUND(a.CompensationRatio, 2), ')')
                                WHEN (a.Deductible is not null and a.CompensationRatio is null)
                                    then CONCAT('(', '免赔额', ROUND(a.Deductible, 2), ')')
                                else '' end) RiskName,
                     d.dutyCode,
                     d.DutyRange             DutyName,
                     b.Amnt,
                     a.prem,
                     b.amountGrageCode
              from FPInsureEflexPlan a,
                   FcDutyAmountGrade b,
                   fdriskinfo c,
                   fdriskdutyinfo d
              where a.AmountGrageCode = b.AmountGrageCode
                and b.RiskCode = c.RiskCode
                and c.RiskCode = d.RiskCode
                and b.DutyCode = d.DutyCode
                and a.EnsureCode = #{ensureCode}
                and a.PerNo = #{perNo}
                and a.PersonId = #{personId}

              union all

              select c.riskCode,
                     c.riskType,
                     CONCAT(CASE
                                WHEN b.RiskCode = '15070' then
                                    CASE
                                        WHEN b.RiskType = '01' then CONCAT(c.RiskName, '(民航班机)')
                                        WHEN b.RiskType = '02' then CONCAT(c.RiskName, '(轨道交通工具)')
                                        WHEN b.RiskType = '03' then CONCAT(c.RiskName, '(水运公共交通工具)')
                                        WHEN b.RiskType = '04' then CONCAT(c.RiskName, '(公路公共交通工具)')
                                        else CONCAT(c.RiskName, '(私家车)') end
                                else c.RiskName end,
                            CASE
                                WHEN (a.Deductible is not null and a.CompensationRatio is not null)
                                    then CONCAT('(', '免赔额', a.Deductible, ',赔付比例', a.CompensationRatio, ')')
                                WHEN (a.Deductible is null and a.CompensationRatio is not null)
                                    then CONCAT('(', '赔付比例', a.CompensationRatio, ')')
                                WHEN (a.Deductible is not null and a.CompensationRatio is null)
                                    then CONCAT('(', '免赔额', a.Deductible, ')')
                                else '' end) RiskName,
                     d.dutyCode,
                     d.DutyRange             DutyName,
                     f.Amnt,
                     e.prem,
                     b.amountGrageCode
              from FPInsureEflexPlan a,
                   FcDutyAmountGrade b,
                   FcDutyGradeOptionalAmountInfo f,
                   fdriskinfo c,
                   fdriskdutyinfo d,
                   FPInsureEflexPlanOptional e
              where a.InsureElfexPlanNo = e.InsureElfexPlanNo
                and e.AmountGrageCode = b.AmountGrageCode
                and b.AmountGrageCode = f.AmountGrageCode
                and b.RiskCode = c.RiskCode
                and c.RiskCode = d.RiskCode
                and f.OptDutyCode = d.DutyCode
                and e.OptDutyCode = d.DutyCode
                and a.EnsureCode = #{ensureCode}
                and a.PerNo = #{perNo}
                and a.PersonId = #{personId}) t
        order by t.RiskName
    </select>
    <update id="updateFcorder" parameterType="java.lang.String">
        update fcorder
        set orderstatus=#{orderStatus,jdbcType=VARCHAR},
            modifyDate=#{modifyDate,jdbcType=DATE},
            modifyTime=#{modifyTime,jdbcType=VARCHAR}
        where orderNo = #{orderNo,jdbcType=VARCHAR}
    </update>

    <select id="getOrderListByPerNo" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FCOrder">
        select * FROM fcorder a,fcgrporder b,fcensure c,fcperregistday d
        WHERE a.perno IN
        (SELECT perNo FROM fcstafffamilyrela WHERE
        PerNo IN (SELECT PerNo FROM fcperinfo WHERE IDNo = (SELECT IDNo FROM fcperinfo WHERE PerNo = #{perNo})) GROUP BY
        PerNo)
        and d.`EnsureCode` = c.`EnsureCode`AND d.`PerNo` = a.`PerNo` AND d.`LockState` = '0'
        AND OrderStatus NOT IN (09)
        AND a.grporderno=b.grporderno
        AND b.grpNo=c.grpNo
        AND b.ensurecode=c.ensurecode
        AND c.PlanType in ('0','1')
        <if test=" commitDate1 != null and commitDate1 != '' ">
            <![CDATA[AND DATE_FORMAT(CommitDate, '%Y-%m-%d') >=  DATE_FORMAT(#{commitDate1}, '%Y-%m-%d')]]>
        </if>
        <if test="commitDate2 != null and commitDate2 != ''">
            <![CDATA[AND DATE_FORMAT(CommitDate, '%Y-%m-%d') <=  DATE_FORMAT(#{commitDate2}, '%Y-%m-%d')]]>
        </if>
        order by a.OrderNo desc
    </select>
    <select id="getOrderListByPerNoStaff" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT a.* ,b.GrpContNo,b.EnsureCode,b.GrpNo
        FROM fcorder a,fcgrpOrder b,fcensure fe,fcperregistday d
        WHERE a.GrpOrderNo=b.GrpOrderNo
        AND b.ensurecode=fe.ensurecode
        and d.`EnsureCode` = fe.`EnsureCode`
        AND d.`PerNo` = a.`PerNo`
        AND a.perno IN
        (SELECT c.perNo FROM fcstafffamilyrela c WHERE c.PerNo IN
        (SELECT d.PerNo FROM fcperinfo d WHERE d.IDNo =#{iDNo}
        ) GROUP BY c.PerNo)
        AND IF(fe.plantype='2',a.OrderStatus IN
        ('01','03','04','05','06','09','010','08','011','012','013','015','016'),a.OrderStatus NOT IN ('09'))
        AND fe.EnsureState!='019'
        AND fe.PolicyEndDate > NOW()
        AND
        (case when fe.plantype in ('0','1') then d.`LockState` = '0'
        when fe.plantype='2' then d.`LockState` ='0' end)

        <if test=" commitDate1 != null and commitDate1 != '' ">
            <![CDATA[AND DATE_FORMAT(a.CommitDate, '%Y-%m-%d') >=  DATE_FORMAT(#{commitDate1}, '%Y-%m-%d')]]>
        </if>
        <if test="commitDate2 != null and commitDate2 != ''">
            <![CDATA[AND DATE_FORMAT(a.CommitDate, '%Y-%m-%d') <=  DATE_FORMAT(#{commitDate2}, '%Y-%m-%d')]]>
        </if>
        order by a.OrderNo desc
    </select>
    <select id="getOrderInfoByOrderNo" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT c.ensureCode                          AS ensureCode,
               c.ensureName                          AS ensureName,
               c.ensureType                          AS ensureType,
               c.planType                            AS planType,
               d.grpname                             AS grpName,
               a.OrderNo                             AS orderNo,
               DATE_FORMAT(a.CommitDate, '%Y-%m-%d') AS commitDate,
               a.OrderStatus                         AS orderStatus,
               (SELECT CodeName
                FROM fdcode
                WHERE CodeType = 'EflexOrderStatus'
                  AND CodeKey = a.OrderStatus)       as orderStatusName,
               a.OrderStatus                         AS orderStatus,
               a.OrderSource                         AS orderSource,
               e.ConfigValue                         as payType,
               DATE_FORMAT(a.CloseDay, '%Y-%m-%d')   as closeDaynew,
               a.CloseDay                            as closeDay
        FROM fcorder a
                 LEFT JOIN fcgrporder b ON a.GrpOrderNo = b.GrpOrderNo
                 LEFT JOIN fcensure c ON b.ensureCode = c.ensureCode
                 LEFT JOIN fcensureConfig e ON e.ensureCode = c.ensureCode AND e.ConfigNo = '008'
                 LEFT JOIN fcgrpinfo d ON b.grpno = d.grpno
        WHERE a.OrderNo = #{orderNo}
          AND a.OrderStatus NOT IN ('09')
    </select>
    <select id="getInsureInfo" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT b.ensureCode  AS ensureCode,
               a.PerNo       AS perNo,
               c.OrderItemNo as orderItemNo
        FROM fcorder a,
             fcgrporder b,
             fcorderitem c
        WHERE a.GrpOrderNo = b.GrpOrderNo
          and a.OrderNo = c.OrderNo
          and a.OrderNo = #{orderNo,jdbcType=VARCHAR}
    </select>
    <select id="getOrderInfoByOrderNoDaily" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT c.ensureCode                          AS ensureCode,
               c.ensureType                          AS ensureType,
               c.planType                            AS planType,
               d.grpname                             AS grpName,
               a.OrderNo                             AS orderNo,
               a.GrpNo                               AS grpNo,
               DATE_FORMAT(a.CommitDate, '%Y-%m-%d') AS commitDate,
               case
                   when c.plantype = '2' then
                       (SELECT CodeName FROM fdcode WHERE CodeType = 'OrderStatus' AND CodeKey = a.OrderStatus)
                   else (SELECT CodeName FROM fdcode WHERE CodeType = 'EflexOrderStatus' AND CodeKey = a.OrderStatus)
                   end                               as orderStatusName,
               a.OrderStatus                         AS orderStatus,
               a.OrderSource                         AS orderSource,
               e.ConfigValue                         as payType,
               DATE_FORMAT(a.CloseDay, '%Y-%m-%d')   as closeDay
        FROM fcorder a
                 LEFT JOIN fcgrporder b ON a.GrpOrderNo = b.GrpOrderNo
                 LEFT JOIN fcensure c ON b.ensureCode = c.ensureCode
                 LEFT JOIN fcensureConfig e ON e.ensureCode = c.ensureCode AND e.ConfigNo = '008'
                 LEFT JOIN fcgrpinfo d ON b.grpno = d.grpno
        WHERE a.OrderNo = #{orderNo}
          AND IF(c.plantype = '2', a.OrderStatus NOT IN ('02'), a.OrderStatus NOT IN ('09'))
    </select>

    <select id="getOrderListByEnsureCode" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT
        c.grpName AS grpName,
        a.orderNo AS orderNo,
        d.PerNo AS perNo,
        d.Name AS `name`,
        d.IDType AS iDType,
        (SELECT CodeName FROM fdcode WHERE CodeType = 'IDType' AND CodeKey = d.IDType) as iDTypeName,
        d.IDNo AS iDNo,
        d.department AS departMent,
        g.Name AS openPer,
        g.PayBankCode AS openBank,
        (SELECT CodeName FROM fdcode WHERE CodeType = 'Bank' AND CodeKey = g.PayBankCode) as openBankName,
        g.BankAccount AS openAccount,
        e.ConfigValue AS payType,
        a.OrderStatus as orderStatus,
        a.OrderSource as orderSource,
        f.LockState as lockState
        FROM fcorder a
        LEFT JOIN fcgrporder b ON a.GrpOrderNo = b.GrpOrderNo
        LEFT JOIN fcgrpinfo c ON b.grpNo = c.grpNo
        LEFT JOIN fcperinfo d ON a.perNo = d.perNo
        LEFT JOIN fcensureConfig e ON e.ensureCode = b.ensureCode AND e.ConfigNo = '008'
        LEFT JOIN fcperregistday f ON f.ensureCode = b.ensureCode AND f.perNo = a.perNo
        LEFT JOIN FcBatchPayBankInfo g ON g.ensureCode = b.ensureCode AND g.perNo = a.perNo AND g.IsSidned = 'Y'
        WHERE b.EnsureCode = #{ensureCode}
        <if test="orderStatus != null and orderStatus != ''">
            and a.OrderStatus = #{orderStatus}
        </if>
        <if test="name != null and name != ''">
            and d.Name like CONCAT('%',#{name},'%')
        </if>
    </select>

    <select id="getOrderStatusByEnsureCode" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT GrpOrderNo as grpOrderNo,OrderNo as orderNo,OrderStatus as orderStatus,PerNo as perNo FROM fcorder WHERE
        GrpOrderNo IN (SELECT GrpOrderNo FROM fcgrporder WHERE EnsureCode = #{ensureCode})
        <if test="perNo != null and perNo != ''">
            AND PerNo = #{perNo}
        </if>
    </select>

    <select id="getDeleteOrderInfo" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT a.ensureCode,
               b.perNo,
               d.personId
        FROM fcgrporder a,
             fcorder b,
             fcorderitem c,
             fcorderinsured d
        WHERE a.GrpOrderNo = b.GrpOrderNo
          AND b.OrderNo = c.OrderNo
          AND c.OrderNo = d.OrderNo
          AND c.OrderItemNo = d.OrderItemNo
          AND b.OrderNo = #{orderNo}
    </select>
    <select id="selectByGrpNoAndPerNo" resultType="java.lang.String">
        select OrderNo
        from fcorder
        where GrpNo = #{grpNo}
          and PerNo = #{perNo}
    </select>
    <select id="selectOtherGrpNoOrderNosByIdNo" resultType="java.lang.String">
        select OrderNo
        from fcorder
        where GrpNo != #{grpNo}
          and PerNo in (select PerNo from fcperinfo where IDNo = #{idNo})
    </select>
    <select id="selectOrderInfoByEnsureCode" resultMap="BaseResultMap">
        select f1.*
        from fcorder f1
                 inner join fcgrporder f2 on f1.GrpOrderNo = f2.GrpOrderNo
        where f2.EnsureCode = #{ensureCode}
    </select>
    <update id="updateCloseDayByEnsureCode" parameterType="java.lang.String">
        update fcorder
        set CloseDay = #{closeDay,jdbcType=DATE}
        where GrpOrderNo = (select GrpOrderNo from fcgrporder where ensurecode = #{ensureCode})
    </update>
    <update id="updateFcorderFace" parameterType="java.util.Map">
        update FcorderFace
        set IsValid    = '2',
            operator=#{operator,jdbcType=VARCHAR},
            ModifyDate = #{modifyDate,jdbcType=DATE},
            ModifyTime = #{modifyTime,jdbcType=VARCHAR}
        where orderNo = #{orderNo}
          and personId = #{personId} /*and IsValid='0'*/
    </update>
    <update id="updateFcorderFaceByQuery" parameterType="java.util.Map">
        update FcorderFace set
        IsValid = #{IsValid,jdbcType=VARCHAR},
        IsPass = #{IsPass,jdbcType=VARCHAR},
        <if test="bestFrame != null">
            bestFrame=#{bestFrame},
        </if>
        operator=#{operator,jdbcType=VARCHAR},
        ModifyDate = #{modifyDate,jdbcType=DATE},
        ModifyTime = #{modifyTime,jdbcType=VARCHAR}
        where orderNo = #{orderNo} and personId = #{personId} and IsValid='0' and IsPass='0';
    </update>
    <update id="updateByGrpOrderNo">
        update fcorder
        set OrderStatus='017'
        where GrpOrderNo = #{grpOrderNo}
    </update>
    <select id="selectAllByGrpNoAndPerNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcorder where GrpNo = #{grpNo} and PerNo = #{perNo}
    </select>
    <select id="selectAllByGrpNoAndPerNoandCode" resultType="java.util.Map">
        select a.*/*,b.Name*/
        from fcorder a
                 inner join fcperinfo b on a.perNo = b.perNo
        where a.GrpNo = #{grpNo}
          and a.PerNo = #{perNo}
          and a.GrpOrderNo = (select c.GrpOrderNo from fcgrporder c where c.ensurecode = #{ensureCode})
          and a.OrderStatus in ('03', '04', '015')
    </select>
    <select id="getBizTokenByPerInfo" parameterType="java.lang.String" resultType="java.lang.String">
        select bizToken
        from fcorderface
        where orderNo = #{orderNo}
          and personId = #{personId}
          and IsValid = '0'
    </select>
    <select id="getFaceInfoByPerInfo" parameterType="java.lang.String" resultType="java.util.Map">
        select isPass,
               IFNULL(bestFrame, '') bestFrame
        from fcorderface
        where orderNo = #{orderNo}
          and personId = #{personId}
          and IsValid = '1'
    </select>
    <select id="selectOrderByTimePerNoGrpNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcorder
        where 1=1
        <if test=" commitDateStart != null and commitDateStart != '' ">
            <![CDATA[AND DATE_FORMAT(CommitDate, '%Y-%m-%d') >=  DATE_FORMAT(#{commitDateStart}, '%Y-%m-%d')]]>
        </if>
        <if test="commitDateEnd != null and commitDateEnd != ''">
            <![CDATA[AND DATE_FORMAT(CommitDate, '%Y-%m-%d') <=  DATE_FORMAT(#{commitDateEnd}, '%Y-%m-%d')]]>
        </if>
        <if test="grpNo != null and grpNo != ''">
            and a.GrpNo = #{grpNo}
        </if>
        <if test="perNo != null and perNo != ''">
            and a.PerNo = #{perNo}
        </if>
    </select>
    <select id="getReview" parameterType="java.lang.String" resultType="java.util.Map">
        select a.Approved                                                                         as approved,
               a.FailedCause                                                                      as failedCause,
               (SELECT CodeName FROM fdcode WHERE CodeType = 'Approved' AND CodeKey = a.Approved) as approvedName
        from fcorderreview a
        where OrderItemNo = #{orderItemNo}
          and ReviewState = '1'
    </select>
    <select id="selectorderPay" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCOrderPay">
        select a.PayNo        as payNo,
               a.OrderNo      as orderNo,
               a.PayType      as payType,
               a.TotalPrem    as totalPrem,
               a.GrpPrem      as grpPrem,
               a.PersonPrem   as personPrem,
               a.IsValid      as isValid,
               a.BankCode     as bankCode,
               a.BankAccType  as bankAccType,
               a.BankAccNo    as bankAccNo,
               a.BankAccName  as bankAccName,
               a.bankAccPhone as bankAccPhone
        from fcorderpay a
                 inner join fcorderitem b on a.OrderNo = b.OrderNo
        where b.OrderItemNo = #{orderItemNo}
          and a.IsValid = '0'
        order by a.MakeDate, a.MakeTime DESC limit 1
    </select>
    <select id="getInsuranceSignatureByOrderNo" parameterType="java.lang.String" resultType="java.lang.String">
        select CONCAT('Split', a.EnsureCode, 'Split', b.PerNo, 'Split', d.PersonID, 'Split', c.OrderItemNo, 'Split')
        from fcgrporder a,
             fcorder b,
             fcorderitem c,
             fcorderinsured d
        where a.GrpOrderNo = b.GrpOrderNo
          and b.OrderNo = c.OrderNo
          and b.OrderNo = d.OrderNo
          and c.OrderItemNo = d.OrderItemNo
          and b.OrderNo = #{orderNo}
    </select>
    <select id="selectLoginOrderCountByEnsureCode" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT count(*)
        FROM fcorder
        WHERE GrpOrderNo IN (SELECT GrpOrderNo FROM fcgrporder WHERE ensureCode = #{ensureCode})
          and OrderSource in ('01', '02')
    </select>
    <select id="selectOrderCountByEnsureCode" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT count(*)
        FROM fcorder
        WHERE GrpOrderNo IN (SELECT GrpOrderNo FROM fcgrporder WHERE ensureCode = #{ensureCode})
    </select>
    <select id="selectGrpOrderInfo" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.confirmInsure.GrpOrderInfo">
        select b.grpNo, b.EnsureCode, a.perNo
        from fcorder a
                 left join fcgrporder b on a.grpOrderNo = b.grpOrderNo
        where a.OrderNo = #{orderNo};
    </select>
    <select id="queryGrpOrder" resultType="com.sinosoft.eflex.model.FCOrder">
        select *
        from fcorder
        where GrpOrderNo = (
            select GrpOrderNo
            from fcorder
            where OrderNo = (
                select fcorderitem.OrderNo from fcorderitem where OrderItemNo = #{orderItemNo}
            )
        )
    </select>
    <select id="selectOrderByPerNoAndEnsureCode" resultType="com.sinosoft.eflex.model.FCOrder">
        select fc.*
        from fcorder fc
        where fc.PerNo = #{perNo}
          and fc.GrpOrderNo = (
            select fg.GrpOrderNo
            from fcgrporder fg
            where EnsureCode = #{ensureCode}
        )
    </select>
    <delete id="deleteByGrpOrderAndPerNo">
        delete
        from fcorder
        where GrpOrderNo = #{grpNo}
          and PerNo = #{perNo}
    </delete>

    <select id="selectGrpOrderAndPerNo" resultType="com.sinosoft.eflex.model.FCOrder">
        select *
        from fcorder
        where GrpOrderNo = #{grpOrderNo}
          and PerNo = #{perNo}
    </select>

    <select id="selectByOrder" resultType="com.sinosoft.eflex.model.FCOrder">
        select
        <include refid="Base_Column_List"/>
        from fcorder
        <where>
            <if test="orderStatus != null">
                and OrderStatus = #{orderStatus,jdbcType=VARCHAR}
            </if>
            <if test="orderType != null">
                and OrderType = #{orderType,jdbcType=VARCHAR}
            </if>
            <![CDATA[AND DATE_FORMAT(openDay, '%Y-%m-%d') <=  DATE_FORMAT(now(), '%Y-%m-%d')]]>
            <![CDATA[AND DATE_FORMAT(closeDay, '%Y-%m-%d') >=  DATE_FORMAT(now(), '%Y-%m-%d')]]>
            <if test="grpOrderNo != null">
                and GrpOrderNo = #{grpOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="grpNo != null">
                and GrpNo = #{grpNo,jdbcType=VARCHAR}
            </if>
            <if test="perNo != null">
                and PerNo = #{perNo,jdbcType=VARCHAR}
            </if>
            <if test="perAppNo != null">
                and PerAppNo = #{perAppNo,jdbcType=VARCHAR}
            </if>
            <if test="costNo != null">
                and CostNo = #{costNo,jdbcType=VARCHAR}
            </if>
            <if test="pubEleNo != null">
                and PubEleNo = #{pubEleNo,jdbcType=VARCHAR}
            </if>
            <if test="effectDate != null">
                and EffectDate = #{effectDate,jdbcType=DATE}
            </if>
            <if test="isInfo != null">
                and IsInfo = #{isInfo,jdbcType=VARCHAR}
            </if>
            <if test="isLock != null">
                and IsLock = #{isLock,jdbcType=VARCHAR}
            </if>
            <if test="orderSource != null">
                and OrderSource = #{orderSource,jdbcType=VARCHAR}
            </if>
            <if test="clientNo != null">
                and ClientNo = #{clientNo,jdbcType=VARCHAR}
            </if>
        </where>
        order by MakeDate, MakeTime DESC
    </select>

</mapper>
