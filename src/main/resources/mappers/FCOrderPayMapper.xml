<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCOrderPayMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCOrderPay">
    <id column="PayNo" jdbcType="VARCHAR" property="payNo" />
    <result column="OrderNo" jdbcType="VARCHAR" property="orderNo" />
    <result column="PayType" jdbcType="VARCHAR" property="payType" />
    <result column="TotalPrem" jdbcType="DOUBLE" property="totalPrem" />
    <result column="GrpPrem" jdbcType="DOUBLE" property="grpPrem" />
    <result column="PersonPrem" jdbcType="DOUBLE" property="personPrem" />
    <result column="BatchPayStatus" jdbcType="VARCHAR" property="batchPayStatus" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    PayNo, OrderNo, PayType, TotalPrem, GrpPrem, PersonPrem, BatchPayStatus, Operator,
    OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from fcorderpay
    where PayNo = #{payNo,jdbcType=VARCHAR}
  </select>
    <select id="selectByOrderNo" resultType="java.util.Map">
      select f.OrderNo,
             f.PayType,
             f.TotalPrem,
             f.GrpPrem,
             f.PersonPrem,
             f.BankCode,
             f1.CodeName BankName,
             f2.CodeName BankAccType,
             f.BankAccNo,
             f.BankAccName
      from fcorderpay f
      left join fdcode f1 on f1.CodeType = 'Bank' and f1.CodeKey = f.BankCode
      left join fdcode f2 on f2.CodeType = 'BankAccType' and f2.CodeKey = f.BankAccType
      where OrderNo = #{orderNo} and IsValid = '0'
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcorderpay
    where PayNo = #{payNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCOrderPay">
    insert into fcorderpay (PayNo, OrderNo, PayType,
      TotalPrem, GrpPrem, PersonPrem,
      BatchPayStatus, Operator, OperatorCom,
      MakeDate, MakeTime, ModifyDate,
      ModifyTime)
    values (#{payNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{payType,jdbcType=VARCHAR},
      #{totalPrem,jdbcType=DOUBLE}, #{grpPrem,jdbcType=DOUBLE}, #{personPrem,jdbcType=DOUBLE},
      #{batchPayStatus,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR},
      #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE},
      #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCOrderPay">
    insert into fcorderpay
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="payNo != null">
        PayNo,
      </if>
      <if test="orderNo != null">
        OrderNo,
      </if>
      <if test="payType != null">
        PayType,
      </if>
      <if test="totalPrem != null">
        TotalPrem,
      </if>
      <if test="grpPrem != null">
        GrpPrem,
      </if>
      <if test="personPrem != null">
        PersonPrem,
      </if>
      <if test="batchPayStatus != null">
        BatchPayStatus,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="payNo != null">
        #{payNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=VARCHAR},
      </if>
      <if test="totalPrem != null">
        #{totalPrem,jdbcType=DOUBLE},
      </if>
      <if test="grpPrem != null">
        #{grpPrem,jdbcType=DOUBLE},
      </if>
      <if test="personPrem != null">
        #{personPrem,jdbcType=DOUBLE},
      </if>
      <if test="batchPayStatus != null">
        #{batchPayStatus,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCOrderPay">
    update fcorderpay
    <set>
      <if test="orderNo != null">
        OrderNo = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        PayType = #{payType,jdbcType=VARCHAR},
      </if>
      <if test="totalPrem != null">
        TotalPrem = #{totalPrem,jdbcType=DOUBLE},
      </if>
      <if test="grpPrem != null">
        GrpPrem = #{grpPrem,jdbcType=DOUBLE},
      </if>
      <if test="personPrem != null">
        PersonPrem = #{personPrem,jdbcType=DOUBLE},
      </if>
      <if test="batchPayStatus != null">
        BatchPayStatus = #{batchPayStatus,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where PayNo = #{payNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCOrderPay">
    update fcorderpay
    set OrderNo = #{orderNo,jdbcType=VARCHAR},
      PayType = #{payType,jdbcType=VARCHAR},
      TotalPrem = #{totalPrem,jdbcType=DOUBLE},
      GrpPrem = #{grpPrem,jdbcType=DOUBLE},
      PersonPrem = #{personPrem,jdbcType=DOUBLE},
      BatchPayStatus = #{batchPayStatus,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where PayNo = #{payNo,jdbcType=VARCHAR}
  </update>
</mapper>