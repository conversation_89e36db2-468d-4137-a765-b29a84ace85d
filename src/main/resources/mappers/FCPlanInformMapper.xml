<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCPlanInformMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCPlanInform">
    <id column="EnsureCode" jdbcType="VARCHAR" property="ensureCode" />
    <id column="PlanCode" jdbcType="VARCHAR" property="planCode" />
    <id column="InformNumber" jdbcType="VARCHAR" property="informNumber" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    EnsureCode, PlanCode, InformNumber, Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, 
    ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCPlanInformKey" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcplaninform
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
      and PlanCode = #{planCode,jdbcType=VARCHAR}
      and InformNumber = #{informNumber,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCPlanInformKey">
    delete from fcplaninform
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
      and PlanCode = #{planCode,jdbcType=VARCHAR}
      and InformNumber = #{informNumber,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCPlanInform">
    insert into fcplaninform (EnsureCode, PlanCode, InformNumber, 
      Operator, OperatorCom, MakeDate, 
      MakeTime, ModifyDate, ModifyTime
      )
    values (#{ensureCode,jdbcType=VARCHAR}, #{planCode,jdbcType=VARCHAR}, #{informNumber,jdbcType=VARCHAR}, 
      #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, 
      #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCPlanInform">
    insert into fcplaninform
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ensureCode != null">
        EnsureCode,
      </if>
      <if test="planCode != null">
        PlanCode,
      </if>
      <if test="informNumber != null">
        InformNumber,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ensureCode != null">
        #{ensureCode,jdbcType=VARCHAR},
      </if>
      <if test="planCode != null">
        #{planCode,jdbcType=VARCHAR},
      </if>
      <if test="informNumber != null">
        #{informNumber,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCPlanInform">
    update fcplaninform
    <set>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
      and PlanCode = #{planCode,jdbcType=VARCHAR}
      and InformNumber = #{informNumber,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCPlanInform">
    update fcplaninform
    set Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
      and PlanCode = #{planCode,jdbcType=VARCHAR}
      and InformNumber = #{informNumber,jdbcType=VARCHAR}
  </update>
  <select id="selectPlanInform" parameterType="java.lang.String" resultType="java.util.Map">
    select fi.InformContent,fi.InformDescribe from fcplaninform fc
    right join fcinform fi on fc.InformNumber=fi.InformNumber
    <where>
      <if test="planCode != null">
        PlanCode= #{planCode,jdbcType=VARCHAR}
      </if>
    </where>
  </select>

  <delete id="deleteByEnsureCode" parameterType="java.lang.String">
    delete from fcplaninform
    where EnsureCode = #{EnsureCode,jdbcType=VARCHAR}
  </delete>
</mapper>