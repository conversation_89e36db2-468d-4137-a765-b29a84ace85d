<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FDCodeZipMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FDCodeZip">
    <id column="ZipCodeKey" jdbcType="VARCHAR" property="zipCodeKey" />
    <result column="Province" jdbcType="VARCHAR" property="province" />
    <result column="City" jdbcType="VARCHAR" property="city" />
    <result column="County" jdbcType="VARCHAR" property="county" />
  </resultMap>
  <sql id="Base_Column_List">
    ZipCodeKey, Province, City, County
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fdcodezip
    where ZipCodeKey = #{zipCodeKey,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fdcodezip
    where ZipCodeKey = #{zipCodeKey,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FDCodeZip">
    insert into fdcodezip (ZipCodeKey, Province, City, 
      County)
    values (#{zipCodeKey,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, 
      #{county,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FDCodeZip">
    insert into fdcodezip
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="zipCodeKey != null">
        ZipCodeKey,
      </if>
      <if test="province != null">
        Province,
      </if>
      <if test="city != null">
        City,
      </if>
      <if test="county != null">
        County,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="zipCodeKey != null">
        #{zipCodeKey,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="county != null">
        #{county,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FDCodeZip">
    update fdcodezip
    <set>
      <if test="province != null">
        Province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        City = #{city,jdbcType=VARCHAR},
      </if>
      <if test="county != null">
        County = #{county,jdbcType=VARCHAR},
      </if>
    </set>
    where ZipCodeKey = #{zipCodeKey,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FDCodeZip">
    update fdcodezip
    set Province = #{province,jdbcType=VARCHAR},
      City = #{city,jdbcType=VARCHAR},
      County = #{county,jdbcType=VARCHAR}
    where ZipCodeKey = #{zipCodeKey,jdbcType=VARCHAR}
  </update>
</mapper>