<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.PremTrailMapper">
    <select id="getPrem_12020" resultType="java.lang.String" parameterType="java.util.Map">
   		select IFNULL(CAST(FORMAT(Prem*((select fg.Amnt*(IFNULL(fg.DiscountRatio,0)/100) from FcDutyAmountGrade fg WHERE fg.amountGrageCode=#{AmountGrageCode})/10000),2) AS Char),'') 
   		from fdriskrateinfo_12020 
   		where RiskCode=#{RiskCode}
   		and Sex=#{Sex}
   		and InsureAge=TIMESTAMPDIFF(YEAR, #{BirthDay}, #{CvaliDate})
    </select>
    <select id="getPrem_15030" resultType="java.lang.String" parameterType="java.util.Map">
   		select IFNULL(CAST(FORMAT(Prem*((select fg.Amnt*(IFNULL(fg.DiscountRatio,0)/100) from FcDutyAmountGrade fg WHERE fg.amountGrageCode=#{AmountGrageCode})/10000),2) AS Char),'')
   		from fdriskrateinfo_15030
   		where RiskCode=#{RiskCode}
   		and Occupationtype=#{OccupationType}
    </select>
    <select id="getPrem_15060" resultType="java.lang.String" parameterType="java.util.Map">
   		select IFNULL(CAST(FORMAT(a.Prem*((b.Amnt*(IFNULL(b.DiscountRatio,0)/100))/100),2) AS Char),'') 
   		from fdriskrateinfo_15060 a,FcDutyAmountGrade b
   		where a.RiskCode=b.RiskCode
   		and a.dutycode=b.dutycode
   		AND b.amountGrageCode=#{AmountGrageCode}
   		and a.RiskCode=#{RiskCode}
   		and a.Occupationtype=#{OccupationType}
    </select>
    <select id="getPrem_15060_2" resultType="java.util.Map" parameterType="java.util.Map">
    	select 
			IFNULL(CAST(FORMAT(b.Prem*((a.Amnt*(IFNULL(c.DiscountRatio,0)/100))/100),2) AS Char),'') Prem,
			a.OptDutyCode
		from FcDutyGradeOptionalAmountInfo a,fdriskrateinfo_15060 b,FcDutyAmountGrade c 
		where a.optdutycode=b.dutycode and a.amountGrageCode=c.amountGrageCode 
			and a.amountGrageCode=#{AmountGrageCode}
			and b.riskcode=#{RiskCode}
			and b.Occupationtype=#{OccupationType}
			and a.optdutycode in 
        <foreach collection="optDutyCode" item="optDutyCode"  open="(" separator="," close=")">
            #{optDutyCode}
        </foreach>
    </select>
    <select id="getPrem_16040" resultType="java.lang.String" parameterType="java.util.Map">
   		select IFNULL(CAST(FORMAT(a.Prem*((b.Amnt*(IFNULL(b.DiscountRatio,0)/100))/10000),2) AS Char),'') 
   		from fdriskrateinfo_16040 a,FcDutyAmountGrade b
   		where a.RiskCode=b.RiskCode 
   		and b.amountGrageCode=#{AmountGrageCode}
   		and a.Sex=#{Sex}
   		and a.InsureAge=TIMESTAMPDIFF(YEAR, #{BirthDay}, #{CvaliDate})
    </select>
    <select id="getPrem_16490" resultType="java.lang.String" parameterType="java.util.Map">
   		select IFNULL(CAST(FORMAT(a.Prem*((b.Amnt*(IFNULL(b.DiscountRatio,0)/100))/10000),2) AS Char),'')
   		from fdriskrateinfo_16490 a,FcDutyAmountGrade b
   		where a.RiskCode=b.RiskCode
   		and b.amountGrageCode=#{AmountGrageCode}
   		and a.Sex=#{Sex}
   		and a.InsureAge=TIMESTAMPDIFF(YEAR, #{BirthDay}, #{CvaliDate})
   		and a.IsRenewalInsurance=#{IsRenewalInsurance};
    </select>
    <select id="getPrem_17020" resultType="java.lang.String" parameterType="java.util.Map">
   		select IFNULL(CAST(FORMAT(a.Prem*((b.Amnt*(IFNULL(b.DiscountRatio,0)/100))/10),2) AS Char),'') 
   		from fdriskrateinfo_17020 a,FcDutyAmountGrade b
   		where a.RiskCode=b.RiskCode 
   		and a.dutycode=b.dutycode
   		and b.amountGrageCode=#{AmountGrageCode}
   		and a.InsureAge=TIMESTAMPDIFF(YEAR, #{BirthDay}, #{CvaliDate})
    </select>
    <select id="getPrem_17020_2" resultType="java.util.Map" parameterType="java.util.Map">
    	select 
			IFNULL(CAST(FORMAT(b.Prem*((a.Amnt*(IFNULL(c.DiscountRatio,0)/100))/IF(a.optdutycode='GD0035',1000,10)),2) AS Char),'') Prem,
			a.OptDutyCode
			from FcDutyGradeOptionalAmountInfo a,fdriskrateinfo_17020 b,FcDutyAmountGrade c 
			where a.optdutycode=b.dutycode and a.amountGrageCode=c.amountGrageCode 
			and a.amountGrageCode=#{AmountGrageCode}
			and b.riskcode=#{RiskCode}
			and b.InsureAge=TIMESTAMPDIFF(YEAR, #{BirthDay}, #{CvaliDate})
			and a.optdutycode in 
        <foreach collection="optDutyCode" item="optDutyCode"  open="(" separator="," close=")">
            #{optDutyCode}
        </foreach>
    </select>
    <select id="getPrem_17030" resultType="java.lang.String" parameterType="java.util.Map">
    	SELECT IFNULL(CAST(FORMAT(IF(#{JoinMedProtect}='0',864,720)*(a.RateAdjustFactor/100)*(b.adjustFactor/100)*(c.Rate/100)*d.Rate*e.Rate*f.adjustFactor*(IFNULL(g.DiscountRatio,0)/100),2) AS Char),'') FROM fdriskamntadjustfactor a,fdriskdeductible b,
		fdriskageadjustfactor c,fdriskoccupationtypedjustfactor d,fdriskinsurecountadjustfactor e,fdriskcompensationratio f,FcDutyAmountGrade g
		where a.RiskCode=b.RiskCode and b.RiskCode=c.RiskCode and c.RiskCode=d.RiskCode and d.RiskCode=e.RiskCode and e.RiskCode=f.RiskCode and f.RiskCode=g.RiskCode
		and a.RiskCode=#{RiskCode}
		and g.amountGrageCode=#{AmountGrageCode}
		and a.Amount = g.Amnt
		and b.Deductible=#{Deductible}
		and (c.MinInsureAge &lt;=TIMESTAMPDIFF(YEAR, #{BirthDay}, #{CvaliDate}) and TIMESTAMPDIFF(YEAR, #{BirthDay}, #{CvaliDate}) &lt;=c.MaxInsureAge)
		and d.OccupationType=#{OccupationType}
		and 10 between e.MinInsureCount and e.MaxInsureCount
		and f.CompensationRatio=#{CompensationRatio}
    </select>
    <select id="getPrem_15040" resultType="java.lang.String" parameterType="java.util.Map">
    select CASE WHEN a.Amnt>2000 then IFNULL(CAST(FORMAT((((b.preRate*2)+((a.Amnt-2000)/1000)*b.AfterRate))*c.adjustFactor*d.adjustFactor*(IFNULL(a.DiscountRatio,0)/100),2) AS Char),'')
				else IFNULL(CAST(FORMAT((a.Amnt/1000)*b.preRate*c.adjustFactor*d.adjustFactor*(IFNULL(a.DiscountRatio,0)/100),2) AS Char),'') end as prem
		from FcDutyAmountGrade a,fdriskrateinfo_15040 b,fdriskdeductible c,fdriskcompensationratio d 
		where a.amountGrageCode=#{AmountGrageCode}
		and a.riskcode=b.riskcode
		and b.riskcode=c.riskcode
		and c.riskcode=d.riskcode
		and b.OccupationType=#{OccupationType}
		and b.JoinMedProtect=#{JoinMedProtect}
		and c.Deductible=#{Deductible}
		and d.CompensationRatio=#{CompensationRatio}
    </select>
    <select id="getPrem_17010" resultType="java.lang.String" parameterType="java.util.Map">
    	SELECT IFNULL(CAST(FORMAT(IF(#{JoinMedProtect}='0',376,312)*(a.RateAdjustFactor/100)*(b.adjustFactor/100)*(c.Rate/100)*d.Rate*e.Rate*f.adjustFactor*(IFNULL(g.DiscountRatio,0)/100),2) AS Char),'') 
    	FROM fdriskamntadjustfactor a,fdriskdeductible b,
		fdriskageadjustfactor c,fdriskoccupationtypedjustfactor d,fdriskinsurecountadjustfactor e,fdriskcompensationratio f,FcDutyAmountGrade g
		where a.RiskCode=b.RiskCode and b.RiskCode=c.RiskCode and c.RiskCode=d.RiskCode and d.RiskCode=e.RiskCode and e.RiskCode=f.RiskCode and f.RiskCode=g.RiskCode
		and a.RiskCode=#{RiskCode}
		and g.amountGrageCode=#{AmountGrageCode}
		and a.Amount = g.Amnt
		and b.Deductible=#{Deductible}
		and (c.MinInsureAge &lt;=TIMESTAMPDIFF(YEAR, #{BirthDay}, #{CvaliDate}) and TIMESTAMPDIFF(YEAR, #{BirthDay}, #{CvaliDate}) &lt;=c.MaxInsureAge)
		and d.OccupationType=#{OccupationType}
		and 10 between e.MinInsureCount and e.MaxInsureCount
		and f.CompensationRatio=#{CompensationRatio}
    </select>
    <select id="getPrem_17050" resultType="java.lang.String" parameterType="java.util.Map">
    	SELECT IFNULL(CAST(FORMAT(b.Rate*c.adjustfactor*(IFNULL(a.DiscountRatio,0)/100),2) AS Char),'') 
    	FROM FcDutyAmountGrade a,fdriskrateinfo_17050 b,fdriskcompensationratio c 
		where a.RiskCode=b.RiskCode and b.RiskCode=c.RiskCode
		and a.amountGrageCode=#{AmountGrageCode}
		and (b.MinInsureAge &lt;=TIMESTAMPDIFF(YEAR, #{BirthDay}, #{CvaliDate}) and TIMESTAMPDIFF(YEAR, #{BirthDay}, #{CvaliDate}) &lt;=b.MaxInsureAge)
		and b.JoinMedProtect=#{JoinMedProtect}
		and b.Amount=a.Amnt
		and c.CompensationRatio=#{CompensationRatio}
    </select>
    <select id="getPrem_15070" resultType="java.lang.String" parameterType="java.util.Map">
   		 SELECT IFNULL(CAST(FORMAT((a.Amnt/10000)*b.Rate*(IFNULL(a.DiscountRatio,0)/100),2) AS Char),'') from FcDutyAmountGrade a,fdriskrateinfo_15070 b 
   		 where a.amountGrageCode=#{AmountGrageCode}
   		 and a.RiskCode=b.RiskCode 
   		 and a.DutyCode=b.DutyCode 
   		 and a.RiskType=b.RiskType
    </select>
    <select id="getPrem_15070_2" resultType="java.util.Map" parameterType="java.util.Map">
   		 SELECT IFNULL(CAST(FORMAT((a.amnt/10000)*c.Rate*(IFNULL(b.DiscountRatio,0)/100),2) AS Char),'') Prem, 
   		 a.OptDutyCode
   		 from FcDutyGradeOptionalAmountInfo a,FcDutyAmountGrade b,fdriskrateinfo_15070 c 
			where a.amountGrageCode=b.amountGrageCode 
			and b.RiskCode=c.RiskCode 
			and b.RiskType=c.RiskType
			and a.amountGrageCode=#{AmountGrageCode}
			and c.JoinMedProtect=#{JoinMedProtect}
			and c.dutycode in 
			<foreach collection="optDutyCode" item="optDutyCode"  open="(" separator="," close=")">
	            #{optDutyCode}
	        </foreach>
    </select>
    <select id="premTrial_311012" resultType="java.lang.String" parameterType="com.sinosoft.eflex.model.DailyPremTrail">
   		 SELECT IFNULL(CAST(FORMAT(((#{amount}+0)/10000*Prem),2) AS Char),'') from fcdailyrate_311012 
	   		 WHERE RiskCode=#{riskCode} 
	   		 and PlanCode=#{planCode}
	   		 and InsurePeriod=#{insurePeriod}
	   		 and InsureAge=TIMESTAMPDIFF(YEAR, #{birthDay}, #{insureDate})
	   		 and Gender=#{gender} 
	   		 and PayPeriod=#{payPeriod}
    </select>
</mapper>