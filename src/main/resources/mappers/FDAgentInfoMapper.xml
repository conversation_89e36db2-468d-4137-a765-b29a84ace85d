<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FDAgentInfoMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FDAgentInfo">
    <id column="SerialNo" jdbcType="VARCHAR" property="serialNo" />
    <result column="AgentCode" jdbcType="VARCHAR" property="agentCode" />
    <result column="AgentGroup" jdbcType="VARCHAR" property="agentGroup" />
    <result column="ManageCom" jdbcType="VARCHAR" property="manageCom" />
    <result column="Password" jdbcType="VARCHAR" property="password" />
    <result column="EntryNo" jdbcType="VARCHAR" property="entryNo" />
    <result column="Name" jdbcType="VARCHAR" property="name" />
    <result column="Sex" jdbcType="CHAR" property="sex" />
    <result column="Birthday" jdbcType="DATE" property="birthday" />
    <result column="NativePlace" jdbcType="VARCHAR" property="nativePlace" />
    <result column="Nationality" jdbcType="VARCHAR" property="nationality" />
    <result column="Marriage" jdbcType="CHAR" property="marriage" />
    <result column="CreditGrade" jdbcType="VARCHAR" property="creditGrade" />
    <result column="HomeAddressCode" jdbcType="VARCHAR" property="homeAddressCode" />
    <result column="HomeAddress" jdbcType="VARCHAR" property="homeAddress" />
    <result column="PostalAddress" jdbcType="VARCHAR" property="postalAddress" />
    <result column="ZipCode" jdbcType="VARCHAR" property="zipCode" />
    <result column="Phone" jdbcType="VARCHAR" property="phone" />
    <result column="BP" jdbcType="VARCHAR" property="BP" />
    <result column="Mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="EMail" jdbcType="VARCHAR" property="EMail" />
    <result column="MarriageDate" jdbcType="TIMESTAMP" property="marriageDate" />
    <result column="IDNo" jdbcType="VARCHAR" property="IDNo" />
    <result column="Source" jdbcType="VARCHAR" property="source" />
    <result column="BloodType" jdbcType="VARCHAR" property="bloodType" />
    <result column="PolityVisage" jdbcType="VARCHAR" property="polityVisage" />
    <result column="Degree" jdbcType="CHAR" property="degree" />
    <result column="GraduateSchool" jdbcType="VARCHAR" property="graduateSchool" />
    <result column="Speciality" jdbcType="VARCHAR" property="speciality" />
    <result column="PostTitle" jdbcType="VARCHAR" property="postTitle" />
    <result column="ForeignLevel" jdbcType="VARCHAR" property="foreignLevel" />
    <result column="WorkAge" jdbcType="SMALLINT" property="workAge" />
    <result column="OldCom" jdbcType="VARCHAR" property="oldCom" />
    <result column="OldOccupation" jdbcType="VARCHAR" property="oldOccupation" />
    <result column="HeadShip" jdbcType="VARCHAR" property="headShip" />
    <result column="RecommendAgent" jdbcType="VARCHAR" property="recommendAgent" />
    <result column="Business" jdbcType="VARCHAR" property="business" />
    <result column="SaleQuaf" jdbcType="CHAR" property="saleQuaf" />
    <result column="QuafNo" jdbcType="VARCHAR" property="quafNo" />
    <result column="QuafStartDate" jdbcType="TIMESTAMP" property="quafStartDate" />
    <result column="QuafEndDate" jdbcType="TIMESTAMP" property="quafEndDate" />
    <result column="DevNo1" jdbcType="VARCHAR" property="devNo1" />
    <result column="DevNo2" jdbcType="VARCHAR" property="devNo2" />
    <result column="RetainContNo" jdbcType="VARCHAR" property="retainContNo" />
    <result column="AgentKind" jdbcType="VARCHAR" property="agentKind" />
    <result column="DevGrade" jdbcType="VARCHAR" property="devGrade" />
    <result column="InsideFlag" jdbcType="CHAR" property="insideFlag" />
    <result column="FullTimeFlag" jdbcType="CHAR" property="fullTimeFlag" />
    <result column="NoWorkFlag" jdbcType="CHAR" property="noWorkFlag" />
    <result column="TrainDate" jdbcType="TIMESTAMP" property="trainDate" />
    <result column="EmployDate" jdbcType="TIMESTAMP" property="employDate" />
    <result column="InDueFormDate" jdbcType="TIMESTAMP" property="inDueFormDate" />
    <result column="OutWorkDate" jdbcType="TIMESTAMP" property="outWorkDate" />
    <result column="RecommendNo" jdbcType="VARCHAR" property="recommendNo" />
    <result column="CautionerName" jdbcType="VARCHAR" property="cautionerName" />
    <result column="CautionerSex" jdbcType="VARCHAR" property="cautionerSex" />
    <result column="CautionerID" jdbcType="VARCHAR" property="cautionerID" />
    <result column="CautionerBirthday" jdbcType="TIMESTAMP" property="cautionerBirthday" />
    <result column="Approver" jdbcType="VARCHAR" property="approver" />
    <result column="ApproveDate" jdbcType="TIMESTAMP" property="approveDate" />
    <result column="AssuMoney" jdbcType="DECIMAL" property="assuMoney" />
    <result column="Remark" jdbcType="VARCHAR" property="remark" />
    <result column="AgentState" jdbcType="VARCHAR" property="agentState" />
    <result column="QualiPassFlag" jdbcType="CHAR" property="qualiPassFlag" />
    <result column="SmokeFlag" jdbcType="CHAR" property="smokeFlag" />
    <result column="RgtAddress" jdbcType="VARCHAR" property="rgtAddress" />
    <result column="BankCode" jdbcType="VARCHAR" property="bankCode" />
    <result column="BankAccNo" jdbcType="VARCHAR" property="bankAccNo" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="MakeDate" jdbcType="TIMESTAMP" property="makeDate" />
    <result column="MakeTime" jdbcType="CHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="ModifyTime" jdbcType="CHAR" property="modifyTime" />
    <result column="BranchType" jdbcType="VARCHAR" property="branchType" />
    <result column="TrainPeriods" jdbcType="VARCHAR" property="trainPeriods" />
    <result column="BranchCode" jdbcType="VARCHAR" property="branchCode" />
    <result column="Age" jdbcType="INTEGER" property="age" />
    <result column="ChannelName" jdbcType="VARCHAR" property="channelName" />
    <result column="IDType" jdbcType="VARCHAR" property="IDType" />
    <result column="BranchType2" jdbcType="VARCHAR" property="branchType2" />
    <result column="ReceiptNo" jdbcType="VARCHAR" property="receiptNo" />
    <result column="QualifNo" jdbcType="VARCHAR" property="qualifNo" />
    <result column="Honor" jdbcType="VARCHAR" property="honor" />
    <result column="IsFlag" jdbcType="CHAR" property="isFlag" />
    <result column="IsFlag1" jdbcType="CHAR" property="isFlag1" />
    <result column="IsFlag2" jdbcType="CHAR" property="isFlag2" />
  </resultMap>
  <sql id="Base_Column_List">
    SerialNo, AgentCode, AgentGroup, ManageCom, Password, EntryNo, Name, Sex, Birthday, 
    NativePlace, Nationality, Marriage, CreditGrade, HomeAddressCode, HomeAddress, PostalAddress, 
    ZipCode, Phone, BP, Mobile, EMail, MarriageDate, IDNo, Source, BloodType, PolityVisage, 
    Degree, GraduateSchool, Speciality, PostTitle, ForeignLevel, WorkAge, OldCom, OldOccupation, 
    HeadShip, RecommendAgent, Business, SaleQuaf, QuafNo, QuafStartDate, QuafEndDate, 
    DevNo1, DevNo2, RetainContNo, AgentKind, DevGrade, InsideFlag, FullTimeFlag, NoWorkFlag, 
    TrainDate, EmployDate, InDueFormDate, OutWorkDate, RecommendNo, CautionerName, CautionerSex, 
    CautionerID, CautionerBirthday, Approver, ApproveDate, AssuMoney, Remark, AgentState, 
    QualiPassFlag, SmokeFlag, RgtAddress, BankCode, BankAccNo, Operator, MakeDate, MakeTime, 
    ModifyDate, ModifyTime, BranchType, TrainPeriods, BranchCode, Age, ChannelName, IDType, 
    BranchType2, ReceiptNo, QualifNo, Honor, IsFlag, IsFlag1, IsFlag2
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fdagentinfo
    where SerialNo = #{serialNo,jdbcType=VARCHAR}
  </select>
  <select id="selectByAgentCode" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fdagentinfo
    where AgentCode = #{agentCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fdagentinfo
    where SerialNo = #{serialNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FDAgentInfo">
    insert into fdagentinfo (SerialNo, AgentCode, AgentGroup, 
      ManageCom, Password, EntryNo, 
      Name, Sex, Birthday, NativePlace, 
      Nationality, Marriage, CreditGrade, 
      HomeAddressCode, HomeAddress, PostalAddress, 
      ZipCode, Phone, BP, 
      Mobile, EMail, MarriageDate, 
      IDNo, Source, BloodType, 
      PolityVisage, Degree, GraduateSchool, 
      Speciality, PostTitle, ForeignLevel, 
      WorkAge, OldCom, OldOccupation, 
      HeadShip, RecommendAgent, Business, 
      SaleQuaf, QuafNo, QuafStartDate, 
      QuafEndDate, DevNo1, DevNo2, 
      RetainContNo, AgentKind, DevGrade, 
      InsideFlag, FullTimeFlag, NoWorkFlag, 
      TrainDate, EmployDate, InDueFormDate, 
      OutWorkDate, RecommendNo, CautionerName, 
      CautionerSex, CautionerID, CautionerBirthday, 
      Approver, ApproveDate, AssuMoney, 
      Remark, AgentState, QualiPassFlag, 
      SmokeFlag, RgtAddress, BankCode, 
      BankAccNo, Operator, MakeDate, 
      MakeTime, ModifyDate, ModifyTime, 
      BranchType, TrainPeriods, BranchCode, 
      Age, ChannelName, IDType, 
      BranchType2, ReceiptNo, QualifNo, 
      Honor, IsFlag, IsFlag1, IsFlag2
      )
    values (#{serialNo,jdbcType=VARCHAR}, #{agentCode,jdbcType=VARCHAR}, #{agentGroup,jdbcType=VARCHAR}, 
      #{manageCom,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}, #{entryNo,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{sex,jdbcType=CHAR}, #{birthday,jdbcType=DATE}, #{nativePlace,jdbcType=VARCHAR}, 
      #{nationality,jdbcType=VARCHAR}, #{marriage,jdbcType=CHAR}, #{creditGrade,jdbcType=VARCHAR}, 
      #{homeAddressCode,jdbcType=VARCHAR}, #{homeAddress,jdbcType=VARCHAR}, #{postalAddress,jdbcType=VARCHAR}, 
      #{zipCode,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, #{BP,jdbcType=VARCHAR}, 
      #{mobile,jdbcType=VARCHAR}, #{EMail,jdbcType=VARCHAR}, #{marriageDate,jdbcType=TIMESTAMP}, 
      #{IDNo,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR}, #{bloodType,jdbcType=VARCHAR}, 
      #{polityVisage,jdbcType=VARCHAR}, #{degree,jdbcType=CHAR}, #{graduateSchool,jdbcType=VARCHAR}, 
      #{speciality,jdbcType=VARCHAR}, #{postTitle,jdbcType=VARCHAR}, #{foreignLevel,jdbcType=VARCHAR}, 
      #{workAge,jdbcType=SMALLINT}, #{oldCom,jdbcType=VARCHAR}, #{oldOccupation,jdbcType=VARCHAR}, 
      #{headShip,jdbcType=VARCHAR}, #{recommendAgent,jdbcType=VARCHAR}, #{business,jdbcType=VARCHAR}, 
      #{saleQuaf,jdbcType=CHAR}, #{quafNo,jdbcType=VARCHAR}, #{quafStartDate,jdbcType=TIMESTAMP}, 
      #{quafEndDate,jdbcType=TIMESTAMP}, #{devNo1,jdbcType=VARCHAR}, #{devNo2,jdbcType=VARCHAR}, 
      #{retainContNo,jdbcType=VARCHAR}, #{agentKind,jdbcType=VARCHAR}, #{devGrade,jdbcType=VARCHAR}, 
      #{insideFlag,jdbcType=CHAR}, #{fullTimeFlag,jdbcType=CHAR}, #{noWorkFlag,jdbcType=CHAR}, 
      #{trainDate,jdbcType=TIMESTAMP}, #{employDate,jdbcType=TIMESTAMP}, #{inDueFormDate,jdbcType=TIMESTAMP}, 
      #{outWorkDate,jdbcType=TIMESTAMP}, #{recommendNo,jdbcType=VARCHAR}, #{cautionerName,jdbcType=VARCHAR}, 
      #{cautionerSex,jdbcType=VARCHAR}, #{cautionerID,jdbcType=VARCHAR}, #{cautionerBirthday,jdbcType=TIMESTAMP}, 
      #{approver,jdbcType=VARCHAR}, #{approveDate,jdbcType=TIMESTAMP}, #{assuMoney,jdbcType=DECIMAL}, 
      #{remark,jdbcType=VARCHAR}, #{agentState,jdbcType=VARCHAR}, #{qualiPassFlag,jdbcType=CHAR}, 
      #{smokeFlag,jdbcType=CHAR}, #{rgtAddress,jdbcType=VARCHAR}, #{bankCode,jdbcType=VARCHAR}, 
      #{bankAccNo,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{makeDate,jdbcType=TIMESTAMP}, 
      #{makeTime,jdbcType=CHAR}, #{modifyDate,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=CHAR}, 
      #{branchType,jdbcType=VARCHAR}, #{trainPeriods,jdbcType=VARCHAR}, #{branchCode,jdbcType=VARCHAR}, 
      #{age,jdbcType=INTEGER}, #{channelName,jdbcType=VARCHAR}, #{IDType,jdbcType=VARCHAR}, 
      #{branchType2,jdbcType=VARCHAR}, #{receiptNo,jdbcType=VARCHAR}, #{qualifNo,jdbcType=VARCHAR}, 
      #{honor,jdbcType=VARCHAR}, #{isFlag,jdbcType=CHAR}, #{isFlag1,jdbcType=CHAR}, #{isFlag2,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FDAgentInfo">
    insert into fdagentinfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="serialNo != null">
        SerialNo,
      </if>
      <if test="agentCode != null">
        AgentCode,
      </if>
      <if test="agentGroup != null">
        AgentGroup,
      </if>
      <if test="manageCom != null">
        ManageCom,
      </if>
      <if test="password != null">
        Password,
      </if>
      <if test="entryNo != null">
        EntryNo,
      </if>
      <if test="name != null">
        Name,
      </if>
      <if test="sex != null">
        Sex,
      </if>
      <if test="birthday != null">
        Birthday,
      </if>
      <if test="nativePlace != null">
        NativePlace,
      </if>
      <if test="nationality != null">
        Nationality,
      </if>
      <if test="marriage != null">
        Marriage,
      </if>
      <if test="creditGrade != null">
        CreditGrade,
      </if>
      <if test="homeAddressCode != null">
        HomeAddressCode,
      </if>
      <if test="homeAddress != null">
        HomeAddress,
      </if>
      <if test="postalAddress != null">
        PostalAddress,
      </if>
      <if test="zipCode != null">
        ZipCode,
      </if>
      <if test="phone != null">
        Phone,
      </if>
      <if test="BP != null">
        BP,
      </if>
      <if test="mobile != null">
        Mobile,
      </if>
      <if test="EMail != null">
        EMail,
      </if>
      <if test="marriageDate != null">
        MarriageDate,
      </if>
      <if test="IDNo != null">
        IDNo,
      </if>
      <if test="source != null">
        Source,
      </if>
      <if test="bloodType != null">
        BloodType,
      </if>
      <if test="polityVisage != null">
        PolityVisage,
      </if>
      <if test="degree != null">
        Degree,
      </if>
      <if test="graduateSchool != null">
        GraduateSchool,
      </if>
      <if test="speciality != null">
        Speciality,
      </if>
      <if test="postTitle != null">
        PostTitle,
      </if>
      <if test="foreignLevel != null">
        ForeignLevel,
      </if>
      <if test="workAge != null">
        WorkAge,
      </if>
      <if test="oldCom != null">
        OldCom,
      </if>
      <if test="oldOccupation != null">
        OldOccupation,
      </if>
      <if test="headShip != null">
        HeadShip,
      </if>
      <if test="recommendAgent != null">
        RecommendAgent,
      </if>
      <if test="business != null">
        Business,
      </if>
      <if test="saleQuaf != null">
        SaleQuaf,
      </if>
      <if test="quafNo != null">
        QuafNo,
      </if>
      <if test="quafStartDate != null">
        QuafStartDate,
      </if>
      <if test="quafEndDate != null">
        QuafEndDate,
      </if>
      <if test="devNo1 != null">
        DevNo1,
      </if>
      <if test="devNo2 != null">
        DevNo2,
      </if>
      <if test="retainContNo != null">
        RetainContNo,
      </if>
      <if test="agentKind != null">
        AgentKind,
      </if>
      <if test="devGrade != null">
        DevGrade,
      </if>
      <if test="insideFlag != null">
        InsideFlag,
      </if>
      <if test="fullTimeFlag != null">
        FullTimeFlag,
      </if>
      <if test="noWorkFlag != null">
        NoWorkFlag,
      </if>
      <if test="trainDate != null">
        TrainDate,
      </if>
      <if test="employDate != null">
        EmployDate,
      </if>
      <if test="inDueFormDate != null">
        InDueFormDate,
      </if>
      <if test="outWorkDate != null">
        OutWorkDate,
      </if>
      <if test="recommendNo != null">
        RecommendNo,
      </if>
      <if test="cautionerName != null">
        CautionerName,
      </if>
      <if test="cautionerSex != null">
        CautionerSex,
      </if>
      <if test="cautionerID != null">
        CautionerID,
      </if>
      <if test="cautionerBirthday != null">
        CautionerBirthday,
      </if>
      <if test="approver != null">
        Approver,
      </if>
      <if test="approveDate != null">
        ApproveDate,
      </if>
      <if test="assuMoney != null">
        AssuMoney,
      </if>
      <if test="remark != null">
        Remark,
      </if>
      <if test="agentState != null">
        AgentState,
      </if>
      <if test="qualiPassFlag != null">
        QualiPassFlag,
      </if>
      <if test="smokeFlag != null">
        SmokeFlag,
      </if>
      <if test="rgtAddress != null">
        RgtAddress,
      </if>
      <if test="bankCode != null">
        BankCode,
      </if>
      <if test="bankAccNo != null">
        BankAccNo,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
      <if test="branchType != null">
        BranchType,
      </if>
      <if test="trainPeriods != null">
        TrainPeriods,
      </if>
      <if test="branchCode != null">
        BranchCode,
      </if>
      <if test="age != null">
        Age,
      </if>
      <if test="channelName != null">
        ChannelName,
      </if>
      <if test="IDType != null">
        IDType,
      </if>
      <if test="branchType2 != null">
        BranchType2,
      </if>
      <if test="receiptNo != null">
        ReceiptNo,
      </if>
      <if test="qualifNo != null">
        QualifNo,
      </if>
      <if test="honor != null">
        Honor,
      </if>
      <if test="isFlag != null">
        IsFlag,
      </if>
      <if test="isFlag1 != null">
        IsFlag1,
      </if>
      <if test="isFlag2 != null">
        IsFlag2,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="serialNo != null">
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="agentCode != null">
        #{agentCode,jdbcType=VARCHAR},
      </if>
      <if test="agentGroup != null">
        #{agentGroup,jdbcType=VARCHAR},
      </if>
      <if test="manageCom != null">
        #{manageCom,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="entryNo != null">
        #{entryNo,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=CHAR},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=DATE},
      </if>
      <if test="nativePlace != null">
        #{nativePlace,jdbcType=VARCHAR},
      </if>
      <if test="nationality != null">
        #{nationality,jdbcType=VARCHAR},
      </if>
      <if test="marriage != null">
        #{marriage,jdbcType=CHAR},
      </if>
      <if test="creditGrade != null">
        #{creditGrade,jdbcType=VARCHAR},
      </if>
      <if test="homeAddressCode != null">
        #{homeAddressCode,jdbcType=VARCHAR},
      </if>
      <if test="homeAddress != null">
        #{homeAddress,jdbcType=VARCHAR},
      </if>
      <if test="postalAddress != null">
        #{postalAddress,jdbcType=VARCHAR},
      </if>
      <if test="zipCode != null">
        #{zipCode,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="BP != null">
        #{BP,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="EMail != null">
        #{EMail,jdbcType=VARCHAR},
      </if>
      <if test="marriageDate != null">
        #{marriageDate,jdbcType=TIMESTAMP},
      </if>
      <if test="IDNo != null">
        #{IDNo,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="bloodType != null">
        #{bloodType,jdbcType=VARCHAR},
      </if>
      <if test="polityVisage != null">
        #{polityVisage,jdbcType=VARCHAR},
      </if>
      <if test="degree != null">
        #{degree,jdbcType=CHAR},
      </if>
      <if test="graduateSchool != null">
        #{graduateSchool,jdbcType=VARCHAR},
      </if>
      <if test="speciality != null">
        #{speciality,jdbcType=VARCHAR},
      </if>
      <if test="postTitle != null">
        #{postTitle,jdbcType=VARCHAR},
      </if>
      <if test="foreignLevel != null">
        #{foreignLevel,jdbcType=VARCHAR},
      </if>
      <if test="workAge != null">
        #{workAge,jdbcType=SMALLINT},
      </if>
      <if test="oldCom != null">
        #{oldCom,jdbcType=VARCHAR},
      </if>
      <if test="oldOccupation != null">
        #{oldOccupation,jdbcType=VARCHAR},
      </if>
      <if test="headShip != null">
        #{headShip,jdbcType=VARCHAR},
      </if>
      <if test="recommendAgent != null">
        #{recommendAgent,jdbcType=VARCHAR},
      </if>
      <if test="business != null">
        #{business,jdbcType=VARCHAR},
      </if>
      <if test="saleQuaf != null">
        #{saleQuaf,jdbcType=CHAR},
      </if>
      <if test="quafNo != null">
        #{quafNo,jdbcType=VARCHAR},
      </if>
      <if test="quafStartDate != null">
        #{quafStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="quafEndDate != null">
        #{quafEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="devNo1 != null">
        #{devNo1,jdbcType=VARCHAR},
      </if>
      <if test="devNo2 != null">
        #{devNo2,jdbcType=VARCHAR},
      </if>
      <if test="retainContNo != null">
        #{retainContNo,jdbcType=VARCHAR},
      </if>
      <if test="agentKind != null">
        #{agentKind,jdbcType=VARCHAR},
      </if>
      <if test="devGrade != null">
        #{devGrade,jdbcType=VARCHAR},
      </if>
      <if test="insideFlag != null">
        #{insideFlag,jdbcType=CHAR},
      </if>
      <if test="fullTimeFlag != null">
        #{fullTimeFlag,jdbcType=CHAR},
      </if>
      <if test="noWorkFlag != null">
        #{noWorkFlag,jdbcType=CHAR},
      </if>
      <if test="trainDate != null">
        #{trainDate,jdbcType=TIMESTAMP},
      </if>
      <if test="employDate != null">
        #{employDate,jdbcType=TIMESTAMP},
      </if>
      <if test="inDueFormDate != null">
        #{inDueFormDate,jdbcType=TIMESTAMP},
      </if>
      <if test="outWorkDate != null">
        #{outWorkDate,jdbcType=TIMESTAMP},
      </if>
      <if test="recommendNo != null">
        #{recommendNo,jdbcType=VARCHAR},
      </if>
      <if test="cautionerName != null">
        #{cautionerName,jdbcType=VARCHAR},
      </if>
      <if test="cautionerSex != null">
        #{cautionerSex,jdbcType=VARCHAR},
      </if>
      <if test="cautionerID != null">
        #{cautionerID,jdbcType=VARCHAR},
      </if>
      <if test="cautionerBirthday != null">
        #{cautionerBirthday,jdbcType=TIMESTAMP},
      </if>
      <if test="approver != null">
        #{approver,jdbcType=VARCHAR},
      </if>
      <if test="approveDate != null">
        #{approveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="assuMoney != null">
        #{assuMoney,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="agentState != null">
        #{agentState,jdbcType=VARCHAR},
      </if>
      <if test="qualiPassFlag != null">
        #{qualiPassFlag,jdbcType=CHAR},
      </if>
      <if test="smokeFlag != null">
        #{smokeFlag,jdbcType=CHAR},
      </if>
      <if test="rgtAddress != null">
        #{rgtAddress,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null">
        #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="bankAccNo != null">
        #{bankAccNo,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=CHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=CHAR},
      </if>
      <if test="branchType != null">
        #{branchType,jdbcType=VARCHAR},
      </if>
      <if test="trainPeriods != null">
        #{trainPeriods,jdbcType=VARCHAR},
      </if>
      <if test="branchCode != null">
        #{branchCode,jdbcType=VARCHAR},
      </if>
      <if test="age != null">
        #{age,jdbcType=INTEGER},
      </if>
      <if test="channelName != null">
        #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="IDType != null">
        #{IDType,jdbcType=VARCHAR},
      </if>
      <if test="branchType2 != null">
        #{branchType2,jdbcType=VARCHAR},
      </if>
      <if test="receiptNo != null">
        #{receiptNo,jdbcType=VARCHAR},
      </if>
      <if test="qualifNo != null">
        #{qualifNo,jdbcType=VARCHAR},
      </if>
      <if test="honor != null">
        #{honor,jdbcType=VARCHAR},
      </if>
      <if test="isFlag != null">
        #{isFlag,jdbcType=CHAR},
      </if>
      <if test="isFlag1 != null">
        #{isFlag1,jdbcType=CHAR},
      </if>
      <if test="isFlag2 != null">
        #{isFlag2,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FDAgentInfo">
    update fdagentinfo
    <set>
      <if test="agentCode != null">
        AgentCode = #{agentCode,jdbcType=VARCHAR},
      </if>
      <if test="agentGroup != null">
        AgentGroup = #{agentGroup,jdbcType=VARCHAR},
      </if>
      <if test="manageCom != null">
        ManageCom = #{manageCom,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        Password = #{password,jdbcType=VARCHAR},
      </if>
      <if test="entryNo != null">
        EntryNo = #{entryNo,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        Name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        Sex = #{sex,jdbcType=CHAR},
      </if>
      <if test="birthday != null">
        Birthday = #{birthday,jdbcType=DATE},
      </if>
      <if test="nativePlace != null">
        NativePlace = #{nativePlace,jdbcType=VARCHAR},
      </if>
      <if test="nationality != null">
        Nationality = #{nationality,jdbcType=VARCHAR},
      </if>
      <if test="marriage != null">
        Marriage = #{marriage,jdbcType=CHAR},
      </if>
      <if test="creditGrade != null">
        CreditGrade = #{creditGrade,jdbcType=VARCHAR},
      </if>
      <if test="homeAddressCode != null">
        HomeAddressCode = #{homeAddressCode,jdbcType=VARCHAR},
      </if>
      <if test="homeAddress != null">
        HomeAddress = #{homeAddress,jdbcType=VARCHAR},
      </if>
      <if test="postalAddress != null">
        PostalAddress = #{postalAddress,jdbcType=VARCHAR},
      </if>
      <if test="zipCode != null">
        ZipCode = #{zipCode,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        Phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="BP != null">
        BP = #{BP,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        Mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="EMail != null">
        EMail = #{EMail,jdbcType=VARCHAR},
      </if>
      <if test="marriageDate != null">
        MarriageDate = #{marriageDate,jdbcType=TIMESTAMP},
      </if>
      <if test="IDNo != null">
        IDNo = #{IDNo,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        Source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="bloodType != null">
        BloodType = #{bloodType,jdbcType=VARCHAR},
      </if>
      <if test="polityVisage != null">
        PolityVisage = #{polityVisage,jdbcType=VARCHAR},
      </if>
      <if test="degree != null">
        Degree = #{degree,jdbcType=CHAR},
      </if>
      <if test="graduateSchool != null">
        GraduateSchool = #{graduateSchool,jdbcType=VARCHAR},
      </if>
      <if test="speciality != null">
        Speciality = #{speciality,jdbcType=VARCHAR},
      </if>
      <if test="postTitle != null">
        PostTitle = #{postTitle,jdbcType=VARCHAR},
      </if>
      <if test="foreignLevel != null">
        ForeignLevel = #{foreignLevel,jdbcType=VARCHAR},
      </if>
      <if test="workAge != null">
        WorkAge = #{workAge,jdbcType=SMALLINT},
      </if>
      <if test="oldCom != null">
        OldCom = #{oldCom,jdbcType=VARCHAR},
      </if>
      <if test="oldOccupation != null">
        OldOccupation = #{oldOccupation,jdbcType=VARCHAR},
      </if>
      <if test="headShip != null">
        HeadShip = #{headShip,jdbcType=VARCHAR},
      </if>
      <if test="recommendAgent != null">
        RecommendAgent = #{recommendAgent,jdbcType=VARCHAR},
      </if>
      <if test="business != null">
        Business = #{business,jdbcType=VARCHAR},
      </if>
      <if test="saleQuaf != null">
        SaleQuaf = #{saleQuaf,jdbcType=CHAR},
      </if>
      <if test="quafNo != null">
        QuafNo = #{quafNo,jdbcType=VARCHAR},
      </if>
      <if test="quafStartDate != null">
        QuafStartDate = #{quafStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="quafEndDate != null">
        QuafEndDate = #{quafEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="devNo1 != null">
        DevNo1 = #{devNo1,jdbcType=VARCHAR},
      </if>
      <if test="devNo2 != null">
        DevNo2 = #{devNo2,jdbcType=VARCHAR},
      </if>
      <if test="retainContNo != null">
        RetainContNo = #{retainContNo,jdbcType=VARCHAR},
      </if>
      <if test="agentKind != null">
        AgentKind = #{agentKind,jdbcType=VARCHAR},
      </if>
      <if test="devGrade != null">
        DevGrade = #{devGrade,jdbcType=VARCHAR},
      </if>
      <if test="insideFlag != null">
        InsideFlag = #{insideFlag,jdbcType=CHAR},
      </if>
      <if test="fullTimeFlag != null">
        FullTimeFlag = #{fullTimeFlag,jdbcType=CHAR},
      </if>
      <if test="noWorkFlag != null">
        NoWorkFlag = #{noWorkFlag,jdbcType=CHAR},
      </if>
      <if test="trainDate != null">
        TrainDate = #{trainDate,jdbcType=TIMESTAMP},
      </if>
      <if test="employDate != null">
        EmployDate = #{employDate,jdbcType=TIMESTAMP},
      </if>
      <if test="inDueFormDate != null">
        InDueFormDate = #{inDueFormDate,jdbcType=TIMESTAMP},
      </if>
      <if test="outWorkDate != null">
        OutWorkDate = #{outWorkDate,jdbcType=TIMESTAMP},
      </if>
      <if test="recommendNo != null">
        RecommendNo = #{recommendNo,jdbcType=VARCHAR},
      </if>
      <if test="cautionerName != null">
        CautionerName = #{cautionerName,jdbcType=VARCHAR},
      </if>
      <if test="cautionerSex != null">
        CautionerSex = #{cautionerSex,jdbcType=VARCHAR},
      </if>
      <if test="cautionerID != null">
        CautionerID = #{cautionerID,jdbcType=VARCHAR},
      </if>
      <if test="cautionerBirthday != null">
        CautionerBirthday = #{cautionerBirthday,jdbcType=TIMESTAMP},
      </if>
      <if test="approver != null">
        Approver = #{approver,jdbcType=VARCHAR},
      </if>
      <if test="approveDate != null">
        ApproveDate = #{approveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="assuMoney != null">
        AssuMoney = #{assuMoney,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        Remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="agentState != null">
        AgentState = #{agentState,jdbcType=VARCHAR},
      </if>
      <if test="qualiPassFlag != null">
        QualiPassFlag = #{qualiPassFlag,jdbcType=CHAR},
      </if>
      <if test="smokeFlag != null">
        SmokeFlag = #{smokeFlag,jdbcType=CHAR},
      </if>
      <if test="rgtAddress != null">
        RgtAddress = #{rgtAddress,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null">
        BankCode = #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="bankAccNo != null">
        BankAccNo = #{bankAccNo,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=CHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=CHAR},
      </if>
      <if test="branchType != null">
        BranchType = #{branchType,jdbcType=VARCHAR},
      </if>
      <if test="trainPeriods != null">
        TrainPeriods = #{trainPeriods,jdbcType=VARCHAR},
      </if>
      <if test="branchCode != null">
        BranchCode = #{branchCode,jdbcType=VARCHAR},
      </if>
      <if test="age != null">
        Age = #{age,jdbcType=INTEGER},
      </if>
      <if test="channelName != null">
        ChannelName = #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="IDType != null">
        IDType = #{IDType,jdbcType=VARCHAR},
      </if>
      <if test="branchType2 != null">
        BranchType2 = #{branchType2,jdbcType=VARCHAR},
      </if>
      <if test="receiptNo != null">
        ReceiptNo = #{receiptNo,jdbcType=VARCHAR},
      </if>
      <if test="qualifNo != null">
        QualifNo = #{qualifNo,jdbcType=VARCHAR},
      </if>
      <if test="honor != null">
        Honor = #{honor,jdbcType=VARCHAR},
      </if>
      <if test="isFlag != null">
        IsFlag = #{isFlag,jdbcType=CHAR},
      </if>
      <if test="isFlag1 != null">
        IsFlag1 = #{isFlag1,jdbcType=CHAR},
      </if>
      <if test="isFlag2 != null">
        IsFlag2 = #{isFlag2,jdbcType=CHAR},
      </if>
    </set>
    where SerialNo = #{serialNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FDAgentInfo">
    update fdagentinfo
    set AgentCode = #{agentCode,jdbcType=VARCHAR},
      AgentGroup = #{agentGroup,jdbcType=VARCHAR},
      ManageCom = #{manageCom,jdbcType=VARCHAR},
      Password = #{password,jdbcType=VARCHAR},
      EntryNo = #{entryNo,jdbcType=VARCHAR},
      Name = #{name,jdbcType=VARCHAR},
      Sex = #{sex,jdbcType=CHAR},
      Birthday = #{birthday,jdbcType=DATE},
      NativePlace = #{nativePlace,jdbcType=VARCHAR},
      Nationality = #{nationality,jdbcType=VARCHAR},
      Marriage = #{marriage,jdbcType=CHAR},
      CreditGrade = #{creditGrade,jdbcType=VARCHAR},
      HomeAddressCode = #{homeAddressCode,jdbcType=VARCHAR},
      HomeAddress = #{homeAddress,jdbcType=VARCHAR},
      PostalAddress = #{postalAddress,jdbcType=VARCHAR},
      ZipCode = #{zipCode,jdbcType=VARCHAR},
      Phone = #{phone,jdbcType=VARCHAR},
      BP = #{BP,jdbcType=VARCHAR},
      Mobile = #{mobile,jdbcType=VARCHAR},
      EMail = #{EMail,jdbcType=VARCHAR},
      MarriageDate = #{marriageDate,jdbcType=TIMESTAMP},
      IDNo = #{IDNo,jdbcType=VARCHAR},
      Source = #{source,jdbcType=VARCHAR},
      BloodType = #{bloodType,jdbcType=VARCHAR},
      PolityVisage = #{polityVisage,jdbcType=VARCHAR},
      Degree = #{degree,jdbcType=CHAR},
      GraduateSchool = #{graduateSchool,jdbcType=VARCHAR},
      Speciality = #{speciality,jdbcType=VARCHAR},
      PostTitle = #{postTitle,jdbcType=VARCHAR},
      ForeignLevel = #{foreignLevel,jdbcType=VARCHAR},
      WorkAge = #{workAge,jdbcType=SMALLINT},
      OldCom = #{oldCom,jdbcType=VARCHAR},
      OldOccupation = #{oldOccupation,jdbcType=VARCHAR},
      HeadShip = #{headShip,jdbcType=VARCHAR},
      RecommendAgent = #{recommendAgent,jdbcType=VARCHAR},
      Business = #{business,jdbcType=VARCHAR},
      SaleQuaf = #{saleQuaf,jdbcType=CHAR},
      QuafNo = #{quafNo,jdbcType=VARCHAR},
      QuafStartDate = #{quafStartDate,jdbcType=TIMESTAMP},
      QuafEndDate = #{quafEndDate,jdbcType=TIMESTAMP},
      DevNo1 = #{devNo1,jdbcType=VARCHAR},
      DevNo2 = #{devNo2,jdbcType=VARCHAR},
      RetainContNo = #{retainContNo,jdbcType=VARCHAR},
      AgentKind = #{agentKind,jdbcType=VARCHAR},
      DevGrade = #{devGrade,jdbcType=VARCHAR},
      InsideFlag = #{insideFlag,jdbcType=CHAR},
      FullTimeFlag = #{fullTimeFlag,jdbcType=CHAR},
      NoWorkFlag = #{noWorkFlag,jdbcType=CHAR},
      TrainDate = #{trainDate,jdbcType=TIMESTAMP},
      EmployDate = #{employDate,jdbcType=TIMESTAMP},
      InDueFormDate = #{inDueFormDate,jdbcType=TIMESTAMP},
      OutWorkDate = #{outWorkDate,jdbcType=TIMESTAMP},
      RecommendNo = #{recommendNo,jdbcType=VARCHAR},
      CautionerName = #{cautionerName,jdbcType=VARCHAR},
      CautionerSex = #{cautionerSex,jdbcType=VARCHAR},
      CautionerID = #{cautionerID,jdbcType=VARCHAR},
      CautionerBirthday = #{cautionerBirthday,jdbcType=TIMESTAMP},
      Approver = #{approver,jdbcType=VARCHAR},
      ApproveDate = #{approveDate,jdbcType=TIMESTAMP},
      AssuMoney = #{assuMoney,jdbcType=DECIMAL},
      Remark = #{remark,jdbcType=VARCHAR},
      AgentState = #{agentState,jdbcType=VARCHAR},
      QualiPassFlag = #{qualiPassFlag,jdbcType=CHAR},
      SmokeFlag = #{smokeFlag,jdbcType=CHAR},
      RgtAddress = #{rgtAddress,jdbcType=VARCHAR},
      BankCode = #{bankCode,jdbcType=VARCHAR},
      BankAccNo = #{bankAccNo,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=TIMESTAMP},
      MakeTime = #{makeTime,jdbcType=CHAR},
      ModifyDate = #{modifyDate,jdbcType=TIMESTAMP},
      ModifyTime = #{modifyTime,jdbcType=CHAR},
      BranchType = #{branchType,jdbcType=VARCHAR},
      TrainPeriods = #{trainPeriods,jdbcType=VARCHAR},
      BranchCode = #{branchCode,jdbcType=VARCHAR},
      Age = #{age,jdbcType=INTEGER},
      ChannelName = #{channelName,jdbcType=VARCHAR},
      IDType = #{IDType,jdbcType=VARCHAR},
      BranchType2 = #{branchType2,jdbcType=VARCHAR},
      ReceiptNo = #{receiptNo,jdbcType=VARCHAR},
      QualifNo = #{qualifNo,jdbcType=VARCHAR},
      Honor = #{honor,jdbcType=VARCHAR},
      IsFlag = #{isFlag,jdbcType=CHAR},
      IsFlag1 = #{isFlag1,jdbcType=CHAR},
      IsFlag2 = #{isFlag2,jdbcType=CHAR}
    where SerialNo = #{serialNo,jdbcType=VARCHAR}
  </update>



  <update id="updateAgentInfo" parameterType="java.util.List">
    <foreach collection="list" item="it" index="index" separator=";">
      update fdagentinfo
      set AgentCode = #{it.agentCode,jdbcType=VARCHAR},
      AgentGroup = #{it.agentGroup,jdbcType=VARCHAR},
      ManageCom = #{it.manageCom,jdbcType=VARCHAR},
      Password = #{it.password,jdbcType=VARCHAR},
      EntryNo = #{it.entryNo,jdbcType=VARCHAR},
      Name = #{it.name,jdbcType=VARCHAR},
      Sex = #{it.sex,jdbcType=CHAR},
      Birthday = #{it.birthday,jdbcType=DATE},
      NativePlace = #{it.nativePlace,jdbcType=VARCHAR},
      Nationality = #{it.nationality,jdbcType=VARCHAR},
      Marriage = #{it.marriage,jdbcType=CHAR},
      CreditGrade = #{it.creditGrade,jdbcType=VARCHAR},
      HomeAddressCode = #{it.homeAddressCode,jdbcType=VARCHAR},
      HomeAddress = #{it.homeAddress,jdbcType=VARCHAR},
      PostalAddress = #{it.postalAddress,jdbcType=VARCHAR},
      ZipCode = #{it.zipCode,jdbcType=VARCHAR},
      Phone = #{it.phone,jdbcType=VARCHAR},
      BP = #{it.BP,jdbcType=VARCHAR},
      Mobile = #{it.mobile,jdbcType=VARCHAR},
      EMail = #{it.EMail,jdbcType=VARCHAR},
      MarriageDate = #{it.marriageDate,jdbcType=TIMESTAMP},
      IDNo = #{it.IDNo,jdbcType=VARCHAR},
      Source = #{it.source,jdbcType=VARCHAR},
      BloodType = #{it.bloodType,jdbcType=VARCHAR},
      PolityVisage = #{it.polityVisage,jdbcType=VARCHAR},
      Degree = #{it.degree,jdbcType=CHAR},
      GraduateSchool = #{it.graduateSchool,jdbcType=VARCHAR},
      Speciality = #{it.speciality,jdbcType=VARCHAR},
      PostTitle = #{it.postTitle,jdbcType=VARCHAR},
      ForeignLevel = #{it.foreignLevel,jdbcType=VARCHAR},
      WorkAge = #{it.workAge,jdbcType=SMALLINT},
      OldCom = #{it.oldCom,jdbcType=VARCHAR},
      OldOccupation = #{it.oldOccupation,jdbcType=VARCHAR},
      HeadShip = #{it.headShip,jdbcType=VARCHAR},
      RecommendAgent = #{it.recommendAgent,jdbcType=VARCHAR},
      Business = #{it.business,jdbcType=VARCHAR},
      SaleQuaf = #{it.saleQuaf,jdbcType=CHAR},
      QuafNo = #{it.quafNo,jdbcType=VARCHAR},
      QuafStartDate = #{it.quafStartDate,jdbcType=TIMESTAMP},
      QuafEndDate = #{it.quafEndDate,jdbcType=TIMESTAMP},
      DevNo1 = #{it.devNo1,jdbcType=VARCHAR},
      DevNo2 = #{it.devNo2,jdbcType=VARCHAR},
      RetainContNo = #{it.retainContNo,jdbcType=VARCHAR},
      AgentKind = #{it.agentKind,jdbcType=VARCHAR},
      DevGrade = #{it.devGrade,jdbcType=VARCHAR},
      InsideFlag = #{it.insideFlag,jdbcType=CHAR},
      FullTimeFlag = #{it.fullTimeFlag,jdbcType=CHAR},
      NoWorkFlag = #{it.noWorkFlag,jdbcType=CHAR},
      TrainDate = #{it.trainDate,jdbcType=TIMESTAMP},
      EmployDate = #{it.employDate,jdbcType=TIMESTAMP},
      InDueFormDate = #{it.inDueFormDate,jdbcType=TIMESTAMP},
      OutWorkDate = #{it.outWorkDate,jdbcType=TIMESTAMP},
      RecommendNo = #{it.recommendNo,jdbcType=VARCHAR},
      CautionerName = #{it.cautionerName,jdbcType=VARCHAR},
      CautionerSex = #{it.cautionerSex,jdbcType=VARCHAR},
      CautionerID = #{it.cautionerID,jdbcType=VARCHAR},
      CautionerBirthday = #{it.cautionerBirthday,jdbcType=TIMESTAMP},
      Approver = #{it.approver,jdbcType=VARCHAR},
      ApproveDate = #{it.approveDate,jdbcType=TIMESTAMP},
      AssuMoney = #{it.assuMoney,jdbcType=DECIMAL},
      Remark = #{it.remark,jdbcType=VARCHAR},
      AgentState = #{it.agentState,jdbcType=VARCHAR},
      QualiPassFlag = #{it.qualiPassFlag,jdbcType=CHAR},
      SmokeFlag = #{it.smokeFlag,jdbcType=CHAR},
      RgtAddress = #{it.rgtAddress,jdbcType=VARCHAR},
      BankCode = #{it.bankCode,jdbcType=VARCHAR},
      BankAccNo = #{it.bankAccNo,jdbcType=VARCHAR},
      Operator = #{it.operator,jdbcType=VARCHAR},
      MakeDate = #{it.makeDate,jdbcType=TIMESTAMP},
      MakeTime = #{it.makeTime,jdbcType=CHAR},
      ModifyDate = #{it.modifyDate,jdbcType=TIMESTAMP},
      ModifyTime = #{it.modifyTime,jdbcType=CHAR},
      BranchType = #{it.branchType,jdbcType=VARCHAR},
      TrainPeriods = #{it.trainPeriods,jdbcType=VARCHAR},
      BranchCode = #{it.branchCode,jdbcType=VARCHAR},
      Age = #{it.age,jdbcType=INTEGER},
      ChannelName = #{it.channelName,jdbcType=VARCHAR},
      IDType = #{it.IDType,jdbcType=VARCHAR},
      BranchType2 = #{it.branchType2,jdbcType=VARCHAR},
      ReceiptNo = #{it.receiptNo,jdbcType=VARCHAR},
      QualifNo = #{it.qualifNo,jdbcType=VARCHAR},
      Honor = #{it.honor,jdbcType=VARCHAR},
      IsFlag = #{it.isFlag,jdbcType=CHAR},
      IsFlag1 = #{it.isFlag1,jdbcType=CHAR},
      IsFlag2 = #{it.isFlag2,jdbcType=CHAR}
      where AgentCode = #{it.agentCode,jdbcType=VARCHAR}
    </foreach>
  </update>
  <update id="updateByIdNo" parameterType="java.util.Map">
    update  fdagentinfo set Phone=#{mobilePhone}    , Mobile=#{mobilePhone}  where IDNo=#{idNo}
  </update>
  <insert id="insertAgentInfo">
    insert into fdagentinfo (SerialNo, AgentCode, AgentGroup,
    ManageCom, Password, EntryNo,
    Name, Sex, Birthday, NativePlace,
    Nationality, Marriage, CreditGrade,
    HomeAddressCode, HomeAddress, PostalAddress,
    ZipCode, Phone, BP,
    Mobile, EMail, MarriageDate,
    IDNo, Source, BloodType,
    PolityVisage, Degree, GraduateSchool,
    Speciality, PostTitle, ForeignLevel,
    WorkAge, OldCom, OldOccupation,
    HeadShip, RecommendAgent, Business,
    SaleQuaf, QuafNo, QuafStartDate,
    QuafEndDate, DevNo1, DevNo2,
    RetainContNo, AgentKind, DevGrade,
    InsideFlag, FullTimeFlag, NoWorkFlag,
    TrainDate, EmployDate, InDueFormDate,
    OutWorkDate, RecommendNo, CautionerName,
    CautionerSex, CautionerID, CautionerBirthday,
    Approver, ApproveDate, AssuMoney,
    Remark, AgentState, QualiPassFlag,
    SmokeFlag, RgtAddress, BankCode,
    BankAccNo, Operator, MakeDate,
    MakeTime, ModifyDate, ModifyTime,
    BranchType, TrainPeriods, BranchCode,
    Age, ChannelName, IDType,
    BranchType2, ReceiptNo, QualifNo,
    Honor, IsFlag, IsFlag1, IsFlag2)
    values
    <foreach collection="list" item="it" index="index" separator=",">
      (#{it.serialNo,jdbcType=VARCHAR}, #{it.agentCode,jdbcType=VARCHAR}, #{it.agentGroup,jdbcType=VARCHAR},
      #{it.manageCom,jdbcType=VARCHAR}, #{it.password,jdbcType=VARCHAR}, #{it.entryNo,jdbcType=VARCHAR},
      #{it.name,jdbcType=VARCHAR}, #{it.sex,jdbcType=CHAR}, #{it.birthday,jdbcType=DATE}, #{it.nativePlace,jdbcType=VARCHAR},
      #{it.nationality,jdbcType=VARCHAR}, #{it.marriage,jdbcType=CHAR}, #{it.creditGrade,jdbcType=VARCHAR},
      #{it.homeAddressCode,jdbcType=VARCHAR}, #{it.homeAddress,jdbcType=VARCHAR}, #{it.postalAddress,jdbcType=VARCHAR},
      #{it.zipCode,jdbcType=VARCHAR}, #{it.phone,jdbcType=VARCHAR}, #{it.BP,jdbcType=VARCHAR},
      #{it.mobile,jdbcType=VARCHAR}, #{it.EMail,jdbcType=VARCHAR}, #{it.marriageDate,jdbcType=TIMESTAMP},
      #{it.IDNo,jdbcType=VARCHAR}, #{it.source,jdbcType=VARCHAR}, #{it.bloodType,jdbcType=VARCHAR},
      #{it.polityVisage,jdbcType=VARCHAR}, #{it.degree,jdbcType=CHAR}, #{it.graduateSchool,jdbcType=VARCHAR},
      #{it.speciality,jdbcType=VARCHAR}, #{it.postTitle,jdbcType=VARCHAR}, #{it.foreignLevel,jdbcType=VARCHAR},
      #{it.workAge,jdbcType=SMALLINT}, #{it.oldCom,jdbcType=VARCHAR}, #{it.oldOccupation,jdbcType=VARCHAR},
      #{it.headShip,jdbcType=VARCHAR}, #{it.recommendAgent,jdbcType=VARCHAR}, #{it.business,jdbcType=VARCHAR},
      #{it.saleQuaf,jdbcType=CHAR}, #{it.quafNo,jdbcType=VARCHAR}, #{it.quafStartDate,jdbcType=TIMESTAMP},
      #{it.quafEndDate,jdbcType=TIMESTAMP}, #{it.devNo1,jdbcType=VARCHAR}, #{it.devNo2,jdbcType=VARCHAR},
      #{it.retainContNo,jdbcType=VARCHAR}, #{it.agentKind,jdbcType=VARCHAR}, #{it.devGrade,jdbcType=VARCHAR},
      #{it.insideFlag,jdbcType=CHAR}, #{it.fullTimeFlag,jdbcType=CHAR}, #{it.noWorkFlag,jdbcType=CHAR},
      #{it.trainDate,jdbcType=TIMESTAMP}, #{it.employDate,jdbcType=TIMESTAMP}, #{it.inDueFormDate,jdbcType=TIMESTAMP},
      #{it.outWorkDate,jdbcType=TIMESTAMP}, #{it.recommendNo,jdbcType=VARCHAR}, #{it.cautionerName,jdbcType=VARCHAR},
      #{it.cautionerSex,jdbcType=VARCHAR}, #{it.cautionerID,jdbcType=VARCHAR}, #{it.cautionerBirthday,jdbcType=TIMESTAMP},
      #{it.approver,jdbcType=VARCHAR}, #{it.approveDate,jdbcType=TIMESTAMP}, #{it.assuMoney,jdbcType=DECIMAL},
      #{it.remark,jdbcType=VARCHAR}, #{it.agentState,jdbcType=VARCHAR}, #{it.qualiPassFlag,jdbcType=CHAR},
      #{it.smokeFlag,jdbcType=CHAR}, #{it.rgtAddress,jdbcType=VARCHAR}, #{it.bankCode,jdbcType=VARCHAR},
      #{it.bankAccNo,jdbcType=VARCHAR}, #{it.operator,jdbcType=VARCHAR}, #{it.makeDate,jdbcType=TIMESTAMP},
      #{it.makeTime,jdbcType=CHAR}, #{it.modifyDate,jdbcType=TIMESTAMP}, #{it.modifyTime,jdbcType=CHAR},
      #{it.branchType,jdbcType=VARCHAR}, #{it.trainPeriods,jdbcType=VARCHAR}, #{it.branchCode,jdbcType=VARCHAR},
      #{it.age,jdbcType=INTEGER}, #{it.channelName,jdbcType=VARCHAR}, #{it.IDType,jdbcType=VARCHAR},
      #{it.branchType2,jdbcType=VARCHAR}, #{it.receiptNo,jdbcType=VARCHAR}, #{it.qualifNo,jdbcType=VARCHAR},
      #{it.honor,jdbcType=VARCHAR}, #{it.isFlag,jdbcType=CHAR}, #{it.isFlag1,jdbcType=CHAR}, #{it.isFlag2,jdbcType=CHAR}
      )
    </foreach>
  </insert>
  <select id="selectAgentCode" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FDAgentInfo">
    select <include refid="Base_Column_List" />
    from  fdagentinfo
    where AgentCode = #{agentCode,jdbcType=VARCHAR}
  </select>

  <select id="getAgentByEnsureCode" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FDAgentInfo">
    SELECT `name`,AgentCode,Phone,ManageCom,AgentGroup,Mobile FROM fdagentinfo WHERE agentcode = (SELECT ClientNo FROM fcensure WHERE ensureCode = #{EnsureCode})
  </select>
    <select id="selectByIdNo" resultType="com.sinosoft.eflex.model.FDAgentInfo">
      select  * from fdagentinfo where IDNo=#{idNo}
    </select>
</mapper>