<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCEdorACInfoMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCEdorACInfo">
    <id column="EdorAppNo" jdbcType="VARCHAR" property="edorAppNo" />
    <result column="GrpContNo" jdbcType="VARCHAR" property="grpContNo" />
    <result column="GrpNo" jdbcType="VARCHAR" property="grpNo"/>
    <result column="GrpName" jdbcType="VARCHAR" property="grpName" />
    <result column="Corporation" jdbcType="VARCHAR" property="corporation" />
    <result column="GetFlag" jdbcType="VARCHAR" property="getFlag" />
    <result column="BankCode" jdbcType="VARCHAR" property="bankCode" />
    <result column="BankAccNo" jdbcType="VARCHAR" property="bankAccNo" />
    <result column="GrpAddress" jdbcType="VARCHAR" property="grpAddress" />
    <result column="ZipCode" jdbcType="VARCHAR" property="zipCode" />
    <result column="Email" jdbcType="VARCHAR" property="email" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    EdorAppNo, GrpContNo,GrpNo, GrpName, Corporation, GetFlag, BankCode, BankAccNo, GrpAddress,
    ZipCode, Email, MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcedoracinfo
    where EdorAppNo = #{edorAppNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcedoracinfo
    where EdorAppNo = #{edorAppNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCEdorACInfo">
    insert into fcedoracinfo (EdorAppNo, GrpContNo, GrpNo,GrpName,
      Corporation, GetFlag, BankCode, 
      BankAccNo, GrpAddress, ZipCode, 
      Email, MakeDate, MakeTime, 
      ModifyDate, ModifyTime)
    values (#{edorAppNo,jdbcType=VARCHAR}, #{grpContNo,jdbcType=VARCHAR},#{grpNo,jdbcType=VARCHAR}, #{grpName,jdbcType=VARCHAR},
      #{corporation,jdbcType=VARCHAR}, #{getFlag,jdbcType=VARCHAR}, #{bankCode,jdbcType=VARCHAR}, 
      #{bankAccNo,jdbcType=VARCHAR}, #{grpAddress,jdbcType=VARCHAR}, #{zipCode,jdbcType=VARCHAR}, 
      #{email,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, 
      #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCEdorACInfo">
    insert into fcedoracinfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="edorAppNo != null">
        EdorAppNo,
      </if>
      <if test="grpContNo != null">
        GrpContNo,
      </if>
      <if test="grpNo != null">
        GrpNo,
      </if>
      <if test="grpName != null">
        GrpName,
      </if>
      <if test="corporation != null">
        Corporation,
      </if>
      <if test="getFlag != null">
        GetFlag,
      </if>
      <if test="bankCode != null">
        BankCode,
      </if>
      <if test="bankAccNo != null">
        BankAccNo,
      </if>
      <if test="grpAddress != null">
        GrpAddress,
      </if>
      <if test="zipCode != null">
        ZipCode,
      </if>
      <if test="email != null">
        Email,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="edorAppNo != null">
        #{edorAppNo,jdbcType=VARCHAR},
      </if>
      <if test="grpContNo != null">
        #{grpContNo,jdbcType=VARCHAR},
      </if>
      <if test="grpNo != null">
        #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="grpName != null">
        #{grpName,jdbcType=VARCHAR},
      </if>
      <if test="corporation != null">
        #{corporation,jdbcType=VARCHAR},
      </if>
      <if test="getFlag != null">
        #{getFlag,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null">
        #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="bankAccNo != null">
        #{bankAccNo,jdbcType=VARCHAR},
      </if>
      <if test="grpAddress != null">
        #{grpAddress,jdbcType=VARCHAR},
      </if>
      <if test="zipCode != null">
        #{zipCode,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCEdorACInfo">
    update fcedoracinfo
    <set>
      <if test="grpContNo != null">
        GrpContNo = #{grpContNo,jdbcType=VARCHAR},
      </if>
      <if test="grpNo != null">
        GrpNo = #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="grpName != null">
        GrpName = #{grpName,jdbcType=VARCHAR},
      </if>
      <if test="corporation != null">
        Corporation = #{corporation,jdbcType=VARCHAR},
      </if>
      <if test="getFlag != null">
        GetFlag = #{getFlag,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null">
        BankCode = #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="bankAccNo != null">
        BankAccNo = #{bankAccNo,jdbcType=VARCHAR},
      </if>
      <if test="grpAddress != null">
        GrpAddress = #{grpAddress,jdbcType=VARCHAR},
      </if>
      <if test="zipCode != null">
        ZipCode = #{zipCode,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        Email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where EdorAppNo = #{edorAppNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCEdorACInfo">
    update fcedoracinfo
    set GrpContNo = #{grpContNo,jdbcType=VARCHAR},
      GrpNo = #{grpNo,jdbcType=VARCHAR},
      GrpName = #{grpName,jdbcType=VARCHAR},
      Corporation = #{corporation,jdbcType=VARCHAR},
      GetFlag = #{getFlag,jdbcType=VARCHAR},
      BankCode = #{bankCode,jdbcType=VARCHAR},
      BankAccNo = #{bankAccNo,jdbcType=VARCHAR},
      GrpAddress = #{grpAddress,jdbcType=VARCHAR},
      ZipCode = #{zipCode,jdbcType=VARCHAR},
      Email = #{email,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where EdorAppNo = #{edorAppNo,jdbcType=VARCHAR}
  </update>
</mapper>