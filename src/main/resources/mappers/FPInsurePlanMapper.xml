<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FPInsurePlanMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FPInsurePlan">
        <id column="InsurePlanNo" jdbcType="VARCHAR" property="insurePlanNo"/>
        <result column="EnsureCode" jdbcType="VARCHAR" property="ensureCode"/>
        <result column="AppntYear" jdbcType="VARCHAR" property="appntYear"/>
        <result column="PlanCode" jdbcType="DATE" property="planCode"/>
        <result column="InsureState" jdbcType="VARCHAR" property="insureState"/>
        <result column="Perno" jdbcType="VARCHAR" property="perno"/>
        <result column="personId" jdbcType="VARCHAR" property="personId"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    InsurePlanNo, EnsureCode, AppntYear, PlanCode, InsureState, Perno, personId, Operator, 
    OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fpinsureplan
        where InsurePlanNo = #{insurePlanNo,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fpinsureplan
    where InsurePlanNo = #{insurePlanNo,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FPInsurePlan">
    insert into fpinsureplan (InsurePlanNo, EnsureCode, AppntYear, 
      PlanCode, InsureState, Perno, 
      personId, Operator, OperatorCom, 
      MakeDate, MakeTime, ModifyDate, 
      ModifyTime)
    values (#{insurePlanNo,jdbcType=VARCHAR}, #{ensureCode,jdbcType=VARCHAR}, #{appntYear,jdbcType=VARCHAR}, 
      #{planCode,jdbcType=DATE}, #{insureState,jdbcType=VARCHAR}, #{perno,jdbcType=VARCHAR}, 
      #{personId,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, 
      #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, 
      #{modifyTime,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FPInsurePlan">
        insert into fpinsureplan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="insurePlanNo != null">
                InsurePlanNo,
            </if>
            <if test="ensureCode != null">
                EnsureCode,
            </if>
            <if test="appntYear != null">
                AppntYear,
            </if>
            <if test="planCode != null">
                PlanCode,
            </if>
            <if test="insureState != null">
                InsureState,
            </if>
            <if test="perno != null">
                Perno,
            </if>
            <if test="personId != null">
                personId,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="insurePlanNo != null">
                #{insurePlanNo,jdbcType=VARCHAR},
            </if>
            <if test="ensureCode != null">
                #{ensureCode,jdbcType=VARCHAR},
            </if>
            <if test="appntYear != null">
                #{appntYear,jdbcType=VARCHAR},
            </if>
            <if test="planCode != null">
                #{planCode,jdbcType=DATE},
            </if>
            <if test="insureState != null">
                #{insureState,jdbcType=VARCHAR},
            </if>
            <if test="perno != null">
                #{perno,jdbcType=VARCHAR},
            </if>
            <if test="personId != null">
                #{personId,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>


    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FPInsurePlan">
        update fpinsureplan
        <set>
            <if test="ensureCode != null">
                EnsureCode = #{ensureCode,jdbcType=VARCHAR},
            </if>
            <if test="appntYear != null">
                AppntYear = #{appntYear,jdbcType=VARCHAR},
            </if>
            <if test="planCode != null">
                PlanCode = #{planCode,jdbcType=DATE},
            </if>
            <if test="insureState != null">
                InsureState = #{insureState,jdbcType=VARCHAR},
            </if>
            <if test="perno != null">
                Perno = #{perno,jdbcType=VARCHAR},
            </if>
            <if test="personId != null">
                personId = #{personId,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where InsurePlanNo = #{insurePlanNo,jdbcType=VARCHAR}
    </update>




    <select id="selectFPInsurePlanPerNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fpinsureplan
        where personId = #{personId,jdbcType=VARCHAR}
    </select>


    <select id="selectEnsureCodeByPersonId" parameterType="java.util.Map"
            resultType="com.sinosoft.eflex.model.FPInsurePlan">
        select
        <include refid="Base_Column_List"/>
        from fpinsureplan
        <where>
            <if test="ensureCode != null and ensureCode != '' ">
                EnsureCode = #{ensureCode,jdbcType=VARCHAR}
            </if>
            <if test="personId != null and personId != '' ">
                and personId = #{personId,jdbcType=VARCHAR}
            </if>
            <if test="perNo != null and perNo != '' ">
                and perNo = #{perNo,jdbcType=VARCHAR}
            </if>
        </where>
    </select>



    <select id="selectPlanList" parameterType="java.util.HashMap" resultType="java.lang.String">
        select
        DISTINCT PlanCode
        from fpinsureplan
        where 1=1
        <if test="ensureCode != null and ensureCode != '' ">
            and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        </if>
        <if test="appntYear != null and appntYear!= '' ">
            and AppntYear = #{appntYear,jdbcType=VARCHAR}
        </if>
        <if test="planCode != null">
            and PlanCode = #{planCode,jdbcType=DATE}
        </if>
        <if test="insureState != null">
            and InsureState = #{insureState,jdbcType=VARCHAR}
        </if>
        <if test="perno != null and perno != '' ">
            and Perno = #{perno,jdbcType=VARCHAR}
        </if>
        <if test="personId != null and personId != '' ">
            and personId = #{personId,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectPlanCodeByPersonId" parameterType="java.util.HashMap" resultType="java.util.Map">
   select a.PersonId,c.ProductCode PlanCode,e.EnsureCode
	from fcorderinsured a LEFT JOIN fcorderitem b on a.OrderItemNo = b.OrderItemNo 
	LEFT JOIN fcorderitemdetail c ON b.OrderItemDetailNo = c.OrderItemDetailNo
	 LEFT JOIN fcorder d on d.OrderNo = b.OrderNo 
	LEFT JOIN fcgrporder e on d.GrpOrderNo = e.GrpOrderNo 
	where  e.GrpOrderStatus = '04' and a.IDNo in (select idno from fcperson where personid=#{personId,jdbcType=VARCHAR})
  </select>
    <select id="getPlanCodeByPersonId" parameterType="java.util.HashMap" resultType="java.util.Map">
   select a.PersonId,c.ProductCode PlanCode,f.EnsureCode
	from fcorderinsured a LEFT JOIN fcorderitem b on a.OrderItemNo = b.OrderItemNo
	LEFT JOIN fcorderitemdetail c ON b.OrderItemDetailNo = c.OrderItemDetailNo
	 LEFT JOIN fcorder d on d.OrderNo = b.OrderNo
	LEFT JOIN fcgrporder e on d.GrpOrderNo = e.GrpOrderNo
	left join fcensure f on e.ensureCode = f.ensureCode
	where  e.GrpOrderStatus = '04' and a.IDNo in (select idno from fcperson where personid=#{personId,jdbcType=VARCHAR})
	and f.planType = '0' order by f.policyEndDate desc
  </select>

    <select id="selectStaffInsured" parameterType="java.lang.String" resultType="java.lang.Integer">
  	select  count(*)  from fpinsureplan where perno = #{perNo}
  	and  personid=(select personid from  fcstafffamilyrela where perno=#{perNo} and relation='0')
  </select>
    <select id="selectStaffFamilyInsured" resultType="java.lang.Integer">
        select count(*)
        from fpinsureplan where perno = #{perNo} and personid = #{personId}
    </select>



    <insert id="insertSubtables">
        insert into fpinsureplan (InsurePlanNo, EnsureCode, AppntYear,
                                      PlanCode, InsureState, Perno,
                                      personId, Operator, OperatorCom,
                                      MakeDate, MakeTime, ModifyDate,
                                      ModifyTime,IsCommit)
        values (#{insurePlanNo,jdbcType=VARCHAR}, #{ensureCode,jdbcType=VARCHAR}, #{appntYear,jdbcType=VARCHAR},
                #{planCode,jdbcType=DATE}, #{insureState,jdbcType=VARCHAR}, #{perno,jdbcType=VARCHAR},
                #{personId,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR},
                #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE},
                #{modifyTime,jdbcType=VARCHAR},#{isCommit,jdbcType=TINYINT})
    </insert>


    <select id="selectFPInsurePlans" parameterType="java.util.HashMap"
            resultType="com.sinosoft.eflex.model.FPInsurePlan">
        select
        <include refid="Base_Column_List"/>
        from fpinsureplan
        where 1=1
        <if test="ensureCode != null and ensureCode != '' ">
            and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        </if>
        <if test="appntYear != null and appntYear!= '' ">
            and AppntYear = #{appntYear,jdbcType=VARCHAR}
        </if>
        <if test="planCode != null">
            and PlanCode = #{planCode,jdbcType=DATE}
        </if>
        <if test="insureState != null">
            and InsureState = #{insureState,jdbcType=VARCHAR}
        </if>
        <if test="perNo != null and perNo != '' ">
            and Perno = #{perNo,jdbcType=VARCHAR}
        </if>
        <if test="personId != null and personId != '' ">
            and personId = #{personId,jdbcType=VARCHAR}
        </if>
        order by InsurePlanNo
    </select>


    <select id="selectFlansInfo" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FPInsurePlan">
        select
        <include refid="Base_Column_List"/>
        from fpinsureplan
        where
        <if test="perNo != null and perNo != '' ">
            Perno = #{perNo,jdbcType=VARCHAR}
        </if>
        <if test="ensureCode != null and ensureCode != '' ">
            and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectFPInsurePlansByState" resultType="com.sinosoft.eflex.model.FPInsurePlan">
        select
        <include refid="Base_Column_List"/>
        from fpinsureplan
        where 1=1
        <if test="ensureCode != null and ensureCode != '' ">
            and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        </if>
        <if test="appntYear != null and appntYear!= '' ">
            and AppntYear = #{appntYear,jdbcType=VARCHAR}
        </if>
        <if test="planCode != null">
            and PlanCode = #{planCode,jdbcType=DATE}
        </if>
        <if test="insureState != null">
            and InsureState = #{insureState,jdbcType=VARCHAR}
        </if>
        <if test="perNo != null and perNo != '' ">
            and Perno = #{perNo,jdbcType=VARCHAR}
        </if>
        <if test="personId != null and personId != '' ">
            and personId = #{personId,jdbcType=VARCHAR}
        </if>
        and  InsureState ='2'
        order by InsurePlanNo
    </select>


    <delete id="deleteInsurePlan" parameterType="com.sinosoft.eflex.model.FPInsurePlan">
        delete
        from fpinsureplan
        where ensureCode = #{ensureCode}
          and perno = #{perno}
    </delete>


    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FPInsurePlan">
        update fpinsureplan
        set EnsureCode = #{ensureCode,jdbcType=VARCHAR},
            AppntYear = #{appntYear,jdbcType=VARCHAR},
            PlanCode = #{planCode,jdbcType=DATE},
            InsureState = #{insureState,jdbcType=VARCHAR},
            Perno = #{perno,jdbcType=VARCHAR},
            personId = #{personId,jdbcType=VARCHAR},
            Operator = #{operator,jdbcType=VARCHAR},
            OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            MakeDate = #{makeDate,jdbcType=DATE},
            MakeTime = #{makeTime,jdbcType=VARCHAR},
            ModifyDate = #{modifyDate,jdbcType=DATE},
            ModifyTime = #{modifyTime,jdbcType=VARCHAR}
        where InsurePlanNo = #{insurePlanNo,jdbcType=VARCHAR}
    </update>


    <select id="selectByEnsureCodeAndPersonId" resultType="com.sinosoft.eflex.model.FPInsurePlan">
        select * from fpinsureplan where personId =#{personID]} and EnsureCode=#{ensureCode}
    </select>
</mapper>
