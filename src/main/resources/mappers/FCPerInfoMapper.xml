<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCPerInfoMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCPerInfo">
        <id column="PerNo" jdbcType="VARCHAR" property="perNo"/>
        <result column="CPerNo" jdbcType="VARCHAR" property="CPerNo"/>
        <result column="GrpNo" jdbcType="VARCHAR" property="grpNo"/>
        <result column="StaffNo" jdbcType="VARCHAR" property="staffNo"/>
        <result column="LevelCode" jdbcType="VARCHAR" property="levelCode"/>
        <result column="department" jdbcType="VARCHAR" property="department"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Sex" jdbcType="VARCHAR" property="sex"/>
        <result column="Nativeplace" jdbcType="VARCHAR" property="nativeplace"/>
        <result column="ServiceTerm" jdbcType="VARCHAR" property="serviceTerm"/>
        <result column="Retirement" jdbcType="VARCHAR" property="retirement"/>
        <result column="IDType" jdbcType="VARCHAR" property="IDType"/>
        <result column="IDNo" jdbcType="VARCHAR" property="IDNo"/>
        <result column="IdTypeEndDate" jdbcType="DATE" property="idTypeEndDate"/>
        <result column="BirthDay" jdbcType="DATE" property="birthDay"/>
        <result column="Phone" jdbcType="VARCHAR" property="phone"/>
        <result column="MobilePhone" jdbcType="VARCHAR" property="mobilePhone"/>
        <result column="OccupationType" jdbcType="VARCHAR" property="occupationType"/>
        <result column="OccupationCode" jdbcType="VARCHAR" property="occupationCode"/>
        <result column="JoinMedProtect" jdbcType="VARCHAR" property="joinMedProtect"/>
        <result column="MedProtectType" jdbcType="VARCHAR" property="medProtectType"/>
        <result column="Email" jdbcType="VARCHAR" property="email"/>
        <result column="ZipCode" jdbcType="VARCHAR" property="zipCode"/>
        <result column="Address" jdbcType="VARCHAR" property="address"/>
        <result column="DefaultPlan" jdbcType="VARCHAR" property="defaultPlan"/>
        <result column="OpenBank" jdbcType="VARCHAR" property="openBank"/>
        <result column="OpenAccount" jdbcType="VARCHAR" property="openAccount"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="Province" jdbcType="VARCHAR" property="province"/>
        <result column="City" jdbcType="VARCHAR" property="city"/>
        <result column="County" jdbcType="VARCHAR" property="county"/>
        <result column="DetaileAddress" jdbcType="VARCHAR" property="detaileAddress"/>
        <result column="IdImage1" jdbcType="VARCHAR" property="idImage1"/>
        <result column="IdImage2" jdbcType="VARCHAR" property="idImage2"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        PerNo
        , CPerNo, GrpNo, StaffNo, LevelCode, department, Name,
		Sex, nativeplace,ServiceTerm,Retirement,IDType,
		IDNo, idTypeEndDate,BirthDay,
		Phone, MobilePhone, OccupationType,
		OccupationCode, JoinMedProtect,
		MedProtectType,
		Email, ZipCode, Address,
		DefaultPlan, OpenBank, OpenAccount, OperatorCom,
		Operator,Province,City,County,DetaileAddress,
		MakeDate,
		MakeTime, ModifyDate, ModifyTime,
		IdImage1, IdImage2
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcperinfo
        where PerNo = #{perNo,jdbcType=VARCHAR}
    </select>
    <select id="getSignBankInfo" parameterType="java.lang.String"
            resultType="java.util.Map">
        select Name,
               PayBankCode,
               BankAccount
        from fcbatchpaybankinfo
        where PerNo = #{perNo,jdbcType=VARCHAR}
          and ensureCode = #{ensureCode,jdbcType=VARCHAR}
          and issidned = 'Y'
    </select>
    <select id="getPayBankInfo" parameterType="java.lang.String"
            resultType="java.util.Map">
        select BankAccName,
               BankCode,
               BankAccNo,
               PayType
        from fcorderpay
        where orderNo in (select orderno from fcorderitem where orderitemno = #{orderItemNo,jdbcType=VARCHAR})
          and IsValid = '0'
          and PayType in ('03', '04', '05')
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fcperinfo
        where PerNo = #{perNo,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FCPerInfo">
        insert into fcperinfo (PerNo,
                               CPerNo, GrpNo,
                               StaffNo, LevelCode, department,
                               Name, Sex, nativeplace, IDType,
                               IDNo, idTypeEndDate,
                               BirthDay, Phone,
                               MobilePhone, OccupationType, OccupationCode,
                               JoinMedProtect, MedProtectType, Email,
                               ZipCode, Address, DefaultPlan,
                               OpenBank, OpenAccount, OperatorCom,
                               Operator, MakeDate, MakeTime,
                               ModifyDate, ModifyTime)
        values (#{perNo,jdbcType=VARCHAR},
                #{CPerNo,jdbcType=VARCHAR},
                #{grpNo,jdbcType=VARCHAR},
                #{staffNo,jdbcType=VARCHAR}, #{levelCode,jdbcType=VARCHAR},
                #{department,jdbcType=VARCHAR},
                #{name,jdbcType=VARCHAR},
                #{sex,jdbcType=VARCHAR},
                #{nativeplace,jdbcType=VARCHAR},
                #{IDType,jdbcType=VARCHAR},
                #{IDNo,jdbcType=VARCHAR}, #{idTypeEndDate,jdbcType=VARCHAR}, #{birthDay,jdbcType=DATE},
                #{phone,jdbcType=VARCHAR},
                #{mobilePhone,jdbcType=VARCHAR},
                #{occupationType,jdbcType=VARCHAR},
                #{occupationCode,jdbcType=VARCHAR},
                #{joinMedProtect,jdbcType=VARCHAR},
                #{medProtectType,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR},
                #{zipCode,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR},
                #{defaultPlan,jdbcType=VARCHAR},
                #{openBank,jdbcType=VARCHAR},
                #{openAccount,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR},
                #{operator,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE},
                #{makeTime,jdbcType=VARCHAR},
                #{modifyDate,jdbcType=DATE},
                #{modifyTime,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCPerInfo">
        insert into fcperinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="perNo != null">
                PerNo,
            </if>
            <if test="CPerNo != null">
                CPerNo,
            </if>
            <if test="grpNo != null">
                GrpNo,
            </if>
            <if test="staffNo != null">
                StaffNo,
            </if>
            <if test="levelCode != null">
                LevelCode,
            </if>
            <if test="department != null">
                department,
            </if>
            <if test="name != null">
                Name,
            </if>
            <if test="sex != null">
                Sex,
            </if>
            <if test="nativeplace != null">
                Nativeplace,
            </if>
            <if test="retirement != null">
                Retirement,
            </if>
            <if test="IDType != null">
                IDType,
            </if>
            <if test="IDNo != null">
                IDNo,
            </if>
            <if test="idTypeEndDate != null">
                IdTypeEndDate,
            </if>
            <if test="birthDay != null">
                BirthDay,
            </if>
            <if test="phone != null">
                Phone,
            </if>
            <if test="mobilePhone != null">
                MobilePhone,
            </if>
            <if test="occupationType != null">
                OccupationType,
            </if>
            <if test="occupationCode != null">
                OccupationCode,
            </if>
            <if test="joinMedProtect != null">
                JoinMedProtect,
            </if>
            <if test="medProtectType != null">
                MedProtectType,
            </if>
            <if test="email != null">
                Email,
            </if>
            <if test="zipCode != null">
                ZipCode,
            </if>
            <if test="address != null">
                Address,
            </if>
            <if test="defaultPlan != null">
                DefaultPlan,
            </if>
            <if test="openBank != null">
                OpenBank,
            </if>
            <if test="openAccount != null">
                OpenAccount,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="province  != null">
                province,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="county != null">
                county,
            </if>
            <if test="detaileAddress != null">
                detaileAddress,
            </if>
            <if test="idImage1 != null">
                IdImage1,
            </if>
            <if test="idImage2 != null">
                IdImage2,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
            <if test="relationship != null">
                relationship,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="perNo != null">
                #{perNo,jdbcType=VARCHAR},
            </if>
            <if test="CPerNo != null">
                #{CPerNo,jdbcType=VARCHAR},
            </if>
            <if test="grpNo != null">
                #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="staffNo != null">
                #{staffNo,jdbcType=VARCHAR},
            </if>
            <if test="levelCode != null">
                #{levelCode,jdbcType=VARCHAR},
            </if>
            <if test="department != null">
                #{department,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="retirement != null">
                #{retirement,jdbcType=VARCHAR},
            </if>
            <if test="IDType != null">
                #{IDType,jdbcType=VARCHAR},
            </if>
            <if test="IDNo != null">
                #{IDNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeEndDate != null">
                #{idTypeEndDate,jdbcType=VARCHAR},
            </if>
            <if test="birthDay != null">
                #{birthDay,jdbcType=DATE},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="occupationType != null">
                #{occupationType,jdbcType=VARCHAR},
            </if>
            <if test="occupationCode != null">
                #{occupationCode,jdbcType=VARCHAR},
            </if>
            <if test="joinMedProtect != null">
                #{joinMedProtect,jdbcType=VARCHAR},
            </if>
            <if test="medProtectType != null">
                #{medProtectType,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="defaultPlan != null">
                #{defaultPlan,jdbcType=VARCHAR},
            </if>
            <if test="openBank != null">
                #{openBank,jdbcType=VARCHAR},
            </if>
            <if test="openAccount != null">
                #{openAccount,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                #{county,jdbcType=VARCHAR},
            </if>
            <if test="detaileAddress != null">
                #{detaileAddress,jdbcType=VARCHAR},
            </if>
            <if test="idImage1 != null">
                #{idImage1,jdbcType=VARCHAR},
            </if>
            <if test="idImage2 != null">
                #{idImage2,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test="relationship != null">
                #{relationship,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCPerInfo">
        update fcperinfo
        <set>
            <if test="CPerNo != null">
                CPerNo = #{CPerNo,jdbcType=VARCHAR},
            </if>
            <if test="grpNo != null">
                GrpNo = #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="staffNo != null">
                StaffNo = #{staffNo,jdbcType=VARCHAR},
            </if>
            <if test="levelCode != null">
                LevelCode = #{levelCode,jdbcType=VARCHAR},
            </if>
            <if test="department != null">
                department = #{department,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                Sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                nativeplace = #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="IDType != null">
                IDType = #{IDType,jdbcType=VARCHAR},
            </if>
            <if test="IDNo != null">
                IDNo = #{IDNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate = #{idTypeEndDate,jdbcType=VARCHAR},
            </if>
            <if test="birthDay != null">
                BirthDay = #{birthDay,jdbcType=DATE},
            </if>
            <if test="phone != null">
                Phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                MobilePhone = #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="occupationType != null">
                OccupationType = #{occupationType,jdbcType=VARCHAR},
            </if>
            <if test="occupationCode != null">
                OccupationCode = #{occupationCode,jdbcType=VARCHAR},
            </if>
            <if test="joinMedProtect != null">
                JoinMedProtect = #{joinMedProtect,jdbcType=VARCHAR},
            </if>
            <if test="medProtectType != null">
                MedProtectType = #{medProtectType,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                Email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                ZipCode = #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                Address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="defaultPlan != null">
                DefaultPlan = #{defaultPlan,jdbcType=VARCHAR},
            </if>
            <if test="serviceTerm != null">
                ServiceTerm = #{serviceTerm,jdbcType=VARCHAR},
            </if>
            <if test="retirement != null">
                Retirement = #{retirement,jdbcType=VARCHAR},
            </if>
            <if test="openBank != null">
                OpenBank = #{openBank,jdbcType=VARCHAR},
            </if>
            <if test="openAccount != null">
                OpenAccount = #{openAccount,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                Province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                City =#{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                County = #{county,jdbcType=VARCHAR},
            </if>
            <if test="detaileAddress != null">
                DetaileAddress = #{detaileAddress,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test="relationship != null">
                relationship = #{relationship,jdbcType=VARCHAR},
            </if>
        </set>
        where 1=1
        <if test="perNo != null and perNo != ''">
            AND PerNo = #{perNo,jdbcType=VARCHAR}
        </if>
        <if test="IDNo != null and IDNo != ''">
            AND IDNo = #{IDNo}
        </if>
    </update>
    <update id="updateByPrimaryKeySelective1" parameterType="com.sinosoft.eflex.model.FCPerInfo">
        update fcperinfo
        <set>
            <if test="CPerNo != null">
                CPerNo = #{CPerNo,jdbcType=VARCHAR},
            </if>
            <if test="grpNo != null">
                GrpNo = #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="staffNo != null">
                StaffNo = #{staffNo,jdbcType=VARCHAR},
            </if>
            <if test="levelCode != null">
                LevelCode = #{levelCode,jdbcType=VARCHAR},
            </if>
            <if test="department != null">
                department = #{department,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                Sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                nativeplace = #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="IDType != null">
                IDType = #{IDType,jdbcType=VARCHAR},
            </if>
            <if test="IDNo != null">
                IDNo = #{IDNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate = #{idTypeEndDate,jdbcType=VARCHAR},
            </if>
            <if test="birthDay != null">
                BirthDay = #{birthDay,jdbcType=DATE},
            </if>
            <if test="phone != null">
                Phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                MobilePhone = #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="occupationType != null">
                OccupationType = #{occupationType,jdbcType=VARCHAR},
            </if>
            <if test="occupationCode != null">
                OccupationCode = #{occupationCode,jdbcType=VARCHAR},
            </if>
            <if test="joinMedProtect != null">
                JoinMedProtect = #{joinMedProtect,jdbcType=VARCHAR},
            </if>
            <if test="medProtectType != null">
                MedProtectType = #{medProtectType,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                Email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                ZipCode = #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                Address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="defaultPlan != null">
                DefaultPlan = #{defaultPlan,jdbcType=VARCHAR},
            </if>
            <if test="serviceTerm != null">
                ServiceTerm = #{serviceTerm,jdbcType=VARCHAR},
            </if>
            <if test="retirement != null">
                Retirement = #{retirement,jdbcType=VARCHAR},
            </if>
            <if test="openBank != null">
                OpenBank = #{openBank,jdbcType=VARCHAR},
            </if>
            <if test="openAccount != null">
                OpenAccount = #{openAccount,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                Province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                City =#{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                County = #{county,jdbcType=VARCHAR},
            </if>
            <if test="detaileAddress != null">
                DetaileAddress = #{detaileAddress,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test="relationship != null">
                relationship = #{relationship,jdbcType=VARCHAR},
            </if>
        </set>
        where 1=1
        <if test="perNo != null and perNo != ''">
            AND PerNo = #{perNo,jdbcType=VARCHAR}
        </if>
    </update>
    <update id="updateByPerNoOrIdNoSelective" parameterType="com.sinosoft.eflex.model.FCPerInfo">
        update fcperinfo
        <set>
            <if test="CPerNo != null">
                CPerNo = #{CPerNo,jdbcType=VARCHAR},
            </if>
            <if test="grpNo != null">
                GrpNo = #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="staffNo != null">
                StaffNo = #{staffNo,jdbcType=VARCHAR},
            </if>
            <if test="levelCode != null">
                LevelCode = #{levelCode,jdbcType=VARCHAR},
            </if>
            <if test="department != null">
                department = #{department,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                Name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                Sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="nativeplace != null">
                nativeplace = #{nativeplace,jdbcType=VARCHAR},
            </if>
            <if test="IDType != null">
                IDType = #{IDType,jdbcType=VARCHAR},
            </if>
            <if test="IDNo != null">
                IDNo = #{IDNo,jdbcType=VARCHAR},
            </if>
            <if test="idTypeEndDate != null">
                idTypeEndDate = #{idTypeEndDate,jdbcType=VARCHAR},
            </if>
            <if test="birthDay != null">
                BirthDay = #{birthDay,jdbcType=DATE},
            </if>
            <if test="phone != null">
                Phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="mobilePhone != null">
                MobilePhone = #{mobilePhone,jdbcType=VARCHAR},
            </if>
            <if test="occupationType != null">
                OccupationType = #{occupationType,jdbcType=VARCHAR},
            </if>
            <if test="occupationCode != null">
                OccupationCode = #{occupationCode,jdbcType=VARCHAR},
            </if>
            <if test="joinMedProtect != null">
                JoinMedProtect = #{joinMedProtect,jdbcType=VARCHAR},
            </if>
            <if test="medProtectType != null">
                MedProtectType = #{medProtectType,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                Email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                ZipCode = #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                Address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="defaultPlan != null">
                DefaultPlan = #{defaultPlan,jdbcType=VARCHAR},
            </if>
            <if test="serviceTerm != null">
                ServiceTerm = #{serviceTerm,jdbcType=VARCHAR},
            </if>
            <if test="retirement != null">
                Retirement = #{retirement,jdbcType=VARCHAR},
            </if>
            <if test="openBank != null">
                OpenBank = #{openBank,jdbcType=VARCHAR},
            </if>
            <if test="openAccount != null">
                OpenAccount = #{openAccount,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                Province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                City =#{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null">
                County = #{county,jdbcType=VARCHAR},
            </if>
            <if test="detaileAddress != null">
                DetaileAddress = #{detaileAddress,jdbcType=VARCHAR},
            </if>
            <if test="idImage1 != null">
                IdImage1 = #{idImage1,jdbcType=VARCHAR},
            </if>
            <if test="idImage2 != null">
                IdImage2 = #{idImage2,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where PerNo = #{perNo,jdbcType=VARCHAR} or IDNo = #{IDNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCPerInfo">
        update fcperinfo
        set CPerNo         = #{CPerNo,jdbcType=VARCHAR},
            GrpNo          =
                #{grpNo,jdbcType=VARCHAR},
            StaffNo        = #{staffNo,jdbcType=VARCHAR},
            LevelCode      = #{levelCode,jdbcType=VARCHAR},
            department     =
                #{department,jdbcType=VARCHAR},
            Name           = #{name,jdbcType=VARCHAR},
            Sex            =
                #{sex,jdbcType=VARCHAR},
            Nativeplace    =
                #{nativeplace,jdbcType=VARCHAR},
            IDType         = #{IDType,jdbcType=VARCHAR},
            IDNo           =
                #{IDNo,jdbcType=VARCHAR},
            IdTypeEndDate  =
                #{idTypeEndDate,jdbcType=VARCHAR},
            BirthDay       = #{birthDay,jdbcType=DATE},
            Phone          =
                #{phone,jdbcType=VARCHAR},
            MobilePhone    =
                #{mobilePhone,jdbcType=VARCHAR},
            OccupationType =
                #{occupationType,jdbcType=VARCHAR},
            OccupationCode =
                #{occupationCode,jdbcType=VARCHAR},
            JoinMedProtect =
                #{joinMedProtect,jdbcType=VARCHAR},
            MedProtectType =
                #{medProtectType,jdbcType=VARCHAR},
            Email          = #{email,jdbcType=VARCHAR},
            ZipCode        = #{zipCode,jdbcType=VARCHAR},
            Address        =
                #{address,jdbcType=VARCHAR},
            DefaultPlan    =
                #{defaultPlan,jdbcType=VARCHAR},
            OpenBank       =
                #{openBank,jdbcType=VARCHAR},
            OpenAccount    =
                #{openAccount,jdbcType=VARCHAR},
            OperatorCom    =
                #{operatorCom,jdbcType=VARCHAR},
            Operator       =
                #{operator,jdbcType=VARCHAR},
            MakeDate       = #{makeDate,jdbcType=DATE},
            MakeTime       = #{makeTime,jdbcType=VARCHAR},
            ModifyDate     =
                #{modifyDate,jdbcType=DATE},
            ModifyTime     =
                #{modifyTime,jdbcType=VARCHAR}
        where PerNo = #{perNo,jdbcType=VARCHAR}
    </update>

    <select id="selectByExample" parameterType="java.util.Map"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcperinfo
        where 1=1
        <if test="IDNo != null and IDNo != '' ">
            and IDNo = #{IDNo}
        </if>
        <if test="grpNo != null and grpNo != '' ">
            and grpNo = #{grpNo}
        </if>
    </select>
    <update id="updatePerNoPrimary" parameterType="com.sinosoft.eflex.model.FCPerInfo">
        update fcperinfo
        set Name           = #{name,jdbcType=VARCHAR},
            Sex            = #{sex,jdbcType=VARCHAR},
            Nativeplace    = #{nativeplace,jdbcType=VARCHAR},
            IDType         = #{IDType,jdbcType=VARCHAR},
            IDNo           = #{IDNo,jdbcType=VARCHAR},
            IdTypeEndDate  = #{idTypeEndDate,jdbcType=VARCHAR},
            BirthDay       = #{birthDay,jdbcType=DATE},
            MobilePhone    =
                #{mobilePhone,jdbcType=VARCHAR},
            OccupationType =
                #{occupationType,jdbcType=VARCHAR},
            OccupationCode =
                #{occupationCode,jdbcType=VARCHAR},
            Email          = #{email,jdbcType=VARCHAR},
            OpenBank       = #{openBank,jdbcType=VARCHAR},
            OpenAccount    =
                #{openAccount,jdbcType=VARCHAR},
            Operator       =
                #{operator,jdbcType=VARCHAR},
            ModifyDate     = #{modifyDate,jdbcType=DATE},
            ModifyTime     = #{modifyTime,jdbcType=VARCHAR}
        where PerNo =
              #{perNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByIdNo" parameterType="java.util.Map">
        update fcperinfo
        set MobilePhone=#{mobilePhone}
        where IDNo = #{idNo}
    </update>

    <select id="findUserInfo" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.FCPerInfo">
        select i.Name, i.Sex, i.IDType, i.IDNo, i.BirthDay
        from fduser u,
             fcperinfo i
        where i.PerNo = u.CustomNo
          and u.UserNo =
              #{userNo,jdbcType=VARCHAR}
    </select>

    <!-- 投保清单导出excel -->
    <select id="selectInsuredDetail" parameterType="java.util.Map"
            resultType="com.sinosoft.eflex.model.FCPerInfo">
        select Name,openBank,openAccount
        from fcperinfo
        where
        PerNo =
        #{perNo,jdbcType=VARCHAR}
        and GrpNo = #{grpNo,jdbcType=VARCHAR}
        <!--<if test="garName != null and garName != ''">
            AND `name` LIKE CONCAT(CONCAT('%',#{garName},'%'))
        </if>
        <if test="department != null and department != ''">
            AND department LIKE CONCAT(CONCAT('%',#{department},'%'))
        </if>
        <if test="garSex != null and garSex != ''">
            AND Sex = #{garSex}
        </if>-->
    </select>

    <!-- 统计企业人数 -->
    <select id="countPeoplesBygrpNo" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1)
        from fcperinfo
        where GrpNo = #{grpNo,jdbcType=VARCHAR}
    </select>
    <resultMap id="BaseResultMapTemp" type="com.sinosoft.eflex.model.FCPerInfoTemp">
        <id column="PerTempNo" jdbcType="VARCHAR" property="perTempNo"/>
        <result column="CPerNo" jdbcType="VARCHAR" property="CPerNo"/>
        <result column="GrpNo" jdbcType="VARCHAR" property="grpNo"/>
        <result column="StaffNo" jdbcType="VARCHAR" property="staffNo"/>
        <result column="LevelCode" jdbcType="VARCHAR" property="levelCode"/>
        <result column="department" jdbcType="VARCHAR" property="department"/>
        <result column="Name" jdbcType="VARCHAR" property="name"/>
        <result column="Sex" jdbcType="VARCHAR" property="sex"/>
        <result column="Nativeplace" jdbcType="VARCHAR" property="nativeplace"/>
        <result column="IDType" jdbcType="VARCHAR" property="IDType"/>
        <result column="IDNo" jdbcType="VARCHAR" property="IDNo"/>
        <result column="IdTypeEndDate" jdbcType="DATE" property="idTypeEndDate"/>
        <result column="BirthDay" jdbcType="DATE" property="birthDay"/>
        <result column="Phone" jdbcType="VARCHAR" property="phone"/>
        <result column="MobilePhone" jdbcType="VARCHAR" property="mobilePhone"/>
        <result column="OccupationType" jdbcType="VARCHAR" property="occupationType"/>
        <result column="OccupationCode" jdbcType="VARCHAR" property="occupationCode"/>
        <result column="JoinMedProtect" jdbcType="VARCHAR" property="joinMedProtect"/>
        <result column="MedProtectType" jdbcType="VARCHAR" property="medProtectType"/>
        <result column="Email" jdbcType="VARCHAR" property="email"/>
        <result column="ZipCode" jdbcType="VARCHAR" property="zipCode"/>
        <result column="Address" jdbcType="VARCHAR" property="address"/>
        <result column="DefaultPlan" jdbcType="VARCHAR" property="defaultPlan"/>
        <result column="OpenBank" jdbcType="VARCHAR" property="openBank"/>
        <result column="OpenAccount" jdbcType="VARCHAR" property="openAccount"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <!-- 根据条件查询员工信息以及其默认计划 -->
    <select id="selectByParam" parameterType="java.util.Map" resultMap="BaseResultMapTemp">
        select a.PerTempNo, a.CPerNo, a.GrpNo, a.StaffNo, a.LevelCode, a.department, a.Name,
        a.Sex,a.Nativeplace, a.IDType,
        a.IDNo,a.idTypeEndDate,a.BirthDay,
        a.Phone, a.MobilePhone, a.OccupationType,
        a.OccupationCode, a.JoinMedProtect,
        a.MedProtectType,
        a.Email, a.ZipCode, a.Address,
        a.DefaultPlan,a.OpenBank, a.OpenAccount, a.OperatorCom,
        a.Operator,
        a.MakeDate,
        a.MakeTime, a.ModifyDate, a.ModifyTime,a.StaffGrpPrem,a.FamilyGrpPrem from fcperinfotemp a
        where 1=1 and a.ImpotStatus='01' and ensurecode=#{ensureCode}
        <if test="defaultPlan != null and defaultPlan != '' ">
            and a.DefaultPlan = #{defaultPlan}
        </if>
        <if test="grpNo != null and grpNo != '' ">
            and a.GrpNo = #{grpNo}
        </if>
        <if test="name != null and name != '' ">
            and a.Name = #{name}
        </if>
        <if test="sex != null and sex != '' ">
            and a.Sex = #{sex}
        </if>
        <if test="IDType != null and IDType != '' ">
            and a.IDType = #{IDType}
        </if>
        <if test="IDNo != null and IDNo != '' ">
            and a.IDNo = #{IDNo}
        </if>
    </select>

    <!-- 判断是否存在该员工 -->
    <select id="isExitPerInfo" parameterType="java.util.Map"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcperinfo
        where 1=1
        <if test="perNo != null and perNo != '' ">
            and PerNo != #{perNo}
        </if>
        <if test="IDNo != null and IDNo != '' ">
            and IDNo = #{IDNo}
        </if>
        <if test="IDType != null and IDType != '' ">
            AND IDType = #{IDType}
        </if>
        <if test="grpNo != null and grpNo != '' ">
            AND grpNo = #{grpNo}
        </if>
    </select>

    <select id="selectByUserNo" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT *
        FROM fcperinfo
        WHERE perno = (SELECT CustomNo FROM fduser WHERE UserNo = #{UserNo} AND CustomType = '1')
    </select>

    <!-- 根据条件查询员工信息以及其默认计划 -->
    <select id="selectByParamList" parameterType="java.util.Map" resultType="java.util.Map">
        select a.PerTempNo as perTempNo, a.CPerNo as CPerNo, a.GrpNo as grpNo, a.StaffNo as staffNo, a.LevelCode as
        levelCode, a.department as department, a.Name as name,
        a.Sex as sex,a.Nativeplace as nativeplace, a.IDType as iDType,
        a.IDNo as iDNo, a.BirthDay as birthDay,a.idTypeEndDate as idTypeEndDate,a.relationship as relationship,
        a.Phone as phone, a.MobilePhone as mobilePhone, a.OccupationType as occupationType,
        a.OccupationCode as occupationCode, a.JoinMedProtect as joinMedProtect,
        a.ServiceTerm,a.Retirement,
        a.MedProtectType as medProtectType,
        a.Email as email, a.ZipCode as zipCode, a.Address as address,
        a.DefaultPlan as defaultPlan,
        (select TotalPrem from fcensureplan where PlanCode = defaultPlan and EnsureCode = #{ensureCode}) as totalPrem,
        a.OpenBank as openBank, a.OpenAccount as openAccount, a.OperatorCom as operatorCom,
        a.Operator as operator,
        a.MakeDate as makeDate,
        a.MakeTime as makeTime, a.ModifyDate as modifyDate, a.ModifyTime as modifyTime,a.StaffGrpPrem as staffGrp
        ,a.FamilyGrpPrem as familyGrp,'0' as relation,a.Name as perName from fcperinfotemp a
        where 1=1 and a.ImpotStatus='01' and ensurecode=#{ensureCode}
        <if test="defaultPlan != null and defaultPlan != '' ">
            and a.DefaultPlan = #{defaultPlan}
        </if>
        <if test="grpNo != null and grpNo != '' ">
            and a.GrpNo = #{grpNo}
        </if>
        <if test="name != null and name != '' ">
            and a.Name like concat('%',#{name},'%')
        </if>
        <if test="sex != null and sex != '' ">
            and a.Sex = #{sex}
        </if>
        <if test="IDType != null and IDType != '' ">
            and a.IDType = #{IDType}
        </if>
        <if test="IDNo != null and IDNo != '' ">
            and a.IDNo = #{IDNo}
        </if>
        <if test="nativeplace != null and nativeplace != '' ">
            and a.nativeplace = #{nativeplace}
        </if>
    </select>
    <select id='selectSinglePerNo' parameterType="java.util.Map" resultType="java.lang.String">
        select *
        from fcperinfo
        where grpno = #{grpNo}
          and idno in (select IDNO from fcperinfo where perno = #{perNo})
    </select>
    <select id='selectPerNo' parameterType="java.lang.String" resultType="java.lang.String">
        select *
        from fcperinfo
        where grpno = #{grpNo}
          and idno in (select IDNO from fcperinfo where perno = #{perNo})
    </select>

    <select id='selectNewFcperinfo' parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCPerInfo">
        select *
        from fcperinfo
        where idno = (select idno from fcperinfo where perno = #{perNo})
        order by ModifyDate desc, Modifytime desc, Makedate desc, Maketime desc limit 1
    </select>


    <select id="getPerInfoByEnsureCode" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT p.`PerNo`                             as perNo,
               p.`name`                              as `name`,
               date_format(p.`BirthDay`, '%Y-%m-%d') as birthDay,
               p.`Sex`                               as sex,
               p.`JoinMedProtect`                    as joinMedProtect,
               p.`OccupationType`                    as occupationType,
               p.`LevelCode`                         as levelCode,
               p.`ServiceTerm`                       as serviceTerm,
               p.`Retirement`                        as retirement
        FROM fcperinfo p
                 INNER JOIN fcperregistday d ON p.`PerNo` = d.`PerNo`
        WHERE d.`EnsureCode` = #{ensureCode}
    </select>

    <select id="queryUnInsuredList" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT a.perNo as perNo,a.`Name` AS `name`,a.`Sex` AS
        sex,date_format(str_to_date(a.BirthDay,'%Y-%m-%d'),'%Y-%m-%d') as birthDay,a.`nativeplace` AS
        nativePlace,a.`IDType` AS idType,
        (select codename from fdcode where CodeType = 'IDType' and CodeKey = a.`IDType`) as iDTypeName,
        (select codename from fdcode where CodeType = 'nativeplace' and CodeKey = a.nativeplace) as nativeplaceName,
        (select codename from fdcode where CodeType = 'OccupationBig' and CodeKey = a.`OccupationType`) as
        occupationTypeName,
        (select codename from fdcode where CodeType = 'OccupationDetail' and CodeKey = a.`OccupationCode`) as
        occupationCodeName,
        CASE a.`Sex` WHEN '0' THEN '男' ELSE '女' END AS sexName,
        a.`IDNo` AS idNo,a.`MobilePhone` AS mobilePhone,
        a.`OccupationType` AS occupationType,a.`OccupationCode` AS occupationCode,a.`JoinMedProtect` AS
        joinMedProtect,a.`Email` AS email,b.`LockState` As lockState
        FROM fcperinfo a
        LEFT JOIN fcperregistday b ON a.`PerNo` = b.`PerNo`
        LEFT JOIN fcstafffamilyrela c ON c.`PerNo` = a.`PerNo` AND c.`Relation` = '0'
        LEFT JOIN fcgrporder e ON e.`EnsureCode` = b.`EnsureCode`
        LEFT JOIN fcperregistday f on f.`EnsureCode` = e.`EnsureCode` and f.`PerNo` = c.`PerNo`
        WHERE b.`EnsureCode` = #{ensureCode} AND b.`PerNo` NOT IN (SELECT PerNo FROM fcorder WHERE GrpOrderNo =
        e.`GrpOrderNo` and OrderStatus not in (05,03))
        <if test="name != null and name != '' ">
            and a.`Name` LIKE CONCAT('%',#{name},'%')
        </if>
        <if test="sex != null and sex != '' ">
            and a.`Sex` = #{sex}
        </if>
        <if test="nativeplace != null and nativeplace != '' ">
            and a.`nativeplace` = #{nativeplace}
        </if>
        <if test="idType != null and idType != '' ">
            and a.`IDType` = #{idType}
        </if>
        <if test="idNo != null and idNo != '' ">
            and a.`IDNo` = #{idNo}
        </if>
    </select>

    <select id="getPerNoByFamIdNoList" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT a.`PerNo`
        FROM fcperinfo a
        WHERE a.`IDNo` = #{perIdNo}
          AND a.`PerNo` NOT IN (
            SELECT r.`PerNo`
            FROM fcstafffamilyrela r
                     INNER JOIN fcperinfo p ON r.`PerNo` = p.`PerNo`
                     INNER JOIN fcperson s ON r.`PersonID` = s.`PersonID`
            WHERE p.`IDNo` = #{perIdNo}
              AND s.`IDNo` = #{famIdNo})
    </select>

    <select id="selectInsuredPerson" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT fco.`Name`,
               fco.`IDType`,
               fco.`IDNo`,
               fct.`DutyCode`,
               fct.`InsurePeriod`,
               fct.`PayPeriod`,
               fct.`PayFrequency`,
               fct.`InsuredAmount`,
               fct.`DailyPrem`
        FROM fcperregistday fr,
             fcorder fc,
             fcorderitem fcm,
             fcorderitemdetail fct,
             fcperinfo fco
        WHERE fr.`PerNo` = fc.`PerNo`
          AND fc.`OrderNo` = fcm.`OrderNo`
          AND fcm.OrderItemDetailNo = fct.OrderItemDetailNo
          AND fco.`PerNo` = fr.`PerNo`
          AND fr.`EnsureCode` = #{EnsureCode}
    </select>
    <select id="selectInsuredPerson_addcheckstatus" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT a.`OrderItemNo`,
               e.`Name`,
               d.`DutyCode`,
               e.`name`,
               e.`IDType`,
               e.`IDNo`,
               d.`DutyCode`,
               d.`InsurePeriod`,
               d.`PayPeriod`,
               d.`PayFrequency`,
               d.`InsuredAmount`,
               g.`GrpPrem`,
               f.`PlanState`
        FROM fcorderitem a
                 INNER JOIN fcorder b ON a.`OrderNo` = b.`OrderNo`
                 INNER JOIN fcgrporder c ON b.`GrpOrderNo` = c.`GrpOrderNo`
                 INNER JOIN fcorderitemdetail d ON d.`OrderItemDetailNo` = a.`OrderItemDetailNo`
                 INNER JOIN fcorderitem g ON g.`OrderNo` = b.`OrderNo`
                 INNER JOIN fcperinfo e ON e.`PerNo` = b.`PerNo`
                 INNER JOIN FcDailyInsureRiskDetailInfo f ON d.`DutyCode` = f.`PlanCode`
        WHERE c.EnsureCode = #{EnsureCode}
          AND f.`EnsureCode` = #{EnsureCode}
    </select>
    <select id="selectInsuredPerson_All" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT a.`orderItemNo`,
               d.`dutyCode`,
               d.`insurePeriod`,
               d.`payPeriod`,
               d.`payFrequency`,
               d.`insuredAmount`,
               h.`riskCode`,
               cast((IFNULL(g.`grpPrem`, 0.00) + IFNULL(g.`selfPrem`, 0.00)) as char) as dailyPrem,
               b.`operator`,
               e.`nativeplace`                                                        as country,
               date_format(e.`idTypeEndDate`, '%Y-%m-%d')                             as idTypeEndDay,
               e.`Email`                                                              as email1,
               e.`OccupationCode`                                                     as occupationCode,
               f.`planState`,
               e.`name`,
               e.`iDType`,
               e.`iDNo`,
               e.`phone`,
               e.`MobilePhone`                                                        as mobilePhone,
               e.`perNo`,
               c.`grpNo`,
               c.`grpOrderNo`,
               e.`Province`                                                           as province,
               e.`City`                                                               as city,
               e.`County`                                                             as area,
               e.`DetaileAddress`                                                     as regAddress,
               e.`ZipCode`                                                            as ZipCode,
               date_format(e.`birthDay`, '%Y-%m-%d')                                  as birthDay,
               e.`sex`,
               e.`idImage1`,
               e.`idImage2`
        FROM fcorderitem a
                 INNER JOIN fcorder b ON a.`OrderNo` = b.`OrderNo`
                 INNER JOIN fcgrporder c ON b.`GrpOrderNo` = c.`GrpOrderNo`
                 INNER JOIN fcorderitemdetail d ON d.`OrderItemDetailNo` = a.`OrderItemDetailNo`
                 INNER JOIN fcorderitem g ON g.`OrderNo` = b.`OrderNo`
                 INNER JOIN fcperinfo e ON e.`PerNo` = b.`PerNo`
                 INNER JOIN FcDailyInsureRiskDetailInfo f ON d.`DutyCode` = f.`PlanCode`
                 INNER JOIN fcdailyinsureriskinfo h ON h.`EnsureCode` = c.`EnsureCode`
        WHERE c.EnsureCode = #{EnsureCode}
          AND f.`EnsureCode` = #{EnsureCode}
          and b.orderSign = '1'
          and f.planState = '0'
    </select>
    <select id="selectByIdNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcperinfo where IDNo = #{idNo}
    </select>
    <select id="selectInsuredPersonByIDNoandEnCode" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT e.`PerNo`
        FROM fcorderitem a
                 INNER JOIN fcorder b ON a.`OrderNo` = b.`OrderNo`
                 INNER JOIN fcgrporder c ON b.`GrpOrderNo` = c.`GrpOrderNo`
                 INNER JOIN fcorderitemdetail d ON d.`OrderItemDetailNo` = a.`OrderItemDetailNo`
                 INNER JOIN fcperinfo e ON e.`PerNo` = b.`PerNo`
                 INNER JOIN FcDailyInsureRiskDetailInfo f ON d.`DutyCode` = f.`PlanCode`
        WHERE c.EnsureCode = #{EnsureCode}
          AND f.`EnsureCode` = #{EnsureCode}
          and e.`IDNo` = #{IDNo}
    </select>
    <select id="selectInsuredPerson_allInfo" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT a.`OrderItemNo`,
               e.`Name`,
               d.`DutyCode`,
               e.`name`,
               e.`IDType`,
               e.`IDNo`,
               d.`DutyCode`,
               d.`InsurePeriod`,
               d.`PayPeriod`,
               d.`PayFrequency`,
               d.`InsuredAmount`,
               d.`DailyPrem`,
               f.`PlanState`,
               e.`Sex`,
               e.`OccupationCode`,
               e.`OccupationType`,
               e.`MobilePhone`,
               e.`OpenAccount`,
               e.`BirthDay`
        FROM fcorderitem a
                 INNER JOIN fcorder b ON a.`OrderNo` = b.`OrderNo`
                 INNER JOIN fcgrporder c ON b.`GrpOrderNo` = c.`GrpOrderNo`
                 INNER JOIN fcorderitemdetail d ON d.`OrderItemDetailNo` = a.`OrderItemDetailNo`
                 INNER JOIN fcperinfo e ON e.`PerNo` = b.`PerNo`
                 INNER JOIN FcDailyInsureRiskDetailInfo f ON d.`DutyCode` = f.`PlanCode`
        WHERE c.EnsureCode = #{EnsureCode}
          AND f.`EnsureCode` = #{EnsureCode}
    </select>
    <select id="selectbyIdNoandGrpNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT *
        FROM fcperinfo
        WHERE IDNo = #{IDNo}
          and GrpNo = #{GrpNo}
    </select>
    <select id="selectByGrpNoIdNoAndPerNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcperinfo where GrpNo = #{grpNo} and IDNo = #{idNo}
        <if test="perNo != null and perNo != ''">
            and PerNo != #{perNo}
        </if>
    </select>
    <select id="selectFromFCPerRegistDayByEnsureCode" resultMap="BaseResultMap">
        select f2.PerNo, f2.Name, f2.IDNo, f2.IDType, f2.MobilePhone
        from fcPerRegistDay f1
                 inner join fcperinfo f2 on f1.PerNo = f2.PerNo
            and f1.EnsureCode = #{ensureCode}
    </select>
    <!--根据  ensureCode,  grpNo,  idNo  判断 日常计划定制中 在一个福利下面，一个企业 选择的这个员工  是否已经存在，返回的是这个员工的个数。-->
    <select id="selectThePeopleNum" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM fcorder b
                 INNER JOIN fcgrporder c ON b.`GrpOrderNo` = c.`GrpOrderNo`
                 INNER JOIN fcperinfo e ON e.`PerNo` = b.`PerNo`
        WHERE c.EnsureCode = #{EnsureCode}
          AND b.`GrpNo` = #{grpNo}
          AND e.`IDNo` = #{idNo};
    </select>
    <select id="selectgrpListByPerNo" parameterType="java.util.Map"
            resultType="java.util.Map">
        SELECT distinct b.GrpName, b.GrpNo
        FROM fcperinfo a
                 INNER JOIN fcgrpinfo b ON a.GrpNo = b.GrpNo
        WHERE a.PerNo IN (SELECT perno FROM fcperinfo WHERE idno IN (SELECT idno FROM fcperinfo WHERE perno = #{perNo}))
    </select>
    <select id="selectgrpListByPerNo2" parameterType="java.util.Map"
            resultType="java.util.Map">
        SELECT distinct b.grpName, b.grpNo,a.perNo
        FROM fcperinfo a
                 INNER JOIN fcgrpinfo b ON a.GrpNo = b.GrpNo
        WHERE a.PerNo IN (SELECT perno FROM fcperinfo WHERE idno IN (SELECT idno FROM fcperinfo WHERE perno = #{perNo}))
    </select>
    <select id="selectperNobyPerNo" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT a.perNo
        FROM fcperinfo a
        WHERE a.IDNo = (SELECT idno FROM fcperinfo WHERE perno = #{perNo})
          and a.GrpNo = #{grpNo}
    </select>
    <select id="selectRepeatPhone" resultType="java.lang.Integer">
        select count(1)
        from fcperinfo
        where mobilephone = #{mobilePhone}
          and perNo != #{perNo}
          and idno != #{idno}
    </select>
    <select id="selectInsuredPerson_Alls" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT e.`nativeplace`                       as country,
               e.`name`,
               e.`iDType`,
               e.`iDNo`,
               date_format(e.`birthDay`, '%Y-%m-%d') as birthDay,
               e.`sex`
        FROM fcorderitem a
                 INNER JOIN fcorder b ON a.`OrderNo` = b.`OrderNo`
                 INNER JOIN fcgrporder c ON b.`GrpOrderNo` = c.`GrpOrderNo`
                 INNER JOIN fcorderitemdetail d ON d.`OrderItemDetailNo` = a.`OrderItemDetailNo`
                 INNER JOIN fcorderitem g ON g.`OrderNo` = b.`OrderNo`
                 INNER JOIN fcperinfo e ON e.`PerNo` = b.`PerNo`
                 INNER JOIN FcDailyInsureRiskDetailInfo f ON d.`DutyCode` = f.`PlanCode`
                 INNER JOIN fcdailyinsureriskinfo h ON h.`EnsureCode` = c.`EnsureCode`
        WHERE c.EnsureCode = #{EnsureCode}
          AND f.`EnsureCode` = #{EnsureCode}
          and b.orderSign = '1'
          and f.planState = '0'
    </select>
    <update id="updateBatchById">
        <foreach collection="list" item="item" separator=";">
            update fcperinfo
            <set>
                <if test="item.CPerNo != null">
                    CPerNo = #{item.CPerNo,jdbcType=VARCHAR},
                </if>
                <if test="item.grpNo != null">
                    GrpNo = #{item.grpNo,jdbcType=VARCHAR},
                </if>
                <if test="item.staffNo != null">
                    StaffNo = #{item.staffNo,jdbcType=VARCHAR},
                </if>
                <if test="item.levelCode != null">
                    LevelCode = #{item.levelCode,jdbcType=VARCHAR},
                </if>
                <if test="item.department != null">
                    department = #{item.department,jdbcType=VARCHAR},
                </if>
                <if test="item.name != null">
                    Name = #{item.name,jdbcType=VARCHAR},
                </if>
                <if test="item.sex != null">
                    Sex = #{item.sex,jdbcType=VARCHAR},
                </if>
                <if test="item.nativeplace != null">
                    nativeplace = #{item.nativeplace,jdbcType=VARCHAR},
                </if>
                <if test="item.IDType != null">
                    IDType = #{item.IDType,jdbcType=VARCHAR},
                </if>
                <if test="item.IDNo != null">
                    IDNo = #{item.IDNo,jdbcType=VARCHAR},
                </if>
                <if test="item.idTypeEndDate != null">
                    idTypeEndDate = #{item.idTypeEndDate,jdbcType=VARCHAR},
                </if>
                <if test="item.birthDay != null">
                    BirthDay = #{item.birthDay,jdbcType=DATE},
                </if>
                <if test="item.phone != null">
                    Phone = #{item.phone,jdbcType=VARCHAR},
                </if>
                <if test="item.mobilePhone != null">
                    MobilePhone = #{item.mobilePhone,jdbcType=VARCHAR},
                </if>
                <if test="item.occupationType != null">
                    OccupationType = #{item.occupationType,jdbcType=VARCHAR},
                </if>
                <if test="item.occupationCode != null">
                    OccupationCode = #{item.occupationCode,jdbcType=VARCHAR},
                </if>
                <if test="item.joinMedProtect != null">
                    JoinMedProtect = #{item.joinMedProtect,jdbcType=VARCHAR},
                </if>
                <if test="item.medProtectType != null">
                    MedProtectType = #{item.medProtectType,jdbcType=VARCHAR},
                </if>
                <if test="item.email != null">
                    Email = #{item.email,jdbcType=VARCHAR},
                </if>
                <if test="item.zipCode != null">
                    ZipCode = #{item.zipCode,jdbcType=VARCHAR},
                </if>
                <if test="item.address != null">
                    Address = #{item.address,jdbcType=VARCHAR},
                </if>
                <if test="item.defaultPlan != null">
                    DefaultPlan = #{item.defaultPlan,jdbcType=VARCHAR},
                </if>
                <if test="item.serviceTerm != null">
                    ServiceTerm = #{item.serviceTerm,jdbcType=VARCHAR},
                </if>
                <if test="item.retirement != null">
                    Retirement = #{item.retirement,jdbcType=VARCHAR},
                </if>
                <if test="item.openBank != null">
                    OpenBank = #{item.openBank,jdbcType=VARCHAR},
                </if>
                <if test="item.openAccount != null">
                    OpenAccount = #{item.openAccount,jdbcType=VARCHAR},
                </if>
                <if test="item.operatorCom != null">
                    OperatorCom = #{item.operatorCom,jdbcType=VARCHAR},
                </if>
                <if test="item.operator != null">
                    Operator = #{item.operator,jdbcType=VARCHAR},
                </if>
                <if test="item.province != null">
                    Province = #{item.province,jdbcType=VARCHAR},
                </if>
                <if test="item.city != null">
                    City =#{item.city,jdbcType=VARCHAR},
                </if>
                <if test="item.county != null">
                    County = #{item.county,jdbcType=VARCHAR},
                </if>
                <if test="item.detaileAddress != null">
                    DetaileAddress = #{item.detaileAddress,jdbcType=VARCHAR},
                </if>
                <if test="item.idImage1 != null">
                    IdImage1 = #{item.idImage1,jdbcType=VARCHAR},
                </if>
                <if test="item.idImage2 != null">
                    IdImage2 = #{item.idImage2,jdbcType=VARCHAR},
                </if>
                <if test="item.makeDate != null">
                    MakeDate = #{item.makeDate,jdbcType=DATE},
                </if>
                <if test="item.makeTime != null">
                    MakeTime = #{item.makeTime,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyDate != null">
                    ModifyDate = #{item.modifyDate,jdbcType=DATE},
                </if>
                <if test="item.modifyTime != null">
                    ModifyTime = #{item.modifyTime,jdbcType=VARCHAR},
                </if>
            </set>
            where
            <choose>
                <when test="item.edorType == 'IC' and item.edorType != null and item.edorType != ''">
                    IDNo = #{item.oldIdNo}
                </when>
                <otherwise>
                    IDNo = #{item.IDNo}
                </otherwise>
            </choose>

        </foreach>
    </update>
    <select id="selectByIdNoAndGrpNo" resultType="com.sinosoft.eflex.model.FCPerInfo">
        select *
        from fcperinfo
        where IDNo = #{idNo}
          and GrpNo = #{grpNo}
    </select>
    <select id="getPerNoByFamIdNoList2" resultType="com.sinosoft.eflex.model.FCPerInfo">
        SELECT a.*
        FROM fcperinfo a
        WHERE a.`IDNo` = #{perIdNo}
          AND a.`PerNo` NOT IN (
            SELECT r.`PerNo`
            FROM fcstafffamilyrela r
                     INNER JOIN fcperinfo p ON r.`PerNo` = p.`PerNo`
                     INNER JOIN fcperson s ON r.`PersonID` = s.`PersonID`
            WHERE p.`IDNo` = #{perIdNo}
              AND s.`IDNo` = #{famIdNo})
    </select>
</mapper>
