<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCInformMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCInform">
    <id column="InformNumber" jdbcType="VARCHAR" property="informNumber" />
    <result column="InformVersion" jdbcType="VARCHAR" property="informVersion" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sinosoft.eflex.model.FCInformWithBLOBs">
    <result column="InformContent" jdbcType="LONGVARCHAR" property="informContent" />
    <result column="InformDescribe" jdbcType="LONGVARCHAR" property="informDescribe" />
  </resultMap>
  <sql id="Base_Column_List">
    InformNumber, InformVersion, Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, 
    ModifyTime
  </sql>
  <sql id="Blob_Column_List">
    InformContent, InformDescribe
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from fcinform
    where InformNumber = #{informNumber,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcinform
    where InformNumber = #{informNumber,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCInformWithBLOBs">
    insert into fcinform (InformNumber, InformVersion, Operator, 
      OperatorCom, MakeDate, MakeTime, 
      ModifyDate, ModifyTime, InformContent, 
      InformDescribe)
    values (#{informNumber,jdbcType=VARCHAR}, #{informVersion,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, 
      #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, 
      #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR}, #{informContent,jdbcType=LONGVARCHAR}, 
      #{informDescribe,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCInformWithBLOBs">
    insert into fcinform
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="informNumber != null">
        InformNumber,
      </if>
      <if test="informVersion != null">
        InformVersion,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
      <if test="informContent != null">
        InformContent,
      </if>
      <if test="informDescribe != null">
        InformDescribe,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="informNumber != null">
        #{informNumber,jdbcType=VARCHAR},
      </if>
      <if test="informVersion != null">
        #{informVersion,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
      <if test="informContent != null">
        #{informContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="informDescribe != null">
        #{informDescribe,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCInformWithBLOBs">
    update fcinform
    <set>
      <if test="informVersion != null">
        InformVersion = #{informVersion,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
      <if test="informContent != null">
        InformContent = #{informContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="informDescribe != null">
        InformDescribe = #{informDescribe,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where InformNumber = #{informNumber,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sinosoft.eflex.model.FCInformWithBLOBs">
    update fcinform
    set InformVersion = #{informVersion,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      InformContent = #{informContent,jdbcType=LONGVARCHAR},
      InformDescribe = #{informDescribe,jdbcType=LONGVARCHAR}
    where InformNumber = #{informNumber,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCInform">
    update fcinform
    set InformVersion = #{informVersion,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where InformNumber = #{informNumber,jdbcType=VARCHAR}
  </update>
</mapper>