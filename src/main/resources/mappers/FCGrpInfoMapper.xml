<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCGrpInfoMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCGrpInfo">
        <id column="GrpNo" jdbcType="VARCHAR" property="grpNo"/>
        <result column="CGrpNo" jdbcType="VARCHAR" property="CGrpNo"/>
        <result column="GrpName" jdbcType="VARCHAR" property="grpName"/>
        <result column="GrpAddRess" jdbcType="VARCHAR" property="grpAddRess"/>
        <result column="ZipCode" jdbcType="VARCHAR" property="zipCode"/>
        <result column="UnifiedsociCode" jdbcType="VARCHAR" property="unifiedsociCode"/>
        <result column="GrpIdType" jdbcType="VARCHAR" property="grpIdType"/>
        <result column="GrpIdNo" jdbcType="VARCHAR" property="grpIdNo"/>
        <result column="GrpType" jdbcType="VARCHAR" property="grpType"/>
        <result column="GrpNatureType" jdbcType="VARCHAR" property="grpNatureType"/>
        <result column="AccName" jdbcType="VARCHAR" property="accName"/>
        <result column="grpBankCode" jdbcType="VARCHAR" property="grpBankCode"/>
        <result column="grpBankAccNo" jdbcType="VARCHAR" property="grpBankAccNo"/>
        <result column="Peoples" jdbcType="INTEGER" property="peoples"/>
        <result column="CorporationMan" jdbcType="VARCHAR" property="corporationMan"/>
        <result column="telphone" jdbcType="VARCHAR" property="telphone"/>
        <result column="regaddress" jdbcType="VARCHAR" property="regaddress"/>
        <result column="Email" jdbcType="VARCHAR" property="email"/>
        <result column="Clientno" jdbcType="VARCHAR" property="clientno"/>
        <result column="clientName" jdbcType="VARCHAR" property="clientName"/>
        <result column="GrpTypeStartDate" jdbcType="VARCHAR" property="grpTypeStartDate"/>
        <result column="GrpTypeEndDate" jdbcType="VARCHAR" property="grpTypeEndDate"/>
        <result column="GrpEstablishDate" jdbcType="VARCHAR" property="grpEstablishDate"/>
        <result column="GrpScaleType" jdbcType="VARCHAR" property="grpScaleType"/>
        <result column="SociologyPlanSign" jdbcType="VARCHAR" property="sociologyPlanSign"/>
        <result column="RegisteredCapital" jdbcType="VARCHAR" property="registeredCapital"/>
        <result column="GrpCategory" jdbcType="VARCHAR" property="grpCategory"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
        <result column="GrpIDImage1" jdbcType="VARCHAR" property="grpIDImage1"/>
        <result column="GrpIDImage2" jdbcType="VARCHAR" property="grpIDImage2"/>
        <result column="grp_image_front" jdbcType="VARCHAR" property="grpImageFront"/>
        <result column="grp_image_back" jdbcType="VARCHAR" property="grpImageBack"/>
        <result column="BusinessTerm" jdbcType="DATE" property="businessTerm"/>
        <result column="Trade" jdbcType="VARCHAR" property="trade"/>
        <result column="LegIDImage1" jdbcType="VARCHAR" property="legIDImage1"/>
        <result column="LegIDImage2" jdbcType="VARCHAR" property="legIDImage2"/>
        <result column="legal_img_front" jdbcType="VARCHAR" property="legalImgFront"/>
        <result column="legal_img_back" jdbcType="VARCHAR" property="legalImgBack"/>
    </resultMap>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        GrpNo
        , CGrpNo, GrpName, GrpAddRess, ZipCode, UnifiedsociCode, GrpIdType, GrpIdNo,GrpNatureType,
    GrpType, AccName, grpBankCode, grpBankAccNo, Peoples, CorporationMan, telphone, regaddress,
    Email, Clientno,
    grpTypeStartDate,grpTypeEndDate,grpEstablishDate,grpScaleType,
    sociologyPlanSign,registeredCapital,grpCategory,
    Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime,grpIDImage1,grpIDImage2,legIDImage1,legIDImage2,
    BusinessTerm, Trade
    </sql>
    <update id="updateImagePath" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            update fcgrpinfo
            <set>
                <if test="item.grpIDImage1 != null">
                    grpIDImage1 = #{item.grpIDImage1},
                </if>
                <if test="item.grpIDImage2 != null">
                    grpIDImage2 =#{item.grpIDImage2},
                </if>
                <if test="item.legIDImage1 != null">
                    legIDImage1 = #{item.legIDImage1},
                </if>
                <if test="item.legIDImage2 != null">
                    legIDImage2 = #{item.legIDImage2}
                </if>
            </set>
            where grpNo = #{item.grpNo}
        </foreach>
    </update>
    <update id="updateHrImagePath" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            update fcgrpcontact
            <set>
                <if test="item.idImage1 != null">
                    idImage1 = #{item.idImage1},
                </if>
                <if test="item.idImage2 != null">
                    idImage2 =#{item.idImage2}
                </if>
            </set>
            where grpNo in (select grpno from fcgrpinfo where unifiedsociCode=#{item.unifiedsociCode})
            and contactType='01'
        </foreach>
    </update>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select *
        from fcgrpinfo
        where GrpNo = #{grpNo,jdbcType=VARCHAR}
    </select>

    <select id="getAllGrpHrInfo" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.HrRegist">
        select
        idImage1,idImage2,unifiedsociCode
        from fchrregisttemp
        where (idImage1 LIKE concat(concat('data','%'))
        or idImage2 LIKE concat(concat('data','%')))
        <if test=" unifiedsociCode != null and unifiedsociCode != '' ">
            and unifiedsociCode = #{unifiedsociCode,jdbcType=VARCHAR}
        </if>
        <if test=" grpIdNo != null and grpIdNo != '' ">
            and grpIdNo = #{grpIdNo,jdbcType=VARCHAR}
        </if>
        limit 10
    </select>

    <select id="getAllGrpInfo" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCGrpInfo">
        select
        grpNo,grpIDImage1,grpIDImage2,legIDImage1,legIDImage2
        from fcgrpinfo
        where (grpIDImage1 LIKE concat(concat('data','%'))
        or grpIDImage2 LIKE concat(concat('data','%'))
        or legIDImage1 LIKE concat(concat('data','%'))
        or legIDImage2 LIKE concat(concat('data','%')))
        <if test='grpNo != null and grpNo != "" '>
            and grpno = #{grpNo}
        </if>
        limit 10
    </select>

    <select id="selectGrpInfoImageNull" resultType="com.sinosoft.eflex.model.FCGrpInfo">
        select grpNo,
               grpIDImage1,
               grpIDImage2,
               legIDImage1,
               legIDImage2,
               grp_image_front as grpImageFront,
               grp_image_back  as grpImageBack,
               legal_img_front as legalImgFront,
               legal_img_back  as legalImgBack
        from fcgrpinfo
        where grp_image_front is null
           or grp_image_back is null
            and legal_img_front is null
            and legal_img_back is null
    </select>


    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fcgrpinfo
        where GrpNo = #{grpNo,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FCGrpInfo">
        insert into fcgrpinfo (GrpNo, CGrpNo, GrpName,
                               GrpAddRess, ZipCode, UnifiedsociCode,
                               GrpIdType, GrpIdNo, GrpType,GrpNatureType,
                               AccName, grpBankCode, grpBankAccNo,
                               Peoples, CorporationMan, telphone,
                               regaddress, Email, Clientno, Operator,
                               grpTypeStartDate, grpTypeEndDate, grpEstablishDate, grpScaleType,
                               sociologyPlanSign, registeredCapital, grpCategory,
                               OperatorCom, MakeDate,
                               MakeTime, ModifyDate, ModifyTime, grpIDImage1, grpIDImage2, grp_image_front,
                               grp_image_back,
                               BusinessTerm, Trade, legIDImage1, legIDImage2, legal_img_front, legal_img_back,
                               LegID,
                               LegIDType,
                               LegSex,
                               LegNationality,
                               LegBirthday,
                               LegIDStartDate,
                               LegIDEndDate, grpRegisterAddress, businesses)
        values (#{grpNo,jdbcType=VARCHAR}, #{CGrpNo,jdbcType=VARCHAR}, #{grpName,jdbcType=VARCHAR},
                #{grpAddRess,jdbcType=VARCHAR}, #{zipCode,jdbcType=VARCHAR}, #{unifiedsociCode,jdbcType=VARCHAR},
                #{grpIdType,jdbcType=VARCHAR}, #{grpIdNo,jdbcType=VARCHAR}, #{grpType,jdbcType=VARCHAR},#{grpNatureType,jdbcType=VARCHAR},
                #{accName,jdbcType=VARCHAR}, #{grpBankCode,jdbcType=VARCHAR}, #{grpBankAccNo,jdbcType=VARCHAR},
                #{peoples,jdbcType=INTEGER}, #{corporationMan,jdbcType=VARCHAR}, #{telphone,jdbcType=VARCHAR},
                #{regaddress,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{clientno,jdbcType=VARCHAR},
                #{operator,jdbcType=VARCHAR},
                #{grpTypeStartDate,jdbcType=DATE}, #{grpTypeEndDate,jdbcType=DATE}, #{grpEstablishDate,jdbcType=DATE},
                #{grpScaleType,jdbcType=VARCHAR},
                #{sociologyPlanSign,jdbcType=VARCHAR}, #{registeredCapital,jdbcType=DATE},
                #{grpCategory,jdbcType=VARCHAR},
                #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE},
                #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR},
                #{grpIDImage1,jdbcType=VARCHAR}, #{grpIDImage2,jdbcType=VARCHAR}, #{grpImageFront,jdbcType=VARCHAR},
                #{grpImageBack,jdbcType=VARCHAR},
                #{businessTerm,jdbcType=DATE}, #{trade,jdbcType=VARCHAR}, #{legIDImage1,jdbcType=VARCHAR},
                #{legIDImage2,jdbcType=VARCHAR}, #{legalImgFront,jdbcType=VARCHAR},
                #{legalImgBack,jdbcType=VARCHAR},
                #{legID,jdbcType=VARCHAR},
                #{legIDType,jdbcType=VARCHAR},
                #{legSex,jdbcType=VARCHAR},
                #{legNationality,jdbcType=VARCHAR},
                #{legBirthday,jdbcType=DATE},
                #{legIDStartDate,jdbcType=DATE},
                #{legIDEndDate,jdbcType=DATE},
                #{grpRegisterAddress,jdbcType=VARCHAR},
                #{businesses,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCGrpInfo">
        insert into fcgrpinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="grpNo != null">
                GrpNo,
            </if>
            <if test="CGrpNo != null">
                CGrpNo,
            </if>
            <if test="grpName != null">
                GrpName,
            </if>
            <if test="grpAddRess != null">
                GrpAddRess,
            </if>
            <if test="zipCode != null">
                ZipCode,
            </if>
            <if test="unifiedsociCode != null">
                UnifiedsociCode,
            </if>
            <if test="grpIdType != null">
                GrpIdType,
            </if>
            <if test="grpIdNo != null">
                GrpIdNo,
            </if>
            <if test="grpType != null">
                GrpType,
            </if>
            <if test="accName != null">
                AccName,
            </if>
            <if test="grpBankCode != null">
                grpBankCode,
            </if>
            <if test="grpBankAccNo != null">
                grpBankAccNo,
            </if>
            <if test="peoples != null">
                Peoples,
            </if>
            <if test="corporationMan != null">
                CorporationMan,
            </if>
            <if test="telphone != null">
                telphone,
            </if>
            <if test="regaddress != null">
                regaddress,
            </if>
            <if test="email != null">
                Email,
            </if>
            <if test="clientno != null">
                Clientno,
            </if>
            <if test="businessTerm != null">
                BusinessTerm,
            </if>
            <if test="trade != null">
                Trade,
            </if>
            <if test="grpTypeStartDate != null">
                GrpTypeStartDate,
            </if>
            <if test="grpTypeEndDate != null">
                GrpTypeEndDate,
            </if>
            <if test="grpEstablishDate != null">
                GrpEstablishDate,
            </if>
            <if test="grpScaleType != null">
                GrpScaleType,
            </if>
            <if test="sociologyPlanSign != null">
                SociologyPlanSign,
            </if>
            <if test="registeredCapital != null">
                RegisteredCapital,
            </if>
            <if test="grpCategory != null">
                GrpCategory,
            </if>
            <if test="legIDImage1 != null">
                LegIDImage1,
            </if>
            <if test="legIDImage2 != null">
                LegIDImage2,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="grpNo != null">
                #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="CGrpNo != null">
                #{CGrpNo,jdbcType=VARCHAR},
            </if>
            <if test="grpName != null">
                #{grpName,jdbcType=VARCHAR},
            </if>
            <if test="grpAddRess != null">
                #{grpAddRess,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="unifiedsociCode != null">
                #{unifiedsociCode,jdbcType=VARCHAR},
            </if>
            <if test="grpIdType != null">
                #{grpIdType,jdbcType=VARCHAR},
            </if>
            <if test="grpIdNo != null">
                #{grpIdNo,jdbcType=VARCHAR},
            </if>
            <if test="grpType != null">
                #{grpType,jdbcType=VARCHAR},
            </if>
            <if test="accName != null">
                #{accName,jdbcType=VARCHAR},
            </if>
            <if test="grpBankCode != null">
                #{grpBankCode,jdbcType=VARCHAR},
            </if>
            <if test="grpBankAccNo != null">
                #{grpBankAccNo,jdbcType=VARCHAR},
            </if>
            <if test="peoples != null">
                #{peoples,jdbcType=INTEGER},
            </if>
            <if test="corporationMan != null">
                #{corporationMan,jdbcType=VARCHAR},
            </if>
            <if test="telphone != null">
                #{telphone,jdbcType=VARCHAR},
            </if>
            <if test="regaddress != null">
                #{regaddress,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="clientno != null">
                #{clientno,jdbcType=VARCHAR},
            </if>
            <if test="businessTerm != null">
                #{businessTerm,jdbcType=DATE},
            </if>
            <if test="trade != null">
                #{trade,jdbcType=VARCHAR},
            </if>
            <if test="grpTypeStartDate != null">
                #{grpTypeStartDate,jdbcType=VARCHAR},
            </if>
            <if test="grpTypeEndDate != null">
                #{grpTypeEndDate,jdbcType=VARCHAR},
            </if>
            <if test='grpEstablishDate != null'>
                <if test='grpEstablishDate == ""'>
                    null,
                </if>
                <if test='grpEstablishDate != ""'>
                    #{grpEstablishDate},
                </if>
            </if>
            <if test="grpScaleType != null">
                #{grpScaleType,jdbcType=VARCHAR},
            </if>
            <if test="sociologyPlanSign != null">
                #{sociologyPlanSign,jdbcType=VARCHAR},
            </if>
            <if test="registeredCapital != null">
                #{registeredCapital,jdbcType=VARCHAR},
            </if>
            <if test="grpCategory != null">
                #{grpCategory,jdbcType=VARCHAR},
            </if>
            <if test="legIDImage1 != null">
                #{legIDImage1,jdbcType=VARCHAR},
            </if>
            <if test="legIDImage2 != null">
                #{legIDImage2,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByExampleSelective" parameterType="map">
        update fcgrpinfo
        <set>
            <if test="record.grpNo != null">
                GrpNo = #{record.grpNo,jdbcType=VARCHAR},
            </if>
            <if test="record.CGrpNo != null">
                CGrpNo = #{record.CGrpNo,jdbcType=VARCHAR},
            </if>
            <if test="record.grpName != null">
                GrpName = #{record.grpName,jdbcType=VARCHAR},
            </if>
            <if test="record.grpAddRess != null">
                GrpAddRess = #{record.grpAddRess,jdbcType=VARCHAR},
            </if>
            <if test="record.zipCode != null">
                ZipCode = #{record.zipCode,jdbcType=VARCHAR},
            </if>
            <if test="record.unifiedsociCode != null">
                UnifiedsociCode = #{record.unifiedsociCode,jdbcType=VARCHAR},
            </if>
            <if test="record.grpIdType != null">
                GrpIdType = #{record.grpIdType,jdbcType=VARCHAR},
            </if>
            <if test="record.grpIdNo != null">
                GrpIdNo = #{record.grpIdNo,jdbcType=VARCHAR},
            </if>
            <if test="record.grpType != null">
                GrpType = #{record.grpType,jdbcType=VARCHAR},
            </if>
            <if test="record.accName != null">
                AccName = #{record.accName,jdbcType=VARCHAR},
            </if>
            <if test="record.grpBankCode != null">
                grpBankCode = #{record.grpBankCode,jdbcType=VARCHAR},
            </if>
            <if test="record.grpBankAccNo != null">
                grpBankAccNo = #{record.grpBankAccNo,jdbcType=VARCHAR},
            </if>
            <if test="record.peoples != null">
                Peoples = #{record.peoples,jdbcType=INTEGER},
            </if>
            <if test="record.corporationMan != null">
                CorporationMan = #{record.corporationMan,jdbcType=VARCHAR},
            </if>
            <if test="record.telphone != null">
                telphone = #{record.telphone,jdbcType=VARCHAR},
            </if>
            <if test="record.regaddress != null">
                regaddress = #{record.regaddress,jdbcType=VARCHAR},
            </if>
            <if test="record.email != null">
                Email = #{record.email,jdbcType=VARCHAR},
            </if>
            <if test="record.clientno != null">
                Clientno = #{record.clientno,jdbcType=VARCHAR},
            </if>
            <if test="record.businessTerm != null">
                BusinessTerm = #{record.businessTerm,jdbcType=DATE},
            </if>
            <if test="record.trade != null">
                Trade = #{record.trade,jdbcType=VARCHAR},
            </if>
            <if test="record.operator != null">
                Operator = #{record.operator,jdbcType=VARCHAR},
            </if>
            <if test="record.operatorCom != null">
                OperatorCom = #{record.operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="record.makeDate != null">
                MakeDate = #{record.makeDate,jdbcType=DATE},
            </if>
            <if test="record.makeTime != null">
                MakeTime = #{record.makeTime,jdbcType=VARCHAR},
            </if>
            <if test="record.modifyDate != null">
                ModifyDate = #{record.modifyDate,jdbcType=DATE},
            </if>
            <if test="record.modifyTime != null">
                ModifyTime = #{record.modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update fcgrpinfo
        set GrpNo = #{record.grpNo,jdbcType=VARCHAR},
        CGrpNo = #{record.CGrpNo,jdbcType=VARCHAR},
        GrpName = #{record.grpName,jdbcType=VARCHAR},
        GrpAddRess = #{record.grpAddRess,jdbcType=VARCHAR},
        ZipCode = #{record.zipCode,jdbcType=VARCHAR},
        UnifiedsociCode = #{record.unifiedsociCode,jdbcType=VARCHAR},
        GrpIdType = #{record.grpIdType,jdbcType=VARCHAR},
        GrpIdNo = #{record.grpIdNo,jdbcType=VARCHAR},
        GrpType = #{record.grpType,jdbcType=VARCHAR},
        AccName = #{record.accName,jdbcType=VARCHAR},
        grpBankCode = #{record.grpBankCode,jdbcType=VARCHAR},
        grpBankAccNo = #{record.grpBankAccNo,jdbcType=VARCHAR},
        Peoples = #{record.peoples,jdbcType=INTEGER},
        CorporationMan = #{record.corporationMan,jdbcType=VARCHAR},
        telphone = #{record.telphone,jdbcType=VARCHAR},
        regaddress = #{record.regaddress,jdbcType=VARCHAR},
        Email = #{record.email,jdbcType=VARCHAR},
        Clientno = #{record.clientno,jdbcType=VARCHAR},
        clientName = #{record.clientName,jdbcType=VARCHAR},
        BusinessTerm = #{record.businessTerm,jdbcType=DATE},
        Trade = #{record.trade,jdbcType=VARCHAR},
        Operator = #{record.operator,jdbcType=VARCHAR},
        OperatorCom = #{record.operatorCom,jdbcType=VARCHAR},
        MakeDate = #{record.makeDate,jdbcType=DATE},
        MakeTime = #{record.makeTime,jdbcType=VARCHAR},
        ModifyDate = #{record.modifyDate,jdbcType=DATE},
        ModifyTime = #{record.modifyTime,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCGrpInfo">
        update fcgrpinfo
        <set>
            <if test="CGrpNo != null">
                CGrpNo = #{CGrpNo,jdbcType=VARCHAR},
            </if>
            <if test="grpName != null">
                GrpName = #{grpName,jdbcType=VARCHAR},
            </if>
            <if test="grpAddRess != null">
                GrpAddRess = #{grpAddRess,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                ZipCode = #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="unifiedsociCode != null">
                UnifiedsociCode = #{unifiedsociCode,jdbcType=VARCHAR},
            </if>
            <if test="grpIdType != null">
                GrpIdType = #{grpIdType,jdbcType=VARCHAR},
            </if>
            <if test="grpIdNo != null">
                GrpIdNo = #{grpIdNo,jdbcType=VARCHAR},
            </if>
            <if test="grpType != null">
                GrpType = #{grpType,jdbcType=VARCHAR},
            </if>
            <if test="grpNatureType != null">
                GrpNatureType = #{grpNatureType,jdbcType=VARCHAR},
            </if>
            <if test='grpIDImage1 != null'>
                grpIDImage1 = #{grpIDImage1,jdbcType=LONGVARCHAR},
            </if>
            grpIDImage2 = #{grpIDImage2,jdbcType=LONGVARCHAR},
            <if test='legIDImage1 != null'>
                legIDImage1 = #{legIDImage1,jdbcType=LONGVARCHAR},
            </if>
            <if test='legIDImage2 != null'>
                legIDImage2 = #{legIDImage2,jdbcType=LONGVARCHAR},
            </if>
            <if test='grpImageFront != null'>
                grp_image_front = #{grpImageFront,jdbcType=LONGVARCHAR},
            </if>
            <if test='grpImageBack != null'>
                grp_image_back = #{grpImageBack,jdbcType=LONGVARCHAR},
            </if>
            <if test='legalImgFront != null'>
                legal_img_front = #{legalImgFront,jdbcType=LONGVARCHAR},
            </if>
            <if test='legalImgBack != null'>
                legal_img_back = #{legalImgBack,jdbcType=LONGVARCHAR},
            </if>
            <if test='grpTypeStartDate != null and grpTypeStartDate != ""'>
                grpTypeStartDate = #{grpTypeStartDate,jdbcType=VARCHAR},
            </if>
            <if test='grpTypeEndDate != null and grpTypeEndDate != ""'>
                grpTypeEndDate = #{grpTypeEndDate,jdbcType=VARCHAR},
            </if>
            <if test="accName != null">
                AccName = #{accName,jdbcType=VARCHAR},
            </if>
            <if test="grpBankCode != null">
                grpBankCode = #{grpBankCode,jdbcType=VARCHAR},
            </if>
            <if test="grpBankAccNo != null">
                grpBankAccNo = #{grpBankAccNo,jdbcType=VARCHAR},
            </if>
            <if test="peoples != null">
                Peoples = #{peoples,jdbcType=INTEGER},
            </if>
            <if test="corporationMan != null">
                CorporationMan = #{corporationMan,jdbcType=VARCHAR},
            </if>
            <if test="telphone != null">
                telphone = #{telphone,jdbcType=VARCHAR},
            </if>
            <if test="regaddress != null">
                regaddress = #{regaddress,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                Email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="clientno != null">
                Clientno = #{clientno,jdbcType=VARCHAR},
            </if>
            <if test="clientName != null">
                clientName = #{clientName,jdbcType=VARCHAR},
            </if>
            <if test="businessTerm != null">
                BusinessTerm = #{businessTerm,jdbcType=DATE},
            </if>
            <if test="trade != null">
                Trade = #{trade,jdbcType=VARCHAR},
            </if>
            <if test="grpScaleType != null">
                GrpScaleType = #{grpScaleType,jdbcType=VARCHAR},
            </if>
            <if test='registeredCapital != null and registeredCapital != ""'>
                RegisteredCapital = #{registeredCapital,jdbcType=VARCHAR},
            </if>
            <if test='grpEstablishDate != null'>
                <if test='grpEstablishDate == ""'>
                    grpEstablishDate = null,
                </if>
                <if test='grpEstablishDate != ""'>
                    grpEstablishDate = #{grpEstablishDate},
                </if>
            </if>
            <if test='sociologyPlanSign != null and sociologyPlanSign != ""'>
                sociologyPlanSign = #{sociologyPlanSign,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test='legID != null and legID != ""'>
                LegID = #{legID,jdbcType=VARCHAR},
            </if>
            <if test='legIDType != null and legIDType != ""'>
                LegIDType = #{legIDType,jdbcType=VARCHAR},
            </if>
            <if test='legSex != null and legSex != ""'>
                LegSex = #{legSex,jdbcType=VARCHAR},
            </if>
            <if test='legNationality != null and legNationality != ""'>
                LegNationality = #{legNationality,jdbcType=VARCHAR},
            </if>
            <if test='legBirthday != null and legBirthday != ""'>
                LegBirthday = #{legBirthday,jdbcType=DATE},
            </if>
            <if test='legIDStartDate != null and legIDStartDate != ""'>
                LegIDStartDate = #{legIDStartDate,jdbcType=DATE},
            </if>
            <if test='legIDEndDate != null and legIDEndDate != ""'>
                LegIDEndDate = #{legIDEndDate,jdbcType=DATE},
            </if>
            <if test='businesses != null and businesses != ""'>
                businesses = #{businesses,jdbcType=VARCHAR},
            </if>
        </set>
        where GrpNo = #{grpNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCGrpInfo">
        update fcgrpinfo
        set CGrpNo          = #{CGrpNo,jdbcType=VARCHAR},
            GrpName         = #{grpName,jdbcType=VARCHAR},
            GrpAddRess      = #{grpAddRess,jdbcType=VARCHAR},
            ZipCode         = #{zipCode,jdbcType=VARCHAR},
            UnifiedsociCode = #{unifiedsociCode,jdbcType=VARCHAR},
            GrpIdType       = #{grpIdType,jdbcType=VARCHAR},
            GrpIdNo         = #{grpIdNo,jdbcType=VARCHAR},
            GrpType         = #{grpType,jdbcType=VARCHAR},
            AccName         = #{accName,jdbcType=VARCHAR},
            grpBankCode     = #{grpBankCode,jdbcType=VARCHAR},
            grpBankAccNo    = #{grpBankAccNo,jdbcType=VARCHAR},
            Peoples         = #{peoples,jdbcType=INTEGER},
            CorporationMan  = #{corporationMan,jdbcType=VARCHAR},
            telphone        = #{telphone,jdbcType=VARCHAR},
            regaddress      = #{regaddress,jdbcType=VARCHAR},
            Email           = #{email,jdbcType=VARCHAR},
            Clientno        = #{clientno,jdbcType=VARCHAR},
            BusinessTerm    = #{businessTerm,jdbcType=DATE},
            Trade           = #{trade,jdbcType=VARCHAR},
            Operator        = #{operator,jdbcType=VARCHAR},
            OperatorCom     = #{operatorCom,jdbcType=VARCHAR},
            MakeDate        = #{makeDate,jdbcType=DATE},
            MakeTime        = #{makeTime,jdbcType=VARCHAR},
            ModifyDate      = #{modifyDate,jdbcType=DATE},
            ModifyTime      = #{modifyTime,jdbcType=VARCHAR}
        where GrpNo = #{grpNo,jdbcType=VARCHAR}
    </update>

    <select id="selectGrpInfo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcgrpinfo
        where
        UnifiedsociCode = #{unifiedsociCode,jdbcType=VARCHAR}
    </select>

    <!-- 查询企业信息 -->
    <select id="selectTranscodingGrpInfo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select GrpNo,
               CGrpNo,
               GrpName,
               GrpAddRess,
               ZipCode,
               UnifiedsociCode,
               GrpIdType,
               GrpNatureType,
               GrpIdNo,
               (select codeName from Fdcode where codeKey = fc.GrpType and codeType = 'GrpNature')  as GrpType,
               AccName,
               (select codeName from Fdcode where codeKey = fc.grpBankCode and codeType = 'Bank')   as grpBankCode,
               (select codeName from Fdcode where codeKey = fc.Trade and codeType = 'BusinessType') as Trade,
               fc.grpTypeStartDate,
               fc.grpTypeEndDate,
               grpBankAccNo,
               Peoples,
               GrpIDImage1,
               GrpIDImage2,
               LegIDImage1,
               LegIDImage2,
               grp_image_front,
               grp_image_back,
               legal_img_front,
               legal_img_back,
               CorporationMan,
               businesses,
               telphone,
               regaddress,
               Email,
               Clientno,
               clientName,
               BusinessTerm,
               Operator,
               OperatorCom,
               MakeDate,
               MakeTime,
               ModifyDate,
               ModifyTime
        from fcgrpinfo fc
        where GrpNo = #{grpNo,jdbcType=VARCHAR}
    </select>

    <select id="selectGrpInfoList" parameterType="java.lang.String" resultMap="BaseResultMap">
        select GrpNo,
               CGrpNo,
               GrpName,
               GrpAddRess,
               ZipCode,
               UnifiedsociCode,
               GrpIdType,
               GrpIdNo,
               (select codeName from Fdcode where codeKey = fc.GrpType and codeType = 'GrpNature')  as GrpType,
               AccName,
               (select codeName from Fdcode where codeKey = fc.grpBankCode and codeType = 'Bank')   as grpBankCode,
               (select codeName from Fdcode where codeKey = fc.Trade and codeType = 'BusinessType') as Trade,
               grpBankAccNo,
               Peoples,
               CorporationMan,
               telphone,
               regaddress,
               Email,
               Clientno,
               BusinessTerm,
               Operator,
               OperatorCom,
               MakeDate,
               MakeTime,
               ModifyDate,
               ModifyTime,
               GrpIDImage1,
               GrpIDImage2
        from fcgrpinfo fc
        where GrpNo = #{grpNo,jdbcType=VARCHAR}
    </select>
    <select id="selectGrpInfoListTemp" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.HrRegist">
        select
        registSN,corporationMan, LegSex, LegID,LegIDType,LegBirthday
        ,LegNationality,grpName,zipCode,grpAddress,grpRegisterAddress,nativeplace,LegIDStartDate,LegIDEndDate,
        unifiedsociCode,grpType,GrpNatureType,accName,grpBankcode,grpBankaccno,birthDay,grpIdType,grpIdNo,businesses,
        telphone,regAddress,clientNo,idImage1,idImage2,legIDImage1,legIDImage2,grpIDImage1,
        grpIDImage2,checkStatus,
        id_card_back as idCardBack,
        id_card_front as idCardFront,
        grp_image_front as grpImageFront ,
        grp_image_back as grpImageBack,
        legal_img_front as legalImgFront,
        legal_img_back as legalImgBack,
        grpTypeStartDate,grpTypeEndDate,grpEstablishDate,grpScaleType,sociologyPlanSign,registeredCapital,grpCategory,
        operatorCom, operator,
        makeDate, makeTime, modifyDate,trade,businessTerm,peoples,mobilePhone,
        modifyTime
        from fchrregisttemp fc
        where 1=1
        <if test="registSN != null and registSN !=''">
            and registSN = #{registSN}
        </if>
    </select>
    <select id="getGrpInfoByContactNo" parameterType="com.sinosoft.eflex.model.FCContactGrpRelaKey"
            resultType="com.sinosoft.eflex.model.FCgrplistInfo">
        SELECT fr.grpNo, fc.grpname
        FROM FCContactGrpRela fr
                 LEFT JOIN fcgrpinfo fc ON fr.grpNo = fc.grpNo
        WHERE contactNo = #{contactNo,jdbcType=VARCHAR}
          AND fr.LockState = '0'
    </select>

    <select id="getGrpListByNameOrCode" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FCGrpInfo">
        SELECT
        g.GrpNo,
        g.GrpName,
        g.UnifiedsociCode,
        g.grpIdtype,
        g.grpIdNo,
        g.CorporationMan,
        g.GrpType,
        g.Trade,
        a.codeName grpTypeName,
        b.codeName tradeName
        FROM fcgrpinfo g
        LEFT JOIN fdcode a ON g.grpType = a.CodeKey AND a.CodeType='grpNature'
        LEFT JOIN fdcode b ON g.trade = b.codeKey AND b.codeType = 'BusinessType'
        LEFT JOIN fdagentinfo d on g.ClientNo = d.AgentCode
        where 1 = 1
        <if test="manageCom != null and manageCom != ''">
            and d.manageCom like #{manageCom}"%"
        </if>
        <if test="grpNo != null and grpNo != ''">
            AND g.GrpNo = #{grpNo,jdbcType=VARCHAR}
        </if>
        <if test=" grpName != null and grpName != '' ">
            AND g.GrpName LIKE concat(concat('%',#{grpName},'%'))
        </if>
        <if test=" grpIdType != null and grpIdType != '' ">
            AND g.GrpIdType = #{grpIdType}
        </if>
        <if test=" grpIdNo != null and grpIdNo != '' ">
            AND g.grpIdNo LIKE #{grpIdNo}
        </if>
        <!--<if test=" unifiedsociCode != null and unifiedsociCode != '' ">
            AND UnifiedsociCode LIKE concat(concat('%',#{unifiedsociCode},'%'))
          </if>-->
        order by g.MakeDate
    </select>
    <select id="selectGrpNoByNameAndCode" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT GrpNo
        FROM fcgrpinfo
        WHERE UnifiedsociCode = #{UnifiedsociCode,jdbcType=VARCHAR}
          and GrpName = #{GrpName,jdbcType=VARCHAR}
    </select>
    <update id="updateclientNoBygrpNo" parameterType="java.util.Map">
        update fcgrpinfo
        set Clientno = #{clientno,jdbcType=VARCHAR}
        where GrpNo = #{grpNo,jdbcType=VARCHAR}
    </update>
    <select id="selectPlanByUnifiedsociCode" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT fd.`PlanCode`
        FROM fcensure fc
                 INNER JOIN fcgrpinfo fo ON fc.`GrpNo` = fo.`GrpNo`
                 INNER JOIN FcDailyInsureRiskDetailInfo fd ON fc.`EnsureCode` = fd.`EnsureCode`
        WHERE fo.`UnifiedsociCode` = #{unifiedsociCode}
          AND fd.`PlanState` = '0'
          and fc.`PlanType` = '2'
          and fc.`EnsureState` = '015'
    </select>
    <select id="getImageInfo" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT b.idImage1,
               b.idImage2,
               c.grpIDImage1,
               c.grpIDImage2,
               c.legIDImage1,
               c.legIDImage2
        FROM fccontactgrprela a,
             fcgrpcontact b,
             fcgrpinfo c
        WHERE a.GrpNo = c.GrpNo
          AND a.ContactNo = b.ContactNo
          AND a.contactType = '01'
          AND a.GrpNo = #{grpNo,jdbcType=VARCHAR}
    </select>
    <select id="selectByEnsureCode" resultMap="BaseResultMap">
        select f1.*
        from fcgrpinfo f1
                 inner join fcensure f2 on f1.GrpNo = f2.GrpNo
            and f2.EnsureCode = #{ensureCode}
    </select>

    <select id="getGrpListBygrpName" parameterType="java.util.List" resultType="java.util.Map">
        select GrpNo as grpNo,GrpName as grpName,UnifiedsociCode as unifiedsociCode,CorporationMan as
        corporationMan,GrpType as grpType,Trade as trade,
        (select CodeName FROM fdcode WHERE CodeType = 'GrpNature' AND CodeKey = grpType) as grpTypeName,
        (select CodeName FROM fdcode WHERE CodeType = 'BusinessType' AND CodeKey = Trade) as tradeName
        from fcgrpinfo
        where 1=1
        <if test="grpName != null and grpName != ''">
            and GrpName LIKE CONCAT(CONCAT('%',#{grpName},'%'))
        </if>
    </select>
    <select id="selectGrpInfo1" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCGrpInfo">
        SELECT a.GrpNo,
               a.CGrpNo,
               a.GrpName,
               a.GrpAddRess,
               a.ZipCode,
               a.UnifiedsociCode,
               a.GrpIdType,
               b.codeName           grpIdTypeName,
               a.GrpIdNo,
               a.GrpType,
               a.GrpNatureType,
               c.codeName           grpTypeName,
               a.AccName,
               a.grpBankCode,
               a.grpBankAccNo,
               a.Peoples,
               a.CorporationMan,
               a.telphone,
               a.regaddress,
               a.Email,
               a.Clientno,
               a.clientName,
               a.grpTypeStartDate,
               a.grpTypeEndDate,
               a.grpEstablishDate,
               a.grpScaleType,
               d.codeName           grpScaleTypeName,
               a.sociologyPlanSign,
               e.codeName           sociologyPlanSignName,
               a.registeredCapital,
               a.grpRegisterAddress,
               a.grpCategory,
               f.codeName           grpCategoryName,
               a.Operator,
               a.OperatorCom,
               a.MakeDate,
               a.MakeTime,
               a.ModifyDate,
               a.ModifyTime,
               a.grpIDImage1,
               a.grpIDImage2,
               a.legIDImage1,
               a.legIDImage2,
               a.grp_image_front as grpImageFront,
               a.grp_image_back  as grpImageBack,
               a.legal_img_front as legalImgFront,
               a.legal_img_back  as legalImgBack,
               a.BusinessTerm,
               a.Trade,
               g.codeName           tradeName,
               a.legID,
               a.legSex,
               a.legNationality,
               a.legIDType,
               a.legBirthday,
               a.legIDStartDate,
               a.legIDEndDate,
               a.businesses
        FROM fcgrpinfo a
                 LEFT JOIN fdcode b ON a.grpIDtype = b.codeKey AND b.codeType = 'grpIdType'
                 LEFT JOIN fdcode c ON a.grpType = c.codeKey AND c.codeType = 'GrpNature'
                 LEFT JOIN fdcode d ON a.grpScaleType = d.codeKey AND d.codeType = 'ScaleType'
                 LEFT JOIN fdcode e ON a.sociologyPlanSign = e.codeKey AND e.codeType = 'SociologyPlanSign'
                 LEFT JOIN fdcode f ON a.grpCategory = f.codeKey AND f.codeType = 'CustomerCategory'
                 LEFT JOIN fdcode g ON a.Trade = g.codeKey AND g.codeType = 'BusinessType'
        WHERE a.GrpNo = #{grpNo,jdbcType=VARCHAR}
    </select>

    <select id="selectGrpInfo2" resultType="com.sinosoft.eflex.model.FCGrpInfo">
        select *
        from fcgrpinfo
        WHERE GrpNo = #{grpNo,jdbcType=VARCHAR}
    </select>

</mapper>