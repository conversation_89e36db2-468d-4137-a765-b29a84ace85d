<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCPlanRiskMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCPlanRisk">
        <id column="EnsureCode" jdbcType="VARCHAR" property="ensureCode"/>
        <id column="PlanCode" jdbcType="VARCHAR" property="planCode"/>
        <id column="RiskCode" jdbcType="VARCHAR" property="riskCode"/>
        <result column="RiskName" jdbcType="VARCHAR" property="riskName"/>
        <result column="reinsuranceMark" jdbcType="VARCHAR" property="reinsuranceMark"/>
        <result column="GiftInsureSign" jdbcType="DOUBLE" property="giftInsureSign"/>
        <result column="OriginalPrem" jdbcType="DOUBLE" property="originalPrem"/>
        <result column="feeRatio" jdbcType="DOUBLE" property="feeRatio"/>
        <result column="commissionOrAllowanceRatio" jdbcType="DOUBLE" property="commissionOrAllowanceRatio"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        EnsureCode
        , PlanCode, RiskCode, RiskName, reinsuranceMark,protocolReading,giftInsureSign, originalPrem,feeRatio,commissionOrAllowanceRatio,
    Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
    </sql>
    <sql id="Base_Column_List1">
        EnsureCode
        , PlanCode, RiskCode, RiskName, reinsuranceMark, (feeRatio*100) as feeRatio, (commissionOrAllowanceRatio*100) as commissionOrAllowanceRatio,
    Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime
    </sql>
    <!--投保清单导出excel -->
    <resultMap type="com.sinosoft.eflex.model.FCPlanRisk" id="InsuredDetails"
               extends="BaseResultMap">
        <collection column="{ensureCode=ensureCode,planCode=PlanCode,riskCode=RiskCode}"
                    property="fcPlanRiskDuties" javaType="java.util.ArrayList"
                    ofType="com.sinosoft.eflex.model.FCPlanRiskDuty"
                    select="com.sinosoft.eflex.dao.FCPlanRiskDutyMapper.selectInsuredDetail"
                    fetchType="lazy"/>
    </resultMap>
    <!-- 投保清单导出excel -->
    <select id="selectInsuredDetail" resultMap="InsuredDetails">
        select PlanCode,
               RiskCode,
               RiskName,
               ensureCode
        from fcplanrisk
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          AND PlanCode = #{planCode,jdbcType=VARCHAR}
    </select>
    <!-- 企业计划汇总 -->
    <resultMap type="com.sinosoft.eflex.model.FCPlanRisk" id="PlanCollection"
               extends="BaseResultMap">
        <!-- 计划险种表中RiskName废弃,现查询险种信息表 -->
        <association column="RiskCode" property="riskName"
                     select="com.sinosoft.eflex.dao.FDRiskInfoMapper.selectRiskNameByRiskCode"/>
        <collection
                column="{ensureCode=EnsureCode,planCode=PlanCode,riskCode=RiskCode}"
                property="fcPlanRiskDuties" javaType="java.util.ArrayList"
                ofType="com.sinosoft.eflex.model.FCPlanRiskDuty"
                select="com.sinosoft.eflex.dao.FCPlanRiskDutyMapper.selectPlanCollection"
                fetchType="lazy"/>
    </resultMap>
    <!--计划详情查询 -->
    <resultMap type="com.sinosoft.eflex.model.FCPlanRisk" id="PlanDetail"
               extends="BaseResultMap">
        <collection column="{planCode=PlanCode,riskCode=RiskCode}"
                    property="fcPlanRiskDuties" javaType="java.util.ArrayList"
                    ofType="com.sinosoft.eflex.model.FCPlanRiskDuty"
                    select="com.sinosoft.eflex.dao.FCPlanRiskDutyMapper.selectPlanDetailList"
                    fetchType="lazy"/>
    </resultMap>
    <!-- 企业计划汇总 -->
    <select id="selectPlanCollection" resultMap="PlanCollection">
        select
        EnsureCode,PlanCode,RiskCode,RiskName
        from fcplanrisk
        <where>
            <if test="ensureCode != null and ensureCode !=''">
                EnsureCode = #{ensureCode,jdbcType=VARCHAR}
            </if>
            <if test="planCode != null and planCode !=''">
                and PlanCode = #{planCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!-- 计划详情查询 -->
    <select id="selectPlanDetailList" resultMap="PlanDetail">
        select
        PlanCode,RiskCode,RiskName,reinsuranceMark,protocolReading,feeRatio,commissionOrAllowanceRatio,giftInsureSign,originalPrem
        from fcplanrisk
        <where>
            EnsureCode = #{ensureCode,jdbcType=VARCHAR}
            <if test="planCode != null and planCode !=''">
                AND PlanCode = #{planCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>


    <select id="selectFcRiskList" parameterType="com.sinosoft.eflex.model.FCPlanRiskKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcplanrisk
        <where>
            <if test="ensureCode != null and ensureCode !=''">
                EnsureCode = #{ensureCode,jdbcType=VARCHAR}
            </if>
            <if test="planCode != null and planCode !=''">
                AND PlanCode = #{planCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>


    <select id="selectByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCPlanRiskKey"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcplanrisk
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        and
        PlanCode = #{planCode,jdbcType=VARCHAR}
        and RiskCode = #{riskCode,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCPlanRiskKey">
        delete
        from fcplanrisk
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          and PlanCode = #{planCode,jdbcType=VARCHAR}
          and RiskCode =
              #{riskCode,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FCPlanRisk">
        insert into fcplanrisk (EnsureCode, PlanCode, RiskCode,
                                RiskName, reinsuranceMark, feeRatio,
                                commissionOrAllowanceRatio, Operator, OperatorCom,
                                MakeDate, MakeTime, ModifyDate,
                                ModifyTime)
        values (#{ensureCode,jdbcType=VARCHAR}, #{planCode,jdbcType=VARCHAR}, #{riskCode,jdbcType=VARCHAR},
                #{riskName,jdbcType=VARCHAR}, #{reinsuranceMark,jdbcType=VARCHAR}, #{feeRatio,jdbcType=DOUBLE},
                #{commissionOrAllowanceRatio,jdbcType=DOUBLE}, #{operator,jdbcType=VARCHAR},
                #{operatorCom,jdbcType=VARCHAR},
                #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE},
                #{modifyTime,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCPlanRisk">
        insert into fcplanrisk
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ensureCode != null">
                EnsureCode,
            </if>
            <if test="planCode != null">
                PlanCode,
            </if>
            <if test="riskCode != null">
                RiskCode,
            </if>
            <if test="riskName != null">
                RiskName,
            </if>
            <if test="reinsuranceMark != null">
                reinsuranceMark,
            </if>
            <if test="feeRatio != null">
                feeRatio,
            </if>
            <if test="commissionOrAllowanceRatio != null">
                commissionOrAllowanceRatio,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ensureCode != null">
                #{ensureCode,jdbcType=VARCHAR},
            </if>
            <if test="planCode != null">
                #{planCode,jdbcType=VARCHAR},
            </if>
            <if test="riskCode != null">
                #{riskCode,jdbcType=VARCHAR},
            </if>
            <if test="riskName != null">
                #{riskName,jdbcType=VARCHAR},
            </if>
            <if test="reinsuranceMark != null">
                #{reinsuranceMark,jdbcType=VARCHAR},
            </if>
            <if test="feeRatio != null">
                #{feeRatio,jdbcType=DOUBLE},
            </if>
            <if test="commissionOrAllowanceRatio != null">
                #{commissionOrAllowanceRatio,jdbcType=DOUBLE},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCPlanRisk">
        update fcplanrisk
        <set>
            <if test="riskName != null">
                RiskName = #{riskName,jdbcType=VARCHAR},
            </if>
            <if test="reinsuranceMark != null">
                reinsuranceMark = #{reinsuranceMark,jdbcType=VARCHAR},
            </if>
            <if test="feeRatio != null">
                feeRatio = #{feeRatio,jdbcType=DOUBLE},
            </if>
            <if test="commissionOrAllowanceRatio != null">
                commissionOrAllowanceRatio = #{commissionOrAllowanceRatio,jdbcType=DOUBLE},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        and PlanCode = #{planCode,jdbcType=VARCHAR}
        and RiskCode = #{riskCode,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCPlanRisk">
        update fcplanrisk
        set RiskName                   = #{riskName,jdbcType=VARCHAR},
            reinsuranceMark            = #{reinsuranceMark,jdbcType=VARCHAR},
            feeRatio                   = #{feeRatio,jdbcType=DOUBLE},
            commissionOrAllowanceRatio = #{commissionOrAllowanceRatio,jdbcType=DOUBLE},
            Operator                   = #{operator,jdbcType=VARCHAR},
            OperatorCom                = #{operatorCom,jdbcType=VARCHAR},
            MakeDate                   = #{makeDate,jdbcType=DATE},
            MakeTime                   = #{makeTime,jdbcType=VARCHAR},
            ModifyDate                 = #{modifyDate,jdbcType=DATE},
            ModifyTime                 = #{modifyTime,jdbcType=VARCHAR}
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          and PlanCode = #{planCode,jdbcType=VARCHAR}
          and RiskCode = #{riskCode,jdbcType=VARCHAR}
    </update>

    <!-- 获取险种编码  排序 -->
    <select id="selectRiskCodeList" parameterType="java.lang.String" resultType="java.lang.String">
        select RiskCode
        from fcplanrisk
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          and PlanCode = #{planCode,jdbcType=VARCHAR}
        order by RiskCode
    </select>

    <select id="selectRiskList" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List1"/>
        from fcplanrisk where EnsureCode = #{ensureCode,jdbcType=VARCHAR} and PlanCode = #{planCode,jdbcType=VARCHAR}
        order by RiskCode
    </select>

    <delete id="deleteByEnsureCode" parameterType="java.lang.String">
        delete
        from fcplanrisk
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    </delete>

    <select id="selectRiskInfo" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCPlanRisk">
        select PlanCode, RiskCode
        from fcplanrisk
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          AND PlanCode = #{planCode,jdbcType=VARCHAR}
    </select>

    <insert id="insertList" parameterType="java.util.List">
        insert into fcplanrisk
        (EnsureCode, PlanCode, RiskCode,
        RiskName, Operator, OperatorCom,
        MakeDate, MakeTime, ModifyDate,
        ModifyTime)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.ensureCode,jdbcType=VARCHAR},
            #{item.planCode,jdbcType=VARCHAR},
            #{item.riskCode,jdbcType=VARCHAR},
            #{item.riskName,jdbcType=VARCHAR},
            #{item.operator,jdbcType=VARCHAR},
            #{item.operatorCom,jdbcType=VARCHAR},
            #{item.makeDate,jdbcType=DATE},
            #{item.makeTime,jdbcType=VARCHAR},
            #{item.modifyDate,jdbcType=DATE},
            #{item.modifyTime,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <insert id="insertList2" parameterType="java.util.List">
        insert into fcplanrisk
        (EnsureCode, PlanCode, RiskCode,
        RiskName, reinsuranceMark,GiftInsureSign, feeRatio ,commissionOrAllowanceRatio,Operator, OperatorCom,
        MakeDate, MakeTime, ModifyDate,
        ModifyTime)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.ensureCode,jdbcType=VARCHAR},
            #{item.planCode,jdbcType=VARCHAR},
            #{item.riskCode,jdbcType=VARCHAR},
            #{item.riskName,jdbcType=VARCHAR},
            #{item.reinsuranceMark,jdbcType=VARCHAR},
            #{item.giftInsureSign,jdbcType=VARCHAR},
            #{item.feeRatio,jdbcType=VARCHAR},
            #{item.commissionOrAllowanceRatio,jdbcType=VARCHAR},
            #{item.operator,jdbcType=VARCHAR},
            #{item.operatorCom,jdbcType=VARCHAR},
            #{item.makeDate,jdbcType=DATE},
            #{item.makeTime,jdbcType=VARCHAR},
            #{item.modifyDate,jdbcType=DATE},
            #{item.modifyTime,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 获取福利险种配置信息 -->
    <select id="getensureRiskInfo" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCPlanRisk">
        select fp.EnsureCode,
               fp.PlanCode,
               fp.RiskCode,
               fc.RiskName,
               fp.reinsuranceMark,
               (fp.feeRatio * 100)                   as feeRatio,
               (fp.commissionOrAllowanceRatio * 100) as commissionOrAllowanceRatio,
               fp.Operator,
               fp.OperatorCom,
               fp.GiftInsureSign,
               fp.OriginalPrem
        from fcplanrisk fp
                 LEFT JOIN FDRISKINFO fc ON fc.riskCode = fp.riskCode
        where ensureCode = #{ensureCode,jdbcType=VARCHAR}
        GROUP BY riskCode
    </select>
    <!-- 修改福利险种配置信息 -->
    <update id="updateEnsureRiskInfo" parameterType="com.sinosoft.eflex.model.FCPlanRisk">
        update FCPlanRisk
        <set>
            <if test="reinsuranceMark != null">
                reinsuranceMark = #{reinsuranceMark,jdbcType=VARCHAR},
            </if>
            <if test="giftInsureSign != null">
                GiftInsureSign = #{giftInsureSign,jdbcType=VARCHAR},
            </if>
            <if test="originalPrem != null">
                OriginalPrem = #{originalPrem,jdbcType=DOUBLE},
            </if>
            <if test="protocolReading != null">
                protocolReading = #{protocolReading,jdbcType=INTEGER},
            </if>
            <if test="feeRatio != null">
                feeRatio = #{feeRatio,jdbcType=DOUBLE},
            </if>
            <if test="commissionOrAllowanceRatio != null">
                commissionOrAllowanceRatio = #{commissionOrAllowanceRatio,jdbcType=DOUBLE},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        and RiskCode = #{riskCode,jdbcType=VARCHAR}
    </update>

    <select id="selectRiskInfoList" resultType="com.sinosoft.eflex.model.FCPlanRisk">
        SELECT a.PlanCode, a.RiskCode
        FROM fcplanrisk a
                 LEFT JOIN fcplanriskduty b ON b.`PlanCode` = a.`PlanCode` AND b.`Riskcode` = a.`RiskCode`
                 LEFT JOIN fdriskdutyinfo c ON c.`DutyCode` = b.`DutyCode`
        WHERE a.PlanCode = #{planCode,jdbcType=VARCHAR}
          AND a.EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        GROUP BY a.`RiskCode`
    </select>
    <select id="selectStopSaleRiskCodeInfoByEnsureCode" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.FCPlanRisk">
        select a.PlanCode, a.RiskCode
        from fcplanrisk a
                 inner join fdriskInfo b on a.RiskCode = b.riskCode and b.StopTime &lt; NOW()
        where a.ensureCode = #{ensureCode};
    </select>
    <select id="selectStopSaleRiskCodeByEnsureCode" parameterType="java.lang.String" resultType="java.lang.String">
        select distinct a.RiskCode
        from fcplanrisk a
                 inner join fdriskInfo b on a.RiskCode = b.riskCode and b.StopTime &lt; NOW()
        where a.ensureCode = #{ensureCode};
    </select>
    <select id="selectPlanList" resultType="com.sinosoft.eflex.model.FCPlanRisk">
        select PlanCode,
               RiskCode,
               RiskName,
               reinsuranceMark,
               feeRatio,
               commissionOrAllowanceRatio,
               giftInsureSign,
               originalPrem
        from fcplanrisk
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          AND PlanCode = #{planCode,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByEnsureCodeAndPlanCode" parameterType="java.lang.String">
        delete
        from fcplanrisk
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          and PlanCode = #{planCode,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteMakeingPlanRiskByEnsureCode" parameterType="java.lang.String">
        delete
        from fcplanrisk
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          and PlanCode in (select planCode from fcensureplan where EnsureCode = #{EnsureCode} and planState = '01')
    </delete>


    <update id="updateByEnsureCode" parameterType="com.sinosoft.eflex.model.FCPlanRisk">
        update FCPlanRisk
        <set>
            <if test="ensureCode != null">
                EnsureCode = #{ensureCode,jdbcType=VARCHAR}
            </if>
        </set>
        where EnsureCode = #{jEnsureCode,jdbcType=VARCHAR}
    </update>
</mapper>