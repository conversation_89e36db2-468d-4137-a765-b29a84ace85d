<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.PersonMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.Person">
    <result column="pid" jdbcType="VARCHAR" property="pid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="age" jdbcType="VARCHAR" property="age" />
    <result column="sex" jdbcType="VARCHAR" property="sex" />
    <result column="makedate" jdbcType="VARCHAR" property="makedate" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sinosoft.eflex.model.Person">
    <result column="imgstr" jdbcType="LONGVARCHAR" property="imgstr" />
  </resultMap>
  <sql id="Blob_Column_List">
    imgstr
  </sql>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.Person">
    insert into person (pid, name, age, 
      sex, makedate, imgstr
      )
    values (#{pid,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{age,jdbcType=VARCHAR}, 
      #{sex,jdbcType=VARCHAR}, #{makedate,jdbcType=VARCHAR}, #{imgstr,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.Person">
    insert into person
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pid != null">
        pid,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="age != null">
        age,
      </if>
      <if test="sex != null">
        sex,
      </if>
      <if test="makedate != null">
        makedate,
      </if>
      <if test="imgstr != null">
        imgstr,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pid != null">
        #{pid,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="age != null">
        #{age,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=VARCHAR},
      </if>
      <if test="makedate != null">
        #{makedate,jdbcType=VARCHAR},
      </if>
      <if test="imgstr != null">
        #{imgstr,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
</mapper>