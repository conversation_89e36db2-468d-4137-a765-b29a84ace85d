<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FDEdorItemMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FDEdorItem">
    <id column="edorcode" jdbcType="VARCHAR" property="edorcode" />
    <result column="edorName" jdbcType="VARCHAR" property="edorName" />
    <result column="IsValid" jdbcType="VARCHAR" property="isValid" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="makeDate" jdbcType="DATE" property="makeDate" />
    <result column="makeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="modifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="modifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    edorcode, edorName, IsValid, Operator, OperatorCom, makeDate, makeTime, modifyDate, 
    modifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fdedoritem
    where edorcode = #{edorcode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fdedoritem
    where edorcode = #{edorcode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FDEdorItem">
    insert into fdedoritem (edorcode, edorName, IsValid, 
      Operator, OperatorCom, makeDate, 
      makeTime, modifyDate, modifyTime
      )
    values (#{edorcode,jdbcType=VARCHAR}, #{edorName,jdbcType=VARCHAR}, #{isValid,jdbcType=VARCHAR}, 
      #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, 
      #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FDEdorItem">
    insert into fdedoritem
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="edorcode != null">
        edorcode,
      </if>
      <if test="edorName != null">
        edorName,
      </if>
      <if test="isValid != null">
        IsValid,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        makeDate,
      </if>
      <if test="makeTime != null">
        makeTime,
      </if>
      <if test="modifyDate != null">
        modifyDate,
      </if>
      <if test="modifyTime != null">
        modifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="edorcode != null">
        #{edorcode,jdbcType=VARCHAR},
      </if>
      <if test="edorName != null">
        #{edorName,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FDEdorItem">
    update fdedoritem
    <set>
      <if test="edorName != null">
        edorName = #{edorName,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        IsValid = #{isValid,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        makeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        makeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        modifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        modifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where edorcode = #{edorcode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FDEdorItem">
    update fdedoritem
    set edorName = #{edorName,jdbcType=VARCHAR},
      IsValid = #{isValid,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      makeDate = #{makeDate,jdbcType=DATE},
      makeTime = #{makeTime,jdbcType=VARCHAR},
      modifyDate = #{modifyDate,jdbcType=DATE},
      modifyTime = #{modifyTime,jdbcType=VARCHAR}
    where edorcode = #{edorcode,jdbcType=VARCHAR}
  </update>
  <select id="findEdorInfo" resultType="com.sinosoft.eflex.model.FDEdorItem">
    select
    <include refid="Base_Column_List" />
    from fdedoritem
  </select>
</mapper>