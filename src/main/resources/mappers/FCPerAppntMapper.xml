<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCPerAppntMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCPerAppnt">
    <id column="PerAppNo" jdbcType="VARCHAR" property="perAppNo" />
    <result column="GrpNo" jdbcType="VARCHAR" property="grpNo" />
    <result column="PerNo" jdbcType="VARCHAR" property="perNo" />
    <result column="Name" jdbcType="VARCHAR" property="name" />
    <result column="Sex" jdbcType="VARCHAR" property="sex" />
    <result column="IDType" jdbcType="VARCHAR" property="IDType" />
    <result column="IDNo" jdbcType="VARCHAR" property="IDNo" />
    <result column="BirthDay" jdbcType="DATE" property="birthDay" />
    <result column="OccupationType" jdbcType="VARCHAR" property="occupationType" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    PerAppNo, GrpNo, PerNo, Name, Sex, IDType, IDNo, BirthDay, OccupationType, OperatorCom, 
    Operator, MakeTime, MakeDate, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcperappnt
    where PerAppNo = #{perAppNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fcperappnt
    where PerAppNo = #{perAppNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCPerAppnt">
    insert into fcperappnt (PerAppNo, GrpNo, PerNo, 
      Name, Sex, IDType, 
      IDNo, BirthDay, OccupationType, 
      OperatorCom, Operator, MakeTime, 
      MakeDate, ModifyDate, ModifyTime
      )
    values (#{perAppNo,jdbcType=VARCHAR}, #{grpNo,jdbcType=VARCHAR}, #{perNo,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{sex,jdbcType=VARCHAR}, #{IDType,jdbcType=VARCHAR}, 
      #{IDNo,jdbcType=VARCHAR}, #{birthDay,jdbcType=DATE}, #{occupationType,jdbcType=VARCHAR}, 
      #{operatorCom,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{makeTime,jdbcType=VARCHAR}, 
      #{makeDate,jdbcType=DATE}, #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCPerAppnt">
    insert into fcperappnt
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="perAppNo != null">
        PerAppNo,
      </if>
      <if test="grpNo != null">
        GrpNo,
      </if>
      <if test="perNo != null">
        PerNo,
      </if>
      <if test="name != null">
        Name,
      </if>
      <if test="sex != null">
        Sex,
      </if>
      <if test="IDType != null">
        IDType,
      </if>
      <if test="IDNo != null">
        IDNo,
      </if>
      <if test="birthDay != null">
        BirthDay,
      </if>
      <if test="occupationType != null">
        OccupationType,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="perAppNo != null">
        #{perAppNo,jdbcType=VARCHAR},
      </if>
      <if test="grpNo != null">
        #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="perNo != null">
        #{perNo,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=VARCHAR},
      </if>
      <if test="IDType != null">
        #{IDType,jdbcType=VARCHAR},
      </if>
      <if test="IDNo != null">
        #{IDNo,jdbcType=VARCHAR},
      </if>
      <if test="birthDay != null">
        #{birthDay,jdbcType=DATE},
      </if>
      <if test="occupationType != null">
        #{occupationType,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCPerAppnt">
    update fcperappnt
    <set>
      <if test="grpNo != null">
        GrpNo = #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="perNo != null">
        PerNo = #{perNo,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        Name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        Sex = #{sex,jdbcType=VARCHAR},
      </if>
      <if test="IDType != null">
        IDType = #{IDType,jdbcType=VARCHAR},
      </if>
      <if test="IDNo != null">
        IDNo = #{IDNo,jdbcType=VARCHAR},
      </if>
      <if test="birthDay != null">
        BirthDay = #{birthDay,jdbcType=DATE},
      </if>
      <if test="occupationType != null">
        OccupationType = #{occupationType,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where PerAppNo = #{perAppNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCPerAppnt">
    update fcperappnt
    set GrpNo = #{grpNo,jdbcType=VARCHAR},
      PerNo = #{perNo,jdbcType=VARCHAR},
      Name = #{name,jdbcType=VARCHAR},
      Sex = #{sex,jdbcType=VARCHAR},
      IDType = #{IDType,jdbcType=VARCHAR},
      IDNo = #{IDNo,jdbcType=VARCHAR},
      BirthDay = #{birthDay,jdbcType=DATE},
      OccupationType = #{occupationType,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where PerAppNo = #{perAppNo,jdbcType=VARCHAR}
  </update>
  <delete id="deleteByKey" parameterType="java.lang.String">
    delete from
    fcperappnt
    where PerNo = #{perNo,jdbcType=VARCHAR}
  </delete>
</mapper>