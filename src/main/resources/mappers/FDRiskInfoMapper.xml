<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FDRiskInfoMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FDRiskInfo">
        <id column="RiskCode" jdbcType="VARCHAR" property="riskCode"/>
        <result column="RiskName" jdbcType="VARCHAR" property="riskName"/>
        <result column="RiskType" jdbcType="VARCHAR" property="riskType"/>
        <result column="BonusGetMode" jdbcType="VARCHAR" property="bonusGetMode"/>
        <result column="InsuranceTerms" jdbcType="VARCHAR" property="insuranceTerms"/>
        <result column="ProductDescription" jdbcType="VARCHAR" property="productDescription"/>
        <result column="InsuranceTermsName" jdbcType="VARCHAR" property="insuranceTermsName"/>
        <result column="ProductDescriptionName" jdbcType="VARCHAR" property="productDescriptionName"/>
        <result column="PdfUrl" jdbcType="VARCHAR" property="pdfUrl"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
        <result column="RiskRange" jdbcType="VARCHAR" property="riskRange"/>
    </resultMap>
    <sql id="Base_Column_List">
        RiskCode
        , RiskName, RiskType, BonusGetMode, InsuranceTerms,ProductDescription, InsuranceTermsName,ProductDescriptionName,PdfUrl,Operator, OperatorCom, MakeDate, MakeTime,
    ModifyDate, ModifyTime
    </sql>

    <select id="selectRiskInfo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdriskinfo
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdriskinfo
        where RiskCode = #{riskCode,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fdriskinfo
        where RiskCode = #{riskCode,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FDRiskInfo">
        insert into fdriskinfo (RiskCode, RiskName, RiskType,
                                BonusGetMode, Operator, OperatorCom,
                                MakeDate, MakeTime, ModifyDate,
                                ModifyTime)
        values (#{riskCode,jdbcType=VARCHAR}, #{riskName,jdbcType=VARCHAR}, #{riskType,jdbcType=VARCHAR},
                #{bonusGetMode,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR},
                #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE},
                #{modifyTime,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FDRiskInfo">
        insert into fdriskinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="riskCode != null">
                RiskCode,
            </if>
            <if test="riskName != null">
                RiskName,
            </if>
            <if test="riskType != null">
                RiskType,
            </if>
            <if test="bonusGetMode != null">
                BonusGetMode,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="riskCode != null">
                #{riskCode,jdbcType=VARCHAR},
            </if>
            <if test="riskName != null">
                #{riskName,jdbcType=VARCHAR},
            </if>
            <if test="riskType != null">
                #{riskType,jdbcType=VARCHAR},
            </if>
            <if test="bonusGetMode != null">
                #{bonusGetMode,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FDRiskInfo">
        update fdriskinfo
        <set>
            <if test="riskName != null">
                RiskName = #{riskName,jdbcType=VARCHAR},
            </if>
            <if test="riskType != null">
                RiskType = #{riskType,jdbcType=VARCHAR},
            </if>
            <if test="bonusGetMode != null">
                BonusGetMode = #{bonusGetMode,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </set>
        where RiskCode = #{riskCode,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FDRiskInfo">
        update fdriskinfo
        set RiskName     = #{riskName,jdbcType=VARCHAR},
            RiskType     = #{riskType,jdbcType=VARCHAR},
            BonusGetMode = #{bonusGetMode,jdbcType=VARCHAR},
            Operator     = #{operator,jdbcType=VARCHAR},
            OperatorCom  = #{operatorCom,jdbcType=VARCHAR},
            MakeDate     = #{makeDate,jdbcType=DATE},
            MakeTime     = #{makeTime,jdbcType=VARCHAR},
            ModifyDate   = #{modifyDate,jdbcType=DATE},
            ModifyTime   = #{modifyTime,jdbcType=VARCHAR}
        where RiskCode = #{riskCode,jdbcType=VARCHAR}
    </update>
    <!-- 根据险种编码查询险种名称 -->
    <select id="selectRiskNameByRiskCode" parameterType="String" resultType="String">
        select RiskName
        from fdriskinfo
        where RiskCode = #{riskCode,jdbcType=VARCHAR}
    </select>

    <select id="selectByRiskType" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FDRiskInfo">
        select RiskType
        from fdriskinfo
        where RiskType = #{riskType,jdbcType=VARCHAR}
    </select>

    <select id="selectByEnsureCode" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FDRiskInfo">
        SELECT RiskCode,RiskName,RiskRange FROM fdriskinfo
        WHERE RiskCode !='16380'
        and RiskCode != '14110'
        AND StopTime > now()
        AND RiskCode NOT IN (
        SELECT
        IF(RiskCode = '15070',
        IF((SELECT RiskCode FROM fdriskdutyinfo WHERE DutyCode IN ('GD0050','GD0051','GD0052','GD0053','GD0054') AND
        DutyCode NOT IN (SELECT DutyCode FROM FcDutyAmountGrade WHERE EnsureCode = #{ensureCode} GROUP BY DutyCode)
        LIMIT 1)IS NULL,RiskCode,''),
        RiskCode)
        FROM FcPlanRiskInfo WHERE EnsureCode = #{ensureCode})
        <if test=" riskCode != null and riskCode != '' ">
            AND RiskCode = #{riskCode}
        </if>
        <if test=" riskName != null and riskName != '' ">
            AND riskName LIKE CONCAT('%',#{riskName},'%')
        </if>
    </select>

    <select id="selectByEnsureCodeAndPlanCode" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.FDRiskInfo">
        SELECT RiskCode,RiskName,RiskRange FROM fdriskinfo
        WHERE
        -- RiskCode !='16380'
        -- and
        RiskCode != '14110'
        AND StopTime > now()
        AND RiskCode NOT IN (
        SELECT
        IF(RiskCode = '15070',
        IF((SELECT RiskCode FROM fdriskdutyinfo WHERE DutyCode IN ('GD0050','GD0051','GD0052','GD0053','GD0054') AND
        DutyCode NOT IN (SELECT DutyCode FROM FcDutyAmountGrade WHERE EnsureCode = #{ensureCode} GROUP BY DutyCode)
        LIMIT 1)IS NULL,RiskCode,''),
        RiskCode)
        FROM fcplanrisk WHERE EnsureCode = #{ensureCode}
        <if test=" planCode != null and planCode != '' ">
            AND planCode = #{planCode}
        </if>
        )
        <if test="list != null and list.size() > 0">
            and RiskCode in
            <foreach collection="list" open="(" close=")" separator="," index="index" item="item">
                ${item}
            </foreach>
        </if>
    </select>

    <select id="selectRiskCode" resultType="com.sinosoft.eflex.model.FDRiskInfo">
        select distinct riskCode
        from fdriskinfo
    </select>
    <select id="selectRiskCodeByPlanType" parameterType="java.lang.String" resultType="java.util.Map">
        select RiskCode, RiskName
        from fdriskinfo
        where PlanType = #{planType,jdbcType=VARCHAR}
          and StopTime > now()
    </select>
    <select id="selectByRiskCode" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FdRIskPlanInfo">
        select *
        from fdriskplaninfo
        where RiskCode = #{riskCode,jdbcType=VARCHAR}
    </select>
    <select id="selectRiskNameAndRiskCodeByEnsureCode" parameterType="java.lang.String" resultType="java.util.Map">
        select a.RiskCode, b.RiskName
        from fcDailyInsureRiskDetailInfo a
                 left join fdriskinfo b
                           on a.RiskCode = b.RiskCode
        where ensureCode = #{ensureCode,jdbcType=VARCHAR} limit 1
    </select>
    <select id="selectStopSaleRiskCode" resultType="java.lang.String">
        select RiskCode
        from fdriskinfo
        where StopTime &lt; now();
    </select>
</mapper>