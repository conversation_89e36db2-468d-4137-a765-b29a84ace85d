<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FcPlanConfigMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FcPlanConfig">
    <id column="SerialNo" jdbcType="VARCHAR" property="serialNo" />
    <result column="EnsureCode" jdbcType="VARCHAR" property="ensureCode" />
    <result column="PlanCode" jdbcType="VARCHAR" property="planCode" />
    <result column="GrpNo" jdbcType="VARCHAR" property="grpNo" />
    <result column="ConfigNo" jdbcType="VARCHAR" property="configNo" />
    <result column="ConfigValue" jdbcType="VARCHAR" property="configValue" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    SerialNo, EnsureCode, PlanCode, GrpNo, ConfigNo, ConfigValue, Operator, OperatorCom, 
    MakeDate, MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fcplanconfig
    where SerialNo = #{serialNo,jdbcType=VARCHAR}
  </select>
  <delete id="delete" parameterType="java.lang.String" >
    delete from fcplanconfig
    where PlanCode = #{planCode,jdbcType=VARCHAR} and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FcPlanConfig">
    insert into fcplanconfig (SerialNo, EnsureCode, PlanCode, 
      GrpNo, ConfigNo, ConfigValue, 
      Operator, OperatorCom, MakeDate, 
      MakeTime, ModifyDate, ModifyTime
      )
    values
      <foreach collection ="planConfig" item="planConfig" index= "index" separator =",">
        (#{planConfig.serialNo,jdbcType=VARCHAR}, #{planConfig.ensureCode,jdbcType=VARCHAR}, #{planConfig.planCode,jdbcType=VARCHAR},
        #{planConfig.grpNo,jdbcType=VARCHAR}, #{planConfig.configNo,jdbcType=VARCHAR}, #{planConfig.configValue,jdbcType=VARCHAR},
        #{planConfig.operator,jdbcType=VARCHAR}, #{planConfig.operatorCom,jdbcType=VARCHAR}, #{planConfig.makeDate,jdbcType=DATE},
        #{planConfig.makeTime,jdbcType=VARCHAR}, #{planConfig.modifyDate,jdbcType=DATE}, #{planConfig.modifyTime,jdbcType=VARCHAR}
        )
      </foreach >
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FcPlanConfig">
    insert into fcplanconfig
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="serialNo != null">
        SerialNo,
      </if>
      <if test="ensureCode != null">
        EnsureCode,
      </if>
      <if test="planCode != null">
        PlanCode,
      </if>
      <if test="grpNo != null">
        GrpNo,
      </if>
      <if test="configNo != null">
        ConfigNo,
      </if>
      <if test="configValue != null">
        ConfigValue,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="serialNo != null">
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="ensureCode != null">
        #{ensureCode,jdbcType=VARCHAR},
      </if>
      <if test="planCode != null">
        #{planCode,jdbcType=VARCHAR},
      </if>
      <if test="grpNo != null">
        #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="configNo != null">
        #{configNo,jdbcType=VARCHAR},
      </if>
      <if test="configValue != null">
        #{configValue,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FcPlanConfig">
    update fcplanconfig
    <set>
      <if test="ensureCode != null">
        EnsureCode = #{ensureCode,jdbcType=VARCHAR},
      </if>
      <if test="planCode != null">
        PlanCode = #{planCode,jdbcType=VARCHAR},
      </if>
      <if test="grpNo != null">
        GrpNo = #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="configNo != null">
        ConfigNo = #{configNo,jdbcType=VARCHAR},
      </if>
      <if test="configValue != null">
        ConfigValue = #{configValue,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where SerialNo = #{serialNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FcPlanConfig">
    update fcplanconfig
    set EnsureCode = #{ensureCode,jdbcType=VARCHAR},
      PlanCode = #{planCode,jdbcType=VARCHAR},
      GrpNo = #{grpNo,jdbcType=VARCHAR},
      ConfigNo = #{configNo,jdbcType=VARCHAR},
      ConfigValue = #{configValue,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where SerialNo = #{serialNo,jdbcType=VARCHAR}
  </update>

  <select id="getFCPlanConfigList" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FcPlanConfig">
    SELECT * FROM fcplanconfig WHERE  1=1
    <if test=" ensureCode != null and ensureCode!='' " >
      AND EnsureCode = #{ensureCode}
    </if>
    <if test=" planCode != null and planCode!='' " >
      AND PlanCode = #{planCode}
    </if>
    <if test=" grpNo != null and grpNo!='' " >
      AND GrpNo = #{grpNo}
    </if>
  </select>

  <select id="getConfigExplain" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FcPlanConfig">
    SELECT * FROM fcplanconfig WHERE  1=1
    <if test=" ensureCode != null and ensureCode!='' " >
      AND EnsureCode = #{ensureCode}
    </if>
    <if test=" planCode != null and planCode!='' " >
      AND PlanCode = #{planCode}
    </if>
    <if test=" grpNo != null and grpNo!='' " >
      AND GrpNo = #{grpNo}
    </if>
    and ConfigNo in("001","002","003")
  </select>

  <select id="getFCPlanConfigByPlanCode" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FcPlanConfig">
    SELECT * FROM fcplanconfig WHERE  1=1 AND PlanCode = #{planCode} AND EnsureCode = #{ensureCode} AND  ConfigNo in("005","006","007","008","009","010")
  </select>

  <select id="getFCPlanConfigMaxMinAge" resultType="com.sinosoft.eflex.model.FcPlanConfig">
    SELECT * FROM fcplanconfig WHERE  1=1 AND EnsureCode = #{ensureCode} AND PlanCode = #{planCode} AND  ConfigNo in("011","012","013","014","015","016","017","018","019","020","021")
  </select>
    <select id="selectFCPlanConfigByEnsureCode" resultType="com.sinosoft.eflex.model.FcPlanConfig">
      SELECT * FROM fcplanconfig WHERE  1=1 AND EnsureCode = #{ensureCode}  group by    ConfigValue
    </select>

</mapper>