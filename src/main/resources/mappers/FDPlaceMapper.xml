<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FDPlaceMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FDPlace">
    <id column="PlaceCode" jdbcType="VARCHAR" property="placeCode" />
    <result column="PlaceType" jdbcType="VARCHAR" property="placeType" />
    <result column="PlaceName" jdbcType="VARCHAR" property="placeName" />
    <result column="UpplaceCode" jdbcType="VARCHAR" property="upplaceCode" />
    <result column="OtherSign" jdbcType="VARCHAR" property="otherSign" />
    <result column="CodeDesc" jdbcType="VARCHAR" property="codeDesc" />
  </resultMap>
  <sql id="Base_Column_List">
    PlaceCode, PlaceType, PlaceName, UpplaceCode, OtherSign, CodeDesc
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fdplace
    where PlaceCode = #{placeCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fdplace
    where PlaceCode = #{placeCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FDPlace">
    insert into fdplace (PlaceCode, PlaceType, PlaceName, 
      UpplaceCode, OtherSign, CodeDesc
      )
    values (#{placeCode,jdbcType=VARCHAR}, #{placeType,jdbcType=VARCHAR}, #{placeName,jdbcType=VARCHAR}, 
      #{upplaceCode,jdbcType=VARCHAR}, #{otherSign,jdbcType=VARCHAR}, #{codeDesc,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FDPlace">
    insert into fdplace
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="placeCode != null">
        PlaceCode,
      </if>
      <if test="placeType != null">
        PlaceType,
      </if>
      <if test="placeName != null">
        PlaceName,
      </if>
      <if test="upplaceCode != null">
        UpplaceCode,
      </if>
      <if test="otherSign != null">
        OtherSign,
      </if>
      <if test="codeDesc != null">
        CodeDesc,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="placeCode != null">
        #{placeCode,jdbcType=VARCHAR},
      </if>
      <if test="placeType != null">
        #{placeType,jdbcType=VARCHAR},
      </if>
      <if test="placeName != null">
        #{placeName,jdbcType=VARCHAR},
      </if>
      <if test="upplaceCode != null">
        #{upplaceCode,jdbcType=VARCHAR},
      </if>
      <if test="otherSign != null">
        #{otherSign,jdbcType=VARCHAR},
      </if>
      <if test="codeDesc != null">
        #{codeDesc,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FDPlace">
    update fdplace
    <set>
      <if test="placeType != null">
        PlaceType = #{placeType,jdbcType=VARCHAR},
      </if>
      <if test="placeName != null">
        PlaceName = #{placeName,jdbcType=VARCHAR},
      </if>
      <if test="upplaceCode != null">
        UpplaceCode = #{upplaceCode,jdbcType=VARCHAR},
      </if>
      <if test="otherSign != null">
        OtherSign = #{otherSign,jdbcType=VARCHAR},
      </if>
      <if test="codeDesc != null">
        CodeDesc = #{codeDesc,jdbcType=VARCHAR},
      </if>
    </set>
    where PlaceCode = #{placeCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FDPlace">
    update fdplace
    set PlaceType = #{placeType,jdbcType=VARCHAR},
      PlaceName = #{placeName,jdbcType=VARCHAR},
      UpplaceCode = #{upplaceCode,jdbcType=VARCHAR},
      OtherSign = #{otherSign,jdbcType=VARCHAR},
      CodeDesc = #{codeDesc,jdbcType=VARCHAR}
    where PlaceCode = #{placeCode,jdbcType=VARCHAR}
  </update>

  <select id="selectCodeType" parameterType="java.util.Map" resultType="java.util.Map">
    select
    PlaceType,PlaceName,UpplaceCode,PlaceCode
    from fdplace
    <where>
      1=1
      <if test="placeType != null and placeType !=''"  >
        and PlaceType = #{placeType,jdbcType=VARCHAR}
      </if>
      <if test="upplaceCode != null and upplaceCode !=''">
        and UpplaceCode = #{upplaceCode,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
    <select id="selectPlaceNameByPlaceCode" resultType="java.lang.String">
      select PlaceName from fdplace where PlaceCode = #{placeCode}
    </select>
    <select id="selectLike" resultType="java.lang.String">
      select PlaceName from fdplace where PlaceCode  like concat('%',#{substring},'%')  limit 1
    </select>
</mapper>