<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCContractMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCContract">
    <id column="ContractSN" jdbcType="VARCHAR" property="contractSN" />
    <result column="GrpNo" jdbcType="VARCHAR" property="grpNo" />
    <result column="PersonId" jdbcType="VARCHAR" property="personId" />
    <result column="Location" jdbcType="VARCHAR" property="location" />
    <result column="Address" jdbcType="VARCHAR" property="address" />
    <result column="Zipcode" jdbcType="VARCHAR" property="zipcode" />
    <result column="Receiver" jdbcType="VARCHAR" property="receiver" />
    <result column="TelPhone" jdbcType="VARCHAR" property="telPhone" />
    <result column="Remark" jdbcType="VARCHAR" property="remark" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
    <result column="CustomType" jdbcType="VARCHAR" property="customType" />
    <result column="EnsureCode" jdbcType="VARCHAR" property="ensureCode" />
    <result column="ApplicantName" jdbcType="VARCHAR" property="applicantName" />
  </resultMap>
  <sql id="Base_Column_List">
    ContractSN, GrpNo, PersonId, Location, Address, Zipcode, Receiver, TelPhone, Remark, 
    Operator, OperatorCom, MakeDate, MakeTime, ModifyDate, ModifyTime, CustomType, EnsureCode,ApplicantName
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fccontract
    where ContractSN = #{contractSN,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fccontract
    where ContractSN = #{contractSN,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FCContract">
    insert into fccontract (ContractSN, GrpNo, PersonId, 
      Location, Address, Zipcode, 
      Receiver, TelPhone, Remark, 
      Operator, OperatorCom, MakeDate, 
      MakeTime, ModifyDate, ModifyTime, 
      CustomType, EnsureCode,ApplicantName)
    values (#{contractSN,jdbcType=VARCHAR}, #{grpNo,jdbcType=VARCHAR}, #{personId,jdbcType=VARCHAR}, 
      #{location,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{zipcode,jdbcType=VARCHAR}, 
      #{receiver,jdbcType=VARCHAR}, #{telPhone,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{operator,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE}, 
      #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, #{modifyTime,jdbcType=VARCHAR}, 
      #{customType,jdbcType=VARCHAR}, #{ensureCode,jdbcType=VARCHAR},#{applicantName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCContract">
    insert into fccontract
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="contractSN != null">
        ContractSN,
      </if>
      <if test="grpNo != null">
        GrpNo,
      </if>
      <if test="personId != null">
        PersonId,
      </if>
      <if test="location != null">
        Location,
      </if>
      <if test="address != null">
        Address,
      </if>
      <if test="zipcode != null">
        Zipcode,
      </if>
      <if test="receiver != null">
        Receiver,
      </if>
      <if test="telPhone != null">
        TelPhone,
      </if>
      <if test="remark != null">
        Remark,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
      <if test="customType != null">
        CustomType,
      </if>
      <if test="ensureCode != null">
        EnsureCode,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="contractSN != null">
        #{contractSN,jdbcType=VARCHAR},
      </if>
      <if test="grpNo != null">
        #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="personId != null">
        #{personId,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="zipcode != null">
        #{zipcode,jdbcType=VARCHAR},
      </if>
      <if test="receiver != null">
        #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="telPhone != null">
        #{telPhone,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
      <if test="customType != null">
        #{customType,jdbcType=VARCHAR},
      </if>
      <if test="ensureCode != null">
        #{ensureCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCContract">
    update fccontract
    <set>
      <if test="grpNo != null">
        GrpNo = #{grpNo,jdbcType=VARCHAR},
      </if>
      <if test="personId != null">
        PersonId = #{personId,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        Location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        Address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="zipcode != null">
        Zipcode = #{zipcode,jdbcType=VARCHAR},
      </if>
      <if test="receiver != null">
        Receiver = #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="telPhone != null">
        TelPhone = #{telPhone,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        Remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
      <if test="customType != null">
        CustomType = #{customType,jdbcType=VARCHAR},
      </if>
      <if test="ensureCode != null">
        EnsureCode = #{ensureCode,jdbcType=VARCHAR},
      </if>
    </set>
    where ContractSN = #{contractSN,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCContract">
    update fccontract
    set GrpNo = #{grpNo,jdbcType=VARCHAR},
      PersonId = #{personId,jdbcType=VARCHAR},
      Location = #{location,jdbcType=VARCHAR},
      Address = #{address,jdbcType=VARCHAR},
      Zipcode = #{zipcode,jdbcType=VARCHAR},
      Receiver = #{receiver,jdbcType=VARCHAR},
      TelPhone = #{telPhone,jdbcType=VARCHAR},
      Remark = #{remark,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      CustomType = #{customType,jdbcType=VARCHAR},
      EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    where ContractSN = #{contractSN,jdbcType=VARCHAR}
  </update>
  <select id="selectContract" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FCContract">
    select
    <include refid="Base_Column_List" />
    from fccontract
    <where>
      1=1
      <if test="ensureCode != null">
       and  EnsureCode = #{ensureCode,jdbcType=VARCHAR}
      </if>
      <if test="customType != null">
        and  CustomType = #{customType,jdbcType=VARCHAR}
      </if>
      <if test="grpNo != null">
        and  GrpNo = #{grpNo,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
</mapper>