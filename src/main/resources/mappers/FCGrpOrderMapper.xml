<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCGrpOrderMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCGrpOrder">
        <id column="GrpOrderNo" jdbcType="VARCHAR" property="grpOrderNo" />
		<result column="GrpOrderType" jdbcType="VARCHAR" property="grpOrderType" />
		<result column="EnsureCode" jdbcType="VARCHAR" property="ensureCode"/>
        <result column="GrpNo" jdbcType="VARCHAR" property="grpNo" />
		<result column="GrpAppNo" jdbcType="VARCHAR" property="grpAppNo" />
		<result column="GrpOrderStatus" jdbcType="VARCHAR" property="grpOrderStatus" />
		<result column="CommitDate" jdbcType="DATE" property="commitDate" />
		<result column="EffectDate" jdbcType="DATE" property="effectDate" />
		<result column="PrtNo" jdbcType="VARCHAR" property="prtNo" />
		<result column="GrpContNo" jdbcType="VARCHAR" property="grpContNo" />
		<result column="CostNo" jdbcType="VARCHAR" property="costNo" />
		<result column="Operator" jdbcType="VARCHAR" property="operator" />
		<result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
		<result column="MakeDate" jdbcType="DATE" property="makeDate" />
		<result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
		<result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
		<result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
	</resultMap>
	<!-- 投保清单导出excel -->
	<resultMap type="com.sinosoft.eflex.model.FCGrpOrder" id="InsuredDetails"
		extends="BaseResultMap">
		<association property="fcGrpApplicant" column="{grpAppNo=GrpAppNo}"
			select="com.sinosoft.eflex.dao.FCGrpApplicantMapper.selectInsuredDetail"
			fetchType="lazy" />
		<collection property="fcOrders" column="{ensureCode=ensureCode,grpOrderNo=GrpOrderNo,grpNo=GrpNo,ensureType=ensureType,garName=garName,garSex=garSex,department=department,stuName=stuName,planName=planName,planObject=planObject}"
			javaType="java.util.ArrayList" ofType="com.sinosoft.eflex.model.FCOrder"
			select="com.sinosoft.eflex.dao.FCOrderMapper.selectInsuredDetail"
			fetchType="lazy" />
	</resultMap>
	<sql id="Base_Column_List">
		GrpOrderNo, GrpOrderType, EnsureCode, GrpNo, GrpAppNo,
		GrpOrderStatus,
		CommitDate,
		EffectDate, PrtNo, GrpContNo, CostNo,
		Operator, OperatorCom, MakeDate, MakeTime,
		ModifyDate, ModifyTime
	</sql>
	<select id="selectByPrimaryKey" parameterType="java.lang.String"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from fcgrporder
		where GrpOrderNo = #{grpOrderNo,jdbcType=VARCHAR}
	</select>
	<select id="getPayType" parameterType="java.lang.String"
		resultType="java.util.Map">
		select b.codeName,a.configValue from fcensureconfig a left join fdcode b on a.configvalue=b.codekey and b.codetype='PaymentType' 
	    where a.grpno=#{grpNo,jdbcType=VARCHAR} 
	    and a.ensurecode=#{ensureCode,jdbcType=VARCHAR}
	    and a.configno='008'
	</select>
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from
		fcgrporder
		where GrpOrderNo = #{grpOrderNo,jdbcType=VARCHAR}
	</delete>
	<insert id="insert" parameterType="com.sinosoft.eflex.model.FCGrpOrder">
		insert into fcgrporder
		(GrpOrderNo, GrpOrderType, EnsureCode,
		GrpNo, GrpAppNo, GrpOrderStatus,
		CommitDate, EffectDate, PrtNo,
		GrpContNo, CostNo, Operator,
		OperatorCom, MakeDate, MakeTime,
		ModifyDate, ModifyTime)
		values
		(#{grpOrderNo,jdbcType=VARCHAR}, #{grpOrderType,jdbcType=VARCHAR},
		#{ensureCode,jdbcType=VARCHAR},
		#{grpNo,jdbcType=VARCHAR},
		#{grpAppNo,jdbcType=VARCHAR}, #{grpOrderStatus,jdbcType=VARCHAR},
		#{commitDate,jdbcType=DATE}, #{effectDate,jdbcType=DATE},
		#{prtNo,jdbcType=VARCHAR},
		#{grpContNo,jdbcType=VARCHAR},
		#{costNo,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
		#{operatorCom,jdbcType=VARCHAR}, #{makeDate,jdbcType=DATE},
		#{makeTime,jdbcType=VARCHAR},
		#{modifyDate,jdbcType=DATE},
		#{modifyTime,jdbcType=VARCHAR})
	</insert>
	<insert id="insertFcPrtAndCoreRela" parameterType="java.util.Map">
		insert into FcPrtAndCoreRela
		(`RelaSn`, `PrtNo`, `tPrtNo`, `Status`, `Describe`, `MakeDate`, `MakeTime`)
		values
		(#{RelaSn,jdbcType=VARCHAR},
		#{PrtNo,jdbcType=VARCHAR},
		#{tPrtNo,jdbcType=VARCHAR},
		'01',
		#{describe,jdbcType=VARCHAR},
		#{currentDate,jdbcType=VARCHAR},
        #{currentTime,jdbcType=VARCHAR})
	</insert>
	<insert id="insertFcPrtAndCoreRela06" parameterType="java.util.Map">
		insert into FcPrtAndCoreRela
		(`RelaSn`, `PrtNo`, `tPrtNo`, `Status`, `Describe`, `MakeDate`, `MakeTime`)
		values
		(#{RelaSn,jdbcType=VARCHAR},
		#{PrtNo,jdbcType=VARCHAR},
		#{tPrtNo,jdbcType=VARCHAR},
		'06',
		#{describe,jdbcType=VARCHAR},
		#{currentDate,jdbcType=VARCHAR},
        #{currentTime,jdbcType=VARCHAR})
	</insert>
	<update id="updateFcPrtAndCoreRela" parameterType="java.util.Map">
		update FcPrtAndCoreRela a set
		 a.status=#{params.status,jdbcType=VARCHAR},
		 a.Describe = #{params.describe,jdbcType=VARCHAR}
		 where tPrtNo=#{params.tPrtNo,jdbcType=VARCHAR}
	</update>
	<insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCGrpOrder">
		insert into fcgrporder
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="grpOrderNo != null">
				GrpOrderNo,
			</if>
			<if test="grpOrderType != null">
				GrpOrderType,
			</if>
			<if test="ensureCode != null">
				EnsureCode,
			</if>
			<if test="grpNo != null">
				GrpNo,
			</if>
			<if test="grpAppNo != null">
				GrpAppNo,
			</if>
			<if test="grpOrderStatus != null">
				GrpOrderStatus,
			</if>
			<if test="commitDate != null">
				CommitDate,
			</if>
			<if test="effectDate != null">
				EffectDate,
			</if>
			<if test="prtNo != null">
				PrtNo,
			</if>
			<if test="grpContNo != null">
				GrpContNo,
			</if>
			<if test="costNo != null">
				CostNo,
			</if>
			<if test="operator != null">
				Operator,
			</if>
			<if test="operatorCom != null">
				OperatorCom,
			</if>
			<if test="makeDate != null">
				MakeDate,
			</if>
			<if test="makeTime != null">
				MakeTime,
			</if>
			<if test="modifyDate != null">
				ModifyDate,
			</if>
			<if test="modifyTime != null">
				ModifyTime,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="grpOrderNo != null">
				#{grpOrderNo,jdbcType=VARCHAR},
			</if>
			<if test="grpOrderType != null">
				#{grpOrderType,jdbcType=VARCHAR},
			</if>
			<if test="ensureCode != null">
				#{ensureCode,jdbcType=VARCHAR},
			</if>
			<if test="grpNo != null">
				#{grpNo,jdbcType=VARCHAR},
			</if>
			<if test="grpAppNo != null">
				#{grpAppNo,jdbcType=VARCHAR},
			</if>
			<if test="grpOrderStatus != null">
				#{grpOrderStatus,jdbcType=VARCHAR},
			</if>
			<if test="commitDate != null">
				#{commitDate,jdbcType=DATE},
			</if>
			<if test="effectDate != null">
				#{effectDate,jdbcType=DATE},
			</if>
			<if test="prtNo != null">
				#{prtNo,jdbcType=VARCHAR},
			</if>
			<if test="grpContNo != null">
				#{grpContNo,jdbcType=VARCHAR},
			</if>
			<if test="costNo != null">
				#{costNo,jdbcType=VARCHAR},
			</if>
			<if test="operator != null">
				#{operator,jdbcType=VARCHAR},
			</if>
			<if test="operatorCom != null">
				#{operatorCom,jdbcType=VARCHAR},
			</if>
			<if test="makeDate != null">
				#{makeDate,jdbcType=DATE},
			</if>
			<if test="makeTime != null">
				#{makeTime,jdbcType=VARCHAR},
			</if>
			<if test="modifyDate != null">
				#{modifyDate,jdbcType=DATE},
			</if>
			<if test="modifyTime != null">
				#{modifyTime,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>
	<update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCGrpOrder">
		update fcgrporder
		<set>
			<if test="grpOrderType != null">
				GrpOrderType = #{grpOrderType,jdbcType=VARCHAR},
			</if>
			<if test="ensureCode != null">
				EnsureCode = #{ensureCode,jdbcType=VARCHAR},
			</if>
			<if test="grpNo != null">
				GrpNo = #{grpNo,jdbcType=VARCHAR},
			</if>
			<if test="grpAppNo != null">
				GrpAppNo = #{grpAppNo,jdbcType=VARCHAR},
			</if>
			<if test="grpOrderStatus != null">
				GrpOrderStatus = #{grpOrderStatus,jdbcType=VARCHAR},
			</if>
			<if test="commitDate != null">
				CommitDate = #{commitDate,jdbcType=DATE},
			</if>
			<if test="effectDate != null">
				EffectDate = #{effectDate,jdbcType=DATE},
			</if>
			<if test="prtNo != null">
				PrtNo = #{prtNo,jdbcType=VARCHAR},
			</if>
			<if test="grpContNo != null">
				GrpContNo = #{grpContNo,jdbcType=VARCHAR},
			</if>
			<if test="costNo != null">
				CostNo = #{costNo,jdbcType=VARCHAR},
			</if>
			<if test="operator != null">
				Operator = #{operator,jdbcType=VARCHAR},
			</if>
			<if test="operatorCom != null">
				OperatorCom = #{operatorCom,jdbcType=VARCHAR},
			</if>
			<if test="makeDate != null">
				MakeDate = #{makeDate,jdbcType=DATE},
			</if>
			<if test="makeTime != null">
				MakeTime = #{makeTime,jdbcType=VARCHAR},
			</if>
			<if test="modifyDate != null">
				ModifyDate = #{modifyDate,jdbcType=DATE},
			</if>
			<if test="modifyTime != null">
				ModifyTime = #{modifyTime,jdbcType=VARCHAR},
			</if>
		</set>
		where GrpOrderNo = #{grpOrderNo,jdbcType=VARCHAR}
	</update>
	<update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCGrpOrder">
		update fcgrporder
		set GrpOrderType = #{grpOrderType,jdbcType=VARCHAR},
		EnsureCode =
		#{ensureCode,jdbcType=VARCHAR},
		GrpNo = #{grpNo,jdbcType=VARCHAR},
		GrpAppNo = #{grpAppNo,jdbcType=VARCHAR},
		GrpOrderStatus =
		#{grpOrderStatus,jdbcType=VARCHAR},
		CommitDate =
		#{commitDate,jdbcType=DATE},
		EffectDate = #{effectDate,jdbcType=DATE},
		PrtNo = #{prtNo,jdbcType=VARCHAR},
		GrpContNo =
		#{grpContNo,jdbcType=VARCHAR},
		CostNo = #{costNo,jdbcType=VARCHAR},
		Operator = #{operator,jdbcType=VARCHAR},
		OperatorCom =
		#{operatorCom,jdbcType=VARCHAR},
		MakeDate = #{makeDate,jdbcType=DATE},
		MakeTime = #{makeTime,jdbcType=VARCHAR},
		ModifyDate =
		#{modifyDate,jdbcType=DATE},
		ModifyTime =
		#{modifyTime,jdbcType=VARCHAR}
		where GrpOrderNo =
		#{grpOrderNo,jdbcType=VARCHAR}
	</update>

	<select id="selectPrtNo" parameterType="java.lang.String"
		resultType="com.sinosoft.eflex.model.FCGrpOrder">
		select
		<include refid="Base_Column_List" />
		from fcgrporder
		where
		PrtNo in (select f.PrtNo from FcPrtAndCoreRela f where f.tprtno=#{prtNo,jdbcType=VARCHAR})
	</select>

	<select id="getFcPrtAndCoreRelaByRelaSn" parameterType="java.lang.String" resultType="java.util.Map">
		SELECT * FROM fcprtandcorerela WHERE RelaSn = #{relaSn}
	</select>

	<!-- 投保清单导出excel -->
	<select id="selectInsuredDetail" parameterType="java.util.Map"
		resultMap="InsuredDetails">
		select
		case when ('${ensureType}' != '') then '${ensureType}' else '' end as ensureType,
		case when ('${garName}' != '') then '${garName}' else '' end as garName,
		case when ('${garSex}' != '') then '${garSex}' else '' end as garSex,
		case when ('${department}' != '') then '${department}' else '' end as department,
		case when ('${stuName}' != '') then '${stuName}' else '' end as stuName,
		case when ('${planName}' != '') then '${planName}' else '' end as planName,
		case when ('${planObject}' != '') then '${planObject}' else '' end as planObject,
		GrpNo,GrpAppNo,PrtNo,GrpOrderNo,ensureCode
		from fcgrporder
		where
		GrpNo = #{grpNo,jdbcType=VARCHAR}
		and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
	</select>

	<select id="selectGrpOrder" parameterType="java.lang.String" resultType="java.util.HashMap">
		select
			b.GrpOrderNo,b.EnsureCode,b.PrtNo
		from fcensure a, fcgrporder b
		where a.EnsureCode = b.EnsureCode
		and b.GrpOrderStatus = '01'
		<![CDATA[ and DATE_FORMAT(a.EndAppntDate, '%Y-%m-%d') <=  DATE_FORMAT(#{sysDate}, '%Y-%m-%d')]]>
	</select>

	<!-- 根据福利编号查询团单 -->
	<select id="selectGrpOrderByEnsureCode" parameterType="java.lang.String" resultMap="BaseResultMap">
		select
			<include refid="Base_Column_List" />
		from fcgrporder
		where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
	</select>
    <!-- 根据保单号查询团单 -->
    <select id="selectGrpOrderByGrpContNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcgrporder
        where GrpContNo = #{grpContNo,jdbcType=VARCHAR}
    </select>
    <!-- 根据福利比编号以及管理机构查询团单 -->
    <select id="selectGrpOrderByEnsureCodeAndManageCom" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.FCGrpOrder">
		select
			a.GrpOrderNo,
			a.GrpOrderType,
			a.EnsureCode,
			a.GrpNo,
			a.GrpAppNo,
		    a.GrpOrderStatus,
		    a.CommitDate,
		    a.EffectDate,
		    a.PrtNo,
		    a.GrpContNo,
		    a.CostNo,
		    a.Operator,
		    a.OperatorCom,
		    a.MakeDate,
		    a.MakeTime,
		    a.ModifyDate,
		    a.ModifyTime
		from fcgrporder a
		left join fcensure b on a.ensureCode = b.ensureCode
		left join fdagentinfo c on b.ClientNo = c.AgentCode
		where a.EnsureCode = #{ensureCode,jdbcType=VARCHAR}
	    and c.manageCom like #{manageCom}"%"
	</select>
	<select id="selectGrpOrderNo" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT fg.GrporderNo,fc.PerAppNo,fc.Orderno,fc.PerNo FROM fcgrporder fg
		INNER JOIN fcorder fc ON fc.GrpOrderNo=fg.GrpOrderNo
		<where>
			1=1
			<if test="ensureCode != null and ensureCode != ''">
				and fg.EnsureCode=#{ensureCode,jdbcType=VARCHAR}
			</if>
			<if test="grpNo != null and grpNo != '' ">
				and fg.GrpNo=#{grpNo,jdbcType=VARCHAR}
			</if>
			<if test="perNo != null and perNo!= '' ">
				and fc.PerNo=#{perNo,jdbcType=VARCHAR}
			</if>
		</where>
	</select>

	<select id="selectGrpOrderNoList" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT fg.GrpContNo as GrpContNo,fg.GrpOrderNo AS GrpOrderNo,fi.ContNo as ContNo,fc.OrderNo as orderNo,fd.PersonID as PersonID FROM fcgrporder fg
		INNER JOIN fcorder fc ON fc.GrpOrderNo = fg.GrpOrderNo
		INNER JOIN fcorderitem fi ON fi.OrderNo = fc.OrderNo
		INNER JOIN fcorderinsured fd ON fd.OrderItemNo = fi.OrderItemNo
		WHERE fg.GrpOrderStatus = '04' and fd.IDNo in (SELECT IDNo FROM fcperson WHERE PersonID = #{personId})
	</select>

	<select id="selectGrpPrem" resultType="java.util.Map" >
		SELECT fg.GrpOrderNo,fc.OrderNo,fi.GrpPrem FROM fcgrporder fg
		LEFT JOIN fcorder fc ON fg.GrpOrderNo=fc.GrpOrderNo
        RIGHT JOIN fcorderitem fi ON fc.OrderNo=fi.orderNo
        <where>
			1=1
			<if test="ensureCode != null and ensureCode != ''">
				and fg.EnsureCode=#{ensureCode,jdbcType=VARCHAR}
			</if>
			<if test="grpNo != null and grpNo != '' ">
				and fg.GrpNo=#{grpNo,jdbcType=VARCHAR}
			</if>
		</where>

	</select>
	<select id="selectGrpOrderInfo" parameterType="java.util.Map" resultType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		  from fcgrporder
		<where>
			1=1
			<if test="ensureCode != null and ensureCode != ''">
				and EnsureCode=#{ensureCode,jdbcType=VARCHAR}
			</if>
			<if test="grpNo != null and grpNo != '' ">
				and GrpNo=#{grpNo,jdbcType=VARCHAR}
			</if>
		</where>
	</select>

	<delete id="deleteByEnsureCode" parameterType="java.lang.String">
		delete from
		fcgrporder
		where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
	</delete>

	<select id="selectByGrpContNo" parameterType="java.util.Map" resultType="com.sinosoft.eflex.model.FCGrpOrder">
		  SELECT * FROM fcgrporder WHERE  GrpContNo = #{grpContNo} AND grpNo = #{grpNo}
	</select>
	<select id="selectPrtNo_Daily" parameterType="java.lang.String"
			resultType="com.sinosoft.eflex.model.FCGrpOrder">
		select
		<include refid="Base_Column_List" />
        from fcgrporder
        where
        PrtNo =#{prtNo,jdbcType=VARCHAR}
    </select>
    <select id="selectbyPrtNo" resultType="java.lang.String">
        select tPrtNo
        from fcprtandcorerela
        where PrtNo = #{prtNo}
          and status = '02'
    </select>
    <select id="selectbyPrtNo06" resultType="java.lang.String">
        select tPrtNo
        from fcprtandcorerela
        where PrtNo = #{prtNo}
          and status in ('06', '03', '01')
    </select>
    <select id="selectGrpOrderByGrpNo" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.FCGrpOrder">
        SELECT *
        FROM fcgrporder
        WHERE grpNo = #{grpNo}
    </select>
    <select id="queryGrpByGrpOrderNo" resultType="com.sinosoft.eflex.model.FCGrpOrder">
		select  * from fcgrporder where GrpOrderNo=#{grpOrderNo}
	</select>
	<select id="selectByEnsureCodeKey" resultType="com.sinosoft.eflex.model.FCGrpOrder">
		select
		<include refid="Base_Column_List" />
		from fcgrporder
		where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
	</select>
    <select id="selectGrpContNo" resultType="java.lang.String">
		select fc.GrpContNo from fcgrporder fc left join fcensure  fe on fc.EnsureCode=fe.EnsureCode  where GrpContNo  is not null and PlanType=0 and fc.MakeDate <![CDATA[ <= ]]> now()
	</select>

</mapper>