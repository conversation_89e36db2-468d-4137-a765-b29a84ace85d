<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.admin.EnsureAuditMapper">

    <select id="getEnsureAuditList" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT fp.GrpName AS grpName,
        fp.UnifiedsociCode AS unifiedsociCode,
        fp.grpIdType,
        fp.grpIdNo,
        fe.EnsureName AS ensureName,
        fe.EnsureCode AS ensureCode,
        CAST(fe.ModifyDate AS CHAR(11)) AS modifyDate,
        fe.ModifyTime as modifyTime ,
        fe.EnsureState AS ensureState,
        fe.PlanType AS planType,
        (SELECT CodeName FROM fdcode WHERE CodeKey = fe.EnsureState AND codetype = 'EnsureState') AS ensureStateName,
        (SELECT fc.ContactNo FROM fccontactgrprela fc WHERE fc.GrpNo = (SELECT GrpNo FROM fcensure WHERE EnsureCode=fe.EnsureCode) AND fc.ContactType = '01') AS contactNo,
        fe.GrpNo as grpNo
        FROM fcensure fe
        inner join fcgrpinfo fp on fe.GrpNo=fp.GrpNo
        left join fdagentinfo fa on fe.ClientNo = fa.AgentCode
        WHERE 1=1
        and fa.manageCom like #{manageCom}"%"
        <if test="ensureCode != null and ensureCode != '' ">
            and ensureCode = #{ensureCode}
        </if>
        <if test="ensureState != null and ensureState !='' ">
            and ensureState = #{ensureState}
        </if>
        <if test="ensureType != null and ensureType !='' ">
            and ensureType = #{ensureType}
        </if>
        <if test="ensureName != null and ensureName !='' ">
            and ensureName LIKE concat('%',#{ensureName},'%')
        </if>
        <if test="grpName != null and grpName !='' ">
            and grpName LIKE concat( '%',#{grpName},'%')

        </if>
        <if test="unifiedsociCode != null and unifiedsociCode !='' ">
            and unifiedsociCode =  #{unifiedsociCode}
        </if>
        <if test=" planType != null and planType != '' ">
            AND planType = #{planType}
        </if>
        <if test=' isReal == "0" '>
            AND (ensureState IN (02,03,05,06,1)
            or (ensureState In (011) AND planType =  '0'))
            order BY FIELD (ensureState,'02','03','011','05','06','1'),ensureCode DESC,modifyDate DESC ,modifyTime desc
        </if>
        <if test=' isReal == "1" '>
            AND ensureState IN (05,06,1)
            order BY FIELD (ensureState,'05','06','1'),ensureCode DESC,modifyDate DESC ,modifyTime desc
        </if>
        <if test=' isReal == "2" '>
            AND ensureState NOT IN (0)
            AND planType =  '1'
            order BY FIELD (ensureState,'07','010','011','04','08','09','02','03','05','06','1'),ensureCode DESC,modifyDate DESC ,modifyTime desc
        </if>
    </select>
    <select id="getEnsureAuditList1" parameterType="com.sinosoft.eflex.model.GetEnsureAuditsReq"
            resultType="java.util.HashMap">
        SELECT fp.GrpName AS grpName,
        fp.UnifiedsociCode AS unifiedsociCode,
        fp.grpIdType,
        fp.grpIdNo,
        fe.EnsureName AS ensureName,
        fe.EnsureCode AS ensureCode,
        CAST(fe.ModifyDate AS CHAR(11)) AS modifyDate,
        fe.ModifyTime as modifyTime ,
        fe.EnsureState AS ensureState,
        fe.PlanType AS planType,
        (SELECT CodeName FROM fdcode WHERE CodeKey = fe.EnsureState AND codetype = 'EnsureState') AS ensureStateName,
        (SELECT fc.ContactNo FROM fccontactgrprela fc WHERE fc.GrpNo = (SELECT GrpNo FROM fcensure WHERE
        EnsureCode=fe.EnsureCode) AND fc.ContactType = '01') AS contactNo,
        fe.GrpNo as grpNo
        FROM fcensure fe
        inner join fcgrpinfo fp on fe.GrpNo=fp.GrpNo

        WHERE 1=1

        <if test="ensureCode != null and ensureCode != '' ">
            and ensureCode = #{ensureCode}
        </if>
        <if test="ensureState != null and ensureState !='' ">
            and ensureState = #{ensureState}
        </if>
        <if test="ensureType != null and ensureType !='' ">
            and ensureType = #{ensureType}
        </if>
        <if test="ensureName != null and ensureName !='' ">
            and ensureName LIKE concat('%',#{ensureName},'%')
        </if>
        <if test="grpName != null and grpName !='' ">
            and grpName LIKE concat( '%',#{grpName},'%')
        </if>
        <if test="grpIdType != null and grpIdType !='' ">
            and fp.grpIdType = #{grpIdType}
        </if>
        <if test="grpIdNo != null and grpIdNo !='' ">
            and fp.grpIdNo = #{grpIdNo}
        </if>
        <if test=" planType != null and planType != '' ">
            AND planType = #{planType}
        </if>
        <if test=' isReal == "0" '>
            AND (ensureState IN (02,03,05,06,1)
            or (ensureState In (011) AND planType = '0'))
            order BY FIELD (ensureState,'02','03','011','05','06','1'),ensureCode DESC,modifyDate DESC ,modifyTime desc
        </if>
        <if test=' isReal == "1" '>
            and (CASE WHEN (ensureState != 1 and ensureState != 06) then fe.Operator != #{userNo} else 1=1 END)
            AND ensureState IN (05,06,1)
            order BY FIELD (ensureState,'05','06','1'),ensureCode DESC,modifyDate DESC ,modifyTime desc
        </if>
        <if test=' isReal == "2" '>
            AND ensureState NOT IN (0)
            AND planType = '1'
            order BY FIELD (ensureState,'07','010','011','04','08','09','02','03','05','06','1'),ensureCode
            DESC,modifyDate DESC ,modifyTime desc
        </if>
    </select>
    <select id="selectEnsureInfoList" parameterType="com.sinosoft.eflex.model.datamanage.SelectEnsureInfoListReq"
            resultType="com.sinosoft.eflex.model.datamanage.EnsureInfo">
        SELECT
        fp.GrpName AS grpName,
        fp.grpIdType,
        fp.grpIdNo,
        fe.EnsureName AS ensureName,
        fe.EnsureCode AS ensureCode,
        fe.EnsureState AS ensureState,
        fe.PlanType AS planType,
        (SELECT CodeName FROM fdcode WHERE CodeKey = fe.EnsureState AND codetype = 'EnsureState') AS ensureStateName
        FROM fcensure fe
        inner join fcgrpinfo fp on fe.GrpNo=fp.GrpNo
        left join fdagentinfo fa on fe.ClientNo = fa.AgentCode
        WHERE 1=1
        and fa.manageCom like #{manageCom}"%"
        <if test="ensureCode != null and ensureCode != '' ">
            and ensureCode = #{ensureCode}
        </if>
        <if test="ensureState != null and ensureState !='' ">
            and ensureState = #{ensureState}
        </if>
        <if test="ensureType != null and ensureType !='' ">
            and ensureType = #{ensureType}
        </if>
        <if test="ensureName != null and ensureName !='' ">
            and ensureName LIKE concat('%',#{ensureName},'%')
        </if>
        <if test="grpName != null and grpName !='' ">
            and grpName LIKE concat( '%',#{grpName},'%')
        </if>
        <if test="grpIdType != null and grpIdType !='' ">
            and fp.grpIdType = #{grpIdType}
        </if>
        <if test="grpIdNo != null and grpIdNo !='' ">
            and fp.grpIdNo = #{grpIdNo}
        </if>
        <if test='startTime != null and startTime != ""'>
            and fe.MakeDate &gt;= #{startTime}
        </if>
        <if test='endTime != null and endTime != ""'>
            and fe.MakeDate &lt;= #{endTime}
        </if>
        <!-- 暂时查询固定计划的 -->
        AND planType = '0'
        <!-- 暂时查询这几个状态的 -->
        AND ensureState IN (1,015,018)
        order BY fe.ensureCode DESC,fe.modifyDate DESC ,fe.modifyTime desc
    </select>

    <select id="getToReviewEnsure" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT fp.GrpName AS grpName,
        fp.UnifiedsociCode AS unifiedsociCode,
        fe.EnsureName AS ensureName,
        fe.EnsureCode AS ensureCode,
        CAST(fe.ModifyDate AS CHAR(11)) AS modifyDate, fe.ModifyTime as modifyTime ,
        fe.EnsureState AS ensureState,
        fe.PlanType AS ensureType,
        (SELECT CodeName FROM fdcode WHERE CodeKey = fe.EnsureState AND codetype = 'EnsureState') AS ensureStateName,
        (SELECT fc.ContactNo FROM fccontactgrprela fc WHERE fc.GrpNo = (SELECT GrpNo FROM fcensure WHERE EnsureCode=fe.EnsureCode) AND fc.ContactType = '01') AS contactNo,
        fe.GrpNo as grpNo
        FROM fcensure fe,fcgrpinfo fp WHERE
        1=1 AND fe.GrpNo=fp.GrpNo
        <if test="ensureCode != null and ensureCode != '' ">
            and ensureCode = #{ensureCode}
        </if>
        <if test="ensureState != null and ensureState !='' ">
            and ensureState = #{ensureState}
        </if>
        <if test="ensureType != null and ensureType !='' ">
            and ensureType = #{ensureType}
        </if>
        <if test="ensureName != null and ensureName !='' ">
            and ensureName LIKE concat('%',#{ensureName},'%')
        </if>
        <if test="grpName != null and grpName !='' ">
            and grpName LIKE concat( '%',#{grpName},'%')

        </if>
        <if test="unifiedsociCode != null and unifiedsociCode !='' ">
            and unifiedsociCode =  #{unifiedsociCode}
        </if>
        AND ensureState IN (05,06,1)
        order BY  ensureState ASC
    </select>

    <select id="getFCEnsureConfigList" parameterType="java.util.Map" resultType="java.util.HashMap">
        SELECT * FROM fcensureconfig WHERE  1=1
        <if test=" ensureCode != null and ensureCode!='' " >
            AND EnsureCode = #{ensureCode}
        </if>
        <if test=" grpNo != null and grpNo!='' " >
            AND GrpNo = #{grpNo}
        </if>
        AND ConfigNo in (003,005,006,008,013,014,015,016,017,018,019,022,023)
    </select>


    <select id="getFCEnsureConfigInfo" parameterType="com.sinosoft.eflex.model.FCEnsureConfig" resultType="com.sinosoft.eflex.model.FCEnsureConfig">
            SELECT ConfigNo as configNo ,ConfigValue as configValue FROM fcensureconfig WHERE  1=1
            <if test=" ensureCode != null and ensureCode!='' " >
                AND EnsureCode = #{ensureCode}
            </if>
            <if test=" grpNo != null and grpNo!='' " >
               AND GrpNo = #{grpNo}
            </if>
            <if  test=" configNo != null and configNo!='' " >
                AND ConfigNo = #{configNo}
            </if>
    </select>

    <update id="updateEnsureConfig"  parameterType="com.sinosoft.eflex.model.FCEnsureConfig">
            update fcensureconfig set ConfigValue = #{configValue}
            WHERE EnsureCode = #{ensureCode} AND GrpNo = #{grpNo} AND  ConfigNo = #{configNo,jdbcType=VARCHAR}
    </update>

    <delete id="delete" parameterType="java.lang.String">
            delete from fcensureconfig
            where EnsureCode = #{ensureCode,jdbcType=VARCHAR} AND ConfigNo not in ('008','010')
    </delete>

    <update id="updateEnsureStatus"  parameterType="com.sinosoft.eflex.model.FCEnsure">
        update fcensure set EnsureState = #{ensureState},Operator=#{operator},ModifyDate=#{modifyDate},ModifyTime=#{modifyTime}
        WHERE EnsureCode = #{ensureCode}
    </update>

    <select id="getDailyplanList" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        fc.`EnsureCode`AS EnsureCode,
        fc.`EnsureName` AS EnsureName,
        fg.`GrpName` AS GrpName,
        fc.`AppDate` AS AppDate,
        fc.`EnsureState` AS EnsureState
        FROM
        fcensure fc,
        fcgrpinfo fg
        WHERE
        1=1 and fc.PlanType='2' AND fc.GrpNo=fg.GrpNo
        <if test="ensureCode != null and ensureCode != '' ">
            and ensureCode = #{ensureCode}
        </if>
        <if test="ensureState != null and ensureState !='' ">
            and ensureState = #{ensureState}
        </if>
        <if test="grpName != null and grpName !='' ">
            and grpName LIKE concat( '%',#{grpName},'%')
        </if>
        <if test="ensureName != null and ensureName !='' ">
            and ensureName LIKE concat('%',#{ensureName},'%')
        </if>
    </select>
    <select id="getDailyplanList_orderBytime" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        fc.`EnsureCode`AS EnsureCode,
        fc.`EnsureName` AS EnsureName,
        fg.`GrpName` AS GrpName,
    date_format(fc.`AppDate`,'%Y-%m-%d') AS AppDate,
        fc.`EnsureState` AS EnsureState
        FROM
        fcensure fc,
        fcgrpinfo fg
        WHERE
        1=1 and fc.PlanType='2' AND fc.GrpNo=fg.GrpNo  and fc.EnsureState in ('012','013','016','017','014','015')
        <if test="ensureCode != null and ensureCode != '' ">
            and ensureCode = #{ensureCode}
        </if>
        <if test="ensureState != null and ensureState !='' ">
            and ensureState = #{ensureState}
        </if>
        <if test="grpName != null and grpName !='' ">
            and grpName LIKE concat( '%',#{grpName},'%')
        </if>
        <if test="ensureName != null and ensureName !='' ">
            and ensureName LIKE concat('%',#{ensureName},'%')
        </if>
        <if test="appDate != null and appDate !='' ">
            and appDate LIKE concat('%',#{appDate},'%')
        </if>
    order by fc.`AppDate` desc ,fc.GrpNo desc
    </select>
 <select id="getDailyplanList_addPrtNo" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        fc.`EnsureCode`AS EnsureCode,
        fc.`EnsureName` AS EnsureName,
        fg.`GrpName` AS GrpName,
    date_format(fc.`AppDate`,'%Y-%m-%d') AS AppDate,
        fc.`EnsureState` AS EnsureState,fc.`AuditType` AS AuditType,
        fp.`tPrtNo` as PrtNo,
        fd.`RiskCode` as RiskCode
        FROM
        fcensure fc,
        fcgrpinfo fg,
        fcgrpOrder fo,
     fcprtandcorerela fp,
     fcdailyinsureriskinfo fd
        WHERE
        1=1 and fc.PlanType='2' AND fc.GrpNo=fg.GrpNo   and fo.EnsureCode=fc.EnsureCode and fd.EnsureCode=fc.EnsureCode and fc.EnsureState in ('012','013','016','017','014','015','018','019') and fo.PrtNo=fp.PrtNo
        <if test="ensureCode != null and ensureCode != '' ">
            and fc.ensureCode = #{ensureCode}
        </if>
        <if test="ensureState != null and ensureState !='' ">
            and fc.ensureState = #{ensureState}
        </if>
        <if test="grpName != null and grpName !='' ">
            and fg.grpName LIKE concat( '%',#{grpName},'%')
        </if>
        <if test="ensureName != null and ensureName !='' ">
            and ensureName LIKE concat('%',#{ensureName},'%')
        </if>
        <if test="appDate != null and appDate !='' ">
            and fc.appDate LIKE concat('%',#{appDate},'%')
        </if>
    order by fc.`ModifyDate` desc ,fc.`ModifyTime` desc ,fc.GrpNo desc
    </select>
<select id="getDailyPlan013" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        fc.`EnsureCode`AS EnsureCode,
        fc.`EnsureName` AS EnsureName,
        fg.`GrpName` AS GrpName,
    date_format(fc.`AppDate`,'%Y-%m-%d') AS AppDate,
        fc.`EnsureState` AS EnsureState,fc.`AuditType` AS AuditType,
        fo.`PrtNo` as PrtNo
        FROM
        fcensure fc,
        fcgrpinfo fg,
        fcgrpOrder fo
        WHERE
        1=1 and fc.PlanType='2' AND fc.GrpNo=fg.GrpNo   and fo.EnsureCode=fc.EnsureCode and fc.EnsureState='013'and fc.AuditType='0'
        <if test="ensureCode != null and ensureCode != '' ">
            and fc.ensureCode = #{ensureCode}
        </if>
        <if test="grpName != null and grpName !='' ">
            and fg.grpName LIKE concat( '%',#{grpName},'%')
        </if>
        <if test="ensureName != null and ensureName !='' ">
            and ensureName LIKE concat('%',#{ensureName},'%')
        </if>
        <if test="appDate != null and appDate !='' ">
            and fc.appDate LIKE concat('%',#{appDate},'%')
        </if>
    order by fc.`ModifyDate` desc ,fc.`ModifyTime` desc ,fc.GrpNo desc
    </select>
<select id="getDailyPlan015" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        fc.`EnsureCode`AS EnsureCode,
        fc.`EnsureName` AS EnsureName,
        fg.`GrpName` AS GrpName,
    date_format(fc.`AppDate`,'%Y-%m-%d') AS AppDate,
        fc.`EnsureState` AS EnsureState,fc.`AuditType` AS AuditType,
        fo.`PrtNo` as PrtNo
        FROM
        fcensure fc,
        fcgrpinfo fg,
        fcgrpOrder fo
        WHERE
        1=1 and fc.PlanType='2' AND fc.GrpNo=fg.GrpNo   and fo.EnsureCode=fc.EnsureCode and fc.EnsureState='013'and fc.AuditType='0'

        <if test="ensureCode != null and ensureCode != '' ">
            and fc.ensureCode = #{ensureCode}
        </if>
        <if test="grpName != null and grpName !='' ">
            and fg.grpName LIKE concat( '%',#{grpName},'%')
        </if>
        <if test="ensureName != null and ensureName !='' ">
            and ensureName LIKE concat('%',#{ensureName},'%')
        </if>
        <if test="appDate != null and appDate !='' ">
            and fc.appDate LIKE concat('%',#{appDate},'%')
        </if>
    order by fc.`ModifyDate` desc ,fc.`ModifyTime` desc ,fc.GrpNo desc
    </select>
    <select id="getDailyplanList_check" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        fc.`EnsureCode`AS EnsureCode,
        fc.`EnsureName` AS EnsureName,
        fg.`GrpName` AS GrpName,
        fc.`AuditType` AS AuditType,
        date_format(fc.`AppDate`,'%Y-%m-%d') AS AppDate,
        fc.`EnsureState` AS EnsureState
        FROM
        fcensure fc,
        fcgrpinfo fg,
        fdagentinfo fa
        WHERE 1=1
        and fc.ClientNo = fa.AgentCode
        and fa.manageCom like #{manageCom}"%"
        and fc.PlanType='2' AND fc.GrpNo=fg.GrpNo and fc.EnsureState in ('013','016','014','015','018','019','020')
        <if test="ensureCode != null and ensureCode != '' ">
            and ensureCode = #{ensureCode}
        </if>
        <if test="ensureState != null and ensureState !=''  ">
            and fc.ensureState = #{ensureState}
        </if>
        <!--<if test="ensureState != null and ensureState !='' and ensureState !='015' ">
            and fc.ensureState = #{ensureState}
        </if>
        <if test="ensureState != null and ensureState !='' and ensureState =='015' ">
            and fc.ensureState in ('013','016','014','015','017')
        </if>-->
        <if test="AuditType != null and AuditType !='' ">
            and AuditType=#{AuditType}
        </if>
        <if test="grpName != null and grpName !='' ">
            and grpName LIKE concat( '%',#{grpName},'%')
        </if>
        <if test="ensureName != null and ensureName !='' ">
            and ensureName LIKE concat('%',#{ensureName},'%')
        </if>
        <if test="appDate != null and appDate !='' ">
            and appDate LIKE concat('%',#{appDate},'%')
        </if>
        order by fc.`AppDate` desc ,fc.`ModifyTime` desc ,fc.GrpNo desc
    </select>
</mapper>