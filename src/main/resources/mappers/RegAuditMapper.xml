<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinosoft.eflex.dao.RegAuditMapper" >

    <select id="findPageInfo" parameterType="com.sinosoft.eflex.model.RegistRegFind" resultType="java.util.HashMap">
        SELECT
        c.registSN,
        c.Name,
        c.MobilePhone,
        c.nativeplace,
        c.IDType,
        c.IDNo,
        c.idTypeEndDate,
        c.GrpName,
        c.UnifiedsociCode,
        c.GrpIdType,
        c.GrpIdNo,
        c.Clientno,
        if(c.grpNo is null,'',c.grpNo) GrpNo,
        c.checkstatus AuditResult,
        c.MakeDate,
        c.MakeTime
        from FCHrRegistTemp c
        left join fdagentinfo d on c.ClientNo = d.AgentCode
       <where>
            1=1
            <if test="name != null and name != ''">
               and c.Name like "%"#{name}"%"
            </if>
            <if test="mobilePhone !=null and mobilePhone !=''">
               and c.MobilePhone=#{mobilePhone}
            </if>
            <if test="nativeplace !=null and nativeplace !=''">
               and c.nativeplace=#{nativeplace}
            </if>
            <if test="IDType !=null and IDType !=''">
               and c.IDType=#{IDType}
            </if>
            <if test="IDNo !=null and IDNo !=''">
                and c.IDNo=#{IDNo}
            </if>
            <if test="grpName !=null and grpName !='' ">
                and c.GrpName like concat('%',#{grpName},'%')
            </if>
            <if test="auditResult !=null and auditResult !=''">
                and c.checkstatus=#{auditResult}
            </if>
            <if test="startDate != null and startDate !=''">
                <![CDATA[and DATE_FORMAT(c.MakeDate, '%Y-%m-%d') >=  DATE_FORMAT(#{startDate}, '%Y-%m-%d')]]>
            </if>
           <if test="endDate != null and endDate !=''">
               <![CDATA[and DATE_FORMAT(c.MakeDate, '%Y-%m-%d') <=  DATE_FORMAT(#{endDate}, '%Y-%m-%d')]]>
           </if>
           and d.manageCom like #{manageCom}"%"
           Order by c.checkstatus,c.MakeDate desc,c.MakeTime desc
       </where>
    </select>


    <select id="selectAllhrName" resultType="java.lang.String">
    	select DISTINCT name from FCGrpContact
    </select>
    
    <select id="selectAllgrpName" resultType="java.lang.String">
    	select DISTINCT GrpName from FCGrpInfo
    </select>
    
</mapper>