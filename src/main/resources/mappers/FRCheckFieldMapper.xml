<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FRCheckFieldMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FRCheckField">
    <id column="RiskCode" jdbcType="VARCHAR" property="riskCode" />
    <id column="SerialNo" jdbcType="VARCHAR" property="serialNo" />
    <result column="RiskVer" jdbcType="VARCHAR" property="riskVer" />
    <result column="FieldName" jdbcType="VARCHAR" property="fieldName" />
    <result column="CalCode" jdbcType="VARCHAR" property="calCode" />
    <result column="PageLocation" jdbcType="VARCHAR" property="pageLocation" />
    <result column="Location" jdbcType="CHAR" property="location" />
    <result column="Msg" jdbcType="VARCHAR" property="msg" />
    <result column="MsgFlag" jdbcType="CHAR" property="msgFlag" />
    <result column="UpdFlag" jdbcType="CHAR" property="updFlag" />
    <result column="ValiFlag" jdbcType="VARCHAR" property="valiFlag" />
    <result column="ReturnValiFlag" jdbcType="CHAR" property="returnValiFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    RiskCode, SerialNo, RiskVer, FieldName, CalCode, PageLocation, Location, Msg, MsgFlag, 
    UpdFlag, ValiFlag, ReturnValiFlag
  </sql>
  <select id="selectByPrimaryKey" parameterType="com.sinosoft.eflex.model.FRCheckFieldKey" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from frcheckfield
    where RiskCode = #{riskCode,jdbcType=VARCHAR}
      and SerialNo = #{serialNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.sinosoft.eflex.model.FRCheckFieldKey">
    delete from frcheckfield
    where RiskCode = #{riskCode,jdbcType=VARCHAR}
      and SerialNo = #{serialNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FRCheckField">
    insert into frcheckfield (RiskCode, SerialNo, RiskVer, 
      FieldName, CalCode, PageLocation, 
      Location, Msg, MsgFlag, UpdFlag, 
      ValiFlag, ReturnValiFlag)
    values (#{riskCode,jdbcType=VARCHAR}, #{serialNo,jdbcType=VARCHAR}, #{riskVer,jdbcType=VARCHAR}, 
      #{fieldName,jdbcType=VARCHAR}, #{calCode,jdbcType=VARCHAR}, #{pageLocation,jdbcType=VARCHAR}, 
      #{location,jdbcType=CHAR}, #{msg,jdbcType=VARCHAR}, #{msgFlag,jdbcType=CHAR}, #{updFlag,jdbcType=CHAR}, 
      #{valiFlag,jdbcType=VARCHAR}, #{returnValiFlag,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FRCheckField">
    insert into frcheckfield
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="riskCode != null">
        RiskCode,
      </if>
      <if test="serialNo != null">
        SerialNo,
      </if>
      <if test="riskVer != null">
        RiskVer,
      </if>
      <if test="fieldName != null">
        FieldName,
      </if>
      <if test="calCode != null">
        CalCode,
      </if>
      <if test="pageLocation != null">
        PageLocation,
      </if>
      <if test="location != null">
        Location,
      </if>
      <if test="msg != null">
        Msg,
      </if>
      <if test="msgFlag != null">
        MsgFlag,
      </if>
      <if test="updFlag != null">
        UpdFlag,
      </if>
      <if test="valiFlag != null">
        ValiFlag,
      </if>
      <if test="returnValiFlag != null">
        ReturnValiFlag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="riskCode != null">
        #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="riskVer != null">
        #{riskVer,jdbcType=VARCHAR},
      </if>
      <if test="fieldName != null">
        #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="calCode != null">
        #{calCode,jdbcType=VARCHAR},
      </if>
      <if test="pageLocation != null">
        #{pageLocation,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=CHAR},
      </if>
      <if test="msg != null">
        #{msg,jdbcType=VARCHAR},
      </if>
      <if test="msgFlag != null">
        #{msgFlag,jdbcType=CHAR},
      </if>
      <if test="updFlag != null">
        #{updFlag,jdbcType=CHAR},
      </if>
      <if test="valiFlag != null">
        #{valiFlag,jdbcType=VARCHAR},
      </if>
      <if test="returnValiFlag != null">
        #{returnValiFlag,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FRCheckField">
    update frcheckfield
    <set>
      <if test="riskVer != null">
        RiskVer = #{riskVer,jdbcType=VARCHAR},
      </if>
      <if test="fieldName != null">
        FieldName = #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="calCode != null">
        CalCode = #{calCode,jdbcType=VARCHAR},
      </if>
      <if test="pageLocation != null">
        PageLocation = #{pageLocation,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        Location = #{location,jdbcType=CHAR},
      </if>
      <if test="msg != null">
        Msg = #{msg,jdbcType=VARCHAR},
      </if>
      <if test="msgFlag != null">
        MsgFlag = #{msgFlag,jdbcType=CHAR},
      </if>
      <if test="updFlag != null">
        UpdFlag = #{updFlag,jdbcType=CHAR},
      </if>
      <if test="valiFlag != null">
        ValiFlag = #{valiFlag,jdbcType=VARCHAR},
      </if>
      <if test="returnValiFlag != null">
        ReturnValiFlag = #{returnValiFlag,jdbcType=CHAR},
      </if>
    </set>
    where RiskCode = #{riskCode,jdbcType=VARCHAR}
      and SerialNo = #{serialNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FRCheckField">
    update frcheckfield
    set RiskVer = #{riskVer,jdbcType=VARCHAR},
      FieldName = #{fieldName,jdbcType=VARCHAR},
      CalCode = #{calCode,jdbcType=VARCHAR},
      PageLocation = #{pageLocation,jdbcType=VARCHAR},
      Location = #{location,jdbcType=CHAR},
      Msg = #{msg,jdbcType=VARCHAR},
      MsgFlag = #{msgFlag,jdbcType=CHAR},
      UpdFlag = #{updFlag,jdbcType=CHAR},
      ValiFlag = #{valiFlag,jdbcType=VARCHAR},
      ReturnValiFlag = #{returnValiFlag,jdbcType=CHAR}
    where RiskCode = #{riskCode,jdbcType=VARCHAR}
      and SerialNo = #{serialNo,jdbcType=VARCHAR}
  </update>

  <select id="selectListByParams" parameterType="java.util.Map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from frcheckfield
    <where>
      <if test="riskCode != null and riskCode !=''">
        and RiskCode = #{riskCode,jdbcType=VARCHAR}
      </if>
      <if test="fieldName != null and fieldName !=''">
        and FieldName = #{fieldName,jdbcType=VARCHAR}
      </if>
    </where>
    order by serialNo
  </select>

  <select id="selectEflexListByParams" parameterType="java.util.Map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from freflexcheckfield
    <where>
      <if test="riskCode != null and riskCode !=''">
        and RiskCode = #{riskCode,jdbcType=VARCHAR}
      </if>
      <if test="fieldName != null and fieldName !=''">
        and FieldName = #{fieldName,jdbcType=VARCHAR}
      </if>
    </where>
    order by serialNo
  </select>

  <!--<select id="selectListByPlanCode" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from frcheckfield
    where RiskCode = #{riskCode,jdbcType=VARCHAR}
    and SerialNo = #{serialNo,jdbcType=VARCHAR}
  </select>-->

</mapper>