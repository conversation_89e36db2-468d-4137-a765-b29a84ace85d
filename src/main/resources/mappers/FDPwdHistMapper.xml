<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FDPwdHistMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FDPwdHist">
    <id column="PassWordSN" jdbcType="VARCHAR" property="passWordSN" />
    <result column="UserNo" jdbcType="VARCHAR" property="userNo" />
    <result column="PassWord" jdbcType="VARCHAR" property="passWord" />
      <result column="PassWordInvalidTime" jdbcType="VARCHAR" property="passWordInvalidTime"/>
    <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
    <result column="Operator" jdbcType="VARCHAR" property="operator" />
    <result column="MakeDate" jdbcType="DATE" property="makeDate" />
    <result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
    <result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
    <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    PassWordSN, UserNo, PassWord, PassWordInvalidTime, OperatorCom, Operator, MakeDate, 
    MakeTime, ModifyDate, ModifyTime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fdpwdhist
    where PassWordSN = #{passWordSN,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from fdpwdhist
    where PassWordSN = #{passWordSN,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FDPwdHist">
    insert into fdpwdhist (PassWordSN, UserNo, PassWord, 
      PassWordInvalidTime, OperatorCom, Operator, 
      MakeDate, MakeTime, ModifyDate, 
      ModifyTime)
    values (#{passWordSN,jdbcType=VARCHAR}, #{userNo,jdbcType=VARCHAR}, #{passWord,jdbcType=VARCHAR}, 
      #{passWordInvalidTime,jdbcType=VARCHAR}, #{operatorCom,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, 
      #{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR}, #{modifyDate,jdbcType=DATE}, 
      #{modifyTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FDPwdHist">
    insert into fdpwdhist
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="passWordSN != null">
        PassWordSN,
      </if>
      <if test="userNo != null">
        UserNo,
      </if>
      <if test="passWord != null">
        PassWord,
      </if>
        <if test="passWordInvalidTime != null">
            PassWordInvalidTime,
        </if>
      <if test="operatorCom != null">
        OperatorCom,
      </if>
      <if test="operator != null">
        Operator,
      </if>
      <if test="makeDate != null">
        MakeDate,
      </if>
      <if test="makeTime != null">
        MakeTime,
      </if>
      <if test="modifyDate != null">
        ModifyDate,
      </if>
      <if test="modifyTime != null">
        ModifyTime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="passWordSN != null">
        #{passWordSN,jdbcType=VARCHAR},
      </if>
      <if test="userNo != null">
        #{userNo,jdbcType=VARCHAR},
      </if>
      <if test="passWord != null">
        #{passWord,jdbcType=VARCHAR},
      </if>
        <if test="passWordInvalidTime != null">
            #{passWordInvalidTime,jdbcType=VARCHAR},
        </if>
      <if test="operatorCom != null">
        #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FDPwdHist">
    update fdpwdhist
    <set>
      <if test="userNo != null">
        UserNo = #{userNo,jdbcType=VARCHAR},
      </if>
      <if test="passWord != null">
        PassWord = #{passWord,jdbcType=VARCHAR},
      </if>
        <if test="passWordInvalidTime != null">
            PassWordInvalidTime = #{passWordInvalidTime,jdbcType=VARCHAR},
        </if>
      <if test="operatorCom != null">
        OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        Operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="makeDate != null">
        MakeDate = #{makeDate,jdbcType=DATE},
      </if>
      <if test="makeTime != null">
        MakeTime = #{makeTime,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        ModifyDate = #{modifyDate,jdbcType=DATE},
      </if>
      <if test="modifyTime != null">
        ModifyTime = #{modifyTime,jdbcType=VARCHAR},
      </if>
    </set>
    where PassWordSN = #{passWordSN,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FDPwdHist">
    update fdpwdhist
    set UserNo = #{userNo,jdbcType=VARCHAR},
      PassWord = #{passWord,jdbcType=VARCHAR},
      PassWordInvalidTime = #{passWordInvalidTime,jdbcType=VARCHAR},
      OperatorCom = #{operatorCom,jdbcType=VARCHAR},
      Operator = #{operator,jdbcType=VARCHAR},
      MakeDate = #{makeDate,jdbcType=DATE},
      MakeTime = #{makeTime,jdbcType=VARCHAR},
      ModifyDate = #{modifyDate,jdbcType=DATE},
      ModifyTime = #{modifyTime,jdbcType=VARCHAR}
    where PassWordSN = #{passWordSN,jdbcType=VARCHAR}
  </update>
  <select id="selectPwdByUserno" parameterType="java.lang.String" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from fdpwdhist f
      where f.userno = #{userNo,jdbcType=VARCHAR}
  </select>
    <select id="selectNewPwdHist" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdpwdhist
        where userNO = #{userNo,jdbcType=VARCHAR}
        order by PassWordSN desc limit 1
    </select>
</mapper>