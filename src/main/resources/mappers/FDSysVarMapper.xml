<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FDSysVarMapper">
  <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FDSysVar">
    <id column="SysVar" jdbcType="VARCHAR" property="sysvar" />
    <result column="SysVarType" jdbcType="VARCHAR" property="sysvartype" />
    <result column="SysVarValue" jdbcType="VARCHAR" property="sysvarvalue" />
  </resultMap>
  <sql id="Base_Column_List">
    SysVar, SysVarType, SysVarValue
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from FDSysVar
    where SysVar = #{sysvar,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from FDSysVar
    where SysVar = #{sysvar,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinosoft.eflex.model.FDSysVar">
    insert into FDSysVar (SysVar, SysVarType, SysVarValue
      )
    values (#{sysvar,jdbcType=VARCHAR}, #{sysvartype,jdbcType=VARCHAR}, #{sysvarvalue,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FDSysVar">
    insert into FDSysVar
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sysvar != null">
        SysVar,
      </if>
      <if test="sysvartype != null">
        SysVarType,
      </if>
      <if test="sysvarvalue != null">
        SysVarValue,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sysvar != null">
        #{sysvar,jdbcType=VARCHAR},
      </if>
      <if test="sysvartype != null">
        #{sysvartype,jdbcType=VARCHAR},
      </if>
      <if test="sysvarvalue != null">
        #{sysvarvalue,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FDSysVar">
    update FDSysVar
    <set>
      <if test="sysvartype != null">
        SysVarType = #{sysvartype,jdbcType=VARCHAR},
      </if>
      <if test="sysvarvalue != null">
        SysVarValue = #{sysvarvalue,jdbcType=VARCHAR},
      </if>
    </set>
    where SysVar = #{sysvar,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FDSysVar">
    update FDSysVar
    set SysVarType = #{sysvartype,jdbcType=VARCHAR},
      SysVarValue = #{sysvarvalue,jdbcType=VARCHAR}
    where SysVar = #{sysvar,jdbcType=VARCHAR}
  </update>
</mapper>