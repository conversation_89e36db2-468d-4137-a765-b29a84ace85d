<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCEnsureMapper">
    <resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCEnsure">
        <id column="EnsureCode" jdbcType="VARCHAR" property="ensureCode"/>
        <result column="GrpNo" jdbcType="VARCHAR" property="grpNo"/>
        <result column="EnsureName" jdbcType="VARCHAR" property="ensureName"/>
        <result column="AppDate" jdbcType="DATE" property="appDate"/>
        <result column="ClientNo" jdbcType="VARCHAR" property="clientNo"/>
        <result column="GreenInsurance" jdbcType="BIT" property="greenInsurance"/>
        <result column="ReNewGrpContNo" jdbcType="VARCHAR" property="reNewGrpContNo"/>
        <result column="Channel" jdbcType="VARCHAR" property="channel"/>
        <result column="IntermediaryOrganCode" jdbcType="VARCHAR" property="intermediaryOrganCode"/>
        <result column="IntermediaryOrganName" jdbcType="VARCHAR" property="intermediaryOrganName"/>
        <result column="EnsureState" jdbcType="VARCHAR" property="ensureState"/>
        <result column="AppntYear" jdbcType="VARCHAR" property="appntYear"/>
        <result column="CvaliDate" jdbcType="DATE" property="cvaliDate"/>
        <result column="PolicyEndDate" jdbcType="DATE" property="policyEndDate"/>
        <result column="InsuredNumber" jdbcType="INTEGER" property="insuredNumber"/>
        <result column="TotalPrem" jdbcType="DOUBLE" property="totalPrem"/>
        <result column="StartAppntDate" jdbcType="DATE" property="startAppntDate"/>
        <result column="EndAppntDate" jdbcType="DATE" property="endAppntDate"/>
        <result column="PolicyState" jdbcType="VARCHAR" property="policyState"/>
        <result column="Operator" jdbcType="VARCHAR" property="operator"/>
        <result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom"/>
        <result column="MakeDate" jdbcType="DATE" property="makeDate"/>
        <result column="MakeTime" jdbcType="VARCHAR" property="makeTime"/>
        <result column="ModifyDate" jdbcType="DATE" property="modifyDate"/>
        <result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime"/>
        <result column="GrpName" jdbcType="VARCHAR" property="grpName"/>
        <result column="UnifiedsociCode" jdbcType="VARCHAR" property="unifiedsociCode"/>
        <result column="PlanType" jdbcType="VARCHAR" property="planType"/>
        <result column="CodeName" jdbcType="VARCHAR" property="codeName"/>
        <result column="EnsureType" jdbcType="VARCHAR" property="ensureType"/>
        <result column="PremCalType" jdbcType="VARCHAR" property="premCalType"/>
        <result column="PayType" jdbcType="VARCHAR" property="payType"/>
        <result column="AuditType" jdbcType="VARCHAR" property="auditType"/>
        <result column="orderNo" jdbcType="VARCHAR" property="orderNo"/>
    </resultMap>
    <!-- 企业计划汇总查询 -->
    <resultMap type="com.sinosoft.eflex.model.FCEnsure" id="PlanCollection"
               extends="BaseResultMap">
        <collection property="fcEnsurePlans" column="{ensureCode=EnsureCode}"
                    javaType="java.util.ArrayList" ofType="com.sinosoft.eflex.model.FCEnsurePlan"
                    select="com.sinosoft.eflex.dao.FCEnsurePlanMapper.selectByEnsureCode"
                    fetchType="lazy"/>
    </resultMap>
    <!-- 保障信息查询折线图 -->
    <resultMap type="com.sinosoft.eflex.model.FCEnsure" id="EnsureChart">
        <result column="AppntYear" jdbcType="VARCHAR" property="appntYear"/>
        <result column="TotalPrem" jdbcType="DOUBLE" property="totalPrem"/>
        <collection property="fcEnsurePlans" javaType="java.util.ArrayList"
                    ofType="com.sinosoft.eflex.model.FCEnsurePlan">
            <result column="PlanObject" jdbcType="VARCHAR" property="planObject"/>
            <result column="InsuredNumber" jdbcType="INTEGER" property="insuredNumber"/>
        </collection>
    </resultMap>
    <!-- 保障信息查询折线图 -->
    <select id="getEnsureChart" parameterType="java.lang.String" resultType="java.util.Map">
        select e.AppntYear as AppntYear,
        sum((select count(*) from fcorderitem fi,fcorder fd,fcgrporder fg ,fcorderinsured fo,fcstafffamilyrela
        ft,fcperregistday fp
        where fi.orderno=fd.orderno and fd.GrpOrderNo=fg.GrpOrderNo
        and fd.perno=ft.perno
        and fo.orderitemno=fi.orderitemno
        and fp.perno=fd.perno and fp.EnsureCode=e.EnsureCode
        and ft.personid=fo.personid
        and ft.relation='0'
        and fp.LockState = '0'
        and fg.EnsureCode=e.EnsureCode )) as employeeNum,
        sum((select count(*) from fcorderitem fi,fcorder fd,fcgrporder fg ,fcorderinsured fo,fcstafffamilyrela
        ft,fcperregistday fp
        where fi.orderno=fd.orderno and fd.GrpOrderNo=fg.GrpOrderNo
        and fd.perno=ft.perno
        and fo.orderitemno=fi.orderitemno
        and ft.personid=fo.personid
        and fp.perno=fd.perno and fp.EnsureCode=e.EnsureCode
        and ft.relation!='0'
        and fp.LockState='0'
        and fg.EnsureCode=e.EnsureCode )) as familyNum
        from
        fcensure e
        where
        e.GrpNo = #{grpNo,jdbcType = VARCHAR}
        <if test="ensureCode != null and ensureCode != ''">
            and e.EnsureCode = #{ensureCode}
        </if>
        and e.PolicyState in (3,4) group by e.GrpNo,e.AppntYear
    </select>
    <select id="getPersonIdAndPlanCode" parameterType="java.lang.String" resultType="java.util.Map">
        select fp.personId,fp.planCode,fc.name personName
        from fpinsureplan fp,fcperson fc
        where fp.personid=fc.personid
        <!-- 		and fp.InsureState='0' 已投保的操作 -->
        and ensurecode=#{ensureCode,jdbcType = VARCHAR}
        and perno=#{perNo,jdbcType = VARCHAR}
    </select>
    <!-- 投保须知 -->
    <select id="selectInsureNotes" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT
        s.StartAppntDate AS StartAppntDate,
        s.EndAppntDate AS EndAppntDate,
        s.CvaliDate AS CvaliDate,
        s.PolicyEndDate AS PolicyEndDate
        FROM
        fcensure s
        <where>
            1=1
            <if test="grpNo != null and grpNo != ''">
                and s.GrpNo = #{grpNo,jdbcType=VARCHAR}
            </if>
            <if test="ensureCode != null and ensureCode != ''">
                and s.EnsureCode = #{ensureCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <sql id="Base_Column_List">
        EnsureCode
        , GrpNo,EnsureName, AppDate,ClientNo,GreenInsurance,ReNewGrpContNo,Channel,IntermediaryOrganCode,IntermediaryOrganName,
		EnsureState,AppntYear, CvaliDate,EnsureType,
		PolicyEndDate, InsuredNumber,
		TotalPrem,orderNo,
		StartAppntDate, EndAppntDate,planType,
		PolicyState,PremCalType,PayType, Operator,
		OperatorCom, MakeDate, MakeTime,
		ModifyDate,
		ModifyTime,AuditType
    </sql>
    <sql id="Base_Column_List1">
        a
        .
        EnsureCode
        ,a.GrpNo,a.EnsureName, a.AppDate,a.ClientNo,
		a.EnsureState,a.AppntYear, a.CvaliDate,a.EnsureType,
		a.PolicyEndDate, a.InsuredNumber,
		a.TotalPrem,
		a.StartAppntDate, a.EndAppntDate,a.planType,
		a.PolicyState,a.PremCalType,a.PayType, a.Operator,
		a.OperatorCom, a.MakeDate, a.MakeTime,a.ModifyDate,
		a.ModifyTime,a.AuditType
    </sql>
    <!-- 企业保障信息查询 -->
    <sql id="Company_Ensure_Info">
        EnsureCode
        ,AppntYear,EnsureName,CvaliDate,PolicyEndDate,InsuredNumber,TotalPrem,
        StartAppntDate,EndAppntDate,PolicyState
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcensure
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    </select>
    <select id="selectEnsureByManageCom" parameterType="com.sinosoft.eflex.model.SelectEnsureByManageComReq"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List1"/>
        from fcensure a
        left join fdagentinfo b on a.ClientNo = b.AgentCode
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        and b.manageCom like #{manageCom}"%"
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fcensure
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.sinosoft.eflex.model.FCEnsure">
        insert into fcensure
        (EnsureCode, GrpNo, EnsureName, EnsureType, AppDate,
        <if test="clientNo != null">
            ClientNo,
        </if>
        <if test="greenInsurance != null">
            GreenInsurance,
        </if>
        EnsureState, AppntYear,
        CvaliDate, PolicyEndDate,
        InsuredNumber,
        TotalPrem, StartAppntDate,
        EndAppntDate,
        PolicyState,
        Operator, OperatorCom,
        MakeDate, MakeTime,
        ModifyDate,
        ModifyTime,
        PlanType, PremCalType, PayType)
        values (#{ensureCode,jdbcType=VARCHAR},
        #{grpNo,jdbcType=VARCHAR},
        #{ensureName,jdbcType=VARCHAR},
        #{ensureType,jdbcType=VARCHAR},
        #{appDate,jdbcType=DATE},
        <if test="clientNo != null">
            #{clientNo,jdbcType=VARCHAR},
        </if>
        <if test="greenInsurance != null">
            #{greenInsurance,jdbcType=BIT},
        </if>
        #{ensureState,jdbcType=VARCHAR},
        #{appntYear,jdbcType=VARCHAR},
        #{cvaliDate,jdbcType=DATE},
        #{policyEndDate,jdbcType=DATE},
        #{insuredNumber,jdbcType=INTEGER},
        #{totalPrem,jdbcType=DOUBLE},
        #{startAppntDate,jdbcType=DATE},
        #{endAppntDate,jdbcType=DATE},
        #{policyState,jdbcType=VARCHAR},
        #{operator,jdbcType=VARCHAR},
        #{operatorCom,jdbcType=VARCHAR},
        #{makeDate,jdbcType=DATE},
        #{makeTime,jdbcType=VARCHAR},
        #{modifyDate,jdbcType=DATE},
        #{modifyTime,jdbcType=VARCHAR},
        #{planType,jdbcType=VARCHAR}, #{premCalType,jdbcType=VARCHAR}, #{payType,jdbcType=VARCHAR})
    </insert>
    <insert id="saveFcBatchPayBankInfo" parameterType="com.sinosoft.eflex.model.FcBatchPayBankInfo">
        insert into FcBatchPayBankInfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ensureCode != null and ensureCode !='' ">
                ensureCode,
            </if>
            <if test="perNo != null and perNo !='' ">
                perNo,
            </if>
            <if test="signSN != null and signSN !='' ">
                signSN,
            </if>
            <if test="bankAccCode != null and bankAccCode !='' ">
                bankAccCode,
            </if>
            <if test="transId != null and transId !='' ">
                transId,
            </if>
            <if test="orderNo != null and orderNo !='' ">
                orderNo,
            </if>
            <if test="name != null and name !='' ">
                name,
            </if>
            <if test="idType != null and idType !='' ">
                idType,
            </if>
            <if test="idNo != null and idNo !='' ">
                idNo,
            </if>
            <if test="bankAccount != null and bankAccount !='' ">
                bankAccount,
            </if>
            <if test="payBankCode != null and payBankCode !='' ">
                payBankCode,
            </if>
            <if test="reservePhone != null and reservePhone !='' ">
                reservePhone,
            </if>
            <if test="isSidned != null and isSidned !='' ">
                isSidned,
            </if>
            <if test="operator != null and operator !='' ">
                Operator,
            </if>
            <if test="operatorCom != null and operatorCom !='' ">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null and makeTime !='' ">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null and modifyTime !='' ">
                ModifyTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ensureCode != null and ensureCode !='' ">
                #{ensureCode,jdbcType=VARCHAR},
            </if>
            <if test="perNo != null and perNo !='' ">
                #{perNo,jdbcType=VARCHAR},
            </if>
            <if test="signSN != null and signSN !='' ">
                #{signSN,jdbcType=VARCHAR},
            </if>
            <if test="bankAccCode != null and bankAccCode !='' ">
                #{bankAccCode,jdbcType=VARCHAR},
            </if>
            <if test="transId != null and transId !='' ">
                #{transId,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null and orderNo !='' ">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name !='' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="idType != null and idType !='' ">
                #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null and idNo !='' ">
                #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="bankAccount != null and bankAccount !='' ">
                #{bankAccount,jdbcType=VARCHAR},
            </if>
            <if test="payBankCode != null and payBankCode !='' ">
                #{payBankCode,jdbcType=VARCHAR},
            </if>
            <if test="reservePhone != null and reservePhone !='' ">
                #{reservePhone,jdbcType=VARCHAR},
            </if>
            <if test="isSidned != null and isSidned !='' ">
                #{isSidned,jdbcType=VARCHAR},
            </if>
            <if test="operator != null and operator !='' ">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null and operatorCom !='' ">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null and makeTime !='' ">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null and modifyTime !='' ">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
        ON DUPLICATE KEY
        UPDATE
        <trim suffixOverrides=",">
            <if test="signSN != null and signSN !='' ">
                signSN = #{signSN,jdbcType=VARCHAR},
            </if>
            <if test="bankAccCode != null and bankAccCode !='' ">
                bankAccCode = #{bankAccCode,jdbcType=VARCHAR},
            </if>
            <if test="transId != null and transId !='' ">
                transId = #{transId,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null and orderNo !='' ">
                orderNo = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name !='' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="idType != null and idType !='' ">
                idType = #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null and idNo !='' ">
                idNo = #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="bankAccount != null and bankAccount !='' ">
                bankAccount = #{bankAccount,jdbcType=VARCHAR},
            </if>
            <if test="payBankCode != null and payBankCode !='' ">
                payBankCode = #{payBankCode,jdbcType=VARCHAR},
            </if>
            <if test="reservePhone != null and reservePhone !='' ">
                reservePhone = #{reservePhone,jdbcType=VARCHAR},
            </if>
            <if test="operator != null and operator !='' ">
                operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null and operatorCom !='' ">
                operatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                modifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null and modifyTime !='' ">
                modifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCEnsure">
        insert into fcensure
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ensureCode != null">
                EnsureCode,
            </if>
            <if test="grpNo != null">
                GrpNo,
            </if>
            <if test="ensureName != null">
                EnsureName,
            </if>
            <if test="appDate != null">
                AppDate,
            </if>
            <if test="clientNo != null">
                ClientNo,
            </if>
            <if test="ensureState != null">
                EnsureState,
            </if>
            <if test="appntYear != null">
                AppntYear,
            </if>
            <if test="cvaliDate != null">
                CvaliDate,
            </if>
            <if test="policyEndDate != null">
                PolicyEndDate,
            </if>
            <if test="insuredNumber != null">
                InsuredNumber,
            </if>
            <if test="totalPrem != null">
                TotalPrem,
            </if>
            <if test="startAppntDate != null">
                StartAppntDate,
            </if>
            <if test="endAppntDate != null">
                EndAppntDate,
            </if>
            <if test="policyState != null">
                PolicyState,
            </if>
            <if test="operator != null">
                Operator,
            </if>
            <if test="operatorCom != null">
                OperatorCom,
            </if>
            <if test="makeDate != null">
                MakeDate,
            </if>
            <if test="makeTime != null">
                MakeTime,
            </if>
            <if test="modifyDate != null">
                ModifyDate,
            </if>
            <if test="modifyTime != null">
                ModifyTime,
            </if>
            <if test="AuditType != null">
                AuditType,
            </if>
            <if test="planType != null">
                PlanType,
            </if>
            <if test="ensureType != null">
                EnsureType,
            </if>
            <if test="greenInsurance != null">
                GreenInsurance,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ensureCode != null">
                #{ensureCode,jdbcType=VARCHAR},
            </if>
            <if test="grpNo != null">
                #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="ensureName != null">
                #{ensureName,jdbcType=VARCHAR},
            </if>
            <if test="appDate != null">
                #{appDate,jdbcType=DATE},
            </if>
            <if test="clientNo != null">
                #{clientNo,jdbcType=VARCHAR},
            </if>
            <if test="ensureState != null">
                #{ensureState,jdbcType=VARCHAR},
            </if>
            <if test="appntYear != null">
                #{appntYear,jdbcType=VARCHAR},
            </if>
            <if test="cvaliDate != null">
                #{cvaliDate,jdbcType=DATE},
            </if>
            <if test="policyEndDate != null">
                #{policyEndDate,jdbcType=DATE},
            </if>
            <if test="insuredNumber != null">
                #{insuredNumber,jdbcType=INTEGER},
            </if>
            <if test="totalPrem != null">
                #{totalPrem,jdbcType=DOUBLE},
            </if>
            <if test="startAppntDate != null">
                #{startAppntDate,jdbcType=DATE},
            </if>
            <if test="endAppntDate != null">
                #{endAppntDate,jdbcType=DATE},
            </if>
            <if test="policyState != null">
                #{policyState,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test="AuditType != null">
                #{AuditType,jdbcType=VARCHAR},
            </if>
            <if test="planType != null">
                #{planType,jdbcType=VARCHAR},
            </if>
            <if test="ensureType != null">
                #{ensureType,jdbcType=VARCHAR},
            </if>
            <if test="greenInsurance != null">
                #{greenInsurance,jdbcType=BIT},
            </if>
        </trim>
    </insert>

    <update id="updateOrderNoNull" parameterType="com.sinosoft.eflex.model.FCEnsure">
        update fcensure
        set orderNo = NULL
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCEnsure">
        update fcensure
        <set>
            <if test="grpNo != null">
                GrpNo = #{grpNo,jdbcType=VARCHAR},
            </if>
            <if test="ensureName != null">
                EnsureName = #{ensureName,jdbcType=VARCHAR},
            </if>
            <if test="ensureType != null">
                EnsureType = #{ensureType,jdbcType=VARCHAR},
            </if>
            <if test="appDate != null">
                AppDate = #{appDate,jdbcType=DATE},
            </if>
            <if test="clientNo != null">
                ClientNo = #{clientNo,jdbcType=VARCHAR},
            </if>
            <if test="ensureState != null">
                EnsureState = #{ensureState,jdbcType=VARCHAR},
            </if>
            <if test="appntYear != null">
                AppntYear = #{appntYear,jdbcType=VARCHAR},
            </if>
            <if test="cvaliDate != null">
                CvaliDate = #{cvaliDate,jdbcType=DATE},
            </if>
            <if test="policyEndDate != null">
                PolicyEndDate = #{policyEndDate,jdbcType=DATE},
            </if>
            <if test="insuredNumber != null">
                InsuredNumber = #{insuredNumber,jdbcType=INTEGER},
            </if>
            <if test="totalPrem != null">
                TotalPrem = #{totalPrem,jdbcType=DOUBLE},
            </if>
            <if test="payType != null">
                PayType  = #{payType,jdbcType=VARCHAR},
            </if>
            <if test="startAppntDate != null">
                StartAppntDate = #{startAppntDate,jdbcType=DATE},
            </if>
            <if test="endAppntDate != null">
                EndAppntDate = #{endAppntDate,jdbcType=DATE},
            </if>
            <if test="policyState != null">
                PolicyState = #{policyState,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                Operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorCom != null">
                OperatorCom = #{operatorCom,jdbcType=VARCHAR},
            </if>
            <if test="makeDate != null">
                MakeDate = #{makeDate,jdbcType=DATE},
            </if>
            <if test="makeTime != null">
                MakeTime = #{makeTime,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null">
                ModifyDate = #{modifyDate,jdbcType=DATE},
            </if>
            <if test="modifyTime != null">
                ModifyTime = #{modifyTime,jdbcType=VARCHAR},
            </if>
            <if test="AuditType != null">
                AuditType = #{AuditType,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                orderNo = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="greenInsurance != null">
                GreenInsurance = #{greenInsurance,jdbcType=BIT},
            </if>
        </set>
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCEnsure">
        update fcensure
        set GrpNo                 = #{grpNo,jdbcType=VARCHAR},
            EnsureName            =
                #{ensureName,jdbcType=VARCHAR},
            AppDate               =
                #{appDate,jdbcType=DATE},
            ClientNo              = #{clientNo,jdbcType=VARCHAR},
            GreenInsurance        = #{greenInsurance,jdbcType=BIT},
            ReNewGrpContNo        = #{reNewGrpContNo ,jdbcType=VARCHAR},
            Channel               = #{channel,jdbcType=VARCHAR},
            IntermediaryOrganCode = #{intermediaryOrganCode,jdbcType=VARCHAR},
            IntermediaryOrganName = #{intermediaryOrganName,jdbcType=VARCHAR},
            EnsureState           =
                #{ensureState,jdbcType=VARCHAR},
            AppntYear             =
                #{appntYear,jdbcType=VARCHAR},
            CvaliDate             = #{cvaliDate,jdbcType=DATE},
            PolicyEndDate         = #{policyEndDate,jdbcType=DATE},
            InsuredNumber         =
                #{insuredNumber,jdbcType=INTEGER},
            TotalPrem             =
                #{totalPrem,jdbcType=DOUBLE},
            StartAppntDate        =
                #{startAppntDate,jdbcType=DATE},
            EndAppntDate          =
                #{endAppntDate,jdbcType=DATE},
            PolicyState           =
                #{policyState,jdbcType=VARCHAR},
            Operator              =
                #{operator,jdbcType=VARCHAR},
            OperatorCom           =
                #{operatorCom,jdbcType=VARCHAR},
            MakeDate              = #{makeDate,jdbcType=DATE},
            MakeTime              = #{makeTime,jdbcType=VARCHAR},
            ModifyDate            =
                #{modifyDate,jdbcType=DATE},
            ModifyTime            =
                #{modifyTime,jdbcType=VARCHAR}
        where EnsureCode =
              #{ensureCode,jdbcType=VARCHAR}
    </update>
    <update id="updateByEnsureCode">
        update fcensure
        set ClientNo=#{clientNo,jdbcType=VARCHAR}
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR};
    </update>
    <update id="updateEnsureState">
        update fcensure
        set EnsureState='021'
        where EnsureCode = #{ensureCode}
    </update>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcensure where 1=1
        <if test="ensureName != null">
            and EnsureCode = #{ensureName,jdbcType=VARCHAR}
        </if>
    </select>

    <!-- 企业保障信息查询 -->
    <select id="selectCompanyEnsureInfo" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        fc.EnsureCode,fc.AppntYear,fc.EnsureName,fc.EnsureType,fc.PlanType,fc.CvaliDate,fc.PolicyEndDate,
        (select count(*) from fcorderitem fi,fcorder fd,fcgrporder fg
        where fi.orderno=fd.orderno and fd.GrpOrderNo=fg.GrpOrderNo
        and fg.EnsureCode=fc.EnsureCode) InsuredNumber,(select if(sum(fp.totalprem) is null,0,sum(fp.totalprem)) from
        fcorderitem fi,fcorder fd,fcgrporder fg ,fcorderinsured fo,
        fcstafffamilyrela ft,fcorderitemdetail fl,fcensureplan fp
        where fi.orderno=fd.orderno and fd.GrpOrderNo=fg.GrpOrderNo and fi.orderitemdetailno=fl.orderitemdetailno
        and fd.perno=ft.perno and fl.productcode=fp.plancode
        and fg.EnsureCode=fp.EnsureCode
        and fo.orderitemno=fi.orderitemno
        and ft.personid=fo.personid
        and fg.EnsureCode=fc.EnsureCode) TotalPrem,
        fc.StartAppntDate,fc.EndAppntDate,fc.PolicyState
        from fcensure fc
        left join fdagentinfo fa on fc.ClientNo = fa.AgentCode
        <where>
            fc.PlanType != '2'
            <if test="grpNo != null and grpNo != ''">
                and fc.GrpNo = #{grpNo,jdbcType=VARCHAR}
            </if>
            <if test="manageCom != null and manageCom != ''">
                and fa.manageCom like #{manageCom}"%"
            </if>
            <if test="ensureState != null and ensureState != ''">
                and fc.EnsureState = #{ensureState}
            </if>
            <if test="appntYear != null and appntYear != ''">
                and fc.AppntYear = #{appntYear}
            </if>
            <if test="policyState !=null and policyState !=''">
                and fc.PolicyState = #{policyState}
            </if>
            <if test="beginDate != null and beginDate !='' ">
                <![CDATA[and DATE_FORMAT(fc.CvaliDate, '%Y-%m-%d') >=  DATE_FORMAT(#{beginDate}, '%Y-%m-%d')]]>
            </if>
            <if test="endDate != null and endDate !=''">
                <![CDATA[and DATE_FORMAT(fc.CvaliDate, '%Y-%m-%d') <=  DATE_FORMAT(#{endDate}, '%Y-%m-%d')]]>
            </if>
            <if test="sysDate != null and sysDate !=''">
                <![CDATA[and DATE_FORMAT(fc.StartAppntDate, '%Y-%m-%d') <=  DATE_FORMAT(#{sysDate}, '%Y-%m-%d')  and DATE_FORMAT(fc.EndAppntDate, '%Y-%m-%d') >=  DATE_FORMAT(#{sysDate}, '%Y-%m-%d')]]>
            </if>
        </where>
    </select>
    <select id="selectFcBatchPayBankInfo" parameterType="java.util.Map"
            resultType="com.sinosoft.eflex.model.FcBatchPayBankInfo">
        select
        fc.ensureCode,
        fc.perNo,
        fc.signSN,
        fc.bankAccCode,
        fc.transId,
        fc.orderNo,
        fc.name,
        fc.idType,
        fc.idNo,
        fc.bankAccount,
        fc.payBankCode,
        fc.reservePhone,
        fc.isSidned
        from fcbatchpaybankinfo fc
        <where>
            1=1
            <if test="perNo != null and perNo != ''">
                and fc.perNo = #{perNo}
            </if>
            <if test="ensureCode != null and ensureCode != ''">
                and fc.ensureCode = #{ensureCode}
            </if>
            <if test="orderNo != null and orderNo != ''">
                and fc.orderNo = #{orderNo}
            </if>
        </where>
    </select>


    <select id="findAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcensure
    </select>

    <select id="findEnsureListByPage" resultType="java.util.HashMap">
        select
        a.ensureName as ensureName,a.ensureCode AS ensureCode,a.EnsureType AS ensureType,
        a.orderNo,
        (SELECT CodeName FROM fdcode WHERE CodeKey = PlanType AND codetype = 'PlanType') AS planType,
        a.ensureState as ensureState,a.makeDate as makeDate,a.PolicyState as policyState,
        (SELECT CodeName FROM fdcode WHERE CodeKey = ensureState AND codetype = 'EnsureState') AS ensureStateName
        from fcensure a
        left join fcdailyinsureriskinfo b
        on a.ensureCode = b.ensureCode
        where 1=1
        <if test="grpNo != null and  grpNo != ''">
            and a.GrpNo = #{grpNo,jdbcType=VARCHAR}
        </if>
        <if test="ensureCode != null and ensureCode != '' ">
            and a.ensurecode = #{ensureCode}
        </if>
        <if test="ensureName != null and ensureName != '' ">
            and a.ensureName like CONCAT(CONCAT('%', #{ensureName}), '%')
        </if>
        <if test="ensureState != null and ensureState !='' ">
            and a.ensurestate = #{ensureState}
        </if>
        AND a.ensurestate IN (0,02,03,04,05,06,08,09,1,012,016,013,021)
        AND
        (case when a.plantype in ('0','1') then 1=1
        when a.plantype='2' then b.riskCode != '16380' end)
        ORDER BY a.ModifyDate desc,a.ModifyTime desc
    </select>


    <select id="findEnsureListByPagePhone" resultMap="BaseResultMap" parameterType="com.sinosoft.eflex.model.FCEnsure">
        select
        a.ensureName as ensureName,a.ensureCode AS ensureCode,a.EnsureType AS ensureType, a.startAppntDate as
        startAppntDate,a.endAppntDate as endAppntDate,
        a.orderNo,
        (SELECT CodeName FROM fdcode WHERE CodeKey = PlanType AND codetype = 'PlanType') AS planType,
        a.ensureState as ensureState,a.makeDate as makeDate,a.PolicyState as policyState,
        (SELECT CodeName FROM fdcode WHERE CodeKey = ensureState AND codetype = 'EnsureState') AS ensureStateName
        from fcensure a
        left join fcdailyinsureriskinfo b
        on a.ensureCode = b.ensureCode
        where 1=1
        <if test="grpNo != null and  grpNo != ''">
            and a.GrpNo = #{grpNo,jdbcType=VARCHAR}
        </if>
        <if test="ensureCode != null and ensureCode != '' ">
            and a.ensurecode = #{ensureCode}
        </if>
        <if test="ensureName != null and ensureName != '' ">
            and a.ensureName like CONCAT(CONCAT('%', #{ensureName}), '%')
        </if>
        <if test="ensureState != null and ensureState !='' ">
            and a.ensurestate = #{ensureState}
        </if>
        AND a.ensurestate IN (0,02,03,04,05,06,08,09,1,012,016,013,021)
        AND
        (case when a.plantype in ('0','1') then 1=1
        when a.plantype='2' then b.riskCode != '16380' end)
        ORDER BY a.ModifyDate desc,a.ModifyTime desc
    </select>


    <select id="findEnsureList" resultType="com.sinosoft.eflex.model.FCEnsure">
        select
        <include refid="Base_Column_List"/>
        from fcensure where
        1=1
        <if test="grpNo != null">
            and GrpNo = #{grpNo,jdbcType=VARCHAR}
        </if>
        <if test="ensureCode != null">
            and ensurecode = #{ensureCode}
        </if>
        <if test="ensureName != null">
            and ensureName = #{ensureName}
        </if>
        <if test="ensureState != null">
            and ensurestate = #{ensureState}
        </if>
        <if test="sysDate != null and sysDate !=''">
            <![CDATA[and DATE_FORMAT(StartAppntDate, '%Y-%m-%d') <=  DATE_FORMAT(#{sysDate}, '%Y-%m-%d')  and DATE_FORMAT(EndAppntDate, '%Y-%m-%d') >=  DATE_FORMAT(#{sysDate}, '%Y-%m-%d')]]>
        </if>
    </select>
    <!-- 查询最新的福利编号 -->
    <select id="findNewensureCode" parameterType="java.util.Map" resultType="java.lang.String">
        select

        EnsureCode

        from fcensure where
        1=1
        <if test="grpNo != null and  grpNo != ''">
            and GrpNo = #{grpNo,jdbcType=VARCHAR}
        </if>
        <if test="ensureState != null">
            and ensurestate = #{ensureState}
        </if>
        <if test="sysDate != null and sysDate !=''">
            <![CDATA[and DATE_FORMAT(StartAppntDate, '%Y-%m-%d') <=  DATE_FORMAT(#{sysDate}, '%Y-%m-%d')  and DATE_FORMAT(EndAppntDate, '%Y-%m-%d') >=  DATE_FORMAT(#{sysDate}, '%Y-%m-%d')]]>
        </if>
        order by ModifyDATE DESC,MOdifytime DESC limit 1
    </select>
    <select id="checkEnsureNameIsExists" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(1) from FCEnsure where
        ensureName=#{ensureName,jdbcType=VARCHAR}
        <if test="ensureCode != null and ensureCode !=''">
            and EnsureCode!=#{ensureCode,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectEnsureList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcensure where
        1=1
        <if test="grpNo != null and  grpNo != ''">
            and GrpNo = #{grpNo,jdbcType=VARCHAR}
        </if>
        <if test="ensureCode != null and ensureCode != '' ">
            and ensurecode = #{ensureCode}
        </if>
        <if test="ensureState != null and ensureState !='' ">
            and ensurestate = #{ensureState}
        </if>
        <if test="policyState != null and policyState !='' ">
            and PolicyState = #{policyState}
        </if>
        <if test="sysDate != null and sysDate !=''">
            <![CDATA[ and DATE_FORMAT(EndAppntDate, '%Y-%m-%d') < DATE_FORMAT(#{sysDate}, '%Y-%m-%d')]]>
        </if>
        ORDER BY ensureCode
    </select>

    <select id="getEffEctiveEnsure" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT 1
        FROM fcensure
        WHERE ensureCode = #{ensureCode}
            /*<![CDATA[ AND CvaliDate <= CURDATE() ]]>*/
		<![CDATA[ AND PolicyEndDate >= CURDATE()
        ]]>
    </select>

    <select id="getFCEnsureByPlanCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM fcensure WHERE ensureCode = #{ensureCode}
    </select>

    <select id="getFcensureByGrpContNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM fcensure WHERE ensureCode in (SELECT ensurecode FROM fcgrporder WHERE GrpContNo = #{grpContNo})
    </select>

    <select id='selectGrpNoByPLan' parameterType="java.lang.String" resultType="java.lang.String">
        select grpno
        from fcensure a
                 LEFT JOIN fcensureplan b on a.ensurecode = b.ensurecode
        where b.plancode = #{plancode}
    </select>
    <select id='selectAllEnsure' parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCEnsureGrpInfo">
        SELECT a.ensureState,
               a.policyState,
               a.planType,
               a.cvaliDate,
               a.policyEndDate,
               a.EnsureType,
               a.EnsureCode,
               a.EnsureName,
               b.GrpNo,
               b.GrpName,
               e.perNo,
               IF(f.`OrderStatus` IS NULL, '', f.`OrderStatus`) AS OrderStatus
        FROM fcensure a
                 LEFT JOIN fcgrpinfo b ON a.GrpNo = b.GrpNo
                 LEFT JOIN fcperinfo c ON b.GrpNo = c.GrpNo
                 LEFT JOIN fcperinfo d ON c.IDNo = d.IDNo
                 LEFT JOIN fcperregistday e ON c.perno = e.perno AND a.EnsureCode = e.EnsureCode
                 LEFT JOIN fcorder f
                           ON f.`GrpOrderNo` = (SELECT GrpOrderNo FROM fcgrporder WHERE EnsureCode = a.`EnsureCode`) AND
                              f.`PerNo` = c.`PerNo`
        where d.perno = #{perNo}
          and e.LockState = '0'
          and a.ensureState = '1'
     		<![CDATA[and DATE_FORMAT(e.OpenDay, '%Y-%m-%d') <= DATE_FORMAT(now(), '%Y-%m-%d')
          and DATE_FORMAT(e.CloseDay, '%Y-%m-%d') >= DATE_FORMAT(now(), '%Y-%m-%d')]]>
          and e.isValidy = '1'
        order by a.Makedate desc, a.Maketime desc, a.Modifydate desc, a.Modifytime desc
    </select>
    <select id='selectAllEnsureandGrpNo' resultType="com.sinosoft.eflex.model.FCEnsureGrpInfo">
        SELECT a.ensureState,
               a.policyState,
               a.planType,
               a.cvaliDate,
               a.policyEndDate,
               a.EnsureType,
               a.EnsureCode,
               a.EnsureName,
               b.GrpNo,
               b.GrpName,
               e.perNo,
               IF(f.`OrderStatus` IS NULL, '', f.`OrderStatus`) AS OrderStatus
        FROM fcensure a
                 LEFT JOIN fcgrpinfo b ON a.GrpNo = b.GrpNo
                 LEFT JOIN fcperinfo c ON b.GrpNo = c.GrpNo
                 LEFT JOIN fcperinfo d ON c.IDNo = d.IDNo
                 LEFT JOIN fcperregistday e ON c.perno = e.perno AND a.EnsureCode = e.EnsureCode
                 LEFT JOIN fcorder f
                           ON f.`GrpOrderNo` = (SELECT GrpOrderNo FROM fcgrporder WHERE EnsureCode = a.`EnsureCode`) AND
                              f.`PerNo` = c.`PerNo`
        where d.perno = #{perNo}
          and a.grpNo = #{grpNo}
            /* and a.ensureState = '1'*/
     		<![CDATA[and DATE_FORMAT(e.OpenDay, '%Y-%m-%d') <= DATE_FORMAT(now(), '%Y-%m-%d')
          and DATE_FORMAT(e.CloseDay, '%Y-%m-%d') >= DATE_FORMAT(now(), '%Y-%m-%d')]]>
          and e.isValidy = '1'
        order by a.Makedate desc, a.Maketime desc, a.Modifydate desc, a.Modifytime desc
    </select>
    <select id="selectFamTempStudentCount" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(*)
        from fcensure a
                 LEFT JOIN fcperinfofamilytemp b on a.ensurecode = b.ensurecode
                 LEFT JOIN fcperson c on b.idno = c.idno
                 LEFT JOIN fcstafffamilyrela d on c.personid = d.personid
                 LEFT JOIN fcperregistday e on d.perno = e.perno
        where a.ensureType = '1'
          and c.idno = (select idno from fcperson where personid = #{personId})
          and b.SubStaus = '02'
          AND e.IsValidy = '1'
            <![CDATA[AND DATE_FORMAT(e.OpenDay, '%Y-%m-%d') <= DATE_FORMAT(NOW(), '%Y-%m-%d')
          AND DATE_FORMAT(e.CloseDay, '%Y-%m-%d') >= DATE_FORMAT(NOW(), '%Y-%m-%d')
        ]]>
    </select>
    <select id="selectEflexEmployList" parameterType="java.util.Map" resultType="java.util.Map">
        select DISTINCT a.AmountGrageCode,
        CASE WHEN b.RiskCode='15070' then
        CASE WHEN b.RiskType='01' then CONCAT(c.RiskName,'(民航班机)')
        WHEN b.RiskType='02' then CONCAT(c.RiskName,'(轨道交通工具)')
        WHEN b.RiskType='03' then CONCAT(c.RiskName,'(水运公共交通工具)')
        WHEN b.RiskType='04' then CONCAT(c.RiskName,'(公路公共交通工具)')
        else CONCAT(c.RiskName,'(私家车)') end
        else c.RiskName end as RiskName,
        d.DutyRange DutyName,
        b.RiskCode,
        b.RiskType,
        CAST(CONVERT(b.amnt,DECIMAL(20,0)) AS Char) Amnt,
        IFNULL(a.IsDefaultFlag,'') IsDefaultFlag,
        REPLACE(CAST(FORMAT(a.DefaultDeductible,0) AS Char),',','') DefaultDeductible,
        REPLACE(CAST(FORMAT(a.DefaultCompensationRatio,0) AS Char),',','') DefaultCompensationRatio,
        date_format(e.CvaliDate,'%Y%m%d') CvaliDate,
        CAST(e.InsuredNumber AS Char) InsuredNumber,
        IF(b.RiskCode='15070','',IFNULL(b.AnnualTimeDeduction,'')) AnnualTimeDeduction
        from FcBusinessProDutyGrpObject a,
        FcDutyAmountGrade b,
        fdriskinfo c,fdriskdutyinfo d,fcensure e
        where a.AmountGrageCode=b.AmountGrageCode
        and a.EnsureCode=b.EnsureCode
        and b.RiskCode=c.RiskCode
        and c.RiskCode=d.Riskcode
        and b.DutyCode=d.DutyCode
        and b.EnsureCode=e.EnsureCode
        and a.EnsureCode=#{EnsureCode}
        <if test="LevelCode != null">
            and (#{LevelCode} BETWEEN a.GradeLevelTopLimit and a.GradeLevelLowLimit
            or (a.GradeLevelTopLimit is null and a.GradeLevelLowLimit is null)
            or (a.GradeLevelTopLimit ='' and a.GradeLevelLowLimit =''))
        </if>
        and (#{OccupationType} BETWEEN a.OccupationTypeLowLimit and a.OccupationTypeTopLimit
        or (a.OccupationTypeTopLimit is null and a.OccupationTypeLowLimit is null)
        or (a.OccupationTypeTopLimit ='' and a.OccupationTypeLowLimit = ''))
        and ((TIMESTAMPDIFF(YEAR, #{BirthDay}, date_format(e.CvaliDate,'%Y%m%d'))) BETWEEN a.AgeLowLimit and
        a.AgeTopLimit
        or (a.AgeLowLimit is null and a.AgeTopLimit is null)
        or (a.AgeLowLimit ='' and a.AgeTopLimit=''))
        and (#{Sex} = a.Sex or a.sex='2' or a.sex is null or a.sex='')
        /*and a.InsuredType='0'*/
        <!-- 服务年限 -->
        <if test="ServiceTerm != null and ServiceTerm !='' ">
            and (#{ServiceTerm}+0 between a.ComeAgeLowLimit and a.ComeAgeTopLimit or a.ComeAgeLowLimit = '' or
            a.ComeAgeLowLimit is NULL )
        </if>
        <!-- 是否退休 -->
        <if test="Retirement != null   and Retirement !='' ">
            and (#{Retirement} = a.Retirement or a.Retirement='2' or a.Retirement = '' OR a.Retirement is NULL )
        </if>
        ORDER BY RiskName,b.Amnt
    </select>
    <select id="selectEflexEmployList1"
            parameterType="com.sinosoft.eflex.model.insureEflexPlanPage.SelectMinAmntDefaultReq"
            resultType="com.sinosoft.eflex.model.insureEflexPlanPage.EflexEmployInfo">
        select
        DISTINCT
        <!-- 保额档次编码 -->
        a.AmountGrageCode as amountGrageCode,
        <!-- 险种编码 -->
        b.RiskCode riskCode,
        <!-- 险种类别 -->
        b.RiskType riskType,
        <!-- 险种名称 -->
        CASE WHEN b.RiskCode='15070' then
        CASE WHEN b.RiskType='01' then CONCAT(c.RiskName,'(民航班机)')
        WHEN b.RiskType='02' then CONCAT(c.RiskName,'(轨道交通工具)')
        WHEN b.RiskType='03' then CONCAT(c.RiskName,'(水运公共交通工具)')
        WHEN b.RiskType='04' then CONCAT(c.RiskName,'(公路公共交通工具)')
        else CONCAT(c.RiskName,'(私家车)') end
        else c.RiskName end as riskName,
        <!-- 责任编码 -->
        d.dutyCode,
        <!-- 责任名称 -->
        d.DutyRange dutyName,
        <!-- 保额 -->
        CAST(CONVERT(b.amnt,DECIMAL(20,0)) AS Char) amnt,
        <!--  是否为默认档次 -->
        IFNULL(a.IsDefaultFlag,'') isDefaultFlag,
        <!-- 免赔额 -->
        REPLACE(CAST(FORMAT(a.DefaultDeductible,0) AS Char),',','') defaultDeductible,
        <!-- 赔付比例 -->
        REPLACE(CAST(FORMAT(a.DefaultCompensationRatio,0) AS Char),',','') defaultCompensationRatio,
        <!-- 保单生效日期 -->
        date_format(e.CvaliDate,'%Y%m%d') cvaliDate,
        <!-- 投保人数 -->
        e.InsuredNumber insuredNumber,
        <!-- 按年、次免赔方式 -->
        IF(b.RiskCode='15070','',IFNULL(b.AnnualTimeDeduction,'')) annualTimeDeduction
        from FcBusinessProDutyGrpObject a,
        FcDutyAmountGrade b,
        fdriskinfo c,
        fdriskdutyinfo d,
        fcensure e
        where a.AmountGrageCode=b.AmountGrageCode
        and a.EnsureCode=b.EnsureCode
        and b.RiskCode=c.RiskCode
        and c.RiskCode=d.Riskcode
        and b.DutyCode=d.DutyCode
        and b.EnsureCode=e.EnsureCode
        and a.EnsureCode=#{ensureCode}
        <!-- 职级 -->
        <if test="levelCode != null and relation == '0'">
            and (#{levelCode} BETWEEN a.GradeLevelTopLimit and a.GradeLevelLowLimit
            or (a.GradeLevelTopLimit is null and a.GradeLevelLowLimit is null)
            or (a.GradeLevelTopLimit ='' and a.GradeLevelLowLimit =''))
        </if>
        <!-- 职业类别 -->
        and (#{occupationType} BETWEEN a.OccupationTypeLowLimit and a.OccupationTypeTopLimit
        or (a.OccupationTypeTopLimit is null and a.OccupationTypeLowLimit is null)
        or (a.OccupationTypeTopLimit ='' and a.OccupationTypeLowLimit = ''))
        <!-- 年龄 -->
        and ((TIMESTAMPDIFF(YEAR, #{birthDay}, date_format(e.CvaliDate,'%Y%m%d'))) BETWEEN a.AgeLowLimit and
        a.AgeTopLimit
        or (a.AgeLowLimit is null and a.AgeTopLimit is null)
        or (a.AgeLowLimit ='' and a.AgeTopLimit=''))
        <!-- 性别 -->
        and (#{sex} = a.Sex or a.sex='2' or a.sex is null or a.sex='')
        <!-- 被保人类型 0-员工 家属应根据关系或者null来判断 -->
        <if test=' relation == "0" '>
            and #{relation} = a.InsuredType
        </if>
        <if test=' relation != "0" '>
            and (#{relation} in ((SELECT SUBSTRING_INDEX(SUBSTRING_INDEX(a.InsuredType, '@', seq),'@' ,-1) sub_id
            FROM sequence
            WHERE seq BETWEEN 1 AND (SELECT 1 + LENGTH(a.InsuredType) - LENGTH(replace(a.InsuredType, '@','')))))
            or a.InsuredType = ''
            or a.InsuredType is NULL)
        </if>
        <!-- 服务年限 -->
        <if test="relation == '0' and serviceTerm != null">
            and (#{serviceTerm}+0 between a.ComeAgeLowLimit and a.ComeAgeTopLimit or a.ComeAgeLowLimit = '' or
            a.ComeAgeLowLimit is NULL )
        </if>
        <!-- 是否退休 -->
        <if test="relation == '0' and retirement != null">
            and (#{retirement} = a.Retirement or a.Retirement='2' or a.Retirement = '' OR a.Retirement is NULL )
        </if>
        ORDER BY RiskName,b.Amnt
    </select>
    <select id="selectInsureRiskInfoList"
            parameterType="com.sinosoft.eflex.model.insureEflexPlanPage.SelectMinAmntDefaultReq"
            resultType="com.sinosoft.eflex.model.insureEflexPlanPage.InsureRiskInfo">
        select
        DISTINCT
        <!-- 险种名称 -->
        CASE WHEN b.RiskCode='15070' then
        CASE WHEN b.RiskType='01' then CONCAT(c.RiskName,'(民航班机)')
        WHEN b.RiskType='02' then CONCAT(c.RiskName,'(轨道交通工具)')
        WHEN b.RiskType='03' then CONCAT(c.RiskName,'(水运公共交通工具)')
        WHEN b.RiskType='04' then CONCAT(c.RiskName,'(公路公共交通工具)')
        else CONCAT(c.RiskName,'(私家车)') end
        else c.RiskName end as riskName,
        <!-- 险种编码 -->
        b.RiskCode riskCode,
        <!-- 险种类别 -->
        b.RiskType riskType,
        <!-- 责任编码 -->
        d.dutyCode,
        <!-- 责任名称 -->
        d.DutyRange dutyName
        from FcBusinessProDutyGrpObject a,
        FcDutyAmountGrade b,
        fdriskinfo c,
        fdriskdutyinfo d,
        fcensure e
        where a.AmountGrageCode=b.AmountGrageCode
        and a.EnsureCode=b.EnsureCode
        and b.RiskCode=c.RiskCode
        and c.RiskCode=d.Riskcode
        and b.DutyCode=d.DutyCode
        and b.EnsureCode=e.EnsureCode
        and a.EnsureCode=#{ensureCode}
        <!-- 职级 -->
        <if test="levelCode != null">
            and (#{levelCode} BETWEEN a.GradeLevelTopLimit and a.GradeLevelLowLimit
            or (a.GradeLevelTopLimit is null and a.GradeLevelLowLimit is null)
            or (a.GradeLevelTopLimit ='' and a.GradeLevelLowLimit =''))
        </if>
        <!-- 职业类别 -->
        and (#{occupationType} BETWEEN a.OccupationTypeLowLimit and a.OccupationTypeTopLimit
        or (a.OccupationTypeTopLimit is null and a.OccupationTypeLowLimit is null)
        or (a.OccupationTypeTopLimit ='' and a.OccupationTypeLowLimit = ''))
        <!-- 年龄 -->
        and ((TIMESTAMPDIFF(YEAR, #{birthDay}, date_format(e.CvaliDate,'%Y%m%d'))) BETWEEN a.AgeLowLimit and
        a.AgeTopLimit
        or (a.AgeLowLimit is null and a.AgeTopLimit is null)
        or (a.AgeLowLimit ='' and a.AgeTopLimit=''))
        <!-- 性别 -->
        and (#{sex} = a.Sex or a.sex='2' or a.sex is null or a.sex='')
        <!-- 被保人类型 0-员工 家属，投保对象存储的值居然为空，经测试是把与本人关系存储到了这里 -->
        <if test=' relation == "0" '>
            and a.InsuredType = #{relation}
        </if>
        <if test=' relation != "0" '>
            and (#{relation} in ((SELECT SUBSTRING_INDEX(SUBSTRING_INDEX(a.InsuredType, '@', seq),'@' ,-1) sub_id
            FROM sequence
            WHERE seq BETWEEN 1 AND (SELECT 1 + LENGTH(a.InsuredType) - LENGTH(replace(a.InsuredType, '@','')))))
            or a.InsuredType = ''
            or a.InsuredType is NULL)
        </if>
        <!-- 服务年限 -->
        <if test=" relation == '0' and serviceTerm != null">
            and (#{serviceTerm}+0 between a.ComeAgeLowLimit and a.ComeAgeTopLimit or a.ComeAgeLowLimit = '' or
            a.ComeAgeLowLimit is NULL )
        </if>
        <!-- 是否退休 -->
        <if test=" relation == '0' and retirement != null">
            and (#{retirement} = a.Retirement or a.Retirement='2' or a.Retirement = '' OR a.Retirement is NULL )
        </if>
        ORDER BY RiskName,b.Amnt
    </select>
    <select id="selectMinAmntDefault" parameterType="java.util.Map" resultType="java.util.Map">
        select DISTINCT a.AmountGrageCode,
        b.RiskCode,
        b.RiskType,
        CAST(CONVERT(MIN(b.Amnt),DECIMAL(20,0)) AS CHAR) Amnt
        from FcBusinessProDutyGrpObject a,
        FcDutyAmountGrade b,
        fdriskinfo c,fdriskdutyinfo d,fcensure e
        where a.AmountGrageCode=b.AmountGrageCode
        and a.EnsureCode=b.EnsureCode
        and b.RiskCode=c.RiskCode
        and c.RiskCode=d.Riskcode
        and b.DutyCode=d.DutyCode
        and b.EnsureCode=e.EnsureCode
        and a.EnsureCode=#{EnsureCode}
        and (#{LevelCode} BETWEEN a.GradeLevelTopLimit and a.GradeLevelLowLimit
        or (a.GradeLevelTopLimit is null and a.GradeLevelLowLimit is null)
        or (a.GradeLevelTopLimit ='' and a.GradeLevelLowLimit =''))
        and (#{OccupationType} BETWEEN a.OccupationTypeLowLimit and a.OccupationTypeTopLimit
        or (a.OccupationTypeTopLimit is null and a.OccupationTypeLowLimit is null)
        or (a.OccupationTypeTopLimit ='' and a.OccupationTypeLowLimit = ''))
        and ((TIMESTAMPDIFF(YEAR, #{BirthDay}, date_format(e.CvaliDate,'%Y%m%d'))) BETWEEN a.AgeLowLimit and
        a.AgeTopLimit
        or (a.AgeLowLimit is null and a.AgeTopLimit is null)
        or (a.AgeLowLimit ='' and a.AgeTopLimit=''))
        and (#{Sex} = a.Sex or a.sex='2' or a.sex is null or a.sex='')
        <!-- 服务年限 -->
        <if test="ServiceTerm != null">
            and (#{ServiceTerm}+0 between a.ComeAgeLowLimit and a.ComeAgeTopLimit or a.ComeAgeLowLimit = '' or
            a.ComeAgeLowLimit is NULL )
        </if>
        <!-- 是否退休 -->
        <if test="Retirement != null">
            and (#{Retirement} = a.Retirement or a.Retirement='2' or a.Retirement = '' OR a.Retirement is NULL )
        </if>
        and a.InsuredType='0'
        and a.IsDefaultFlag='1'
        group by b.RiskCode,
        b.RiskType
    </select>
    <select id="selectMinAmntDefault1"
            parameterType="com.sinosoft.eflex.model.insureEflexPlanPage.SelectMinAmntDefaultReq"
            resultType="com.sinosoft.eflex.model.insureEflexPlanPage.EflexEmployInfo">
        select DISTINCT
        <!-- 保额档次 -->
        a.AmountGrageCode as amountGrageCode,
        <!-- 险种编码 -->
        b.RiskCode as riskCode,
        <!-- 险种类别 -->
        b.RiskType as riskType,
        <!-- 保额 -->
        CAST(CONVERT(MIN(b.Amnt),DECIMAL(20,0)) AS CHAR) amnt
        from FcBusinessProDutyGrpObject a,
        FcDutyAmountGrade b,
        fdriskinfo c,
        fdriskdutyinfo d,
        fcensure e
        where a.AmountGrageCode=b.AmountGrageCode
        and a.EnsureCode=b.EnsureCode
        and b.RiskCode=c.RiskCode
        and c.RiskCode=d.Riskcode
        and b.DutyCode=d.DutyCode
        and b.EnsureCode=e.EnsureCode
        and a.EnsureCode=#{ensureCode}
        <!-- 职级 -->
        and (#{levelCode} BETWEEN a.GradeLevelTopLimit and a.GradeLevelLowLimit
        or (a.GradeLevelTopLimit is null and a.GradeLevelLowLimit is null)
        or (a.GradeLevelTopLimit ='' and a.GradeLevelLowLimit =''))
        <!-- 职业类别 -->
        and (#{occupationType} BETWEEN a.OccupationTypeLowLimit and a.OccupationTypeTopLimit
        or (a.OccupationTypeTopLimit is null and a.OccupationTypeLowLimit is null)
        or (a.OccupationTypeTopLimit ='' and a.OccupationTypeLowLimit = ''))
        <!-- 出生日期 -->
        and ((TIMESTAMPDIFF(YEAR, #{birthDay}, date_format(e.CvaliDate,'%Y%m%d'))) BETWEEN a.AgeLowLimit and
        <!-- 年龄-->
        a.AgeTopLimit
        or (a.AgeLowLimit is null and a.AgeTopLimit is null)
        or (a.AgeLowLimit ='' and a.AgeTopLimit=''))
        and (#{sex} = a.Sex or a.sex='2' or a.sex is null or a.sex='')
        <!-- 服务年限 -->
        <if test="serviceTerm != null">
            and (#{serviceTerm}+0 between a.ComeAgeLowLimit and a.ComeAgeTopLimit or a.ComeAgeLowLimit = '' or
            a.ComeAgeLowLimit is NULL )
        </if>
        <!-- 是否退休 -->
        <if test="retirement != null">
            and (#{retirement} = a.Retirement or a.Retirement='2' or a.Retirement = '' OR a.Retirement is NULL )
        </if>
        <!-- 被保人类型 0-员工 1-家属 -->
        and a.InsuredType='0'
        <!-- 默认档次 -->
        and a.IsDefaultFlag='1'
        group by b.RiskCode,b.RiskType
    </select>
    <select id="selectEflexEmployFamilyList" parameterType="java.util.Map" resultType="java.util.Map">
        select
        DISTINCT
        <!-- 保额档次编码 -->
        a.AmountGrageCode,
        <!-- 险种编码 -->
        b.RiskCode,
        <!-- 险种类别 -->
        b.RiskType,
        <!-- 险种名称 -->
        CASE WHEN b.RiskCode='15070' then
        CASE WHEN b.RiskType='01' then CONCAT(c.RiskName,'(民航班机)')
        WHEN b.RiskType='02' then CONCAT(c.RiskName,'(轨道交通工具)')
        WHEN b.RiskType='03' then CONCAT(c.RiskName,'(水运公共交通工具)')
        WHEN b.RiskType='04' then CONCAT(c.RiskName,'(公路公共交通工具)')
        else CONCAT(c.RiskName,'(私家车)') end
        else c.RiskName end as RiskName,
        <!-- 责任编码 -->
        d.dutyCode,
        <!-- 责任名称 -->
        d.DutyRange DutyName,
        <!-- 保额 -->
        CAST(CONVERT(b.Amnt,DECIMAL(20,0)) AS Char) Amnt,
        <!-- 是否为默认档次 -->
        IFNULL(a.IsDefaultFlag,'') IsDefaultFlag,
        <!-- 免赔额 -->
        REPLACE(CAST(FORMAT(a.DefaultDeductible,0) AS Char),',','') DefaultDeductible,
        <!-- 赔付比例 -->
        REPLACE(CAST(FORMAT(a.DefaultCompensationRatio,0) AS Char),',','') DefaultCompensationRatio,
        <!-- 保单生效日期 -->
        date_format(e.CvaliDate,'%Y%m%d') CvaliDate,
        <!-- 投保人数 -->
        CAST(e.InsuredNumber AS Char) InsuredNumber,
        <!-- 免赔方式 -->
        IF(b.RiskCode='15070','',IFNULL(b.AnnualTimeDeduction,'')) AnnualTimeDeduction
        from FcBusinessProDutyGrpObject a,
        FcDutyAmountGrade b,
        fdriskinfo c,fdriskdutyinfo d,fcensure e
        where a.AmountGrageCode=b.AmountGrageCode
        and a.EnsureCode=b.EnsureCode
        and b.RiskCode=c.RiskCode
        and c.RiskCode=d.Riskcode
        and b.DutyCode=d.DutyCode
        and b.EnsureCode=e.EnsureCode
        <!-- 福利编码 -->
        and a.EnsureCode=#{EnsureCode}
        <!-- 职业类别 -->
        and (#{OccupationType} BETWEEN a.OccupationTypeLowLimit and a.OccupationTypeTopLimit
        or (a.OccupationTypeTopLimit is null and a.OccupationTypeLowLimit is null)
        or (a.OccupationTypeTopLimit ='' and a.OccupationTypeLowLimit = ''))
        <!-- 年龄 -->
        and ((TIMESTAMPDIFF(YEAR, #{BirthDay}, date_format(e.CvaliDate,'%Y%m%d'))) BETWEEN a.AgeLowLimit and
        a.AgeTopLimit
        or (a.AgeLowLimit is null and a.AgeTopLimit is null)
        or (a.AgeLowLimit ='' and a.AgeTopLimit=''))
        <!-- 性别 -->
        and (#{Sex} = a.Sex or a.sex='2' or a.sex is null or a.sex='')
        <if test=' Relation == "0" '>
            and #{Relation} = a.InsuredType
        </if>
        <if test=' Relation != "0" '>
            and (#{Relation} in ((SELECT SUBSTRING_INDEX(SUBSTRING_INDEX(a.InsuredType, '@', seq),'@' ,-1) sub_id
            FROM sequence
            WHERE seq BETWEEN 1 AND (SELECT 1 + LENGTH(a.InsuredType) - LENGTH(replace(a.InsuredType, '@','')))))
            or a.InsuredType = ''
            or a.InsuredType is NULL)
        </if>
        ORDER BY RiskName,b.Amnt
    </select>
    <select id="selectFamMinAmntDefault" parameterType="java.util.Map" resultType="java.util.Map">
        select DISTINCT a.AmountGrageCode,
        b.RiskCode,
        b.RiskType,
        CAST(CONVERT(MIN(b.Amnt),DECIMAL(20,0)) AS CHAR) Amnt
        from FcBusinessProDutyGrpObject a,
        FcDutyAmountGrade b,
        fdriskinfo c,fdriskdutyinfo d,fcensure e
        where a.AmountGrageCode=b.AmountGrageCode
        and a.EnsureCode=b.EnsureCode
        and b.RiskCode=c.RiskCode
        and c.RiskCode=d.Riskcode
        and b.DutyCode=d.DutyCode
        and b.EnsureCode=e.EnsureCode
        and a.EnsureCode=#{EnsureCode}
        and (#{OccupationType} BETWEEN a.OccupationTypeLowLimit and a.OccupationTypeTopLimit
        or (a.OccupationTypeTopLimit is null and a.OccupationTypeLowLimit is null)
        or (a.OccupationTypeTopLimit ='' and a.OccupationTypeLowLimit = ''))
        and ((TIMESTAMPDIFF(YEAR, #{BirthDay}, date_format(e.CvaliDate,'%Y%m%d'))) BETWEEN a.AgeLowLimit and
        a.AgeTopLimit
        or (a.AgeLowLimit is null and a.AgeTopLimit is null)
        or (a.AgeLowLimit ='' and a.AgeTopLimit=''))
        and (#{Sex} = a.Sex or a.sex='2' or a.sex is null or a.sex='')
        <if test=' Relation == "0" '>
            and #{Relation} = a.InsuredType
        </if>
        <if test=' Relation != "0" '>
            and (#{Relation} in ((SELECT SUBSTRING_INDEX(SUBSTRING_INDEX(a.InsuredType, '@', seq),'@' ,-1) sub_id
            FROM sequence WHERE seq BETWEEN 1 AND (SELECT 1 + LENGTH(a.InsuredType) - LENGTH(replace(a.InsuredType, '@',
            '')))
            )) or a.InsuredType = '' OR a.InsuredType is NULL )
        </if>
        and a.IsDefaultFlag='1'
        group by b.RiskCode,
        b.RiskType
    </select>
    <select id="selectDeductibleList" parameterType="java.lang.String" resultType="java.lang.String">
        select REPLACE(CAST(FORMAT(Deductible, 0) AS Char), ',', '')
        from FcDutyGroupDeductible
        where AmountGrageCode = #{amountGrageCode}
    </select>
    <select id="selectCheckDeductibleList" parameterType="java.lang.String" resultType="java.util.Map">
        select
        <if test="deductible != null and deductible != ''">
            IF(Deductible=#{deductible},'1','0') isChecked,
        </if>
        CAST(FORMAT(Deductible,0) AS Char) Deductible
        from FcDutyGroupDeductible
        where AmountGrageCode=#{amountGrageCode}
    </select>
    <select id="selectCompensationRatioList" parameterType="java.lang.String" resultType="java.lang.String">
        select REPLACE(CAST(FORMAT(CompensationRatio, 0) AS Char), ',', '')
        from FcDutGradeCompensationRatio
        where AmountGrageCode = #{amountGrageCode}
    </select>
    <select id="selectCheckCompensationRatioList" parameterType="java.lang.String" resultType="java.util.Map">
        select
        <if test="compensationRatio != null and compensationRatio != ''">
            IF(CompensationRatio=#{compensationRatio},'1','0') isChecked,
        </if>
        CAST(FORMAT(CompensationRatio,0) AS Char) CompensationRatio
        from FcDutGradeCompensationRatio where AmountGrageCode=#{amountGrageCode}
    </select>
    <select id="selectOptDutyCodeList" parameterType="java.lang.String" resultType="java.util.Map">
        select distinct b.DutyRange DutyName, CAST(a.Amnt AS Char) Amnt, a.OptDutyCode, '0' Prem
        from FcDutyGradeOptionalAmountInfo a,
             fdriskdutyinfo b
        where a.OptDutyCode = b.dutycode
          and a.AmountGrageCode = #{amountGrageCode}
          and b.riskCode = #{riskCode}
    </select>
    <select id="selectOptDutyCodeInfoList" parameterType="java.lang.String"
            resultType="com.sinosoft.eflex.model.insureEflexPlanPage.OptDutyCodeInfo">
        select distinct b.DutyRange DutyName, CAST(a.Amnt AS Char) amount, a.OptDutyCode, '0' Prem
        from FcDutyGradeOptionalAmountInfo a,
             fdriskdutyinfo b
        where a.OptDutyCode = b.dutycode
          and a.AmountGrageCode = #{amountGrageCode}
          and b.riskCode = #{riskCode}
    </select>
    <select id="selectGrpInfo" resultType="com.sinosoft.eflex.model.FCGrpInfo">
        SELECT DISTINCT b.GrpNo, b.GrpName
        FROM fcensure a,
             fcgrpinfo b
        WHERE a.GrpNo = b.GrpNo
          and a.EnsureState = '1'
        ORDER BY a.ModifyDate Desc, a.ModifyTime Desc
    </select>
    <select id="getEnsureByGrpNo" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCEnsure">
        SELECT *
        FROM fcensure e
                 INNER JOIN fcensurecontact c ON c.`EnsureCode` = e.`EnsureCode`
        WHERE e.GrpNo = #{grpNo}
          AND c.`IDNo` = #{IDNo}
          AND EnsureState = '1'
        ORDER BY e.EnsureCode DESC LIMIT 1
    </select>
    <select id="selectByEnsureName" parameterType="java.lang.String"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcensure
        where EnsureName = #{EnsureName,jdbcType=VARCHAR}
    </select>
    <select id="checkEnsureNameIsExists_Daily" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(1) from FCEnsure where
        grpNo=#{grpNo,jdbcType=VARCHAR}
        and PlanType='2'
        <if test="EnsureName != null and EnsureName !=''">
            and EnsureName=#{EnsureName,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectEnsureList_Daily" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcensure where
        1=1
        and PlanType='2'
        and GrpNo = #{grpNo,jdbcType=VARCHAR}
        and EnsureState in('012','013','016','017','014','015','018')
    </select>
    <select id="selectByPrimaryKey_Daily" parameterType="java.lang.String"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcensure
        where PlanType='2'
        and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
    </select>
    <select id="selectDailyPlan" resultType="java.util.Map">
        select fc.EnsureCode ensureCode,
        fc.EnsureName ensureName,
        fc.PlanType planType,
        f1.CodeName planTypeName,
        fc.AppntYear appntYear,
        date_format(fc.StartAppntDate, '%Y-%m-%d') startAppntDate,
        date_format(fc.EndAppntDate, '%Y-%m-%d') endAppntDate,
        date_format(fc.CvaliDate, '%Y-%m-%d') cvaliDate,
        date_format(fc.PolicyEndDate, '%Y-%m-%d') policyEndDate,
        fc.PolicyState policyState,
        f2.CodeName policyStateName,
        count(fi.OrderItemNo) insuredNumber,
        sum(fi.SelfPrem + fi.GrpPrem) totalPrem
        from fcensure fc
        inner join fcgrporder fg on fg.EnsureCode = fc.EnsureCode
        inner join fcorder fd on fd.GrpOrderNo = fg.GrpOrderNo
        inner join fcorderitem fi on fi.OrderNo = fd.OrderNo
        left join fdcode f1 on f1.CodeType = 'PlanType' and f1.CodeKey = fc.PlanType
        left join fdcode f2 on f2.CodeType = 'policyState' and f2.CodeKey = fc.PolicyState
        where fc.GrpNo = #{grpNo} and fc.PlanType = '2' and fc.PolicyState = '3' and fd.OrderStatus='08'
        <if test="ensureName != null and ensureName != ''">
            and fc.EnsureName like concat('%',#{ensureName},'%')
        </if>
        <if test="beginDate != null and beginDate !='' ">
            <![CDATA[and DATE_FORMAT(fc.CvaliDate, '%Y-%m-%d') >=  DATE_FORMAT(#{beginDate}, '%Y-%m-%d')]]>
        </if>
        <if test="endDate != null and endDate !=''">
            <![CDATA[and DATE_FORMAT(fc.CvaliDate, '%Y-%m-%d') <=  DATE_FORMAT(#{endDate}, '%Y-%m-%d')]]>
        </if>
        <if test="sysDate != null and sysDate !=''">
            <![CDATA[and DATE_FORMAT(fc.StartAppntDate, '%Y-%m-%d') <=  DATE_FORMAT(#{sysDate}, '%Y-%m-%d')  and DATE_FORMAT(fc.EndAppntDate, '%Y-%m-%d') >=  DATE_FORMAT(#{sysDate}, '%Y-%m-%d')]]>
        </if>
        group by fc.EnsureCode
    </select>
    <select id="selectDaliyPlanInfo" resultType="java.util.Map">
        SELECT d.ProductCode           productCode,
               d.DutyCode              dutyCode,
               d.PlanName              planName,
               d.PayFrequency          payFrequency,
               COUNT(*)             AS insuredNum,
               SUM(d.InsuredAmount) AS insuredAmounts,
               SUM(d.totalPrem)     AS totalPrem
        FROM (SELECT a.orderitemdetailNo,
                     f1.PlanName,
                     a.productCode,
                     a.DutyCode,
                     a.InsuredAmount,
                     a.InsurePeriod,
                     a.payFrequency,
                     a.payperiod,
                     b.selfprem + b.grpprem                       AS totalpremone,
                     CASE
                         WHEN (a.payFrequency = '1' AND a.payPeriod = '01') THEN b.SelfPrem + b.GrpPrem
                         ELSE CAST((b.SelfPrem + b.GrpPrem) *
                                   (CASE
                                        WHEN a.payPeriod = '02' THEN 5
                                        WHEN a.payPeriod = '03' THEN 10
                                        ELSE 20 END) AS CHAR) END AS totalPrem
              FROM fcorderitemdetail a
                       INNER JOIN fcorderitem b ON a.orderitemdetailNo = b.orderitemdetailNo
                       INNER JOIN FdRIskPlanInfo f1 ON f1.PlanCode = a.DutyCode
              WHERE a.OrderItemDetailNo IN (
                  SELECT orderitemdetailNo
                  FROM fcorderitem
                  WHERE orderNo IN
                        (SELECT orderNo
                         FROM fcorder
                         WHERE grpOrderNo =
                               (SELECT grpOrderNo FROM fcgrporder WHERE ensureCode = #{ensureCode}and OrderStatus='08')))) d
        GROUP BY d.dutyCode
        UNION
        SELECT fd.RiskCode,
               fd.PlanCode,
               fd.PlanName,
               ''      payFrequency,
               0    AS insuredNum,
               0.00 AS insuredAmounts,
               0.00 AS totalPrem
        FROM FdRIskPlanInfo fd
        WHERE PlanCode NOT IN (
            SELECT f1.DutyCode
            FROM fcorderitemdetail f1
                     INNER JOIN fcorderitem f2 ON f2.OrderItemDetailNo = f1.OrderItemDetailNo
                     INNER JOIN fcorder f3 ON f3.OrderNo = f2.OrderNo
                     INNER JOIN fcgrporder f4 ON f4.GrpOrderNo = f3.GrpOrderNo
            WHERE f4.ensureCode = #{ensureCode}
        )
    </select>
    <select id="selectDaliyPlanInfoNew" resultType="java.util.Map">
        SELECT d.ProductCode           productCode,
               d.DutyCode              dutyCode,
               d.PlanName              planName,
               d.PayFrequency          payFrequency,
               COUNT(*)             AS insuredNum,
               SUM(d.InsuredAmount) AS insuredAmounts,
               SUM(d.totalPrem)     AS totalPrem
        FROM (SELECT a.orderitemdetailNo,
                     f1.PlanName,
                     a.productCode,
                     a.DutyCode,
                     a.InsuredAmount,
                     a.InsurePeriod,
                     a.payFrequency,
                     a.payperiod,
                     b.selfprem + b.grpprem AS totalpremone,
                     b.SelfPrem + b.GrpPrem AS totalPrem
              FROM fcorderitemdetail a
                       INNER JOIN fcorderitem b ON a.orderitemdetailNo = b.orderitemdetailNo
                       INNER JOIN FdRIskPlanInfo f1 ON f1.PlanCode = a.DutyCode
              WHERE a.OrderItemDetailNo IN (
                  SELECT orderitemdetailNo
                  FROM fcorderitem
                  WHERE orderNo IN
                        (SELECT orderNo
                         FROM fcorder
                         WHERE grpOrderNo =
                               (SELECT grpOrderNo FROM fcgrporder WHERE ensureCode = #{ensureCode}and OrderStatus='08')))) d
        GROUP BY d.dutyCode
        UNION
        SELECT fd.RiskCode,
               fd.PlanCode,
               fd.PlanName,
               ''      payFrequency,
               0    AS insuredNum,
               0.00 AS insuredAmounts,
               0.00 AS totalPrem
        FROM FdRIskPlanInfo fd
        WHERE PlanCode NOT IN (
            SELECT f1.DutyCode
            FROM fcorderitemdetail f1
                     INNER JOIN fcorderitem f2 ON f2.OrderItemDetailNo = f1.OrderItemDetailNo
                     INNER JOIN fcorder f3 ON f3.OrderNo = f2.OrderNo
                     INNER JOIN fcgrporder f4 ON f4.GrpOrderNo = f3.GrpOrderNo
            WHERE f4.ensureCode = #{ensureCode}
        )
    </select>
    <select id="selectDaliyPlanInfoForPrem" resultType="java.util.Map">
        SELECT d.ProductCode                    productCode,
               d.PayFrequency                   payFrequency,
               d.PayPeriod                      payPeriod,
               d.DutyCode                       dutyCode,
               cast(d.InsuredAmount as char) as insuredAmounts,
               cast(c.`GrpPrem` as char)     as grpPrem,
               CAST(c.`SelfPrem` as char)    as selfPrem,
               f1.PlanName                      planName
        FROM fcgrporder a
                 INNER JOIN fcorder b ON a.GrpOrderNo = b.GrpOrderNo
                 INNER JOIN fcorderitem c ON b.OrderNo = c.OrderNo
                 INNER JOIN fcorderitemdetail d ON c.OrderItemDetailNo = d.OrderItemDetailNo
                 LEFT JOIN FdRIskPlanInfo f1 ON f1.PlanCode = d.DutyCode
        WHERE a.EnsureCode = #{ensureCode}
    </select>
    <select id="selectishasDaily" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM fcorderinsured fi
        WHERE GrpOrderNo = #{GrpOrderNo}
          AND IDNo = #{IDNo}
    </select>
    <select id="selectishasonDaily" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM fcorderinsured fi
        WHERE IDNo = #{IDNo}
    </select>
    <select id="selectAllDailyPlan" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        fc.`EnsureCode`AS EnsureCode,
        fc.`EnsureName` AS EnsureName,
        fg.`GrpName` AS GrpName,
        date_format(fc.`AppDate`,'%Y-%m-%d') AS AppDate,
        fc.`EnsureState` AS EnsureState,fc.`AuditType` AS AuditType,
        -- fo.`PrtNo` as PrtNo
        fp.`tPrtNo` as PrtNo,
        fd.`RiskCode` as RiskCode
        FROM
        fcensure fc,
        fcgrpinfo fg,
        fcgrpOrder fo,
        fcprtandcorerela fp,
        fcdailyinsureriskinfo fd,
        fdagentinfo fa
        WHERE 1=1
        and fc.PlanType='2'
        AND fc.GrpNo=fg.GrpNo
        and fo.EnsureCode=fc.EnsureCode
        and fd.EnsureCode=fc.EnsureCode
        and fc.EnsureState in ('04','08','09','010','011','012','013','014','015','016','017','018','019')
        and fo.PrtNo=fp.PrtNo
        and fc.ClientNo = fa.AgentCode
        and fa.manageCom like #{manageCom}"%"
        <if test="ensureCode != null and ensureCode != '' ">
            and fc.ensureCode = #{ensureCode}
        </if>
        <if test="ensureState != null and ensureState!= '' ">
            and fc.ensureState = #{ensureState}
        </if>
        <if test="grpName != null and grpName !='' ">
            and fg.grpName LIKE concat( '%',#{grpName},'%')
        </if>
        <if test="ensureName != null and ensureName !='' ">
            and ensureName LIKE concat('%',#{ensureName},'%')
        </if>
        <if test="appDate != null and appDate !='' ">
            and fc.appDate LIKE concat('%',#{appDate},'%')
        </if>
        order by fc.`ModifyDate` desc ,fc.`ModifyTime` desc ,fc.GrpNo desc
    </select>
    <select id="selectByEnsureState013" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        fc.`EnsureCode`AS EnsureCode,
        fc.`EnsureName` AS EnsureName,
        fg.`GrpName` AS GrpName,
        date_format(fc.`AppDate`,'%Y-%m-%d') AS AppDate,
        fc.`EnsureState` AS EnsureState,fc.`AuditType` AS AuditType,
        -- fo.`PrtNo` as PrtNo
        fp.`tPrtNo` as PrtNo,
        fd.`RiskCode` as RiskCode
        FROM
        fcensure fc,
        fcgrpinfo fg,
        fcgrpOrder fo,
        fcprtandcorerela fp,
        fcdailyinsureriskinfo fd
        WHERE
        1=1 and fc.PlanType='2' AND fc.GrpNo=fg.GrpNo and fo.EnsureCode=fc.EnsureCode and fd.EnsureCode=fc.EnsureCode
        and fc.EnsureState ='013' and fc.`AuditType`='0' and fo.PrtNo=fp.PrtNo
        <if test="ensureCode != null and ensureCode != '' ">
            and fc.ensureCode = #{ensureCode}
        </if>
        <if test="grpName != null and grpName !='' ">
            and fg.grpName LIKE concat( '%',#{grpName},'%')
        </if>
        <if test="ensureName != null and ensureName !='' ">
            and ensureName LIKE concat('%',#{ensureName},'%')
        </if>
        <if test="appDate != null and appDate !='' ">
            and fc.appDate LIKE concat('%',#{appDate},'%')
        </if>
        order by fc.`ModifyDate` desc ,fc.`ModifyTime` desc ,fc.GrpNo desc
    </select>
    <select id="selectByEnsureState013_check" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        fc.`EnsureCode`AS EnsureCode,
        fc.`EnsureName` AS EnsureName,
        fg.`GrpName` AS GrpName,
        date_format(fc.`AppDate`,'%Y-%m-%d') AS AppDate,
        fc.`EnsureState` AS EnsureState,fc.`AuditType` AS AuditType,
        fo.`PrtNo` as PrtNo
        FROM
        fcensure fc,
        fcgrpinfo fg,
        fcgrpOrder fo,
        fdagentinfo fa
        WHERE 1=1
        and fc.ClientNo = fa.AgentCode
        and fa.manageCom like #{manageCom}"%"
        and fc.PlanType='2'
        AND fc.GrpNo=fg.GrpNo
        and fo.EnsureCode=fc.EnsureCode
        <if test="ensureCode != null and ensureCode != '' ">
            and fc.ensureCode = #{ensureCode}
        </if>
        <if test="grpName != null and grpName !='' ">
            and fg.grpName LIKE concat( '%',#{grpName},'%')
        </if>
        <if test="ensureState != null and ensureState !=''and ensureState =='013' ">
            and fc.ensureState ='013'
        </if>
        <if test="ensureState != null and ensureState !=''and ensureState =='016'  ">
            and fc.ensureState ='016'
        </if>
        <if test="ensureName != null and ensureName !='' ">
            and ensureName LIKE concat('%',#{ensureName},'%')
        </if>
        <if test="appDate != null and appDate !='' ">
            and fc.appDate LIKE concat('%',#{appDate},'%')
        </if>
        order by fc.`ModifyDate` desc ,fc.`ModifyTime` desc ,fc.GrpNo desc
    </select>
    <select id="selectByEnsureState017" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        fc.`EnsureCode`AS EnsureCode,
        fc.`EnsureName` AS EnsureName,
        fg.`GrpName` AS GrpName,
        date_format(fc.`AppDate`,'%Y-%m-%d') AS AppDate,
        fc.`EnsureState` AS EnsureState,fc.`AuditType` AS AuditType,
        -- fo.`PrtNo` as PrtNo
        fp.`tPrtNo` as PrtNo,
        fd.`RiskCode` as RiskCode
        FROM
        fcensure fc,
        fcgrpinfo fg,
        fcgrpOrder fo,
        fcprtandcorerela fp,
        fcdailyinsureriskinfo fd,
        fdagentinfo fa
        WHERE 1=1
        and fc.ClientNo = fa.AgentCode
        and fa.manageCom like #{manageCom}"%"
        and fc.PlanType='2'
        AND fc.GrpNo=fg.GrpNo
        and fo.EnsureCode=fc.EnsureCode
        and fd.EnsureCode=fc.EnsureCode
        and fc.EnsureState ='017'
        and fc.`AuditType`='0'
        and fo.PrtNo=fp.PrtNo
        <if test="ensureCode != null and ensureCode != '' ">
            and fc.ensureCode = #{ensureCode}
        </if>
        <if test="grpName != null and grpName !='' ">
            and fg.grpName LIKE concat( '%',#{grpName},'%')
        </if>
        <if test="ensureName != null and ensureName !='' ">
            and ensureName LIKE concat('%',#{ensureName},'%')
        </if>
        <if test="appDate != null and appDate !='' ">
            and fc.appDate LIKE concat('%',#{appDate},'%')
        </if>
        order by fc.`ModifyDate` desc ,fc.`ModifyTime` desc ,fc.GrpNo desc
    </select>
    <select id="selectByEnsureState015" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select * from (
        ( SELECT
        fc.`EnsureCode`AS EnsureCode,
        fc.`EnsureName` AS EnsureName,
        fg.`GrpName` AS GrpName,
        date_format(fc.`AppDate`,'%Y-%m-%d') AS AppDate,
        fc.`EnsureState` AS EnsureState,fc.`AuditType` AS AuditType,fc.`ModifyDate` as ModifyDate,
        -- fo.`PrtNo` as PrtNo
        fp.`tPrtNo` as PrtNo,
        fd.`RiskCode` as RiskCode
        FROM
        fcensure fc,
        fcgrpinfo fg,
        fcgrpOrder fo,
        fcprtandcorerela fp,
        fcdailyinsureriskinfo fd,
        fdagentinfo fa
        WHERE 1=1
        and fc.PlanType='2'
        AND fc.GrpNo=fg.GrpNo
        and fo.EnsureCode=fc.EnsureCode
        and fd.EnsureCode=fc.EnsureCode
        and fc.`AuditType`='1'
        and fc.EnsureState ='015'
        and fo.PrtNo=fp.PrtNo
        and fc.ClientNo = fa.AgentCode
        and fa.manageCom like #{manageCom}"%"
        <if test="ensureCode != null and ensureCode != '' ">
            and fc.ensureCode = #{ensureCode}
        </if>
        <if test="grpName != null and grpName !='' ">
            and fg.grpName LIKE concat( '%',#{grpName},'%')
        </if>
        <if test="ensureName != null and ensureName !='' ">
            and ensureName LIKE concat('%',#{ensureName},'%')
        </if>
        <if test="appDate != null and appDate !='' ">
            and fc.appDate LIKE concat('%',#{appDate},'%')
        </if>
        /* order by fc.`ModifyDate` desc ,fc.`ModifyTime` desc ,fc.GrpNo desc*/)
        UNION ALL
        ( SELECT
        fc.`EnsureCode`AS EnsureCode,
        fc.`EnsureName` AS EnsureName,
        fg.`GrpName` AS GrpName,
        date_format(fc.`AppDate`,'%Y-%m-%d') AS AppDate,
        fc.`EnsureState` AS EnsureState,fc.`AuditType` AS AuditType,fc.`ModifyDate` as ModifyDate,
        fo.`PrtNo` as PrtNo,
        fd.`RiskCode` as RiskCode
        FROM
        fcensure fc,
        fcgrpinfo fg,
        fcgrpOrder fo,
        fcdailyinsureriskinfo fd
        WHERE
        1=1 and fc.PlanType='2' AND fc.GrpNo=fg.GrpNo and fo.EnsureCode=fc.EnsureCode AND fd.EnsureCode=fc.EnsureCode
        and fc.EnsureState ='015' and
        fc.`AuditType`='0'
        <if test="ensureCode != null and ensureCode != '' ">
            and fc.ensureCode = #{ensureCode}
        </if>
        <if test="grpName != null and grpName !='' ">
            and fg.grpName LIKE concat( '%',#{grpName},'%')
        </if>
        <if test="ensureName != null and ensureName !='' ">
            and ensureName LIKE concat('%',#{ensureName},'%')
        </if>
        <if test="appDate != null and appDate !='' ">
            and fc.appDate LIKE concat('%',#{appDate},'%')
        </if>/* order by fc.`ModifyDate` desc ,fc.`ModifyTime` desc ,fc.GrpNo desc*/)
        ) t order by t.ModifyDate

    </select>
    <select id="selectByEnsure0" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        fc.`EnsureCode`AS EnsureCode,
        fc.`EnsureName` AS EnsureName,
        fg.`GrpName` AS GrpName,
        date_format(fc.`AppDate`,'%Y-%m-%d') AS AppDate,
        fc.`EnsureState` AS EnsureState,fc.`AuditType` AS AuditType,
        fo.`PrtNo` as PrtNo
        FROM
        fcensure fc,
        fcgrpinfo fg,
        fcgrpOrder fo,
        fdagentinfo fa
        WHERE 1=1
        and fc.ClientNo = fa.AgentCode
        and fa.manageCom like #{manageCom}"%"
        and fc.PlanType='2' AND fc.GrpNo=fg.GrpNo and fo.EnsureCode=fc.EnsureCode and fc.EnsureState ='015' and
        fc.`AuditType`='0'
        <if test="ensureCode != null and ensureCode != '' ">
            and fc.ensureCode = #{ensureCode}
        </if>
        <if test="grpName != null and grpName !='' ">
            and fg.grpName LIKE concat( '%',#{grpName},'%')
        </if>
        <if test="ensureName != null and ensureName !='' ">
            and ensureName LIKE concat('%',#{ensureName},'%')
        </if>
        <if test="appDate != null and appDate !='' ">
            and fc.appDate LIKE concat('%',#{appDate},'%')
        </if>
        order by fc.`ModifyDate` desc ,fc.`ModifyTime` desc ,fc.GrpNo desc
    </select>
    <select id="selectByEnsure1" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        fc.`EnsureCode`AS EnsureCode,
        fc.`EnsureName` AS EnsureName,
        fg.`GrpName` AS GrpName,
        date_format(fc.`AppDate`,'%Y-%m-%d') AS AppDate,
        fc.`EnsureState` AS EnsureState,fc.`AuditType` AS AuditType,
        fo.`PrtNo` as PrtNo
        FROM
        fcensure fc,
        fcgrpinfo fg,
        fcgrpOrder fo,
        fdagentinfo fa
        WHERE 1=1
        and fc.ClientNo = fa.AgentCode
        and fa.manageCom like #{manageCom}"%"
        and fc.PlanType='2' AND fc.GrpNo=fg.GrpNo and fo.EnsureCode=fc.EnsureCode and fc.`AuditType`='1' and
        fc.EnsureState ='015'
        <if test="ensureCode != null and ensureCode != '' ">
            and fc.ensureCode = #{ensureCode}
        </if>
        <if test="grpName != null and grpName !='' ">
            and fg.grpName LIKE concat( '%',#{grpName},'%')
        </if>
        <if test="ensureName != null and ensureName !='' ">
            and ensureName LIKE concat('%',#{ensureName},'%')
        </if>
        <if test="appDate != null and appDate !='' ">
            and fc.appDate LIKE concat('%',#{appDate},'%')
        </if>
        order by fc.`ModifyDate` desc ,fc.`ModifyTime` desc ,fc.GrpNo desc
    </select>
    <select id="selectEnsureList_DailyPhone" resultType="com.sinosoft.eflex.model.FCEnsureGrpInfo">
        (select
        a.EnsureCode,a.EnsureName,b.GrpNo,b.GrpName,a.PlanType,a.EnsureState,a.AuditType,a.EnsureType
        from fcensure a
        LEFT JOIN fcgrpinfo b on a.GrpNo=b.GrpNo
        where
        1=1 and a.ensurestate='015' and a.PlanType='2' and a.PolicyState = '3'
        <if test="grpNo != null and  grpNo != ''">
            and a.GrpNo = #{grpNo,jdbcType=VARCHAR}
        </if>
        <if test="sysDate != null and sysDate !=''">
            <![CDATA[ and DATE_FORMAT(a.CvaliDate, '%Y-%m-%d') <= DATE_FORMAT(#{sysDate}, '%Y-%m-%d')]]>
        </if>
        ORDER BY a.ModifyDate desc, a.ModifyTime desc)
        union
        (select
        a.EnsureCode,a.EnsureName,b.GrpNo,b.GrpName,a.PlanType,a.EnsureState,a.AuditType,a.EnsureType
        from fcensure a
        LEFT JOIN fcgrpinfo b on a.GrpNo=b.GrpNo
        where
        1=1 and a.Ensurestate='1' and a.PlanType in ('0','1') and a.PolicyState ='1'
        <if test="grpNo != null and  grpNo != ''">
            and a.GrpNo = #{grpNo,jdbcType=VARCHAR}
        </if>
        <if test="sysDate != null and sysDate !=''">
            <![CDATA[ and DATE_FORMAT(a.EndAppntDate, '%Y-%m-%d') > DATE_FORMAT(#{sysDate}, '%Y-%m-%d')]]>
        </if>
        <if test="sysDate != null and sysDate !=''">
            <![CDATA[ and DATE_FORMAT(a.StartAppntDate, '%Y-%m-%d') < DATE_FORMAT(#{sysDate}, '%Y-%m-%d')]]>
        </if>
        ORDER BY a.ModifyDate desc, a.ModifyTime desc)
    </select>
    <select id="selectEnsureList_DailyChange" resultType="com.sinosoft.eflex.model.FCEnsureGrpInfo">
        (select
        a.EnsureCode,a.EnsureName,b.GrpNo,b.GrpName,a.PlanType,a.EnsureState,a.AuditType,a.EnsureType
        from fcensure a
        LEFT JOIN fcgrpinfo b on a.GrpNo=b.GrpNo
        where
        1=1 and a.ensurestate='015' and a.PlanType='2' and a.PolicyState = '3'
        <if test="grpNo != null and  grpNo != ''">
            and a.GrpNo = #{grpNo,jdbcType=VARCHAR}
        </if>
        <if test="sysDate != null and sysDate !=''">
            <![CDATA[ and DATE_FORMAT(a.CvaliDate, '%Y-%m-%d') <= DATE_FORMAT(#{sysDate}, '%Y-%m-%d')]]>
        </if>
        ORDER BY a.ModifyDate desc, a.ModifyTime desc)
        UNION
        (SELECT
        a.EnsureCode,a.EnsureName,b.GrpNo,b.GrpName,a.PlanType,a.EnsureState,a.AuditType,a.EnsureType
        FROM fcensure a
        LEFT JOIN fcgrpinfo b ON a.GrpNo=b.GrpNo
        WHERE
        1=1 AND a.AuditType='1'
        <if test="grpNo != null and  grpNo != ''">
            and a.GrpNo = #{grpNo,jdbcType=VARCHAR}
        </if>
        <if test="sysDate != null and sysDate !=''">
            <![CDATA[ and DATE_FORMAT(a.CvaliDate, '%Y-%m-%d') <= DATE_FORMAT(#{sysDate}, '%Y-%m-%d')]]>
        </if>
        ORDER BY a.ModifyDate DESC, a.ModifyTime DESC)
    </select>
    <select id="selectEnsureList_Dailys" resultType="com.sinosoft.eflex.model.FCEnsureGrpInfo">
        (select
        a.EnsureCode,a.EnsureName,b.GrpNo,b.GrpName,a.PlanType,a.EnsureState,a.AuditType,a.EnsureType
        from fcensure a
        LEFT JOIN fcgrpinfo b on a.GrpNo=b.GrpNo
        where
        1=1 and a.ensurestate='015' and a.PlanType='2' and a.PolicyState = '3'
        <if test="grpNo != null and  grpNo != ''">
            and a.GrpNo = #{grpNo,jdbcType=VARCHAR}
        </if>
        <if test="sysDate != null and sysDate !=''">
            <![CDATA[ and DATE_FORMAT(a.CvaliDate, '%Y-%m-%d') <= DATE_FORMAT(#{sysDate}, '%Y-%m-%d')]]>
        </if>
        ORDER BY a.ModifyDate desc, a.ModifyTime desc)
        UNION
        (SELECT
        a.EnsureCode,a.EnsureName,b.GrpNo,b.GrpName,a.PlanType,a.EnsureState,a.AuditType,a.EnsureType
        FROM fcensure a
        LEFT JOIN fcgrpinfo b ON a.GrpNo=b.GrpNo
        WHERE
        1=1 AND a.AuditType='1'
        <if test="grpNo != null and  grpNo != ''">
            and a.GrpNo = #{grpNo,jdbcType=VARCHAR}
        </if>
        <if test="sysDate != null and sysDate !=''">
            <![CDATA[ and DATE_FORMAT(a.CvaliDate, '%Y-%m-%d') <= DATE_FORMAT(#{sysDate}, '%Y-%m-%d')]]>
        </if>
        ORDER BY a.ModifyDate DESC, a.ModifyTime DESC)
    </select>
    <select id="selectEnsureList_Years" resultType="com.sinosoft.eflex.model.FCEnsureGrpInfo">
        select
        a.EnsureCode,a.EnsureName,b.GrpNo,b.GrpName,a.PlanType,a.EnsureState,a.AuditType,a.EnsureType
        from fcensure a
        LEFT JOIN fcgrpinfo b on a.GrpNo=b.GrpNo
        where
        1=1 and a.Ensurestate='1' and a.PlanType in ('0','1') and a.PolicyState ='1'
        <if test="grpNo != null and  grpNo != ''">
            and a.GrpNo = #{grpNo,jdbcType=VARCHAR}
        </if>
        <if test="sysDate != null and sysDate !=''">
            <![CDATA[ and DATE_FORMAT(a.EndAppntDate, '%Y-%m-%d') > DATE_FORMAT(#{sysDate}, '%Y-%m-%d')]]>
        </if>
        <if test="sysDate != null and sysDate !=''">
            <![CDATA[ and DATE_FORMAT(a.StartAppntDate, '%Y-%m-%d') < DATE_FORMAT(#{sysDate}, '%Y-%m-%d')]]>
        </if>
        ORDER BY a.ModifyDate desc, a.ModifyTime desc
    </select>
    <select id="selectEnsureByOrderNo" parameterType="java.lang.String"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List1"/>
        from fcensure a
        inner join fcgrpOrder b on a.EnsureCode = b.EnsureCode
        inner join fcorder c on b.GrpOrderNo = c.GrpOrderNo
        where c.orderNo = #{orderNo};
    </select>
    <select id="selectByOperator" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(*)
        from fcensure
        where operator = #{userNo}
    </select>
    <select id="selectEnsureInsureInfo" parameterType="java.util.List"
            resultType="com.sinosoft.eflex.model.datamanage.EnsureInsureInfo">
        SELECT
        b.GrpName,
        a.EnsureName,
        a.EnsureCode,
        a.EndAppntDate,
        a.CvaliDate,
        a.PolicyEndDate,
        c.`Name`,
        c.MobilePhone,
        c.EMail email,
        a.ClientNo,
        (SELECT SUM(d.GrpPrem+d.SelfPrem) FROM fcorderitem d
        INNER JOIN fcorderinsured e ON e.OrderItemNo = d.OrderItemNo
        INNER JOIN fcorder f ON d.OrderNo = f.OrderNo
        INNER JOIN fcgrporder g ON f.GrpOrderNo = g.GrpOrderNo
        INNER JOIN fcstafffamilyrela h ON h.PersonID = e.PersonID AND h.PerNo = f.PerNo
        WHERE h.Relation = '0' AND g.EnsureCode = a.EnsureCode
        ) staffPrem,
        (SELECT SUM(d.GrpPrem+d.SelfPrem) FROM fcorderitem d
        INNER JOIN fcorderinsured e ON e.OrderItemNo = d.OrderItemNo
        INNER JOIN fcorder f ON d.OrderNo = f.OrderNo
        INNER JOIN fcgrporder g ON f.GrpOrderNo = g.GrpOrderNo
        INNER JOIN fcstafffamilyrela h ON h.PersonID = e.PersonID AND h.PerNo = f.PerNo
        WHERE h.Relation != '0' AND g.EnsureCode = a.EnsureCode
        ) familyPrem,
        (SELECT COUNT(1) FROM fcorderitem d
        INNER JOIN fcorderinsured e ON e.OrderItemNo = d.OrderItemNo
        INNER JOIN fcorder f ON d.OrderNo = f.OrderNo
        INNER JOIN fcgrporder g ON f.GrpOrderNo = g.GrpOrderNo
        INNER JOIN fcstafffamilyrela h ON h.PersonID = e.PersonID AND h.PerNo = f.PerNo
        WHERE h.Relation = '0' AND g.EnsureCode = a.EnsureCode
        ) staffCount,
        (SELECT COUNT(1) FROM fcorderitem d
        INNER JOIN fcorderinsured e ON e.OrderItemNo = d.OrderItemNo
        INNER JOIN fcorder f ON d.OrderNo = f.OrderNo
        INNER JOIN fcgrporder g ON f.GrpOrderNo = g.GrpOrderNo
        INNER JOIN fcstafffamilyrela h ON h.PersonID = e.PersonID AND h.PerNo = f.PerNo
        WHERE h.Relation != '0' AND g.EnsureCode = a.EnsureCode
        ) familyCount,
        (SELECT SUM(f.TotalPrem) FROM fcdefaultplan d
        inner JOIN fcensureplan f on f.PlanCode = d.PlanCode and f.EnsureCode = d.EnsureCode
        where d.EnsureCode = a.EnsureCode and d.personId not in (select PersonID from fcorderinsured where GrpOrderNo =
        m.GrpOrderNo)) stayDefaultPlanPrem,
        (SELECT COUNT(1) FROM fcdefaultplan d
        where d.EnsureCode = a.EnsureCode and d.personId not in (select PersonID from fcorderinsured where GrpOrderNo =
        m.GrpOrderNo)) stayDefaultPlanPeopleCount
        FROM fcensure a
        INNER JOIN fcgrpinfo b ON b.grpno = a.GrpNo
        INNER JOIN fcensurecontact c ON c.EnsureCode = a.EnsureCode
        INNER JOIN fcgrporder m on m.EnsureCode = a.EnsureCode
        WHERE a.EnsureCode in
        <foreach collection="list" index="index" item="ensureCode" open="(" separator="," close=")">
            #{ensureCode}
        </foreach>
        GROUP BY a.EnsureCode;
    </select>
    <select id="selectExportEnsureInsureInfo" parameterType="com.sinosoft.eflex.model.datamanage.GetEnsureInsureDataReq"
            resultType="com.sinosoft.eflex.model.datamanage.EnsureInsureInfo">
        SELECT
        b.GrpName,
        a.EnsureName,
        a.EnsureCode,
        a.EndAppntDate,
        a.CvaliDate,
        a.PolicyEndDate,
        c.`Name`,
        c.MobilePhone,
        c.EMail email,
        a.ClientNo,
        (SELECT SUM(d.GrpPrem+d.SelfPrem) FROM fcorderitem d
        INNER JOIN fcorderinsured e ON e.OrderItemNo = d.OrderItemNo
        INNER JOIN fcorder f ON d.OrderNo = f.OrderNo
        INNER JOIN fcgrporder g ON f.GrpOrderNo = g.GrpOrderNo
        INNER JOIN fcstafffamilyrela h ON h.PersonID = e.PersonID AND h.PerNo = f.PerNo
        WHERE h.Relation = '0' AND g.EnsureCode = a.EnsureCode
        ) staffPrem,
        (SELECT SUM(d.GrpPrem+d.SelfPrem) FROM fcorderitem d
        INNER JOIN fcorderinsured e ON e.OrderItemNo = d.OrderItemNo
        INNER JOIN fcorder f ON d.OrderNo = f.OrderNo
        INNER JOIN fcgrporder g ON f.GrpOrderNo = g.GrpOrderNo
        INNER JOIN fcstafffamilyrela h ON h.PersonID = e.PersonID AND h.PerNo = f.PerNo
        WHERE h.Relation != '0' AND g.EnsureCode = a.EnsureCode
        ) familyPrem,
        (SELECT COUNT(1) FROM fcorderitem d
        INNER JOIN fcorderinsured e ON e.OrderItemNo = d.OrderItemNo
        INNER JOIN fcorder f ON d.OrderNo = f.OrderNo
        INNER JOIN fcgrporder g ON f.GrpOrderNo = g.GrpOrderNo
        INNER JOIN fcstafffamilyrela h ON h.PersonID = e.PersonID AND h.PerNo = f.PerNo
        WHERE h.Relation = '0' AND g.EnsureCode = a.EnsureCode
        ) staffCount,
        (SELECT COUNT(1) FROM fcorderitem d
        INNER JOIN fcorderinsured e ON e.OrderItemNo = d.OrderItemNo
        INNER JOIN fcorder f ON d.OrderNo = f.OrderNo
        INNER JOIN fcgrporder g ON f.GrpOrderNo = g.GrpOrderNo
        INNER JOIN fcstafffamilyrela h ON h.PersonID = e.PersonID AND h.PerNo = f.PerNo
        WHERE h.Relation != '0' AND g.EnsureCode = a.EnsureCode
        ) familyCount,
        (SELECT SUM(f.TotalPrem) FROM fcdefaultplan d
        inner JOIN fcensureplan f on f.PlanCode = d.PlanCode and f.EnsureCode = d.EnsureCode
        where d.EnsureCode = a.EnsureCode and d.personId not in (select PersonID from fcorderinsured where GrpOrderNo =
        m.GrpOrderNo)) stayDefaultPlanPrem,
        (SELECT COUNT(1) FROM fcdefaultplan d
        where d.EnsureCode = a.EnsureCode and d.personId not in (select PersonID from fcorderinsured where GrpOrderNo =
        m.GrpOrderNo)) stayDefaultPlanPeopleCount
        FROM fcensure a
        INNER JOIN fcgrpinfo b ON b.grpno = a.GrpNo
        INNER JOIN fcensurecontact c ON c.EnsureCode = a.EnsureCode
        INNER JOIN fcgrporder m on m.EnsureCode = a.EnsureCode
        left join fdagentinfo fa on a.ClientNo = fa.AgentCode
        WHERE 1=1
        and fa.manageCom like concat(#{manageCom},'%')
        <if test="ensureCodeList.size() > 0">
            and a.EnsureCode in
            <foreach collection="ensureCodeList" index="index" item="ensureCode" open="(" separator="," close=")">
                #{ensureCode}
            </foreach>
        </if>
        <if test="ensureCodeList.size() == 0">
            <if test='grpName != null and grpName != ""'>
                and b.grpName like concat('%',#{grpName},'%')
            </if>
            <if test='grpIdType != null and grpIdType != ""'>
                and b.grpIdType = #{grpIdType}
            </if>
            <if test='grpIdNo != null and grpIdNo != ""'>
                and b.grpIdNo = #{grpIdNo}
            </if>
            <!--            <if test='planType != null and planType != ""'>-->
            <!--                and a.planType = #{planType}-->
            <!--            </if>-->
            <if test='ensureState != null and ensureState != ""'>
                and a.ensureState = #{ensureState}
            </if>
            <!-- 暂时查询固定计划的 -->
            and a.planType = '0'
            <!-- 暂时查询这几个状态的 -->
            and ensureState IN (1,015,018)
            <if test='ensureName != null and ensureName != ""'>
                and a.ensureName = concat('%',#{ensureName},'%')
            </if>
            <if test='ensureCode != null and ensureCode != ""'>
                and a.ensureCode = #{ensureCode}
            </if>
            <if test='startTime != null and startTime != ""'>
                and a.MakeDate &gt;= #{startTime}
            </if>
            <if test='endTime != null and endTime != ""'>
                and a.MakeDate &lt;= #{endTime}
            </if>
        </if>
        GROUP BY a.EnsureCode;
    </select>
    <select id="selectEnsureByGrpNo" parameterType="java.lang.String" resultType="com.sinosoft.eflex.model.FCEnsure">
        SELECT e.*
        FROM fcensure e
                 INNER JOIN fcensurecontact c ON c.`EnsureCode` = e.`EnsureCode`
        WHERE e.GrpNo = #{grpNo}
          AND c.`IDNo` = #{idNo}
    </select>


    <select id="selectByOrderNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fcensure
        where orderNo = #{orderNo};
    </select>
</mapper>