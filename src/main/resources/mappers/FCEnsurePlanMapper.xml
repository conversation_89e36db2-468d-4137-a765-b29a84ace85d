<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.eflex.dao.FCEnsurePlanMapper">
	<resultMap id="BaseResultMap" type="com.sinosoft.eflex.model.FCEnsurePlan">
		<id column="PlanCode" jdbcType="VARCHAR" property="planCode" />
		<result column="EnsureCode" jdbcType="VARCHAR" property="ensureCode" />
		<result column="PlanName" jdbcType="VARCHAR" property="planName" />
		<result column="PlanObject" jdbcType="VARCHAR" property="planObject" />
        <result column="PlanKey" jdbcType="VARCHAR" property="planKey"/>
        <result column="PlanState" jdbcType="VARCHAR" property="planState"/>
		<result column="InsuredNumber" jdbcType="INTEGER" property="insuredNumber" />
		<result column="TotalPrem" jdbcType="DOUBLE" property="totalPrem" />
		<result column="Operator" jdbcType="VARCHAR" property="operator" />
		<result column="OperatorCom" jdbcType="VARCHAR" property="operatorCom" />
		<result column="MakeDate" jdbcType="DATE" property="makeDate" />
		<result column="MakeTime" jdbcType="VARCHAR" property="makeTime" />
		<result column="ModifyDate" jdbcType="DATE" property="modifyDate" />
		<result column="ModifyTime" jdbcType="VARCHAR" property="modifyTime" />
	</resultMap>
	<sql id="Base_Column_List">
		PlanCode, EnsureCode, PlanName, PlanObject, PlanKey,
		InsuredNumber, TotalPrem,
		Operator,
		OperatorCom, MakeDate, MakeTime,
		ModifyDate, ModifyTime
	</sql>
	<!-- 企业计划汇总 -->
	<sql id="Plan_collection">
		PlanCode,EnsureCode,PlanName,PlanObject,PlanKey,InsuredNumber,TotalPrem
	</sql>
	<!-- 投保清单导出excel -->
	<resultMap type="com.sinosoft.eflex.model.FCEnsurePlan" id="InsuredDetails"
		extends="BaseResultMap">
		<collection property="fcPlanRisks" column="{ensureCode=ensureCode,planCode=PlanCode}"
			javaType="java.util.ArrayList" ofType="com.sinosoft.eflex.model.FCPlanRisk"
			select="com.sinosoft.eflex.dao.FCPlanRiskMapper.selectInsuredDetail"
			fetchType="lazy" />
	</resultMap>
	<!-- 投保清单导出excel -->
	<select id="selectInsuredDetail" parameterType="java.util.Map"
		resultMap="InsuredDetails">
		select
		PlanCode,PlanName,PlanObject,PlanKey,TotalPrem,p.ensureCode
		from
		fcensureplan p,fcorderitemdetail d,fcorderitem i,fcorderinsured u,fcorder o,fcperinfo f
		where p.PlanCode = d.ProductCode
		AND d.OrderItemDetailNo = i.OrderItemDetailNo
		AND i.OrderItemNo = u.OrderItemNo
		AND u.OrderNo = o.OrderNo
		AND o.PerNo = f.PerNo
		AND p.ensureCode=#{ensureCode,jdbcType=VARCHAR}
		AND p.PlanCode = #{planCode,jdbcType=VARCHAR}
		AND i.OrderItemNo = #{OrderItemNo}
		<if test='ensureType == "0" '>
			<if test="garName != null and garName != ''">
				AND f.name LIKE CONCAT(CONCAT('%',#{garName},'%'))
			</if>
			<if test="department != null and department != ''">
				AND u.Department LIKE CONCAT(CONCAT('%',#{department},'%'))
			</if>
			<if test="garSex != null and garSex != ''">
				AND u.Sex = #{garSex}
			</if>
			<if test="planObject != null and planObject != ''">
				AND p.PlanObject = #{planObject}
			</if>
		</if>
		<if test='ensureType == "1" '>
			<if test=" stuName != null and stuName != ''">
				AND u.`name` LIKE CONCAT(CONCAT('%',#{stuName},'%'))
			</if>
			<if test="garName != null and garName != ''">
				AND f.name LIKE CONCAT(CONCAT('%',#{garName},'%'))
			</if>
			<if test="planName != null and planName != ''">
				AND p.PlanName LIKE CONCAT(CONCAT('%',#{planName},'%'))
			</if>
		</if>
	</select>
	<!-- 企业计划汇总 -->
	<resultMap type="com.sinosoft.eflex.model.FCEnsurePlan" id="PlanCollection"
		extends="BaseResultMap">
		<collection property="fcPlanRisks"
			column="{ensureCode=EnsureCode,planCode=PlanCode}" javaType="java.util.ArrayList"
			ofType="com.sinosoft.eflex.model.FCPlanRisk" select="com.sinosoft.eflex.dao.FCPlanRiskMapper.selectPlanCollection"
			fetchType="lazy" />
	</resultMap>
	<!-- 计划详情查询 -->
	<resultMap type="com.sinosoft.eflex.model.FCEnsurePlan" id="PlanDetailList"
		extends="BaseResultMap">
		<collection property="fcPlanRisks" column="{planCode=PlanCode}"
			javaType="java.util.ArrayList" ofType="com.sinosoft.eflex.model.FCPlanRisk"
			select="com.sinosoft.eflex.dao.FCPlanRiskMapper.selectPlanDetailList"
			fetchType="lazy" />
	</resultMap>
	<!-- 计划详情查询 -->
	<select id="selectPlanDetail" parameterType="java.util.Map"
		resultMap="PlanDetailList">
		select
		PlanCode,PlanName,PlanObject,PlanKey,TotalPrem
		from fcensureplan
		<where>
		    1=1
			<if test="ensureCode != null and ensureCode !=''">
				AND ensureCode = #{ensureCode,jdbcType=VARCHAR}
			</if>
			<if test="planCode != null and planCode !=''">
				And PlanCode = #{planCode,jdbcType=VARCHAR}
			</if>
		</where>
	</select>
	<!-- 查询计划编码是否存在 -->
	<select id="existdefultPlanCode" parameterType="map" resultType="Integer">
	   select count(*) as PlanCodeSum from fcensureplan where PlanCode = #{planCode,jdbcType=VARCHAR} 
	   and EnsureCode = #{ensureCode,jdbcType=VARCHAR}  and  PlanObject='1'
	</select>
	<select id="selectByPrimaryKey" parameterType="java.util.Map"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from fcensureplan
		where EnsureCode = #{ensureCode,jdbcType=VARCHAR} and PlanCode = #{planCode,jdbcType=VARCHAR}
	</select>
	<delete id="deleteByPrimaryKey" parameterType="java.lang.String">
		delete from
		fcensureplan
		where PlanCode = #{planCode,jdbcType=VARCHAR}
	</delete>
	<insert id="insert" parameterType="com.sinosoft.eflex.model.FCEnsurePlan">
		insert into fcensureplan
		(PlanCode, EnsureCode, PlanName,
		PlanObject, PlanKey, PlanState,InsuredNumber,
		TotalPrem, Operator, OperatorCom,
		MakeDate, MakeTime, ModifyDate,
		ModifyTime)
		values (#{planCode,jdbcType=VARCHAR},
		#{ensureCode,jdbcType=VARCHAR},
		#{planName,jdbcType=VARCHAR},
		#{planObject,jdbcType=VARCHAR},
		#{planKey,jdbcType=VARCHAR},
		#{planState,jdbcType=VARCHAR},
		#{insuredNumber,jdbcType=INTEGER},
		#{totalPrem,jdbcType=DOUBLE},
		#{operator,jdbcType=VARCHAR},
		#{operatorCom,jdbcType=VARCHAR},
		#{makeDate,jdbcType=DATE}, #{makeTime,jdbcType=VARCHAR},
		#{modifyDate,jdbcType=DATE},
		#{modifyTime,jdbcType=VARCHAR})
	</insert>
	<insert id="insertSelective" parameterType="com.sinosoft.eflex.model.FCEnsurePlan">
		insert into fcensureplan
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="planCode != null">
				PlanCode,
			</if>
			<if test="ensureCode != null">
				EnsureCode,
			</if>
			<if test="planName != null">
				PlanName,
			</if>
			<if test="planObject != null">
				PlanObject,
			</if>
			<if test="planKey != null">
				PlanKey,
			</if>
            <if test="planState != null">
                planState,
            </if>
			<if test="insuredNumber != null">
				InsuredNumber,
			</if>
			<if test="totalPrem != null">
				TotalPrem,
			</if>
			<if test="operator != null">
				Operator,
			</if>
			<if test="operatorCom != null">
				OperatorCom,
			</if>
			<if test="makeDate != null">
				MakeDate,
			</if>
			<if test="makeTime != null">
				MakeTime,
			</if>
			<if test="modifyDate != null">
				ModifyDate,
			</if>
			<if test="modifyTime != null">
				ModifyTime,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="planCode != null">
				#{planCode,jdbcType=VARCHAR},
			</if>
			<if test="ensureCode != null">
				#{ensureCode,jdbcType=VARCHAR},
			</if>
			<if test="planName != null">
				#{planName,jdbcType=VARCHAR},
			</if>
			<if test="planObject != null">
				#{planObject,jdbcType=VARCHAR},
			</if>
			<if test="planKey != null">
				#{planKey,jdbcType=DATE},
			</if>
            <if test="planState != null">
                #{planState,jdbcType=DATE},
            </if>
			<if test="insuredNumber != null">
				#{insuredNumber,jdbcType=INTEGER},
			</if>
			<if test="totalPrem != null">
				#{totalPrem,jdbcType=DOUBLE},
			</if>
			<if test="operator != null">
				#{operator,jdbcType=VARCHAR},
			</if>
			<if test="operatorCom != null">
				#{operatorCom,jdbcType=VARCHAR},
			</if>
			<if test="makeDate != null">
				#{makeDate,jdbcType=DATE},
			</if>
			<if test="makeTime != null">
				#{makeTime,jdbcType=VARCHAR},
			</if>
			<if test="modifyDate != null">
				#{modifyDate,jdbcType=DATE},
			</if>
			<if test="modifyTime != null">
				#{modifyTime,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>
	<update id="updateByPrimaryKeySelective" parameterType="com.sinosoft.eflex.model.FCEnsurePlan">
		update fcensureplan
		<set>
			<if test="ensureCode != null">
				EnsureCode = #{ensureCode,jdbcType=VARCHAR},
			</if>
			<if test="planName != null">
				PlanName = #{planName,jdbcType=VARCHAR},
			</if>
			<if test="planObject != null">
				PlanObject = #{planObject,jdbcType=VARCHAR},
			</if>
			<if test="planKey != null">
                PlanKey = #{planKey,jdbcType=VARCHAR},
			</if>
            <if test="planState != null">
                planState = #{planState,jdbcType=VARCHAR},
            </if>
			<if test="insuredNumber != null">
				InsuredNumber = #{insuredNumber,jdbcType=INTEGER},
			</if>
			<if test="totalPrem != null">
				TotalPrem = #{totalPrem,jdbcType=DOUBLE},
			</if>
			<if test="operator != null">
				Operator = #{operator,jdbcType=VARCHAR},
			</if>
			<if test="operatorCom != null">
				OperatorCom = #{operatorCom,jdbcType=VARCHAR},
			</if>
			<if test="makeDate != null">
				MakeDate = #{makeDate,jdbcType=DATE},
			</if>
			<if test="makeTime != null">
				MakeTime = #{makeTime,jdbcType=VARCHAR},
			</if>
			<if test="modifyDate != null">
				ModifyDate = #{modifyDate,jdbcType=DATE},
			</if>
			<if test="modifyTime != null">
				ModifyTime = #{modifyTime,jdbcType=VARCHAR},
			</if>
		</set>
		where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
		AND   PlanCode = #{planCode,jdbcType=VARCHAR}
	</update>
	<update id="updateByPrimaryKey" parameterType="com.sinosoft.eflex.model.FCEnsurePlan">
		update
		fcensureplan
		set EnsureCode = #{ensureCode,jdbcType=VARCHAR},
		PlanName =
		#{planName,jdbcType=VARCHAR},
		PlanObject =
		#{planObject,jdbcType=VARCHAR},
		PlanKey = #{planKey,jdbcType=VARCHAR},
		planState = #{planState,jdbcType=VARCHAR},
		InsuredNumber = #{insuredNumber,jdbcType=INTEGER},
		TotalPrem =
		#{totalPrem,jdbcType=DOUBLE},
		Operator = #{operator,jdbcType=VARCHAR},
		OperatorCom = #{operatorCom,jdbcType=VARCHAR},
		MakeDate =
		#{makeDate,jdbcType=DATE},
		MakeTime = #{makeTime,jdbcType=VARCHAR},
		ModifyDate = #{modifyDate,jdbcType=DATE},
		ModifyTime =
		#{modifyTime,jdbcType=VARCHAR}
		where 
		EnsureCode = #{ensureCode,jdbcType=VARCHAR}
		AND	PlanCode = #{planCode,jdbcType=VARCHAR}
	</update>

	<!-- 企业计划汇总 -->
	<select id="selectByEnsureCode" parameterType="java.lang.String"
		resultMap="PlanCollection">
		select
		fc.PlanCode,fc.EnsureCode,fc.PlanName,fc.PlanObject,fc.PlanKey,
		(select count(*) from fcorderitem fi,fcorder fd,fcgrporder fg,fcorderitemdetail ft,fcperregistday fp
		where fi.orderno=fd.orderno and fd.GrpOrderNo=fg.GrpOrderNo and fi.orderitemdetailno=ft.orderitemdetailno and fp.perno = fd.perno and fp.EnsureCode = fc.EnsureCode
		and ft.productcode=fc.plancode
		and fg.EnsureCode=fc.EnsureCode
		and fp.LockState = '0') InsuredNumber,fc.TotalPrem
		from fcensureplan fc
		<where>
            and fc.planState != '01'
			<if test="ensureCode != null and ensureCode !=''">
                and fc.EnsureCode = #{ensureCode,jdbcType=VARCHAR}
			</if>
		</where>
	</select>
	<select id="selectFCEnsurePlans" parameterType="java.util.HashMap"
		resultType="com.sinosoft.eflex.model.FCEnsurePlan">
		select
		<include refid="Base_Column_List" />
		from fcensureplan
        where
        planState = '02'
		<if test="planCode != null and planCode != '' ">
			and PlanCode = #{planCode}
		</if>
		<if test="ensureCode != null and ensureCode != ''">
			and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
		</if>
		<if test="planName != null">
			and PlanName = #{planName,jdbcType=VARCHAR}
		</if>
		<if test="planObject != null and planObject != ''">
			and PlanObject = #{planObject,jdbcType=VARCHAR}
		</if>
		<if test="planKey != null">
            and PlanKey = #{planKey,jdbcType=VARCHAR}
		</if>
        <if test="planState != null">
            and PlanState = #{planState,jdbcType=VARCHAR}
        </if>
		<if test="insuredNumber != null">
			and InsuredNumber = #{insuredNumber,jdbcType=INTEGER}
		</if>
		<if test="totalPrem != null">
			and TotalPrem = #{totalPrem,jdbcType=DOUBLE}
		</if>
        order by LENGTH(planCode)
	</select>
    <select id="selectAllFCEnsurePlans" parameterType="java.util.HashMap"
            resultType="com.sinosoft.eflex.model.FCEnsurePlan">
        select
        <include refid="Base_Column_List"/>
        from fcensureplan
        where 1=1
        <if test="planCode != null and planCode != '' ">
            and PlanCode = #{planCode}
        </if>
        <if test="ensureCode != null and ensureCode != ''">
            and EnsureCode = #{ensureCode,jdbcType=VARCHAR}
        </if>
        <if test="planName != null">
            and PlanName = #{planName,jdbcType=VARCHAR}
        </if>
        <if test="planObject != null and planObject != ''">
            and PlanObject = #{planObject,jdbcType=VARCHAR}
        </if>
        <if test="planKey != null">
            and PlanKey = #{planKey,jdbcType=VARCHAR}
        </if>
        <if test="planState != null">
            and PlanState = #{planState,jdbcType=VARCHAR}
        </if>
        <if test="insuredNumber != null">
            and InsuredNumber = #{insuredNumber,jdbcType=INTEGER}
        </if>
        <if test="totalPrem != null">
            and TotalPrem = #{totalPrem,jdbcType=DOUBLE}
        </if>
    </select>

	<delete id="deleteByEnsureCode" parameterType="java.lang.String">
		delete from
			fcensureplan
		where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
	</delete>
	<select id="selectEnsureCodeByplanObject" resultMap="PlanCollection">
		select
		<include refid="Plan_collection" />
		from fcensureplan
		<where>
			<if test="ensureCode != null and ensureCode !=''">
				EnsureCode = #{ensureCode,jdbcType=VARCHAR}
			</if>
			<if test="planObject != null and planObject !=''">
				and PlanObject = #{planObject,jdbcType=VARCHAR}
			</if>
		</where>
		order by PlanCode
	</select>

	<select id="selectPlanInfo" resultType="com.sinosoft.eflex.model.FCEnsurePlan">
		select PlanCode, EnsureCode, PlanName, PlanObject,TotalPrem,PlanKey
	    from fcensureplan
		<where>
			<if test="ensureCode != null and ensureCode !=''">
				EnsureCode = #{ensureCode,jdbcType=VARCHAR}
			</if>
			<if test="planObject != null and planObject !=''">
				and PlanObject = #{planObject,jdbcType=VARCHAR}
			</if>
		</where>
	</select>

	<!-- 查询转码后的计划 -->
	<select id="selectTranscodingFCEnsurePlans" parameterType="java.util.HashMap" resultType="com.sinosoft.eflex.model.FCEnsurePlan">
		select fc.PlanCode,fc.EnsureCode,fc.PlanName,
			(CASE WHEN fc.PlanObject = 1 then '员工' else '家属' END) as PlanObject,
			fc.PlanKey,(select count(*) from fcorderitem fi,fcorder fd,fcgrporder fg 
			where fi.orderno=fd.orderno and fd.GrpOrderNo=fg.GrpOrderNo
			and fg.EnsureCode=fc.EnsureCode) InsuredNumber,fc.TotalPrem,
			fc.Operator,fc.OperatorCom,fc.MakeDate,fc.MakeTime,fc.ModifyDate,fc.ModifyTime 
		from fcensureplan fc
		where 1=1
		<if test="ensureCode != null">
			and fc.EnsureCode = #{ensureCode,jdbcType=VARCHAR}
		</if>
	</select>
	<select id="selectByKey" parameterType="java.util.Map"
			resultMap="BaseResultMap">
		select
		<include refid="Plan_collection" />
		from fcensureplan
		where EnsureCode = #{ensureCode,jdbcType=VARCHAR} and PlanCode = #{planCode,jdbcType=VARCHAR}
	</select>

	<insert id="insertList" parameterType="java.util.List">
		insert into fcensureplan
		(PlanCode, EnsureCode, PlanName,
        PlanObject, PlanKey, PlanState,InsuredNumber,
		TotalPrem, Operator, OperatorCom,
		MakeDate, MakeTime, ModifyDate,
		ModifyTime) values
		<foreach collection ="list" item="item" index= "index" separator =",">
			(#{item.planCode,jdbcType=VARCHAR},
			#{item.ensureCode,jdbcType=VARCHAR},
			#{item.planName,jdbcType=VARCHAR},
			#{item.planObject,jdbcType=VARCHAR},
            #{item.planKey,jdbcType=VARCHAR},
            #{item.planState,jdbcType=VARCHAR},
			#{item.insuredNumber,jdbcType=INTEGER},
			#{item.totalPrem,jdbcType=DOUBLE},
			#{item.operator,jdbcType=VARCHAR},
			#{item.operatorCom,jdbcType=VARCHAR},
			#{item.makeDate,jdbcType=DATE},
			#{item.makeTime,jdbcType=VARCHAR},
			#{item.modifyDate,jdbcType=DATE},
			#{item.modifyTime,jdbcType=VARCHAR})
		</foreach >
	</insert>
	<!-- 查询默认计划编码 -->
	<select id="existListdefultPlanCode" parameterType="java.util.Map" resultType="java.lang.String">
	   select plancode from fcensureplan 
	   where  EnsureCode = #{ensureCode,jdbcType=VARCHAR}  and  PlanObject=#{planObject,jdbcType=VARCHAR}
	</select>
	<!-- 计划详情查询 -->
	<select id="selectPlanPrem" parameterType="java.lang.String"
			resultType="java.lang.Double">
		select
		 TotalPrem
		from fcensureplan where EnsureCode = #{ensureCode,jdbcType=VARCHAR}  and PlanCode = #{planCode,jdbcType=VARCHAR}
	</select>
	
	<select id="selectCountStudentEnsure" parameterType="java.util.List" resultType="java.lang.Integer">
		select  count(*) from  fcensureplan where planObject='3' and ensurecode in 
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")">  
            #{item}
        </foreach>
	</select>

	<select id="getSumTotalPrem" parameterType="java.lang.String" resultType="java.lang.String">
		SELECT if(SUM(TotalPrem) is not NULL,SUM(TotalPrem),'0') FROM fcensureplan where EnsureCode = #{ensureCode,jdbcType=VARCHAR} 
		AND PlanCode in (select PlanCode from fcdefaultplan
		WHERE EnsureCode = #{ensureCode,jdbcType=VARCHAR} AND PersonID IN (SELECT PersonID FROM fcstafffamilyrela WHERE PerNo = #{perNo,jdbcType=VARCHAR})
			AND PersonID NOT IN (SELECT PersonID FROM fpinsureplan WHERE EnsureCode = #{ensureCode,jdbcType=VARCHAR} AND PerNo = #{perNo,jdbcType=VARCHAR}))
	</select>

	<delete id="deleteByEnsureCodeAndPlanCode" parameterType="java.lang.String">
		delete from
			fcensureplan
		where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
		and PlanCode = #{planCode,jdbcType=VARCHAR}
	</delete>
    <delete id="deleteMakeingPlanByEnsureCode" parameterType="java.lang.String">
		delete from
			fcensureplan
		where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
		and PlanState = '01'
	</delete>
    <select id="selectPlanObject" parameterType="java.lang.String" resultType="java.lang.String">
        select planObject from  fcensureplan
        where EnsureCode = #{ensureCode,jdbcType=VARCHAR}
          and PlanState = '02'
    </select>



	<update id="updateByEnsureCode" parameterType="com.sinosoft.eflex.model.FCEnsurePlan">
		update fcensureplan
		<set>
			<if test="ensureCode != null">
				EnsureCode = #{ensureCode,jdbcType=VARCHAR},
				planState = '02'
			</if>
		</set>
		where EnsureCode = #{jEnsureCode,jdbcType=VARCHAR}
	</update>
</mapper>